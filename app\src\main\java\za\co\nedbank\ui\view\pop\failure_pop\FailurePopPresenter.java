package za.co.nedbank.ui.view.pop.failure_pop;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;

public class FailurePopPresenter extends NBBasePresenter<FailurePopView> {

    private final NavigationRouter mNavigationRouter;

    @Inject
    FailurePopPresenter(final NavigationRouter navigationRouter) {
        this.mNavigationRouter = navigationRouter;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

}
