/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by charurani on 18-08-2017.
 */

public class MyRecipientsViewModel implements Parcelable{

    private String name;

    public MyRecipientsViewModel(){}

    private MyRecipientsViewModel(Parcel in) {
        name = in.readString();
    }

    public static final Creator<MyRecipientsViewModel> CREATOR = new Creator<MyRecipientsViewModel>() {
        @Override
        public MyRecipientsViewModel createFromParcel(Parcel in) {
            return new MyRecipientsViewModel(in);
        }

        @Override
        public MyRecipientsViewModel[] newArray(int size) {
            return new MyRecipientsViewModel[size];
        }
    };

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(name);
    }
}
