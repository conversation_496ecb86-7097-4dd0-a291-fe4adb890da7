/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository;

import io.reactivex.Observable;
import za.co.nedbank.ui.data.entity.money_request.NotificationsResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentRequestEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentResponseEntity;

/**
 * Created by sandip.lawate on 2/22/2018.
 */

public interface IMoneyRequestRepository {
    Observable<PaymentResponseEntity> sendPaymentsRequest(PaymentRequestEntity paymentRequestModel);

    Observable<NotificationsResponseEntity> getNotifications();
}
