package za.co.nedbank.ui.view.developer_option

import za.co.nedbank.core.base.NBBasePresenter
import za.co.nedbank.core.base.NBBaseView
import za.co.nedbank.core.navigation.NavigationRouter
import za.co.nedbank.core.tracking.Analytics
import javax.inject.Inject

public class DeveloperOptionAlertPresenter @Inject constructor(
    private val navigationRouter: NavigationRouter,
    private val mAnalytics: Analytics
) : NBBasePresenter<NBBaseView>() {

    fun closeActivity() {
        if (view != null) {
            view.close()
        }
    }
}