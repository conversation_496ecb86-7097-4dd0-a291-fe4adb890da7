/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsAdapterModel;

/**
 * Created by swapnil.gawande on 2/20/2018.
 */

interface SentMoneyRequestsView extends NBBaseView {

    void showLoadingView();

    void hideLoadingView();

    void showEmptyListIndicator();

    void showMoneyRequests(List<MoneyRequestsAdapterModel> moneyRequestsAdapterModels);

    void updateMoneyRequests();

    void showError(String error);
}
