package za.co.nedbank.ui.view.home.non_tp_client.non_tp_apply;


import za.co.nedbank.R;

public enum NonTpCyclicPagerData {

    BANK(R.string.non_tp_apply_bank_action_link, R.string.non_tp_apply_bank_description, R.drawable.ic_non_tp_bank_green),
    FINANCIAL_PLANNER(R.string.non_tp_apply_fp_action_link, R.string.non_tp_apply_fp_description, R.drawable.ic_non_tp_financial_planner_green),
    LOAN(R.string.non_tp_apply_loan_action_link, R.string.non_tp_apply_loan_description, R.drawable.ic_non_tp_loan_green);


    private final int itemActionLink;
    private final int itemText;
    private final int itemIcon;


    NonTpCyclicPagerData(int itemName, int itemText, int itemIcon) {
        this.itemActionLink = itemName;
        this.itemText = itemText;
        this.itemIcon = itemIcon;
    }


    public int getItemActionLink() {
        return itemActionLink;
    }

    public int getItemText() {
        return itemText;
    }

    public int getItemIcon() {
        return itemIcon;
    }
}
