package za.co.nedbank.ui.view.refica;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingParam;

public class VerifyMePresenter extends NBBasePresenter<VerifyMeView> {

    private final Analytics mAnalytics;

    @Inject
    public VerifyMePresenter(Analytics analytics) {
        this.mAnalytics=analytics;
    }


    public void sendAnalytics() {
        mAnalytics.sendEventActionWithMap(TrackingParam.MDM_SUCCESS_REFICA_VERIFY, null);
    }
}
