/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.di.components;

import javax.inject.Singleton;

import dagger.Component;
import za.co.nedbank.core.di.modules.ActivityModule;
import za.co.nedbank.core.di.modules.ApplicationModule;
import za.co.nedbank.core.di.modules.FragmentModule;
import za.co.nedbank.ui.NBApplication;
import za.co.nedbank.ui.di.modules.AppActivityModule;
import za.co.nedbank.ui.di.modules.AppApplicationModule;
import za.co.nedbank.ui.di.modules.AppFragmentModule;
import za.co.nedbank.ui.di.modules.AppServiceModule;

@Singleton
@Component(modules = {AppApplicationModule.class, ApplicationModule.class})
public interface AppApplicationComponent {
    AppActivityComponent plus(AppActivityModule appActivityModule, ActivityModule activityModule);

    AppFragmentComponent plus(AppFragmentModule appfragmentModule, FragmentModule fragmentModule);

    AppServiceComponent plus(AppServiceModule appServiceModule);

    void inject(NBApplication application);
}
