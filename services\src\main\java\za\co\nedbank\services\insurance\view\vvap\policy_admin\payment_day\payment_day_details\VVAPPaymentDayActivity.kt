package za.co.nedbank.services.insurance.view.vvap.policy_admin.payment_day.payment_day_details

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.google.android.material.snackbar.Snackbar
import za.co.nedbank.core.base.NBBaseActivity
import za.co.nedbank.core.tracking.InsuranceTrackingEvent.EVENT_MANAGE_POLICY_PAYMENT_DAY
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.utils.CollectionUtils
import za.co.nedbank.core.utils.FormattingUtil
import za.co.nedbank.core.utils.StringUtils
import za.co.nedbank.core.utils.ViewUtils
import za.co.nedbank.services.R
import za.co.nedbank.services.databinding.ActivityVvapPaymentDayDetailsBinding
import za.co.nedbank.services.di.ServicesDI
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.ParamKeys.PARAM_IS_PL_FLOW
import za.co.nedbank.services.insurance.view.other.helper.InsuranceHelperClass
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel
import za.co.nedbank.services.insurance.view.other.utils.InsuranceViewUtils
import za.co.nedbank.uisdk.component.CompatButton
import za.co.nedbank.uisdk.component.CompatTextViewEnhanced
import javax.inject.Inject

class VVAPPaymentDayActivity : NBBaseActivity(), VVAPPaymentDayView, View.OnClickListener {

    private lateinit var mToolbar: Toolbar
    private lateinit var mMainView: LinearLayout
    private lateinit var mEditButton: CompatButton
    private lateinit var mProgressBar: ProgressBar
    private lateinit var mAnnualInfoContainer: LinearLayout
    private lateinit var mCoverTypeTV: CompatTextViewEnhanced
    private lateinit var mPaymentDayTV: CompatTextViewEnhanced
    private lateinit var mProductNameTV: CompatTextViewEnhanced
    private lateinit var mCallUsAnnualTv: CompatTextViewEnhanced
    private lateinit var mPolicyNumberTV: CompatTextViewEnhanced
    private lateinit var mPaymentDayLabel: CompatTextViewEnhanced
    private lateinit var mPolicyStartDateTV: CompatTextViewEnhanced

    private var isGAPFlow = false
    private var isTLCFlow = false
    private var isPolicyPl = false
    private var isTyreRimFlow = false
    private var isEssentialFlow = false
    private var isPolicyFuneral = false
    private val binding get() = _binding
    private var isDentScratchFlow = false
    private var subProduct: String? = null
    private var isComprehensiveFlow = false
    private var mPaymentDate: String? = null
    private var mAccountViewModel = AccountViewModel()
    private var _binding: ActivityVvapPaymentDayDetailsBinding? = null

    @Inject
    lateinit var mPresenter: VVAPPaymentDayPresenter

    @Inject
    lateinit var mHelperErrorClass: InsuranceHelperClass

    override fun onCreate(savedInstanceState: Bundle?) {
        ServicesDI.getActivityComponent(this).inject(this)
        super.onCreate(savedInstanceState)
        _binding = ActivityVvapPaymentDayDetailsBinding.inflate(layoutInflater)
        setContentView(binding!!.root)
        mPresenter.bind(this)
        initializeVariables()
    }

    private fun getIntentData() {

        if (intent != null) {
            mAccountViewModel =
                intent.getParcelableExtra(InsuranceConstants.ParamKeys.PARAM_POLICY_DETAILS)!!
            isGAPFlow =
                intent.getBooleanExtra(InsuranceConstants.ParamKeys.PARAM_IS_VVAP_GAP_FLOW, false)
            isTLCFlow =
                intent.getBooleanExtra(InsuranceConstants.ParamKeys.PARAM_IS_VVAP_TLC_FLOW, false)
            isDentScratchFlow = intent.getBooleanExtra(
                InsuranceConstants.ParamKeys.PARAM_IS_VVAP_DENT_SCRATCH_FLOW, false
            )
            isTyreRimFlow = intent.getBooleanExtra(
                InsuranceConstants.ParamKeys.PARAM_IS_VVAP_TYRE_RIM_FLOW, false
            )
            isEssentialFlow = intent.getBooleanExtra(
                InsuranceConstants.ParamKeys.PARAM_IS_VVAP_ESSENTIAL_FLOW, false
            )
            isComprehensiveFlow = intent.getBooleanExtra(
                InsuranceConstants.ParamKeys.PARAM_IS_VVAP_COMPREHENSIVE_FLOW, false
            )
            subProduct =
                intent.getStringExtra(InsuranceConstants.ParamKeys.PARAM_VVAPS_SUB_PRODUCT_NAME)
            isPolicyFuneral = intent.getBooleanExtra(
                InsuranceConstants.ParamKeys.PARAM_IS_FUNERAL_FLOW, false
            )
            isPolicyPl = intent.getBooleanExtra(PARAM_IS_PL_FLOW, false)
        }
    }

    private fun initializeVariables() {

        //initializing all the variables
        mToolbar = binding!!.toolbar
        mMainView = binding!!.mainView
        mAnnualInfoContainer = binding!!.llAnnualInfoContainer
        mCallUsAnnualTv = binding!!.tvCallUsAnnual
        mEditButton = binding!!.editBtn
        mCoverTypeTV = binding!!.tvCoverType
        mProductNameTV = binding!!.tvProductName
        mProgressBar = binding!!.progressBarView
        mPolicyNumberTV = binding!!.tvPolicyNumber
        mPolicyStartDateTV = binding!!.tvPolicyStartDate

        mPaymentDayLabel = binding!!.tvPaymentDayLabel
        mPaymentDayTV = binding!!.tvPaymentDay
        // set toolbar
        initToolbar(mToolbar, true, false)
        mToolbar.title = getString(R.string.vvap_admin_payment_day_title)

        // set click listeners
        mEditButton.setOnClickListener(this)
        // get intent data
        getIntentData()

        // get banking details
        if (isPolicyPl) {
            mPresenter.paymentFrequencyAPI(
                mAccountViewModel.accountNumber!!,
                InsuranceConstants.ONE_STRING,
                false
            )
            createAnalyticSubProductForPL()
        } else {//VVAPS Products
            mPresenter.getPaymentDayDetails(
                mAccountViewModel.accountNumber!!,
                isGAPFlow,
                isTLCFlow,
                isDentScratchFlow,
                isTyreRimFlow,
                isEssentialFlow,
                isComprehensiveFlow
            )
        }
    }

    override fun onClick(view: View) {
        clickAnalyticsTag()
        mPresenter.editPaymentDayDetails(
            mAccountViewModel.accountNumber!!, mPaymentDate, subProduct, isPolicyPl, isPolicyFuneral
        )
    }

    private fun clickAnalyticsTag() {
        if (isPolicyPl) {
            mPresenter.sendEventWithProduct(
                EVENT_MANAGE_POLICY_PAYMENT_DAY,
                subProduct,
                TrackingEvent.ANALYTICS.PL_COMBO_TITLE
            )
        } else {
            mPresenter.sendEventWithProduct(
                EVENT_MANAGE_POLICY_PAYMENT_DAY, subProduct,
                TrackingEvent.ANALYTICS.VVAPS_TITLE
            )
        }
    }

    override fun showProgressBar(isVisible: Boolean) {
        if (isVisible) {
            ViewUtils.showViews(mProgressBar)
            window.setFlags(
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
            )
        } else {
            window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            ViewUtils.hideViews(mProgressBar)
        }
    }

    override fun setPaymentDate(isMonthly: Boolean, paymentDate: String?) {
        // set policy details
        mPolicyNumberTV.text = mAccountViewModel.accountNumber
        mProductNameTV.text = mAccountViewModel.accountType
        if (CollectionUtils.isNotEmpty(mAccountViewModel.productType)) {
            val stringBuilder = StringBuilder()
            for (cover in mAccountViewModel.productType!!) {
                stringBuilder.append(cover).append(StringUtils.NEW_LINE)
            }
            mCoverTypeTV.text = stringBuilder.substring(0, stringBuilder.toString().length - 1)
        }

        mPolicyStartDateTV.text = FormattingUtil.getFormattedDate(
            mAccountViewModel.policyStartDate,
            FormattingUtil.DATE_FORMAT_YYYY_MM_DD,
            FormattingUtil.DATE_FORMAT_DD_SPACE_MMMM_SPACE_YYYY
        )
        // payment day details

        setPaymentDay(paymentDate, isMonthly)

        //Show Main View after response
        ViewUtils.showViews(mMainView)
    }

    private fun setPaymentDay(paymentDate: String?, isMonthly: Boolean) {
        this.mPaymentDate = paymentDate
        if (isMonthly) {
            setMonthlyView(paymentDate)
        } else {
            setAnnualView(paymentDate)
        }
    }

    private fun setAnnualView(paymentDate: String?) {
        mPaymentDayLabel.text = getString(R.string.vvap_payment_next_payment_date)
        if (paymentDate?.isNotEmpty() == true) {
            mPaymentDayTV.text = FormattingUtil.getFormattedDate(
                paymentDate,
                FormattingUtil.DATE_FORMAT_YYYY_MM_DD,
                FormattingUtil.DATE_FORMAT_DD_SPACE_MMM_SPACE_YYYY
            )
            ViewUtils.showViews(mPaymentDayTV, mPaymentDayLabel)
        } else {
            ViewUtils.hideViews(mPaymentDayTV, mPaymentDayLabel)
        }
        ViewUtils.showViews(mAnnualInfoContainer)
        ViewUtils.hideViews(mEditButton)
        InsuranceViewUtils.setHelplineNumber(
            this,
            getString(R.string.vvap_payment_day_call_us),
            getString(R.string.help_number_policy),
            mCallUsAnnualTv
        )
    }

    private fun setMonthlyView(paymentDate: String?) {
        ViewUtils.hideViews(mAnnualInfoContainer)
        ViewUtils.showViews(mEditButton)
        if (paymentDate?.isNotEmpty() == true) {
            mPaymentDayTV.text = FormattingUtil.getDayOfMonthSuffix(
                FormattingUtil.getDayOfMonthFromDate(
                    paymentDate, FormattingUtil.DATE_FORMAT_YYYY_MM_DD
                )
            )
            ViewUtils.showViews(mPaymentDayTV, mPaymentDayLabel)
        } else {
            ViewUtils.hideViews(mPaymentDayTV, mPaymentDayLabel)
        }
    }


    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)

        if (intent != null && intent.hasExtra(InsuranceConstants.ParamKeys.PARAM_UPDATE_PAYMENT_DETAILS)) {
            val successMsg: String
            val actionMsg: String
            val paymentDay: String =
                intent.getStringExtra(InsuranceConstants.ParamKeys.PARAM_UPDATE_PAYMENT_DETAILS)!!
            var view = findViewById<View>(za.co.nedbank.core.R.id.coordinator_layout)
            if (view == null) {
                view = findViewById(android.R.id.content)
            }
            if (paymentDay.isNotEmpty()) {
                setPaymentDay(paymentDay, true)
                successMsg = getString(R.string.vvap_payment_day_success)
                actionMsg = getString(R.string.snackbar_action_done)
            } else {
                successMsg = getString(R.string.vvap_payment_day_failure)
                actionMsg = getString(R.string.snackbar_action_ok)
            }

            Snackbar.make(
                view, successMsg, Snackbar.LENGTH_INDEFINITE
            ).setAction(actionMsg) { }.setActionTextColor(
                ContextCompat.getColor(
                    this, R.color.overview_everyday_pattern_2
                )
            ).apply {
                (view.findViewById<View?>(R.id.snackbar_text) as? TextView?)?.isSingleLine = false
            }.show()
        }

    }

    override fun showAPIError() {
        mHelperErrorClass.showOkayErrorScreen { mPresenter.moveToProductListScreen() }
    }

    private fun createAnalyticSubProductForPL() {
        if (CollectionUtils.isNotEmpty(mAccountViewModel.productType)) {
            val stringBuilder = StringBuilder()
            for (cover in mAccountViewModel.productType!!) {
                stringBuilder.append(cover).append(StringUtils.COMA)
            }
            subProduct = stringBuilder.substring(0, stringBuilder.toString().length - 1)
        }
    }

}