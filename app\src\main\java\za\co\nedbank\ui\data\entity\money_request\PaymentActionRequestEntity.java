package za.co.nedbank.ui.data.entity.money_request;

import com.squareup.moshi.Json;

public class PaymentActionRequestEntity {

    @Json(name = "paymentBatchId")
    private long paymentBatchId;

    @<PERSON><PERSON>(name = "processAmount")
    private double processAmount;

    @<PERSON><PERSON>(name = "payerAccountNumber")
    private String payerAccountNumber;

    @J<PERSON>(name = "payerAccountType")
    private String payerAccountType;

    @<PERSON><PERSON>(name = "payerDescription")
    private String payerDescription;

    @<PERSON>son(name = "paymentRequestId")
    private long paymentRequestId;

    @<PERSON><PERSON>(name = "paymentRequestAction")
    private String paymentRequestAction;

    public void setPaymentBatchId(long paymentBatchId) {
        this.paymentBatchId = paymentBatchId;
    }

    public void setProcessAmount(double processAmount) {
        this.processAmount = processAmount;
    }

    public void setPayerAccountNumber(String payerAccountNumber) {
        this.payerAccountNumber = payerAccountNumber;
    }

    public void setPayerAccountType(String payerAccountType) {
        this.payerAccountType = payerAccountType;
    }

    public void setPayerDescription(String payerDescription) {
        this.payerDescription = payerDescription;
    }

    public void setPaymentRequestId(long paymentRequestId) {
        this.paymentRequestId = paymentRequestId;
    }

    public void setPaymentRequestAction(String paymentRequestAction) {
        this.paymentRequestAction = paymentRequestAction;
    }

}
