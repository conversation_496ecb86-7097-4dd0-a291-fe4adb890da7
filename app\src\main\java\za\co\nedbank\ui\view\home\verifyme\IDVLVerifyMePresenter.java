package za.co.nedbank.ui.view.home.verifyme;

import java.util.HashMap;
import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.ClientTypeUtil;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.UnboxingUtil;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.nid_sdk.main.views.pin_biometric.PinBiometricFlows;

public class IDVLVerifyMePresenter extends NBBasePresenter<IDVLVerifyMeView> {

    private final NavigationRouter mNavigationRouter;
    private final ApplicationStorage mMemoryApplicationStorage;
    private final ApplicationStorage mApplicationStorage;
    private final Analytics mAnalytics;
    private FeatureSetController featureSetController;
    private final ClientTypeUtil mClientTypeUtil;
    @Inject
    public IDVLVerifyMePresenter(final NavigationRouter navigationRouter,
                                 @Named("memory") ApplicationStorage memoryApplicationStorage,
                                 final ApplicationStorage applicationStorage,
                                 Analytics analytics,
                                 final FeatureSetController featureSetController,
                                 final ClientTypeUtil clientTypeUtil) {
        this.mNavigationRouter = navigationRouter;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.mAnalytics = analytics;
        this.featureSetController = featureSetController;
        this.mClientTypeUtil = clientTypeUtil;
        this.mApplicationStorage = applicationStorage;
    }

    public void navigateToNextScreen(PinBiometricFlows pinBiometricFlow, boolean fromSecondLoginScreen, boolean isFingerPrintAltered) {
        NavigationTarget navigationTarget;
        if (mMemoryApplicationStorage.getObject(StorageKeys.FRAUD_AWARENESS_NOTICE_LIST) == null) {
            navigationTarget = UnboxingUtil.
                        checkUnboxingFlow(featureSetController,
                                APIInformation.getInstance(),
                                mClientTypeUtil,
                                mApplicationStorage);
        } else {
            navigationTarget = NavigationTarget.to(NavigationTarget.FRAUD_AWARENESS_CAMPAIGN);
        }
        navigationTarget.withParam(Constants.BUNDLE_KEYS.PIN_BIOMETRIC_FLOW, pinBiometricFlow);
        navigationTarget.withParam(NavigationTarget.FROM_SECOND_LOGIN_SCREEN, fromSecondLoginScreen)
                .withParam(Constants.BUNDLE_KEYS.IS_FINGERPRINT_ALTERED, isFingerPrintAltered);
        mNavigationRouter.navigateTo(navigationTarget);
        view.close();
    }

    public void sendPageAnalytics(){
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setEntryPoint(TrackingParam.VAL_APP_REVISIT);
        mAnalytics.sendEventStateWithMap(EnrollV2TrackingEvent.IDVL.PAGE_NAME_IDV_PROMPT, cdata);
    }

    public void trackActionVerifyMe(){
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setScreenName(TrackingParam.VERIFY_YOUR_IDENTITY_PROMPT);
        adobeContextData.setFeature(TrackingParam.VAL_REGISTRATION);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_LOGON_AND_REGISTRATION);
        adobeContextData.setEntryPoint(TrackingParam.VAL_APP_REVISIT);

        mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.IDVL.IDV_VERIFY_ME, cdata);
    }

    public void trackActionVerifyMeClose(){
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setScreenName(TrackingParam.VERIFY_YOUR_IDENTITY_PROMPT);

        mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.IDVL.IDV_VERIFY_ME_CLOSE, cdata);
    }

    public void trackActionVerifyMeNotNow(String yesorno){
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setScreenName(TrackingParam.VERIFY_YOUR_IDENTITY_PROMPT);
        adobeContextData.setContext10(String.format(TrackingParam.SKIP,yesorno));
        adobeContextData.setContext10Count();

        mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.IDVL.IDV_VERIFY_ME_NOT_NOW, cdata);
    }
}
