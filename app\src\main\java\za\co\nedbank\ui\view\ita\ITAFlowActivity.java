package za.co.nedbank.ui.view.ita;

import android.os.Bundle;

import androidx.annotation.Nullable;

import com.entersekt.sdk.Auth;
import com.entersekt.sdk.push.config.PushConfigType;
import com.google.firebase.FirebaseOptions;
import com.huawei.agconnect.config.AGConnectServicesConfig;

import org.greenrobot.eventbus.EventBus;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.Seconds;

import javax.inject.Inject;

import za.co.nedbank.core.AuthManager;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.utils.XmsApiCheck;
import za.co.nedbank.databinding.ActivityItaFlowBinding;
import za.co.nedbank.nid_sdk.main.interaction.TransaktConfig;
import za.co.nedbank.ui.di.AppDI;

public class ITAFlowActivity extends NBBaseActivity implements ITAFlowView {

    @Inject
    ITAFlowPresenter itaFlowPresenter;
    private String itaActionType = StringUtils.EMPTY_STRING;
    private boolean flowForLoggedOut;
    private String hmsToken;
    private ActivityItaFlowBinding binding;

    public ITAFlowActivity() {
        isITAFlowStarted = true;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        itaFlowPresenter.bind(this);
        itaFlowPresenter.setFlowStatus(true);
        binding = ActivityItaFlowBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        showHideCountdownTimer(Boolean.FALSE);
        itaFlowPresenter.handleAuthIfAny();
    }

    @Override
    public String getITAActionType() {
        return itaActionType;
    }

    @Override
    public void setITAActionType(String itaActionType) {
        this.itaActionType = itaActionType;
    }

    @Override
    public boolean isFlowForLoggedOut() {
        return flowForLoggedOut;
    }

    @Override
    public void setFlowForLoggedOut(boolean flowForLoggedOut) {
        this.flowForLoggedOut = flowForLoggedOut;
    }

    @Override
    public void showCountingProgress(int value) {
        int time = value;
        if (time < 0) time = 0;
        binding.tvItaTime.setText(getString(za.co.nedbank.nid_sdk.enrolment_sdk.R.string.ita_processing_time, time));
        binding.progressBar.setProgress(time);
    }


    @Override
    public void showHideCountdownTimer(boolean isShow) {
        if (isShow) {
            ViewUtils.showViews(binding.tvItaTime, binding.progressBar);
        } else {
            ViewUtils.hideViews(binding.tvItaTime, binding.progressBar);
        }
    }

    @Override
    public Auth getAuth() {
        return AuthManager.getInstance().getAuth();
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        itaFlowPresenter.setPaymentViaTrustedDevice(false);
        itaFlowPresenter.setFlowStatus(false);
        itaFlowPresenter.unbind();
        super.onDestroy();
    }

    @Override
    public TransaktConfig getTransaktConfig() {
        TransaktConfig transaktConfig = new TransaktConfig();
        if (XmsApiCheck.isHmsApiPreferred(this)) {
            if (hmsToken == null) hmsToken = itaFlowPresenter.getHMSToken();
            transaktConfig.setCloudMessagingToken(hmsToken);
            transaktConfig.setSenderId(getSenderIdOfHuawei());
            transaktConfig.setPushConfigType(PushConfigType.HUAWEI_PUSH_KIT);
        } else {
            transaktConfig.setCloudMessagingToken(itaFlowPresenter.getFBToken());
            transaktConfig.setSenderId(FirebaseOptions.fromResource(getApplicationContext()).getGcmSenderId());
            transaktConfig.setPushConfigType(PushConfigType.FIREBASE_CLOUD_MESSAGING);
        }
        return transaktConfig;
    }

    @Override
    public int getTimeDifference(Auth auth) {
        DateTime startTime = new DateTime(auth.getTimestamp(), DateTimeZone.getDefault());
        return Seconds.secondsBetween(startTime, DateTime.now()).getSeconds();
    }

    public String getSenderIdOfHuawei() {
        return AGConnectServicesConfig.fromContext(this)
                .getString("client/app_id");
    }
}
