/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.di.modules;

import android.app.Application;
import android.app.NotificationManager;
import android.content.Context;

import javax.inject.Named;
import javax.inject.Singleton;

import dagger.Module;
import dagger.Provides;
import za.co.nedbank.core.domain.repository.investmentonline.IInvestmentsRepository;
import za.co.nedbank.core.domain.repository.investmentonline.InvestmentsRepository;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.enroll_v2.data.apply_flow_sbs.NBApplyFlowSbsRepository;
import za.co.nedbank.enroll_v2.data.card_delivery.CardDeliveryRepository;
import za.co.nedbank.enroll_v2.data.fica.FicaRepository;
import za.co.nedbank.enroll_v2.domain.repository.IApplyFlowSbsRepository;
import za.co.nedbank.enroll_v2.domain.repository.ICardDeliveryRepository;
import za.co.nedbank.enroll_v2.domain.repository.IFicaRepository;
import za.co.nedbank.enroll_v2.view.applications.repository.ApplicationListRepository;
import za.co.nedbank.enroll_v2.view.applications.repository.IApplicationListRepository;
import za.co.nedbank.enroll_v2.view.ntf.etetoken.repository.EteTokenRepo;
import za.co.nedbank.enroll_v2.view.ntf.etetoken.repository.NBNTFRepository;
import za.co.nedbank.loans.homeloan.domain.repository.IInsightsMaxLimitRepository;
import za.co.nedbank.loans.homeloan.entity.repository.NBInsightsMaxLimitRepository;
import za.co.nedbank.services.data.accounts.NBAccountsRepository;
import za.co.nedbank.services.data.cards.NBCardsRepository;
import za.co.nedbank.services.data.debit.NBDebitOrderRepository;
import za.co.nedbank.services.data.lifestyle.repository.LifestyleDashboardRepository;
import za.co.nedbank.services.domain.mapper.MetaDataEntityToDataMapper;
import za.co.nedbank.services.domain.repository.AccountsRepository;
import za.co.nedbank.services.domain.repository.CardsRepository;
import za.co.nedbank.services.domain.repository.DebitOrderRepository;
import za.co.nedbank.services.domain.repository.ILifestyleDashboardRepository;
import za.co.nedbank.services.insurance.domain.repository.funeral.IInsuranceRepository;
import za.co.nedbank.services.insurance.entity.repository.generic.NBInsuranceRepository;
import za.co.nedbank.ui.app_shortcut.data.DynamicAppShortcutRepository;
import za.co.nedbank.ui.app_shortcut.domain.DynamicShortcutRepository;
import za.co.nedbank.ui.data.cache.GetRecipientContactsStoreCache;
import za.co.nedbank.ui.domain.mapper.pop.TransactionProviderEntityToDataMapper;
import za.co.nedbank.ui.domain.repository.IGetUserContactsCache;
import za.co.nedbank.ui.domain.repository.closed_account.IClosedAccountsRepository;
import za.co.nedbank.ui.domain.repository.closed_account.NBClosedAccountsRepository;
import za.co.nedbank.ui.domain.repository.pop.NBShareProofOfPaymentRepository;
import za.co.nedbank.ui.domain.repository.pop.NBTransactionRepository;
import za.co.nedbank.ui.domain.repository.pop.ShareProofOfPaymentRepository;
import za.co.nedbank.ui.domain.repository.pop.TransactionRepository;
import za.co.nedbank.ui.notifications.DefaultNotificationBuilder;
import za.co.nedbank.ui.notifications.NotificationBuilder;
import za.co.nedbank.ui.notifications.NotificationItemResolver;
import za.co.nedbank.ui.notifications.PushNotificationItemResolver;

@Module
public class AppApplicationModule {

    private final Application application;

    public AppApplicationModule(final Application application) {
        this.application = application;
    }

    @Provides
    @Singleton
    public AccountsRepository providesAccoountsRepository(NetworkClient networkClient, ApplicationStorage applicationStorage, @Named("memory")ApplicationStorage memoryApplicationStorage) {
        return new NBAccountsRepository(networkClient, applicationStorage,memoryApplicationStorage);
    }

    @Provides
    @Singleton
    CardsRepository providesCardsRepository(final NetworkClient networkClient, final MetaDataEntityToDataMapper metaDataEntityToDataMapper) {
        return new NBCardsRepository(networkClient, metaDataEntityToDataMapper);
    }

    @Provides
    @Singleton
    public DebitOrderRepository provideDebitOrdersRepository(final NetworkClient networkClient) {
        return new NBDebitOrderRepository(networkClient);
    }

    @Provides
    IInsightsMaxLimitRepository provideInsightsMaxLimit(NBInsightsMaxLimitRepository nbInsightsMaxLimitRepository) {
        return nbInsightsMaxLimitRepository;
    }

    @Provides
    @Singleton
    public TransactionRepository provideTransactionRepository(final TransactionProviderEntityToDataMapper transactionProviderEntityToDataMapperfinal, final NetworkClient networkClient) {
        return new NBTransactionRepository(transactionProviderEntityToDataMapperfinal, networkClient);
    }

    @Provides
    @Singleton
    public DynamicShortcutRepository provideUserInfoRepository(Context context) {
        return new DynamicAppShortcutRepository(context);
    }

    @Provides
    @Singleton
    IGetUserContactsCache provideGetUserContactsCache() {
        return new GetRecipientContactsStoreCache();
    }

    @Provides
    @Singleton
    public IInvestmentsRepository provideInvestmentsRepository(final NetworkClient networkClient) {
        return new InvestmentsRepository(networkClient);
    }

    @Provides
    @Singleton
    public NotificationItemResolver providePushNotificationItemResolver(NotificationManager notificationManager) {
        return new PushNotificationItemResolver(notificationManager);
    }

    @Provides
    @Singleton
    public NotificationBuilder provideNotificationBuilder() {
        return new DefaultNotificationBuilder();
    }

    @Provides
    @Singleton
    public NotificationManager provideNotificationManager(Context context) {
        return (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
    }

    @Provides
    @Singleton
    IFicaRepository provideFicaRepository(FicaRepository ficaRepository) {
        return ficaRepository;
    }

    @Provides
    @Singleton
    public ShareProofOfPaymentRepository provideShareProofOfPaymentRepository( final NetworkClient networkClient) {
        return new NBShareProofOfPaymentRepository(networkClient);
    }



    @Provides
    @Singleton
    public IClosedAccountsRepository provideIClosedAccountsRepository(final NetworkClient networkClient) {
        return new NBClosedAccountsRepository(networkClient);
    }

    @Provides
    @Singleton
    IInsuranceRepository provideInsuranceRepository(final NetworkClient networkClient) {
        return new NBInsuranceRepository(networkClient);
    }

    @Provides
    @Singleton
    ICardDeliveryRepository provideCardDeliveryRepository(CardDeliveryRepository cardDeliveryRepository) {
        return cardDeliveryRepository;
    }

    @Provides
    @Singleton
    IApplyFlowSbsRepository provideApplyFlowSbsRepository(final NBApplyFlowSbsRepository applyFlowSbsRepository) {
        return applyFlowSbsRepository;
    }

    @Provides
    @Singleton
    ILifestyleDashboardRepository provideLifestyleDashboardRepository(final NetworkClient networkClient) {
        return new LifestyleDashboardRepository(networkClient);
    }

    @Provides
    @Singleton
    EteTokenRepo provideEteTokenRepo (final NetworkClient networkClient) {
        return new NBNTFRepository(networkClient);
    }

    @Provides
    @Singleton
    IApplicationListRepository provideApplicationListRepository  (final NetworkClient networkClient) {
        return new ApplicationListRepository(networkClient);
    }
}
