/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home;

/**
 * Created by pi<PERSON><PERSON>gu<PERSON>01 on 8/3/2017.
 */

//TODO

/**
 * this interface will be used as callback to parent view to notify events happening in this widget.
 * This will be used to on "Link nedbank account" button to simulate account link success and update
 * UI accordingly. This click will be replaced by an API call in future and this interface will be
 * removed
 */
public interface IEventListener {
    int EVENT_LINK_ACCOUNT_BUTTON_CLICKED = 1;

    void onEvent(int event);
}
