package za.co.nedbank.ui.view.home.cms_content;

import static za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.CAN_HIDE_TOOLBAR;
import static za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.ENABLE_JAVA_SCRIPT;
import static za.co.nedbank.enroll_v2.Constants.BundleKeys.CMS_URL;
import static za.co.nedbank.enroll_v2.Constants.BundleKeys.FROM_WIDGET;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebView;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.di.CoreDI;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.terms.details.NBWebViewClient;
import za.co.nedbank.core.view.terms.details.WebViewProgressListener;
import za.co.nedbank.databinding.ActivityCmsMediaContentBinding;
import za.co.nedbank.ui.di.AppDI;

public class CMSMediaContentActivity extends NBBaseActivity implements WebViewProgressListener, CMSMediaContentView {

    @Inject
    CMSMediaContentPresenter cmsMediaContentPresenter;

    private NBWebViewClient webViewClient;

    private ValueCallback<Uri[]> mFilePathCallback;

    private static String WEB_HANDLER = "WebHandler";

    private final int PICK_IMAGE_OR_PDF = 110;
    private boolean isJavaScriptEnabled = false;
    private ActivityCmsMediaContentBinding binding;
    private boolean isFromWrongInfoScreen = false;

    @Override
    protected void onCreate(@Nullable final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCmsMediaContentBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        CoreDI.getActivityComponent(this).inject(this);
        AppDI.getActivityComponent(this).inject(this);

        initToolbar(binding.toolbar, true, getString(R.string.title_detail));
        readIntent();
        initBrowser();
        hideToolbar();
    }

    private void readIntent() {
        isJavaScriptEnabled = IntentUtils.getBooleanValue(getIntent(), ENABLE_JAVA_SCRIPT,false);
        isFromWrongInfoScreen=getIntent().getBooleanExtra(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.IS_FROM_USER_WRONG_INFO,false);
    }

    @Override
    protected void onResume() {
        super.onResume();
        cmsMediaContentPresenter.bind(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (webViewClient != null) {
            webViewClient.setProgressListener(null);
        }
        cmsMediaContentPresenter.unbind();
    }

    private void hideToolbar() {
        if (getIntent() != null && getIntent().getBooleanExtra(CAN_HIDE_TOOLBAR, false)) {
            ViewUtils.hideViews(binding.toolbar);
        }
    }


    @SuppressLint("JavascriptInterface")
    @Override
    public void initWebView() {
        webViewClient = new NBWebViewClient();
        webViewClient.setProgressListener(this);
        binding.webviewCms.addJavascriptInterface(this, WEB_HANDLER);
        binding.webviewCms.getSettings().setJavaScriptEnabled(isJavaScriptEnabled);
        binding.webviewCms.getSettings().setDomStorageEnabled(true);
        binding.webviewCms.setWebViewClient(webViewClient);
        binding.webviewCms.loadUrl(getContentUrl());
        binding.webviewCms.setWebChromeClient(new WebClient());
    }

    public void initBrowser() {
        IntentUtils.openDefaultBrowser(this, getContentUrl());
        finish();
    }

    @Override
    public void openGallery() {
        IntentUtils.openImageAndPdfInGallery(this, PICK_IMAGE_OR_PDF);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == PICK_IMAGE_OR_PDF) {
            if (resultCode == RESULT_OK) {
                if (data != null) {
                    Uri imageUri = data.getData();
                    // send back URL to web view
                    if (mFilePathCallback != null)
                        mFilePathCallback.onReceiveValue(new Uri[]{imageUri});
                }
            } else {
                if (mFilePathCallback != null)
                    mFilePathCallback.onReceiveValue(new Uri[]{});
            }
        }
    }

    private class WebClient extends WebChromeClient {
        // THis method call whenever we click on brows file option from html.
        // This will only if input type is file type in HTML page.
        @Override
        public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
            mFilePathCallback = filePathCallback;
            cmsMediaContentPresenter.checkPermissions();
            return true;
        }
    }


    private String getContentUrl() {
        String url = StringUtils.EMPTY_STRING;
        if (getIntent() != null) {
            url = getIntent().getStringExtra(CMS_URL);
        }
        return url;
    }

    @Override
    public boolean isFromWidget() {
        boolean fromWidget = false;
        if (getIntent() != null) {
            fromWidget = getIntent().getBooleanExtra(FROM_WIDGET, false);
        }
        return fromWidget;
    }

    @JavascriptInterface
    public void postMessage(final String value) {
        if (getString(R.string.close_handler).equalsIgnoreCase(value)) {
            if (isFromWrongInfoScreen) {
                cmsMediaContentPresenter.navigateToOverview();
            } else {
                finish();
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(menuItem);
    }

    @Override
    public void permissionDeny() {
        mFilePathCallback.onReceiveValue(new Uri[]{});
    }

    @Override
    public void setProgressVisible(boolean visible) {
        binding.webviewProgressCms.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    @Override
    public void receiveError(WebResourceError error) {
        binding.webviewProgressCms.setVisibility(View.GONE);
    }

    @Override
    public void finishActivity() {
        ActivityCompat.finishAffinity(this);
    }

    @Override
    public void onBackPressed() {
        if(isFromWrongInfoScreen){
            cmsMediaContentPresenter.navigateToOverview();
        }else {
            super.onBackPressed();
        }
    }
}
