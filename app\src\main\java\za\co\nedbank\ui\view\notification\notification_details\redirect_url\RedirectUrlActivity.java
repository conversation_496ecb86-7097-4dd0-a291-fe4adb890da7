package za.co.nedbank.ui.view.notification.notification_details.redirect_url;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.WebResourceError;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.base.dialog.IDialog;
import za.co.nedbank.core.base.dialog.UserConsentDialog;
import za.co.nedbank.core.databinding.ActivityRedirectUrlBinding;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.view.terms.details.NBWebViewClient;
import za.co.nedbank.core.view.terms.details.WebViewProgressListener;
import za.co.nedbank.ui.di.AppDI;

public class RedirectUrlActivity extends NBBaseActivity implements WebViewProgressListener, IDialog, NBBaseView {

    String mRedirectUrl;
    boolean isAvoAppUrl;
    private String title;
    NBWebViewClient webViewClient;
    private static String webHandler = "WebHandler";
    private ActivityRedirectUrlBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityRedirectUrlBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        receiveBundle();
        if(title != null) {
            initToolbar(binding.tbRedirectUrl, false, title);
        } else {
            initToolbar(binding.tbRedirectUrl, false, false);
        }
        initWebView();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(za.co.nedbank.payment.R.menu.menu_close_dark, binding.tbRedirectUrl.getMenu());
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == za.co.nedbank.payment.R.id.menu_item_close) {
            if(title == null) {
                UserConsentDialog.getInstance(getString(za.co.nedbank.services.R.string.are_you_sure_you_want_to_leave), getString(za.co.nedbank.services.R.string.do_if_you_leave_now_progress),
                        getString(za.co.nedbank.services.R.string.stay), getString(za.co.nedbank.services.R.string.leave)).show(getSupportFragmentManager(), UserConsentDialog.TAG);
                return true;
            } else {
                close();
            }
        }
        return super.onOptionsItemSelected(menuItem);
    }

    private void initWebView() {
        if(mRedirectUrl!=null) {
            webViewClient = new NBWebViewClient();
            webViewClient.setProgressListener(this);
            binding.webview.getSettings().setLoadsImagesAutomatically(true);
            binding.webview.addJavascriptInterface(this, webHandler);
            binding.webview.getSettings().setJavaScriptEnabled(true);
            binding.webview.getSettings().setDomStorageEnabled(true);
            binding.webview.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);
            binding.webview.setWebViewClient(webViewClient);
            binding.webview.loadUrl(mRedirectUrl);
            binding.webview.setDownloadListener((url, userAgent, contentDisposition, mimetype, contentLength) -> {
                Intent i = new Intent(Intent.ACTION_VIEW);
                i.setData(Uri.parse(url));
                startActivity(i);
            });
        }
    }


    @JavascriptInterface
    public void postMessage(final String value) {
        if (getString(R.string.close_handler).equalsIgnoreCase(value)) {
            finish();
        }
    }

    @Override
    public void setProgressVisible(boolean visible) {
        if (!isAvoAppUrl) {
            binding.webviewProgress.setVisibility(visible ? View.VISIBLE : View.GONE);
        }
    }

    @Override
    public void receiveError(WebResourceError error) {
        binding.webviewProgress.setVisibility(View.GONE);
    }


    private void receiveBundle() {
        if (getIntent() != null && getIntent().getExtras() != null) {
            mRedirectUrl = getIntent().getExtras().getString(NotificationConstants.EXTRA.URL);
            isAvoAppUrl = getIntent().getExtras().getBoolean(NotificationConstants.EXTRA.IS_AVO_APP_URL, false);
            title = getIntent().getExtras().getString(NotificationConstants.EXTRA.SCREEN_TITLE);
        }
    }

    @Override
    public void onPositiveButtonClick() {
        //Do nothing...
    }

    @Override
    public void onNegativeButtonClick() {
        close();
    }
}
