package za.co.nedbank.ui.view.home.odd_restriction;


import android.content.Intent;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.core.content.ContextCompat;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.ActivityOddRestrictionErrorBinding;
import za.co.nedbank.ui.di.AppDI;


public class OddRestrictionErrorActivity extends NBBaseActivity implements OddRestrictionErrorView {

    public static final String ACTION_KEY = "action";

    @Inject
    OddRestrictionErrorPresenter presenter;
    private OddRestrictionErrorType type;
    private ActivityOddRestrictionErrorBinding binding;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityOddRestrictionErrorBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        presenter.bind(this);
        getBundleData();
        setImage();
        binding.btnBranchLocator.setOnClickListener(v -> onBranchLocatorClicked());
        binding.ivBlock.setOnClickListener(v -> onExitClicked());
    }

    private void setImage() {
        if (type == OddRestrictionErrorType.ODD_VERIFYING || type == OddRestrictionErrorType.JURISTIC_ODD_VERIFYING) {
            binding.ivBlock.setImageResource(za.co.nedbank.profile.R.drawable.ic_under_review);
        } else {
            binding.ivBlock.setImageResource(za.co.nedbank.profile.R.drawable.ic_icon_no_denied);
        }
    }

    public void onBranchLocatorClicked() {
        presenter.sendBranchLocatorTags(type);
    }

    public void onExitClicked() {
        presenter.sendCloseTags(type);
        finish();
    }

    private void getBundleData() {
        Intent intent = getIntent();
        if (null != intent) {
            type = (OddRestrictionErrorType) intent.getSerializableExtra(Constants.TYPE);
            prepareTncText();
        }
    }

    private void prepareTncText() {
        String tncBaseText = "Please call ";
        String remaining = "";

        if (type == OddRestrictionErrorType.JURISTIC) {
            binding.tvMsgTitle.setText(getString(R.string.we_couldn_t_confirm_your_profile_details));
            binding.tvMsgTitle.setContentDescription(getString(R.string.we_couldn_t_confirm_your_profile_details));
            remaining = " or visit your nearest branch with your FICA documents to confirm your profile details.\n\nUntil then, your transactions will be limited.";
        } else {
            if (type == OddRestrictionErrorType.REASON_1015) {
                ViewUtils.showViews(binding.tvMsg);
            }
            remaining = " or visit your nearest branch with your ID/passport to update your personal details.\n\nUntil then, your transactions will be limited.";
        }

        if (type == OddRestrictionErrorType.ODD_VERIFYING) {
            tncBaseText = "Until then, your transactions will be limited.\n\nIf you need assistance, \n Please call ";
            remaining = " or visit your nearest branch with your ID/passport.";
            binding.tvMsgTitle.setText("We are still verifying \nyour details");
            binding.tvMsgTitle.setContentDescription("We are still verifying your details");
        }

        if (type == OddRestrictionErrorType.JURISTIC_ODD_VERIFYING) {
            tncBaseText = "Until then, your transactions will be limited.\n\nIf you need assistance, \n Please call ";
            remaining = " or visit your nearest branch with your FICA documents.";
            binding.tvMsgTitle.setText("We are still verifying \nyour details");
            binding.tvMsgTitle.setTypeface(null, Typeface.BOLD);
            binding.tvMsgTitle.setContentDescription("We are still verifying your details");
        }

        String tncLink = "0800 555 111";

        SpannableString spanBaseTxt = new SpannableString(tncBaseText);
        SpannableString spanLink = new SpannableString(tncLink);

        final int linkColorPrimary = ContextCompat.getColor(this, R.color.colorPrimary);

        ClickableSpan clickableSpanInfo = new ClickableSpan() {
            @Override
            public void onClick(final View view) {
                Intent intentData = new Intent(Intent.ACTION_DIAL);
                intentData.setData(Uri.parse("tel:0800 555 111"));
                startActivity(intentData);
            }

            @Override
            public void updateDrawState(TextPaint textPaint) {
                textPaint.setColor(linkColorPrimary);
            }
        };
        spanLink.setSpan(clickableSpanInfo, 0, spanLink.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        binding.tvMsg1.setText(TextUtils.concat(spanBaseTxt, spanLink, remaining));
        binding.tvMsg1.setMovementMethod(LinkMovementMethod.getInstance());
    }

    @Override
    public void onBranchSuccess() {
        Intent i = getIntent();
        i.putExtra(ACTION_KEY, "1");
        setResult(RESULT_OK, i);
        finish();
    }

}

