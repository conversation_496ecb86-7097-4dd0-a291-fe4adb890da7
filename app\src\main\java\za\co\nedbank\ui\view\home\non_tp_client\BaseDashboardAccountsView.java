package za.co.nedbank.ui.view.home.non_tp_client;

import android.content.res.Resources;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;

public interface BaseDashboardAccountsView extends NBBaseView {
    void onUnreadChatEvent(UnreadChatEvent unreadChatEvent);

    void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent);

    void setPreferredName(String name);

    void handlePreferredNameError();

    void setCustomerName(String customerUserName);

    void setUserInfo(UserDetailViewModel userDetailViewModel, boolean updateCustomerName);

    void showError(String message);

    void showEmptyUserView(String msg);

    boolean isFeatureDisabled(String feature);

    void receiveNotificationCount(int notificationCount);

    boolean canTransact();

    boolean isSAResident();

    String getFicaStatus();

    void startBrowser(String url);

    void updateMediaCardViewModel(MediaCardViewModel mediaCardViewModel);

    Resources getResources();


    void onServerStateChanged(boolean chatConnected);
}
