/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import org.greenrobot.eventbus.EventBus;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.ActivityMoneyRequestSuccessBinding;
import za.co.nedbank.ui.di.AppDI;

/**
 * Created by sandip.lawate on 2/15/2018.
 */
public class MoneyRequestSuccessActivity extends NBBaseActivity implements MoneyRequestSuccessView {

    @Inject
    MoneyRequestSuccessPresenter mMoneyRequestSuccessPresenter;
    private ActivityMoneyRequestSuccessBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityMoneyRequestSuccessBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        String referenceNumber = getMoneyRequestReferenceNumber();
        String successMessage = String.format("%s%s<b>%s</b>%s%s%s%s%s.", getString(R.string.request_sent_to), StringUtils.SPACE, getRecipientName(), StringUtils.SPACE, StringUtils.NEW_LINE, getString(R.string.unique_request_code_message), StringUtils.SPACE, referenceNumber);
        binding.tvSuccessMessage.setText(Html.fromHtml(successMessage));
        if (TextUtils.isEmpty(referenceNumber)) {
            showFailureView();
        } else {
            showSuccessView();
        }
        binding.btnDoneNow.setOnClickListener(v -> onDoneButtonClick());
        binding.ivCancelTerms.setOnClickListener(v -> mMoneyRequestSuccessPresenter.navigateToDashboardScreen());
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMoneyRequestSuccessPresenter.bind(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        mMoneyRequestSuccessPresenter.unbind();
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
    }

    @Override
    public void showSuccessView() {
        ViewUtils.hideViews(binding.moneyRequestFailureView);
        ViewUtils.showViews(binding.moneyRequestSuccessView);
        ViewUtils.showViews(binding.btnDoneNow);
        ViewUtils.hideViews(binding.ivCancelTerms);
    }

    @Override
    public void showFailureView() {
        ViewUtils.hideViews(binding.moneyRequestSuccessView);
        ViewUtils.showViews(binding.moneyRequestFailureView);
        ViewUtils.showViews(binding.ivCancelTerms);
        ViewUtils.hideViews(binding.btnDoneNow);
    }

    @Override
    public String getMoneyRequestReferenceNumber() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_REFERENCE_NUMBER)) {
            return bundle.getString(NavigationTarget.PARAM_REFERENCE_NUMBER);
        }

        return null;
    }

    @Override
    public String getRecipientName() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_RECIPIENT_NAME)) {
            return bundle.getString(NavigationTarget.PARAM_RECIPIENT_NAME);
        }

        return null;
    }

    @Override
    public void onBackPressed() {
        mMoneyRequestSuccessPresenter.navigateToDashboardScreen();
    }

    void onDoneButtonClick() {
        mMoneyRequestSuccessPresenter.navigateToDashboardScreen();
        finish();
    }
}
