/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.edit_recipient;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.Map;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.dialog.IDialog;
import za.co.nedbank.core.base.dialog.UserConsentDialog;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.mapper.ElectricityMeterViewDataModel;
import za.co.nedbank.core.view.mapper.RecipientViewModelToEntityMapper;
import za.co.nedbank.core.view.recipient.CreditCardViewDataModel;
import za.co.nedbank.core.view.recipient.EmailViewDataModel;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.databinding.ActivityEditRecipientBinding;
import za.co.nedbank.payment.common.view.tracking.PaymentsTracking;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.add_recipient.BankAccountAdapter;
import za.co.nedbank.ui.view.home.add_recipient.BaseRecipientActivity;
import za.co.nedbank.ui.view.home.add_recipient.CreditCardAdapter;
import za.co.nedbank.ui.view.home.add_recipient.ElectricityMeterAdapter;
import za.co.nedbank.ui.view.home.add_recipient.EmailAdapter;
import za.co.nedbank.ui.view.home.add_recipient.MobileNumberAdapter;
import za.co.nedbank.ui.view.home.add_recipient.ShapIDAdapter;
import za.co.nedbank.uisdk.component.CompatEditText;
import za.co.nedbank.uisdk.widget.NBSnackbar;

/**
 * Created by priyadhingra on 9/12/2017.
 */

public class EditRecipientActivity extends BaseRecipientActivity implements EditRecipientView, IDialog {

    @Inject
    EditRecipientPresenter mEditRecipientPresenter;
    private RecipientViewModel mDeleteRecipientRequestViewModel;
    @Inject
    RecipientViewModelToEntityMapper mEditRecipientViewModelToEntityMapper;
    private ActivityEditRecipientBinding binding;
    private int mMatchBackNumber;

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        binding = ActivityEditRecipientBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        super.onCreate(savedInstanceState, mEditRecipientPresenter);
        initToolbar(binding.toolbar, true, false);
        if (getIntent() != null) {
            mRecipientViewModel = getIntent().getParcelableExtra(Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL);
            setUpViewAsPerIntentData();
            mDeleteRecipientRequestViewModel = new RecipientViewModel();
        }
        if (etRecipientName != null) {
            addListenerForRecipientName(etRecipientName);
        }
        binding.tvDeleteContactcard.setOnClickListener(v -> handleDeleteRecipient());
    }

    void handleDeleteRecipient() {
        UserConsentDialog.getInstance(getString(R.string.delete), getString(R.string.delete_dialog_message),
                getString(R.string.ok), getString(R.string.cancel)).show(getSupportFragmentManager(), UserConsentDialog.TAG);
    }

    private void deleteRecipient() {
        if (mRecipientViewModel != null) {
            mEditRecipientPresenter.callDeleteRecipientUsecase(mRecipientViewModel.getContactCardId());
        }
    }

    private void setUpViewAsPerIntentData() {
        if (mRecipientViewModel != null && !TextUtils.isEmpty(mRecipientViewModel.getRecipientName())) {
            binding.tvNameInitial.setText(StringUtils.getNameInitials(mRecipientViewModel.getRecipientName()));
            etRecipientName.setText(mRecipientViewModel.getRecipientName());
        }
        setupRecyclerViews();
        setLayoutParamOfRecyclerView(true);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mEditRecipientPresenter.bind(this);
        mEditRecipientPresenter.checkToggleForShapId();
        mEditRecipientPresenter.fetchFastPayDomainList();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mEditRecipientPresenter.unbind();
    }

    @Override
    protected void setUpRecyclerViewAdapter() {
        if (mRecipientViewModel == null) return;

        if (isAccountListNotEmpty()) {
            mBankAccountViewDataModelList = mRecipientViewModel.getBankAccountViewDataModelList();
        } else {
            mBankAccountViewDataModelList = new ArrayList<>();
            mRecipientViewModel.setBankAccountViewDataModelList(mBankAccountViewDataModelList);
        }

        mBankAccountAdapter = new BankAccountAdapter(EditRecipientActivity.this, mBankAccountNbFlexibleItemCountRecyclerviewModel, mBankAccountViewDataModelList, null, null);
        mBankAccountAdapter.setINBRecyclerViewListener(this);
        mBankAccountAdapter.setIActivityAdapterComListener(this);
        mBankAccountAdapter.setEditable(true);
        rvAccount.setAdapter(mBankAccountAdapter);

        if (!isBankApprovedBeneficiary()) {
            handleBankBeneficiaryNotApproved();
        }
    }

    private void handleBankBeneficiaryNotApproved() {
        if (mRecipientViewModel.getMobileNumberViewDataModelList() != null && mRecipientViewModel.getMobileNumberViewDataModelList().size() > 0) {
            mMobileNumberViewDataModelList = mRecipientViewModel.getMobileNumberViewDataModelList();
        } else {
            mMobileNumberViewDataModelList = new ArrayList<>();
            mRecipientViewModel.setMobileNumberViewDataModelList(mMobileNumberViewDataModelList);
        }

        mMobileNumberAdapter = new MobileNumberAdapter(EditRecipientActivity.this, mMobileNumberNbFlexibleItemCountRecyclerviewModel, mMobileNumberViewDataModelList, null, null);
        mMobileNumberAdapter.setINBRecyclerViewListener(this);
        mMobileNumberAdapter.setIActivityAdapterComListener(this);
        mMobileNumberAdapter.setEditable(true);
        rvMobileNumber.setAdapter(mMobileNumberAdapter);

        if (mRecipientViewModel.getCreditCardViewDataModelList() != null && mRecipientViewModel.getCreditCardViewDataModelList().size() > 0) {
            mCreditCardViewDataModelList = mRecipientViewModel.getCreditCardViewDataModelList();
        } else {
            mCreditCardViewDataModelList = new ArrayList<>();
            mRecipientViewModel.setCreditCardViewDataModelList(mCreditCardViewDataModelList);
        }

        if (CollectionUtils.isNotEmpty(mRecipientViewModel.getShapIdViewDataModelList())) {
            mShapIDViewDataModelList = mRecipientViewModel.getShapIdViewDataModelList();
        } else {
            mShapIDViewDataModelList = new ArrayList<>();
            mRecipientViewModel.setShapIdViewDataModelList(mShapIDViewDataModelList);
        }
        mShapIDAdapter = new ShapIDAdapter(this, mShapIdNbFlexibleItemCountRecyclerviewModel, mShapIDViewDataModelList, null, null);
        mShapIDAdapter.setINBRecyclerViewListener(this);
        mShapIDAdapter.setIActivityAdapterComListener(this);
        mShapIDAdapter.setEditable(true);
        rvShapeID.setAdapter(mShapIDAdapter);

        mCreditCardAdapter = new CreditCardAdapter(this, mCreditCardNbFlexibleItemCountRecyclerviewModel, mCreditCardViewDataModelList, null, null);
        mCreditCardAdapter.setINBRecyclerViewListener(this);
        mCreditCardAdapter.setIActivityAdapterComListener(this);
        mCreditCardAdapter.setEditable(true);
        rvCreditCard.setAdapter(mCreditCardAdapter);

        if (mRecipientViewModel.getElectricityMeterViewDataModelList() != null && mRecipientViewModel.getElectricityMeterViewDataModelList().size() > 0) {
            mElectricityMeterViewDataModelList = mRecipientViewModel.getElectricityMeterViewDataModelList();
        } else {
            mElectricityMeterViewDataModelList = new ArrayList<>();
            mRecipientViewModel.setElectricityMeterViewDataModelList(mElectricityMeterViewDataModelList);
        }

        mElectricityMeterAdapter = new ElectricityMeterAdapter(this, mElectricityNbFlexibleItemCountRecyclerviewModel, mElectricityMeterViewDataModelList, null, null);
        mElectricityMeterAdapter.setINBRecyclerViewListener(this);
        mElectricityMeterAdapter.setIActivityAdapterComListener(this);
        mElectricityMeterAdapter.setEditable(true);
        rvElectricity.setAdapter(mElectricityMeterAdapter);

        if (mRecipientViewModel.getEmailViewDataModelList() != null && mRecipientViewModel.getEmailViewDataModelList().size() > 0) {
            mEmailViewDataModelList = mRecipientViewModel.getEmailViewDataModelList();
        } else {
            mEmailViewDataModelList = new ArrayList<>();
            mEmailViewDataModelList.add(new EmailViewDataModel());
            mRecipientViewModel.setEmailViewDataModelList(mEmailViewDataModelList);
        }

        mEmailAdapter = new EmailAdapter(this, mEmailNbFlexibleItemCountRecyclerviewModel, mEmailViewDataModelList, null, null);
        mEmailAdapter.setEditable(true);
        mEmailAdapter.setIActivityAdapterComListener(this);
        mEmailAdapter.setINBRecyclerViewListener(this);
        rvEmail.setAdapter(mEmailAdapter);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            Bundle bundle = intent.getExtras();
            if (bundle != null && bundle.containsKey(NavigationTarget.IS_ENROLLMENT_SUCCESS)) {
                boolean isEnrollmentSuccess = bundle.containsKey(NavigationTarget.IS_ENROLLMENT_SUCCESS)
                        && bundle.getBoolean(NavigationTarget.IS_ENROLLMENT_SUCCESS, false);
                if (isEnrollmentSuccess) {
                    mEditRecipientPresenter.callRecipientStatus(mEditRecipientPresenter.getVerificationReferenceId());
                } else {
                    mEditRecipientPresenter.moveToStopScreen();
                }
            }
        }
    }

    private boolean isAccountListNotEmpty() {
        return mRecipientViewModel.getBankAccountViewDataModelList() != null
                && !mRecipientViewModel.getBankAccountViewDataModelList().isEmpty();
    }

    @Override
    public void dataValidationSuccess() {
        putEditRecipient();
    }

    @Override
    public void putEditRecipient() {
        if (!mEditRecipientPresenter.isApprovedTransaction()) {
            mEditRecipientPresenter.moveToErrorLocScreen(getString(R.string.edit_recipient_loc_error_header));
        } else {
            if (mDeleteRecipientRequestViewModel != null) {
                binding.nestedScrollView.fullScroll(View.FOCUS_DOWN);
                mEditRecipientPresenter.putEditRecipient(etRecipientName.getValue(), mRecipientViewModel.getContactCardId(), mBankAccountViewDataModelList, mMobileNumberViewDataModelList, mElectricityMeterViewDataModelList, mEmailViewDataModelList, mCreditCardViewDataModelList, mDeleteRecipientRequestViewModel, mRecipientViewModel.getUserBeneficiaryNotificationDetailsViewModelList(), mShapIDViewDataModelList);
                binding.etFocus.requestFocus();
            }
        }
    }

    @Override
    public void finishIt() {
        finish();
    }

    @Override
    public void onRecipientEdited(RecipientViewModel recipientViewModel) {
        mEditRecipientPresenter.trackSuccessAction(PaymentsTracking.UPDATE_RECIPIENTS_SUCCESSFUL, TrackingEvent.ANALYTICS.VAL_STEP_NAME_UPDATE_RECIPIENT_DETAILS);
        Bundle bundle = new Bundle();
        bundle.putBoolean(Constants.EXTRAS.IS_ITEM_EDITED, true);
        bundle.putParcelable(Constants.EXTRAS.EDIT_RECIPIENT, recipientViewModel);

        Intent intent = new Intent();
        intent.putExtras(bundle);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    @Override
    public void handleFewRecipientEditionFailedError(String... message) {
        Bundle bundle = new Bundle();
        Intent intent = new Intent();
        bundle.putStringArray(Constants.EXTRAS.ERROR_MESSAGE, message);
        bundle.putBoolean(Constants.EXTRAS.IS_ITEM_EDITED, true);
        intent.putExtras(bundle);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    @Override
    public void onItemRemoved(int pos, int viewType, NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel) {
        super.onItemRemoved(pos, viewType, nbRecyclerViewBaseDataModel);
        /**
         * do not need to remove account number for newly added item in UI as they are not yet added to db server
         */
        if (!nbRecyclerViewBaseDataModel.isExistingItem()) {
            return;
        }
        NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE sectionViewType = NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.values()[viewType];
        switch (sectionViewType) {
            case BANK:
                if (mDeleteRecipientRequestViewModel.getBankAccountViewDataModelList() != null) {
                    mDeleteRecipientRequestViewModel.getBankAccountViewDataModelList().add(mEditRecipientViewModelToEntityMapper.mapNbRecyclerViewBaseDataModelToDeleteRequestViewModel(nbRecyclerViewBaseDataModel));
                }
                break;
            case MOBILE_NUMBER:
                if (mDeleteRecipientRequestViewModel.getMobileNumberViewDataModelList() != null) {
                    mDeleteRecipientRequestViewModel.getMobileNumberViewDataModelList().add(mEditRecipientViewModelToEntityMapper.mapNbRecyclerViewBaseDataModelToDeleteRequestViewModel(nbRecyclerViewBaseDataModel));
                }
                break;
            case ELECTRICITY_METER:
                if (mDeleteRecipientRequestViewModel.getElectricityMeterViewDataModelList() != null && nbRecyclerViewBaseDataModel instanceof ElectricityMeterViewDataModel) {
                    mDeleteRecipientRequestViewModel.getElectricityMeterViewDataModelList().add(mEditRecipientViewModelToEntityMapper.mapNbRecyclerViewBaseDataModelToDeleteRequestViewModel(nbRecyclerViewBaseDataModel));
                }
                break;
            case CREDIT_CARD:
                if (mDeleteRecipientRequestViewModel.getCreditCardViewDataModelList() != null && nbRecyclerViewBaseDataModel instanceof CreditCardViewDataModel) {
                    mDeleteRecipientRequestViewModel.getCreditCardViewDataModelList().add(mEditRecipientViewModelToEntityMapper.mapNbRecyclerViewBaseDataModelToDeleteRequestViewModel(nbRecyclerViewBaseDataModel));
                }
                break;
            default:
        }
    }

    @Override
    public void showEditRecipientApiError() {
        showLoadingOnButton(false);
        setEnabledActivityTouch(true);
        setLoadingButtonEnabled(true);
        NBSnackbar.instance().action(getString(R.string.snack_bar_okay), () -> {
        }).build(lnrTopView, getString(R.string.snackbar_recipient_not_saved));
    }

    @Override
    public void showEditRecipientApiError(String... message) {
        showEditRecipientApiError(null, message);
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void showEditRecipientApiError(Map<Integer, String> matchBackNumber, String... message) {
        showLoadingOnButton(false);
        setEnabledActivityTouch(true);
        if (matchBackNumber == null || matchBackNumber.isEmpty()) {
            setLoadingButtonEnabled(true);
            NBSnackbar.instance().action(getString(R.string.snackbar_action_retry), this::putEditRecipient).build(lnrTopView, message);
        } else {
            mBankAccountAdapter.setMatchBackNumberErrorMap(matchBackNumber);
            mCreditCardAdapter.setMatchBackNumberErrorMap(matchBackNumber);
            mElectricityMeterAdapter.setMatchBackNumberErrorMap(matchBackNumber);
            setLoadingButtonEnabled(false);
            mBankAccountAdapter.notifyDataSetChanged();
            mCreditCardAdapter.notifyDataSetChanged();
            mElectricityMeterAdapter.notifyDataSetChanged();
            NBSnackbar.instance().action(getString(R.string.snack_bar_okay), () -> {
            }).build(lnrTopView, getString(R.string.snackbar_recipient_save_error));
        }
    }

    @Override
    public void showDeleteRecipientApiError(String... message) {
        setLoadingButtonEnabled(true);
        showLoadingOnButton(false);
        setEnabledActivityTouch(true);
        NBSnackbar.instance().action(getString(R.string.snackbar_action_retry), this::deleteRecipient).build(lnrTopView, message);
    }

    @Override
    public void showApproveItApiError(String... message) {
        setLoadingButtonEnabled(true);
        showLoadingOnButton(false);
        setEnabledActivityTouch(true);
        NBSnackbar.instance()
                .header(getString(R.string.action_denied))
                .duration(NBSnackbar.FOREVER)
                .action(getString(R.string.snackbar_action_ok), this::onBackPressed).build(lnrTopView, message);
    }

    @Override
    public void onRecipientDeleted() {
        mEditRecipientPresenter.trackSuccessAction(PaymentsTracking.DELETE_RECIPIENTS_SUCCESSFUL, TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_DELETE_RECIPIENT);
        Bundle bundle = new Bundle();
        bundle.putBoolean(Constants.EXTRAS.IS_ITEM_DELETED, true);
        Intent intent = new Intent();
        intent.putExtras(bundle);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    @Override
    public RecipientViewModel getRecipientViewModel() {
        return mRecipientViewModel;
    }

    @Override
    public void onPositiveButtonClick() {
        deleteRecipient();
    }

    @Override
    public void onNegativeButtonClick() {
            // Not required to handle negative button click
    }

    @Override
    public int getMatchBackNumber() {
        return ++mMatchBackNumber;
    }

    @Override
    public void setMatchBackNumber(int matchBackNumber) {
        mMatchBackNumber = matchBackNumber;
    }
}
