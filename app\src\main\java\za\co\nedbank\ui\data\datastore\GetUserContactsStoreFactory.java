/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.datastore;

import android.os.RemoteException;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.payment.buy.domain.model.response.UserContactData;
import za.co.nedbank.ui.data.local.RecipientContactsProvider;
import za.co.nedbank.ui.domain.repository.IGetUserContactsCache;


public class GetUserContactsStoreFactory {

    private final IGetUserContactsCache mGetUserContactsCache;
    private final RecipientContactsProvider mUserContactsProvider;

    @Inject
    GetUserContactsStoreFactory(IGetUserContactsCache bankBeneficiaryCache, RecipientContactsProvider userContactsProvider) {
        this.mGetUserContactsCache = bankBeneficiaryCache;
        this.mUserContactsProvider = userContactsProvider;
    }

    public Observable<List<UserContactData>> getData(String searchQuery) {
        if (mGetUserContactsCache.isDataCached()) {
            return mGetUserContactsCache.getContacts(searchQuery);
        }
        try {
            Observable<List<UserContactData>> userBeneficiaryEntityObservable = mUserContactsProvider.getUserContacts();
            return userBeneficiaryEntityObservable.flatMap(
                    userContactDatas -> {
                        mGetUserContactsCache.setList(userContactDatas);
                        return userBeneficiaryEntityObservable;
                    }
            );

        } catch (RemoteException e) {
            return Observable.error(e);
        }
    }

}
