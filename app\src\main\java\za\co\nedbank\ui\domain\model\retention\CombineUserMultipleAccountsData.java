package za.co.nedbank.ui.domain.model.retention;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.data.accounts.model.AccountDto;
import za.co.nedbank.core.data.accounts.model.AccountsContainerDto;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.branchcode.BranchCodeDataModel;
import za.co.nedbank.services.domain.model.overview.OverviewType;

public class CombineUserMultipleAccountsData {

    private UserDetailData userDetail;
    private List<AccountsContainerDto> accountsContainerDtos;
    private List<BranchCodeDataModel> branchCodeDataModels;

    public UserDetailData getUserDetail() {
        return userDetail;
    }

    public void setUserDetail(UserDetailData userDetail) {
        this.userDetail = userDetail;
    }

    public List<AccountsContainerDto> getAccountsContainerDtos() {
        return accountsContainerDtos;
    }

    public void setAccountsContainerDtos(List<AccountsContainerDto> accountsContainerDtos) {
        this.accountsContainerDtos = accountsContainerDtos;
    }

    public List<BranchCodeDataModel> getBranchCodeDataModels() {
        return branchCodeDataModels;
    }

    public void setBranchCodeDataModels(List<BranchCodeDataModel> branchCodeDataModels) {
        this.branchCodeDataModels = branchCodeDataModels;
    }


    public List<AccountSummary> mapAccountWithType(List<AccountsContainerDto> accountsContainerDto, boolean isDebitOrderFlow) {
        List<AccountSummary> accountSummaryList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(accountsContainerDtos) && accountsContainerDtos.size()>0) {
            for (AccountsContainerDto container : accountsContainerDto) {
                OverviewType overviewType = getOverviewType(container.name);
                    if ( overviewType!=null && isDebitOrderAccounts(isDebitOrderFlow,container)){
                        final List<AccountDto> acc = container.accounts;
                        accountSummaryList = addDataToAccountSummaryList(acc,overviewType);
                    }
            }
        }
        return accountSummaryList;
    }

    private List<AccountSummary> addDataToAccountSummaryList(List<AccountDto> acc,OverviewType overviewType){
        List<AccountSummary> accountSummaryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(acc)) {
            for(AccountDto account:acc){
                if(isShareDetailsAvailable(account)) {
                    accountSummaryList.add(getAccountSummary(account, overviewType));
                }
            }
        }
        return accountSummaryList;
    }

    private boolean isDebitOrderAccounts(boolean isDebitOrderFlow,AccountsContainerDto container){
       return  !isDebitOrderFlow && (container.name.equalsIgnoreCase(Constants.AccountContainerName.BANK) || container.name.equalsIgnoreCase(Constants.AccountContainerName.LOAN))
                ||  isDebitOrderFlow && container.name.equalsIgnoreCase(Constants.AccountContainerName.BANK);
        }

    private boolean isShareDetailsAvailable(AccountDto accountDto){
        if(accountDto!=null && accountDto.getAccountType()!=null){
            String accountType = accountDto.getAccountType();
            return accountType.equals(Constants.ACCOUNT_TYPES.CA.getAccountTypeCode())
                    || accountType.equals(Constants.ACCOUNT_TYPES.SA.getAccountTypeCode())
                    || accountType.equals(Constants.ACCOUNT_TYPES.PL.getAccountTypeCode())
                    || accountType.equals(Constants.ACCOUNT_TYPES.HL.getAccountTypeCode());
        }
        return false;
    }

    private OverviewType getOverviewType(final String containerName) {
        switch (containerName) {
            case Constants.AccountContainerName.BANK:
                return OverviewType.EVERYDAY_BANKING;
            case Constants.AccountContainerName.CARD:
                return OverviewType.CREDIT_CARDS;
            case Constants.AccountContainerName.LOAN:
                return OverviewType.LOANS;
            case Constants.AccountContainerName.INVESTMENT:
                return OverviewType.INVESTMENTS;
            case Constants.AccountContainerName.FOREIGN:
                return OverviewType.FOREIGN_CURRENCY_ACCOUNT;
            case Constants.AccountContainerName.REWARDS:
                return OverviewType.REWARDS;
            case Constants.AccountContainerName.CLUBACCOUNT:
                return OverviewType.CLUB_ACCOUNTS;
            default:
                break;
        }
        return null;
    }

    @NonNull
    private AccountSummary getAccountSummary(AccountDto account,OverviewType overviewType) {
        AccountSummary summary = new AccountSummary();
        summary.setId(account.getId());
        summary.setName(account.getName());

        if(account.getMaintainOptionsEntity()!=null){
            if(account.getMaintainOptionsEntity().getPlaceNotice()!=null){
                summary.setPlaceNotice(account.getMaintainOptionsEntity().getPlaceNotice());
            }
            if(account.getMaintainOptionsEntity().getDeleteNotice() != null) {
                summary.setDeleteNotice(account.getMaintainOptionsEntity().getDeleteNotice());
            }
            if (account.getMaintainOptionsEntity().getAddEarlyRelease() != null) {
                summary.setAddEarlyRelease(account.getMaintainOptionsEntity().getAddEarlyRelease());
            }

            if (account.getMaintainOptionsEntity().getDeleteEarlyRelease() != null) {
                summary.setDeleteEarlyRelease(account.getMaintainOptionsEntity().getDeleteEarlyRelease());
            }
            if(account.getMaintainOptionsEntity().getPayoutFixedDeposit() != null) {
                summary.setPayoutFixedDeposit(account.getMaintainOptionsEntity().getPayoutFixedDeposit());
            }
            if(account.getMaintainOptionsEntity().getReinvestFixedDeposit() != null) {
                summary.setReinvestFixedDeposit(account.getMaintainOptionsEntity().getReinvestFixedDeposit());
            }
            if(account.getMaintainOptionsEntity().getHasRecurringPayment()!=null) {
                summary.setHasRecurringPayment(account.getMaintainOptionsEntity().getHasRecurringPayment());
            }
            if(account.getMaintainOptionsEntity().getDeleteRecurringPayment()!=null) {
                summary.setDeleteRecurringPayment(account.getMaintainOptionsEntity().getDeleteRecurringPayment());
            }
            if(account.getMaintainOptionsEntity().getNoticeProduct()!=null) {
                summary.setNoticeProduct(account.getMaintainOptionsEntity().getNoticeProduct());
            }
            if(account.getMaintainOptionsEntity().getMaintainRecurringPayment()!=null) {
                summary.setMaintainRecurringPayment(account.getMaintainOptionsEntity().getMaintainRecurringPayment());
            }
            if(!TextUtils.isEmpty(account.getMaintainOptionsEntity().getViewPayout())) {
                summary.setViewPayout(account.getMaintainOptionsEntity().getViewPayout());
            }
            if(!TextUtils.isEmpty(account.getMaintainOptionsEntity().getDeletePayout())) {
                summary.setDeletePayout(account.getMaintainOptionsEntity().getDeletePayout());
            }
            if(!TextUtils.isEmpty(account.getMaintainOptionsEntity().getViewReinvest())) {
                summary.setViewReinvest(account.getMaintainOptionsEntity().getViewReinvest());
            }
            if(!TextUtils.isEmpty(account.getMaintainOptionsEntity().getDeleteReinvest())) {
                summary.setDeleteReinvest(account.getMaintainOptionsEntity().getDeleteReinvest());
            }
            if(!TextUtils.isEmpty(account.getMaintainOptionsEntity().getAddRecurringPayment())) {
                summary.setAddRecurringPayment(account.getMaintainOptionsEntity().getAddRecurringPayment());
            }
        }
        summary.setProductType(account.getProductType());
        summary.setAccountShown(account.isShow());
        summary.setNumber(account.getNumber());
        summary.setCardRelationShip(account.getCardRelationship());
        summary.setIdentificationNumber(account.getIdentificationNumber());
        summary.setAccountType(overviewType);
        summary.setAccountCode(account.accountType);
        summary.setAccountHolderName(account.getAccountHolderName());
        summary.setMarketValue(account.balance);
        if (!TextUtils.isEmpty(account.getPledgeAccountStatus())){
            summary.setPledgeAccount(account.getPledgeAccountStatus());
        }
        if (!TextUtils.isEmpty(account.getTfsIndicator())){
            summary.setTfsIndicator(account.getTfsIndicator());
        }
        if (!TextUtils.isEmpty(account.getStatus())){
            summary.setStatus(account.getStatus());
        }
        if (!TextUtils.isEmpty(account.getProductId())){
            summary.setInvestmentProductId(account.getProductId());
        }
        if (account.getAccountStatusCode() != null && account.getAccountStatusCode().equalsIgnoreCase(za.co.nedbank.services.Constants.DORMANT_ACCOUNT_STATUS_CODE)) {
            summary.setDormantAccount(true);
        }

        if(account.accountType.equalsIgnoreCase(Constants.ACCOUNT_TYPES.CA.getAccountTypeCode())
                &&(za.co.nedbank.services.Constants.ACCOUNT_STATUS_TYPE_08.equalsIgnoreCase(account.getAccountStatusCode())
                || za.co.nedbank.services.Constants.ACCOUNT_STATUS_TYPE_8.equalsIgnoreCase(account.getAccountStatusCode()))){
            summary.setPendingCurrentAccount(true);
        }
        summary.setTravelCardAccount(account.getAccountType().equalsIgnoreCase(Constants.ACCOUNT_TYPES.CC.getAccountTypeCode()) && account.getNumber().startsWith(Constants.TRAVEL_CARD_BIN));
        summary.setCurrency(account.getCurrency());
        summary.setProfileAccount(account.isProfileAccount());
        summary.setAlternateAccount(account.isAlternateAccount());
        return summary;
    }
}
