package za.co.nedbank.ui.view.home.product_deep_linking;

import static za.co.nedbank.core.data.storage.StorageKeys.CLIENT_TYPE;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_APPLY_NOW;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_APPLY_TP_NOW;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_BANK;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_CANCEL;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_DEEP_LINK_ENTRY_POINT;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_EXPAND;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_EXPAND_TP_CAMPAIGN_SUMMARY;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_PRODUCT_ON_BOARDING;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_SALES_AND_ONBOARDING;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_TP_ONBOARDING_CAMPAIGN;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.FicaProductFlow;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.moa.ProductUseCase;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.mapper.moa.ProductRequestViewModelToDataMapper;
import za.co.nedbank.core.view.mapper.moa.ProductResponseDataToViewModelMapper;
import za.co.nedbank.core.view.model.moa.ProductRequestViewModel;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.enroll_v2.view.fica.error.FicaErrorHandler;

public class ProductDeepLinkPresenter extends NBBasePresenter<ProductDeepLinkView> {
    private final ApplicationStorage mApplicationStorage;
    private final ApplicationStorage mPersistantStorage;
    private final FicaErrorHandler mFicaErrorHandler;
    private final NavigationRouter mNavigationRouter;
    private final Analytics mAnalytics;
    private final ProductUseCase mProductUseCase;
    private final ProductRequestViewModelToDataMapper mProductRequestViewModelToDataMapper;
    private final ProductResponseDataToViewModelMapper mProductResponseDataToViewModelMapper;

    @Inject
    public ProductDeepLinkPresenter(final @Named("memory") ApplicationStorage applicationStorage,
                                    final ApplicationStorage persistantStorage,
                                    final NavigationRouter navigationRouter,
                                    final Analytics mAnalytics,
                                    final ProductUseCase productUseCase,
                                    final FicaErrorHandler ficaErrorHandler,
                                    final ProductRequestViewModelToDataMapper productRequestViewModelToDataMapper,
                                    final ProductResponseDataToViewModelMapper productResponseDataToViewModelMapper) {
        this.mApplicationStorage = applicationStorage;
        this.mPersistantStorage = persistantStorage;
        this.mNavigationRouter = navigationRouter;
        this.mAnalytics = mAnalytics;
        this.mProductUseCase = productUseCase;
        this.mFicaErrorHandler = ficaErrorHandler;
        this.mProductRequestViewModelToDataMapper = productRequestViewModelToDataMapper;
        this.mProductResponseDataToViewModelMapper = productResponseDataToViewModelMapper;
    }

    public void handleAnalytics(boolean isFromTransactionalFlow) {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);
        contextData.setProductCategory(VAL_BANK);
        contextData.setEntryPoint(VAL_DEEP_LINK_ENTRY_POINT);
        if (isFromTransactionalFlow) {
            contextData.setPageCategory(TrackingEvent.ANALYTICS.VAL_CAMPAIGN);
            contextData.setCategoryAndProduct(TrackingEvent.ANALYTICS.VAL_PRODUCT_CAMPAIGN_CATEGORY, getProductName());
            contextData.setProductAccount(getProductName());
            mAnalytics.sendEventStateWithMap(TrackingEvent.ANALYTICS.VAL_NAME_CAMPAIGN_PRODUCT_SUMMARY, contextData.getCdata());
        } else {
            contextData.setCampaignName(VAL_TP_ONBOARDING_CAMPAIGN);
            contextData.setCampaignType(VAL_EXPAND);
            contextData.setPageCategory(TrackingEvent.ANALYTICS.VAL_CAMPAIGN);
            contextData.setCategoryAndProduct(TrackingEvent.ANALYTICS.VAL_PRODUCT_CAMPAIGN_CATEGORY, StringUtils.EMPTY_STRING);
            mAnalytics.sendEventStateWithMap(VAL_EXPAND_TP_CAMPAIGN_SUMMARY, contextData.getCdata());
        }
    }

    public String getProductName() {
      return  mApplicationStorage.getString(StorageKeys.DEEP_LINK_PRODUCT_CAMPAIGN_NAME, StringUtils.EMPTY_STRING);
    }

    public void showContent(String description) {
        if(view!=null) {
            view.showContent(description);
        }
    }

    public void navigateToNextScreen(boolean isFromTransactionalFlow) {
        sendAnalyticsOnApplyNowClick(isFromTransactionalFlow);
        if (mApplicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) == FicaWorkFlow.IN_APP) {
            if (mApplicationStorage.getInteger(StorageKeys.FICA_PRODUCT_FLOW, FicaProductFlow.DEFAULT) == FicaProductFlow.MINOR ||
                    StringUtils.isNotEmpty(mApplicationStorage.getString(StorageKeys.DEEP_LINK_SUB_PRODUCT_ID, StringUtils.EMPTY_STRING))) {
                if (ClientType.TP == getClientType()) {
                    navigateToDetailsScreen();
                } else {
                    mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.FICA_MINOR_DEEPLINK_SCREEN));
                }
            } else {
                mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MOA_PRODUCT_LIST));
            }
        } else {
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MOA_BASIC_INFORMATION));
        }
    }

    public void navigateToDetailsScreen() {
        mApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, EnrollV2TrackingEvent.ANALYTICS.VAL_BANK_NEDBANK_4ME);
        mApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT, za.co.nedbank.enroll_v2.Constants.MINOR_PRODUCT_NAME);
        mApplicationStorage.putBoolean(StorageKeys.IS_PROFESSIONAL_PRODUCT, false);
        ProductRequestViewModel productRequestViewModel = new ProductRequestViewModel();
        productRequestViewModel.setMonthlySalary(0);
        productRequestViewModel.setDateofbirth(new SimpleDateFormat(za.co.nedbank.enroll_v2.Constants.DATE_FORMAT, Locale.getDefault()).format(new Date()));
        productRequestViewModel.setProductType(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.getAccountTypeCode());

        mProductUseCase.execute(mProductRequestViewModelToDataMapper.mapData(productRequestViewModel))
                .compose(bindToLifecycle())
                .map(mProductResponseDataToViewModelMapper::mapData)
                .subscribe(productResponseViewModel -> {
                    if (productResponseViewModel.getMetadata().isSuccess()
                            && productResponseViewModel.getProductDataResponseDataViewModel() != null
                            && productResponseViewModel.getProductDataResponseDataViewModel().getProductDataViewModels() != null
                            && !productResponseViewModel.getProductDataResponseDataViewModel().getProductDataViewModels().isEmpty()) {
                        mApplicationStorage.putString(StorageKeys.FICA_SESSION_ID,
                                productResponseViewModel.getProductDataResponseDataViewModel().getSessionId());
                        mApplicationStorage.putInteger(StorageKeys.FICA_PRODUCT_FLOW,
                                FicaProductFlow.MINOR);
                        mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.MOA_PRODUCT_DETAIL)
                                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                                .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SELECTED_PRODUCT, productResponseViewModel
                                        .getProductDataResponseDataViewModel()
                                        .getProductDataViewModels()
                                        .get(0)));
                    } else {
                        handleException(productResponseViewModel.getMetadata().getThrowable());
                    }
                }, this::handleException);
    }

    private void handleException(Throwable throwable) {
        mFicaErrorHandler.handleError(throwable);
    }

    public ClientType getClientType() {
        String clientType = mPersistantStorage.getString(CLIENT_TYPE, ClientType.TP.getValue());
        return ClientType.getEnum(clientType);
    }

    public void sendAnalyticsOnApplyNowClick(boolean isFromTransactionalFlow) {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);
        contextData.setFeature(VAL_PRODUCT_ON_BOARDING);
        contextData.setProductCategory(VAL_BANK);
        contextData.setFeatureCategory(VAL_SALES_AND_ONBOARDING);
        if(isFromTransactionalFlow) {
            contextData.setCategoryAndProduct(TrackingEvent.ANALYTICS.VAL_PRODUCT_CAMPAIGN_CATEGORY, getProductName());
            contextData.setProductAccount(getProductName());
            contextData.setValue(VAL_APPLY_NOW);
            contextData.setScreenName(TrackingEvent.ANALYTICS.VAL_NAME_CAMPAIGN_PRODUCT_SUMMARY);
            mAnalytics.sendEventActionWithMap(TrackingEvent.ANALYTICS.MOA_CASA_APPLY_NOW, contextData.getCdata());
        }else{
            contextData.setCategoryAndProduct(TrackingEvent.ANALYTICS.VAL_PRODUCT_CAMPAIGN_CATEGORY, StringUtils.EMPTY_STRING);
            contextData.setScreenName(VAL_EXPAND_TP_CAMPAIGN_SUMMARY);
            contextData.setValue(VAL_APPLY_TP_NOW);
            mAnalytics.sendEventStateWithMap(TrackingEvent.ANALYTICS.MOA_CASA_TP_APPLY_NOW, contextData.getCdata());
        }
    }

    public void handleViewMoreClick(boolean mIsFromTransactionalFlow) {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.DEEP_LINK_PRODUCT_FULL_DETAIL)
                .withParam(Constants.BUNDLE_KEYS.FROM_TRANSACTIONAL_FLOW, mIsFromTransactionalFlow));
    }

    public void onCrossClick(boolean isFromTransactionalFlow) {
        sendAnalyticsOnCrossClick(isFromTransactionalFlow);
        mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.FICA_LEAVING_SOON));
    }

    public void sendAnalyticsOnCrossClick(boolean isFromTransactionalFlow) {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);
        if (isFromTransactionalFlow) {
            contextData.setCategoryAndProduct(TrackingEvent.ANALYTICS.VAL_PRODUCT_CAMPAIGN_CATEGORY, getProductName());
            contextData.setProductAccountCount();
            contextData.setProductAccount(getProductName());
            contextData.setScreenName(TrackingEvent.ANALYTICS.VAL_NAME_CAMPAIGN_PRODUCT_SUMMARY);
        } else {
            contextData.setCategoryAndProduct(TrackingEvent.ANALYTICS.VAL_PRODUCT_CAMPAIGN_CATEGORY, StringUtils.EMPTY_STRING);
            contextData.setScreenName(VAL_EXPAND_TP_CAMPAIGN_SUMMARY);
        }
        contextData.setProductCategory(VAL_BANK);
        contextData.setFeatureCategoryCount();
        contextData.setFeatureCount();
        contextData.setProductCategoryCount();
        contextData.setValue(VAL_CANCEL);
        mAnalytics.sendEventActionWithMap(TrackingEvent.ANALYTICS.MOA_CASA_CANCEL, contextData.getCdata());
    }

    public String getShortDescription() {
        String description = mApplicationStorage.getString(StorageKeys.PRODUCT_SHORT_DESCRIPTION, StringUtils.EMPTY_STRING);
        mApplicationStorage.clearValue(StorageKeys.PRODUCT_SHORT_DESCRIPTION);
        return description;
    }
}
