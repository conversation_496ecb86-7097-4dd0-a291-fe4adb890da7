package za.co.nedbank.ui.view.refica;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingParam;

public class FicaErrorPresenter extends NBBasePresenter<FicaErrorView> {

    private final Analytics mAnalytics;

    @Inject
    public FicaErrorPresenter(final Analytics mAnalytics){
        this.mAnalytics = mAnalytics;
    }

    public void sendAnalytics() {
        mAnalytics.sendEventActionWithMap(TrackingParam.MDM_FAILURE_REFICA_VERIFY, null);
    }
}
