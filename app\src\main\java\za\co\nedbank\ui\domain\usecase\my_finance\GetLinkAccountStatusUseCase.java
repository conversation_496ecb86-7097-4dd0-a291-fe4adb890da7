/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.my_finance;


import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.VoidUseCase;
import za.co.nedbank.ui.domain.repository.ILinkAccountStatusRepository;

public class GetLinkAccountStatusUseCase extends VoidUseCase<Integer> {

    private final ILinkAccountStatusRepository mLinkAccountInfoRepository;

    @Inject
    public GetLinkAccountStatusUseCase(final UseCaseComposer composer
            , final ILinkAccountStatusRepository iLinkAccountInfoRepository) {
        super(composer);
        this.mLinkAccountInfoRepository = iLinkAccountInfoRepository;
    }

    @Override
    protected Observable<Integer> createUseCaseObservable() {
        return Observable.defer(() -> Observable.just(mLinkAccountInfoRepository.getAccountLinkedStatus()));
    }

}
