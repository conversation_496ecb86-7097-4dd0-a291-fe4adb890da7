/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import org.joda.time.DateTime;
import org.zakariya.stickyheaders.SectioningAdapter;

import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.databinding.CommonSectionHeaderBinding;
import za.co.nedbank.databinding.MoneyRequestSentItemLayoutBinding;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsAdapterModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsViewModel;

/**
 * Created by swapnil.gawande on 2/20/2018.
 */

public class SentMoneyRequestsRecyclerAdapter extends SectioningAdapter {
    private final Context mContext;
    private final ISentMoneyRequestsRecyclerListener mSentMoneyRequestsRecyclerListener;

    private List<MoneyRequestsAdapterModel> mMoneyRequestsAdapterModels;

    SentMoneyRequestsRecyclerAdapter(final Context context,
                                     @NonNull ISentMoneyRequestsRecyclerListener sentMoneyRequestsRecyclerListener) {
        this.mContext = context;
        this.mSentMoneyRequestsRecyclerListener = sentMoneyRequestsRecyclerListener;
    }

    void setMoneyRequestsAdapterModels(List<MoneyRequestsAdapterModel> moneyRequestsAdapterModels) {
        mMoneyRequestsAdapterModels = moneyRequestsAdapterModels;
        notifyAllSectionsDataSetChanged();
    }

    @Override
    public HeaderViewHolder onCreateHeaderViewHolder(final ViewGroup parent, final int headerUserType) {
        CommonSectionHeaderBinding binding = CommonSectionHeaderBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new HeaderViewHolder(binding);
    }

    @Override
    public ItemViewHolder onCreateItemViewHolder(final ViewGroup parent, final int itemUserType) {
        MoneyRequestSentItemLayoutBinding binding = MoneyRequestSentItemLayoutBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ItemViewHolder(binding, mSentMoneyRequestsRecyclerListener);
    }

    @Override
    public void onBindHeaderViewHolder(SectioningAdapter.HeaderViewHolder viewHolder, int sectionIndex, int headerType) {
        super.onBindHeaderViewHolder(viewHolder, sectionIndex, headerType);
        int headerStringId = mMoneyRequestsAdapterModels.get(sectionIndex).getHeader();
        ((HeaderViewHolder) viewHolder).setHeader(headerStringId);
    }

    @Override
    public void onBindItemViewHolder(SectioningAdapter.ItemViewHolder viewHolder, int sectionIndex, int itemIndex, int itemType) {
        super.onBindItemViewHolder(viewHolder, sectionIndex, itemIndex, itemType);
        MoneyRequestsViewModel moneyRequestsViewModel = getViewModel(sectionIndex, itemIndex);
        DateTime moneyRequestDate = FormattingUtil.API_DATE_FORMATTER.parseDateTime(moneyRequestsViewModel.getRequestDate());

        ItemViewHolder itemViewHolder = (ItemViewHolder) viewHolder;
        itemViewHolder.tvInitials.setText(StringUtils.getNameInitials(moneyRequestsViewModel.getPartyName()));
        String amount = FormattingUtil.convertToSouthAfricaFormattedCurrency(moneyRequestsViewModel.getRequestAmount());
        String sentRequestMsg = String.format(mContext.getString(R.string.view_money_requests_sent_item_message),
                FormattingUtil.DATE_FORMATTER_WITH_MMM.print(moneyRequestDate), amount, moneyRequestsViewModel.getPartyName());
        itemViewHolder.tvRequestMsg.setText(sentRequestMsg);
        String expiryDate = String.format(mContext.getString(R.string.view_money_requests_expiry_date), FormattingUtil.getFormattedDateWithSlash(moneyRequestsViewModel.getExpiryDate()));
        itemViewHolder.tvExpiryDate.setText(expiryDate);
        itemViewHolder.enableRemindAction(!moneyRequestsViewModel.isReminder());
        itemViewHolder.setSectionPosition(sectionIndex);
        itemViewHolder.setItemPosition(itemIndex);
        if (mMoneyRequestsAdapterModels.get(sectionIndex).getHeader() == MoneyRequestsType.PAID_REQUESTS) {
            itemViewHolder.enableRemindAction(false);
        }
        itemViewHolder.tvRemind.setOnClickListener(v -> itemViewHolder.onRemindClicked());
    }

    static class HeaderViewHolder extends SectioningAdapter.HeaderViewHolder {
        TextView tvHeader;

        HeaderViewHolder(CommonSectionHeaderBinding binding) {
            super(binding.getRoot());
            tvHeader = binding.tvHeader;
        }

        void setHeader(int stringId) {
            tvHeader.setText(stringId);
        }
    }

     class ItemViewHolder extends SectioningAdapter.ItemViewHolder {
        private final ISentMoneyRequestsRecyclerListener sentMoneyRequestsRecyclerListener;
        private int sectionPosition;
        private int itemPosition;
        TextView tvInitials;
        TextView tvRequestMsg;
        TextView tvExpiryDate;
        TextView tvRemind;

        void setSectionPosition(int sectionPosition) {
            this.sectionPosition = sectionPosition;
        }

        void setItemPosition(int itemPosition) {
            this.itemPosition = itemPosition;
        }

        ItemViewHolder(MoneyRequestSentItemLayoutBinding binding, ISentMoneyRequestsRecyclerListener sentMoneyRequestsRecyclerListener) {
            super(binding.getRoot());
            this.sentMoneyRequestsRecyclerListener = sentMoneyRequestsRecyclerListener;
            this.tvInitials = binding.tvInitials;
            this.tvRequestMsg = binding.tvRequestMsg;
            this.tvExpiryDate = binding.tvExpiryDate;
            this.tvRemind = binding.tvRemind;
        }

        void enableRemindAction(boolean enabled) {
            tvRemind.setEnabled(enabled);
        }

        void onRemindClicked() {
            sentMoneyRequestsRecyclerListener.onRemindClicked(getViewModel(sectionPosition, itemPosition));
        }
    }

    @Override
    public boolean doesSectionHaveHeader(int sectionIndex) {
        return true;
    }

    @Override
    public boolean doesSectionHaveFooter(int sectionIndex) {
        return false;
    }

    @Override
    public int getNumberOfSections() {
        return mMoneyRequestsAdapterModels == null ? 0 : mMoneyRequestsAdapterModels.size();
    }

    @Override
    public int getNumberOfItemsInSection(int sectionIndex) {
        if (mMoneyRequestsAdapterModels != null && mMoneyRequestsAdapterModels.get(sectionIndex) != null
                && mMoneyRequestsAdapterModels.get(sectionIndex).getMoneyRequestsViewModels() != null) {
            return mMoneyRequestsAdapterModels.get(sectionIndex).getMoneyRequestsViewModels().size();
        }
        return 0;
    }

    private MoneyRequestsViewModel getViewModel(int sectionIndex, int itemIndex) {
        return mMoneyRequestsAdapterModels.get(sectionIndex).getMoneyRequestsViewModels().get(itemIndex);
    }

    interface ISentMoneyRequestsRecyclerListener {
        void onRemindClicked(MoneyRequestsViewModel moneyRequestsViewModel);
    }
}
