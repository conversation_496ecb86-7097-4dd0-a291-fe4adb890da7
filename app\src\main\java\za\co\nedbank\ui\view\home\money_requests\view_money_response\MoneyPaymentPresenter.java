/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import java.util.Calendar;
import java.util.Date;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.constants.IAccountOptions;
import za.co.nedbank.core.domain.usecase.GetAccountsUseCase;
import za.co.nedbank.core.enumerations.NotificationType;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.payment.PaymentsUtility;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.EmailValidator;
import za.co.nedbank.core.validation.MobileNumberValidator;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.validation.TransferAmountEmptyValidator;
import za.co.nedbank.core.validation.ValidationResult;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.payment.common.domain.data.model.limits.LimitsInnerDataModel;
import za.co.nedbank.payment.common.domain.usecases.LimitsDataUseCase;
import za.co.nedbank.payment.common.view.listener.ILimitOptions;
import za.co.nedbank.core.payment.recent.NotificationDetailsViewModel;
import za.co.nedbank.payment.pay.validation.AccountBalanceValidator;
import za.co.nedbank.payment.pay.validation.AmountLimitValidator;
import za.co.nedbank.payment.pay.view.model.PaymentViewModel;
import za.co.nedbank.payment.transfer.domain.data.mapper.LimitsDataToViewModelMapper;
import za.co.nedbank.payment.transfer.view.model.response.LimitsViewModel;
import za.co.nedbank.ui.view.tracking.AppTracking;
import za.co.nedbank.uisdk.validation.ValidatableInput;

/**
 * Created by devrath.rathee on 2/20/2018.
 */

public class MoneyPaymentPresenter extends NBBasePresenter<MoneyResponseView> {

    private final NavigationRouter navigationRouter;
    private final ErrorHandler errorHandler;
    private LimitsViewModel paymentLimitsViewModel;
    private final LimitsDataUseCase limitsDataUseCase;
    private final AccountBalanceValidator accountBalanceValidator;
    private final AmountLimitValidator amountLimitValidator;
    private final TransferAmountEmptyValidator transferAmountEmptyValidator;
    private final NonEmptyTextValidator nonEmptyTextValidator;
    private final LimitsDataToViewModelMapper limitsDataToViewModelMapper;
    private final EmailValidator emailValidator;
    private final MobileNumberValidator mobileNumberValidator;
    private final GetAccountsUseCase mGetAccountsUseCase;
    private double availableLimit;
    private final Analytics mAnalytics;

    @Inject
    MoneyPaymentPresenter(final AccountBalanceValidator accountBalanceValidator, final AmountLimitValidator amountLimitValidator,
                          final TransferAmountEmptyValidator transferAmountEmptyValidator, final NonEmptyTextValidator nonEmptyTextValidator,
                          final EmailValidator emailValidator, final MobileNumberValidator mobileNumberValidator,
                          final GetAccountsUseCase mGetAccountsUseCase, LimitsDataUseCase limitsDataUseCase,
                          LimitsDataToViewModelMapper limitsDataToViewModelMapper, @NonNull NavigationRouter navigationRouter, @NonNull ErrorHandler errorHandler, Analytics mAnalytics) {
        this.accountBalanceValidator = accountBalanceValidator;
        this.amountLimitValidator = amountLimitValidator;
        this.transferAmountEmptyValidator = transferAmountEmptyValidator;
        this.emailValidator = emailValidator;
        this.mobileNumberValidator = mobileNumberValidator;
        this.nonEmptyTextValidator = nonEmptyTextValidator;
        this.mGetAccountsUseCase = mGetAccountsUseCase;
        this.limitsDataUseCase = limitsDataUseCase;
        this.limitsDataToViewModelMapper = limitsDataToViewModelMapper;
        this.navigationRouter = navigationRouter;
        this.errorHandler = errorHandler;
        this.mAnalytics = mAnalytics;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }


    void getAccounts() {
        if (view != null) {
            view.setRequestedAmount(view.getRequestedAmount());
        }

        mGetAccountsUseCase.execute(IAccountOptions.PAYMENT_ACCOUNTS)
                .compose(bindToLifecycle())
                .flatMapIterable(accountViewModels -> accountViewModels)
                .filter(PaymentsUtility::isValidFromAccount)
                .toList()
                .subscribe(payAccountsList -> {
                    if (payAccountsList != null && payAccountsList.size() > 0) {
                        if (view != null) {
                            view.setAccountContainerList(payAccountsList);
                            view.setAccounts();
                        }
                    } else {
                        if (view != null)
                            view.trackFetchAccountsFailure();
                    }
                }, error -> {
                    if (view != null) {
                        view.showError(errorHandler.getErrorMessage(error).getMessage());
                    }
                    trackFailure(false, za.co.nedbank.core.ApiAliasConstants.ACC_LIST, errorHandler.getErrorMessage(error).getMessage(), null);
                });
    }

    boolean validateNotificationType(ValidatableInput<String> mobileNumberEditTextLayout, ValidatableInput<String> emailAddressEditTextLayout) {
        if (view != null) {
            if (view.getNotificationType().equals(NotificationType.SMS.getValue())) {
                ValidationResult validationResult = mobileNumberValidator.validateInput(mobileNumberEditTextLayout.getValue());
                view.showMobileNumberError(validationResult);
                return validationResult.isOk();
            } else if (view.getNotificationType().equals(NotificationType.EMAIL.getValue())) {
                ValidationResult validationResult = emailValidator.validateInput(emailAddressEditTextLayout.getValue());
                view.showEmailError(validationResult);
                return validationResult.isOk();
            } else if (view.getNotificationType().equals(NotificationType.NONE.getValue())) {
                return true;
            }
        }
        return false;
    }

    void checkFields(final ValidatableInput<String> amountInput, double selectedAccountBalance,
                     final ValidatableInput<String> yourReference, final ValidatableInput<String> recipientsReference, ValidatableInput<String> mobileNumberEditTextLayout, ValidatableInput<String> emailAddressEditTextLayout, boolean isViewAvailBalance) {

        accountBalanceValidator.setAvailableBalance(selectedAccountBalance, isViewAvailBalance);
        boolean isBalanceAvailable = accountBalanceValidator.validateInput(amountInput.getValue()).isOk();
        view.setAccountEnabled(isBalanceAvailable);

        boolean isAmountNotEmpty = transferAmountEmptyValidator.validateInput(amountInput.getValue()).isOk();
        amountLimitValidator.setLimit(availableLimit);
        final boolean isAmountWithinLimits = validate(amountInput, amountLimitValidator);

        boolean isYourRefNonEmpty = nonEmptyTextValidator.validateInput(yourReference.getValue()).isOk();
        boolean isRecipientsRefNonEmpty = nonEmptyTextValidator.validateInput(recipientsReference.getValue()).isOk();
        boolean isNotificationTypeCorrect = validateNotificationType(mobileNumberEditTextLayout, emailAddressEditTextLayout);

        if (isBalanceAvailable && isAmountNotEmpty && isAmountWithinLimits && isYourRefNonEmpty && isRecipientsRefNonEmpty && isNotificationTypeCorrect) {
            if (view != null) {
                view.setNextButtonEnabled(true);
            }
        } else {
            if (view != null) {
                view.setNextButtonEnabled(false);
            }
        }
    }

    void getLimitsForAccount() {
        Observable<LimitsInnerDataModel> instantPaymentLimitObservable = limitsDataUseCase.execute(ILimitOptions.INSTANT_PAYMENT_LIMIT).compose(bindToLifecycle());
        Observable<LimitsInnerDataModel> paymentLimitObservable = limitsDataUseCase.execute(ILimitOptions.PAYMENT_LIMIT).compose(bindToLifecycle());
        if (null != instantPaymentLimitObservable && null != paymentLimitObservable) {
            Observable.zip(instantPaymentLimitObservable, paymentLimitObservable,
                    (instantPaymentLimitInnerDataModel, paymentLimitsInnerDataModel) -> {
                        LimitsInnerDataModel[] limitModels = new LimitsInnerDataModel[2];
                        limitModels[0] = paymentLimitsInnerDataModel;
                        limitModels[1] = instantPaymentLimitInnerDataModel;
                        return limitModels;
                    }).compose(bindToLifecycle())
                    .subscribe(limitsData -> {
                        paymentLimitsViewModel = limitsDataToViewModelMapper.mapLimitsDataToViewModel(limitsData[0]);
                        if (view != null) {
                            publishLimits();
                        }
                    }, throwable -> {
                        Error error = errorHandler.getErrorMessage(throwable);
                        if (view != null) {
                            view.showError(error.getMessage());
                        }
                        trackFailure(false, za.co.nedbank.core.ApiAliasConstants.ACC_LIMIT, error.getMessage(), null);
                    });
        }
    }

    private void publishLimits() {
        if (paymentLimitsViewModel != null) {
            availableLimit = paymentLimitsViewModel.getRemainingLimit();
            if (view != null) {
                view.setLimits(paymentLimitsViewModel);
            }
        } else {
            if (view != null)
                view.trackLimitsFailure();
        }
    }

    void showNotificationOptions() {
        if (view != null) {
            view.showNotificationOptions();
        }
    }

    void setNotificationType(NotificationType type) {
        if (view != null) {
            view.setNotificationType(type.getValue());
        }
    }

    void setYourReference() {
        if (view != null) {
            view.setYourReference(view.getReceiverDescription());
        }
    }

    void setPaymentData() {
        PaymentViewModel paymentViewModel = new PaymentViewModel();
        paymentViewModel.setAmount(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(view.amountToPay())));
        paymentViewModel.setBeneficiaryName(view.getReceiverName());
        paymentViewModel.setUserReference(view.getYourReference());
        paymentViewModel.setBeneficiaryReference(view.getRecipientReference());
        paymentViewModel.setSaveBeneficiary(true);
        paymentViewModel.setBankName(Constants.NEDBANK_NAME);
        if (!TextUtils.equals(view.getNotificationType(), NotificationType.NONE.getValue())) {
            paymentViewModel.setNotificationDetailsViewModel(getNotificationDetailsViewModel());
        }
        Date mPaymentDate = new Date();
        mPaymentDate.setTime(Calendar.getInstance().getTimeInMillis());
        paymentViewModel.setStartDate(mPaymentDate.getTime());
        paymentViewModel.setInstantPayment(false);

        AccountViewModel accountViewModel = new AccountViewModel();
        accountViewModel.setAccountNumber(view.getReceiverAccountNumber());
        accountViewModel.setAccountType(view.getReceiverAccountType());
        paymentViewModel.setToAccountViewModel(accountViewModel);
        paymentViewModel.setFromAccountViewModel(view.provideSelectedAccount());

        view.setPaymentDetails(paymentViewModel);
        mAnalytics.sendEvent(TrackingEvent.PAY_ME_SECTION, TrackingParam.PAY_ME_REQUEST_PAID, StringUtils.EMPTY_STRING);
    }

    private NotificationDetailsViewModel getNotificationDetailsViewModel() {
        NotificationDetailsViewModel notificationDetailsViewModel = new NotificationDetailsViewModel();
        notificationDetailsViewModel.setNotificationType(view.getNotificationType());
        if (NotificationType.EMAIL.getValue().equals(view.getNotificationType())) {
            notificationDetailsViewModel.setNotificationAddress(view.getNotificationEmailAddress());
        } else if (NotificationType.SMS.getValue().equals(view.getNotificationType())) {
            notificationDetailsViewModel.setNotificationAddress(view.getNotificationMobileNumber());
        }
        return notificationDetailsViewModel;
    }

    void navigateToMoneyPaymentReview() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MONEY_RESPONSE_REVIEW)
                .withParam(NavigationTarget.PARAM_PAYMENT_MODEL, view.getPaymentModel())
                .withParam(NavigationTarget.PARAM_RECEIVER_MOBILE_NUMBER, view.getReceiverMobileNumber())
                .withParam(NavigationTarget.PARAM_PAYMENT_REQUEST_ID, view.getPaymentRequestId())
                .withParam(NavigationTarget.PARAM_NOTIFICATION_MOBILE_NUMBER, view.getNotificationMobileNumber())
                .withParam(NavigationTarget.PARAM_NOTIFICATION_EMAIL_ADDRESS, view.getNotificationEmailAddress()));
    }

    void trackFetchAccountsFailure(String errorMessage) {
        trackFailure(true, za.co.nedbank.core.ApiAliasConstants.ACC_LIST, errorMessage, null);
    }

    void trackLimitsFailure(String errorMessage) {
        trackFailure(true, za.co.nedbank.core.ApiAliasConstants.ACC_LIMIT, errorMessage, null);
    }

    void trackFailure(boolean isApiFailure, String apiName, String errorMessage, String errorCode) {
        mAnalytics.trackFailure(isApiFailure, AppTracking.TAG_PAY_ME_FAILURE, apiName, errorMessage, errorCode);
    }
}