/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.mapper.money_requests;

import javax.inject.Inject;

import za.co.nedbank.core.data.mapper.MetaDataEntityToDataMapper;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestDataEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentResponseEntity;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestDataModel;
import za.co.nedbank.ui.domain.model.money_request.PaymentResponseModel;

public class MoneyResponseEntityToDataMapper {

    private final MetaDataEntityToDataMapper metaDataEntityToDataMapper;

    @Inject
    MoneyResponseEntityToDataMapper(MetaDataEntityToDataMapper metaDataEntityToDataMapper) {
        this.metaDataEntityToDataMapper = metaDataEntityToDataMapper;
    }

    public PaymentResponseModel mapResponseEntityToData(PaymentResponseEntity paymentResponseEntity) {
        PaymentResponseModel paymentResponseModel = new PaymentResponseModel();
        if (paymentResponseEntity != null) {
            paymentResponseModel.setMetadata(metaDataEntityToDataMapper.mapMetaData(paymentResponseEntity.getMetaDataEntity()));
            paymentResponseModel.setData(mapAccountDataEntityToData(paymentResponseEntity.getMoneyRequestDataEntity()));
        }
        return paymentResponseModel;
    }

    private MoneyRequestDataModel mapAccountDataEntityToData(MoneyRequestDataEntity accountMoneyRequestDataEntity) {
        MoneyRequestDataModel accountMoneyRequestDataModel = new MoneyRequestDataModel();
        if (accountMoneyRequestDataEntity != null) {
            accountMoneyRequestDataModel.setReferenceNumber(accountMoneyRequestDataEntity.getReferenceNumber());
            accountMoneyRequestDataModel.setRequestStatus(accountMoneyRequestDataEntity.getRequestStatus());
        }
        return accountMoneyRequestDataModel;
    }
}
