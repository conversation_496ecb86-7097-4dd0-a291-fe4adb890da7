package za.co.nedbank.ui.view.pop;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryCollectiveDataViewModel;

public interface RecipientDetailsView extends NBBaseView {

    void onItemSelected(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE value, int itemPositionInSection);
    RecipientViewModel getmRecipientViewModel();
    UserBeneficiaryCollectiveDataViewModel getUserBeneficiaryCollectiveDataViewModel();

    void errorSnackBar();

    void handleProgressBar(boolean isVisible);

}
