package za.co.nedbank.ui.view.ita.ita_authentication;

import static za.co.nedbank.core.view.IFragmentToActivityComListener.EVENT_CONSTANTS.EVENT_PARTIAL_LOGIN_SUCCESS;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.enroll.view.authentication.fingerprint.ScanPayFingerprintDialogFragment;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.IFragmentToActivityComListener;
import za.co.nedbank.enroll_v2.Constants;
import za.co.nedbank.enroll_v2.databinding.FragmentFicaApplyBinding;
import za.co.nedbank.ui.di.AppDI;

public class ITAAuthFragment extends NBBaseFragment implements ITAAuthView {
    private static final String TAG = ITAAuthFragment.class.getSimpleName();
    int applyOption;
    private FragmentFicaApplyBinding binding;

    public static ITAAuthFragment getInstance(String fromScreen) {
        Bundle b = new Bundle();
        b.putString(za.co.nedbank.core.Constants.FROM_SCREEN, fromScreen);
        ITAAuthFragment fragment = new ITAAuthFragment();
        fragment.setArguments(b);
        return fragment;
    }

    @Inject
    ITAAuthPresenter itaAuthPresenter;

    private IFragmentToActivityComListener mIFragmentToActivityComListener;

    private String fromScreen = za.co.nedbank.core.Constants.FROM_ITA_FLOW;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentFicaApplyBinding.inflate(inflater, container, false);
        if (getArguments() != null && getArguments().containsKey(za.co.nedbank.core.Constants.FROM_SCREEN)) {
            fromScreen = getArguments().getString(za.co.nedbank.core.Constants.FROM_SCREEN);
        }
        return binding.getRoot();
    }

    @Override
    public void onActivityCreated(@Nullable final Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
        itaAuthPresenter.bind(this);
        applyOption = Constants.ApplyOption.ITA_PRE_LOGIN;
        checkAuthentication(applyOption, fromScreen);
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof IFragmentToActivityComListener) {
            mIFragmentToActivityComListener = (IFragmentToActivityComListener) getActivity();
        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        itaAuthPresenter.unbind();
    }

    private void checkAuthentication(int applyOption, String fromScreen) {
        itaAuthPresenter.checkFPAuthentication(applyOption, fromScreen);
    }

    @Override
    public void isFingerPrintSettingEnabled(boolean isFingerPrintEnabled, int applyOption, String fromScreen) {
        if (isFingerPrintEnabled) {
            ScanPayFingerprintDialogFragment dialogFragment;
            dialogFragment = ScanPayFingerprintDialogFragment.getInstance(false, false, false, false, true, fromScreen);

            dialogFragment.show(getChildFragmentManager(), ScanPayFingerprintDialogFragment.TAG);
        } else if (applyOption == za.co.nedbank.enroll_v2.Constants.ApplyOption.ITA_PRE_LOGIN) {
            itaAuthPresenter.pinPasswordLogin(fromScreen);
        }
    }

    @Override
    public void showRegisterTouchIdError() {
        NBLogger.d(TAG, "showRegisterTouchIdError");
        showProgressVisible(false);
        showError(getString(za.co.nedbank.core.R.string.dialog_error_general), getString(za.co.nedbank.core.R.string.error_generic));
    }

    @Override
    public void showTnCLink(boolean isVisible) {
        // Do Nothing
    }

    @Override
    public void showProgressVisible(boolean isVisible) {
        if (isVisible) {
            ViewUtils.showViews(binding.loadingView);
        } else {
            ViewUtils.hideViews(binding.loadingView);
        }
        setEnabledActivityTouch(!isVisible);
    }

    @Override
    public void showError(String errorMessage) {
        NBLogger.d(TAG, "show Register TouchId Error");
        showProgressVisible(false);
        showError("Authentication", errorMessage);
    }

    @Override
    public void returnResult() {
        if (mIFragmentToActivityComListener != null) {
            mIFragmentToActivityComListener.onEvent(EVENT_PARTIAL_LOGIN_SUCCESS, null);
        }
    }

    @Override
    public boolean isFromScanTnCScreen() {
        return false;
    }

    @Override
    public boolean isComingFromHomeLoanScreen() {
        return false;
    }

    @Override
    public boolean isComingFromPreLoginFicaScreen() {
        return false;
    }

    public void pinPasswordLogin(String fromScreen) {
        itaAuthPresenter.pinPasswordLogin(fromScreen);
    }

    public void handleFingerPrintDetectSuccess() {
        itaAuthPresenter.handleFPDetectSuccess();
    }
}
