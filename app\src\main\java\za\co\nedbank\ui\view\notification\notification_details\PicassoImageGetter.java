package za.co.nedbank.ui.view.notification.notification_details;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.Html;

import com.squareup.picasso.Picasso;
import com.squareup.picasso.Target;

import za.co.nedbank.R;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.uisdk.component.CompatTextViewEnhanced;

public class PicassoImageGetter implements Html.ImageGetter {

    private CompatTextViewEnhanced textView;
    public static final int ASPECT_WIDTH = 730;
    public static final int ASPECT_HEIGHT = 340;
    private int mActualImageWidth;
    private Activity mActivity;

    public PicassoImageGetter(CompatTextViewEnhanced target, Activity activity) {
        textView = target;
        mActivity = activity;
    }

    @Override
    public Drawable getDrawable(String source) {
        BitmapDrawablePlaceHolder drawable = new BitmapDrawablePlaceHolder();
        Picasso.get()
                .load(source)
                .placeholder(R.drawable.progress_drawable)
                .into(drawable);
        return drawable;
    }

    private class BitmapDrawablePlaceHolder extends BitmapDrawable implements Target {

        protected Drawable drawable;

        @Override
        public void draw(final Canvas canvas) {
            if (drawable != null) {
                drawable.draw(canvas);
            }
        }

        public void setDrawable(Drawable drawable) {
            this.drawable = drawable;
            int deviceWidth = DeviceUtils.getDeviceWidth(mActivity);
            mActualImageWidth = (deviceWidth - (2 * mActivity.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_10dp)));
            int mActualImageHeight = (mActualImageWidth * ASPECT_HEIGHT) / ASPECT_WIDTH;

            drawable.setBounds(0, 0, mActualImageWidth, mActualImageHeight);
            setBounds(0, 0, mActualImageWidth, mActualImageHeight);
            if (textView != null) {
                textView.setText(textView.getText());
            }
        }

        @Override
        public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
            if(textView!=null){
                setDrawable(new BitmapDrawable(textView.getResources(), bitmap));
            }
        }
        @Override
        public void onBitmapFailed(Exception e, Drawable errorDrawable) {
           // Do Nothing
        }

        @Override
        public void onPrepareLoad(Drawable placeHolderDrawable) {
           //Do Nothing
        }

    }
}
