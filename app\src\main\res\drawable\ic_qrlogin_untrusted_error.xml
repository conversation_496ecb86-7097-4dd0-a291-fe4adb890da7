<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">
  <path
      android:pathData="M90.536,91L90.536,105C90.536,110.523 86.059,115 80.536,115L38.964,115C33.441,115 28.964,110.523 28.964,105L28.964,91L90.536,91ZM59.833,99.204C57.594,99.204 55.78,101.019 55.78,103.257C55.78,105.496 57.594,107.31 59.833,107.31C62.071,107.31 63.886,105.496 63.886,103.257C63.886,101.019 62.071,99.204 59.833,99.204Z"
      android:strokeWidth="1"
      android:fillColor="#E4F2D2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M80.536,5C86.059,5 90.536,9.477 90.536,15L90.536,15L90.536,20.99L28.536,20.99L28.536,15C28.536,9.477 33.013,5 38.536,5L38.536,5ZM48.536,11C47.432,11 46.536,11.895 46.536,13C46.536,14.105 47.432,15 48.536,15C49.641,15 50.536,14.105 50.536,13C50.536,11.895 49.641,11 48.536,11ZM71.536,11L57.536,11C56.432,11 55.536,11.895 55.536,13C55.536,14.105 56.432,15 57.536,15L57.536,15L71.536,15C72.641,15 73.536,14.105 73.536,13C73.536,11.895 72.641,11 71.536,11L71.536,11Z"
      android:strokeWidth="1"
      android:fillColor="#E4F2D2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M78.648,63.042L64.247,37.67C62.737,35.01 58.89,35.006 57.378,37.67L42.977,63.042C41.484,65.671 43.39,68.941 46.411,68.941L75.213,68.941C78.236,68.941 80.139,65.671 78.648,63.042L78.648,63.042Z"
      android:strokeWidth="1"
      android:fillColor="#E4F2D2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M81.31,4C87.192,4 92,8.814 92,14.7L92,14.7L92,105.301C92,111.189 87.193,116 81.31,116L81.31,116L41,116C33.82,116 28,110.18 28,103L28,103L28,14.7C28,8.814 32.808,4 38.69,4L38.69,4ZM90,92L30,92L30,103C30,108.979 34.77,113.843 40.712,113.996L41,114L81.31,114C86.088,114 90,110.085 90,105.301L90,105.301L90,92ZM59.833,98.204C62.624,98.204 64.886,100.466 64.886,103.257C64.886,106.048 62.624,108.31 59.833,108.31C57.042,108.31 54.78,106.048 54.78,103.257C54.78,100.466 57.042,98.204 59.833,98.204ZM59.833,100.204C58.147,100.204 56.78,101.571 56.78,103.257C56.78,104.943 58.147,106.31 59.833,106.31C61.519,106.31 62.886,104.943 62.886,103.257C62.886,101.571 61.519,100.204 59.833,100.204ZM90,21.99L30,21.99L30,90L90,90L90,21.99ZM56.508,37.177C58.402,33.838 63.223,33.84 65.116,37.176L65.116,37.176L79.518,62.549C81.387,65.844 79.002,69.941 75.213,69.941L75.213,69.941L46.411,69.941C42.623,69.941 40.236,65.844 42.107,62.549L42.107,62.549ZM63.377,38.164C62.25,36.177 59.375,36.176 58.247,38.164L58.247,38.164L43.847,63.536C42.733,65.498 44.156,67.941 46.411,67.941L46.411,67.941L75.213,67.941C77.469,67.941 78.891,65.498 77.778,63.536L77.778,63.536ZM60.948,59.766C61.861,59.766 62.576,60.459 62.576,61.372C62.576,62.285 61.861,63 60.948,63C60.035,63 59.342,62.285 59.342,61.372C59.342,60.459 60.035,59.766 60.948,59.766ZM62.114,47.193L62.114,57.731L59.804,57.731L59.804,47.193L62.114,47.193ZM81.31,6L38.69,6C33.913,6 30,9.918 30,14.7L30,14.7L30,19.99L90,19.99L90,14.7C90,10.005 86.228,6.143 81.57,6.004L81.31,6ZM48.536,10C50.193,10 51.536,11.343 51.536,13C51.536,14.657 50.193,16 48.536,16C46.879,16 45.536,14.657 45.536,13C45.536,11.343 46.879,10 48.536,10ZM71.536,10C73.193,10 74.536,11.343 74.536,13C74.536,14.657 73.193,16 71.536,16L57.536,16C55.879,16 54.536,14.657 54.536,13C54.536,11.343 55.879,10 57.536,10L71.536,10ZM48.536,12C47.984,12 47.536,12.448 47.536,13C47.536,13.552 47.984,14 48.536,14C49.088,14 49.536,13.552 49.536,13C49.536,12.448 49.088,12 48.536,12ZM71.536,12L57.536,12C56.984,12 56.536,12.448 56.536,13C56.536,13.552 56.984,14 57.536,14L71.536,14C72.088,14 72.536,13.552 72.536,13C72.536,12.448 72.088,12 71.536,12Z"
      android:strokeWidth="1"
      android:fillColor="#009639"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
</vector>
