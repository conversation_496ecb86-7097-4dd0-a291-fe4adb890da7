/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.money_request;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.ui.data.entity.money_request.PaymentRequestEntity;
import za.co.nedbank.ui.data.mapper.money_requests.MoneyRequestDataToEntityMapper;
import za.co.nedbank.ui.data.mapper.money_requests.MoneyResponseEntityToDataMapper;
import za.co.nedbank.ui.domain.model.money_request.PaymentRequestDataModel;
import za.co.nedbank.ui.domain.model.money_request.PaymentResponseModel;
import za.co.nedbank.ui.domain.repository.IMoneyRequestRepository;

public class PaymentRequestsUseCase extends UseCase<PaymentRequestDataModel, PaymentResponseModel> {
    private final IMoneyRequestRepository iMoneyRequestRepository;
    private final MoneyResponseEntityToDataMapper moneyResponseEntityToDataMapper;
    private final MoneyRequestDataToEntityMapper moneyRequestDataToEntityMapper;


    @Inject
    PaymentRequestsUseCase(final UseCaseComposer useCaseComposer, final IMoneyRequestRepository iMoneyRequestRepository,
                           MoneyResponseEntityToDataMapper moneyResponseEntityToDataMapper, MoneyRequestDataToEntityMapper moneyRequestDataToEntityMapper) {
        super(useCaseComposer);
        setCacheObservable(false);
        this.iMoneyRequestRepository = iMoneyRequestRepository;
        this.moneyRequestDataToEntityMapper = moneyRequestDataToEntityMapper;
        this.moneyResponseEntityToDataMapper = moneyResponseEntityToDataMapper;
    }

    @Override
    protected Observable<PaymentResponseModel> createUseCaseObservable(PaymentRequestDataModel paymentRequestDataModel) {
        PaymentRequestEntity paymentRequest = moneyRequestDataToEntityMapper.mapPaymentRequestModelToPaymentRequestEntity(paymentRequestDataModel);
        return iMoneyRequestRepository.sendPaymentsRequest(paymentRequest).map(moneyResponseEntityToDataMapper::mapResponseEntityToData);
    }
}
