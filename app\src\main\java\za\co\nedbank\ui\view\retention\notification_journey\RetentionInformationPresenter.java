package za.co.nedbank.ui.view.retention.notification_journey;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.ui.view.retention.RetentionConstants;
import za.co.nedbank.ui.view.tracking.AppTracking;


public class RetentionInformationPresenter extends NBBasePresenter<RetentionInformationView> {

    private final Analytics mAnalytics;

    @Inject
    RetentionInformationPresenter(final Analytics analytics) {
        this.mAnalytics = analytics;
    }

    public void setHeaderAndInfoText() {
        if(view != null) {
            view.setHeaderAndInfoText();
        }
    }

    public void sendPageEvent(RetentionConstants.RetentionInfoType retentionInfoType) {
        if (retentionInfoType!=null) {
            if (retentionInfoType == RetentionConstants.RetentionInfoType.SHARE_ACCOUNT) {
                mAnalytics.sendState(AppTracking.RETENTION_SCREEN_SHARE_ACCOUNT_INFORMATION);
            } else  if (retentionInfoType == RetentionConstants.RetentionInfoType.LOGIN_SECURITY) {
                mAnalytics.sendState(AppTracking.RETENTION_SCREEN_LOGIN_AND_SECURITY_INFORMATION);
            } else  if (retentionInfoType == RetentionConstants.RetentionInfoType.PROFILE_LIMIT) {
                mAnalytics.sendState(AppTracking.RETENTION_SCREEN_PROFILE_LIMITS_INFORMATION);
            } else  if (retentionInfoType == RetentionConstants.RetentionInfoType.DEBIT_ORDER_SWITCHING) {
                mAnalytics.sendState(AppTracking.RETENTION_SCREEN_DEBIT_ORDER_INFORMATION);
            }
        }
    }

    public void sendBackArrowAnalytics(RetentionConstants.RetentionInfoType retentionInfoType) {
        if (retentionInfoType!=null) {
            if (retentionInfoType == RetentionConstants.RetentionInfoType.SHARE_ACCOUNT) {
                mAnalytics.sendEvent(AppTracking.RETENTION_CLICK_SHARE_ACCOUNT_INFORMATION_BACK, StringUtils.EMPTY_STRING,StringUtils.EMPTY_STRING);
            } else  if (retentionInfoType == RetentionConstants.RetentionInfoType.LOGIN_SECURITY) {
                mAnalytics.sendEvent(AppTracking.RETENTION_CLICK_LOGIN_AND_SECURITY_INFORMATION_BACK, StringUtils.EMPTY_STRING,StringUtils.EMPTY_STRING);
            } else  if (retentionInfoType == RetentionConstants.RetentionInfoType.PROFILE_LIMIT) {
                mAnalytics.sendEvent(AppTracking.RETENTION_CLICK_PROFILE_LIMITS_INFORMATION_BACK, StringUtils.EMPTY_STRING,StringUtils.EMPTY_STRING);
            } else  if (retentionInfoType == RetentionConstants.RetentionInfoType.DEBIT_ORDER_SWITCHING) {
                mAnalytics.sendEvent(AppTracking.RETENTION_CLICK_DEBIT_ORDER_INFORMATION_BACK, StringUtils.EMPTY_STRING,StringUtils.EMPTY_STRING);
            }
        }
    }
}
