package za.co.nedbank.ui.view.notification.transaction_notification.inbox;

import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.Observable;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.base.adapter.SectionAdapterItem;
import za.co.nedbank.core.domain.CachableValue;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.FBNotificationMultipleAnalyticData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsAnalyticData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsGetRequestData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsMetaData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsResponseData;
import za.co.nedbank.core.domain.model.notifications.FBResponseData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.notifications.FBNotificationsAnalyticsUseCase;
import za.co.nedbank.core.domain.usecase.notifications.FBNotificationsMultipleAnalyticsUseCase;
import za.co.nedbank.core.domain.usecase.notifications.GetFBNotificationsDataUseCase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.core.view.mapper.fbnotifications.FBNotificationsDataToViewModelMapper;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;

public class TransactionInboxPresenter extends NBBasePresenter<TransactionInboxView> {

    public static final String TAG = TransactionInboxPresenter.class.getSimpleName();

    private final NavigationRouter mNavigationRouter;
    private final GetFBNotificationsDataUseCase mGetNotificationUseCase;
    private FBNotificationsDataToViewModelMapper mFbNotificationsDataToViewModelMapper;
    private final ErrorHandler mErrorHandler;
    private FBNotificationsAnalyticsUseCase mFBNotificationsAnalyticsUseCase;
    private final ApplicationStorage mApplicationStorage;
    private final FBNotificationsMultipleAnalyticsUseCase mFbNotificationsMultipleAnalyticsUseCase;
    private ApplicationStorage mMemoryApplicationStorage;
    private boolean hasTransactableAccount;
    private String mClientType;
    private String mFicaStatus;
    private final GetOverviewUseCase mGetOverviewUseCase;
    private final GetUserDetailUseCase mGetUserDetailUseCase;
    private final FeatureSetController mFeatureSetController;
    private final Analytics mAnalytics;

    @Inject
    public TransactionInboxPresenter(NavigationRouter mNavigationRouter,
                                     GetFBNotificationsDataUseCase getNotificationUseCase,
                                     FBNotificationsDataToViewModelMapper mFbNotificationsDataToViewModelMapper,
                                     FBNotificationsAnalyticsUseCase mFBNotificationsAnalyticsUseCase,
                                     FBNotificationsMultipleAnalyticsUseCase mFbNotificationsMultipleAnalyticsUseCase,
                                     ErrorHandler mErrorHandler,
                                     @Named("memory") ApplicationStorage memoryApplicationStorage,
                                     ApplicationStorage mApplicationStorage,
                                     GetOverviewUseCase getOverviewUseCase,
                                     GetUserDetailUseCase getUserDetailUseCase,
                                     FeatureSetController featureSetController,
                                     final Analytics analytics) {
        this.mNavigationRouter = mNavigationRouter;
        this.mGetNotificationUseCase = getNotificationUseCase;
        this.mFbNotificationsDataToViewModelMapper = mFbNotificationsDataToViewModelMapper;
        this.mErrorHandler = mErrorHandler;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.mApplicationStorage = mApplicationStorage;
        this.mFbNotificationsMultipleAnalyticsUseCase = mFbNotificationsMultipleAnalyticsUseCase;
        this.mFBNotificationsAnalyticsUseCase = mFBNotificationsAnalyticsUseCase;
        this.mGetOverviewUseCase = getOverviewUseCase;
        this.mGetUserDetailUseCase = getUserDetailUseCase;
        this.mFeatureSetController = featureSetController;
        this.mAnalytics = analytics;
    }

    public void navigateToReportPage(FBTransactionNotificationsViewModel transaction) {
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_TRANSACTION_NOTIFICATION_REPORT_FRAUD)
                    .withParam(NavigationTarget.PARAM_NAME, transaction));
    }

    public void loadMessages(int pageNo) {
        FBNotificationsGetRequestData fbNotificationsGetRequestData = new FBNotificationsGetRequestData();
        fbNotificationsGetRequestData.setPageNum(pageNo);
        fbNotificationsGetRequestData.setNumOfRecords(NotificationConstants.PAGINATION.DEFAULT_PAGE_SIZE);
        fbNotificationsGetRequestData.setType(NotificationConstants.NOTIFICATION_STATUS_TYPES.TRANSACTION_NOTIFICATION_TYPE);

        mGetNotificationUseCase.execute(fbNotificationsGetRequestData)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgress(true);
                    }
                })
                .doFinally(() -> {
                    if (view != null) {
                        view.showProgress(false);
                    }
                })
                .subscribe(fbNotificationsResponseData -> {
                    handleNotificationResponse(fbNotificationsResponseData, pageNo);
                    view.changeEmptyView();

                }, throwable -> {
                    if (view != null) {
                        view.showErrorForNotificationMessages(mErrorHandler.getErrorMessage(throwable).getMessage());
                    }
                });

    }

    public void loadMessages() {

        FBNotificationsGetRequestData fbNotificationsGetRequestData = new FBNotificationsGetRequestData();
        fbNotificationsGetRequestData.setPageNum(NotificationConstants.PAGINATION.DEFAULT_PAGE_NO);
        fbNotificationsGetRequestData.setNumOfRecords(NotificationConstants.PAGINATION.DEFAULT_PAGE_SIZE);
        fbNotificationsGetRequestData.setType(NotificationConstants.NOTIFICATION_STATUS_TYPES.TRANSACTION_NOTIFICATION_TYPE);

        mGetNotificationUseCase.execute(fbNotificationsGetRequestData)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgress(true);
                    }
                })
                .doFinally(() -> {
                    if (view != null) {
                        view.showProgress(false);
                    }
                })
                .subscribe(fbNotificationsResponseData -> {
                    handleNotificationResponse(fbNotificationsResponseData, NotificationConstants.PAGINATION.DEFAULT_PAGE_NO);
                    view.changeEmptyView();
                    view.showListLoaded();

                }, throwable -> {
                    if (view != null) {
                        view.showErrorForNotificationMessages(mErrorHandler.getErrorMessage(throwable).getMessage());
                    }
                });

    }

    private void handleNotificationResponse(FBNotificationsResponseData fbNotificationsResponseData, int pageNo) {
        int ONLY_SUCCESS_ELEMENT = 1;
        if (fbNotificationsResponseData != null && fbNotificationsResponseData.getMetaData() != null) {
            FBNotificationsMetaData metaDataModel = fbNotificationsResponseData.getMetaData();
            for (ResultDataModel resultDataModel : metaDataModel.getResultData()) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && resultDetailList.size() > 0) {
                    if (metaDataModel.getResultData().size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        if (view != null && fbNotificationsResponseData.getFbNotificationsDataList() != null) {
                            List<FBTransactionNotificationsViewModel> items = mFbNotificationsDataToViewModelMapper.transformToTransactionViewModel(fbNotificationsResponseData.getFbNotificationsDataList());
                            if (items != null && !items.isEmpty()) {
                                Collections.sort(items, compareByDate);
                            }
                            if (pageNo == NotificationConstants.PAGINATION.DEFAULT_PAGE_NO) {
                                view.updateNotificationMessages(items);
                            } else {
                                view.updateNotificationMessagesPageData(items);
                            }
                        }
                        break;
                    } else {
                        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
                            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                                NBLogger.e(TAG, resultDetailViewModel.getReason());
                            }
                        }
                    }
                }
            }
        }
    }

    public void filter(String searchWord, String filter, List<FBTransactionNotificationsViewModel> searchList) {
        List<FBTransactionNotificationsViewModel> transactions = new ArrayList<>();
        if (searchList != null && !searchList.isEmpty()) {
            if (searchWord.trim().length() > 0) {
                for (FBTransactionNotificationsViewModel transaction : searchList) {
                    if (transaction.getMetaChannelName().toLowerCase().contains(searchWord.toLowerCase())
                            || transaction.getMetaAmount().contains(searchWord.toLowerCase()))
                        transactions.add(transaction);
                }
            } else {
                transactions.addAll(searchList);
            }
        }
        view.onSearchFilter(transactions, filter);
    }

    public void markRead(FBTransactionNotificationsViewModel notificationsViewModel) {
        view.showProgress(true);
        if (!notificationsViewModel.isRead()) {
            changeReadCounterBy(1);
            notificationsViewModel.setRead(true);
            notificationsViewModel.setStatus(NotificationConstants.NOTIFICATION_STATUS_TYPES.NOTIFICATION_READ);
        }
        sendAnalyticsToServer(notificationsViewModel);
    }

    public void undoDeletedItems(List<SectionAdapterItem<FBTransactionNotificationsViewModel>> sectionAdapterItems) {
        view.restoreDeletedItems();
        mFbNotificationsMultipleAnalyticsUseCase.execute(createMultipleDeleteOrUndoRequest(sectionAdapterItems, NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_UNDO))
                .compose(bindToLifecycle())
                .subscribe(fbResponseData -> {
                    //background api no need to handle
                }, throwable -> {
                    //  no need to handle background api
                    NBLogger.e(TAG, throwable.getMessage());
                });
    }

    public void deleteMessage(SectionAdapterItem<FBTransactionNotificationsViewModel> notificationsViewModel) {
        view.removeDeletedItem(notificationsViewModel);
        view.showUndoDeleteOption(1);
        mFBNotificationsAnalyticsUseCase.execute(createDeleteAnalyticsRequest(notificationsViewModel))
                .compose(bindToLifecycle())
                .subscribe(analyticsResponseData -> {
                    handleAnalyticsResponse(analyticsResponseData, false);
                }, throwable -> {
                    //  no need to handle background api
                    NBLogger.e(TAG, throwable.getMessage());
                });
    }

    public void deleteMessages(List<SectionAdapterItem<FBTransactionNotificationsViewModel>> selectedItems) {
        if (selectedItems.isEmpty()) return;

        int noOfDeleteditems = 0;
        for (SectionAdapterItem<FBTransactionNotificationsViewModel> selectedItem : selectedItems) {
            if (selectedItem.getContent() != null)
                noOfDeleteditems += 1;
        }
        changeReadCounterBy(noOfDeleteditems);
        view.clearDeletedItems();
        view.endSelection();
        view.showUndoDeleteOption(noOfDeleteditems);
        mFbNotificationsMultipleAnalyticsUseCase.execute(createMultipleDeleteOrUndoRequest(selectedItems, NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_DELETE))

                .compose(bindToLifecycle())
                .subscribe(fbResponseData -> {
                    handleAnalyticsResponse(fbResponseData, true);
                }, throwable -> {
                    //  no need to handle background api
                    NBLogger.e(TAG, throwable.getMessage());
                });
    }

    private void changeReadCounterBy(int readCount) {
        int unreadCount = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, 0);
        mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, unreadCount - readCount);
        int totalUnreadNotificationCount = mApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, 0);
        mApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, totalUnreadNotificationCount - readCount);
    }


    public void handleMessageItemClick(SectionAdapterItem<FBTransactionNotificationsViewModel> notificationsViewModel) {
        markRead(notificationsViewModel.getContent());
    }

    Comparator<FBTransactionNotificationsViewModel> compareByDate = (FBTransactionNotificationsViewModel o1, FBTransactionNotificationsViewModel o2) ->
            (o2.getMetaDate()).compareTo(o1.getMetaDate());

    Comparator<FBTransactionNotificationsViewModel> compareByDateRecent = (FBTransactionNotificationsViewModel o1, FBTransactionNotificationsViewModel o2) ->
            (o1.getMetaDate()).compareTo(o2.getMetaDate());

    Comparator<FBTransactionNotificationsViewModel> compareByAmount = (FBTransactionNotificationsViewModel o1, FBTransactionNotificationsViewModel o2) ->
            Double.compare(o1.getMetaAmountInDouble(), o2.getMetaAmountInDouble());

    Comparator<FBTransactionNotificationsViewModel> compareByAmountReversed = (FBTransactionNotificationsViewModel o1, FBTransactionNotificationsViewModel o2) ->
            Double.compare(o2.getMetaAmountInDouble(), o1.getMetaAmountInDouble());

    Comparator<FBTransactionNotificationsViewModel> compareByAccountNumber = (FBTransactionNotificationsViewModel o1, FBTransactionNotificationsViewModel o2) ->
            (o1.getMetaAccountname()).compareTo(o2.getMetaAccountname());

    Comparator<FBTransactionNotificationsViewModel> compareByCardNumber = (FBTransactionNotificationsViewModel o1, FBTransactionNotificationsViewModel o2) ->
            (o1.getMetaCardnumber()).compareTo(o2.getMetaCardnumber());

    Comparator<FBTransactionNotificationsViewModel> compareByReadStatus = (FBTransactionNotificationsViewModel o1, FBTransactionNotificationsViewModel o2) ->
            (o1.isRead()).compareTo(o2.isRead());

    Comparator<FBTransactionNotificationsViewModel> compareByTransactionType = (FBTransactionNotificationsViewModel o1, FBTransactionNotificationsViewModel o2) ->
            (o1.getMetaTransType()).compareTo(o2.getMetaTransType());


    public void filterTransactionList(String sortParameter, List<FBTransactionNotificationsViewModel> transactionList) {
        List<FBTransactionNotificationsViewModel> filterList = new ArrayList<>();
        try {
            switch (sortParameter) {
                case Constants.FilterTypes.MOST_RECENT_DATE:
                    filterList.addAll(transactionList);
                    Collections.sort(filterList, compareByDate);
                    break;
                case Constants.FilterTypes.OLDEST_DATE:
                    filterList.addAll(transactionList);
                    Collections.sort(filterList, compareByDateRecent);
                    break;
                case Constants.FilterTypes.LOWEST_AMOUNT:
                    filterList.addAll(transactionList);
                    Collections.sort(filterList, compareByAmount);
                    break;
                case Constants.FilterTypes.HIGHEST_AMOUNT:
                    filterList.addAll(transactionList);
                    Collections.sort(filterList, compareByAmountReversed);
                    break;
                case Constants.FilterTypes.READ_UNREAD:
                    filterList.addAll(transactionList);
                    Collections.sort(filterList, compareByReadStatus);
                    break;
                case Constants.FilterTypes.TRANSACTION_TYPE:
                    filterList.addAll(transactionList);
                    Collections.sort(filterList, compareByTransactionType);
                    break;
                case Constants.FilterTypes.ACC_NUMBER:
                    filterList.addAll(transactionList);
                    Collections.sort(filterList, compareByAccountNumber);
                    break;
                case Constants.FilterTypes.CARD_NUMBER:
                    filterList.addAll(transactionList);
                    Collections.sort(filterList, compareByCardNumber);
                    break;
                case Constants.FilterTypes.MONEY_OUT:
                    for (FBTransactionNotificationsViewModel viewModel : transactionList) {
                        if (viewModel.getMetaDebitedCreditInd() != null && viewModel.getMetaDebitedCreditInd().equalsIgnoreCase(NotificationConstants.TRANSACTION_META_TYPES.DEBIT))
                            filterList.add(viewModel);
                    }
                    Collections.sort(filterList, compareByDate);
                    break;
                case Constants.FilterTypes.MONEY_IN:
                    for (FBTransactionNotificationsViewModel viewModel : transactionList) {
                        if (viewModel.getMetaDebitedCreditInd() != null && !viewModel.getMetaDebitedCreditInd().equalsIgnoreCase(NotificationConstants.TRANSACTION_META_TYPES.DEBIT))
                            filterList.add(viewModel);
                    }
                    Collections.sort(filterList, compareByDate);
                    break;

                default:
                    break;
            }
        } catch (Exception e) {
            NBLogger.e("Transaction Filter", e.getMessage());
        }
        view.onSearchFilter(filterList, sortParameter);
    }

    void sendAnalyticsToServer(FBTransactionNotificationsViewModel fbTransactionNotificationsViewModel) {

        FBNotificationsAnalyticData fbNotificationsAnalyticData = createAnalyticsRequest(fbTransactionNotificationsViewModel);
        Log.e("data", fbNotificationsAnalyticData.getNotificationId() + "");
        mFBNotificationsAnalyticsUseCase.execute(fbNotificationsAnalyticData)
                .compose(bindToLifecycle())
                .subscribe(analyticsResponseData -> {
                    view.markReadMessage(fbTransactionNotificationsViewModel, true);
                    view.showProgress(false);
                }, throwable -> {
                    NBLogger.e(TAG, throwable.getMessage());
                });
    }


    private FBNotificationsAnalyticData createAnalyticsRequest(FBTransactionNotificationsViewModel fbTransactionNotificationsViewModel) {
        FBNotificationsAnalyticData analyticsRequest = new FBNotificationsAnalyticData();
        FBNotificationsAnalyticData.Analytic analytic = new FBNotificationsAnalyticData.Analytic();
        analytic.setDeviceId(getDeviceID());
        analytic.setAnalyticValue(NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_READ);
        analytic.setDeviceDate(getDate());
        analyticsRequest.setAnalytic(analytic);

        analyticsRequest.setNotificationId(fbTransactionNotificationsViewModel.getNotificationId());
        return analyticsRequest;
    }

    public String getDeviceID() {
        return mApplicationStorage.getString(NotificationConstants.STORAGE_KEYS.UUID, StringUtils.EMPTY_STRING);
    }

    private String getDate() {
        SimpleDateFormat dateFormatForParsing = new SimpleDateFormat(FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
        dateFormatForParsing.setTimeZone(TimeZone.getTimeZone(FormattingUtil.SOUTH_AFRICA_TIMZONE_ID));
        return dateFormatForParsing.format(new Date());
    }

    private FBNotificationMultipleAnalyticData createMultipleDeleteOrUndoRequest(List<SectionAdapterItem<FBTransactionNotificationsViewModel>> selectedItems, String analyticType) {
        FBNotificationMultipleAnalyticData fbNotificationMultipleAnalyticData = new FBNotificationMultipleAnalyticData();
        FBNotificationMultipleAnalyticData.Analytic analytic = new FBNotificationMultipleAnalyticData.Analytic();
        analytic.setAnalyticValue(analyticType);
        analytic.setDeviceId(getDeviceID());
        analytic.setDeviceDate(getDate());
        fbNotificationMultipleAnalyticData.setAnalytic(analytic);

        List<FBNotificationMultipleAnalyticData.Notification> notifications = new ArrayList<>();
        for (SectionAdapterItem<FBTransactionNotificationsViewModel> notificationsViewModel : selectedItems) {
            if (notificationsViewModel.getContent() != null) {
                FBNotificationMultipleAnalyticData.Notification notification = new FBNotificationMultipleAnalyticData.Notification();
                notification.setNotificationId(notificationsViewModel.getContent().getNotificationId());
                notifications.add(notification);
            }
        }
        fbNotificationMultipleAnalyticData.setNotifications(notifications);
        return fbNotificationMultipleAnalyticData;
    }

    private void handleAnalyticsResponse(FBResponseData fbResponseData, boolean isMultiple) {
        boolean isFailed = false;
        int ONLY_SUCCESS_ELEMENT = 1;
        if (fbResponseData != null && fbResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = fbResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && resultDetailList.size() > 0) {
                    if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        // success case
                        NBLogger.e(TAG, "delete analytics api success, notification id :" + resultDataModel.getResultDetail().get(0).getOperationReference());
                        if(view != null)
                            view.setDeleteSuccess(true);
                        trackDeleteApiSuccessAction(isMultiple ? 2:1);

                    } else {
                        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
                            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                                NBLogger.e(TAG, "delete analytics api failure, notification id :" + resultDataModel.getResultDetail().get(0).getOperationReference() + " Reason: " + resultDetailViewModel.getReason());
                                isFailed = true;
                            }
                        }
                    }
                }
            }
            if (isFailed && isMultiple) {
                displayErrorMsg();
            }

        }
    }

    private void displayErrorMsg() {
        if (view != null) {
            view.showErrorForNotificationMessages(view.getDeleteErrorMsg());
        }
    }

    private FBNotificationsAnalyticData createDeleteAnalyticsRequest(SectionAdapterItem<FBTransactionNotificationsViewModel> notificationsViewModel) {
        FBNotificationsAnalyticData fbNotificationsAnalyticData = new FBNotificationsAnalyticData();
        fbNotificationsAnalyticData.setNotificationId(notificationsViewModel.getContent().getNotificationId());
        FBNotificationsAnalyticData.Analytic analytic = new FBNotificationsAnalyticData.Analytic();
        fbNotificationsAnalyticData.setNotificationId(notificationsViewModel.getContent().getNotificationId());
        analytic.setDeviceId(getDeviceID());
        analytic.setDeviceDate(getDate());
        analytic.setAnalyticValue(NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_DELETE);
        fbNotificationsAnalyticData.setAnalytic(analytic);

        return fbNotificationsAnalyticData;
    }


    void clearNotificationModelFromMemory() {
        mMemoryApplicationStorage.clearValue(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL);
    }

    void handleDebitOrderListFlow(String accountNo) {
        Observable<CachableValue<Overview>> overviewObservable = mGetOverviewUseCase.execute();
        Observable<UserDetailData> userDetailsObservable = mGetUserDetailUseCase.execute(false);
        if (null != overviewObservable && null != userDetailsObservable) {
            Observable.zip(overviewObservable, userDetailsObservable,
                    (overviewCachableValue, userDetailData) -> {
                        if (null != overviewCachableValue && null != userDetailData) {
                            Overview overviewValue = overviewCachableValue.get().clone();
                            hasTransactableAccount = checkForAnyTransactableAccount(overviewValue);
                            mClientType = userDetailData.getClientType();
                            mFicaStatus = userDetailData.getFicaStatus();
                            return getAccountSummary(overviewValue, accountNo);
                        }
                        return null;
                    }).compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        if (view != null) {
                            view.showProgress(true);
                        }
                    })
                    .doOnTerminate(() -> {
                        if (view != null) {
                            view.showProgress(false);
                        }
                    }).subscribe(accountSummary -> {
                        if (null != view && null != accountSummary) {
                            navigateToAccountDetails(accountSummary, NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_LIST);
                        } else {
                            handleNavigationErrorFlow();
                        }

                    },
                    error -> {
                        if (view != null)
                            handleNavigationErrorFlow();
                    });
        }
    }

    private void handleNavigationErrorFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_ERROR));
    }

    private AccountSummary getAccountSummary(Overview overview, String accountNum) {
        AccountSummary accountSummary = null;
        for (AccountsOverview accountsOverview : overview.accountsOverviews) {
            for (AccountSummary summary : accountsOverview.accountSummaries) {
                if (summary.getNumber() != null && summary.getNumber().equalsIgnoreCase(accountNum)) {
                    accountSummary = summary;
                }
            }
        }
        return accountSummary;

    }

    private boolean checkForAnyTransactableAccount(Overview overview) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.CREDIT_CARDS ||
                        accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.accountSummaries != null) {
                    for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
                        if (!accountSummary.isDormantAccount()) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private void navigateToAccountDetails(AccountSummary accountSummary, String target) {
        mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, canTransact())
                .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, mClientType)
                .withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate())
                .withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice())
                .withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit())
                .withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct())
                .withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit())
                .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, mFicaStatus)
                .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                .withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, target)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber())
        );
    }

    private boolean canTransact() {
        return hasTransactableAccount;
    }

   public void trackDeleteApiSuccessAction(int noOfDeletedMessages) {
       if (view != null && view.isUndoTimerComplete() && view.isDeleteSuccess()) {
           view.setDeleteSuccess(false);
           HashMap<String, Object> cdata = new HashMap<>();
           AdobeContextData adobeContextData = new AdobeContextData(cdata);
           adobeContextData.setValue(NotificationConstants.TRACKING_PARAMS.DELETE_TRANSACTIONAL_NOTIFICATIONS);
           if (noOfDeletedMessages == 1)
               mAnalytics.sendEventActionWithMap(NotificationConstants.TRACKING_PARAMS.NOTIFICATION_DELETE_SUCCESSFUL, cdata);
           else
               mAnalytics.sendEventActionWithMap(NotificationConstants.TRACKING_PARAMS.NOTIFICATION_DELETE_SELECTED_SUCCESSFUL, cdata);
       }
   }
}
