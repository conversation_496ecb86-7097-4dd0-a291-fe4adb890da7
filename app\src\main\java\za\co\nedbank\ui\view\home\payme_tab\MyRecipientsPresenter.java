

package za.co.nedbank.ui.view.home.payme_tab;

import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_SHOW_INTERNATIONAL_ACCOUNT_TILE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_SHOW_PAY_TO_BUSINESS;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_WHICH_IP_FLOW;
import static za.co.nedbank.payment.internationalpayment.Constants.CLIENT_NUMBER_TYPE;
import static za.co.nedbank.payment.pay.navigator.PayNavigatorTarget.EXTRAS.IS_QUICK_PAY_FLOW;
import static za.co.nedbank.ui.view.home.my_recipients.IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_LIMITS_API;

import androidx.annotation.NonNull;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.Observable;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.concierge.chat.GlobalEventBus;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.constants.IAccountOptions;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.ClientPreferenceDto;
import za.co.nedbank.core.domain.model.profile.UserProfile;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetAccountsUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetMdmProfileUseCase;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.errors.HttpStatus;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;

import za.co.nedbank.core.utils.CMAUtils;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.KidsProfileUtilsWrapper;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.payment.common.domain.usecases.GetCollatedUserBeneficiaryUseCase;
import za.co.nedbank.payment.common.domain.usecases.GetDefaultAccountUseCase;
import za.co.nedbank.payment.common.domain.usecases.LimitsDataUseCase;
import za.co.nedbank.payment.common.view.tracking.PaymentsTrackingEvent;
import za.co.nedbank.payment.internationalpayment.domain.model.request.RequestHeaderDataModel;
import za.co.nedbank.payment.internationalpayment.domain.usecase.GetForexClientDetailUseCase;
import za.co.nedbank.payment.internationalpayment.view.IPType;
import za.co.nedbank.payment.internationalpayment.view.mapper.AddressOTTDataToViewMapper;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.payment.pay.validation.AccountBalanceValidator;
import za.co.nedbank.payment.pay.validation.AmountInMultiplesValidator;
import za.co.nedbank.payment.pay.validation.AmountLimitValidator;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;
import za.co.nedbank.payment.transfer.navigator.TransferNavigationTarget;
import za.co.nedbank.payment.transfer.validation.MinimumTransferAmountValidator;
import za.co.nedbank.profile.domain.usecase.profile.GetProfileLimitsUseCase;
import za.co.nedbank.ui.view.home.my_recipients.IMyRecipientsView;
import za.co.nedbank.ui.view.home.my_recipients.IQuickPayView;
import za.co.nedbank.uisdk.validation.ValidatableInput;

/**
 * Created by charurani on 21-08-2017.
 */

public class MyRecipientsPresenter extends NBBasePresenter<IQuickPayView> {

    private final NavigationRouter mNavigationRouter;
    private final GetCollatedUserBeneficiaryUseCase mGetCollatedUserBeneficiaryUseCase;
    private final GetDefaultAccountUseCase mGetDefaultAccountUseCase;
    private final GetAccountsUseCase mGetAccountsUseCase;
    private final NonEmptyTextValidator mNonEmptyTextValidator;
    private final ErrorHandler mErrorHandler;
    private final MinimumTransferAmountValidator mMinimumTransferAmountValidator;
    private final AmountInMultiplesValidator mAmountInMultiplesValidator;
    private final AmountLimitValidator mAmountLimitValidator;
    private final AccountBalanceValidator mAccountBalanceValidator;
    private final FeatureSetController featureSetController;
    private final GetMdmProfileUseCase mGetMdmProfileUseCase;
    private Analytics mAnalytics;
    private ApplicationStorage mMemoryStorage;
    private boolean mIsRecipientSelectionTracked;
    private final KidsProfileUtilsWrapper kidsProfileUtilsWrapper;
    private final GetForexClientDetailUseCase mGetForexClientDetailUseCase;
    private final AddressOTTDataToViewMapper mAddressOTTDataToViewMapper;
    private final FeatureSetController mFeatureSetController;
    private final GetProfileLimitsUseCase mProfileLimitUseCase;

    @Inject
    MyRecipientsPresenter(@NonNull NavigationRouter navigationRouter,
                          @NonNull GetCollatedUserBeneficiaryUseCase getCollatedUserBeneficiaryUseCase,
                          @NonNull GetDefaultAccountUseCase getDefaultAccountUseCase,
                          @NonNull LimitsDataUseCase limitsDataUseCase,
                          @NonNull GetAccountsUseCase getAccountsUseCase,
                          @NonNull NonEmptyTextValidator nonEmptyTextValidator,
                          @NonNull MinimumTransferAmountValidator minimumTransferAmountValidator,
                          @NonNull AmountInMultiplesValidator amountInMultiplesValidator,
                          @NonNull AmountLimitValidator amountLimitValidator,
                          @NonNull AccountBalanceValidator accountBalanceValidator,
                          @NonNull ErrorHandler errorHandler,
                          @NonNull Analytics analytics,
                          @NonNull FeatureSetController featureSetController,
                          @NonNull GetMdmProfileUseCase mdmProfileUseCase,
                          @Named("memory") ApplicationStorage memoryStorage,
                          @NonNull GetForexClientDetailUseCase getForexClientDetailUseCase,
                          @NonNull KidsProfileUtilsWrapper kidsProfileUtilsWrapper,
                          @NonNull AddressOTTDataToViewMapper mAddressOTTDataToViewMapper,
                          FeatureSetController mFeatureSetController,
                          GetProfileLimitsUseCase mProfileLimitUseCase) {
        this.mNavigationRouter = navigationRouter;
        this.mGetCollatedUserBeneficiaryUseCase = getCollatedUserBeneficiaryUseCase;
        this.mGetDefaultAccountUseCase = getDefaultAccountUseCase;
        this.mGetAccountsUseCase = getAccountsUseCase;
        this.mNonEmptyTextValidator = nonEmptyTextValidator;
        this.mMinimumTransferAmountValidator = minimumTransferAmountValidator;
        this.mAmountInMultiplesValidator = amountInMultiplesValidator;
        this.mAmountLimitValidator = amountLimitValidator;
        this.mAccountBalanceValidator = accountBalanceValidator;
        this.mErrorHandler = errorHandler;
        this.mAnalytics = analytics;
        this.featureSetController = featureSetController;
        this.kidsProfileUtilsWrapper = kidsProfileUtilsWrapper;
        this.mGetMdmProfileUseCase=mdmProfileUseCase;
        this.mMemoryStorage=memoryStorage;
        this.mGetForexClientDetailUseCase=getForexClientDetailUseCase;
        this.mAddressOTTDataToViewMapper = mAddressOTTDataToViewMapper;
        this.mFeatureSetController = mFeatureSetController;
        this.mProfileLimitUseCase = mProfileLimitUseCase;
    }

    @Override
    protected void onBind() {
        super.onBind();
        GlobalEventBus.getBus().register(this);
    }

    @Override
    protected void onUnbind() {
        super.onUnbind();
        GlobalEventBus.getBus().unregister(this);
    }

    public void getLimits() {
        if (view == null)
            return;
        view.showProgress(true);
        mProfileLimitUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(
                        limits -> {
                            if (view != null) {
                                view.showProgress(false);
                                view.setLimits(limits);
                            }
                        },
                        error -> {
                            if (view != null) {
                                view.showProgress(false);
                            }
                        }
                );
    }

    void getUserBeneficiaryData(boolean isClearCacheData) {
        mGetCollatedUserBeneficiaryUseCase.execute(isClearCacheData)
                .compose(bindToLifecycle())
                .subscribe(
                        beneficiaryList -> {
                            if (view != null) {
                                view.receiveUserBeneficiaryBeans(beneficiaryList);
                            }
                        },
                        throwable -> {
                            if (view != null) {
                                handleError(throwable, true, IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API);
                            }
                        });
    }

    void handleRecipientsTypeClick() {
        if (view != null) {
            view.showChooseRecipientDialog();
        }
    }

    void handleFromAccountTypeClick() {
        if (view != null) {
            view.showFromAccountTypeDialog();
        }
    }

    void handleAmountTextEnter() {
        if (view != null) {
            view.handleEnterAccountNumber();
        }
    }

    void checkInputsOnScreen(@NonNull ValidatableInput<String> amountNumberInput, String fromAccount, boolean isMobileNoFlow) {
        if (view == null) {
            return;
        }
        if(isMobileNoFlow){
            mAmountInMultiplesValidator.setCellPhonePayment(Boolean.TRUE);
        }
        try {
            if (mNonEmptyTextValidator.validateInput(amountNumberInput.getValue()).isOk() &&
                    Float.parseFloat(FormattingUtil.convertCurrencyToAmount(amountNumberInput.getValue())) > 0
                    && !StringUtils.isNullOrEmpty(fromAccount)) {
                if (isMobileNoFlow && !mAmountInMultiplesValidator.validateInput(amountNumberInput.getValue()).isOk()) {
                    view.showAmountValidationError();
                    view.clearLimitError();
                } else if (!view.isDailyLimitAvailable()) {
                    view.showLimitError();
                } else {
                    view.setPayButtonEnabled(true);
                    amountNumberInput.clearErrors();
                    view.clearLimitError();
                }
            } else {
                view.setPayButtonEnabled(false);
            }
        } catch (NumberFormatException exception) {
            view.setPayButtonEnabled(false);
        }
    }

    void handlePayButtonClick(String beneficiaryType, Double imaliDailyLimit, Double paymentDailyLimit) {
        if (view == null) {
            return;
        }
            view.hideKeyBoard();
            view.setActivityTouchEnabled(false);
            view.showLoadingOnButton(true);

        if (beneficiaryType.equalsIgnoreCase(BeneficiaryConstants.BENEFICIARY_TYPE_PREPAID) && (imaliDailyLimit < paymentDailyLimit)) {
            view.setLimitToBeCompared(imaliDailyLimit);
        } else {
            view.setLimitToBeCompared(paymentDailyLimit);
        }
        navigateToPayReview();
    }

    void handleDismissSnackBarActionClick(@IMyRecipientsView.IMyRecipientsViewAPIErrorType int apiErrorType) {
        if (view != null && apiErrorType != IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API) {
            view.setPayButtonEnabled(true);
            view.dismissSnackBar();
        }
    }

    void handleFailedLoadRecipientLayoutClick() {
        if (view != null) {
            view.setVisibilityOnRecipientLoadLayout(false);
            view.updateUserBeneficiaryData();
        }
    }

    boolean isAmountValid(ValidatableInput<String> amountInput, boolean isMobileNumberFlow) {
        mMinimumTransferAmountValidator.setMinimumTransferAmount(TransferNavigationTarget.VALUES.MINIMUM_AMOUNT);
        if (isMobileNumberFlow) {
            return mAmountInMultiplesValidator.validateInput(amountInput.getValue()).isOk();
        } else {
            return mMinimumTransferAmountValidator.validateInput(amountInput.getValue()).isOk();
        }
    }

    boolean isAmountWithinLimits(ValidatableInput<String> amountInput, double availableLimit) {
        mAmountLimitValidator.setLimit(availableLimit);
        return mAmountLimitValidator.validateInput(amountInput.getValue()).isOk();
    }

    boolean isAmountLessThanAccountBalance(ValidatableInput<String> amountInput, double selectedAccountBalance, boolean isAvailViewBalance) {
        mAccountBalanceValidator.setAvailableBalance(selectedAccountBalance, isAvailViewBalance);
        return mAccountBalanceValidator.validateInput(amountInput.getValue()).isOk();
    }

    void handleRecipientSelected() {
        if (!mIsRecipientSelectionTracked) {
            mIsRecipientSelectionTracked = true;
        }
    }

    private void navigateToPayReview() {
        if (view != null) {
            AccountViewModel fromAccountViewModel = view.getSelectedFromAccountViewModel();
            if (fromAccountViewModel != null) {
               PaymentsViewModel paymentsViewModel= view.buildPaymentsViewModel(fromAccountViewModel);
               if(paymentsViewModel!=null && CollectionUtils.isNotEmpty(paymentsViewModel.getPaymentViewModelList()) && CMAUtils.isCMABranch(featureSetController,paymentsViewModel.getPaymentViewModelList().get(0).getSortCode())){
                 view.showPopForInterNationalPayment(paymentsViewModel.getPaymentViewModelList().get(0).getBankName());
               }else {
                   HashMap<String, Object> cdata = new HashMap<>();
                   AdobeContextData adobeContextData = new AdobeContextData(cdata);
                   adobeContextData.setStep2();
                   adobeContextData.setFromAccount(view.fetchFromAccountName());
                   adobeContextData.setToAccount(view.fetchToAccountName());
                   adobeContextData.setContext2(view.getBeneficiaryType());
                   adobeContextData.setStepName(PaymentsTrackingEvent.VAL_STEP_NAME_ACCOUNT_SELECTION);
                   mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.KEY_QUICK_PAY_NEXT, cdata);
                   NavigationTarget navigationTarget = NavigationTarget.to(PayNavigatorTarget.PAY_REVIEW);
                   navigationTarget.withParam(PayNavigatorTarget.EXTRAS.PAY_MODEL, paymentsViewModel);
                   navigationTarget.withParam(IS_QUICK_PAY_FLOW, true);
                   mNavigationRouter.navigateTo(navigationTarget);
               }
                view.setActivityTouchEnabled(true);
                view.showLoadingOnButton(false);
            }
        }
    }

    private void handleError(Throwable throwable, boolean shouldCheckNoContent, @IMyRecipientsView.IMyRecipientsViewAPIErrorType int apiErrorType) {
        Error error = mErrorHandler.getErrorMessage(throwable);
        if (shouldCheckNoContent && error.getCode() == HttpStatus.NO_CONTENT) {
            switch (apiErrorType) {
                case IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API:
                    view.setVisibilityOnEmptyStateView(true);
                    break;
                case ERROR_IN_LIMITS_API:
                    view.showError(mErrorHandler.getErrorMessage(throwable).getMessage(), apiErrorType);
                    break;
                case IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_ACCOUNTS_API:
                    view.showError(view.provideNoAccountString(), apiErrorType);
                    break;
            }
        } else {
            if (apiErrorType != IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API) {
                view.showError(mErrorHandler.getErrorMessage(throwable).getMessage(), apiErrorType);
            } else {
                view.setVisibilityOnRecipientLoadLayout(true);
            }
        }
        view.setActivityTouchEnabled(true);
        if (apiErrorType != IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API) {
            view.showLoadingOnButton(false);
        }
        view.setPayButtonEnabled(false);
    }

    void fetchFromAccountDetails() {
        Observable<ClientPreferenceDto> defaultAccountObservable = mGetDefaultAccountUseCase.execute();
        Observable<List<AccountViewModel>> accountsObservable = mGetAccountsUseCase.execute(IAccountOptions.PAYMENT_ACCOUNTS);

        if (defaultAccountObservable != null && accountsObservable != null) {
            Observable.zip(defaultAccountObservable.doOnError(throwable -> {
                if (view != null) {
                    handleError(throwable, false, IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_DEFAULT_ACCOUNTS_API);
                }
            }), accountsObservable.doOnError(throwable -> {
                if (view != null) {
                    handleError(throwable, true, IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_ACCOUNTS_API);
                }
            }), this::processResponse).compose(bindToLifecycle()).subscribe(isResponseReceivedFromApis -> {
                //response already sent to view above
            }, throwable -> {
                //need not do anything, respective error handled by observables
            });
        }
    }

    @NonNull
    private Boolean processResponse(ClientPreferenceDto clientPreferenceDto, List<AccountViewModel> accountViewModels) {
        if (clientPreferenceDto != null && accountViewModels != null) {
            if (view != null) {
                view.receiveAccounts(accountViewModels);
                view.receiveDefaultAccountIdentifierValue(clientPreferenceDto.value);
            }
            return true;
        }
        return false;
    }


    public void handleAddRecipient() {
        if(!featureSetController.isFeatureDisabled(FeatureConstants.FEATURE_RECIPIENT_WORKFLOW))
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ADD_RECIPIENT));
        else
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.COMING_SOON));
    }

//  Event will be fired from PayReviewActivity in order to update edited data
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUpdateReceived(PaymentsViewModel paymentsViewModel) {
        if (view != null)
            view.updateEditData(paymentsViewModel);
    }

    public void navigateToMinorRestrictionErrorScreen() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.LOC_TRANSACTION_ERROR_SCREEN));
    }
    // Checks if the user is a kid's profile
    public boolean isKidsProfile() {
        return kidsProfileUtilsWrapper.isUserUnderAged();
    }

    public void fetchProfileForInternationalPaymentQuickPayFlow() {
        fetchProfileForInternationalPaymentQuickPayFlow(null);
    }
    public void fetchProfileForInternationalPaymentQuickPayFlow(String entryPoint) {
        Object obj = mMemoryStorage.getObject(StorageKeys.MDM_USER_PROFILE);
        if (obj == null) {
            mGetMdmProfileUseCase.execute()
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> view.showLoadingOnButton(true))
                    .doOnTerminate(() -> view.showLoadingOnButton(false))
                    .subscribe(profile -> {
                        if (profile != null) {
                            mMemoryStorage.putObject(StorageKeys.MDM_USER_PROFILE, profile);
                            mMemoryStorage.putString(StorageKeys.MDM_CIS_NUMBER, profile.getCisNumber());
                            getForexDetailInfoForQuickFlow(profile.getCisNumber(),entryPoint);
                        }
                    }, e -> {
                        if (view != null) {
                            view.showError(StringUtils.EMPTY_STRING,IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_INTERNATIONAL_FLOW);
                        }
                    });
        } else {
            if (obj instanceof UserProfile) {
                getForexDetailInfoForQuickFlow(((UserProfile) obj).getCisNumber(),entryPoint);
            }
        }
    }

    private void getForexDetailInfoForQuickFlow(String cisNumber,String entryPoint) {
        Object obj = mMemoryStorage.getObject(StorageKeys.FOREX_CLIENT_DETAIL);
        if (obj == null) {
            RequestHeaderDataModel headerDataModel = new RequestHeaderDataModel();
            headerDataModel.setChannelCode(za.co.nedbank.payment.internationalpayment.Constants.CHANNEL_CODE);
            headerDataModel.setClientNumber(cisNumber);
            headerDataModel.setClientNumberType(CLIENT_NUMBER_TYPE);
            mGetForexClientDetailUseCase.execute(headerDataModel)
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> view.showLoadingOnButton(true))
                    .doOnTerminate(() -> view.showLoadingOnButton(false))
                    .subscribe(addressOTTModel -> {
                        if (addressOTTModel != null) {
                            mMemoryStorage.putObject(StorageKeys.FOREX_CLIENT_DETAIL, mAddressOTTDataToViewMapper.map(addressOTTModel));
                            navigateToInternationalPayment(entryPoint);
                        }
                    }, e -> {
                        if (view != null) {
                            view.showError(StringUtils.EMPTY_STRING,IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_INTERNATIONAL_FLOW);
                        }
                    });
        } else {
            navigateToInternationalPayment(entryPoint);
        }
    }

    private void navigateToInternationalPayment(String entryPoint) {
        mMemoryStorage.putBoolean(StorageKeys.FOREX_IS_PAY_FRM_RECIPIENT, true);
        mMemoryStorage.putBoolean(StorageKeys.FOREX_GO_TO_HOME, true);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.INTERNATIONAL_PAYMENT_ACCOUNT_SELECTION)
                .withParam(PARAM_EXTRA_WHICH_IP_FLOW, IPType.CMA)
                .withParam(PARAM_EXTRA_SHOW_PAY_TO_BUSINESS, true)
                .withParam(NavigationTarget.ENTRY_POINT_FOR_INTERNATION_PAYMENT, entryPoint)
                .withParam(PARAM_EXTRA_SHOW_INTERNATIONAL_ACCOUNT_TILE, false));
    }

    public void navigateToUpdateProfileLimits(boolean mobileNumberFlow) {
        HashMap<String, Object> cdata = new HashMap<>();
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.KEY_UPDATE_QUICK_PAY_DAILY_LIMIT, cdata);
        String navigateFor= Constants.PAYMENT_LIMIT;
        if(mobileNumberFlow){
            navigateFor=Constants.SEND_I_MALI;
        }
        mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.EDIT_PROFILE_LIMIT)
                        .withParam(NavigationTarget.PARAM_PROFILE_LIMIT_ID, navigateFor)
                        .withParam(NavigationTarget.EDIT_LIMIT_GP_FLOW, true))
                .subscribe(navigationResult -> {
                            if (navigationResult != null && navigationResult.isOk()) {
                                getLimits();
                            }
                        }
                );
    }

    public void trackCmaActionOnContinueClick() {
        HashMap<String, Object> cdata = new HashMap<>();
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.CMA_POPUP_CONTINUE, cdata);
    }
    public void trackCmaActionOnCancelClick() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setContext1Count();
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.CMA_POPUP_CANCEL, cdata);
    }
    public void trackCmaActionOnOpen(String bankName) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setContext1(bankName);
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.CMA_POPUP_OPEN, cdata);
    }
}
