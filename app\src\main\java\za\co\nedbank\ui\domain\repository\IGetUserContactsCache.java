/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository;

import java.util.List;

import io.reactivex.Observable;
import za.co.nedbank.payment.buy.domain.model.response.UserContactData;

public interface IGetUserContactsCache {
    Observable<List<UserContactData>> getContacts(String searchQuery);

    void setList(List<UserContactData> userContactDataList);

    boolean isDataCached();
}
