package za.co.nedbank.ui.view.notification.notification_preferences;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.Observable;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.accounts.model.AccountDto;
import za.co.nedbank.core.data.accounts.model.AccountsContainerDto;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.ClientPreferenceData;
import za.co.nedbank.core.domain.model.notifications.DeviceClientPreferenceData;
import za.co.nedbank.core.domain.model.notifications.FBPreferencesResponseData;
import za.co.nedbank.core.domain.model.notifications.FBResponseData;
import za.co.nedbank.core.domain.model.notifications.FBUpdatePreferenceRequestData;
import za.co.nedbank.core.domain.model.notifications.TiPreferenceData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.notifications.GetPreferencesUseCase;
import za.co.nedbank.core.domain.usecase.notifications.UpdatePreferenceUseCase;
import za.co.nedbank.core.enroll.domain.usecase.GetAccountsUseCase;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.mapper.FBPreferencesDataToViewModelMapper;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.AccountPreference;
import za.co.nedbank.core.view.fbnotifications.ClientPreferenceViewModel;
import za.co.nedbank.core.view.fbnotifications.FBPreferencesResponseViewModel;
import za.co.nedbank.loans.preapprovedaccountsoffers.Constants;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.PreApprovedOffersTrackingEvent;

public class NotificationPreferencePresenter extends NBBasePresenter<NotificationPreferenceView> {

    public static final String TAG = NotificationPreferencePresenter.class.getSimpleName();
    private NavigationRouter mNavigationRouter;
    private GetPreferencesUseCase mGetPreferencesUseCase;
    private GetAccountsUseCase mGetAccountsUseCase;
    private UpdatePreferenceUseCase mUpdatePreferenceUseCase;

    private final ApplicationStorage mApplicationStorage;
    private final ApplicationStorage mMemoryApplicationStorage;
    private FBPreferencesDataToViewModelMapper mFBPreferencesDataToViewModelMapper;
    private FeatureSetController mFeatureSetController;
    private Analytics mAnalytics;

    @Inject
    public NotificationPreferencePresenter(final NavigationRouter navigationRouter,
                                           final GetPreferencesUseCase getPreferencesUseCase,
                                           final GetAccountsUseCase mGetAccountsUseCase,
                                           final UpdatePreferenceUseCase updatePreferenceUseCase,
                                           final ApplicationStorage applicationStorage,
                                           @Named("memory") final ApplicationStorage memoryApplicationStorage,
                                           final FBPreferencesDataToViewModelMapper fbPreferencesDataToViewModelMapper,
                                           final FeatureSetController featureSetController,
                                           Analytics analytics) {
        this.mNavigationRouter = navigationRouter;
        this.mGetPreferencesUseCase = getPreferencesUseCase;
        this.mGetAccountsUseCase = mGetAccountsUseCase;
        this.mUpdatePreferenceUseCase = updatePreferenceUseCase;
        this.mApplicationStorage = applicationStorage;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.mFBPreferencesDataToViewModelMapper = fbPreferencesDataToViewModelMapper;
        this.mFeatureSetController = featureSetController;
        this.mAnalytics = analytics;
    }

    public void loadPreferences() {
        Observable.zip(mGetAccountsUseCase.execute(), mGetPreferencesUseCase.execute(getDeviceId()),
                        (accountsContainerDataModels, preferencesResponseData) -> {
                            if (accountsContainerDataModels != null && preferencesResponseData != null) {
                                return checkForAccountStatusCode(accountsContainerDataModels, preferencesResponseData);
                            }
                            return null;
                        }).compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgress(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgress(false);
                    }
                })
                .subscribe(preferencesResponseData -> {
                    if (preferencesResponseData != null && view != null) {
                        handlePreferenceData(preferencesResponseData);
                    } else {
                        showError();
                    }
                }, error -> {
                    if (view != null) {
                        showError();
                    }
                });
    }

    private FBPreferencesResponseData checkForAccountStatusCode(List<AccountsContainerDto> accountsContainerDataModels,
                                                                FBPreferencesResponseData preferencesResponseData) {
        if (preferencesResponseData.getData() != null && preferencesResponseData.getData().getTiPreferences() != null) {
            for (TiPreferenceData prefItem : preferencesResponseData.getData().getTiPreferences()) {
                for (AccountsContainerDto accountContainer : accountsContainerDataModels) {
                    putAccountStatusCodeIfMatch(accountContainer, prefItem);
                }
            }
        }
        return preferencesResponseData;
    }

    private void putAccountStatusCodeIfMatch(AccountsContainerDto accountContainer, TiPreferenceData prefItem) {
        if (accountContainer.getAccounts() != null) {
            for (AccountDto account : accountContainer.getAccounts()) {
                if (account.getNumber().equals(prefItem.getAccount())) {
                    prefItem.setAccountStatusCode(account.getAccountStatusCode());
                }
            }
        }
    }

    private void handlePreferenceData(FBPreferencesResponseData preferencesResponseData) {
        int ONLY_SUCCESS_ELEMENT = 1;
        if (preferencesResponseData != null && preferencesResponseData.getMetadata() != null) {
            MetaDataModel metaDataModel = preferencesResponseData.getMetadata();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && resultDetailList.size() > 0) {
                    if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        handleSuccess(preferencesResponseData);
                        break;
                    } else {
                        handleError(resultDetailList);
                    }
                }
            }
        }
    }

    private void handleSuccess(FBPreferencesResponseData preferencesResponseData) {
        if (view != null) {
            if (preferencesResponseData.getData() != null) {
                FBPreferencesResponseViewModel fbPreferencesResponseViewModel = mFBPreferencesDataToViewModelMapper.transform(preferencesResponseData);
                fbPreferencesResponseViewModel = getFilteredPreferences(fbPreferencesResponseViewModel);
                view.setData(fbPreferencesResponseViewModel);
            } else {
                showError();
            }
        }
    }

    private void handleError(ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                showError();
            }
        }
    }

    public FBPreferencesResponseViewModel getFilteredPreferences(FBPreferencesResponseViewModel preferencesResponseViewModel) {
        List<AccountPreference> apiAccountPreferenceList = preferencesResponseViewModel.getAllAccountPreferenceList();
        String jsonPreferences = mMemoryApplicationStorage.getString(NotificationConstants.STORAGE_KEYS.PREF_CASA_LIST, "");
        Gson gson = new Gson();
        Type type = new TypeToken<List<AccountPreference>>() {
        }.getType();
        List<AccountPreference> accountPreferences = gson.fromJson(jsonPreferences, type);
        List<AccountPreference> apiAccountPreferenceListTmp = new ArrayList<>(apiAccountPreferenceList);
        if (accountPreferences != null) {
            for (AccountPreference preference : apiAccountPreferenceList) {
                if (accountPreferences.contains(preference)) {
                    accountPreferences.set(accountPreferences.indexOf(preference), preference);
                } else {
                    apiAccountPreferenceListTmp.remove(preference);
                }
            }
        }
        FBPreferencesResponseViewModel fbPreferencesResponseViewModel = new FBPreferencesResponseViewModel();
        fbPreferencesResponseViewModel.setAllAccountPreferenceList(accountPreferences);
        fbPreferencesResponseViewModel.setSetPreferenceList(apiAccountPreferenceListTmp);
        fbPreferencesResponseViewModel.setClientPreferenceViewModel(preferencesResponseViewModel.getClientPreferenceViewModel());
        return fbPreferencesResponseViewModel;
    }

    private String getDeviceId() {
        return mApplicationStorage.getString(NotificationConstants.STORAGE_KEYS.UUID, StringUtils.EMPTY_STRING);
    }

    private void showError() {
        view.showFullScreenError();
    }

    public void navigateToAllAccountsPreferences(List<AccountPreference> mAllAccountsList) {
        mAnalytics.sendEvent(PreApprovedOffersTrackingEvent.TAG_SET_ACCOUNT_NOTIFICATION_ALL, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES_ALL_ACCOUNTS).withParam(NotificationConstants.EXTRA.ACCOUNT_PREFERENCE_LIST, mAllAccountsList));
    }


    public void navigateToAccountPreference(AccountPreference accountPreference) {
        mAnalytics.sendEvent(PreApprovedOffersTrackingEvent.TAG_SET_ACCOUNT_NOTIFICATION, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES_ACCOUNT).withParam(NotificationConstants.EXTRA.ACCOUNT_PREFERENCE, accountPreference));
    }

    public void updatePreferences(boolean allowForYou, boolean allowUnauthInbox) {
        mUpdatePreferenceUseCase.execute(getUpdatePrefReqData(allowForYou, allowUnauthInbox)).compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgress(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgress(false);
                    }
                })
                .subscribe(fbResponseData -> {
                    if (fbResponseData != null && view != null) {
                        handleUpdatePreferencesResponse(fbResponseData);
                    } else {
                        showPreferencesError();
                    }
                }, throwable -> {
                    showPreferencesError();
                });
    }

    private void handleUpdatePreferencesResponse(FBResponseData fbResponseData) {
        int ONLY_SUCCESS_ELEMENT = 1;
        if (fbResponseData != null && fbResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = fbResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && resultDetailList.size() > 0) {
                    if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        break;
                    } else {
                        handleErrorWhileUpdateFbResponse(resultDetailList);
                    }
                }
            }
        }
    }

    private void handleErrorWhileUpdateFbResponse(ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                showPreferencesError();
            }
        }
    }


    private FBUpdatePreferenceRequestData getUpdatePrefReqData(boolean allowForYou, boolean allowUnauthInbox) {
        FBUpdatePreferenceRequestData fbUpdatePreferenceRequestData = new FBUpdatePreferenceRequestData();
        FBUpdatePreferenceRequestData.Preference preference = fbUpdatePreferenceRequestData.new Preference();
        ClientPreferenceData clientPreferenceData = new ClientPreferenceData();
        clientPreferenceData.setAllowPushNotificationForOffers(allowForYou);
        clientPreferenceData.setLowPriorityDeliveryPeriod(view.getLowPriorityDeliveryPeriod());
        DeviceClientPreferenceData deviceClientPreferenceData = new DeviceClientPreferenceData();
        deviceClientPreferenceData.setAllowUnauthenticatedInbox(allowUnauthInbox);
        deviceClientPreferenceData.setDeviceId(getDeviceId());
        clientPreferenceData.setDeviceClientPreference(deviceClientPreferenceData);

        preference.setClientPreference(clientPreferenceData);
        preference.setTiPreferences(null);

        fbUpdatePreferenceRequestData.setPreference(preference);
        return fbUpdatePreferenceRequestData;
    }


    private void showPreferencesError() {
        if (view != null) {
            view.showErrorForUpdatePreferences();
        }
    }

    void onUndoClick() {
        view.updateUI();
    }

    public boolean isFeatureDisabled(String featureToggle) {
        return mFeatureSetController.isFeatureDisabled(featureToggle);
    }

    public void navigateToDeliveryPreferences(ClientPreferenceViewModel clientPreferenceViewModel) {
        mAnalytics.sendEvent(PreApprovedOffersTrackingEvent.TAG_SET_MARKETING_NOTIFICATION, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_DELIVERY_PREFRENCES).withParam(NotificationConstants.EXTRA.CLIENT_PREFERENCES_FOR_YOU, clientPreferenceViewModel.getAllowPushNotificationForOffers())
                .withParam(NotificationConstants.EXTRA.CLIENT_PREFERENCES_UNAUTH, clientPreferenceViewModel.getAllowUnauthentictedInbox())
                .withParam(NotificationConstants.EXTRA.CLIENT_PREFERENCES_DELIVERY, clientPreferenceViewModel.getLowPriorityDeliveryPeriod()));
    }

    public void navigateToTurnOffPersonalCreditOffers(int categoryId) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setScreenName(za.co.nedbank.core.Constants.NOTIFICATION_SETTINGS);
        if (categoryId == za.co.nedbank.core.Constants.PreApprovedOffersNotificationCategoryType.FOR_YOU_OFFERS.getCategoryId()) {
            adobeContextData.setFeature(TrackingParam.VAL_FEATURE_FOR_YOU_OFFER_NOTIFICATION_SETTINGS);
            mAnalytics.sendEventActionWithMap(PreApprovedOffersTrackingEvent.TAG_SETTINGS_PREFERENCES_FOR_YOU_OFFERS, cdata);
        } else {
            adobeContextData.setFeature(TrackingParam.VAL_FEATURE_VALUE_ADDS_NOTIFICATION_SETTINGS);
            mAnalytics.sendEventActionWithMap(PreApprovedOffersTrackingEvent.TAG_SETTINGS_PREFERENCES_VALUE_ADDS, cdata);
        }
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_OFFERS_PREFERENCE_LIST_SCREEN)
                .withParam(Constants.BundleKeys.CATEGORY_ID, categoryId));
    }

    public void trackActionNotificationPreferenceBack() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCount();
        adobeContextData.setFeatureCategoryCount();
        adobeContextData.setSubFeatureCount();
        mAnalytics.sendEventActionWithMap(PreApprovedOffersTrackingEvent.TAG_SETTINGS_NOTIFICATION_BACK, cdata);
    }
}