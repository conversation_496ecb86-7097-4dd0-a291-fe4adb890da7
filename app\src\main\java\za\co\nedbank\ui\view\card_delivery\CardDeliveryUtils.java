package za.co.nedbank.ui.view.card_delivery;

import android.content.Context;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.TypefaceSpan;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import java.util.Locale;

import za.co.nedbank.R;
import za.co.nedbank.core.data.networking.client.ResultErrorCodes;
import za.co.nedbank.core.domain.model.metadata_v2.MetaDataModel;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.StringUtils;

import static android.text.Spanned.SPAN_EXCLUSIVE_INCLUSIVE;
import static za.co.nedbank.core.Constants.ONE;
import static za.co.nedbank.core.Constants.TAG;
import static za.co.nedbank.core.Constants.THOUSAND_METERS;
import static za.co.nedbank.core.Constants.ZERO;

public class CardDeliveryUtils {

    private CardDeliveryUtils() {
        //No instance needed
    }

    public static boolean isSuccess(MetaDataModel metaDataModel) {
        return metaDataModel != null
                && null != metaDataModel.getResultData()
                && !metaDataModel.getResultData().isEmpty()
                && null != metaDataModel.getResultData().get(0)
                && metaDataModel.getResultData().get(0).getCode()
                .equalsIgnoreCase(ResultErrorCodes.VALID_R_RESULT);
    }


    public static String calculateDistance(double srcLat, double srcLong, double destLat, double destLong) {
        float[] distance = new float[ONE];
        android.location.Location.distanceBetween(srcLat, srcLong, destLat, destLong, distance);
        if (Double.compare(distance[ZERO], THOUSAND_METERS) == 1) {
            float distanceKm = distance[ZERO] / THOUSAND_METERS;
            return String.format(Locale.getDefault(), "%.2f", distanceKm)
                    .concat(StringUtils.SPACE)
                    .concat(StringUtils.KILO_METER_KMS)
                    .concat(StringUtils.SPACE);
        } else {
            String dist = StringUtils.EMPTY_STRING;

            // Getting IndexOutOfBoundsException once in a while.
            try {
                dist = String.format(Locale.getDefault(), "%f", distance[ONE])
                        .concat(StringUtils.SPACE)
                        .concat(StringUtils.METER_M)
                        .concat(StringUtils.SPACE);
            } catch (IndexOutOfBoundsException e) {
                NBLogger.d(TAG, e.getMessage());
            }

            return dist;
        }
    }

    public static SpannableString createPhNumberSpannableRrbTelNo(Context context, String message){
        return createSpannableUtil(context, message, context.getString(R.string.user_call_back_contact_number_rrb), context.getString(R.string.customer_care_number_part_one_rrb), context.getString(R.string.cutomer_care_number_part_two));
    }

    public static SpannableString createPhNumberSpannable(Context context, String message){
        return createSpannableUtil(context, message, context.getString(R.string.user_call_back_contact_number), context.getString(R.string.customer_care_number_part_one), context.getString(R.string.cutomer_care_number_part_two));
    }

    public static SpannableString createSpannableUtil(Context context, String message, String customerCarePhoneNumber, String noPartOne, String noPartTwo) {
        String placeHolder = context.getString(R.string.cutomer_care_number_place_holder);
        message = message.replace(placeHolder, customerCarePhoneNumber);
        SpannableString spannable = new SpannableString(message);
        int start = message.indexOf(customerCarePhoneNumber);
        int end = start + customerCarePhoneNumber.length();
        spannable.setSpan(new ForegroundColorSpan(ContextCompat.getColor(context, R.color.green_009639)),
                start, end, SPAN_EXCLUSIVE_INCLUSIVE);
        spannable.setSpan(new TypefaceSpan("sans-serif-medium"), start, end, SPAN_EXCLUSIVE_INCLUSIVE);

        int spaceOne = message.indexOf(noPartOne) + noPartOne.length();
        spannable.setSpan(new ForegroundColorSpan(ContextCompat.getColor(context, android.R.color.transparent)), spaceOne, spaceOne + 1, SPAN_EXCLUSIVE_INCLUSIVE);

        int spaceTwoAt = message.indexOf(noPartTwo) + noPartTwo.length();
        spannable.setSpan(new ForegroundColorSpan(ContextCompat.getColor(context, android.R.color.transparent)), spaceTwoAt, spaceTwoAt + 1, SPAN_EXCLUSIVE_INCLUSIVE);

        spannable.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                IntentUtils.startDialNumberIntent(context,
                        customerCarePhoneNumber);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
            }
        }, start, end, SPAN_EXCLUSIVE_INCLUSIVE);
        return spannable;
    }


    public static String extractResponseCode(MetaDataModel metaDataModel) {
        if (metaDataModel != null
                && null != metaDataModel.getResultData()
                && !metaDataModel.getResultData().isEmpty()
                && null != metaDataModel.getResultData().get(0)) {
            return metaDataModel.getResultData().get(0).getCode();
        }
        return null;
    }

    public static String extractMessage(MetaDataModel metaDataModel) {
        if (metaDataModel != null
                && null != metaDataModel.getResultData()
                && !metaDataModel.getResultData().isEmpty()
                && null != metaDataModel.getResultData().get(0)) {
            return metaDataModel.getResultData().get(0).getDescription();
        }
        return null;
    }
}
