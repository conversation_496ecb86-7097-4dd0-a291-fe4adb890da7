package za.co.nedbank.ui.view.mapper;

import javax.inject.Inject;

import za.co.nedbank.ui.domain.model.pop.ShareProofOfPaymentRequestData;
import za.co.nedbank.ui.view.model.ShareProofOfPaymentRequestViewModel;

public class ShareProofOfPaymentViewModelToDataMapper {

    @Inject
    public ShareProofOfPaymentViewModelToDataMapper() {
        // injected class constructor

    }

    public ShareProofOfPaymentRequestData mapData(ShareProofOfPaymentRequestViewModel shareProofOfPaymentRequestViewModel) {
        ShareProofOfPaymentRequestData shareProofOfPaymentRequestData = null;
        if (null != shareProofOfPaymentRequestViewModel) {
            shareProofOfPaymentRequestData = new ShareProofOfPaymentRequestData();
            shareProofOfPaymentRequestData.setSharePopNotificationTypeData(shareProofOfPaymentRequestViewModel.getSharePopNotificationTypeData());
            shareProofOfPaymentRequestData.setFromRecentPayments(shareProofOfPaymentRequestViewModel.isFromRecentPayments());
            shareProofOfPaymentRequestData.setSopFromPayDone(shareProofOfPaymentRequestViewModel.isSopFromPayDone());
            if (!shareProofOfPaymentRequestViewModel.isFromRecentPayments()) {
                shareProofOfPaymentRequestData.setTransactionDate(shareProofOfPaymentRequestViewModel.getTransactionDate());
                shareProofOfPaymentRequestData.setTransactionKind(shareProofOfPaymentRequestViewModel.getTransactionKind());
            }
        }

        return shareProofOfPaymentRequestData;
    }
}
