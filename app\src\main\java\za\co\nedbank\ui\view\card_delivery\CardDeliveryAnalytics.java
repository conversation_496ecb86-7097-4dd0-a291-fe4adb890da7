package za.co.nedbank.ui.view.card_delivery;

public class CardDeliveryAnalytics {


    public static final String VAL_DELIVERY_OPTIONS = "Delivery Options";
    public static final String VAL_CARD_REPLACEMENT = "Card Replacement";
    public static final String VAL_CARD_MAINTENANCE = "Card Maintenance";
    public static final String VAL_CARD_ORDERING_LEAD = "Card Ordering Lead";
    public static final String VAL_DELIVERY_OPTIONS_ERROR = "Delivery Options Error";
    public static final String VAL_ADDRESS_DELIVERY_ERROR = "Address Delivery Error";
    public static final String VAL_BRANCH_COLLECTION_ERROR = "Branch Collection Error";
    public static final String VAL_CARDS = "Cards";
    public static final String VAL_SALES_AND_ONBOARDING = "Sales and Onboarding";
    public static final String VAL_PRODUCT_ON_BOARDING = "Product Onboarding";
    public static final String VAL_ADDRESS_DETAILS_CONFIRMED = "Address Details Confirmed";
    public static final String VAL_DELIVERY_VIA_COURIER = "Delivery via Courier";
    public static final String VAL_LOCKER_MAP = "Locker Map";


    public static final String EVENT_CM_DELIVERY_METHOD = "cm_select_delivery_method";
    public static final String EVENT_CM_ORDER_CARD_FAILURE = "cm_order_card_failure";
    public static final String EVENT_CM_REPLACE_CARD_FAILURE = "cm_replace_card_failure";
    public static final String EVENT_CM_CLOSE_ARRANGE_DELIVERY = "cm_close_arrange_delivery";
    public static final String EVENT_CM_CLOSE = "cm_close";
    public static final String VAL_CARDS_CATEGORY = "Cards;%s;;";

    //mobile/clientonboarding/v1/users/cards/deliveryoptions
    public static final String API_CAR_DEL = "CAR_DEL";
    //mobile/clientonboarding/v1/users/cards
    public static final String API_USE_CAR = "USE_CAR";
    //referencedata/v1/retrievelockerlocations
    public static final String API_REF_RET = "REF_RET";
    //channeldistribution/v2/branches
    public static final String API_CHA_BRA = "CHA_BRA";


    public static final String EVENT_CM_ORDER_CARD_SUCCESSFUL = "cm_order_card_successful";
    public static final String VAL_DEFAULT_BRANCH = "Default Branch";
    public static final String VAL_DELIVERY_TO_A_NEDBANK_BRANCH = "Delivery to a Nedbank Branch";
    public static final String VAL_CONFIRM_BRANCH_DETAILS = "Confirm Branch Details";
    public static final String EVENT_CM_CHANGE_BRANCH = "cm_change_branch";
    public static final String EVENT_CM_CONFIRM_BRANCH = "cm_confirm_branch";
    public static final String EVENT_CM_CONFIRM_LOCKER = "cm_confirm_locker";
    public static final String EVENT_CM_REPLACE_CARD_FAILURE_RETRY = "cm_replace_card_failure_retry";
    public static final String DEFAULT_BRANCH_CONTEXT = "Order card | Default branch : %s";
    public static final String PICK_IT_UP_FROM_DSV = "Pick it from a DSV locker";

    public static final String VAL_YES = "Yes";
    public static final String VAL_NO = "No";
    public static final String VAL_BRANCH_DETAILS_CONFIRMED = "Branch Details Confirmed";
    public static final String VAL_LOCKER_DETAILS_CONFIRMED = "Locker Details Confirmed";
    public static final String VAL_LOCKER_RETRY = "Locker Retry";
    public static final String VAL_CONFIRM_LOCKER_DETAILS = "Confirm Locker Details";
    public static final String VAL_DELIVERY_TO_A_DSV_LOCKER = "Delivery to a DSV Locker";
    public static final String EVENT_CM_CHANGE_LOCKER = "cm_change_locker";
    public static final String EVENT_CM_SEARCH_NEAREST_LOCKER = "cm_search_nearest_locker";
}
