/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.data.mapper.RecipientResponseEntityToDomainDataMapper;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.model.response.RecipientResponseData;
import za.co.nedbank.core.domain.repository.IRecipientRepository;
import za.co.nedbank.core.view.mapper.RecipientViewModelToEntityMapper;
import za.co.nedbank.core.view.recipient.RecipientViewModel;

/**
 * Created by priyadhingra on 28-07-2017.
 */
public class AddRecipientUseCase extends UseCase<RecipientViewModel, RecipientResponseData> {

    private final String TAG = AddRecipientUseCase.class.getSimpleName();
    private final IRecipientRepository mIRecipientRepository;
    private final RecipientResponseEntityToDomainDataMapper mAddRecipientResponseEntityToDomainDataMapper;
    private final RecipientViewModelToEntityMapper mAddRecipientViewModelToEntityMapper;

    @Inject
    protected AddRecipientUseCase(final UseCaseComposer useCaseComposer, final IRecipientRepository iRecipientRepository, RecipientResponseEntityToDomainDataMapper addRecipientResponseEntityToDomainDataMapper, RecipientViewModelToEntityMapper addRecipientViewModelToEntityMapper) {
        super(useCaseComposer);
        this.mIRecipientRepository = iRecipientRepository;
        this.mAddRecipientResponseEntityToDomainDataMapper = addRecipientResponseEntityToDomainDataMapper;
        this.mAddRecipientViewModelToEntityMapper = addRecipientViewModelToEntityMapper;
        setCacheObservable(false);
    }

    @Override
    protected Observable<RecipientResponseData> createUseCaseObservable(RecipientViewModel addRecipientRequestViewModel) {
        return mIRecipientRepository.postAddRecipient(mAddRecipientViewModelToEntityMapper.mapEditRecipientViewModelToEntity(addRecipientRequestViewModel,false), addRecipientRequestViewModel.isValidate()).map(mAddRecipientResponseEntityToDomainDataMapper::mapRecipientResponseEntityToData);
    }
}
