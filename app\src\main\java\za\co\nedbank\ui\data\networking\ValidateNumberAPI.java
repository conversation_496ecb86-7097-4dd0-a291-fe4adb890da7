/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.networking;

import io.reactivex.Observable;
import retrofit2.http.GET;
import retrofit2.http.Query;
import za.co.nedbank.ui.data.entity.money_request.ValidateNumberEntity;


public interface ValidateNumberAPI {

    @GET("retail/paymentrequest/v1/validations")
    Observable<ValidateNumberEntity> validateNumberEntityObservable(@Query("contactNumber") String mobileNumber);
}
