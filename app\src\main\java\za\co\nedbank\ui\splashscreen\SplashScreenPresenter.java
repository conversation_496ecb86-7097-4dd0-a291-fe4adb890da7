/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.splashscreen;

import android.content.Context;
import android.util.Log;

import com.google.firebase.messaging.FirebaseMessaging;
import com.huawei.agconnect.config.AGConnectServicesConfig;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.disposables.CompositeDisposable;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.concierge.chat.GlobalEventBus;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.Permission;
import za.co.nedbank.core.domain.model.ita.ITAFlowEvent;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.CheckPermissionUseCase;
import za.co.nedbank.core.domain.usecase.DisconnectNidSDKUsecase;
import za.co.nedbank.core.domain.usecase.InitializeNidSDKUsecase;
import za.co.nedbank.core.domain.usecase.SetLoginUseCase;
import za.co.nedbank.core.domain.usecase.SetNBUserIdUseCase;
import za.co.nedbank.core.domain.usecase.notifications.GetHMSTokenUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationResult;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.INetworkFlow;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.core.notification.mapper.NotificationDataToViewModelMapper;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.tracking.appsflyer.AddContextData;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.nid_sdk.main.core.transakt.dto.OSUpgradeContent;
import za.co.nedbank.nid_sdk.main.core.transakt.dto.StartupStatusDto;
import za.co.nedbank.nid_sdk.main.core.transakt.dto.UpgradeContent;

import static za.co.nedbank.core.Constants.DEEP_LINK_FROM_SMS_RETENTION_COUNT;
import static za.co.nedbank.core.data.storage.StorageKeys.RPP_INTRODUCTION_SESSION_COUNTER;
import static za.co.nedbank.core.di.modules.ApplicationModule.NETWORK_FLOW;


public class SplashScreenPresenter extends NBBasePresenter<SplashScreenView> {

    private static final String TAG = SplashScreenPresenter.class.getCanonicalName();

    private final NavigationRouter navigationRouter;
    private final InitializeNidSDKUsecase initializeNidSDKUsecase;
    private final CheckPermissionUseCase checkPermissionUseCase;
    private final GetHMSTokenUseCase mGetHMSTokenUseCase;
    private final NotificationDataToViewModelMapper notificationDataToViewModelMapper;
    private final ApplicationStorage mApplicationStorage;
    private final SetLoginUseCase setLoginUseCase;
    private final SetNBUserIdUseCase setNBUserIdUseCase;
    private final CompositeDisposable disposables;
    private final DisconnectNidSDKUsecase disconnectNidSDKUsecase;

    private NavigationResult mNavigationResult;
    private FeatureSetController mFeatureSetController;
    private final ApplicationStorage mPersistenceApplicationStorage;
    private final Analytics mAnalytics;
    private final Context mContext;
    private String mHMSToken;
    private boolean isITAFlowStarted = false;
    public LinkedList<NavigationTarget> holdedNavigationTargets = new LinkedList<>();
    private final AFAnalyticsTracker mAfAnalyticsTracker;
    private String blacklistedAppPackageName;

    @Inject
    SplashScreenPresenter(final DisconnectNidSDKUsecase disconnectNidSDKUsecase,
                          final Analytics analytics,
                          final NavigationRouter navigationRouter,
                          final CheckPermissionUseCase checkPermissionUseCase,
                          final FeatureSetController featureSetController,
                          final @Named("memory") ApplicationStorage memoryApplicationStorage,
                          final ApplicationStorage persistenceApplicationStorage,
                          final NotificationDataToViewModelMapper notificationDataToViewModelMapper,
                          final SetLoginUseCase setLoginUseCase, final InitializeNidSDKUsecase initializeNidSDKUsecase,
                          final SetNBUserIdUseCase setNBUserIdUseCase, final Context context,
                          final CompositeDisposable compositeDisposable, final GetHMSTokenUseCase getHMSTokenUseCase,
                          final AFAnalyticsTracker afAnalyticsTracker) {
        this.navigationRouter = navigationRouter;
        this.mAnalytics = analytics;
        this.checkPermissionUseCase = checkPermissionUseCase;
        this.mFeatureSetController = featureSetController;
        this.mPersistenceApplicationStorage = persistenceApplicationStorage;
        this.mApplicationStorage = memoryApplicationStorage;
        this.notificationDataToViewModelMapper = notificationDataToViewModelMapper;
        this.setLoginUseCase = setLoginUseCase;
        this.initializeNidSDKUsecase = initializeNidSDKUsecase;
        this.setNBUserIdUseCase = setNBUserIdUseCase;
        this.mContext = context;
        this.disposables = compositeDisposable;
        this.mGetHMSTokenUseCase = getHMSTokenUseCase;
        this.disconnectNidSDKUsecase = disconnectNidSDKUsecase;
        this.mAfAnalyticsTracker = afAnalyticsTracker;
    }

    @Override
    protected void onBind() {
        super.onBind();
        GlobalEventBus.getBus().register(this);
    }

    @Override
    protected void onUnbind() {
        super.onUnbind();
        GlobalEventBus.getBus().unregister(this);
    }

    void checkPermissions() {
        checkPermissionUseCase.execute(Permission.READ_PHONE_STATE, Permission.FINE_LOCATION, Permission.COURSE_LOCATION, Permission.WRITE_EXTERNAL_STORAGE)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (disposables != null) {
                        disposables.add(disposable);
                    }
                })
                .subscribe(
                        permissionsGranted -> {
                            view.prepareAnimation();
                            view.checkAppsFlyerSMSDeeplink();
                        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage())
                );
    }

    void disconnectEnterSktSDK() {
        if (NETWORK_FLOW == INetworkFlow.HARD_CODED_TOKEN) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME));
        } else {
            disconnectNidSDKUsecase.execute().subscribe();
        }
    }

    void initializeEntersekt() {
        //noinspection ConstantConditions
        if (NETWORK_FLOW == INetworkFlow.HARD_CODED_TOKEN) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME));
        } else {
            if (view != null) {
                initializeNidSDKUsecase.execute(view.getTransaktConfig())
                        .compose(bindToLifecycle())
                        .doOnSubscribe(disposable -> {
                            if (disposables != null) {
                                disposables.add(disposable);
                            }
                        })
                        .subscribe(startupStatusDto -> handleStartupStatus(startupStatusDto), this::handleEntersektException);
            }
        }
    }

    private void handleStartupStatus(final StartupStatusDto status) {
        view.initializePinPointSDK();
        mApplicationStorage.clearValue(RPP_INTRODUCTION_SESSION_COUNTER);
        setUpAFWithEmcert();

        if (status != null) {
            if (status.getNedbankIdUsername() != null) {
                addLoginInStorage(status.getNedbankIdUsername());
            }
            if (!StringUtils.isNullOrEmpty(status.getNedbankID())) {
                addUUIDInStorage(status.getNedbankID());
            }
            if (!StringUtils.isNullOrEmpty(status.getSessionId())) {
                mApplicationStorage.putString(StorageKeys.SESSION_ID, status.getSessionId());
                mApplicationStorage.putString(StorageKeys.GCM_KEY, mFeatureSetController.getDynamicFeatureValue(FeatureConstants.SET_GCP_KEY));
                mApplicationStorage.putBoolean(FeatureConstants.DynamicToggle.FTR_ONBOARDINGTP2, !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ONBOARDINGTP2));
            }
            //save AJO adobe notification toggle
            mPersistenceApplicationStorage.putBoolean(StorageKeys.AJO_ADOBE_TOGGLE, !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ADOBE_AJO_NOTIFICATION));
            subscribeUnSubscribeForNotification(!status.getShowEnrolment());
            handleNavigation(status);
        }
    }

    private void handleNavigation(StartupStatusDto status) {
        OSUpgradeContent osUpgradeContent = status.getOsUpgradeScreen();
        if (osUpgradeContent != null) {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.OS_OUTDATED);
            navigationTarget.withParam(Constants.BUNDLE_KEYS.OS_UPGRADE_CONTENT, osUpgradeContent);
            navigationRouter.navigateWithResult(navigationTarget).subscribe(
                    navigationResult -> {
                        if (view != null) {
                            if (osUpgradeContent.isSoftOsUpdate()) {
                                checkIfBlackListedAppsInstalled(status);
                            } else {
                                view.close();
                            }
                        }
                    }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
            );
        } else {
            checkIfBlackListedAppsInstalled(status);
        }
    }

    private void checkIfBlackListedAppsInstalled(StartupStatusDto status) {
        if (isBlackListAppsInstalled()) {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.APP_BLACK_LIST).withParam(NavigationTarget.PARAM_BLACKLISTED_PACKAGE_NAME, blacklistedAppPackageName);
            navigationRouter.navigateTo(navigationTarget);
            if (view != null)
                view.close();
        } else if (!status.isMaintenanceMode() && !status.isAppBlocked() && status.isVersionSupported()) {
            updateAppIfAvailable(status);
        }
    }

    private boolean isBlackListAppsInstalled() {
        List<String> installedAppsPackageNameList = view.getInstalledAppsPackageName();
        List<String> blackListAppsPackageNameList = getBlackListAppsPackageName();
        if (installedAppsPackageNameList != null && !installedAppsPackageNameList.isEmpty() && blackListAppsPackageNameList != null && !blackListAppsPackageNameList.isEmpty()) {
            for (String blackListAppPackageName : blackListAppsPackageNameList) {
                for (String installedAppsPackageName : installedAppsPackageNameList) {
                    if (installedAppsPackageName.equalsIgnoreCase(blackListAppPackageName.trim())) {
                        blacklistedAppPackageName = installedAppsPackageName;
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private List<String> getBlackListAppsPackageName() {
        String appBlacklist = mFeatureSetController.getDynamicFeatureValue(FeatureConstants.APP_BLACK_LIST_KEY);
        List<String> blackListAppsPackageNameList = new ArrayList<>();
        if (StringUtils.isNotEmpty(appBlacklist)) {
            blackListAppsPackageNameList = Arrays.asList(appBlacklist.split(","));
        }

        return blackListAppsPackageNameList;
    }

    private void subscribeUnSubscribeForNotification(boolean isSecondLogin) {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS)) {
            try {
                if (!isSecondLogin) {
                    FirebaseMessaging.getInstance().subscribeToTopic(NotificationConstants.TOPICS.UNENROLLED);
                    NBLogger.e("firebase_topic", "subscribeUnenrolledNotification");
                } else {
                    FirebaseMessaging.getInstance().unsubscribeFromTopic(NotificationConstants.TOPICS.UNENROLLED);
                    NBLogger.e("firebase_topic", "unsubscribeUnenrolledNotification");
                }
            } catch (Exception e) {
                NBLogger.e(TAG, e.getMessage());
            }
        }
    }

    private void updateAppIfAvailable(StartupStatusDto status) {

        boolean isAppUpdateAvailable = status.isUpdateAvailable();
        boolean isRooted = status.isRooted();
        boolean isSecondLogin = !status.getShowEnrolment();
        List<UpgradeContent> upgradeContentList = status.getUpgradeContent();

        boolean isShortcutScanPay = false;
        if (view != null) {
            isShortcutScanPay = view.isShortcutScanPay();
        }

        if (isAppUpdateAvailable) {
            if (view != null && DeviceUtils.isDeveloperOptionsEnabled(view.getActivityContext())) {

                if (DeviceUtils.isAlwaysFinishActivitiesOptionEnabledInDeveloperOption(view.getActivityContext())
                        && DeviceUtils.develop_option_uncheck) {
                    NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.DEVELOPER_OPTION_ALERT);
                    navigationRouter.navigateWithResult(navigationTarget).subscribe(
                            navigationResult -> appUpdate(isRooted, view.isShortcutScanPay(), isSecondLogin, upgradeContentList), throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
                } else {
                    appUpdate(isRooted, isShortcutScanPay, isSecondLogin, upgradeContentList);
                }
            } else {
                appUpdate(isRooted, isShortcutScanPay, isSecondLogin, upgradeContentList);
            }
        } else {
            performDeveloperOptionCheck(isRooted, isShortcutScanPay, isSecondLogin);
        }
    }

    private void appUpdate(boolean isRooted, boolean isShortcutScanPay, boolean isSecondLogin, List<UpgradeContent> upgradeContentList) {
        if (DeviceUtils.isSkip_soft_update()) {
            DeviceUtils.setSkip_soft_update(false);
            performDeveloperOptionCheck(isRooted, isShortcutScanPay, isSecondLogin);
            return;
        }
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.APP_SOFT_UPDATE_AVAILABLE);
        navigationTarget
                .withParam(NavigationTarget.IS_DEVICE_ROOTED_PARAM, isRooted)
                .withParam(NavigationTarget.IS_FROM_APP_SHORTCUT, isShortcutScanPay)
                .withParam(NavigationTarget.IS_SECOND_LOGIN, isSecondLogin)
                .withParam(NavigationTarget.APP_UPDATE_TYPE, upgradeContentList);

        if (isITAFlowStarted) holdedNavigationTargets.add(navigationTarget);
        else navigateToAppUpdate(navigationTarget);
    }

    public void setUpAFWithEmcert() {
        HashMap<String, Object> customData = new HashMap<>();
        AddContextData addContextData = new AddContextData(customData);
        addContextData.setEmcert(mPersistenceApplicationStorage.getString(StorageKeys.EMCERT_ID, ""));
        mAfAnalyticsTracker.sendCustomData(customData);
    }

    public void saveECID(String ecid) {
        String cloudExperienceId = mPersistenceApplicationStorage.getString(StorageKeys.EXPERIENCE_CLOUD_ID, StringUtils.EMPTY_STRING);
        if (cloudExperienceId.isEmpty()) {
            mPersistenceApplicationStorage.putString(StorageKeys.EXPERIENCE_CLOUD_ID, ecid);
        }
    }

    private void navigateToAppUpdate(NavigationTarget navigationTarget) {
        navigationRouter.navigateWithResult(navigationTarget).subscribe(
                navigationResult -> {
                    mNavigationResult = navigationResult;
                    if (view != null) {
                        view.setResult(mNavigationResult.getParams());
                        mNavigationResult = null;
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage())
        );
    }

    protected void addLoginInStorage(final String username) {
        setLoginUseCase
                .execute(username)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (disposables != null) {
                        disposables.add(disposable);
                    }
                })
                .subscribe(result -> {
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    protected void addUUIDInStorage(final String uuid) {
        setNBUserIdUseCase
                .execute(uuid)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (disposables != null) {
                        disposables.add(disposable);
                    }
                })
                .subscribe(result -> {
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void performDeveloperOptionCheck(boolean isRooted, boolean isShortcutScanPay, boolean isSecondLogin) {
        if (view != null && DeviceUtils.isDeveloperOptionsEnabled(view.getActivityContext())) {
            if (DeviceUtils.isAlwaysFinishActivitiesOptionEnabledInDeveloperOption(view.getActivityContext()) && DeviceUtils.develop_option_uncheck) {
                NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.DEVELOPER_OPTION_ALERT);
                navigationRouter.navigateWithResult(navigationTarget).subscribe(
                        navigationResult -> performFurtherChecksForNavigation(isRooted, isShortcutScanPay, isSecondLogin), throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
            } else {
                performFurtherChecksForNavigation(isRooted, isShortcutScanPay, isSecondLogin);
            }
        } else {
            performFurtherChecksForNavigation(isRooted, isShortcutScanPay, isSecondLogin);
        }
    }

    public void performFurtherChecksForNavigation(boolean isRooted, boolean isShortcutScanPay, boolean isSecondLogin) {
        if (isRooted && view != null && !view.isComingFromRootedFlow()) {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.APP_ROOTED)
                    .withParam(NavigationTarget.IS_FROM_APP_SHORTCUT, isShortcutScanPay)
                    .withParam(EnrollV2NavigatorTarget.IS_SECOND_LOGIN, isSecondLogin);
            if (isITAFlowStarted) holdedNavigationTargets.add(navigationTarget);
            else {
                navigationRouter.navigateTo(navigationTarget);
                view.close();
            }
        } else {
            performNavigation(isSecondLogin, isRooted, view != null && view.isShortcutScanPay());
        }
    }

    private void performNavigation(final boolean isSecondLogin, boolean isRooted, boolean isScanPay) {
        NavigationTarget navigationTarget;
        if (DeviceUtils.isBuildType(DeviceUtils.BUILD_TYPE_MOCK))
            navigationTarget = NavigationTarget.to(NavigationTarget.HOME);
        else navigationTarget = AppUtility.getNavigationTarget(isSecondLogin, isRooted, isScanPay);

        boolean doCloseView = false;
        if (isITAFlowStarted) {
            holdedNavigationTargets.add(navigationTarget);
        } else {
            navigationRouter.navigateTo(navigationTarget);
            doCloseView = true;
        }

        if (view != null && doCloseView) {
            view.close();
        }
    }

    private void handleEntersektException(final Throwable throwable) {
        disposeAllObservers();
        NBLogger.e(TAG, "Error while initializing entersekt", throwable);
        if (view != null)
            view.showEntersektError();
    }

    void handleNotificationPayload(NotificationData notificationData) {

        if (notificationData != null
                && notificationData.getNotificationId() != null
                && notificationData.getNotificationId() != 0) {
            FBNotificationsViewModel notificationViewModel = notificationDataToViewModelMapper.transform(notificationData);
            handleNotificationPayload(notificationViewModel);
        }
    }

    void handleNotificationPayload(FBNotificationsViewModel notificationViewModel) {
        mApplicationStorage.putObject(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL, notificationViewModel);

    }

    void navigateToTransactionDetails() {
        FBNotificationsViewModel notificationViewModel = (FBNotificationsViewModel) mApplicationStorage.getObject(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_TRANSACTION_DETAILS)
                .withIntentFlagNewTask(true)
                .withClearStack(true)
                .withParam(NavigationTarget.PARAM_TRANS_PUSH_DATA, notificationViewModel);
        if (isITAFlowStarted) holdedNavigationTargets.add(navigationTarget);
        else navigationRouter.navigateTo(navigationTarget);
    }

    boolean getAllowAnonymous() {
        FBNotificationsViewModel fbNotificationsViewModel = (FBNotificationsViewModel) mApplicationStorage.getObject(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL);
        if (fbNotificationsViewModel != null) {
            return (fbNotificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.UBC) || fbNotificationsViewModel.getNotificationType().equalsIgnoreCase(NotificationConstants.NOTIFICATION_TYPES.TRANSACTION) || fbNotificationsViewModel.getNotificationType().equalsIgnoreCase(NotificationConstants.NOTIFICATION_TYPES.INFO) || fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.ECERT) || fbNotificationsViewModel.getAllowAnonymous());
        }
        return false;
    }

    public void getHuaweiConfig() {
        try {
            final String senderId = AGConnectServicesConfig.fromContext(mContext)
                    .getString("client/app_id");

            mGetHMSTokenUseCase.execute()
                    .compose(bindToLifecycle())
                    .subscribe(result -> {
                        mHMSToken = result;
                        mPersistenceApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.HMS_TOKEN, mHMSToken);
                        view.setHMSConfigValues(senderId, mHMSToken);
                    }, throwable -> mHMSToken = StringUtils.EMPTY_STRING);

        } catch (Exception e) {
            NBLogger.i(TAG, "getToken failed, " + e);

        }
    }

    void disposeAllObservers() {
        if (disposables != null) {
            disposables.clear();
        }
    }

    void updateSMSRetentionDeepLinkFlag(boolean status) {
        mPersistenceApplicationStorage.putBoolean(za.co.nedbank.core.Constants.DEEP_LINK_FROM_SMS_RETENTION, status);
    }

    public String getHMSToken() {
        return mPersistenceApplicationStorage.getString(NotificationConstants.STORAGE_KEYS.HMS_TOKEN, null);
    }

    public int getRetentionSMSDeepLinkCount() {
        return mPersistenceApplicationStorage.getInteger(DEEP_LINK_FROM_SMS_RETENTION_COUNT, 0);
    }

    void updateAdvertisingId(String advertisingId) {
        mApplicationStorage.putString(StorageKeys.ADVERTISING_ID, advertisingId);
    }

    private void continueHoldedNavigation() {
        boolean doCloseView = true;
        while (!holdedNavigationTargets.isEmpty()) {
            NavigationTarget navigationTarget = holdedNavigationTargets.getFirst();
            if (navigationTarget.getTarget().equals(NavigationTarget.APP_SOFT_UPDATE_AVAILABLE)) {
                navigateToAppUpdate(navigationTarget);
                doCloseView = false;
            } else {
                navigationRouter.navigateTo(navigationTarget);
                doCloseView = true;
            }
            holdedNavigationTargets.removeFirst();
        }
        if (doCloseView && view != null) view.close();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onITAFlowStatusChanged(ITAFlowEvent itaFlowEvent) {
        isITAFlowStarted = itaFlowEvent.isFlowStarted();
        if (!isITAFlowStarted) continueHoldedNavigation();
    }

    public void saveFBToken(String fbToken) {
        mPersistenceApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.FB_TOKEN, fbToken);
    }

    void updateDeeplinkFeature(String feature, boolean isDeeplinkFlow, String productId, String subProductId, String caseId) {
        mPersistenceApplicationStorage.putString(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE, feature);
        mApplicationStorage.putBoolean(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, isDeeplinkFlow);
        mApplicationStorage.putString(StorageKeys.DEEP_LINK_PRODUCT_ID, productId);
        mApplicationStorage.putString(StorageKeys.DEEP_LINK_SUB_PRODUCT_ID, subProductId);
        mApplicationStorage.putString(StorageKeys.DEEP_LINK_CASE_ID, caseId);
    }

    void trackAutoLogOut() {
        if (view == null)
            return;
        if (view.isAutoLogout()) {
            HashMap<String, Object> cdata = new HashMap<>();
            long loginSessionTime = mApplicationStorage.getLong(StorageKeys.START_SESSION_TIMESTAMP, 0L);
            Long sessionTimeStampInSecond = (Calendar.getInstance().getTimeInMillis() - loginSessionTime) / za.co.nedbank.core.Constants.ONE_SECOND;
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_TIMESPENTPERSESSION, sessionTimeStampInSecond + StringUtils.EMPTY_STRING);
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_LOGGED_IN_STATE, TrackingEvent.ANALYTICS.VAL_LOGGED_OUT);
            mAnalytics.sendEventActionWithMap(TrackingEvent.AUTO_LOG_OUT, cdata);
        }
    }
}
