package za.co.nedbank.ui.view.home.apply;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.investmentonline.GetFicaStatusUseCase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.utils.StringUtils;

/**
 * Created by sreedev.r on 05-Dec-18.
 */

public class ApplyPresenter extends NBBasePresenter<ApplyView> {

    private final GetUserDetailUseCase getUserDetailUseCase;
    private final GetFicaStatusUseCase getFicaStatusUseCase;
    private final NavigationRouter navigationRouter;
    private final FeatureSetController featureSetController;
    private final ErrorHandler errorHandler;
    private String clientType;
    private String secOfficerCd;
    private String birthDate;
    private String cisNumber;

    @Inject
    ApplyPresenter(final GetUserDetailUseCase getUserDetailUseCase,
                   final GetFicaStatusUseCase getFicaStatusUseCase,
                   final NavigationRouter navigationRouter,
                   final FeatureSetController featureSetController,
                   final ErrorHandler errorHandler) {
        this.getUserDetailUseCase = getUserDetailUseCase;
        this.getFicaStatusUseCase = getFicaStatusUseCase;
        this.navigationRouter = navigationRouter;
        this.featureSetController = featureSetController;
        this.errorHandler = errorHandler;
    }

    void getUserDetails() {
        getUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    if (userDetail != null && view != null) {
                        birthDate = userDetail.getBirthDate();
                        clientType = userDetail.getClientType();
                        secOfficerCd = userDetail.getSecOfficerCd();
                        cisNumber = userDetail.getCisNumber();
                        view.showProgressBar(false);
                    }
                }, error -> {
                    if (view != null) {
                        view.showProgressBar(false);
                        view.showGenericError();
                    }
                });
    }

    void getFicaStatus() {
        if (!featureSetController.isFeatureDisabled(FeatureConstants.INVESTMENT_ONLINE_OPEN_NEW_ACCOUNT)) {
            getFicaStatusUseCase
                    .execute()
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        if (view != null) {
                            view.showProgressBar(true);
                        }
                    })
                    .doOnTerminate(() -> {
                        if (view != null) {
                            view.showProgressBar(false);
                        }
                    })
                    .subscribe(ficaStatusDataModel -> {
                        if (ficaStatusDataModel.getIsFica()) {
                            view.showProgressBar(false);
                            navigateToFiasDisclaimer();
                        } else {
                            onInvalidClient(za.co.nedbank.core.Constants.INVONLINE_ONIA_ERROR_FICA_ERROR_TYPE);
                        }
                    }, throwable -> {
                        if (view != null) {
                            view.showErrorMessage(errorHandler.getErrorMessage(throwable).getMessage());
                        }
                    });
        }
    }

    void onInvalidClient(int invOnlineOniaErrorType) {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_ONIA_INVESTMENT_ERROR)
                .withParam(NavigationTarget.KEY_ONIA_ERROR_TYPE, invOnlineOniaErrorType));
    }

    void navigateToFiasDisclaimer() {
        if (view != null) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_RIGHT_OPTIONS)
                    .withParam(NavigationTarget.PARAM_ONIA_CLIENT_TYPE, clientType)
                    .withParam(NavigationTarget.PARAM_ONIA_BIRTH_DATE, birthDate)
                    .withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, cisNumber)
                    .withParam(NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD, secOfficerCd));
        }
    }

    boolean isClientTypeValid() {
        return view != null && !StringUtils.isNullOrEmpty(clientType) && (!clientType.equals(Constants.CLIENT_TYPE_51) || clientType.equals(Constants.CLIENT_TYPE_52));
    }
}
