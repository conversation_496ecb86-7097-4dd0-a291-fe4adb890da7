/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.networking;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.PUT;
import retrofit2.http.Query;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsActionResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsMainResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentActionRequestEntity;

/**
 * Created by swapnil.gawande on 2/23/2018.
 */

public interface MoneyRequestsApi {
    @GET("retail/paymentrequest/v1/paymentrequests")
    Observable<MoneyRequestsMainResponseEntity> getMoneyRequestList(@Query("requesterRole") String requesterRole);

    @PUT("retail/paymentrequest/v1/paymentrequests")
    Observable<MoneyRequestsActionResponseEntity> paymentRequestsAction(@Body PaymentActionRequestEntity paymentActionRequestEntity);
}
