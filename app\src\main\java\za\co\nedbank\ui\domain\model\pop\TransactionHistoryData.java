package za.co.nedbank.ui.domain.model.pop;

import java.util.List;

import za.co.nedbank.payment.common.domain.data.model.NotificationDetailsData;

public class TransactionHistoryData {
    private long batchID;
    private long transactionID;
    private String capturedDate;
    private String startDate;
    private String nextTransDate;
    private int beneficiaryID;
    private String destinationNumber;
    private String serviceProvider;
    private String productCode;
    private double amount;
    private String prepaidStatus;
    private String purchaseReferenceNumber;
    private String bfName;
    private String myDescription;
    private String myReference;
    private String beneficiaryAccount;
    private int contactCardID;
    private String transactionType;
    private boolean instantPayment;
    private String status;
    private List<NotificationDetailsData> notificationDetailsData;

    public String getBeneficiaryType() {
        return beneficiaryType;
    }

    public void setBeneficiaryType(String beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    private String beneficiaryType;

    private boolean rapidPayment;

    private String proxyName;
    private String proxyDomain;
    private String proxyType;

    public String getProxyName() {
        return proxyName;
    }

    public String getProxyDomain() {
        return proxyDomain;
    }

    public String getProxyType() {
        return proxyType;
    }

    public void setProxyName(String proxyName) {
        this.proxyName = proxyName;
    }

    public void setProxyDomain(String proxyDomain) {
        this.proxyDomain = proxyDomain;
    }

    public void setProxyType(String proxyType) {
        this.proxyType = proxyType;
    }

    public boolean isRapidPayment() {
        return rapidPayment;
    }

    public void setRapidPayment(boolean rapidPayment) {
        this.rapidPayment = rapidPayment;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public int getContactCardID() {
        return contactCardID;
    }

    public void setContactCardID(int contactCardID) {
        this.contactCardID = contactCardID;
    }

    public String getBeneficiaryAccount() {
        return beneficiaryAccount;
    }

    public void setBeneficiaryAccount(String beneficiaryAccount) {
        this.beneficiaryAccount = beneficiaryAccount;
    }

    public long getBatchID() {
        return batchID;
    }

    public void setBatchID(long batchID) {
        this.batchID = batchID;
    }

    public long getTransactionID() {
        return transactionID;
    }

    public void setTransactionID(long transactionID) {
        this.transactionID = transactionID;
    }

    public String getCapturedDate() {
        return capturedDate;
    }

    public void setCapturedDate(String capturedDate) {
        this.capturedDate = capturedDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getNextTransDate() {
        return nextTransDate;
    }

    public void setNextTransDate(String nextTransDate) {
        this.nextTransDate = nextTransDate;
    }

    public int getBeneficiaryID() {
        return beneficiaryID;
    }

    public void setBeneficiaryID(int beneficiaryID) {
        this.beneficiaryID = beneficiaryID;
    }


    public String getDestinationNumber() {
        return destinationNumber;
    }

    public void setDestinationNumber(String destinationNumber) {
        this.destinationNumber = destinationNumber;
    }

    public String getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(String serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getPrepaidStatus() {
        return prepaidStatus;
    }

    public void setPrepaidStatus(String prepaidStatus) {
        this.prepaidStatus = prepaidStatus;
    }

    public String getPurchaseReferenceNumber() {
        return purchaseReferenceNumber;
    }

    public void setPurchaseReferenceNumber(String purchaseReferenceNumber) {
        this.purchaseReferenceNumber = purchaseReferenceNumber;
    }

    public String getBfName() {
        return bfName;
    }

    public void setBfName(String bfName) {
        this.bfName = bfName;
    }

    public String getMyDescription() {
        return myDescription;
    }

    public void setMyDescription(String myDescription) {
        this.myDescription = myDescription;
    }

    public String getMyReference() {
        return myReference;
    }

    public void setMyReference(String myReference) {
        this.myReference = myReference;
    }

    public List<NotificationDetailsData> getNotificationDetailsData() {
        return notificationDetailsData;
    }

    public void setNotificationDetailsData(List<NotificationDetailsData> notificationDetailsData) {
        this.notificationDetailsData = notificationDetailsData;
    }

    public boolean isInstantPayment() {
        return instantPayment;
    }

    public void setInstantPayment(boolean instantPayment) {
        this.instantPayment = instantPayment;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
