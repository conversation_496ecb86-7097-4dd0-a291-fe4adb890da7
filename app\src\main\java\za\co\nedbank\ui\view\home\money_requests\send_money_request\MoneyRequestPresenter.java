package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import android.util.Log;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationResult;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.validation.RecipientContactValidator;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.ui.domain.usecase.money_request.ValidateMobileNumberUseCase;
import za.co.nedbank.uisdk.validation.ValidatableInput;


public class MoneyRequestPresenter extends NBBasePresenter<MoneyRequestView> {

    private final NavigationRouter navigationRouter;
    private NavigationResult mNavigationResult;
    private final RecipientContactValidator mMobileNumberValidator;
    private final NonEmptyTextValidator nonEmptyTextValidator;
    private final ValidateMobileNumberUseCase validateMobileNumberUseCase;
    private final ErrorHandler errorHandler;
    private final Analytics analytics;

    @Inject
    MoneyRequestPresenter(final NavigationRouter navigationRouter, final RecipientContactValidator mMobileNumberValidator,
                          final NonEmptyTextValidator nonEmptyTextValidator, ValidateMobileNumberUseCase validateMobileNumberUseCase,
                          ErrorHandler errorHandler, Analytics analytics) {
        this.navigationRouter = navigationRouter;
        this.mMobileNumberValidator = mMobileNumberValidator;
        this.nonEmptyTextValidator = nonEmptyTextValidator;
        this.validateMobileNumberUseCase = validateMobileNumberUseCase;
        this.errorHandler = errorHandler;
        this.analytics = analytics;
    }

    void handleRecipientIconClick() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.PHONE_CONTACT_BENEFICIARY_LIST);
        navigationTarget.withParam(Constants.BundleKeys.VIEW_PAGER_WTH_TAB_VIEW, Constants.ISearchListViewType.VIEW_TYPE_SEND_MONEY);
        navigationRouter.navigateWithResult(navigationTarget).subscribe(
                navigationResult -> mNavigationResult = navigationResult
                ,throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
        );
    }

    @Override
    protected void onBind() {
        if (null != mNavigationResult && mNavigationResult.isOk() && view != null) {
            view.setResult(mNavigationResult.getParams());
            mNavigationResult = null;
        }
    }

    void handleNextClick() {
        if (view != null) {
            view.handleNextClick();
        }
    }

    String getMobileNumberWithoutContryCode(String userMobileNumber) {
        String mobileNumber;
        int mobileNumberMaxLength = 9;
        if (userMobileNumber.length() > mobileNumberMaxLength && userMobileNumber.contains(StringUtils.PLUS + StringUtils.COUNTRY_CODE)) {
            int subStringStartIndex = 3;
            mobileNumber = userMobileNumber.substring(subStringStartIndex, userMobileNumber.length());
        } else {
            mobileNumber = userMobileNumber;
        }

        return StringUtils.ZERO + mobileNumber;
    }

    void navigateToRequestDetailScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.MONEY_REQUEST_DETAIL);
        navigationTarget.withParam(NavigationTarget.PARAM_RECIPIENT_NAME, view.getRecipientName());
        navigationTarget.withParam(NavigationTarget.RECIPIENT_PHONE_NUMBER, view.getRecipientMobileNumber());
        navigationRouter.navigateTo(navigationTarget);
    }

    void handleRecipientNameTextChanged() {
        if (null != view) {
            view.handleEnterRecipientName();
        }
    }

    void handleMobileNumberTextChanged() {
        if (null != view) {
            view.handleEnterMobileNumber();
        }
    }

    void validateMobileNumber(String mobileNumber) {
        if (view == null) {
            return;
        }
        if (mobileNumber == null) {
            view.showErrorMessage(errorHandler.getUnknownError());
            return;
        }
        validateMobileNumberUseCase.execute(mobileNumber)
                .compose(bindToLifecycle())
                .subscribe(this::validateMobileNumber, throwable -> {
                    Error error = errorHandler.getErrorMessage(throwable);
                    if (view != null) {
                        view.showLoadingOnButton(false);
                        view.showErrorMessage(error.getMessage());
                    }
                });
    }

    void validateMobileNumber(MetaDataModel metaDataModel) {
        if (metaDataModel == null && view == null)
            return;

        List<ResultDataModel> validateNumberDataModelList = metaDataModel != null ? metaDataModel.getResultData() : null;
            if (validateNumberDataModelList != null && !validateNumberDataModelList.isEmpty()) {
                for (ResultDataModel resultDataModel : validateNumberDataModelList) {
                    ArrayList<ResultDetailModel> validateNumberDetailDataModelList = resultDataModel.getResultDetail();
                    for (ResultDetailModel resultDetailModel : validateNumberDetailDataModelList) {
                        if (resultDetailModel.getResult().equalsIgnoreCase(za.co.nedbank.services.Constants.SUCCESS)) {
                            view.navigateToRequestDetailOnSuccess();
                            analytics.sendEvent(TrackingEvent.PAY_ME_SECTION, TrackingParam.PAY_ME_RETURN_STATUS, String.format("%s", true));
                        } else {
                            handleValidateNumberFailure();
                            analytics.sendEvent(TrackingEvent.PAY_ME_SECTION, TrackingParam.PAY_ME_RETURN_STATUS, String.format("%s", false));
                        }
                    }
                }
            }
            view.showLoadingOnButton(false);
    }


    void checkInputsOnScreen(
            final ValidatableInput<String> recipientNameInput,
            final ValidatableInput<String> mobileNumberInput) {
        if (null != view) {
            if (nonEmptyTextValidator.validateInput(recipientNameInput.getValue()).isOk() && nonEmptyTextValidator.validateInput(mobileNumberInput.getValue()).isOk() && validateInput(mobileNumberInput, Validator.ValidatorType.RECIPIENT_CONTACT_VALIDATOR)) {
                view.setNextButtonEnabled(true);
            } else {
                view.setNextButtonEnabled(false);
            }
        }
    }

    private void handleValidateNumberFailure() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.MONEY_REQUEST_SUCCESS);
        navigationRouter.navigateTo(navigationTarget);
    }

    boolean validateInput(final ValidatableInput<String> input,
                          final Validator.ValidatorType validatorType) {
        switch (validatorType) {
            case RECIPIENT_CONTACT_VALIDATOR:
                return validate(input, mMobileNumberValidator);
            default:
        }
        return false;
    }
}
