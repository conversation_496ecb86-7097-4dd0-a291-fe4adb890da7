/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.mapper.money_requests;

import javax.inject.Inject;

import za.co.nedbank.ui.data.entity.money_request.PaymentRequestEntity;
import za.co.nedbank.ui.domain.model.money_request.PaymentRequestDataModel;


public class MoneyRequestDataToEntityMapper {

    @Inject
    MoneyRequestDataToEntityMapper() {
    }

    public PaymentRequestEntity mapPaymentRequestModelToPaymentRequestEntity(PaymentRequestDataModel paymentRequestDataModel) {
        PaymentRequestEntity paymentRequestEntity = new PaymentRequestEntity();
        paymentRequestEntity.setPayerName(paymentRequestDataModel.getPayerName());
        paymentRequestEntity.setAmount(paymentRequestDataModel.getAmount());
        paymentRequestEntity.setDescription(paymentRequestDataModel.getDescription());
        paymentRequestEntity.setItemAccountId(paymentRequestDataModel.getItemAccountId());
        paymentRequestEntity.setPayerPhoneNumber(paymentRequestDataModel.getPayerPhoneNumber());
        return paymentRequestEntity;
    }
}
