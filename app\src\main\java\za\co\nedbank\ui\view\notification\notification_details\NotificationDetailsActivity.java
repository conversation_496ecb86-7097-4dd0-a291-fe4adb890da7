package za.co.nedbank.ui.view.notification.notification_details;

import android.app.NotificationManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.view.WindowInsetsController;
import android.view.WindowManager;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.core.text.HtmlCompat;

import com.facebook.shimmer.ShimmerFrameLayout;
import com.facetec.sdk.FaceTecSDK;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Transformation;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.investmentonline.model.NoticesDataViewModel;
import za.co.nedbank.core.investmentonline.model.NoticesResponseViewModel;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.databinding.ActivityNotificationDetailsBinding;
import za.co.nedbank.databinding.LayoutRichContentLandBinding;
import za.co.nedbank.enroll_v2.Constants;
import za.co.nedbank.enroll_v2.view.model.fica.ClientDeviceInfoRequestViewModel;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.component.CompatButton;

public class NotificationDetailsActivity extends NBBaseActivity implements NotificationDetailsView, View.OnClickListener {

    private ShimmerFrameLayout shimmerLayout;

    @Inject
    NotificationManager notificationManager;
    @Inject
    public NotificationDetailsPresenter mNotificationDetailsPresenter;

    private FBNotificationsViewModel mFBNotificationsViewModel;

    public static final int ASPECT_WIDTH = 730;
    public static final int ASPECT_HEIGHT = 340;
    private int mActualImageWidth;
    private String mEmbbededContentDetails;
    private String mDisplayType;
    private ActivityNotificationDetailsBinding activityNotificationDetailsBinding;
    private LayoutRichContentLandBinding layoutRichContentLandBinding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        receiveBundle();
        mNotificationDetailsPresenter.setNotificationData(mFBNotificationsViewModel);
        if (mFBNotificationsViewModel != null
                && NotificationConstants.NOTIFICATION_TYPES.CHAT.equalsIgnoreCase(mFBNotificationsViewModel.getNotificationType())) {
            mNotificationDetailsPresenter.handleChatNotification();
        } else {
            activityNotificationDetailsBinding = ActivityNotificationDetailsBinding.inflate(getLayoutInflater());
            layoutRichContentLandBinding = LayoutRichContentLandBinding.inflate(getLayoutInflater());
            int orientation = getResources().getConfiguration().orientation;
            if (orientation == Configuration.ORIENTATION_PORTRAIT) {
                setContentView(activityNotificationDetailsBinding.getRoot());
                shimmerLayout = findViewById(R.id.shimmer_layout);
            } else {
                setContentView(layoutRichContentLandBinding.getRoot());
                handleFullScreen();
            }
            initToolbar(activityNotificationDetailsBinding.toolbar, true, false);
            mNotificationDetailsPresenter.bind(this);
            setUpView();
            mNotificationDetailsPresenter.sendAnalyticsToServer();
            mNotificationDetailsPresenter.clearNotificationFromTray();
            mNotificationDetailsPresenter.trackPageLoadOnAdobe();
            if (mNotificationDetailsPresenter.isNotifDetailsShowed()) {
                mNotificationDetailsPresenter.handleOnClick(mNotificationDetailsPresenter.getSelectedResponse(),
                        getString(R.string.app_update_available_message_chatbot),
                        getString(R.string.app_update_available_title_chatbot));
            }
        }
    }

    @SuppressWarnings("deprecation")
    private void handleFullScreen() {
        final WindowInsetsController insetsController;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            insetsController = getWindow().getInsetsController();
            if (insetsController != null) {
                insetsController.hide(WindowInsets.Type.statusBars());
                insetsController.hide(WindowInsets.Type.navigationBars());
            }
        } else {
            //noinspection deprecation
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }

    private void receiveBundle() {
        if (getIntent() != null && getIntent().getExtras() != null) {
            mFBNotificationsViewModel = (FBNotificationsViewModel) getIntent().getExtras().get(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL);
        }
    }

    private void setUpView() {
        if (mFBNotificationsViewModel != null) {
            setHeadingAndSubHeading();
            if (mFBNotificationsViewModel.getRichContent() != null && !mFBNotificationsViewModel.getRichContent().isEmpty()) {
                loadRichContentUi(filterRichContents(mFBNotificationsViewModel.getRichContent()));
            } else {
                if (mFBNotificationsViewModel.getBody() != null && activityNotificationDetailsBinding.notificationDetails != null) {
                    activityNotificationDetailsBinding.notificationDetails.loadDataWithBaseURL(null, mFBNotificationsViewModel.getBody() , Constants.MIME_TYPE, Constants.UTF_8, null);
                    activityNotificationDetailsBinding.notificationDetails.setVisibility(View.VISIBLE);
                }
            }
            List<CompatButton> buttonList = createButtons(mFBNotificationsViewModel.getResponseOptions());
            for (CompatButton button : buttonList) {
                if (activityNotificationDetailsBinding.notificationLayout != null) {
                    activityNotificationDetailsBinding.notificationLayout.addView(button);
                }
            }
        }
    }

    private void setHeadingAndSubHeading() {
        if (mFBNotificationsViewModel.getHeading() != null && activityNotificationDetailsBinding.notificationHeading != null) {
            activityNotificationDetailsBinding.notificationHeading.setText(getFormattedText(mFBNotificationsViewModel.getHeading()));
        }
        if (mFBNotificationsViewModel.getSubHeading() != null && activityNotificationDetailsBinding.notificationSubheading != null) {
            activityNotificationDetailsBinding.notificationSubheading.setText(getFormattedText(mFBNotificationsViewModel.getSubHeading()));
        }
    }

    private CharSequence getFormattedText(String text) {
        return HtmlCompat.fromHtml(text, HtmlCompat.FROM_HTML_MODE_LEGACY);
    }

    private List<FBNotificationsViewModel.RichContent> filterRichContents(List<FBNotificationsViewModel.RichContent> richContentList) {

        List<FBNotificationsViewModel.RichContent> richContents = new ArrayList<>();

        if (richContentList != null) {
            for (FBNotificationsViewModel.RichContent richContent :
                    richContentList) {
                if (!NotificationConstants.CONTENT_TYPE.UNKNOWN.equals(richContent.getContentType())) {
                    richContents.add(richContent);
                }
            }
        }

        return richContents;
    }

    void loadRichContentUi(List<FBNotificationsViewModel.RichContent> richContents) {

        if (richContents != null && !richContents.isEmpty()) {

            switch (richContents.get(0).getDisplayType()) {
                case NotificationConstants.DISPLAY_TYPE_BANNER:
                case NotificationConstants.DISPLAY_TYPE_EMBEDDED:
                case NotificationConstants.DISPLAY_TYPE_FOOTER:
                    mNotificationDetailsPresenter.populateRichContentUrls(richContents);
                    break;
                default:

            }

        }
    }


    public void setCorousalData(List<FBNotificationsViewModel.RichContent> urlList, String displayType) {
        RichNotificationAdapter richNotificationAdapter = new RichNotificationAdapter();
        richNotificationAdapter.seRichContent(urlList);
        if (displayType.equals(NotificationConstants.DISPLAY_TYPE_BANNER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayout.richContentPager.setAdapter(richNotificationAdapter);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.richContentPagerDots.setupWithViewPager(activityNotificationDetailsBinding.layoutRichNotificationLayout.richContentPager);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.richNotificationContainer.setVisibility(View.VISIBLE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.richContentPager.setVisibility(View.VISIBLE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.richContentPagerContainer.setVisibility(View.VISIBLE);
        } else if (displayType.equals(NotificationConstants.DISPLAY_TYPE_FOOTER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.richContentPagerFooter.setAdapter(richNotificationAdapter);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.richContentPagerDotsFooter.setupWithViewPager(activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.richContentPagerFooter);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.richNotificationContainerFooter.setVisibility(View.VISIBLE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.richContentPagerFooter.setVisibility(View.VISIBLE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.richContentPagerContainerFooter.setVisibility(View.VISIBLE);
        }

        ViewUtils.hideViews(shimmerLayout);
    }

    @Override
    public void showEmbeddedContent(List<FBNotificationsViewModel.RichContent> richContentList) {
        for (FBNotificationsViewModel.RichContent richContent : richContentList) {
            if (richContent != null) {
                setEmbeddedContent(richContent.getUrl(), richContent.getToken());
            }
        }
        activityNotificationDetailsBinding.notificationDetails.loadData(mEmbbededContentDetails, Constants.MIME_TYPE, Constants.UTF_8);
        activityNotificationDetailsBinding.notificationDetails.setVisibility(View.VISIBLE);
        shimmerLayout.stopShimmer();
        ViewUtils.hideViews(shimmerLayout);
    }

    @Override
    public NoticesDataViewModel filterNoticesDataViewModel(NoticesResponseViewModel mapDataModelToViewModel, String noticeId) {
        for (NoticesDataViewModel noticesDataViewModel : mapDataModelToViewModel.getNoticesDataViewModels()) {
            if (noticesDataViewModel.getNoticeID().equalsIgnoreCase(noticeId)) {
                return noticesDataViewModel;
            }

        }
        return null;
    }

    private void setEmbeddedContent(String url, String token) {
        if (StringUtils.isNullOrEmpty(mEmbbededContentDetails)) {
            mEmbbededContentDetails = mFBNotificationsViewModel.getBody();
        }
        if (!StringUtils.isNullOrEmpty(url)) {
            mEmbbededContentDetails = mEmbbededContentDetails.replace(token, String.format("%s%s%s%s%s", getString(R.string.line_break), getString(R.string.img_start_tag), url, getString(R.string.img_end_tag), getString(R.string.line_break)));
        }
    }


    public void setIvRichContentImage(final String imageUrl, String displayType) {
        ImageView img = activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentImage;
        if (displayType.equals(NotificationConstants.DISPLAY_TYPE_BANNER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGif.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.gifContainer.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentIcon.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentImage.setVisibility(View.VISIBLE);
        } else if (displayType.equals(NotificationConstants.DISPLAY_TYPE_FOOTER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.webViewGifFooter.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.gifContainerFooter.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentIconFooter.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentImageFooter.setVisibility(View.VISIBLE);
            img = activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentImageFooter;
        }
        Picasso.get()
                .load(imageUrl)
                .noPlaceholder()
                .priority(Picasso.Priority.HIGH)
                .into(img, new Callback() {
                    @Override
                    public void onSuccess() {
                        ViewUtils.hideViews(shimmerLayout);
                        showRichMediaContainer(displayType);
                    }

                    @Override
                    public void onError(Exception e) {
                        shimmerLayout.stopShimmer();
                    }
                });
    }

    public void setRichImageGif(String imageGifUrl, String displayType) {
        WebView webView = activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGif;
        if (displayType.equals(NotificationConstants.DISPLAY_TYPE_BANNER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGifIcon.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.gifContainer.setVisibility(View.VISIBLE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentImage.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentIcon.setVisibility(View.GONE);
        } else if (displayType.equals(NotificationConstants.DISPLAY_TYPE_FOOTER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.webViewGifIconFooter.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.gifContainerFooter.setVisibility(View.VISIBLE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentImageFooter.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentIconFooter.setVisibility(View.GONE);
        }

        webView.setVisibility(View.VISIBLE);
        webView.setBackgroundColor(Color.TRANSPARENT);
        webView.getSettings().setLoadWithOverviewMode(true);
        webView.getSettings().setUseWideViewPort(true);
        webView.setClickable(false);
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                //not required implementation
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                ViewUtils.hideViews(shimmerLayout);
                showRichMediaContainer(displayType);
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                shimmerLayout.stopShimmer();
            }

        });
        activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGif.loadUrl(imageGifUrl);

    }

    private void showRichMediaContainer(String displayType) {
        if (displayType.equals(NotificationConstants.DISPLAY_TYPE_BANNER)) {
            ViewUtils.showViews(activityNotificationDetailsBinding.layoutRichNotificationLayout.richNotificationContainer);
        } else if (displayType.equals(NotificationConstants.DISPLAY_TYPE_FOOTER)) {
            ViewUtils.showViews(activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.richNotificationContainerFooter);
        }
    }

    private List<CompatButton> createButtons(List<FBNotificationsViewModel.ResponseOption> responseOptions) {
        List<CompatButton> buttonList = new ArrayList<>();
        if (responseOptions != null) {
            for (int i = 0; i < responseOptions.size(); i++) {

                FBNotificationsViewModel.ResponseOption responseOption = responseOptions.get(i);
                CompatButton button = new CompatButton(this);
                button.setText(responseOption.getLabel());


                button.setTag(responseOption);
                button.setOnClickListener(this);
                LinearLayout.LayoutParams param = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                int leftRight = getResources().getDimensionPixelSize(R.dimen.dimen_20dp);
                int top = getResources().getDimensionPixelSize(R.dimen.dimen_16dp);
                param.setMargins(leftRight, top, leftRight, 0);
                button.setLayoutParams(param);

                CompatButton.ButtonType buttonType;
                String responseType = responseOption.getType();
                if ((responseType.equalsIgnoreCase(NotificationConstants.RESPONSE_TYPES.RESPONSE_PRIMARY_BUTTON)) || (responseType.equalsIgnoreCase(NotificationConstants.RESPONSE_TYPES.RESPONSE_PRIMARY_URL)) ||
                        (responseType.equalsIgnoreCase(NotificationConstants.RESPONSE_TYPES.RESPONSE_PRIMARY_APP_LINK))) {
                    buttonType = CompatButton.ButtonType.PRIMARY;
                    button.setButtonType(buttonType);
                    buttonList.add(0, button);

                } else {
                    buttonType = CompatButton.ButtonType.SECONDARY;
                    button.setButtonType(buttonType);
                    buttonList.add(button);
                }

            }

        }
        if (!buttonList.isEmpty()) {
            CompatButton button = buttonList.get(buttonList.size() - 1);
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) button.getLayoutParams();
            params.bottomMargin = getResources().getDimensionPixelSize(R.dimen.dimen_20dp);
            button.setLayoutParams(params);
        }
        return buttonList;
    }

    @Override
    public void onClick(View v) {
        mNotificationDetailsPresenter.handleOnClick((FBNotificationsViewModel.ResponseOption) v.getTag(),
                getString(R.string.app_update_available_message_chatbot),
                getString(R.string.app_update_available_title_chatbot));
    }

    @Override
    protected void onDestroy() {
        mNotificationDetailsPresenter.unbind();
        super.onDestroy();
    }

    @Override
    protected void onResume() {
        super.onResume();
        mNotificationDetailsPresenter.clearNotificationData();
    }

    @Override
    public void showProgressVisible(boolean visibility) {
        activityNotificationDetailsBinding.progressBar.setVisibility(visibility ? View.VISIBLE : View.GONE);
    }

    @Override
    public void clearNotificationFromTray(FBNotificationsViewModel fbNotificationsViewModel) {
        if (fbNotificationsViewModel != null) {
            notificationManager.cancel(fbNotificationsViewModel.getNotificationId());
        }
    }

    @Override
    public ClientDeviceInfoRequestViewModel getClientDeviceInfoRequestViewModel() {
        ClientDeviceInfoRequestViewModel clientDeviceInfoRequestViewModel = new ClientDeviceInfoRequestViewModel();
        clientDeviceInfoRequestViewModel.setOperatingSystem(za.co.nedbank.enroll_v2.Constants.OS_NAME);
        clientDeviceInfoRequestViewModel.setOperatingSystemVersion(Build.VERSION.RELEASE);
        clientDeviceInfoRequestViewModel.setSerialNumber(DeviceUtils.getSerialNumber(this));
        clientDeviceInfoRequestViewModel.setZoomVersion(FaceTecSDK.version());
        clientDeviceInfoRequestViewModel.setModelNumber(Build.MODEL);
        return clientDeviceInfoRequestViewModel;
    }


    @Override
    public void showRichContent(FBNotificationsViewModel.RichContent appLayoutViewModel, String displayType) {
        if (appLayoutViewModel != null && !StringUtils.isNullOrEmpty(appLayoutViewModel.getUrl())) {
            setRichContent(appLayoutViewModel.getUrl(), displayType, appLayoutViewModel.getContentType());
        } else {
            shimmerLayout.stopShimmer();
            if (displayType.equals(NotificationConstants.DISPLAY_TYPE_EMBEDDED)) {
                activityNotificationDetailsBinding.layoutRichNotificationLayout.richNotificationContainer.setVisibility(View.GONE);
            }
        }
    }

    public void displayNotificationDetails() {
        if (activityNotificationDetailsBinding.notificationDetails != null) {
            activityNotificationDetailsBinding.notificationDetails.loadData(mFBNotificationsViewModel.getBody(), Constants.MIME_TYPE, Constants.UTF_8);
            activityNotificationDetailsBinding.notificationDetails.setVisibility(View.VISIBLE);
        }
    }

    private void setRichContent(String mediaUrl, String displayType, String contentType) {
        switch (contentType) {
            case NotificationConstants.CONTENT_TYPE.IMAGE:
                setIvRichContentImage(mediaUrl, displayType);
                break;
            case NotificationConstants.CONTENT_TYPE.IMAGE_GIF:
                setRichImageGif(mediaUrl, displayType);
                break;
            case NotificationConstants.CONTENT_TYPE.ICON:
                setIvRichContentIcon(mediaUrl, displayType);
                break;
            case NotificationConstants.CONTENT_TYPE.ICON_GIF:
                setRichContentIconGif(mediaUrl, displayType);
                break;
            case NotificationConstants.CONTENT_TYPE.VIDEO:
                setRichContentVideo(mediaUrl, displayType);
                break;
            default:
                break;
        }
    }

    public void setRichContentVideo(String mediaUrl, String displayType) {
        mDisplayType = displayType;
        WebView webView = layoutRichContentLandBinding.webViewVideo;
        if (activityNotificationDetailsBinding.dataUsageAlertContainer != null) {
            activityNotificationDetailsBinding.dataUsageAlertContainer.setVisibility(View.VISIBLE);
        }
        if (!isLandscape()) {
            if (displayType.equals(NotificationConstants.DISPLAY_TYPE_BANNER)) {
                activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGifIcon.setVisibility(View.GONE);
                activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGif.setVisibility(View.GONE);
                activityNotificationDetailsBinding.layoutRichNotificationLayout.gifContainer.setVisibility(View.VISIBLE);
                activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentImage.setVisibility(View.GONE);
                activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentIcon.setVisibility(View.GONE);

            } else if (displayType.equals(NotificationConstants.DISPLAY_TYPE_FOOTER)) {
                activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.webViewGifIconFooter.setVisibility(View.GONE);
                activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.webViewGifFooter.setVisibility(View.GONE);
                activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.gifContainerFooter.setVisibility(View.VISIBLE);
                activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentImageFooter.setVisibility(View.GONE);
                activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentIconFooter.setVisibility(View.GONE);
                webView = activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.webViewVideoFooter;
            }
            showRichMediaContainer(displayType);
        }

        webView.setWebViewClient(new BrowserHome());
        webView.setWebChromeClient(new CustomChromeClient(this));
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setAllowFileAccess(true);
        webView.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);
        webView.getSettings().setJavaScriptCanOpenWindowsAutomatically(true);
        webView.getSettings().setMediaPlaybackRequiresUserGesture(false);
        String path = "<iframe src='" + mediaUrl + "' width='100%' height='100%' style='border: none;'allowfullscreen></iframe>";
        webView.loadData(path, "text/html", "utf-8");
        webView.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean isLandscape() {
        return getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;
    }

    private class BrowserHome extends WebViewClient {
        BrowserHome() {
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            if (!isLandscape()) {
                ViewUtils.hideViews(shimmerLayout);
                showRichMediaContainer(mDisplayType);
            }
        }
    }


    private void setRichContentIconGif(String mediaUrl, String displayType) {
        WebView webView = activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGifIcon;
        if (displayType.equals(NotificationConstants.DISPLAY_TYPE_BANNER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGif.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.gifContainer.setVisibility(View.VISIBLE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentImage.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentIcon.setVisibility(View.GONE);
        } else if (displayType.equals(NotificationConstants.DISPLAY_TYPE_FOOTER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.webViewGifFooter.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.gifContainerFooter.setVisibility(View.VISIBLE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentImageFooter.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentIconFooter.setVisibility(View.GONE);

        }
        webView.setVisibility(View.VISIBLE);
        webView.setBackgroundColor(Color.TRANSPARENT);
        webView.getSettings().setLoadWithOverviewMode(true);
        webView.getSettings().setUseWideViewPort(true);
        webView.setClickable(false);
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                ViewUtils.hideViews(shimmerLayout);
                showRichMediaContainer(displayType);
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                shimmerLayout.stopShimmer();
            }
        });
        activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGifIcon.loadUrl(mediaUrl);
    }

    public void setIvRichContentIcon(final String imageUrl, String displayType) {
        ImageView imageView = activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentIcon;
        if (displayType.equals(NotificationConstants.DISPLAY_TYPE_BANNER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayout.webViewGif.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.gifContainer.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayout.ivRichContentImage.setVisibility(View.GONE);
        } else if (displayType.equals(NotificationConstants.DISPLAY_TYPE_FOOTER)) {
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.webViewGifFooter.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.gifContainerFooter.setVisibility(View.GONE);
            activityNotificationDetailsBinding.layoutRichNotificationLayoutFooter.ivRichContentImageFooter.setVisibility(View.GONE);
        }

        int deviceWidth = DeviceUtils.getDeviceWidth(this);
        mActualImageWidth = (deviceWidth - (2 * getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_140dp)));
        int mActualImageHeight = (mActualImageWidth * ASPECT_HEIGHT) / ASPECT_WIDTH;
        imageView.getLayoutParams().width = mActualImageWidth;
        imageView.setMaxHeight(mActualImageHeight);
        imageView.setVisibility(View.VISIBLE);
        Picasso.get()
                .load(imageUrl)
                .noPlaceholder()
                .priority(Picasso.Priority.HIGH)
                .transform(cropPosterTransformation)
                .into(imageView, new Callback() {
                    @Override
                    public void onSuccess() {
                        ViewUtils.hideViews(shimmerLayout);
                        showRichMediaContainer(displayType);
                    }

                    @Override
                    public void onError(Exception e) {
                        shimmerLayout.stopShimmer();
                    }
                });
    }

    private Transformation cropPosterTransformation = new Transformation() {

        @Override
        public Bitmap transform(Bitmap source) {
            double aspectRatio = (double) source.getHeight() / (double) source.getWidth();
            int targetHeight = (int) (mActualImageWidth * aspectRatio);
            Bitmap result = Bitmap.createScaledBitmap(source, mActualImageWidth, targetHeight, false);
            if (result != source) {
                source.recycle();
            }
            return result;
        }

        @Override
        public String key() {
            return "cropPosterTransformation" + mActualImageWidth;
        }
    };

    @Override
    public void showImageLoading(boolean isLoading) {
        if (isLoading) {
            ViewUtils.showViews(shimmerLayout);
            ViewUtils.hideViews(activityNotificationDetailsBinding.layoutRichNotificationLayout.richNotificationContainer);
        }
    }

    @Override
    public void showImageLoadingFailure() {
        shimmerLayout.stopShimmer();
    }

    @Override
    public boolean onOptionsItemSelected(final MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            mNotificationDetailsPresenter.sendBackArrowAnalytics();
            super.onOptionsItemSelected(item);
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        mNotificationDetailsPresenter.sendBackArrowAnalytics();
        super.onBackPressed();
    }

    @Override
    public String getCMSBasePath(String sourceCms) {
        if (sourceCms.equalsIgnoreCase(NotificationConstants.SOURCECMS_ADOBE_DAM)) {
            if (DeviceUtils.isStoreAndProdBuild()) {
                return getString(R.string.sourcms_adobe_dam_release);
            } else {
                return getString(R.string.sourcms_adobe_dam);
            }
        } else if (sourceCms.equalsIgnoreCase(NotificationConstants.SOURCECMS_GEN)) {
            return StringUtils.EMPTY_STRING;
        }
        return null;
    }
}
