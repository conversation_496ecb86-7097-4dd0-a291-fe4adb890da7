package za.co.nedbank.ui.view.home.non_tp_client.non_tp_apply;

import java.util.List;

import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;
import za.co.nedbank.ui.view.home.non_tp_client.BaseDashboardAccountsView;

public interface NonTpApplyView extends BaseDashboardAccountsView {
    void setChatIcon(int totalUnreadMessageCount);

    void showProgressBar(boolean loading);

    void showMediaAndOfferCards(List<AppLayoutViewModel> mediaContentList);
}
