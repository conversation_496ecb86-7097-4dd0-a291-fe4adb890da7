package za.co.nedbank.ui.view.ita.qrcodelogin;

import android.os.Bundle;

import androidx.annotation.Nullable;

import org.greenrobot.eventbus.EventBus;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.ui.di.AppDI;

public class QrCodeProcessingActivity extends NBBaseActivity implements QrCodeProcessingView {

    @Inject
    QrCodeProcessingPresenter qrCodeProcessingPresenter;

    private String intent;
    private String token;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        qrCodeProcessingPresenter.bind(this);
        setContentView(R.layout.activity_qrcode_processing);

        getIntentData();
        qrCodeProcessingPresenter.loginWithQrCode();
    }

    private void getIntentData() {
        if (getIntent() != null && getIntent().getExtras() != null) {
            Bundle extras = getIntent().getExtras();
            intent = extras.getString(Constants.QRCODE_INTENT, StringUtils.EMPTY_STRING);
            token = extras.getString(Constants.QRCODE_TOKEN, StringUtils.EMPTY_STRING);
        }
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
        qrCodeProcessingPresenter.unbind();
    }

    @Override
    public String getIntentType() {
        return intent;
    }

    @Override
    public String getToken() {
        return token;
    }
}
