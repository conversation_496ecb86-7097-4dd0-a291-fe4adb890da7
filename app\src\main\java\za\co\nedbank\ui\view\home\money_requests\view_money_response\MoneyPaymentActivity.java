/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.widget.EditText;

import androidx.core.view.ViewCompat;

import com.jakewharton.rxbinding2.widget.RxTextView;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.enumerations.NotificationType;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.ValidationResult;
import za.co.nedbank.core.view.accounts.listener.IViewHolderInteraction;
import za.co.nedbank.core.view.accounts.ui.AccountsViewFragment;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.databinding.ActivityMoneyResponsePaymentBinding;
import za.co.nedbank.payment.pay.view.amount.PayNotificationBottomSheetDialog;
import za.co.nedbank.payment.pay.view.model.PaymentViewModel;
import za.co.nedbank.payment.transfer.navigator.TransferNavigationTarget;
import za.co.nedbank.payment.transfer.view.model.response.LimitsViewModel;
import za.co.nedbank.ui.di.AppDI;

public class MoneyPaymentActivity extends NBBaseActivity implements MoneyResponseView, IViewHolderInteraction {

    @Inject
    MoneyPaymentPresenter moneyPaymentPresenter;

    private AccountsViewFragment fragFromAccounts;
    private Handler mHandler;
    private PayNotificationBottomSheetDialog mBottomSheetDialogFragment;
    private PaymentViewModel paymentViewModel;
    boolean isAccountReceived, isLimitReceived;
    private List<AccountViewModel> accountViewModelList;
    private ActivityMoneyResponsePaymentBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityMoneyResponsePaymentBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        setSupportActionBar(binding.toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowHomeEnabled(true);
        binding.txvHeader.setText(String.format("%s%s", getString(R.string.pay_toolbar_title), TextUtils.isEmpty(getReceiverName()) ? StringUtils.EMPTY_STRING : String.format("%s%s", StringUtils.SPACE, getReceiverName())));
        mHandler = new Handler(Looper.getMainLooper());
        setNextButtonEnabled(false);
        fragFromAccounts = (AccountsViewFragment) getSupportFragmentManager().findFragmentById(za.co.nedbank.payment.R.id.frgFromAccounts);

        EditText edtAmount = binding.lnrTopView.getEditText();
        binding.mainAppbar.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> ViewCompat.setElevation(appBarLayout, 50));

        Observable<CharSequence> textChangeObservable = RxTextView.textChanges(edtAmount).skip(1);
        textChangeObservable.subscribe(chars -> {
            if (isAccountReceived && isLimitReceived) {
                moneyPaymentPresenter.checkFields(binding.lnrTopView, getSelectedAccountBalance(),
                        binding.tilYourRef, binding.tilRecipientReference, binding.tilRecipientMobileNumber, binding.tilRecipientEmailAddress, isViewAvailBalance());
            }
        },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        moneyPaymentPresenter.bind(this);
        moneyPaymentPresenter.setYourReference();
        moneyPaymentPresenter.getAccounts();
        moneyPaymentPresenter.getLimitsForAccount();
        binding.edtNotification.setOnClickListener(v -> onNotificationClick());
        binding.btnNext.setOnClickListener(v -> nextButtonClick());
        setTextWatchers();
    }

    private void setTextWatchers() {
        binding.edtYourRef.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // No action required
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                yourReferenceTextChanged();
            }

            @Override
            public void afterTextChanged(Editable s) {
                // No action required
            }
        });
        binding.edtRecipientReference.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // No action required
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                recipientReferenceTextChanged();
            }

            @Override
            public void afterTextChanged(Editable s) {
                // No action required
            }
        });
        binding.edtRecipientMobileNumber.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // No action required
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                recipientMobileNumberTextChanged();
            }

            @Override
            public void afterTextChanged(Editable s) {
                // No action required
            }
        });
        binding.edtRecipientEmailAddress.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // No action required
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                recipirntEmailAddressTextChanged();
            }

            @Override
            public void afterTextChanged(Editable s) {
                // No action required
            }
        });
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
        moneyPaymentPresenter.unbind();
    }

    @Override
    public void showError(String error) {
        showError(getString(R.string.error), error);
    }

    @Override
    public void setAccounts() {
        isAccountReceived = true;
        for (int i = 0; i < accountViewModelList.size(); i++) {
            if (!accountViewModelList.get(i).isViewCurrBal()) {
                accountViewModelList.remove(i);
            }
        }
        mHandler.postDelayed(() -> {
            if (accountViewModelList.size() > 0) {
                fragFromAccounts.refreshViews(accountViewModelList, true);
            } else {
                fragFromAccounts.refreshViews(null, true, getString(za.co.nedbank.payment.R.string.no_accounts_available));
            }
        }, TransferNavigationTarget.VALUES.ACCOUNTS_UPDATE_DELAY);
    }

    @Override
    public void setAccountContainerList(List<AccountViewModel> accountContainerList) {
        this.accountViewModelList = accountContainerList;
    }


    @Override
    public void setNextButtonEnabled(boolean isEnabled) {
        binding.btnNext.setEnabled(isEnabled);
    }

    public void nextButtonClick() {
        moneyPaymentPresenter.setPaymentData();
        moneyPaymentPresenter.navigateToMoneyPaymentReview();
    }

    public void yourReferenceTextChanged() {
        moneyPaymentPresenter.checkFields(binding.lnrTopView, getSelectedAccountBalance(), binding.tilYourRef, binding.tilRecipientReference, binding.tilRecipientMobileNumber, binding.tilRecipientEmailAddress, isViewAvailBalance());
    }

    public void recipientReferenceTextChanged() {
        moneyPaymentPresenter.checkFields(binding.lnrTopView, getSelectedAccountBalance(), binding.tilYourRef, binding.tilRecipientReference, binding.tilRecipientMobileNumber, binding.tilRecipientEmailAddress, isViewAvailBalance());
    }

    public void recipientMobileNumberTextChanged() {
        moneyPaymentPresenter.checkFields(binding.lnrTopView, getSelectedAccountBalance(), binding.tilYourRef, binding.tilRecipientReference, binding.tilRecipientMobileNumber, binding.tilRecipientEmailAddress, isViewAvailBalance());
    }

    public void recipirntEmailAddressTextChanged() {
        moneyPaymentPresenter.checkFields(binding.lnrTopView, getSelectedAccountBalance(), binding.tilYourRef, binding.tilRecipientReference, binding.tilRecipientMobileNumber, binding.tilRecipientEmailAddress, isViewAvailBalance());
    }

    @Override
    public void onParentViewSelected(int position, boolean isFromAdapter) {
        if (!isFromAdapter) {
            moneyPaymentPresenter.checkFields(binding.lnrTopView, getSelectedAccountBalance(), binding.tilYourRef, binding.tilRecipientReference, binding.tilRecipientMobileNumber, binding.tilRecipientEmailAddress, isViewAvailBalance());
            mHandler.postDelayed(() -> fragFromAccounts.refreshViews(accountViewModelList, false), TransferNavigationTarget.VALUES.ACCOUNTS_UPDATE_DELAY);
        }
    }

    @Override
    public void showNotificationOptions() {
        if (mBottomSheetDialogFragment == null) {
            mBottomSheetDialogFragment = new PayNotificationBottomSheetDialog();
            mBottomSheetDialogFragment.setCallBackListener(type -> moneyPaymentPresenter.setNotificationType(type));
        }
        if (!mBottomSheetDialogFragment.isAdded()) {
            mBottomSheetDialogFragment.show(getSupportFragmentManager(), mBottomSheetDialogFragment.getTag());
        }
    }

    protected void onNotificationClick() {
        moneyPaymentPresenter.showNotificationOptions();
    }

    @Override
    public void setNotificationType(String type) {
        binding.edtNotification.setText(type);
        if (type.equals(NotificationType.SMS.getValue())) {
            ViewUtils.showViews(binding.tilRecipientMobileNumber);
            ViewUtils.hideViews(binding.tilRecipientEmailAddress);
        } else if (type.equals(NotificationType.EMAIL.getValue())) {
            ViewUtils.showViews(binding.tilRecipientEmailAddress);
            ViewUtils.hideViews(binding.tilRecipientMobileNumber);
        } else if (type.equals(NotificationType.NONE.getValue())) {
            ViewUtils.hideViews(binding.tilRecipientMobileNumber, binding.tilRecipientEmailAddress);
        }
    }

    @Override
    public void setYourReference(String reference) {
        binding.edtYourRef.setText(reference);
    }

    @Override
    public String getNotificationMobileNumber() {
        return binding.edtRecipientMobileNumber.getText().toString();
    }

    @Override
    public String getNotificationEmailAddress() {
        return binding.edtRecipientEmailAddress.getText().toString();
    }

    @Override
    public void trackFetchAccountsFailure() {
        moneyPaymentPresenter.trackFetchAccountsFailure(getString(za.co.nedbank.payment.R.string.no_accounts_available));
    }

    @Override
    public void trackLimitsFailure() {
        moneyPaymentPresenter.trackLimitsFailure(getString(za.co.nedbank.payment.R.string.error_generic));
    }

    @Override
    public String getReceiverName() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_RECEIVER_NAME)) {
            return bundle.getString(NavigationTarget.PARAM_RECEIVER_NAME);
        }

        return null;
    }

    @Override
    public String getReceiverMobileNumber() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_RECEIVER_MOBILE_NUMBER)) {
            return bundle.getString(NavigationTarget.PARAM_RECEIVER_MOBILE_NUMBER);
        }

        return null;
    }

    @Override
    public String getReceiverDescription() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_RECEIVER_DESCRIPTION)) {
            return bundle.getString(NavigationTarget.PARAM_RECEIVER_DESCRIPTION);
        }

        return null;
    }

    @Override
    public String getReceiverAccountNumber() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_RECEIVER_ACCOUNT_NUMBER)) {
            return bundle.getString(NavigationTarget.PARAM_RECEIVER_ACCOUNT_NUMBER);
        }

        return null;
    }

    @Override
    public String getReceiverAccountType() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_RECEIVER_ACCOUNT_TYPE)) {
            return bundle.getString(NavigationTarget.PARAM_RECEIVER_ACCOUNT_TYPE);
        }
        return null;
    }

    @Override
    public long getPaymentRequestId() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_PAYMENT_REQUEST_ID)) {
            return bundle.getLong(NavigationTarget.PARAM_PAYMENT_REQUEST_ID);
        }

        return 0;
    }

    @Override
    public String getYourReference() {
        return binding.edtYourRef.getText().toString();
    }

    @Override
    public String getRecipientReference() {
        return binding.edtRecipientReference.getText().toString();
    }

    @Override
    public String getNotificationType() {
        return binding.edtNotification.getText().toString();
    }

    @Override
    public void setPaymentDetails(PaymentViewModel paymentViewModel) {
        this.paymentViewModel = paymentViewModel;
    }

    @Override
    public void setRequestedAmount(double amount) {
        binding.lnrTopView.getEditText().setText(FormattingUtil.convertToSouthAfricaFormattedCurrency(amount));
    }

    @Override
    public PaymentViewModel getPaymentModel() {
        return paymentViewModel;
    }

    @Override
    public double getRequestedAmount() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_REQUESTED_AMOUNT)) {
            return bundle.getDouble(NavigationTarget.PARAM_REQUESTED_AMOUNT);
        }
        return 0;
    }

    @Override
    public String amountToPay() {
        return binding.lnrTopView.getValue();
    }

    @Override
    public AccountViewModel provideSelectedAccount() {
        return fragFromAccounts.provideSelectedAccount();
    }

    private double getSelectedAccountBalance() {
        return fragFromAccounts.getSelectedAccountBalance();
    }

    private boolean isViewAvailBalance() {
        return fragFromAccounts.isAvailViewBalance();
    }


    @Override
    public void setAccountEnabled(boolean isEnabled) {
        fragFromAccounts.setEnableAccount(isEnabled);
    }

    @Override
    public void showEmailError(ValidationResult result) {
        if (result.isOk()) {
            binding.tilRecipientEmailAddress.setError(StringUtils.EMPTY_STRING);
        } else {
            binding.tilRecipientEmailAddress.setError(result.getErrorsMessage());
        }
    }

    @Override
    public void showMobileNumberError(ValidationResult result) {
        if (result.isOk()) {
            binding.tilRecipientMobileNumber.setError(StringUtils.EMPTY_STRING);
        } else {
            binding.tilRecipientMobileNumber.setError(result.getErrorsMessage());
        }
    }

    @Override
    public void setLimits(LimitsViewModel limitsViewModel) {
        isLimitReceived = true;
        binding.lnrTopView.setProgress(limitsViewModel.getLimitProgressPercentage());
        binding.lnrTopView.getAmountLimitTextView().setText(String.format(getString(za.co.nedbank.payment.R.string.payment_limit_remaining), FormattingUtil.convertToSouthAfricaFormattedCurrency(limitsViewModel.getRemainingLimit())));
    }
}
