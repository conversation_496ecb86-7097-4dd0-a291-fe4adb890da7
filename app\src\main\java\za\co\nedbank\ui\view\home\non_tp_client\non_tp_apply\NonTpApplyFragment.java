package za.co.nedbank.ui.view.home.non_tp_client.non_tp_apply;

import static za.co.nedbank.core.utils.StringUtils.removeTitles;
import static za.co.nedbank.enroll_v2.Constants.BundleKeys.HIDE_NOTIFICATIONS;
import static za.co.nedbank.uisdk.utils.StringUtils.EMPTY_STRING;
import static za.co.nedbank.uisdk.utils.StringUtils.isNullOrEmpty;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.List;
import java.util.logging.Logger;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.common.FeatureCardEnum;
import za.co.nedbank.core.common.ProductType;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.convochatbot.view.ChatbotConstants;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.media_content.MediaContentAdapter;
import za.co.nedbank.core.view.media_content.MediaContentClickListener;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.databinding.FragmentNonTpApplyBinding;
import za.co.nedbank.enroll_v2.view.applications.ApplicationFragment;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.tracking.AppTracking;
import za.co.nedbank.uisdk.utils.StringUtils;

public class NonTpApplyFragment extends NBBaseFragment implements NonTpApplyView, MediaContentClickListener {

    @Inject
    FeatureSetController mFeatureSetController;
    @Inject
    ApplicationStorage mApplicationStorage;
    @Inject
    protected NonTpApplyPresenter mPresenter;
    private boolean isBankCardDisable;
    private boolean isUnreadChatAvailable;
    private boolean isChatConnected;

    private String conversationId = AppTracking.EN_3_NON_TP_APPLY_SCREEN_LOAD;
    private boolean hideNotifications;
    private FragmentNonTpApplyBinding binding;

    public static NonTpApplyFragment getInstance(boolean hideNotifications) {
        NonTpApplyFragment fragment = new NonTpApplyFragment();
        Bundle bundle = new Bundle();
        bundle.putBoolean(HIDE_NOTIFICATIONS, hideNotifications);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        hideNotifications = getArguments().getBoolean(HIDE_NOTIFICATIONS, false);
        AppDI.getFragmentComponent(this).inject(this);
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_SAVE_RESUME)) {
            Fragment applicationFragment = ApplicationFragment.Companion.getNewInstance(false, EMPTY_STRING);
            replaceFragment(applicationFragment, R.id.content);
        }
    }

    private void setUpRecyclerView() {
        binding.rvAppbarContent.setHasFixedSize(true);
        binding.rvAppbarContent.setLayoutManager(new LinearLayoutManager(getActivity()));
        binding.rvAppbarContent.setItemAnimator(null);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mPresenter.unbind();
    }

    @Override
    public View onCreateView(final LayoutInflater inflater, final ViewGroup container, final Bundle savedInstanceState) {
        binding = FragmentNonTpApplyBinding.inflate(inflater, container, false);
        mPresenter.bind(this);
        binding.nonTpApplyChatIcon.setOnClickListener(v -> onChatIconClick());
        binding.nonTpApplyLotteBellView.setOnClickListener(v -> onClickNotificationCount());
        binding.nonTpApplyBellIcon.setOnClickListener(v -> onClickNotificationCount());
        return binding.getRoot();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        binding.nonTpApplyTitle.setVisibility(View.INVISIBLE);

        if (!isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS)
                || !isFeatureDisabled(FeatureConstants.PRE_APPROVED_ACCOUNTS)) {
            ViewUtils.showViews(binding.nonTpApplyBellIcon);
        }

        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_SAVE_RESUME)) {
            binding.nonTpApplyTitle.setText(getString(R.string.welcome));
            binding.rvAppbarContent.setVisibility(View.GONE);
        } else {
            isBankCardDisable = mFeatureSetController.isFeatureDisabled(FeatureConstants.BANK_CARD_DISABLE);
            setUpRecyclerView();
        }
        if (hideNotifications) {
            ViewUtils.hideViews(binding.nonTpApplyBellIcon);
        }
    }


    @Override
    public void showMediaAndOfferCards(List<AppLayoutViewModel> mediaContentList) {
        boolean isNewFeatureTileEnable = !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_NEWFEATURES_TILE_CONTROL);
        MediaContentAdapter adapter = new MediaContentAdapter(getContext(), this, mediaContentList, DeviceUtils.getDeviceWidth(getActivity()), false, false, mFeatureSetController);
        adapter.removeNewFeatureTile(isNewFeatureTileEnable);
        binding.rvAppbarContent.setAdapter(adapter);
    }

    @Override
    public void onResume() {
        super.onResume();
        Logger.getLogger("#### NONTP_APPLY_Fragment ####");
        mPresenter.getTotalBankingUnreadMessages();
        if (!hideNotifications) {
            mPresenter.loadNotificationCount();
        }
        if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_SAVE_RESUME))
            mPresenter.loadProductCard(isBankCardDisable);
    }

    @Override
    public void onUnreadChatEvent(UnreadChatEvent unreadChatEvent) {
        isUnreadChatAvailable = unreadChatEvent.isUnreadChat();
        binding.nonTpApplyChatIcon.setImageResource(unreadChatEvent.isUnreadChat() ?
                R.drawable.ic_chat_green_notification_wrapper : R.drawable.ic_chat_icon_green_wrapper);
    }

    @Override
    public void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent) {
        binding.nonTpApplyChatIcon.setImageResource(unreadChatEvent.isUnreadLifestyleChat() ?
                R.drawable.ic_chat_green_notification_wrapper : R.drawable.ic_chat_icon_green_wrapper);
    }


    @Override
    public void setPreferredName(String name) {
        if (name == null || name.trim().isEmpty()) {
            mPresenter.loadCustomerNAme();
        } else {
            binding.nonTpApplyTitle.setText(name.trim());
        }
        binding.nonTpApplyTitle.setVisibility(View.VISIBLE);
    }

    @Override
    public void handlePreferredNameError() {
        mPresenter.fetchUserDetail(Boolean.TRUE);
    }

    @Override
    public void receiveNotificationCount(int notificationCount) {
        if (notificationCount > 0) {
            ViewUtils.hideViews(binding.nonTpApplyBellIcon);
            ViewUtils.showViews(binding.nonTpApplyLotteBellView);
            binding.nonTpApplyLotteBellView.setRepeatCount(2);
            binding.nonTpApplyLotteBellView.playAnimation();
        } else {
            ViewUtils.hideViews(binding.nonTpApplyLotteBellView);
            ViewUtils.showViews(binding.nonTpApplyBellIcon);
        }
    }

    @Override
    public void setCustomerName(String customerUserName) {
        if (isNullOrEmpty(customerUserName.trim())) {
            binding.nonTpApplyTitle.setText(EMPTY_STRING);
        } else {
            mApplicationStorage.putString(
                    Constants.KEY_USER_CLIENT_NAME, customerUserName.trim());
            binding.nonTpApplyTitle.setText(removeTitles(customerUserName.trim()));
        }
        binding.nonTpApplyTitle.setVisibility(View.VISIBLE);
    }

    @Override
    public void setUserInfo(UserDetailViewModel userDetailViewModel, boolean updateCustomerName) {
        if (!TextUtils.isEmpty(userDetailViewModel.getClientType())) {
            mApplicationStorage.putInteger(Constants.KEY_USER_CLIENT_TYPE, Integer.parseInt(userDetailViewModel.getClientType()));
        }
        if (userDetailViewModel != null && updateCustomerName) {
            setCustomerName(userDetailViewModel.getFullNames());
        }
    }

    @Override
    public void setChatIcon(int totalUnreadMessageCount) {
        binding.nonTpApplyChatIcon.setImageResource(totalUnreadMessageCount > 0 ?
                R.drawable.ic_chat_green_notification_wrapper : R.drawable.ic_chat_icon_green_wrapper);
    }

    @Override
    public void showProgressBar(boolean loading) {

    }

    @Override
    public void showError(String message) {
        showError(getString(R.string.error), message);
    }

    @Override
    public void showEmptyUserView(String msg) {

    }

    @Override
    public boolean isFeatureDisabled(String feature) {
        return mFeatureSetController.isFeatureDisabled(feature);
    }

    @Override
    public boolean canTransact() {
        return false;
    }

    @Override
    public boolean isSAResident() {
        return false;
    }

    @Override
    public String getFicaStatus() {
        return null;
    }

    @Override
    public void startBrowser(String url) {
        //Not required here...
    }

    @Override
    public void updateMediaCardViewModel(MediaCardViewModel mediaCardViewModel) {
        //Method not required here...
    }

    @Override
    public void onServerStateChanged(boolean chatConnected) {
        isChatConnected = chatConnected;

    }

    @Override
    public void onClickMediaContentItem(MediaCardViewModel mediaCardViewModel, int position) {
        position++;
        if (mediaCardViewModel != null && StringUtils.isNotEmpty(mediaCardViewModel.getName())) {
            mPresenter.logMediaCardClickEvent(mediaCardViewModel.getName(), position);
        }
    }

    @Override
    public void onClickProductOfferItem(ProductType productType) {
        if (ProductType.BANK == productType) {
            mPresenter.navigateToBankLayout();
        } else if (ProductType.FINANCIAL_PLANNER == productType) {
            mPresenter.onFinancialPlannerClick();
        } else if (ProductType.LOAN == productType) {
            mPresenter.onLoanClick();
        }

    }

    @Override
    public void onClickFeatureCardItem(FeatureCardEnum featureCardEnum) {
        //No implementation for now.
    }

    @Override
    public void onClickFeatureCardItem(MediaCardViewModel featureCardItemViewModel) {
        //No implementation for now.
    }

    public void onChatIconClick() {

        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_FORCE_UPDATE)) {
            mPresenter.handleUpdateApp(getString(R.string.app_update_available_message_chatbot),
                    getString(R.string.app_update_available_title_chatbot));
        } else if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_CHATBOT)) {
            handleEnbiFlow();
        } else {
            handleNormalChatFlow();
        }
    }

    public void onClickNotificationCount() {
        mPresenter.handleClickNotificationCount();
    }

    private void handleEnbiFlow() {
        if (isChatConnected || isUnreadChatAvailable) {
            mPresenter.navigateToChatActivity();
        } else if (!mPresenter.isBusinessUser()
                || !mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_CHATBOT_SBS)) {
            handleEnbiFlowFirstTimeAndReturningUsers();
        } else {
            handleNormalChatFlow();
        }
    }

    private void handleNormalChatFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.APP_CHAT)) {
            mPresenter.navigateToChatActivity();
        } else {
            mPresenter.navigateToChatErrorActivity();
        }
    }

    private void handleEnbiFlowFirstTimeAndReturningUsers() {
        if (isChatbotIntoJourneyCompleted())
            mPresenter.navigateToChatBotActivity(conversationId);
        else
            mPresenter.navigateToChatBotIntroductionActivity();
    }

    public boolean isChatbotIntoJourneyCompleted() {
        return mApplicationStorage.getBoolean(ChatbotConstants.StorageKeys.CHATBOT_INTRO_DISPLAYED, false);
    }

    private void enbiflowNonTpLoggedInUser() {
        if (isChatConnected || isUnreadChatAvailable) {
            mPresenter.navigateToChatActivity();
        } else {
            // dc chat  and lifestyle both enabled
            if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_DCCHAT)
            ) {
                mPresenter.navigateToChatActivity();
            }
            // lifestyle chat enabled and dc disabled
            else if (mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_DCCHAT)) {
                enbiflowNonTpLoggedInUserDcDisableAndLifestyleEnbled();
            } else {
                if (isChatbotIntoJourneyCompleted())
                    mPresenter.navigateToChatBotActivity(conversationId);
                else
                    mPresenter.navigateToChatBotIntroductionActivity();
            }
        }
    }


    public void enbiflowNonTpLoggedInUserDcDisableAndLifestyleEnbled() {
        if (!mPresenter.isBusinessUser()) {
            mPresenter.navigateToChatActivity();
        } else {
            if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_CHATBOT_SBS)) {
                if (isChatbotIntoJourneyCompleted())
                    mPresenter.navigateToChatBotActivity(conversationId);
                else
                    mPresenter.navigateToChatBotIntroductionActivity();
            } else {
                // reduntant consition we can not make both dc chat and sbs chatbot off at same time
                mPresenter.navigateToChatActivity();
            }
        }
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mPresenter.loadPreferredUserName();
    }
}
