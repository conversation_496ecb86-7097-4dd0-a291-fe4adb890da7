package za.co.nedbank.ui.view.card_delivery.deliver_result;

import za.co.nedbank.core.base.NBBaseView;

public interface CardDeliveryResultView extends NBBaseView {
    String getSelectedDeliveryOption();

    boolean isOperationSuccess();

    void showLockerSuccess();

    void showDeliverToMeSuccess();

    void showBranchPickSuccess();

    void showEficaCardServiceUnavailableError();

    void showReplaceCardServiceUnavailableError();

    void showReplaceCardOrderingFailure();

    boolean isLockerLocationError();

    void showEficaLockerListError();

    String getFlow();

    String getCardDeliverySubFeature();

    String getCardActionName();
}
