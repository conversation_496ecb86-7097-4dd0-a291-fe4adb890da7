package za.co.nedbank.ui.view.notification.notification_preferences.all_accounts_preferences;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.FBResponseData;
import za.co.nedbank.core.domain.model.notifications.FBUpdatePreferenceRequestData;
import za.co.nedbank.core.domain.model.notifications.TiPreferenceData;
import za.co.nedbank.core.domain.usecase.notifications.UpdatePreferenceUseCase;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.view.fbnotifications.AccountPreference;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.PreApprovedOffersTrackingEvent;

public class AllAccountsPreferencePresenter extends NBBasePresenter<AllAccountsPreferenceView> {
    private final NavigationRouter mNavigationRouter;
    private Analytics mAnalytics;
    private UpdatePreferenceUseCase mUpdatePreferenceUseCase;
    private boolean isSMSPrefOn = false;
    private boolean isPushPrefOn = false;


    @Inject
    AllAccountsPreferencePresenter(final NavigationRouter navigationRouter,
                                   final UpdatePreferenceUseCase updatePreferenceUseCase,
                                   final Analytics analytics) {
        this.mNavigationRouter = navigationRouter;
        this.mUpdatePreferenceUseCase = updatePreferenceUseCase;
        this.mAnalytics = analytics;
    }

    public void updatePreferences(List<AccountPreference> accountPreferences, AccountPreference accountPreference, boolean useAppSpendLimitPrevValue, boolean useAppBalanceLimitPrevValue, boolean useSMSSpendLimitPrevValue, boolean useSMSBalanceLimitPrevValue) {
        isSMSPrefOn = accountPreference.isAllowSMSForTrans();
        isPushPrefOn = accountPreference.isAllowPushForTrans();
        mUpdatePreferenceUseCase.execute(getUpdatePrefReqData(accountPreferences, accountPreference, useAppSpendLimitPrevValue, useAppBalanceLimitPrevValue, useSMSSpendLimitPrevValue, useSMSBalanceLimitPrevValue)).compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgress(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgress(false);
                    }
                })
                .subscribe(fbResponseData -> {
                    if (fbResponseData != null && view != null) {
                        handleUpdatePreferencesResponse(fbResponseData);
                    }
                }, throwable -> {
                    showPreferencesError(false);
                });

    }

    public FBUpdatePreferenceRequestData getUpdatePrefReqData(List<AccountPreference> accountPreferences, AccountPreference accountPreference, boolean useAppSpendLimitPrevValue, boolean useAppBalanceLimitPrevValue, boolean useSMSSpendLimitPrevValue, boolean useSMSBalanceLimitPrevValue) {
        FBUpdatePreferenceRequestData fbUpdatePreferenceRequestData = new FBUpdatePreferenceRequestData();
        FBUpdatePreferenceRequestData.Preference preference = fbUpdatePreferenceRequestData.new Preference();
        List<TiPreferenceData> tiPreferenceDataList = new ArrayList<>();
        for (AccountPreference preAccountPreference : accountPreferences) {

            if (!accountPreference.isAllowPushForTrans() && !accountPreference.isAllowSMSForTrans()) {
                TiPreferenceData tiSmsEmptyPreference = new TiPreferenceData();
                tiSmsEmptyPreference.setAccount(preAccountPreference.getAccountNumber());
                tiSmsEmptyPreference.setAllowSmsForTransactions(false);
                tiSmsEmptyPreference.setAllowPushNotificationForTransactions(false);
                tiSmsEmptyPreference.setSpendAmountThreshold(0.0);
                tiSmsEmptyPreference.setLowBalanceThreshold(0.0);
                tiPreferenceDataList.add(tiSmsEmptyPreference);

                TiPreferenceData tiAppEmptyPreference = new TiPreferenceData();
                tiAppEmptyPreference.setAccount(preAccountPreference.getAccountNumber());
                tiAppEmptyPreference.setAllowSmsForTransactions(false);
                tiAppEmptyPreference.setAllowPushNotificationForTransactions(false);
                tiAppEmptyPreference.setSpendAmountThreshold(0.0);
                tiAppEmptyPreference.setLowBalanceThreshold(0.0);
                tiPreferenceDataList.add(tiAppEmptyPreference);

            } else if (accountPreference.isAllowPushForTrans() && accountPreference.isAllowSMSForTrans()) {
                TiPreferenceData tiSmsEmptyPreference = new TiPreferenceData();
                tiSmsEmptyPreference.setAccount(preAccountPreference.getAccountNumber());
                tiSmsEmptyPreference.setAllowSmsForTransactions(true);
                tiSmsEmptyPreference.setAllowPushNotificationForTransactions(false);

                if (useSMSSpendLimitPrevValue) {
                    tiSmsEmptyPreference.setSpendAmountThreshold(preAccountPreference.getSpendLimitSMS());
                } else {
                    tiSmsEmptyPreference.setSpendAmountThreshold(accountPreference.getSpendLimitSMS());
                }
                if (useSMSBalanceLimitPrevValue) {
                    tiSmsEmptyPreference.setLowBalanceThreshold(preAccountPreference.getBalanceLimitSMS());
                } else {
                    tiSmsEmptyPreference.setLowBalanceThreshold(accountPreference.getBalanceLimitSMS());
                }

                tiPreferenceDataList.add(tiSmsEmptyPreference);

                TiPreferenceData tiAppEmptyPreference = new TiPreferenceData();
                tiAppEmptyPreference.setAccount(preAccountPreference.getAccountNumber());
                tiAppEmptyPreference.setAllowSmsForTransactions(false);
                tiAppEmptyPreference.setAllowPushNotificationForTransactions(true);

                if (useAppSpendLimitPrevValue) {
                    tiAppEmptyPreference.setSpendAmountThreshold(preAccountPreference.getSpendLimitApp());
                } else {
                    tiAppEmptyPreference.setSpendAmountThreshold(accountPreference.getSpendLimitApp());
                }
                if (useAppBalanceLimitPrevValue) {
                    tiAppEmptyPreference.setLowBalanceThreshold(preAccountPreference.getBalanceLimitApp());
                } else {
                    tiAppEmptyPreference.setLowBalanceThreshold(accountPreference.getBalanceLimitApp());
                }


                tiPreferenceDataList.add(tiAppEmptyPreference);

            } else if (!accountPreference.isAllowPushForTrans() && accountPreference.isAllowSMSForTrans()) {
                TiPreferenceData tiSmsEmptyPreference = new TiPreferenceData();
                tiSmsEmptyPreference.setAccount(preAccountPreference.getAccountNumber());
                tiSmsEmptyPreference.setAllowSmsForTransactions(true);
                tiSmsEmptyPreference.setAllowPushNotificationForTransactions(false);

                if (useSMSSpendLimitPrevValue) {
                    tiSmsEmptyPreference.setSpendAmountThreshold(preAccountPreference.getSpendLimitSMS());
                } else {
                    tiSmsEmptyPreference.setSpendAmountThreshold(accountPreference.getSpendLimitSMS());
                }
                if (useSMSBalanceLimitPrevValue) {
                    tiSmsEmptyPreference.setLowBalanceThreshold(preAccountPreference.getBalanceLimitSMS());
                } else {
                    tiSmsEmptyPreference.setLowBalanceThreshold(accountPreference.getBalanceLimitSMS());
                }
                tiPreferenceDataList.add(tiSmsEmptyPreference);

                TiPreferenceData tiAppEmptyPreference = new TiPreferenceData();
                tiAppEmptyPreference.setAccount(preAccountPreference.getAccountNumber());
                tiAppEmptyPreference.setAllowSmsForTransactions(false);
                tiAppEmptyPreference.setAllowPushNotificationForTransactions(false);
                tiAppEmptyPreference.setSpendAmountThreshold(0.0);
                tiAppEmptyPreference.setLowBalanceThreshold(0.0);
                tiPreferenceDataList.add(tiAppEmptyPreference);

            } else if (accountPreference.isAllowPushForTrans() && !accountPreference.isAllowSMSForTrans()) {
                TiPreferenceData tiSmsEmptyPreference = new TiPreferenceData();
                tiSmsEmptyPreference.setAccount(preAccountPreference.getAccountNumber());
                tiSmsEmptyPreference.setAllowSmsForTransactions(false);
                tiSmsEmptyPreference.setAllowPushNotificationForTransactions(false);
                tiSmsEmptyPreference.setSpendAmountThreshold(0.0);
                tiSmsEmptyPreference.setLowBalanceThreshold(0.0);
                tiPreferenceDataList.add(tiSmsEmptyPreference);

                TiPreferenceData tiAppEmptyPreference = new TiPreferenceData();
                tiAppEmptyPreference.setAccount(preAccountPreference.getAccountNumber());
                tiAppEmptyPreference.setAllowSmsForTransactions(false);
                tiAppEmptyPreference.setAllowPushNotificationForTransactions(true);

                if (useAppSpendLimitPrevValue) {
                    tiAppEmptyPreference.setSpendAmountThreshold(preAccountPreference.getSpendLimitApp());
                } else {
                    tiAppEmptyPreference.setSpendAmountThreshold(accountPreference.getSpendLimitApp());
                }
                if (useAppBalanceLimitPrevValue) {
                    tiAppEmptyPreference.setLowBalanceThreshold(preAccountPreference.getBalanceLimitApp());
                } else {
                    tiAppEmptyPreference.setLowBalanceThreshold(accountPreference.getBalanceLimitApp());
                }

                tiPreferenceDataList.add(tiAppEmptyPreference);
            }

        }
        preference.setTiPreferences(tiPreferenceDataList);
        preference.setClientPreference(null);
        fbUpdatePreferenceRequestData.setPreference(preference);
        return fbUpdatePreferenceRequestData;
    }

    private void handleUpdatePreferencesResponse(FBResponseData fbResponseData) {
        int ONLY_SUCCESS_ELEMENT = 1;
        if (fbResponseData != null && fbResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = fbResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && resultDetailList.size() > 0) {
                    if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        navigateToPreferenceHme();
                        break;
                    } else {
                        handleError(resultDetailList);
                    }
                }
            }
        }
    }

    private void navigateToPreferenceHme() {
        if (view != null) {
            navigateToPreferenceHome();
        }
    }

    private void handleError(ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                showPreferencesError(true);
            }
        }
    }

    private void navigateToPreferenceHome() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCount();
        adobeContextData.setFeatureCategoryCount();
        adobeContextData.setSubFeature(PreApprovedOffersTrackingEvent.VAL_PUSH_SMS_SETTINGS);
        adobeContextData.setContext1(isPushPrefOn ? PreApprovedOffersTrackingEvent.VAL_PUSH_NOTIFICATION_ON : PreApprovedOffersTrackingEvent.VAL_PUSH_NOTIFICATION_OFF);
        adobeContextData.setContext2(isSMSPrefOn ? PreApprovedOffersTrackingEvent.VAL_SMS_NOTIFICATION_ON : PreApprovedOffersTrackingEvent.VAL_SMS_NOTIFICATION_OFF);
        mAnalytics.sendEventActionWithMap(PreApprovedOffersTrackingEvent.TAG_ACCOUNT_NOTIFICATION_PREF_ALL_SUCCESSFUL, cdata);
        trackActionAllAccountPreferenceBack();
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES).withIntentFlagClearTopSingleTop(true));
    }

    public void trackFailure(boolean isApiFailure, String message, boolean isPush, boolean isSMS) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCount();
        adobeContextData.setSubFeature(PreApprovedOffersTrackingEvent.VAL_PUSH_SMS_SETTINGS);
        adobeContextData.setContext1(isPush ? PreApprovedOffersTrackingEvent.VAL_PUSH_NOTIFICATION_ON : PreApprovedOffersTrackingEvent.VAL_PUSH_NOTIFICATION_OFF);
        adobeContextData.setContext2(isSMS ? PreApprovedOffersTrackingEvent.VAL_SMS_NOTIFICATION_ON : PreApprovedOffersTrackingEvent.VAL_SMS_NOTIFICATION_OFF);
        mAnalytics.trackFailure(isApiFailure, PreApprovedOffersTrackingEvent.TAG_ACCOUNT_NOTIFICATION_PREF_ALL_FAILURE, ApiAliasConstants.AC_NT_PREF, message, null, cdata);
    }

    private void showPreferencesError(boolean isApiFailure) {
        if (view != null) {
            view.showErrorForUpdatePreferences(isApiFailure, isPushPrefOn, isSMSPrefOn);
        }
    }

    public void navigateToAccountPreference(AccountPreference accountPreference) {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES_ACCOUNT).withParam(NotificationConstants.EXTRA.ACCOUNT_PREFERENCE, accountPreference));
    }

    void onUndoClick() {
        view.updateUI();
    }

    public void trackActionAllAccountPreferenceBack() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setSubFeatureCount();
        adobeContextData.setContext1Count();
        adobeContextData.setContext2Count();
        mAnalytics.sendEventActionWithMap(PreApprovedOffersTrackingEvent.TAG_BACK_NOTIFICATION_PREFERENCES, cdata);
    }
}
