package za.co.nedbank.ui.view.notification.ajo_notification_details;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;

public class AJONotificationDetailsPresenter extends NBBasePresenter<AJONotificationDetailsView> {

    private final NavigationRouter navigationRouter;
    private final ApplicationStorage mMemoryApplicationStorage;

    @Inject
    public AJONotificationDetailsPresenter(final NavigationRouter navigationRouter,
                                           final @Named("memory") ApplicationStorage memoryApplicationStorage) {
        this.navigationRouter = navigationRouter;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
    }

    void clearNotificationData() {
        mMemoryApplicationStorage.clearValue(NotificationConstants.STORAGE_KEYS.AJO_NOTIFICATION_PAYLOAD);
    }

    void handleOnClick(AjoPushPayloadDataModel.ActionButton selectedResponse) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_GLOBAL_NAVIGATION_HANDLER)
                .withParam(NotificationConstants.Navigation.TARGET, selectedResponse.getLink())
                .withParam(NotificationConstants.Navigation.FROM, NotificationConstants.AjoConstants.AJO_PUSH);
        navigationRouter.navigateWithResult(navigationTarget).subscribe(
                navigationResult -> {
                    boolean actionDismiss = navigationResult.getStringParam(NotificationConstants.AjoConstants.ACTION)
                            .equalsIgnoreCase(NotificationConstants.AjoConstants.DISMISS);
                    if (navigationResult.isOk() && actionDismiss) view.close();
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }
}
