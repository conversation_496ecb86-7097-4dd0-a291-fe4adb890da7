package za.co.nedbank.ui.view.home.non_tp_client.non_tp_dashboard;

import static za.co.nedbank.core.data.storage.StorageKeys.CLIENT_TYPE;
import static za.co.nedbank.core.feature.FeatureConstants.DynamicToggle.FTR_SBS_ACCOUNT_OPENING;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_NTF;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_BIRTH_DATE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_CLIENT_TYPE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.APPLY_TYPE_CIS_NUMBER_PARAM;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.APPLY_TYPE_ID_NUMBER_PARAM;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.IS_PRE_LOGIN_SBS_APPLY_PARAM;

import android.text.TextUtils;
import android.util.Log;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.data.networking.client.ResultErrorCodes;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.data.storage.StorageUtility;
import za.co.nedbank.core.domain.GetNonTpUserDetailUseCase;
import za.co.nedbank.core.domain.model.Permission;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.CheckPermissionGrantedUseCase;
import za.co.nedbank.core.domain.usecase.GetPreferredNameUseCase;
import za.co.nedbank.core.domain.usecase.app_personalisation.GetPersonalisationInfoUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.domain.usecase.investmentonline.GetFicaStatusUseCase;
import za.co.nedbank.core.domain.usecase.media_content.AvoMediaContentUseCase;
import za.co.nedbank.core.domain.usecase.notifications.GetFBNotificationsCountExtendedUseCase;
import za.co.nedbank.core.domain.usecase.preapprovedoffers.GetPreApprovedOfferUseCase;
import za.co.nedbank.core.enumerations.BackgroundImageTypeEnum;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.app_personalisation.mapper.PersonalisationDataModelToViewModelMapper;
import za.co.nedbank.core.view.app_personalisation.model.PersonalisationViewModel;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.preapprovedoffers.PreApprovedOffersDataToViewModelMapper;
import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;
import za.co.nedbank.core.view.model.media_content.ProductStatus;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.domain.usecases.fica.AcquisitionUseCase;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingValue;
import za.co.nedbank.enroll_v2.view.fica.error.FicaErrorHandler;
import za.co.nedbank.enroll_v2.view.mapper.fica.AcquisitionUseCaseRequestViewModelToDataMapper;
import za.co.nedbank.enroll_v2.view.mapper.fica.AcquisitionUseCaseResponseDataToViewModelMapper;
import za.co.nedbank.enroll_v2.view.model.fica.AcquisitionUseCaseRequestViewModel;
import za.co.nedbank.enroll_v2.view.model.fica.AcquisitionUseCaseResponseViewModel;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.CreditHealthTrackingValue;
import za.co.nedbank.services.domain.mapper.PendingAccountsEntityToDataModelMapper;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.usecase.investmentnotices.GetPendingAccountsUsecase;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.ui.domain.usecase.GetAvoWalletDetailsUseCase;
import za.co.nedbank.ui.view.home.non_tp_client.BaseDashboardAccountsPresenter;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class NonTpAccountsPresenter extends BaseDashboardAccountsPresenter<NonTPAccountsView> {

    private final GetPersonalisationInfoUseCase getPersonalisationInfoUseCase;
    private final CheckPermissionGrantedUseCase checkPermissionGrantedUseCase;
    private final PersonalisationDataModelToViewModelMapper personalisationDataModelToViewModelMapper;

    private BackgroundImageTypeEnum mSelectedImageType = BackgroundImageTypeEnum.UNKNOWN;

    @Inject
    public NonTpAccountsPresenter(GetPreferredNameUseCase getPreferredNameUseCase,
                                  GetOverviewUseCase getOverviewUseCase,
                                  NavigationRouter navigationRouter, ErrorHandler errorHandler, Analytics analytics,
                                  FeatureSetController featureSetController, ApplicationStorage applicationStorage,
                                  @Named("memory") ApplicationStorage memoryApplicationStorage, GetFBNotificationsCountExtendedUseCase getFBNotificationsCountExtendedUseCase,
                                  GetNonTpUserDetailUseCase getNonTpUserDetailUseCase, UserDetailDataToViewModelMapper userDetailDataToViewModelMapper,
                                  GetFicaStatusUseCase getFicaStatusUseCase, final FicaErrorHandler ficaErrorHandler, AcquisitionUseCase acquisitionUseCase,
                                  final AcquisitionUseCaseResponseDataToViewModelMapper acquisitionUseCaseResponseDataToViewModelMapper,
                                  final AcquisitionUseCaseRequestViewModelToDataMapper acquisitionUseCaseRequestViewModelToDataMapper,
                                  final GetFedarationListUseCase getFedarationListUseCase,
                                  final GetAvoWalletDetailsUseCase walletDetailsUseCase,
                                  final AFAnalyticsTracker afAnalyticsTracker,
                                  final AvoMediaContentUseCase mediaContentUseCase,
                                  GetPreApprovedOfferUseCase getPreApprovedOffersUseCase,
                                  final GetPendingAccountsUsecase getPendingAccountsUsecase,
                                  final StorageUtility storageUtility,
                                  final PendingAccountsEntityToDataModelMapper pendingAccountsEntitiyToDataModelMapper,
                                  PreApprovedOffersDataToViewModelMapper preApprovedOffersDataToViewModelMapper,
                                  final CheckPermissionGrantedUseCase checkPermissionGrantedUseCase,
                                  final PersonalisationDataModelToViewModelMapper personalisationDataModelToViewModelMapper,
                                  final GetPersonalisationInfoUseCase getPersonalisationInfoUseCase) {
        super(getPreferredNameUseCase, getOverviewUseCase, navigationRouter, errorHandler, analytics, storageUtility, featureSetController,
                applicationStorage, memoryApplicationStorage, getFBNotificationsCountExtendedUseCase, getNonTpUserDetailUseCase,
                userDetailDataToViewModelMapper, getFicaStatusUseCase, ficaErrorHandler, acquisitionUseCase,
                acquisitionUseCaseResponseDataToViewModelMapper, acquisitionUseCaseRequestViewModelToDataMapper,
                getFedarationListUseCase, walletDetailsUseCase, afAnalyticsTracker,pendingAccountsEntitiyToDataModelMapper,getPendingAccountsUsecase , mediaContentUseCase, getPreApprovedOffersUseCase, preApprovedOffersDataToViewModelMapper);
        this.checkPermissionGrantedUseCase = checkPermissionGrantedUseCase;
        this.personalisationDataModelToViewModelMapper = personalisationDataModelToViewModelMapper;
        this.getPersonalisationInfoUseCase = getPersonalisationInfoUseCase;
    }

    @Override
    protected void onBind() {
        super.onBind();
        analytics.sendEvent(StringUtils.EMPTY_STRING, AppTracking.NON_TP_ACCOUNT_SCREEN_LOAD, StringUtils.EMPTY_STRING);
    }

    @Override
    public void onAccountsLoading(boolean loading) {
        if (view != null) {
            ((NonTPAccountsView) view).showAccountsLoading(loading);
        }
    }

    @Override
    public void onAccountsLoaded(Overview overview, boolean isContinue) {
        if (view != null) {
            ((NonTPAccountsView) view).setNonTpAccounts(overview, isContinue);
            ((NonTPAccountsView) view).hasTransactableAccount(checkForAnyTransactableAccount(overview));
        }
    }

    @Override
    public void onAccountsLoadingError(String errorMsg) {
        if (view != null) {
            ((NonTPAccountsView) view).showAccountsLoadingError(errorMsg);
        }
    }


    @Override
    public void onFicaStatusError(String msg) {
        if (view != null) {
            view.showError(msg);
        }
    }

    @Override
    public String getFicaStatusValue() {
        if (view != null) {
            view.getFicaStatus();
        }
        return StringUtils.EMPTY_STRING;
    }

    @Override
    public boolean canTransact() {
        return false;
    }

    @Override
    public void onChatIconCount(int chatIconCount) {
        if (view != null) {
            ((NonTPAccountsView) view).setChatIcon(chatIconCount);
        }
    }

    @Override
    protected void onReceivePreApprovedOffersCount(int notificationCount) {
        if (view != null) {
            view.receiveNotificationCount(notificationCount);
        }
    }

    public ClientType getClientType() {
        String clientType = mApplicationStorage.getString(CLIENT_TYPE, ClientType.TP.getValue());
        return ClientType.getEnum(clientType);
    }

    private boolean checkForAnyTransactableAccount(Overview overview) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.CREDIT_CARDS ||
                        accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.accountSummaries != null) {
                    for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
                        if (!accountSummary.isDormantAccount()) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public void navigateToAvoLifestyleApp() {
        trackAppsFlyerEvent();
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setFeature(AppTracking.VAL_AVO_FEATURE);
        analytics.sendEventActionWithMap(AppTracking.MYACCOUNTS_GO_TO_AVO, cdata);
        getAvoWalletDetails();
    }

    int fetchAccountType(String accountCode) {
        for (za.co.nedbank.core.Constants.ACCOUNT_TYPES account : za.co.nedbank.core.Constants.ACCOUNT_TYPES.values()) {
            if (account.getAccountTypeCode().equalsIgnoreCase(accountCode)) {
                return account.getAccountTypeStringResId();
            }
        }
        return -1;
    }

    public void trackActionAccountProductNGroup(OverviewType overviewType, String accountName, String accountCode, String trackAction, boolean isPocketAccount) {
        if (view != null) {
            String productGroup = ((NonTPAccountsView) view).getOverviewProductGroup(overviewType);
            String product = ((NonTPAccountsView) view).getOverviewAccountType(overviewType, accountName, accountCode, isPocketAccount);

            if (!TextUtils.isEmpty(productGroup) && !TextUtils.isEmpty(product)) {
                String val = productGroup + ";" + product + ";;";

                mMemoryApplicationStorage.putString(StorageKeys.DASHBOARD_SELECTED_PRODUCT_AND_CATEGORY, val);

                HashMap<String, Object> cdata = new HashMap<>();
                AdobeContextData adobeContextData = new AdobeContextData(cdata);
                adobeContextData.setProductCategory(productGroup);
                adobeContextData.setProductAccount(product);
                adobeContextData.setCategoryAndProduct(val);
                adobeContextData.setProductCode(accountCode);
                analytics.sendEventActionWithMap(trackAction, cdata);
            }
        }
    }

    void trackChat() {
        HashMap<String, Object> cdata = new HashMap<>();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_USER_JOURNEY, TrackingParam.VAL_INITIATE_CHAT);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_INTERACTION, TrackingEvent.ANALYTICS.VAL_ONE);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_CHAT, cdata);
    }

    void trackActionMyAccountProductGroup(String trackAction) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeature(TrackingParam.VAL_NOTIFICATIONS);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_CX_SERVICES);
        analytics.sendEventActionWithMap(trackAction, cdata);
    }

    void loadBackgroundImage() {
        checkPermissionGrantedUseCase
                .execute(Permission.READ_EXTERNAL_STORAGE)
                .compose(bindToLifecycle()).flatMap(getPersonalisationInfoUseCase::execute)
                .subscribe(appPersonalisationDataModel -> {
                    if (appPersonalisationDataModel != null && view != null) {
                        PersonalisationViewModel personalisationViewModel = personalisationDataModelToViewModelMapper.mapPersonalisationDataToViewModel(appPersonalisationDataModel);
                        ((NonTPAccountsView) view).loadOverview(personalisationViewModel.getSelectedImageType());

                        if (mSelectedImageType != personalisationViewModel.getSelectedImageType()) {
                            mSelectedImageType = personalisationViewModel.getSelectedImageType();
                            if (personalisationViewModel.getSelectedImageType() == BackgroundImageTypeEnum.CUSTOM) {
                                ((NonTPAccountsView) view).setBackgroundImage(personalisationViewModel.getCustomImagePath());
                            } else {
                                ((NonTPAccountsView) view).setBackgroundImage(personalisationViewModel.getSelectedImageType().bgDrawableId);
                            }
                            ((NonTPAccountsView) view).shouldShowOverlay(mSelectedImageType == BackgroundImageTypeEnum.CUSTOM);
                        }
                    }
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    public void saveShowHideBalance() {
        boolean isBalanceHide = isBalanceHidden();
        mApplicationStorage.putBoolean(StorageKeys.IS_BALANCE_HIDE, !isBalanceHide);
        ((NonTPAccountsView) view).refreshBalances();
    }

    public boolean isBalanceHidden() {
        return mApplicationStorage.getBoolean(StorageKeys.IS_BALANCE_HIDE, false);
    }

    public void moveToAvoDemoSplashScreen() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.AVO_DEMO_SPLASH_SCREEN));
    }

    public void shopDashborad() {
        boolean doNotShowAgain = mApplicationStorage.getBoolean(StorageKeys.DATA_USAGE_NEVER_ASK_AGAIN_WIDGET_SHOP, false);

        if (doNotShowAgain || featureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_DATA_USAGE_SCREEN)) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHOP_DASHBOARD));
            return;
        }
        navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.DATA_USAGE_WARNING)
                        .withParam(za.co.nedbank.core.Constants.FROM_SCREEN, za.co.nedbank.core.Constants.FROM_SHOP_WIDGET)
                        .withParam(za.co.nedbank.core.Constants.COMPLETE_SCREEN_LABEL, ((NonTPAccountsView) view).getActivityLabel()))
                .subscribe(navigationResult -> {
                    boolean accepted = navigationResult.getBooleanParam(NavigationTarget.RESULT_DATA_USAGE_WARNING_ACCEPT);
                    if (accepted) {
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHOP_DASHBOARD));
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void trackActionForShopWidget() {
        HashMap<String, Object> cData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.setFeature(TrackingParam.VAL_AVO_SHOP);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_WIDGET_SHOP, cData);
    }

    public int getOverviewPosition(List<AccountsOverview> accountsOverviews, OverviewType overviewType) {
        if (overviewType != null && accountsOverviews != null && !accountsOverviews.isEmpty()) {
            for (AccountsOverview overview :
                    accountsOverviews) {
                if (overview.overviewType == overviewType) {
                    return accountsOverviews.indexOf(overview);
                }
            }
        }
        return 0;
    }

    public boolean isDynamicContentToggleEnabled(){
       return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_CONTENT_FEATURES);
    }
    public boolean isNonTpWidgetFeatureEnabled() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.NON_TP_WIDGETS_FEATURE);
    }

    public void trackFinancialWellnessPageLoad() {
        if (!mApplicationStorage.getBoolean(StorageKeys.IS_FINANCIAL_WELLNESS_PAGE_LOAD_TRACKED, false)) {
            analytics.sendState(CreditHealthTrackingValue.PAGE_LOAD_FINANCIAL_WELLNESS);
            mApplicationStorage.putBoolean(StorageKeys.IS_FINANCIAL_WELLNESS_PAGE_LOAD_TRACKED, true);
        }
    }

    void navigateToProductCardDetail(AppLayoutViewModel viewModel) {
        sendAnalyticsForInvestment();
        ProductStatus mDisableState = new ProductStatus();
        mDisableState.setDisable(true);
        viewModel.getProductOffer().setFinancial(mDisableState);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NGI_GET_STARTED)
                .withParam(PARAM_NTF, true)
                .withParam(PARAM_ONIA_CLIENT_TYPE, "51924")
                .withParam(PARAM_ONIA_BIRTH_DATE, "0")
                .withParam(PARAM_ONIA_SEC_OFFICER_CD, "52")
                .withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, "0"));
    }

    void navigateToBankLayout() {
        sendAnalyticsForBankAccounts();
        if (featureSetController.isFeatureDisabled(FTR_SBS_ACCOUNT_OPENING)) {
            mMemoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, StringUtils.EMPTY_STRING);
            analytics.sendEvent(StringUtils.EMPTY_STRING, EnrollV2TrackingEvent.EN_3_LANDING_BANK_VIEW, StringUtils.EMPTY_STRING);
            if (mMemoryApplicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) == FicaWorkFlow.IN_APP
                    && featureSetController.isFeatureDisabled(FeatureConstants.EFICA_ENROLLED_FLOW)) {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.BECOME_CLIENT));
            } else if (mMemoryApplicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) == FicaWorkFlow.IN_APP) {
                setSessionId();
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MOA_BASIC_INFORMATION));
            }
        } else {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.APPLY_TYPE)
                    .withParam(IS_PRE_LOGIN_SBS_APPLY_PARAM, true)
                    .withParam(APPLY_TYPE_CIS_NUMBER_PARAM, cisNumber)
                    .withParam(APPLY_TYPE_ID_NUMBER_PARAM, idOrTaxIdNo));
        }
    }

    void setSessionId() {
        AcquisitionUseCaseRequestViewModel acquisitionUseCaseRequestViewModel = new AcquisitionUseCaseRequestViewModel(cisNumber);
        acquisitionUseCase.execute(acquisitionUseCaseRequestViewModelToDataMapper.mapData(acquisitionUseCaseRequestViewModel))
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (null != view) {
                        ((NonTPAccountsView) view).showAccountsLoading(true);
                    }
                }).doOnTerminate(() -> {
                    if (null != view) {
                        ((NonTPAccountsView) view).showAccountsLoading(false);
                    }
                }).map(acquisitionUseCaseResponseDataToViewModelMapper::mapData)
                .subscribe(acquisitionUseCaseResponseViewModel -> {
                    if (acquisitionUseCaseResponseViewModel != null
                            && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel()
                            && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData()
                            && !acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().isEmpty()
                            && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0)
                            && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail()
                            && !acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail().isEmpty()
                            && (acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail().get(0)
                            .getResult().equalsIgnoreCase(ResultErrorCodes.VALID_R_RESULT) ||
                            acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail().get(0)
                                    .getResult().equalsIgnoreCase(ResultErrorCodes.DATA_ITEM_ALREADY_SET))
                            && acquisitionUseCaseResponseViewModel.getDataEntity() != null
                            && acquisitionUseCaseResponseViewModel.getDataEntity().getSessionId() != null) {
                        handleAcquisitionDataAvailableScenario(acquisitionUseCaseResponseViewModel);
                    } else {
                        handleErrorScenario(acquisitionUseCaseResponseViewModel);
                    }
                }, this::handleException);
    }

    private void handleErrorScenario(AcquisitionUseCaseResponseViewModel acquisitionUseCaseResponseViewModel) {
        if (acquisitionUseCaseResponseViewModel != null
                && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel()
                && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData()
                && !acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().isEmpty()
                && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0)
                && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail()) {
            Throwable throwable = new Throwable(acquisitionUseCaseResponseViewModel.getMetaDataViewModel()
                    .getResultData().get(0).getResultDetail().get(0)
                    .getResult());
            handleException(throwable);
        }
    }

    private void handleAcquisitionDataAvailableScenario(AcquisitionUseCaseResponseViewModel acquisitionUseCaseResponseViewModel) {
        mMemoryApplicationStorage.putString(StorageKeys.FICA_SESSION_ID, acquisitionUseCaseResponseViewModel.getDataEntity().getSessionId());
        boolean isStudent = false;
        if (idOrTaxIdNo != null) {
            isStudent = isStudent(idOrTaxIdNo.substring(za.co.nedbank.core.Constants.ZERO, za.co.nedbank.core.Constants.DIGIT_COUNT_USER_AGE));
            mMemoryApplicationStorage.putString(StorageKeys.FICA_ID_NUMBER, idOrTaxIdNo);
        }
        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BANK_INTENT)
                .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.IS_STUDENT, isStudent));
    }

    private void handleException(Throwable throwable) {
        ficaErrorHandler.handleError(throwable);
    }

    private boolean isStudent(String number) {
        if (number.isEmpty()) {
            return false;
        }
        Date date = FormattingUtil.getDate(number);
        if (null != date) {
            return AppUtility.isDateInStudentAgeRange(date);
        }
        return false;
    }

    void onClickApplyLoan(String flowJourneyFlag, boolean hasNedbankID) {
        sendAnalyticsForApplyLoan();
        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BORROW_INTENT)
                .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_JOURNEY_FLAG, flowJourneyFlag)
                .withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.IS_NON_NEDBANK_ID_FLOW, hasNedbankID));
    }

    private void sendAnalyticsForApplyLoan() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setClientType(TrackingEvent.ANALYTICS.VALUE_NTF);
        adobeContextData.setEntryPoint(TrackingParam.VAL_APP_APPLY_APP_REGISTRATION);
        adobeContextData.setFeature(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_ON_BOARDING);
        adobeContextData.setFeatureCategory(EnrollV2TrackingValue.ANALYTICS.VAL_SALES_AND_ON_BOARDING);
        adobeContextData.setProductCategory(EnrollV2TrackingValue.ANALYTICS.KEY_PRODUCT_GROUP_LOAN_OR_CREDIT);
        analytics.sendEventActionWithMap(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_SELECTED, cdata);
    }

    public void trackEyeIconAnalytics(String eventName) {
        analytics.sendEvent(eventName, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    public void sendAnalyticsForInvestment() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setClientType(TrackingEvent.ANALYTICS.VALUE_NTF);
        adobeContextData.setEntryPoint(TrackingParam.VAL_APP_APPLY_APP_REGISTRATION);
        adobeContextData.setFeature(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_ON_BOARDING);
        adobeContextData.setFeatureCategory(EnrollV2TrackingValue.ANALYTICS.VAL_SALES_AND_ON_BOARDING);
        adobeContextData.setProduct(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_GROUP_INVESTMENT);
        adobeContextData.setProductCategory(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_GROUP_INVESTMENT);
        adobeContextData.setScreenName(EnrollV2TrackingEvent.ANALYTICS.NEDBANK_MONEY);
        analytics.sendEventActionWithMap(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_SELECTED, cdata);
    }

    private void sendAnalyticsForBankAccounts() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setEntryPoint(TrackingParam.VAL_APP_APPLY_APP_REGISTRATION);
        adobeContextData.setCategoryAndProduct(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_BANK + ";;;");
        adobeContextData.setClientType(TrackingEvent.ANALYTICS.VALUE_NTF);
        adobeContextData.setProductCategory(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_BANK);
        adobeContextData.setFeature(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_ON_BOARDING);
        adobeContextData.setFeatureCategory(EnrollV2TrackingValue.ANALYTICS.VAL_SALES_AND_ON_BOARDING);
        analytics.sendEventActionWithMap(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_SELECTED, cdata);
    }
}
