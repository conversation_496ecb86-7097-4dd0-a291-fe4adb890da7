package za.co.nedbank.ui.view.ita.ita_authentication;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.domain.usecase.ApproveTMCUseCase;
import za.co.nedbank.core.domain.usecase.GetEmcertIdUseCase;
import za.co.nedbank.core.domain.usecase.GetStartupStatusUseCase;
import za.co.nedbank.core.domain.usecase.RefreshUserUseCase;
import za.co.nedbank.core.domain.usecase.RetrieveSettingsUseCase;
import za.co.nedbank.core.domain.usecase.enrol.FetchProfileJwtUseCase;
import za.co.nedbank.core.domain.usecase.enrol.login.ShouldShowTnCUseCase;
import za.co.nedbank.core.domain.usecase.enrol.login.ValidateTouchIdUseCase;
import za.co.nedbank.core.domain.usecase.fingerprint.GetUseFingerPrintSettingUseCase;
import za.co.nedbank.core.domain.usecase.fingerprint.IsFingerPrintSignatureExistUseCase;
import za.co.nedbank.core.enroll.view.authentication.ScanPayAuthBasePresenter;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.errors.ErrorProvider;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.nid_sdk.main.domain.model.fingerprint.SetTouchIDStatus;

public class ITAAuthPresenter extends ScanPayAuthBasePresenter<ITAAuthView> {
    private static final String TAG = ITAAuthPresenter.class.getSimpleName();

    private final NavigationRouter mNavigationRouter;
    private final GetUseFingerPrintSettingUseCase mGetUseFingerPrintSettingUseCase;
    private final IsFingerPrintSignatureExistUseCase mIsFingerPrintSignatureExistUseCase;
    private final ValidateTouchIdUseCase mValidateTouchIdUseCase;
    private int mVerificationFailedCount;
    private static final int MAX_NUMBER_OF_ATTEMPT = 3;

    @Inject
    ITAAuthPresenter(final NavigationRouter navigationRouter,
                     final GetUseFingerPrintSettingUseCase getUseFingerPrintSettingUseCase,
                     final IsFingerPrintSignatureExistUseCase isFingerPrintSignatureExistUseCase,
                     final ValidateTouchIdUseCase validateTouchIdUseCase,
                     final ShouldShowTnCUseCase shouldShowTMCUseCase,
                     final RefreshUserUseCase refreshUserUseCase,
                     final ApproveTMCUseCase approveTMCUseCase,
                     final ErrorHandler errorHandler,
                     final ErrorProvider errorProvider,
                     final FetchProfileJwtUseCase fetchProfileJwtUseCase,
                     final Analytics analytics,
                     final RetrieveSettingsUseCase retrieveSettingsUseCase,
                     final GetEmcertIdUseCase getEmcertUseCase,
                     final GetStartupStatusUseCase getStartupStatusUseCase) {
        super(shouldShowTMCUseCase, refreshUserUseCase, approveTMCUseCase, errorHandler, errorProvider, fetchProfileJwtUseCase, analytics, retrieveSettingsUseCase
                , getEmcertUseCase, getStartupStatusUseCase);
        this.mNavigationRouter = navigationRouter;
        this.mGetUseFingerPrintSettingUseCase = getUseFingerPrintSettingUseCase;
        this.mIsFingerPrintSignatureExistUseCase = isFingerPrintSignatureExistUseCase;
        this.mValidateTouchIdUseCase = validateTouchIdUseCase;
    }

    public void checkFPAuthentication(int applyOption, String fromScreen) {
        mGetUseFingerPrintSettingUseCase
                .execute()
                .subscribe(isFingerPrintEnabled -> {
                    if (Boolean.TRUE.equals(isFingerPrintEnabled) && APIInformation.getInstance().isFederated()) {
                        checkForFPSignature(applyOption, fromScreen);
                    } else {
                        if (view != null) {
                            view.isFingerPrintSettingEnabled(false, applyOption, fromScreen);
                        }
                    }
                }, throwable -> NBLogger.d(TAG, throwable.getMessage()));
    }

    public void checkForFPSignature(int applyOption, String fromScreen) {
        mIsFingerPrintSignatureExistUseCase
                .execute()
                .subscribe(isSignatureExist -> {
                    if (view != null) {
                        view.isFingerPrintSettingEnabled(isSignatureExist, applyOption, fromScreen);
                    }
                }, throwable -> NBLogger.d(TAG, throwable.getMessage()));
    }

    public void handleFPDetectSuccess() {
        if (view != null) {
            view.showProgressVisible(true);
        }
        mValidateTouchIdUseCase
                .execute()
                .subscribe(validateTouchIdResult -> {
                    if (validateTouchIdResult != null && validateTouchIdResult.status == SetTouchIDStatus.TOUCH_VALIDATED) {
                        resetVerificationFailureCount();
                        NBLogger.d(TAG, "handle FingerPrint Detect Success - > validateTouchIdUseCase - >success");
                        refreshToken(validateTouchIdResult.pinToken);

                    } else {
                        NBLogger.d(TAG, "handle FingerPrint DetectSuccess - > validateTouchIdUseCase - >Failure");
                        handleValidationServiceFailed("", true);
                    }
                }, throwable -> {
                    String errorMsg = errorHandler.getErrorMessage(throwable).getMessage();
                    handleValidationServiceFailed(errorMsg, false);
                });
    }

    private void setResult() {
        if (view != null) {
            view.returnResult();
        }
    }

    private void handleValidationServiceFailed(String errorMsg, boolean isValidationFailed) {
        mVerificationFailedCount++;
        if (mVerificationFailedCount >= MAX_NUMBER_OF_ATTEMPT) {
            if (view != null) {
                if (isValidationFailed) {
                    view.showRegisterTouchIdError();
                } else {
                    view.showError(errorMsg);
                }
            }
        } else {
            handleFPDetectSuccess();
        }
    }

    private void resetVerificationFailureCount() {
        mVerificationFailedCount = 0;
    }

    public void pinPasswordLogin(String fromScreen) {
        mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.SCAN_PAY_AUTHENTICATION)
                .withParam(Constants.IS_FROM_APPLY_FLOW, true)
                .withParam(Constants.FROM_SCREEN, fromScreen))
                .subscribe(navigationResult -> {
                    if (null != navigationResult && navigationResult.isOk()) {
                        setResult();
                    }
                });
    }

}

