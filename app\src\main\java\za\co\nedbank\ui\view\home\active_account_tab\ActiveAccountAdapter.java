package za.co.nedbank.ui.view.home.active_account_tab;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.payment.databinding.TaxCertificateActiveAccountBinding;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.uisdk.component.CompatTextView;

public class ActiveAccountAdapter extends RecyclerView.Adapter<ActiveAccountAdapter.DataViewHolder> {
    private final ActiveAccountAdapter.IViewHolderInteraction mIViewHolderInteraction;
    private List<AccountSummary> accountSummaries;

    ActiveAccountAdapter(ActiveAccountAdapter.IViewHolderInteraction mIViewHolderInteraction, List<AccountSummary> accountSummaries) {
        this.mIViewHolderInteraction = mIViewHolderInteraction;
        this.accountSummaries = accountSummaries;
    }

    @NonNull
    @Override
    public ActiveAccountAdapter.DataViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        TaxCertificateActiveAccountBinding binding = TaxCertificateActiveAccountBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ActiveAccountAdapter.DataViewHolder(binding, mIViewHolderInteraction);
    }


    @Override
    public void onBindViewHolder(@NonNull ActiveAccountAdapter.DataViewHolder holder, int position) {
        if (CollectionUtils.isNotEmpty(accountSummaries) && accountSummaries.get(position) != null) {
            holder.invest_type.setText(accountSummaries.get(position).getName());
            holder.account.setText(accountSummaries.get(position).getNumber());
            holder.activeAccountContainer.setOnClickListener(v -> holder.handleParentViewClick());
        }
    }


    @Override
    public int getItemCount() {
        return accountSummaries.size();
    }

    public class DataViewHolder extends RecyclerView.ViewHolder {
        IViewHolderInteraction iViewHolderInteraction;
        CompatTextView invest_type;
        CompatTextView account;
        LinearLayout activeAccountContainer;

        public DataViewHolder(@NonNull TaxCertificateActiveAccountBinding binding, IViewHolderInteraction iViewHolderInteraction) {
            super(binding.getRoot());
            this.iViewHolderInteraction = iViewHolderInteraction;
            invest_type = binding.investType;
            account = binding.account;
            activeAccountContainer = binding.activeAccountContainer;
        }

        void handleParentViewClick() {
            int currentPosition = getAdapterPosition();
            if (iViewHolderInteraction != null) {
                iViewHolderInteraction.onAccountSelected(accountSummaries.get(currentPosition));
            }
        }
    }


    interface IViewHolderInteraction {
        void onAccountSelected(AccountSummary accountSummary);
    }
}
