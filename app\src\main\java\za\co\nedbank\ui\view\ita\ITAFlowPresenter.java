package za.co.nedbank.ui.view.ita;

import android.content.Context;

import com.entersekt.sdk.Auth;
import com.entersekt.sdk.Button;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.disposables.CompositeDisposable;
import za.co.nedbank.R;
import za.co.nedbank.core.AuthManager;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.concierge.chat.GlobalEventBus;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.ita.ITAAuthDataModel;
import za.co.nedbank.core.domain.model.ita.ITAFlowEvent;
import za.co.nedbank.core.domain.model.ita.ITAFlowType;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetStartupStatusUseCase;
import za.co.nedbank.core.domain.usecase.InitializeNidSDKUsecase;
import za.co.nedbank.core.domain.usecase.LogoutUserUseCase;
import za.co.nedbank.core.domain.usecase.enrol.login.SendAuthUseCase;
import za.co.nedbank.core.domain.usecase.ita.ITAAuthUseCase;
import za.co.nedbank.core.domain.usecase.ita.ITATimeoutCounterUseCase;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.nid_sdk.base.Constants.ITA_AUTH_ROLE;
import za.co.nedbank.nid_sdk.main.interaction.ita.ITAFlows;
import za.co.nedbank.nid_sdk.main.interaction.model.AcknowledgeDto;
import za.co.nedbank.uisdk.widget.NBSnackbar;

public class ITAFlowPresenter extends NBBasePresenter<ITAFlowView> {

    private final NavigationRouter navigationRouter;
    private final LogoutUserUseCase logoutUserUseCase;
    private final ITAAuthUseCase itaAuthUseCase;
    private final SendAuthUseCase sendAuthUseCase;
    private final ITATimeoutCounterUseCase countingUseCase;
    private final ITAFlows itaFlows;
    private final APIInformation apiInformation;
    private final ApplicationStorage applicationStorage;
    private final ApplicationStorage memoryApplicationStorage;
    private boolean stopCounter;

    private final GetStartupStatusUseCase getStartupStatusUseCase;
    private final InitializeNidSDKUsecase initializeNidSDKUsecase;

    private final Context context;
    private final CompositeDisposable disposables;

    @Inject
    ITAFlowPresenter(
            final NavigationRouter navigationRouter,
            final LogoutUserUseCase logoutUserUseCase,
            final ITAAuthUseCase itaAuthUseCase,
            final SendAuthUseCase sendAuthUseCase,
            final ITATimeoutCounterUseCase countingUseCase,
            final ITAFlows itaFlows,
            final APIInformation apiInformation,
            final ApplicationStorage applicationStorage,
            @Named("memory") final ApplicationStorage memoryApplicationStorage,
            final GetStartupStatusUseCase getStartupStatusUseCase,
            final InitializeNidSDKUsecase initializeNidSDKUsecase,
            final Context context,
            final CompositeDisposable compositeDisposable
    ) {
        this.navigationRouter = navigationRouter;
        this.logoutUserUseCase = logoutUserUseCase;
        this.itaAuthUseCase = itaAuthUseCase;
        this.sendAuthUseCase = sendAuthUseCase;
        this.countingUseCase = countingUseCase;
        this.itaFlows = itaFlows;
        this.apiInformation = apiInformation;
        this.applicationStorage = applicationStorage;
        this.memoryApplicationStorage = memoryApplicationStorage;
        this.getStartupStatusUseCase=getStartupStatusUseCase;
        this.initializeNidSDKUsecase = initializeNidSDKUsecase;
        this.context=context;
        this.disposables=compositeDisposable;
    }

    public void setPaymentViaTrustedDevice(boolean status) {
        applicationStorage.putBoolean(StorageKeys.IS_PAYMENT_VIA_TRUSTED_DEVICE, status);
    }

    public boolean isPaymentViaTrustedDevice() {
        return applicationStorage.getBoolean(StorageKeys.IS_PAYMENT_VIA_TRUSTED_DEVICE, false);
    }

    public void handleAuthIfAny() {
        Auth auth = view.getAuth();
        if (auth != null) {
            memoryApplicationStorage.putBoolean(auth.getId(), true);
        }
        if (auth != null) {
            startCountdownTimer(auth);
            view.setFlowForLoggedOut(apiInformation.isLoggedOut());
            itaAuthUseCase.execute(new ITAAuthDataModel(ITAFlowType.ACTION, auth))
                    .compose(bindToLifecycle())
                    .subscribe(this::handleITAActionResponse, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        } else {
            view.close();
        }
    }

    void handleITAActionResponse(AcknowledgeDto acknowledgeDto) {
        String actionType = acknowledgeDto.getActionType();
        view.setITAActionType(actionType);
        if (actionType == null)
            actionType = ITA_AUTH_ROLE.DECLINE;
        switch (actionType) {
            case ITA_AUTH_ROLE.REPORT:
            case ITA_AUTH_ROLE.APPROVE:
                checkForLogin();
                break;
            case ITA_AUTH_ROLE.DECLINE:
                sendAuth();
                break;
            case ITA_AUTH_ROLE.TIMEOUT:
                handleITATimeout();
                break;
            default:
                showError();
                break;
        }
        AuthManager.getInstance().authDone();
    }

    public void checkForLogin() {
        if (apiInformation.isLoggedOut()) {
            getStartupStatusUseCase
                    .execute()
                    .compose(bindToLifecycle())
                    .subscribe(startupStatusDto -> navigateToLogin(), throwable -> {
                        if (throwable instanceof NullPointerException) {
                            getStartUpStatus();
                        }
                    });
        } else {
            sendAuth();
        }
    }

    private void getStartUpStatus() {
        initializeNidSDKUsecase.execute(view.getTransaktConfig())
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (disposables != null) {
                        disposables.add(disposable);
                    }
                })
                .subscribe(startupStatusDto -> navigateToLogin(), throwable -> showError());
    }

    public void navigateToLogin() {
        navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.TARGET_ITA_AUTHENTICATION_SCREEN)
                .withParam(Constants.FROM_SCREEN, Constants.FROM_ITA_FLOW))
                .subscribe(navigationResult -> {
                    if (navigationResult.isOk()) {
                        sendAuth();
                    } else {
                        showError();
                    }
                }, throwable -> showError());
    }

    private void sendAuth() {
        Auth auth = itaFlows.getCurrentITAAuth();
        if (auth != null) {
            Auth updatedAuth = getUpdatedAuthWithAction(auth);
            sendAuthUseCase.execute(updatedAuth)
                    .compose(bindToLifecycle())
                    .subscribe(isSuccess -> handleSendAuthResponse(isSuccess, updatedAuth),
                            throwable -> showError());
        } else {
            showError();
        }
    }

    private void handleSendAuthResponse(boolean isSuccess, Auth updatedAuth) {
        boolean isPaymentViaTrustedDevice = isPaymentViaTrustedDevice();
        stopCounter = true;
        if (Boolean.TRUE.equals(isSuccess)) {
            AuthManager.getInstance().authDone();
            if (isPaymentViaTrustedDevice) {
                view.setITAActionType(null);
                view.close();
            } else {
                handleITACompletionFlow(updatedAuth);
            }
        } else {
            if (isPaymentViaTrustedDevice) {
                view.close();
            } else {
                showError();
            }
        }
    }

    public Auth getUpdatedAuthWithAction(Auth auth) {
        List<Button> authButtons = auth.getButtons();
        if (authButtons != null) {
            String action = view.getITAActionType();
            for (int i = 0; i < authButtons.size(); i++) {
                if (authButtons.get(i).getRole().equalsIgnoreCase(action))
                    authButtons.get(i).select();
            }
        }
        return auth;
    }

    public void handleITACompletionFlow(Auth auth) {
        ITAAuthDataModel itaAuthDataModel = new ITAAuthDataModel(ITAFlowType.AUTH, auth);
        itaAuthDataModel.setRole(view.getITAActionType());
        itaAuthUseCase.execute(itaAuthDataModel)
                .compose(bindToLifecycle())
                .subscribe(acknowledgeDto -> {
                    view.setITAActionType(null);
                    if (view.isFlowForLoggedOut()) {
                        doLogout();
                    } else {
                        view.close();
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        AuthManager.getInstance().authDone();
    }

    public void handleITATimeout() {
        stopCounter = true;
        if (isPaymentViaTrustedDevice()) {
            view.setITAActionType(null);
            view.close();
        } else {
            ITAAuthDataModel itaAuthDataModel = new ITAAuthDataModel(ITAFlowType.TIMEOUT, null);
            itaAuthUseCase.execute(itaAuthDataModel)
                    .compose(bindToLifecycle())
                    .subscribe(acknowledgeDto -> {
                        view.setITAActionType(null);
                        view.close();
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
        AuthManager.getInstance().authDone();
    }

    public void doLogout() {
        logoutUserUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(aBoolean -> view.close()
                        , err -> view.close());
    }

    public void startCountdownTimer(Auth auth) {
        if (auth != null) {
            int diffInSeconds = view.getTimeDifference(auth);
            if (diffInSeconds > 60 || diffInSeconds<0) {
                view.showHideCountdownTimer(Boolean.FALSE);
            } else {
                countingUseCase.execute(auth.getTimestamp())
                        .compose(bindToLifecycle())
                        .doOnNext(counter -> {
                            if (view != null) view.showCountingProgress(counter);
                            if (counter == 0 && !stopCounter) {
                                stopCounter = true;
                                handleITATimeout();
                            }
                        }).takeUntil(val -> this.stopCounter).subscribe();
                view.showHideCountdownTimer(Boolean.TRUE);
            }
        }
    }

    private void showError() {
        stopCounter = true;
        view.showError(
                view.getString(R.string.ita_processing_error_title),
                view.getString(R.string.ita_processing_error_msg),
                view.getString(R.string.ita_processing_error_action_dismiss),
                NBSnackbar.FOREVER, () -> {
                    if (view != null) {
                        view.close();
                    }
                });
    }

    public void setFlowStatus(boolean status) {
        GlobalEventBus.getBus().post(new ITAFlowEvent(status));
    }

    public String getFBToken() {
        return applicationStorage.getString(NotificationConstants.STORAGE_KEYS.FB_TOKEN, "");
    }

    public String getHMSToken() {
        return applicationStorage.getString(NotificationConstants.STORAGE_KEYS.HMS_TOKEN, "");
    }
}
