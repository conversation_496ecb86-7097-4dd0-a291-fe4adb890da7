/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.entity.money_request;

import com.squareup.moshi.Json;

import java.util.List;

import za.co.nedbank.core.data.metadata.MetaDataEntity;


/**
 * Created by swapnil.gawande on 2/23/2018.
 */

public class MoneyRequestsMainResponseEntity {
    @Json(name = "data")
    private List<MoneyRequestsResponseEntity> moneyRequestsResponseEntities;
    @Json(name = "metadata")
    private MetaDataEntity metaDataEntity;

    public List<MoneyRequestsResponseEntity> getMoneyRequestsResponseEntities() {
        return moneyRequestsResponseEntities;
    }

    public MetaDataEntity getMetaDataEntity() {
        return metaDataEntity;
    }

}
