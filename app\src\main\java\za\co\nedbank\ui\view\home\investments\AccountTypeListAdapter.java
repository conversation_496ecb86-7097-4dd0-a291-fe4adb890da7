package za.co.nedbank.ui.view.home.investments;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.lang.ref.WeakReference;

import za.co.nedbank.core.R;
import za.co.nedbank.core.base.adapter.BaseAdapter;
import za.co.nedbank.core.databinding.AccountSummeryRowBinding;
import za.co.nedbank.core.databinding.AccountSummeryRowHeaderBinding;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.databinding.HomeWidgetBinding;
import za.co.nedbank.services.domain.model.account.AccountSummary;

public class AccountTypeListAdapter extends BaseAdapter<AccountSummary> {
    private WeakReference<AccountTypeRowInterface> rowInterface;
    private Context mContext;

    public AccountTypeListAdapter(Context context) {
        super(context);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        mContext=parent.getContext();
        AccountSummeryRowBinding binding = AccountSummeryRowBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new AccountTypeInvestmentViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, AccountSummary model, int position) {
            ((AccountTypeInvestmentViewHolder)holder).bind(model, rowInterface, position,items,mContext);
    }

    public void setSelectedRowInterface(AccountTypeRowInterface accountTypeInvestmentActivity) {
        this.rowInterface = new WeakReference<>(accountTypeInvestmentActivity);
    }
}
