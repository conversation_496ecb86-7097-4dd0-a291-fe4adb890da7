package za.co.nedbank.ui.view.mapper;

import java.util.ArrayList;

import javax.inject.Inject;

import za.co.nedbank.ui.domain.model.closed_account.ClosedAccountDataModel;
import za.co.nedbank.ui.domain.model.closed_account.ClosedAccountResponseData;
import za.co.nedbank.ui.view.model.ClosedAccount;
import za.co.nedbank.ui.view.model.ClosedAccountResponseViewModel;

public class ClosedAccountResponseDataToViewModelMapper {

    @Inject
    ClosedAccountResponseDataToViewModelMapper() {
    }

    public ClosedAccountResponseViewModel mapData(ClosedAccountResponseData closedaccountResponseData) {
        ClosedAccountResponseViewModel closedaccountResponseViewModel = new ClosedAccountResponseViewModel();
        closedaccountResponseViewModel.setMetaDataModel(closedaccountResponseData.getMetaDataModel());
        ArrayList<ClosedAccount> closedAccounts = new ArrayList<>();
        if (closedaccountResponseData != null && closedaccountResponseData.getData() != null && closedaccountResponseData.getData().size() > 0) {
            for (ClosedAccountDataModel closedAccountDataModel : closedaccountResponseData.getData()) {
                closedAccounts.add(mapClosedAccountDataModelToClosedAccount(closedAccountDataModel));
            }
            closedaccountResponseViewModel.setData(closedAccounts);
        }
        return closedaccountResponseViewModel;
    }

    private ClosedAccount mapClosedAccountDataModelToClosedAccount(ClosedAccountDataModel closedAccountDataModel) {
        ClosedAccount closedAccount = new ClosedAccount();
        if (closedAccountDataModel != null) {
            closedAccount.setAccountNumber(closedAccountDataModel.getAccountNumber());
            closedAccount.setInvestorNumber(closedAccountDataModel.getInvestorNumber());
            closedAccount.setAccountName(closedAccountDataModel.getAccountName());
        }
        return closedAccount;
    }

}