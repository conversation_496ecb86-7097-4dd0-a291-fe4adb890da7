package za.co.nedbank.services.insurance.view.vvap_policy_admin.payment_day

import io.reactivex.Observable
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.any
import org.mockito.Mockito.atLeastOnce
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.eq
import org.mockito.Mockito.isNull
import org.mockito.Mockito.never
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.verifyNoMoreInteractions
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import za.co.nedbank.core.errors.Error
import za.co.nedbank.core.errors.ErrorHandler
import za.co.nedbank.core.navigation.NavigationRouter
import za.co.nedbank.core.navigation.NavigationTarget
import za.co.nedbank.core.tracking.Analytics
import za.co.nedbank.core.tracking.InsuranceTrackingEvent
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.tracking.adobe.AdobeContextData
import za.co.nedbank.services.Constants
import za.co.nedbank.services.insurance.domain.data.request.vvaps.claim.policy_detail.PolicyDetailRequestDataModel
import za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyDetailResponseDataModel
import za.co.nedbank.services.insurance.domain.usecase.personal_lines.PLPolicyDetailUseCase
import za.co.nedbank.services.insurance.domain.usecase.vvap.claim.VVAPPolicyDetailUseCase
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.claim.PolicyDetailRequestViewToDataMapper
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.claim.PolicyDetailResponseDataToViewMapper
import za.co.nedbank.services.insurance.view.other.model.request.vvaps.claim.policy_detail.PolicyDetailRequestViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.AmountItemValueViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.AssignedIdentifierViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.BankAccountViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.ContractAmountItemViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.CoverageViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.ExternalIdentifierViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.FinanceViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.FinancialAmountItemViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.FrequencyCodeViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.InsuranceAmountItemViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.InsurerReferencesViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.InterestedPartyPeriodViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.InterestedPartyReferencesViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.ItemAmountViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.LimitAmountViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.LimitViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.OrganizationReferencesViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PaymentViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PersonalVehicleItemSectionViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicyBillingViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicyDetailResponseViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicyInquiryViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicyInterestedPartyReferenceViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicyReferencesViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicySectionViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicyVehicleViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicyViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.ResultSetViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.RoleCodeViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.TypeCodeViewModel
import za.co.nedbank.services.insurance.view.vvap.policy_admin.payment_day.payment_day_details.VVAPPaymentDayPresenter
import za.co.nedbank.services.insurance.view.vvap.policy_admin.payment_day.payment_day_details.VVAPPaymentDayView
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget

@RunWith(MockitoJUnitRunner.Silent::class)
class VVAPPaymentDayPresenterTest {
    @InjectMocks
    private val mPresenter: VVAPPaymentDayPresenter? = null

    @Mock
    val mView: VVAPPaymentDayView? = null

    @Mock
    val plPolicyDetailUseCase: PLPolicyDetailUseCase? = null

    @Mock
    private val mNavigationRouter: NavigationRouter? = null

    @Mock
    private val policyDetailUseCase: VVAPPolicyDetailUseCase? = null

    @Mock
    private val requestViewToDataMapper: PolicyDetailRequestViewToDataMapper? = null

    @Mock
    private val responseDataToViewMapper: PolicyDetailResponseDataToViewMapper? = null

    @Mock
    private val mPolicyDetailResponseDataModel: PolicyDetailResponseDataModel? = null

    @Mock
    private val mPolicyDetailResponseViewModel: PolicyDetailResponseViewModel? = null

    @Mock
    private val mPolicyDetailRequestViewModel: PolicyDetailRequestViewModel? = null

    @Mock
    private val mPolicyDetailRequestDataModel: PolicyDetailRequestDataModel? = null

    @Mock
    private val mErrorHandler: ErrorHandler? = null

    @Mock
    private val mThrowable: Throwable? = null

    @Mock
    private val mError: Error? = null

    @Mock
    private val mAnalytics: Analytics? = null

    @Before
    fun setUp() {
        mPresenter!!.bind(mView)
    }

    @Test
    fun getPolicyDetailsTest() {
        val dataModel = getPolicyDetailResponseDataModel()
        `when`(requestViewToDataMapper!!.map(ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java))).thenReturn(
            mPolicyDetailRequestDataModel
        )
        `when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel)).thenReturn(
            Observable.just(
                dataModel
            )
        )

        `when`(responseDataToViewMapper!!.map(dataModel)).thenReturn(
            getPolicyDetailResponseViewModel()
        )

        mPresenter!!.getPaymentDayDetails("123456", true, false, false, false, false, false)
        verify(mView, atLeastOnce())!!.showProgressBar(false)

        verify(mView)!!.setPaymentDate(true, "2020/01/01")
    }

    @Test
    fun getPolicyDetailsFailureTest() {
        `when`(mErrorHandler?.getErrorMessage(mThrowable)).thenReturn(mError)
        `when`(requestViewToDataMapper!!.map(ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java))).thenReturn(
            mPolicyDetailRequestDataModel
        )
        `when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel)).thenReturn(
            Observable.error(
                mThrowable
            )
        )

        mPresenter!!.getPaymentDayDetails("123456", true, false, false, false, false, false)

        verify(mView, atLeastOnce())!!.showProgressBar(false)
        verify(mView, atLeastOnce())!!.showAPIError()
    }

    @Test
    fun getPolicyDetailsFailureInvalidResponseTest() {
        val dataModel = getPolicyDetailResponseDataModel()
        val viewModel = getPolicyDetailResponseViewModel()
        viewModel.resultSet?.resultCode = "R01"
        `when`(requestViewToDataMapper!!.map(ArgumentMatchers.any(PolicyDetailRequestViewModel::class.java))).thenReturn(
            mPolicyDetailRequestDataModel
        )
        `when`(policyDetailUseCase!!.execute(mPolicyDetailRequestDataModel)).thenReturn(
            Observable.just(
                dataModel
            )
        )

        `when`(responseDataToViewMapper!!.map(dataModel)).thenReturn(viewModel)

        mPresenter!!.getPaymentDayDetails("123456", true, false, false, false, false, false)
        verify(mView, atLeastOnce())!!.showProgressBar(false)

        verify(mView, atLeastOnce())!!.showAPIError()
    }

    @Test
    fun paymentFrequencyAPI_callsPLUseCase_whenIsVVAPFlowIsFalse() {
        val policyNumber = "123456"
        val riskSerialNumber = "RISK123"
        val requestModel = mPolicyDetailRequestDataModel
        `when`(requestViewToDataMapper?.map(any())).thenReturn(mPolicyDetailRequestDataModel)
        `when`(plPolicyDetailUseCase?.execute(requestModel)).thenReturn(
            Observable.just(
                mPolicyDetailResponseDataModel
            )
        )

        mPresenter!!.paymentFrequencyAPI(policyNumber, riskSerialNumber, false)

        verify(plPolicyDetailUseCase)?.execute(requestModel)
        verify(policyDetailUseCase, never())?.execute(any())
    }

    @Test
    fun paymentFrequencyAPI_callsVVAPUseCase_whenIsVVAPFlowIsTrue() {
        val policyNumber = "123456"
        val riskSerialNumber = "RISK123"

        `when`(requestViewToDataMapper?.map(any())).thenReturn(mPolicyDetailRequestDataModel)
        `when`(policyDetailUseCase?.execute(mPolicyDetailRequestDataModel)).thenReturn(
            Observable.just(
                mPolicyDetailResponseDataModel
            )
        )

        mPresenter!!.paymentFrequencyAPI(policyNumber, riskSerialNumber, true)

        verify(policyDetailUseCase)?.execute(mPolicyDetailRequestDataModel)
        verify(plPolicyDetailUseCase, never())?.execute(any())
    }

    @Test
    fun paymentFrequencyAPI_showsAPIError_onError() {
        val policyNumber = "123456"
        val riskSerialNumber = "RISK123"
        val throwable = Throwable("API error")
        `when`(requestViewToDataMapper?.map(any())).thenReturn(mPolicyDetailRequestDataModel)
        `when`(policyDetailUseCase?.execute(mPolicyDetailRequestDataModel)).thenReturn(
            Observable.error(
                throwable
            )
        )

        mPresenter!!.paymentFrequencyAPI(policyNumber, riskSerialNumber, true)

        verify(mView)?.showAPIError()
        verify(mView, times(2))?.showProgressBar(false)
    }

    @Test
    fun handlePolicyDetailResponseTest() {
        val dataModel = getPolicyDetailResponseDataModel()
        doReturn(getPolicyDetailResponseViewModel()).`when`(responseDataToViewMapper)!!
            .map(dataModel)
        mPresenter!!.handlePolicyDetailResponse(dataModel)
        verify(mView)!!.setPaymentDate(true, "2020/01/01")
    }

    @Test
    fun handlePolicyDetailResponseFrequencyTest() {
        val dataModel = getPolicyDetailResponseDataModel()
        val viewModel = getPolicyDetailResponseViewModel()
        val frequencyCode =
            FrequencyCodeViewModel()
        frequencyCode.value = InsuranceConstants.ANNUAL_FREQUENCY
        viewModel.policyInquiry?.get(Constants.ZERO)?.policy?.policySection?.policyBilling?.payment?.get(
            Constants.ZERO
        )?.frequencyCode = frequencyCode
        doReturn(viewModel).`when`(responseDataToViewMapper)!!.map(dataModel)
        mPresenter!!.handlePolicyDetailResponse(dataModel)
        verify(mView)!!.setPaymentDate(false, "2020/01/01")
    }

    @Test
    fun handlePolicyDetailResponseInvalidResponseTest() {
        val dataModel = getPolicyDetailResponseDataModel()
        val viewModel = getPolicyDetailResponseViewModel()
        viewModel.resultSet?.resultCode = "R01"
        doReturn(viewModel).`when`(responseDataToViewMapper)!!.map(dataModel)
        mPresenter!!.handlePolicyDetailResponse(dataModel)
        verify(mView!!).showAPIError()
        verify(mView).showProgressBar(false)
    }

    @Test
    fun handlePolicyDetailResponseNullViewTest() {
        mPresenter!!.unbind()
        mPresenter.handlePolicyDetailResponse(mPolicyDetailResponseDataModel!!)
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun handlePolicyDetailResponse_shouldShowAPIError_whenDataIsNull() {
        mPresenter!!.handlePolicyDetailResponse(null)
        verify(mView)!!.showAPIError()
        verify(mView)!!.showProgressBar(false)
    }

    @Test
    fun parseFrequencyAndDate_shouldSetPaymentDateMonthly() {
        val viewModel = getPolicyDetailResponseViewModel()
        viewModel.policyInquiry?.get(Constants.ZERO)
            ?.policy?.policySection?.policyBilling?.payment?.get(Constants.ZERO)
            ?.frequencyCode?.value = InsuranceConstants.MONTHLY_FREQUENCY
        viewModel.policyInquiry?.get(Constants.ZERO)
            ?.policy?.policySection?.policyBilling?.payment?.get(Constants.ZERO)
            ?.paymentDate = "2024/07/01"

        mPresenter!!.parseFrequencyAndDate(viewModel)

        verify(mView)?.setPaymentDate(true, "2024/07/01")
    }

    @Test
    fun parseFrequencyAndDate_shouldSetPaymentDateNonMonthly() {
        val viewModel = getPolicyDetailResponseViewModel()
        viewModel.policyInquiry?.get(Constants.ZERO)
            ?.policy?.policySection?.policyBilling?.payment?.get(Constants.ZERO)
            ?.frequencyCode?.value = InsuranceConstants.ANNUAL_FREQUENCY
        viewModel.policyInquiry?.get(Constants.ZERO)
            ?.policy?.policySection?.policyBilling?.payment?.get(Constants.ZERO)
            ?.paymentDate = "2024/08/01"

        mPresenter!!.parseFrequencyAndDate(viewModel)

        verify(mView)?.setPaymentDate(false, "2024/08/01")
    }

    @Test
    fun parseFrequencyAndDate_shouldHandleNullPayment() {
        val viewModel = getPolicyDetailResponseViewModel()
        viewModel.policyInquiry?.get(Constants.ZERO)
            ?.policy?.policySection?.policyBilling?.payment = null

        mPresenter!!.parseFrequencyAndDate(viewModel)

        verify(mView)?.setPaymentDate(false, null)
    }

    @Test
    fun parseFrequencyAndDate_shouldHandleNullPolicySection() {
        val viewModel = getPolicyDetailResponseViewModel()
        viewModel.policyInquiry?.get(Constants.ZERO)?.policy?.policySection = null

        mPresenter!!.parseFrequencyAndDate(viewModel)

        verify(mView)?.setPaymentDate(false, null)
    }

    @Test
    fun showViewProgressBar_shouldShowProgressBarWithTrue() {
        mPresenter!!.showViewProgressBar(true)
        verify(mView)?.showProgressBar(true)
    }

    @Test
    fun showViewProgressBar_shouldShowProgressBarWithFalse() {
        mPresenter!!.showViewProgressBar(false)
        verify(mView)?.showProgressBar(false)
    }

    @Test
    fun showViewProgressBar_shouldNotThrow_whenViewIsNull() {
        mPresenter!!.unbind()
        mPresenter.showViewProgressBar(true)
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun showAPIErrorTest() {
        mPresenter?.showAPIError()
        verify(mView!!).showAPIError()
        verify(mView).showProgressBar(false)
    }

    @Test
    fun showAPIError_shouldNotThrow_whenViewIsNull() {
        mPresenter?.unbind()
        mPresenter?.showAPIError()
        verifyNoMoreInteractions(mView)
    }

    @Test
    fun moveToProductListScreenTest() {
        `when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any())).thenReturn(true)
        mPresenter!!.moveToProductListScreen()
        verify(mNavigationRouter).navigateTo(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_DETAILS_CONTAINER))
        verify(mView)?.close()
    }

    @Test
    fun moveToProductListScreen_shouldCloseView_whenViewIsNotNull() {
        mPresenter!!.moveToProductListScreen()
        verify(mView)?.close()
    }

    @Test
    fun editPaymentDayDetailsTest() {
        `when`(mNavigationRouter!!.navigateTo(ArgumentMatchers.any())).thenReturn(true)
        mPresenter!!.editPaymentDayDetails(
            "p0987665", "2022/01/01",
            InsuranceConstants.AnalyticsConstants.SUBPRODUCT_CSF,
            false, false
        )
        verify(mNavigationRouter).navigateTo(NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_EDIT_PAYMENT_DAY_DETAILS))
    }

    @Test
    fun editPaymentDayDetails_shouldNavigateWithCorrectParams() {
        val policyNumber = "p0987665"
        val paymentDate = "2022/01/01"
        val subProduct = InsuranceConstants.AnalyticsConstants.SUBPRODUCT_CSF
        val isPersonalLine = false
        val isFuneral = false

        `when`(mNavigationRouter!!.navigateTo(any())).thenReturn(true)

        mPresenter!!.editPaymentDayDetails(policyNumber, paymentDate, subProduct, isPersonalLine,isFuneral)

        verify(mNavigationRouter).navigateTo(
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_EDIT_PAYMENT_DAY_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_VVAP_POLICY_NUMBER, policyNumber)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EDIT_PAYMENT_DAY_DETAILS, paymentDate)
                .withParam(InsuranceConstants.ParamKeys.PARAM_VVAP_RISK_SERIAL_NUMBER, "RISK123")
                .withParam(InsuranceConstants.ParamKeys.PARAM_VVAPS_SUB_PRODUCT_NAME, subProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_IS_PL_FLOW, isPersonalLine)
        )
    }

    @Test
    fun editPaymentDayDetails_shouldHandleNullPaymentDateAndSubProduct() {
        val policyNumber = "p0987665"
        val paymentDate: String? = null
        val subProduct: String? = null
        val isPersonalLine = true
        val isFuneral = false

        `when`(mNavigationRouter!!.navigateTo(any())).thenReturn(true)

        mPresenter!!.editPaymentDayDetails(policyNumber, paymentDate, subProduct, isPersonalLine,isFuneral)

        verify(mNavigationRouter).navigateTo(
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_EDIT_PAYMENT_DAY_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_VVAP_POLICY_NUMBER, policyNumber)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EDIT_PAYMENT_DAY_DETAILS, paymentDate)
                .withParam(InsuranceConstants.ParamKeys.PARAM_VVAP_RISK_SERIAL_NUMBER, "RISK999")
                .withParam(InsuranceConstants.ParamKeys.PARAM_VVAPS_SUB_PRODUCT_NAME, subProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_IS_PL_FLOW, isPersonalLine)
        )

    }

    private fun getPolicyDetailResponseDataModel(): PolicyDetailResponseDataModel {
        val policyDetailResponseDataModel = PolicyDetailResponseDataModel()

        var policyInquiryList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyInquiryDataModel>()
        var resultSetDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.ResultSetDataModel()
        resultSetDataModel.resultCode = "R00"
        resultSetDataModel.resultDescription = "Success"

        var policyInquiryDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyInquiryDataModel()

        var policyDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyDataModel()

        var personalVehicleItemSectionDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PersonalVehicleItemSectionDataModel()

        var assignedIdentifierList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.AssignedIdentifierDataModel>()
        var coverageList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.CoverageDataModel>()
        var policyInterestedPartyReferencesList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyInterestedPartyReferenceDataModel>()
        var insuranceAmountItemList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.InsuranceAmountItemDataModel>()


        var assignedIdentifierDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.AssignedIdentifierDataModel()
        var coverageDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.CoverageDataModel()
        var policyInterestedPartyReferenceDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyInterestedPartyReferenceDataModel()
        var typeCodeDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.TypeCodeDataModel()
        typeCodeDataModel.value = ""
        var insuranceAmountItemDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.InsuranceAmountItemDataModel()
        var contractAmountItemList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.ContractAmountItemDataModel>()
        var contractAmountItemDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.ContractAmountItemDataModel()
        var itemAmountDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.ItemAmountDataModel()
        itemAmountDataModel.value = 12.2f
        contractAmountItemDataModel.itemAmount = itemAmountDataModel
        contractAmountItemDataModel.typeCode = typeCodeDataModel
        contractAmountItemList.add(contractAmountItemDataModel)



        insuranceAmountItemDataModel.contractAmountItem = contractAmountItemList
        insuranceAmountItemDataModel.typeCode = typeCodeDataModel


        var interestedPartyReferences =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.InterestedPartyReferencesDataModel()
        var interestedPartyPeriod =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.InterestedPartyPeriodDataModel()

        var organizationReferencesDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.OrganizationReferencesDataModel()


        var externalIdentifierDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.ExternalIdentifierDataModel()

        externalIdentifierDataModel.id = ""
        externalIdentifierDataModel.typeCode = typeCodeDataModel

        var externalIdentifierList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.ExternalIdentifierDataModel>()
        externalIdentifierList.add(externalIdentifierDataModel)

        organizationReferencesDataModel.externalIdentifier = externalIdentifierList

        interestedPartyReferences.organizationReferences = organizationReferencesDataModel
        interestedPartyPeriod.endDate = "22-4-2004"
        interestedPartyPeriod.startDate = "22-4-2004"

        var roleCodeDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.RoleCodeDataModel()
        roleCodeDataModel.value = "abc"

        policyInterestedPartyReferenceDataModel.interestedPartyPeriod = interestedPartyPeriod
        policyInterestedPartyReferenceDataModel.interestedPartyReferences =
            interestedPartyReferences
        policyInterestedPartyReferenceDataModel.roleCode = roleCodeDataModel

        assignedIdentifierDataModel.id = ""
        assignedIdentifierDataModel.roleCode = roleCodeDataModel


        var insurerReferences =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.InsurerReferencesDataModel()
        insurerReferences.externalIdentifier = externalIdentifierList

        var limitDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.LimitDataModel()


        var limitAmountDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.LimitAmountDataModel()
        limitAmountDataModel.itemAmount = itemAmountDataModel

        limitDataModel.limitAmount = limitAmountDataModel
        limitDataModel.typeCode = typeCodeDataModel

        var limitList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.LimitDataModel>()
        limitList.add(limitDataModel)

        var policyReferencesDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyReferencesDataModel()
        policyReferencesDataModel.assignedIdentifier = assignedIdentifierList

        coverageDataModel.effectiveDate = ""
        coverageDataModel.insurerReferences = insurerReferences
        coverageDataModel.limit = limitList
        coverageDataModel.policyReferences = policyReferencesDataModel
        coverageDataModel.typeCode = typeCodeDataModel



        assignedIdentifierList.add(assignedIdentifierDataModel)
        coverageList.add(coverageDataModel)
        policyInterestedPartyReferencesList.add(policyInterestedPartyReferenceDataModel)
        insuranceAmountItemList.add(insuranceAmountItemDataModel)

        personalVehicleItemSectionDataModel.assignedIdentifier = assignedIdentifierList
        personalVehicleItemSectionDataModel.coverage = coverageList
        personalVehicleItemSectionDataModel.insuranceAmountItem = insuranceAmountItemList
        personalVehicleItemSectionDataModel.policyInterestedPartyReferences =
            policyInterestedPartyReferencesList


        var personalVehicleItemSectionList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PersonalVehicleItemSectionDataModel>()
        personalVehicleItemSectionList.add(personalVehicleItemSectionDataModel)

        var policySectionDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicySectionDataModel()

        var policyVehicleDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyVehicleDataModel()
        var financeDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.FinanceDataModel()
        var financialAmountItemList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.FinancialAmountItemDataModel>()
        var amountItemValueList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.AmountItemValueDataModel>()
        var amountItemValueDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.AmountItemValueDataModel()
        amountItemValueDataModel.itemAmount = itemAmountDataModel
        amountItemValueList.add(amountItemValueDataModel)

        var financialAmountItemDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.FinancialAmountItemDataModel()
        financialAmountItemDataModel.typeCode = typeCodeDataModel
        financialAmountItemDataModel.amountItemValue = amountItemValueList
        financialAmountItemList.add(financialAmountItemDataModel)
        financeDataModel.financialAmountItem = financialAmountItemList

        financeDataModel.financeAccountNumberId = "1234"
        financeDataModel.financialAmountItem = financialAmountItemList
        policyVehicleDataModel.finance = financeDataModel

        var policyVehicleList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyVehicleDataModel>()
        policyVehicleList.add(policyVehicleDataModel)
        var policyBillingDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyBillingDataModel()

        var bankAccountList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.BankAccountDataModel>()
        var bankAccountDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.BankAccountDataModel()
        bankAccountDataModel.accountNumberId = "123"
        bankAccountDataModel.bankName = "abcd"
        bankAccountDataModel.routingNumberId = "123"
        bankAccountDataModel.typeCode = "abc"
        bankAccountList.add(bankAccountDataModel)

        var paymentList =
            ArrayList<za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PaymentDataModel>()
        var paymentDataModel =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PaymentDataModel()
        var frequencyCode =
            za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.FrequencyCodeDataModel()
        frequencyCode.value = InsuranceConstants.MONTHLY_FREQUENCY
        paymentDataModel.frequencyCode = frequencyCode
        paymentDataModel.typeCode = typeCodeDataModel
        paymentDataModel.paymentDate = "2020/01/01"
        paymentList.add(paymentDataModel)

        policyBillingDataModel.bankAccountList = bankAccountList
        policyBillingDataModel.payment = paymentList


        policySectionDataModel.policyBilling = policyBillingDataModel
        policySectionDataModel.policyVehicle = policyVehicleList


        policyDataModel.personalVehicleItemSection = personalVehicleItemSectionList
        policyDataModel.policySection = policySectionDataModel


        policyInquiryDataModel.policy = policyDataModel
        policyInquiryList.add(policyInquiryDataModel)


        policyDetailResponseDataModel.policyInquiry = policyInquiryList
        policyDetailResponseDataModel.resultSet = resultSetDataModel

        return policyDetailResponseDataModel
    }

    private fun getPolicyDetailResponseViewModel(): PolicyDetailResponseViewModel {
        val policyDetailResponseViewModel =
            PolicyDetailResponseViewModel()

        val policyInquiryList = ArrayList<PolicyInquiryViewModel>()
        val resultSetViewModel =
            ResultSetViewModel()
        resultSetViewModel.resultCode = "R00"
        resultSetViewModel.resultDescription = "Success"

        val policyInquiryViewModel =
            PolicyInquiryViewModel()

        val policyViewModel =
            PolicyViewModel()

        val personalVehicleItemSectionViewModel =
            PersonalVehicleItemSectionViewModel()

        val assignedIdentifierList = ArrayList<AssignedIdentifierViewModel>()
        val coverageList = ArrayList<CoverageViewModel>()
        val policyInterestedPartyReferencesList =
            ArrayList<PolicyInterestedPartyReferenceViewModel>()
        val insuranceAmountItemList = ArrayList<InsuranceAmountItemViewModel>()


        val assignedIdentifierViewModel =
            AssignedIdentifierViewModel()
        val coverageViewModel =
            CoverageViewModel()
        val policyInterestedPartyReferenceViewModel =
            PolicyInterestedPartyReferenceViewModel()
        val typeCodeViewModel =
            TypeCodeViewModel()
        typeCodeViewModel.value = ""
        val insuranceAmountItemViewModel =
            InsuranceAmountItemViewModel()
        val contractAmountItemList = ArrayList<ContractAmountItemViewModel>()
        val contractAmountItemViewModel =
            ContractAmountItemViewModel()
        val itemAmountViewModel =
            ItemAmountViewModel()
        itemAmountViewModel.value = 12.2f
        contractAmountItemViewModel.itemAmount = itemAmountViewModel
        contractAmountItemViewModel.typeCode = typeCodeViewModel
        contractAmountItemList.add(contractAmountItemViewModel)

        insuranceAmountItemViewModel.contractAmountItem = contractAmountItemList
        insuranceAmountItemViewModel.typeCode = typeCodeViewModel

        val interestedPartyReferences =
            InterestedPartyReferencesViewModel()
        val interestedPartyPeriod =
            InterestedPartyPeriodViewModel()

        val organizationReferencesViewModel =
            OrganizationReferencesViewModel()


        val externalIdentifierViewModel =
            ExternalIdentifierViewModel()

        externalIdentifierViewModel.id = ""
        externalIdentifierViewModel.typeCode = typeCodeViewModel

        val externalIdentifierList = ArrayList<ExternalIdentifierViewModel>()
        externalIdentifierList.add(externalIdentifierViewModel)

        organizationReferencesViewModel.externalIdentifier = externalIdentifierList

        interestedPartyReferences.organizationReferences = organizationReferencesViewModel
        interestedPartyPeriod.endDate = "22-4-2004"
        interestedPartyPeriod.startDate = "22-4-2004"

        val roleCodeViewModel =
            RoleCodeViewModel()
        roleCodeViewModel.value = "abc"

        policyInterestedPartyReferenceViewModel.interestedPartyPeriod = interestedPartyPeriod
        policyInterestedPartyReferenceViewModel.interestedPartyReferences =
            interestedPartyReferences
        policyInterestedPartyReferenceViewModel.roleCode = roleCodeViewModel

        assignedIdentifierViewModel.id = ""
        assignedIdentifierViewModel.roleCode = roleCodeViewModel


        val insurerReferences =
            InsurerReferencesViewModel()
        insurerReferences.externalIdentifier = externalIdentifierList

        val limitViewModel =
            LimitViewModel()


        val limitAmountViewModel =
            LimitAmountViewModel()
        limitAmountViewModel.itemAmount = itemAmountViewModel

        limitViewModel.limitAmount = limitAmountViewModel
        limitViewModel.typeCode = typeCodeViewModel

        val limitList = ArrayList<LimitViewModel>()
        limitList.add(limitViewModel)

        val policyReferencesViewModel =
            PolicyReferencesViewModel()
        policyReferencesViewModel.assignedIdentifier = assignedIdentifierList

        coverageViewModel.effectiveDate = ""
        coverageViewModel.insurerReferences = insurerReferences
        coverageViewModel.limit = limitList
        coverageViewModel.policyReferences = policyReferencesViewModel
        coverageViewModel.typeCode = typeCodeViewModel



        assignedIdentifierList.add(assignedIdentifierViewModel)
        coverageList.add(coverageViewModel)
        policyInterestedPartyReferencesList.add(policyInterestedPartyReferenceViewModel)
        insuranceAmountItemList.add(insuranceAmountItemViewModel)

        personalVehicleItemSectionViewModel.assignedIdentifier = assignedIdentifierList
        personalVehicleItemSectionViewModel.coverage = coverageList
        personalVehicleItemSectionViewModel.insuranceAmountItem = insuranceAmountItemList
        personalVehicleItemSectionViewModel.policyInterestedPartyReferences =
            policyInterestedPartyReferencesList


        val personalVehicleItemSectionList = ArrayList<PersonalVehicleItemSectionViewModel>()
        personalVehicleItemSectionList.add(personalVehicleItemSectionViewModel)

        val policySectionViewModel =
            PolicySectionViewModel()

        val policyVehicleViewModel =
            PolicyVehicleViewModel()
        val financeViewModel =
            FinanceViewModel()
        val financialAmountItemList = ArrayList<FinancialAmountItemViewModel>()
        val amountItemValueList = ArrayList<AmountItemValueViewModel>()
        val amountItemValueViewModel =
            AmountItemValueViewModel()
        amountItemValueViewModel.itemAmount = itemAmountViewModel
        amountItemValueList.add(amountItemValueViewModel)

        val financialAmountItemViewModel =
            FinancialAmountItemViewModel()
        financialAmountItemViewModel.typeCode = typeCodeViewModel
        financialAmountItemViewModel.amountItemValue = amountItemValueList
        financialAmountItemList.add(financialAmountItemViewModel)
        financeViewModel.financialAmountItem = financialAmountItemList

        financeViewModel.financeAccountNumberId = "1234"
        financeViewModel.financialAmountItem = financialAmountItemList
        policyVehicleViewModel.finance = financeViewModel

        val policyVehicleList = ArrayList<PolicyVehicleViewModel>()
        policyVehicleList.add(policyVehicleViewModel)
        val policyBillingViewModel =
            PolicyBillingViewModel()

        val bankAccountList = ArrayList<BankAccountViewModel>()
        val bankAccountViewModel =
            BankAccountViewModel()
        bankAccountViewModel.accountNumberId = "123"
        bankAccountViewModel.bankName = "abcd"
        bankAccountViewModel.routingNumberId = "123"
        bankAccountViewModel.typeCode = "abc"
        bankAccountList.add(bankAccountViewModel)

        val paymentList = ArrayList<PaymentViewModel>()
        val paymentViewModel =
            PaymentViewModel()
        val frequencyCode =
            FrequencyCodeViewModel()
        frequencyCode.value = InsuranceConstants.MONTHLY_FREQUENCY
        paymentViewModel.frequencyCode = frequencyCode
        paymentViewModel.typeCode = typeCodeViewModel
        paymentViewModel.paymentDate = "2020/01/01"
        paymentList.add(paymentViewModel)

        policyBillingViewModel.bankAccountList = bankAccountList
        policyBillingViewModel.payment = paymentList

        policySectionViewModel.policyBilling = policyBillingViewModel
        policySectionViewModel.policyVehicle = policyVehicleList


        policyViewModel.personalVehicleItemSection = personalVehicleItemSectionList
        policyViewModel.policySection = policySectionViewModel


        policyInquiryViewModel.policy = policyViewModel
        policyInquiryList.add(policyInquiryViewModel)


        policyDetailResponseViewModel.policyInquiry = policyInquiryList
        policyDetailResponseViewModel.resultSet = resultSetViewModel

        return policyDetailResponseViewModel
    }

    @Test
    fun sendEventWithProduct() {
        val cdata = java.util.HashMap<String?, Any?>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY,
            TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        adobeContextData.setSubProduct(InsuranceConstants.AnalyticsConstants.SUBPRODUCT_GAP)
        adobeContextData.setProductAccount(TrackingEvent.ANALYTICS.VVAPS_TITLE)
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
        mPresenter?.sendEventWithProduct(
            InsuranceTrackingEvent.EVENT_MANAGE_POLICY_SAVE_WARRANTY,
            InsuranceConstants.AnalyticsConstants.SUBPRODUCT_GAP,
            TrackingEvent.ANALYTICS.VVAPS_TITLE
        )
        Mockito.verify(mAnalytics)
            ?.sendEventActionWithMap(
                InsuranceTrackingEvent.EVENT_MANAGE_POLICY_SAVE_WARRANTY,
                cdata
            )
    }

    @Test
    fun sendEventWithProduct_shouldSendEventWithCorrectData() {
        val eventName = "test_event"
        val subProduct = "test_sub_product"
        val cdata = java.util.HashMap<String?, Any?>()

        mPresenter!!.sendEventWithProduct(eventName, subProduct, TrackingEvent.ANALYTICS.VVAPS_TITLE)

        verify(mAnalytics)?.sendEventActionWithMap(eq(eventName), any())
    }

    @Test
    fun sendEventWithProduct_shouldHandleNullEventNameAndSubProduct() {
        mPresenter!!.sendEventWithProduct(null, null, TrackingEvent.ANALYTICS.PL_COMBO_TITLE)
        verify(mAnalytics)?.sendEventActionWithMap(isNull(), any())
    }


}