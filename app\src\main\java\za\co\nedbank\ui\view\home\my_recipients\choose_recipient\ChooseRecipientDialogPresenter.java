/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients.choose_recipient;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;

/**
 * Created by charurani on 23-08-2017.
 */

public class ChooseRecipientDialogPresenter extends NBBasePresenter<ChooseRecipientDialogView> {

    @Inject
    ChooseRecipientDialogPresenter(){

    }

    void handleRecipientSelected(int position){
        view.dismissDialogWithData(view.buildRecipientSelectedModel(position));
    }
}
