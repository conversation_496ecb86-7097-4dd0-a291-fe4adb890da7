package za.co.nedbank.ui.view.notification.notification_preferences;

import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.RadioGroup;

import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.sharedui.listener.IListItemClickListener;
import za.co.nedbank.core.sharedui.ui.SimpleDividerItemDecoration;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.AccountPreference;
import za.co.nedbank.core.view.fbnotifications.ClientPreferenceViewModel;
import za.co.nedbank.core.view.fbnotifications.FBPreferencesResponseViewModel;
import za.co.nedbank.databinding.ActivityNotificationPreferenceBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.notification.notification_preferences.all_accounts_preferences.AllAccountsPreferenceRecyclerViewAdapter;

public class NotificationPreferenceActivity extends NBBaseActivity implements NotificationPreferenceView, IListItemClickListener<Integer>, RadioGroup.OnCheckedChangeListener {

    @Inject
    NotificationPreferencePresenter mNotificationPreferencePresenter;

    private AllAccountsPreferenceRecyclerViewAdapter mAllAccountsPreferenceRecyclerViewAdapter;

    List<AccountPreference> mAccountPreferenceList = new ArrayList<>();

    List<AccountPreference> mAllAccountsList = new ArrayList<>();

    private FBPreferencesResponseViewModel mFBPreferencesResponseViewModel;
    private ActivityNotificationPreferenceBinding binding;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityNotificationPreferenceBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        mNotificationPreferencePresenter.bind(this);
        initView();
        initToolbar(binding.toolbar, true, true);
        binding.switchPreloginNotifications.setOnCheckedChangeListener((buttonView, isChecked) -> onUnauthInboxCheckedChanged(isChecked));

        binding.tvAccountNotifications.setOnClickListener(v -> onClickSetAccountNotifications());
        binding.allAccountsContainer.setOnClickListener(v -> onClickSetAccountNotifications());

        binding.deliveryPrefsContainer.setOnClickListener(v -> onClickDeliveryPreferences());
        binding.llForYouOffers.setOnClickListener(v -> onClickTurnOffPersonalCreditOffers());
        binding.llValueAddNotification.setOnClickListener(v -> onClickTurnOffValueOfferNotifications());

    }

    @Override
    protected void onDestroy() {
        mNotificationPreferencePresenter.unbind();
        super.onDestroy();
    }

    @Override
    protected void onResume() {
        super.onResume();
        mNotificationPreferencePresenter.loadPreferences();
    }

    private void initView() {
        if (mNotificationPreferencePresenter.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_TURNOFF_PERSONAL_CREDIT_OFFERS)) {
            ViewUtils.hideViews(binding.llForYouOffers, binding.dividerTurnOff);
        } else {
            ViewUtils.showViews(binding.llForYouOffers, binding.dividerTurnOff);
        }
        if (mNotificationPreferencePresenter.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_TURNOFF_VALUE_ADDS_OFFERS)) {
            ViewUtils.hideViews(binding.llValueAddNotification, binding.dividerValueTurnOff);
        } else {
            ViewUtils.showViews(binding.llValueAddNotification, binding.dividerValueTurnOff);
        }

        if (mNotificationPreferencePresenter.isFeatureDisabled(FeatureConstants.DynamicToggle.UN_AUTHENTICATE_TRANSACTION_INBOX)) {

            binding.transactionRow.setVisibility(View.GONE);
            binding.transactionOptionContainer.setVisibility(View.GONE);

            binding.forYouRow.setVisibility(View.GONE);
            binding.forYouOptionContainer.setVisibility(View.GONE);
        }

        binding.viewSelectedAccountsPrefRecyclerview.setItemAnimator(null);
        binding.viewSelectedAccountsPrefRecyclerview.setHasFixedSize(true);
        binding.viewSelectedAccountsPrefRecyclerview.setNestedScrollingEnabled(false);
        binding.viewSelectedAccountsPrefRecyclerview.addItemDecoration(new SimpleDividerItemDecoration(this));
        binding.viewSelectedAccountsPrefRecyclerview.setLayoutManager(new LinearLayoutManager(this));
        ViewUtils.hideViews(binding.allAccountsContainer, binding.viewSelectedAccountsPrefRecyclerview,
                binding.setAmountsLimitsRow, binding.dividerAccountList, binding.tvAccountNotifications);
    }

    @Override
    public void showProgress(boolean isVisible) {
        binding.progressBar.setVisibility(isVisible ? View.VISIBLE : View.GONE);
    }

    public void onUnauthInboxCheckedChanged(boolean isChecked) {
        mNotificationPreferencePresenter.updatePreferences(binding.switchForYouReceivePush.isChecked(), isChecked);
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        int id = group.getId();
        if (id == R.id.switch_for_you_receive_push) {
            mNotificationPreferencePresenter.updatePreferences(binding.switchForYouReceivePush.isChecked(), binding.switchPreloginNotifications.isChecked());
        }

    }


    public void onClickSetAccountNotifications() {
        mNotificationPreferencePresenter.navigateToAllAccountsPreferences(mAllAccountsList);
    }

    public void onClickDeliveryPreferences() {
        if (mFBPreferencesResponseViewModel != null)
            mNotificationPreferencePresenter.navigateToDeliveryPreferences(mFBPreferencesResponseViewModel.getClientPreferenceViewModel());
    }

    public void onClickTurnOffPersonalCreditOffers() {
            mNotificationPreferencePresenter.navigateToTurnOffPersonalCreditOffers(za.co.nedbank.core.Constants.PreApprovedOffersNotificationCategoryType.FOR_YOU_OFFERS.getCategoryId());
    }

    public void onClickTurnOffValueOfferNotifications() {
            mNotificationPreferencePresenter.navigateToTurnOffPersonalCreditOffers(Constants.PreApprovedOffersNotificationCategoryType.VALUE_ADD_NOTIFICATION.getCategoryId());
    }

    @Override
    public void onItemClick(Integer item) {
        mNotificationPreferencePresenter.navigateToAccountPreference(mAccountPreferenceList.get(item));
    }

    @Override
    public void showFullScreenError() {
        ViewUtils.showViews(binding.fullScreenErrorContainer);
    }

    @Override
    public void setData(FBPreferencesResponseViewModel fbPreferencesResponseViewModel) {
        mFBPreferencesResponseViewModel = fbPreferencesResponseViewModel;
        setClientPreferences(fbPreferencesResponseViewModel.getClientPreferenceViewModel());
        if (fbPreferencesResponseViewModel.getAllAccountPreferenceList() != null) {
            mAllAccountsList = fbPreferencesResponseViewModel.getAllAccountPreferenceList();
            mAccountPreferenceList = fbPreferencesResponseViewModel.getSetPreferenceList();
        }

        if (mAllAccountsList != null && !mAllAccountsList.isEmpty()) {
            if (mAccountPreferenceList != null && !mAccountPreferenceList.isEmpty()) {
                mAllAccountsPreferenceRecyclerViewAdapter = new AllAccountsPreferenceRecyclerViewAdapter(mAccountPreferenceList, this);
                binding.viewSelectedAccountsPrefRecyclerview.setAdapter(mAllAccountsPreferenceRecyclerViewAdapter);
                ViewUtils.hideViews(binding.allAccountsContainer);
                ViewUtils.showViews(binding.viewSelectedAccountsPrefRecyclerview, binding.tvAccountNotifications,
                        binding.setAmountsLimitsRow, binding.dividerAccountList);
            } else {
                ViewUtils.showViews(binding.setAmountsLimitsRow, binding.tvAccountNotifications);
                ViewUtils.hideViews(binding.allAccountsContainer, binding.viewSelectedAccountsPrefRecyclerview);
            }
        } else {
            ViewUtils.hideViews(binding.allAccountsContainer, binding.viewSelectedAccountsPrefRecyclerview,
                    binding.setAmountsLimitsRow, binding.dividerAccountList, binding.tvAccountNotifications);
        }
        binding.switchForYouReceivePush.setOnCheckedChangeListener(this);
        binding.switchPreloginNotifications.setEnabled(true);
    }


    private void setClientPreferences(ClientPreferenceViewModel clientPreferenceViewModel) {
        if (clientPreferenceViewModel != null) {
            binding.switchForYouReceivePush.setChecked(clientPreferenceViewModel.getAllowPushNotificationForOffers());
            binding.switchPreloginNotifications.setChecked(clientPreferenceViewModel.getAllowUnauthentictedInbox());
        }
    }

    @Override
    public void showErrorForUpdatePreferences() {
        showError(getString(R.string.something_went_wrong), getString(R.string.try_again_later), getString(R.string.snackbar_action_undo), () -> mNotificationPreferencePresenter.onUndoClick());

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(menuItem);
    }

    @Override
    public void onBackPressed() {
        mNotificationPreferencePresenter.trackActionNotificationPreferenceBack();
        super.onBackPressed();
    }

    @Override
    public void updateUI() {
        setClientPreferences(mFBPreferencesResponseViewModel.getClientPreferenceViewModel());
    }

    @Override
    public String getLowPriorityDeliveryPeriod() {
        return mFBPreferencesResponseViewModel.getClientPreferenceViewModel().getLowPriorityDeliveryPeriod();
    }

}