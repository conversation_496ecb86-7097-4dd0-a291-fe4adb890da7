/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.overview;


import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.viewpager.widget.PagerAdapter;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import xyz.santeri.wvp.WrappingViewPager;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.enumerations.BackgroundImageTypeEnum;
import za.co.nedbank.core.sharedui.ui.LifecycleView;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.view.overview.AccountTypeRowInterface;
import za.co.nedbank.services.view.overview.OverviewToolbarPage;


public class OverviewToolbarPagerAdapter extends PagerAdapter {

    private final NBBaseActivity activity;

    private final List<AccountsOverview> overviews = new ArrayList<>();

    private final LayoutInflater inflater;

    private WeakReference<AccountTypeRowInterface> accountTypeRowInterface;

    private final Map<OverviewType, OverviewToolbarPage> pages = new HashMap<>();

    private int mCurrentPosition = -1;

    private final BackgroundImageTypeEnum mSelectedImageType;
    private boolean mIsDummyData = false;

    public OverviewToolbarPagerAdapter(final NBBaseActivity activity, final AccountTypeRowInterface accountTypeRowInterface, BackgroundImageTypeEnum selectedImageType) {
        this.activity = activity;
        this.accountTypeRowInterface = new WeakReference<>(accountTypeRowInterface);
        inflater = LayoutInflater.from(activity);
        mSelectedImageType = selectedImageType;
    }

    public void swapData(List<AccountsOverview> overviews) {
        this.overviews.clear();
        if (overviews != null) {
            this.overviews.addAll(overviews);
            for (AccountsOverview overview : overviews) {
                OverviewToolbarPage page = pages.get(overview.overviewType);
                if (page != null) {
                    page.displayOverview(overview);
                }
            }
        }
        notifyDataSetChanged();
    }

    public void dummyData(boolean isDummyData) {
        this.mIsDummyData = isDummyData;
        this.overviews.clear();
        List<AccountsOverview> overviewDataList = new ArrayList<>();
        AccountsOverview accountsOverview = new AccountsOverview();
        accountsOverview = accountsOverview.dummy();
        overviewDataList.add(accountsOverview);

        if (overviewDataList != null) {
            this.overviews.addAll(overviewDataList);
            for (AccountsOverview overview : overviewDataList) {
                OverviewToolbarPage page = pages.get(overview.overviewType);
                if (page != null) {
                    page.displayOverview(overview);
                }
            }
        }
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return overviews.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        LifecycleView lifecycleView = createView(activity, position);
        View view = lifecycleView.createView(inflater, container);
        container.addView(view);
        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        container.removeView((View)object);
    }

    private LifecycleView createView(NBBaseActivity activity, int position) {
        OverviewToolbarPage page = new OverviewToolbarPage(activity, overviews.get(position), accountTypeRowInterface.get(), mSelectedImageType);
        pages.put(overviews.get(position).overviewType, page);
        return page;
    }

    @Override
    public void setPrimaryItem(ViewGroup container, int position, Object object) {
        super.setPrimaryItem(container, position, object);

        if (position != mCurrentPosition) {
            View view = (View) object;
            WrappingViewPager pager = (WrappingViewPager) container;
            if (view != null) {
                mCurrentPosition = position;
                pager.onPageChanged(view);
            }
        }
    }

    public AccountsOverview getItem(final int position) {
        if (position >= overviews.size()) {
            return null;
        }
        return overviews.get(position);
    }

    public int getOverviewPageIndex(OverviewType overviewType) {
        for (int index = 0; index < overviews.size(); index++) {
            AccountsOverview overview = overviews.get(index);
            if (overviewType == overview.overviewType) {
                return index;
            }
        }
        return -1;
    }
}
