package za.co.nedbank.ui.view.pop;

import androidx.recyclerview.widget.RecyclerView;

import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.ItemTransactionHistoryFooterBinding;

class TransactionHistoryFooterViewHolder extends RecyclerView.ViewHolder {

    private FooterViewInterface mFooterViewInterface;
    ItemTransactionHistoryFooterBinding binding;

    TransactionHistoryFooterViewHolder(ItemTransactionHistoryFooterBinding binding) {
        super(binding.getRoot());
        this.binding = binding;
    }

    public void setup(FooterViewInterface footerViewInterface, TransactionFooterViewType footerViewType) {
        mFooterViewInterface = footerViewInterface;
        if(footerViewType == null) {
            return;
        }
        switch (footerViewType) {
            case VIEW_MORE :
                ViewUtils.showViews(binding.viewMore);
                ViewUtils.hideViews(binding.progressBarNextPage,binding.noMorePayments);
                break;
            case NEXT_PAGE_LOADING:
                ViewUtils.showViews(binding.progressBarNextPage);
                ViewUtils.hideViews(binding.viewMore,binding.noMorePayments);
                break;
            case NO_MORE_PAYMENTS:
                ViewUtils.showViews(binding.noMorePayments);
                ViewUtils.hideViews(binding.viewMore,binding.progressBarNextPage);
                break;

        }
        binding.viewMore.setOnClickListener(v -> onViewMoreClick());
    }

    void onViewMoreClick() {
        mFooterViewInterface.footerViewClicked();
    }
}
