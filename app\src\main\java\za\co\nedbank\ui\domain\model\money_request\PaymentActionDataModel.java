package za.co.nedbank.ui.domain.model.money_request;

/**
 * Created by mayuri.birajdar on 31-05-2018.
 */

public class PaymentActionDataModel {
    private long paymentBatchId;
    private double processAmount;
    private String payerAccountNumber;
    private String payerAccountType;
    private String payerDescription;
    private long paymentRequestId;
    private String paymentRequestAction;

    public long getPaymentBatchId() {
        return paymentBatchId;
    }

    public void setPaymentBatchId(long paymentBatchId) {
        this.paymentBatchId = paymentBatchId;
    }

    public double getProcessAmount() {
        return processAmount;
    }

    public void setProcessAmount(double processAmount) {
        this.processAmount = processAmount;
    }

    public String getPayerAccountNumber() {
        return payerAccountNumber;
    }

    public void setPayerAccountNumber(String payerAccountNumber) {
        this.payerAccountNumber = payerAccountNumber;
    }

    public String getPayerAccountType() {
        return payerAccountType;
    }

    public void setPayerAccountType(String payerAccountType) {
        this.payerAccountType = payerAccountType;
    }

    public String getPayerDescription() {
        return payerDescription;
    }

    public void setPayerDescription(String payerDescription) {
        this.payerDescription = payerDescription;
    }

    public void setPaymentRequestId(long paymentRequestId) {
        this.paymentRequestId = paymentRequestId;
    }

    public void setPaymentRequestAction(String paymentRequestAction) {
        this.paymentRequestAction = paymentRequestAction;
    }

    public long getPaymentRequestId() {
        return paymentRequestId;
    }

    public String getPaymentRequestAction() {
        return paymentRequestAction;
    }
}
