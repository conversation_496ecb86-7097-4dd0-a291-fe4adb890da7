package za.co.nedbank.ui.domain.mapper;

import java.util.ArrayList;

import javax.inject.Inject;

import za.co.nedbank.core.data.metadatav3.MetaDataV3EntityToDataMapper;
import za.co.nedbank.ui.data.entity.closed_account.ClosedAccountEntity;
import za.co.nedbank.ui.data.entity.closed_account.ClosedAccountResponseEntity;
import za.co.nedbank.ui.domain.model.closed_account.ClosedAccountDataModel;
import za.co.nedbank.ui.domain.model.closed_account.ClosedAccountResponseData;

public class ClosedAccountResponseEntityToDataMapper {

    private final MetaDataV3EntityToDataMapper metaDataEntityToDataMapper;
    @Inject
    ClosedAccountResponseEntityToDataMapper(MetaDataV3EntityToDataMapper metaDataEntityToDataMapper) {
        this.metaDataEntityToDataMapper = metaDataEntityToDataMapper;
    }

    public ClosedAccountResponseData mapData(ClosedAccountResponseEntity closedaccountResponseEntity) {
        ClosedAccountResponseData closedaccountResponseData = new ClosedAccountResponseData();
        closedaccountResponseData.setMetaDataModel(metaDataEntityToDataMapper.mapMetaData(closedaccountResponseEntity.getMetaDataEntity()));
        ArrayList<ClosedAccountDataModel> closedAccountDataModels = new ArrayList<>();
        if (closedaccountResponseEntity != null && closedaccountResponseEntity.getData() != null && closedaccountResponseEntity.getData().size() > 0) {
            for (ClosedAccountEntity closedAccountEntity : closedaccountResponseEntity.getData()) {
                closedAccountDataModels.add(mapClosedaccountEntityToClosedAccountDataModel(closedAccountEntity));
            }
            closedaccountResponseData.setData(closedAccountDataModels);
        }
        return closedaccountResponseData;
    }

    private ClosedAccountDataModel mapClosedaccountEntityToClosedAccountDataModel(ClosedAccountEntity closedAccountEntity) {
        ClosedAccountDataModel closedAccountDataModel = new ClosedAccountDataModel();
        if (closedAccountEntity != null) {
            closedAccountDataModel.setAccountNumber(closedAccountEntity.getAccountNumber());
            closedAccountDataModel.setInvestorNumber(closedAccountEntity.getInvestorNumber());
            closedAccountDataModel.setAccountName(closedAccountEntity.getAccountName());
        }
        return closedAccountDataModel;
    }

}