package za.co.nedbank.ui.view.home.overview.dashboard;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jakewharton.rxbinding2.view.RxView;

import java.lang.ref.WeakReference;

import io.reactivex.android.schedulers.AndroidSchedulers;
import za.co.nedbank.R;
import za.co.nedbank.core.base.adapter.BaseAdapter;
import za.co.nedbank.core.dashboard.WidgetData;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.databinding.HomeWidgetBinding;
import za.co.nedbank.uisdk.component.CompatTextViewEnhanced;

public class HomeWidgetAdapter extends BaseAdapter<WidgetData> {

    private WeakReference<WidgetDataInterface> mWidgetDataInterface;
    private boolean isShopBadgeRequired;
    private ApplicationStorage mApplicationStorage;

    public HomeWidgetAdapter(Context context, WidgetDataInterface widgetDataInterface, boolean isShopBadgeRequired,
                             ApplicationStorage mApplicationStorage) {
        super(context);
        this.mWidgetDataInterface = new WeakReference(widgetDataInterface);
        this.isShopBadgeRequired = isShopBadgeRequired;
        this.mApplicationStorage = mApplicationStorage;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        HomeWidgetBinding binding = HomeWidgetBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new HomeWidgetViewHolder(binding, mWidgetDataInterface);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, WidgetData model, int position) {
        ((HomeWidgetViewHolder) holder).bind(model);
    }

    public class HomeWidgetViewHolder extends RecyclerView.ViewHolder {
        CompatTextViewEnhanced widgetTextView;
        AppCompatImageView widgetImageView;
        View mainView;
        CompatTextViewEnhanced notificationCountTextView;
        private WidgetData mWidgetData;
        private WeakReference<WidgetDataInterface> mWidgetDataInterface;

        HomeWidgetViewHolder(HomeWidgetBinding binding, final WeakReference<WidgetDataInterface> widgetDataInterfaceWeakReference) {
            super(binding.getRoot());
            this.mWidgetDataInterface = widgetDataInterfaceWeakReference;
            this.widgetTextView = binding.widgetTv;
            this.widgetImageView = binding.widgetIv;
            this.mainView = binding.llWidget;
            this.notificationCountTextView = binding.widgetNotificationCountTv;
        }

        @SuppressLint("CheckResult")
        public void bind(WidgetData widgetData) {
            if(getContext() == null)
                return;

            if (widgetData != null) {
                this.mWidgetData = widgetData;
                if (!TextUtils.isEmpty(widgetData.getWidgetName())) {
                    try {
                        widgetTextView.setText(widgetData.getWidgetName());
                        widgetTextView.setTextColor(getContext().getResources().getColor(widgetData.getTextColor()));
                    }catch (Exception e){
                        NBLogger.e("Exception",e.getMessage());
                    }
                }
                widgetImageView.setImageDrawable(ContextCompat.getDrawable(getContext(), widgetData.getWidgetResource()));
                if (widgetData.isHasNotificationIcon() && !StringUtils.isNullOrEmpty(widgetData.getNotificationCountText())) {
                    handleWidgetNotification(widgetData);
                } else {
                    notificationCountTextView.setVisibility(View.GONE);
                }

                RxView.clicks(mainView)
                        .first(1)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(o -> {
                            if (mWidgetDataInterface.get() != null) {
                                mWidgetDataInterface.get().widgetClicked(mWidgetData);
                            }
                        }, throwable -> {
                            //do nothing
                        });
            }
        }

        private void handleWidgetNotification(WidgetData widgetData) {
            if (widgetData.getWidgetName().equals(getContext().getString(R.string.shop_widget))) {
                if (isShopBadgeRequired) {
                    String lastLoginTime = mApplicationStorage.getString(StorageKeys.NGI_LOGIN_TIMESTAMP, "");
                    mApplicationStorage.putString(StorageKeys.LAST_SHOP_NOTIFICATION_DISPLAY_TIME, lastLoginTime);
                    notificationCountTextView.setVisibility(View.VISIBLE);
                    notificationCountTextView.setText(widgetData.getNotificationCountText());
                } else {
                    notificationCountTextView.setVisibility(View.GONE);
                }
            } else {
                notificationCountTextView.setVisibility(View.VISIBLE);
                notificationCountTextView.setText(widgetData.getNotificationCountText());
            }
        }
    }
}