package za.co.nedbank.ui.view.notification.notification_preferences.account_preference;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;

import java.util.IllegalFormatConversionException;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.AccountPreference;
import za.co.nedbank.databinding.ActivityPreferenceAccountBinding;
import za.co.nedbank.ui.di.AppDI;

public class AccountPreferenceActivity extends NBBaseActivity implements AccountPreferenceView {

    private static final String TAG = "Crash";
    private AccountPreference mAccountPreference;
    private AccountPreference mAPIAccountPreference = new AccountPreference();
    @Inject
    AccountPreferencePresenter mAccountPreferencePresenter;
    private ActivityPreferenceAccountBinding binding;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityPreferenceAccountBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        receiveBundle();
        mAccountPreferencePresenter.bind(this);
        String toolbarTitle = mAccountPreference.getAccountNumber();
        if (Constants.PENDING_ACT_ACCOUNT_STATUS_CODE.equals(mAccountPreference.getAccountStatusCode())) {
            toolbarTitle = getResources().getString(R.string.pending_activation);
        }
        initToolbar(binding.toolbar, true, toolbarTitle);
        setUpView();
        binding.saveChangesButton.setOnClickListener(v -> onClickSaveChanges());

        binding.textHowMuchSpendAccount.setOnFocusChangeListener((v, hasFocus) -> onPushSpendTextFocusChange(hasFocus));
        binding.textLowAccountBalanceAccount.setOnFocusChangeListener((v, hasFocus) -> onPushBalanceTextFocusChange(hasFocus));
        binding.textHowMuchSpendSmsAccount.setOnFocusChangeListener((v, hasFocus) -> onSMSSpendTextFocusChange(hasFocus));
        binding.textLowAccountBalanceSmsAccount.setOnFocusChangeListener((v, hasFocus) -> onSMSBalanceTextFocusChange(hasFocus));
        binding.switchPushNotifications.setOnCheckedChangeListener((buttonView, isChecked) -> onPushCheckedChanged(isChecked));
        binding.switchSmsNotifications.setOnCheckedChangeListener((buttonView, isChecked) -> onSmsCheckedChanged(isChecked));
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onDestroy() {
        mAccountPreferencePresenter.unbind();
        super.onDestroy();
    }

    private void setUpView() {
        if (mAccountPreference.isAllowPushForTrans()) {
            binding.switchPushNotifications.setChecked(true);
            binding.pushPreferenceContainer.setVisibility(View.VISIBLE);
            binding.textHowMuchSpendAccount.setText(FormattingUtil.convertToFormattedCurrency(mAccountPreference.getSpendLimitApp()));
            binding.textLowAccountBalanceAccount.setText(FormattingUtil.convertToFormattedCurrency(mAccountPreference.getBalanceLimitApp()));
            binding.tvReceivePush.setContentDescription(String.format(NotificationConstants.FORMAT_SFSF, getString(R.string.push_set_on_account), mAccountPreference.getSpendLimitApp(), getString(R.string.balance_limit), mAccountPreference.getBalanceLimitApp()));
        }
        if (!mAccountPreference.isAllowPushForTrans()) {
            binding.tvReceivePush.setContentDescription(getString(R.string.no_push_settings));

        }

        if (mAccountPreference.isAllowSMSForTrans()) {
            binding.switchSmsNotifications.setChecked(true);
            binding.smsPreferenceContainer.setVisibility(View.VISIBLE);
            binding.textHowMuchSpendSmsAccount.setText(FormattingUtil.convertToFormattedCurrency(mAccountPreference.getSpendLimitSMS()));
            binding.textLowAccountBalanceSmsAccount.setText(FormattingUtil.convertToFormattedCurrency(mAccountPreference.getBalanceLimitSMS()));
            binding.tvReceiveSms.setContentDescription(String.format(NotificationConstants.FORMAT_SFSF,getString(R.string.sms_set_on_account) , mAccountPreference.getSpendLimitSMS() , getString(R.string.balance_limit) , mAccountPreference.getBalanceLimitSMS()));
        }
        if (!mAccountPreference.isAllowSMSForTrans()) {
            binding.tvReceiveSms.setContentDescription(getString(R.string.no_push_set));
        }

        enableSaveButton(false);
    }


    private void receiveBundle() {
        if (getIntent() != null && getIntent().getExtras() != null) {
            mAccountPreference = getIntent().getExtras().getParcelable(NotificationConstants.EXTRA.ACCOUNT_PREFERENCE);
        }
    }

    private void announceForAccessibility(String accesiblitycontent) {
        Observable.timer(2, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result -> AccessibilityUtils.announceForAccessibility(this, accesiblitycontent));

    }

    public void onPushSpendTextFocusChange(boolean hasFocus) {
        if (hasFocus) {
            enableSaveButton(true);
            try {
                announceForAccessibility(String.format(NotificationConstants.FORMAT_SSS, getString(R.string.your_current_spend_limit), binding.textHowMuchSpendAccount.getValue(), getString(R.string.will_notify_of_spend_overuse)));
            } catch (IllegalFormatConversionException e) {
                NBLogger.d(TAG, e.getMessage());
            }
        }

    }

    public void onPushBalanceTextFocusChange(boolean hasFocus) {
        if (hasFocus) {
            enableSaveButton(true);
            announceForAccessibility(String.format(NotificationConstants.FORMAT_SSS,getString(R.string.current_balance_limit) ,binding.textLowAccountBalanceAccount.getValue() , getString(R.string.will_notify_of_below_balance)));
        }

    }

    public void onSMSSpendTextFocusChange(boolean hasFocus) {
        if (hasFocus) {
            enableSaveButton(true);
            announceForAccessibility(String.format(NotificationConstants.FORMAT_SSS,getString(R.string.your_current_spend_limit) , binding.textHowMuchSpendSmsAccount.getValue() , getString(R.string.will_notify_of_spend_overuse)));
        }

    }

    public void onSMSBalanceTextFocusChange(boolean hasFocus) {
        if (hasFocus) {
            enableSaveButton(true);
            announceForAccessibility(String.format(NotificationConstants.FORMAT_SSS,getString(R.string.current_balance_limit) , binding.textLowAccountBalanceSmsAccount.getValue() , getString(R.string.will_notify_of_below_balance)));
        }

    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(menuItem);
    }

    @Override
    public void onBackPressed() {
        mAccountPreferencePresenter.trackActionAccountPreferenceBack();
        super.onBackPressed();
    }


    public void onClickSaveChanges() {
        mAPIAccountPreference.setAccountNumber(mAccountPreference.getAccountNumber());

        mAPIAccountPreference.setAllowPushForTrans(binding.switchPushNotifications.isChecked());
        if (binding.switchPushNotifications.isChecked()) {
            if (!TextUtils.isEmpty(binding.textLowAccountBalanceAccount.getValue())) {
                mAPIAccountPreference.setBalanceLimitApp(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.textLowAccountBalanceAccount.getValue())));
            }
            if (!TextUtils.isEmpty(binding.textHowMuchSpendAccount.getValue())) {
                mAPIAccountPreference.setSpendLimitApp(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.textHowMuchSpendAccount.getValue())));
            }
        }

        mAPIAccountPreference.setAllowSMSForTrans(binding.switchSmsNotifications.isChecked());
        if (binding.switchSmsNotifications.isChecked()) {
            if (!TextUtils.isEmpty(binding.textHowMuchSpendSmsAccount.getValue())) {
                mAPIAccountPreference.setSpendLimitSMS(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.textHowMuchSpendSmsAccount.getValue())));
            }

            if (!TextUtils.isEmpty(binding.textLowAccountBalanceSmsAccount.getValue())) {
                mAPIAccountPreference.setBalanceLimitSMS(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.textLowAccountBalanceSmsAccount.getValue())));
            }
        }
        mAccountPreferencePresenter.updatePreferences(mAPIAccountPreference);
    }

    public void onPushCheckedChanged(boolean isChecked) {

        if (isChecked) {
            binding.pushPreferenceContainer.setVisibility(View.VISIBLE);
            binding.textHowMuchSpendAccount.setText(getResources().getString(R.string.default_spend_limit));
            binding.textLowAccountBalanceAccount.setText(getResources().getString(R.string.default_balance_limit));
            announceForAccessibility(getString(R.string.you_are_choosing_to_switch_push));

        } else {
            binding.pushPreferenceContainer.setVisibility(View.GONE);
            ViewUtils.hideSoftKeyboard(this, binding.pushPreferenceContainer);
            announceForAccessibility(getString(R.string.you_are_choosing_to_switch_off_push));
        }
        enableSaveButton(true);
    }

    public void onSmsCheckedChanged(boolean isChecked) {

        if (isChecked) {
            binding.smsPreferenceContainer.setVisibility(View.VISIBLE);
            binding.textHowMuchSpendSmsAccount.setText(getResources().getString(R.string.default_spend_limit));
            binding.textLowAccountBalanceSmsAccount.setText(getResources().getString(R.string.default_balance_limit));
            announceForAccessibility(getString(R.string.you_are_choosing_to_switch_on_sms));
        } else {
            binding.smsPreferenceContainer.setVisibility(View.GONE);
            ViewUtils.hideSoftKeyboard(this, binding.smsPreferenceContainer);
            announceForAccessibility(getString(R.string.you_are_choosing_to_switch_off_sms));
        }
        enableSaveButton(true);
    }

    @Override
    public void showProgress(boolean isVisible) {
        binding.progressBar.setVisibility(isVisible ? View.VISIBLE : View.GONE);
    }

    @Override
    public void showErrorForUpdatePreferences(boolean isApiFailure) {
        showError(getString(R.string.something_went_wrong), getString(R.string.try_again_later), getString(R.string.snackbar_action_undo), () -> mAccountPreferencePresenter.onUndoClick());
        mAccountPreferencePresenter.trackFailure(isApiFailure, getString(R.string.something_went_wrong));
    }

    @Override
    public void enableSaveButton(boolean isEnable) {
        binding.saveChangesButton.setEnabled(isEnable);
        binding.saveChangesButton.setClickable(isEnable);
    }

    @Override
    public void updateUI() {
        setUpView();
    }

}
