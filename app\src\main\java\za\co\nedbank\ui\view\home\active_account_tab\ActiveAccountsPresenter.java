package za.co.nedbank.ui.view.home.active_account_tab;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;

public class ActiveAccountsPresenter extends NBBasePresenter<ActiveAccountsView> {

    private final NavigationRouter mNavigationRouter;
    private final Analytics mAnalytics;


    @Inject
    ActiveAccountsPresenter(final NavigationRouter navigationRouter,
                            final Analytics mAnalytics) {
        this.mNavigationRouter = navigationRouter;
        this.mAnalytics = mAnalytics;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

    public void navigateToDownloadStatement(AccountSummary accountSummary) {
        if (accountSummary != null) {
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.DOWNLOAD_STATEMENTS);
            navigationTarget.withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_NUMBER, accountSummary.getNumber())
                    .withParam(ServicesNavigationTarget.FROM_DASHBORD_INV_TAX_FLOW, true)
                    .withParam(ServicesNavigationTarget.FROM_DASHBORD_INV_ACTIVE_ACCOUNTS_TAX_FLOW, true)
                    .withParam(ServicesNavigationTarget.DOWNLOAD_STATEMENT_TYPE, Constants.StatementType.TAX_STATEMENT);
            mNavigationRouter.navigateTo(navigationTarget);
        }

    }

    public void onActiveAccountsFragmentVisible() {
        mAnalytics.sendEvent(TrackingEvent.CLICK_ACTIVE_TAX_CERT_TAB_TAXCERTINV, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }
}