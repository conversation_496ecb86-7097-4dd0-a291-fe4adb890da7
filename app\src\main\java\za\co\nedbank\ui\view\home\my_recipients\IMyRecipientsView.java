/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients;

import static za.co.nedbank.ui.view.home.my_recipients.IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_ACCOUNTS_API;
import static za.co.nedbank.ui.view.home.my_recipients.IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_DEFAULT_ACCOUNTS_API;
import static za.co.nedbank.ui.view.home.my_recipients.IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_LIMITS_API;
import static za.co.nedbank.ui.view.home.my_recipients.IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.domain.model.beneficiary.user.UserBeneficiaryData;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;

/**
 * Created by charurani on 21-08-2017.
 */

public interface IMyRecipientsView extends NBBaseView {
    void updateUserBeneficiaryData();

    void showChooseRecipientDialog();

    void showFromAccountTypeDialog();

    void showError(String error, @IMyRecipientsViewAPIErrorType int errorType);

    void receiveUserBeneficiaryBeans(List<UserBeneficiaryData> userBeneficiaryDataList);

    void receiveAccounts(List<AccountViewModel> payAccounts);

    PaymentsViewModel buildPaymentsViewModel(AccountViewModel accountViewModel);

    AccountViewModel provideDefaultFromAccount();

    void handleEnterAccountNumber();

    void setPayButtonEnabled(boolean isEnabled);

    void setActivityTouchEnabled(boolean isEnabled);

    void showLoadingOnButton(boolean inProgress);

    void hideKeyBoard();

    void setLimitToBeCompared(double limit);

    void setVisibilityOnEmptyStateView(boolean isVisible);

    void showAmountValidationError();

    String provideNoAccountString();

    void dismissSnackBar();

    void setVisibilityOnRecipientLoadLayout(boolean isVisible);

    void receiveDefaultAccountIdentifierValue(String value);

    void updateEditData(PaymentsViewModel paymentViewModel);

    AccountViewModel getSelectedFromAccountViewModel();

    boolean isFeatureDisabled(String feature);

    String fetchFromAccountName();

    String fetchToAccountName();

    String getBeneficiaryType();

    void showPopForInterNationalPayment(String bankName);





    @IntDef({ERROR_IN_RECIPIENTS_API, ERROR_IN_LIMITS_API, ERROR_IN_ACCOUNTS_API, ERROR_IN_DEFAULT_ACCOUNTS_API, IMyRecipientsViewAPIErrorType.ERROR_IN_INTERNATIONAL_FLOW})
    @Retention(RetentionPolicy.RUNTIME)
    @interface IMyRecipientsViewAPIErrorType {
        int ERROR_IN_RECIPIENTS_API = 1;
        int ERROR_IN_LIMITS_API = 2;
        int ERROR_IN_ACCOUNTS_API = 3;
        int ERROR_IN_DEFAULT_ACCOUNTS_API = 4;
        int ERROR_IN_INTERNATIONAL_FLOW=5;
    }
}
