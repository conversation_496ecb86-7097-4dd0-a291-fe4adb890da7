package za.co.nedbank.ui.data.entity.closed_account;

import com.squareup.moshi.Json;

public class ClosedAccountEntity {
    @Json(name = "InvestorNumber")
    private String investorNumber;
    @<PERSON>son(name = "AccountNumber")
    private String accountNumber;
    @<PERSON><PERSON>(name = "AccountName")
    private String accountName;

    public String getAccountName() {
        return accountName;
    }

    public String getInvestorNumber() {
        return investorNumber;
    }

    public String getAccountNumber() {
        return accountNumber;
    }
}
