package za.co.nedbank.ui.view.pop;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.base.adapter.BaseSectionedAdapter;
import za.co.nedbank.core.base.adapter.Section;
import za.co.nedbank.core.base.adapter.SectionAdapterItem;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;
import za.co.nedbank.databinding.ItemTransactionHistoryBinding;
import za.co.nedbank.databinding.ItemTransactionHistoryFooterBinding;
import za.co.nedbank.databinding.ItemTransactionHistorySectionBinding;
import za.co.nedbank.payment.databinding.TaxCertificateActiveAccountBinding;

import static za.co.nedbank.core.utils.FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS;

class TransactionHistoryAdapter extends BaseSectionedAdapter<TransactionHistoryViewModel> {
    private static final int ITEM_TYPE_FOOTER = 3;
    private final List<Section<TransactionHistoryViewModel>> sections = new ArrayList<>();
    private Context mContext;
    private TransactionHistoryRowInterface transactionHistoryRowInterface;
    private FooterViewInterface mFooterViewInterface;
    private TransactionFooterViewType mFooterViewType;


    TransactionHistoryAdapter(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    protected List<Section<TransactionHistoryViewModel>> getSections(List<TransactionHistoryViewModel> items) {
        return prepareSections(items);
    }

    private List<Section<TransactionHistoryViewModel>> prepareSections(final List<TransactionHistoryViewModel> transactions) {
        List<Section<TransactionHistoryViewModel>> sections = new ArrayList<>();

        final DateTimeFormatter monthFormatter = DateTimeFormat.forPattern(getContext().getString(za.co.nedbank.services.R.string.transactions_section_pattern));

        final DateTime now = new DateTime();
        final DateTime previousWeek = now.minusWeeks(1);

        Section<TransactionHistoryViewModel> thisWeekSection = new TransactionHistorySection(getContext().getString(za.co.nedbank.services.R.string.transactions_this_week),
                now.dayOfWeek().withMinimumValue().millisOfDay().withMinimumValue().getMillis(),
                now.dayOfWeek().withMaximumValue().millisOfDay().withMaximumValue().getMillis());

        Section<TransactionHistoryViewModel> lastWeekSection = new TransactionHistorySection(getContext().getString(za.co.nedbank.services.R.string.transactions_last_week),
                previousWeek.dayOfWeek().withMinimumValue().millisOfDay().withMinimumValue().getMillis(),
                previousWeek.dayOfWeek().withMaximumValue().millisOfDay().withMaximumValue().getMillis());

        sections.add(thisWeekSection);
        sections.add(lastWeekSection);

        //months
        final DateTime minDateTime = new DateTime(getMinimumTransactionDate(transactions));

        DateTime monthStart = now;

        while (12 * monthStart.getYear() + monthStart.getMonthOfYear() >= 12 * minDateTime.getYear() + minDateTime.getMonthOfYear()) {

            final DateTime finalMonthStart = monthStart;

            Section<TransactionHistoryViewModel> nextMonthSection = new TransactionHistorySection(monthFormatter.print(finalMonthStart.getMillis()),
                    finalMonthStart.dayOfMonth().withMinimumValue().millisOfDay().withMinimumValue().getMillis(),
                    finalMonthStart.dayOfMonth().withMaximumValue().millisOfDay().withMaximumValue().getMillis());

            sections.add(nextMonthSection);

            monthStart = monthStart.minusMonths(1);
        }

        return sections;
    }

    private long getMinimumTransactionDate(final List<TransactionHistoryViewModel> transactions) {
        if (transactions == null || transactions.size() <= 0) return 0;
        long minDate = FormattingUtil.getFormattedDate(transactions.get(0).getStartDate(), DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);

        for (TransactionHistoryViewModel transaction : transactions) {
            long date = FormattingUtil.getFormattedDate(transaction.getStartDate(), DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            if (date <= minDate) {
                minDate = date;
            }
        }
        return minDate;
    }


    @Override
    public RecyclerView.ViewHolder onCreateItemViewHolder(final ViewGroup parent, final int viewType) {
        if (viewType == ITEM_TYPE_FOOTER) {
            return onCreateFooterViewHolder(parent, viewType);
        }
        return super.onCreateItemViewHolder(parent, viewType);
    }



    @Override
    protected RecyclerView.ViewHolder onCreateContentViewHolder(final ViewGroup parent, final int viewType) {
        ItemTransactionHistoryBinding binding = ItemTransactionHistoryBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new TransactionHistoryViewHolder(binding);
    }

    @Override
    protected RecyclerView.ViewHolder onCreateSectionViewHolder(final ViewGroup parent, final int viewType) {
        ItemTransactionHistorySectionBinding binding = ItemTransactionHistorySectionBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new TransactionHistorySectionViewHolder(binding);
    }

    private RecyclerView.ViewHolder onCreateFooterViewHolder(final ViewGroup parent, final int viewType) {
        ItemTransactionHistoryFooterBinding binding = ItemTransactionHistoryFooterBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new TransactionHistoryFooterViewHolder(binding);
    }

    @Override
    public int getItemCount() {
        // For showing Footer View - View More, Loading or End of history
        if(mFooterViewType != null) {
            return super.getItemCount() + 1;
        }
        return super.getItemCount();
    }

    @Override
    public int getItemViewType(int position) {
        if (mFooterViewType !=null  && position == getItemCount() - 1) {
            return ITEM_TYPE_FOOTER;
        }  else
            return super.getItemViewType(position);
    }



    @Override
    protected void onBindContentViewHolder(final RecyclerView.ViewHolder holder, final SectionAdapterItem<TransactionHistoryViewModel> model,
                                           final int position) {
        ((TransactionHistoryViewHolder) holder).setup(transactionHistoryRowInterface, model.getContent(), isLastItemInSection(position));
    }

    @Override
    protected void onBindSectionViewHolder(final RecyclerView.ViewHolder holder, final SectionAdapterItem<TransactionHistoryViewModel> model,
                                           final int position) {
        ((TransactionHistorySectionViewHolder) holder).setup(model.getSection());
    }

    private void onBindFooterViewHolder(RecyclerView.ViewHolder holder) {
        ((TransactionHistoryFooterViewHolder) holder).setup(mFooterViewInterface,mFooterViewType);
    }

    @Override
    public void onBindItemViewHolder(RecyclerView.ViewHolder holder, SectionAdapterItem<TransactionHistoryViewModel> model, int position) {
        if (getItemViewType(position) == ITEM_TYPE_FOOTER) {
            onBindFooterViewHolder(holder);
        } else {
            super.onBindItemViewHolder(holder, model, position);
        }
    }

    void setRowInterface(final TransactionHistoryRowInterface transactionHistoryRowInterface,
                         final FooterViewInterface footerViewInterface) {
        this.transactionHistoryRowInterface = transactionHistoryRowInterface;
        this.mFooterViewInterface = footerViewInterface;
    }

    void showFooterView(TransactionFooterViewType footerViewType) {
        mFooterViewType = footerViewType;
        notifyDataSetChanged();
    }

     boolean isViewMoreVisible() {
        return mFooterViewType == TransactionFooterViewType.VIEW_MORE;
    }

    @Override
    protected Comparator<TransactionHistoryViewModel> getSortingComparator() {
        return (first, second) -> {
            long firstDate = FormattingUtil.getFormattedDate(first.getStartDate(), DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            long secondDate = FormattingUtil.getFormattedDate(second.getStartDate(), DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            return Long.compare(secondDate, firstDate);
        };
    }

    @Override
    protected boolean shouldSortData() {
        return true;
    }
}
