/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.validation.RequestAmountValidator;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.payment.transfer.validation.MinimumTransferAmountValidator;
import za.co.nedbank.ui.domain.model.money_request.PaymentRequestDataModel;
import za.co.nedbank.ui.domain.model.money_request.PaymentResponseModel;
import za.co.nedbank.ui.domain.usecase.money_request.GetRequesterAccountsUseCase;
import za.co.nedbank.ui.domain.usecase.money_request.PaymentRequestsUseCase;
import za.co.nedbank.ui.view.tracking.AppTracking;
import za.co.nedbank.uisdk.validation.ValidatableInput;

/**
 * Created by sandip.lawate on 2/15/2018.
 */
public class ViewMoneyRequestDetailsPresenter extends NBBasePresenter<MoneyRequestDetailsView> {

    private final NavigationRouter mNavigationRouter;
    private final ErrorHandler mErrorHandler;
    private final GetRequesterAccountsUseCase getRequesterAccountsUseCase;
    private final PaymentRequestsUseCase paymentRequestsUseCase;
    private final NonEmptyTextValidator mNonEmptyTextValidator;
    private final RequestAmountValidator amountLimitValidator;
    private final MinimumTransferAmountValidator minimumTransferAmountValidator;
    private List<AccountViewModel> accountViewModelList = null;
    private final Analytics analytics;

    @Inject
    ViewMoneyRequestDetailsPresenter(@NonNull NavigationRouter navigationRouter,
                                     @NonNull ErrorHandler errorHandler, final GetRequesterAccountsUseCase payAccountsUseCase, PaymentRequestsUseCase paymentRequestsUseCase,
                                     NonEmptyTextValidator mNonEmptyTextValidator, MinimumTransferAmountValidator minimumTransferAmountValidator, RequestAmountValidator amountLimitValidator, Analytics analytics) {
        this.mNavigationRouter = navigationRouter;
        this.getRequesterAccountsUseCase = payAccountsUseCase;
        this.mErrorHandler = errorHandler;
        this.mNonEmptyTextValidator = mNonEmptyTextValidator;
        this.minimumTransferAmountValidator = minimumTransferAmountValidator;
        this.analytics = analytics;
        minimumTransferAmountValidator.setMinimumTransferAmount(Constants.MONEY_TRANSFER_MIN_LIMIT);
        this.paymentRequestsUseCase = paymentRequestsUseCase;
        this.amountLimitValidator = amountLimitValidator;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

    void getAccounts() {
        if (view == null) {
            return;
        }
        getRequesterAccountsUseCase.execute()
                .compose(bindToLifecycle())
                .flatMapIterable(accountViewModels -> accountViewModels)
                .toList()
                .subscribe(payAccountsList -> {
                    if (payAccountsList != null && payAccountsList.size() > 0) {
                        for (Iterator<AccountViewModel> iterator = payAccountsList.iterator(); iterator.hasNext(); ) {
                            AccountViewModel accountViewModel = iterator.next();
                            if (isAccountTypeCriteriaMatches(accountViewModel)) {
                                iterator.remove();
                            }
                        }
                        if (view != null) {
                            setAccountContainerDtoList(payAccountsList);
                            view.setAccounts(payAccountsList);
                        }

                    } else {
                        if (view != null) {
                            view.showAccountsErrorView();
                            view.trackApiFailure(za.co.nedbank.core.ApiAliasConstants.PM_FCH_ACC);
                        }
                    }
                }, throwable -> {
                    analytics.trackFailure(false, AppTracking.TAG_PAY_ME_FAILURE, za.co.nedbank.core.ApiAliasConstants.PM_FCH_ACC, mErrorHandler.getErrorMessage(throwable).getMessage(), null);
                    handleException(throwable);
                });
    }

    private boolean isAccountTypeCriteriaMatches(AccountViewModel accountViewModel){
        return !accountViewModel.isAllowCredits() || accountViewModel.isRestricted()
                || (!Constants.ACCOUNT_TYPES.CA.getAccountTypeCode().equalsIgnoreCase(accountViewModel.getAccountType()))
                && !Constants.ACCOUNT_TYPES.SA.getAccountTypeCode().equalsIgnoreCase(accountViewModel.getAccountType())
                && !Constants.ACCOUNT_TYPES.CC.getAccountTypeCode().equalsIgnoreCase(accountViewModel.getAccountType());
    }

    void trackApiFailure(String errorMessage, String apiName) {
        analytics.trackFailure(true, AppTracking.TAG_PAY_ME_FAILURE, apiName, errorMessage, null);
    }

    private void setAccountContainerDtoList(List<AccountViewModel> accountsViewModelList) {
        this.accountViewModelList = accountsViewModelList;
    }

    private void handleException(final Throwable throwable) {
        handleError(mErrorHandler.getErrorMessage(throwable).getMessage());
    }

    private void handleError(final String message) {
        if (view != null) {
            view.showError(message);
        }
    }

    void checkInputForAllFields(final ValidatableInput<String> amountInput, String reason) {
        if (view != null) {
            if (isInputAmountOk(amountInput) && isReasonFieldOk(reason)) {
                view.setNextButtonEnabled(true);
            } else {
                view.setNextButtonEnabled(false);
            }
        }
    }

    private boolean isInputAmountOk(final ValidatableInput<String> amountInput) {
        amountLimitValidator.setLimit(Constants.MONEY_TRANSFER_MAX_LIMIT);
        final boolean isAmountWithinLimits = validate(amountInput, amountLimitValidator);
        boolean isAmountValid = minimumTransferAmountValidator.validateInput(amountInput.getValue()).isOk();
        return isAmountWithinLimits && isAmountValid && mNonEmptyTextValidator.validateInput(amountInput.getValue()).isOk();
    }

    private boolean isReasonFieldOk(String reason) {
        return mNonEmptyTextValidator.validateInput(reason).isOk();
    }


    void handleSendClick(String referenceNumber, String recipientName) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.MONEY_REQUEST_SUCCESS);
        navigationTarget.withParam(NavigationTarget.PARAM_REFERENCE_NUMBER, referenceNumber);
        navigationTarget.withParam(NavigationTarget.PARAM_RECIPIENT_NAME, recipientName);

        mNavigationRouter.navigateTo(navigationTarget);
    }

    List<AccountViewModel> getToAccounts() {
        return accountViewModelList;
    }


    void sendPaymentRequest(final ValidatableInput<String> amountInput, int mToSelectedPosition, String recipientName, String recipientPhoneNumber, final ValidatableInput<String> descriptionInput) {
        if (view == null) {
            return;
        }
        List<AccountViewModel> toAccountsList = getToAccounts();
        view.showLoadingOnButton(true);
        if (toAccountsList != null && mToSelectedPosition < toAccountsList.size()) {
            PaymentRequestDataModel paymentRequestDataModel = new PaymentRequestDataModel();
            String amount = amountInput.getValue().substring(1).trim();
            amount = amount.replaceAll("\\s", StringUtils.EMPTY);
            if (!TextUtils.isEmpty(amount)) {
                paymentRequestDataModel.setAmount(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(amount)));
            }
            paymentRequestDataModel.setDescription(descriptionInput.getValue());
            paymentRequestDataModel.setItemAccountId(toAccountsList.get(mToSelectedPosition).getItemAccountId());
            paymentRequestDataModel.setPayerName(toAccountsList.get(mToSelectedPosition).getDisplayAccountName());
            paymentRequestDataModel.setPayerPhoneNumber(recipientPhoneNumber);
            paymentRequestDataModel.setPayerName(recipientName);

            paymentRequestsUseCase
                    .execute(paymentRequestDataModel)
                    .compose(bindToLifecycle())
                    .subscribe(
                            this::sendMoneyRequest,
                            throwable -> {
                                if (view != null) {
                                    Error error = mErrorHandler.getErrorMessage(throwable);
                                    view.showLoadingOnButton(false);
                                    view.showError(error.getMessage());
                                }
                                analytics.trackFailure(false, AppTracking.TAG_PAY_ME_FAILURE, za.co.nedbank.core.ApiAliasConstants.PM_RQ, mErrorHandler.getErrorMessage(throwable).getMessage(), null);
                            }
                    );
        }
    }

    private void sendMoneyRequest(PaymentResponseModel paymentResponseModel) {
        if (view != null) {
            if (paymentResponseModel != null
                    && StringUtils.equals(paymentResponseModel.getMetadata().getResultData().get(0).getResultDetail().get(0).getResult(), za.co.nedbank.services.Constants.SUCCESS)) {
                view.sendMoneyRequestSuccess(paymentResponseModel);
                analytics.sendEvent(AppTracking.TAG_PAY_ME_SUCCESSFUL, StringUtils.EMPTY, StringUtils.EMPTY);
            } else {
                view.sendMoneyRequestFailure(paymentResponseModel);
                view.trackApiFailure(za.co.nedbank.core.ApiAliasConstants.PM_RQ);
            }
        }
    }
}

