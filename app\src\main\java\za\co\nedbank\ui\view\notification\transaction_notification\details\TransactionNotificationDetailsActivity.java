package za.co.nedbank.ui.view.notification.transaction_notification.details;

import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.view.WindowInsetsController;
import android.view.WindowManager;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.LinearLayout;

import androidx.core.text.HtmlCompat;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.databinding.ActivityTransNotificationDetailsBinding;
import za.co.nedbank.databinding.LayoutRichContentLandBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.notification.notification_details.CustomChromeClient;
import za.co.nedbank.uisdk.component.CompatButton;

public class TransactionNotificationDetailsActivity extends NBBaseActivity implements TransactionNotificationDetailsView, View.OnClickListener {

    private final int MAX_LENGTH = 25;
    private final int SUBSTRING_LENGTH = 22;

    @Inject
    TransactionNotificationDetailsPresenter mTransactionNotificationDetailsPresenter;

    private FBNotificationsViewModel mFBNotificationsViewModel;

    private FBTransactionNotificationsViewModel mFBTransactionNotificationsViewModel;
    private ActivityTransNotificationDetailsBinding activityTransNotificationDetailsBinding;
    private LayoutRichContentLandBinding layoutRichContentLandBinding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        int orientation = getResources().getConfiguration().orientation;
        activityTransNotificationDetailsBinding = ActivityTransNotificationDetailsBinding.inflate(getLayoutInflater());
        layoutRichContentLandBinding = LayoutRichContentLandBinding.inflate(getLayoutInflater());
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            setContentView(activityTransNotificationDetailsBinding.getRoot());
            initToolbar(activityTransNotificationDetailsBinding.toolbar, false, getResources().getString(R.string.transaction_notification));
            activityTransNotificationDetailsBinding.imgClose.setOnClickListener(v -> onCrossClick());
        } else {
            setContentView(layoutRichContentLandBinding.getRoot());
            handleFullScreen();
        }
        getDataFromIntent();
        setupUI();
        mTransactionNotificationDetailsPresenter.bind(this);
    }

    @SuppressWarnings("deprecation")
    private void handleFullScreen() {
        final WindowInsetsController insetsController;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            insetsController = getWindow().getInsetsController();
            if (insetsController != null) {
                insetsController.hide(WindowInsets.Type.statusBars());
                insetsController.hide(WindowInsets.Type.navigationBars());
            }
        } else {
            //noinspection deprecation
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }

    private void setupUI() {
        if (mFBNotificationsViewModel != null) {
            mTransactionNotificationDetailsPresenter.sendAnalyticsToServer(mFBNotificationsViewModel);
            if (isBroadcastorInfo()) {
                if (Boolean.FALSE.equals(mFBNotificationsViewModel.getAllowAnonymous())) {
                    mTransactionNotificationDetailsPresenter.clearNotificationData(mFBNotificationsViewModel.getNotificationId());
                }
                populateBroadcastsUI();
            } else {
                populateTransactionUI();
            }
        }
    }

    private boolean isBroadcastorInfo() {
        if (mFBNotificationsViewModel != null && mFBNotificationsViewModel.getDistributionType() != null) {
            return mFBNotificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.UBC) || mFBNotificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.ECERT) || mFBNotificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.TBC) || mFBNotificationsViewModel.getNotificationType().equalsIgnoreCase(NotificationConstants.NOTIFICATION_TYPES.INFO) || Boolean.TRUE.equals(mFBNotificationsViewModel.getAllowAnonymous());
        } else return false;
    }

    private void populateBroadcastsUI() {
        if (activityTransNotificationDetailsBinding.notificationDetailsContent != null) {
            activityTransNotificationDetailsBinding.notificationDetailsContent.setVisibility(View.VISIBLE);
            activityTransNotificationDetailsBinding.broadcastDetails.setVisibility(View.VISIBLE);
            activityTransNotificationDetailsBinding.title.setText(getResources().getString(R.string.money_title));
            activityTransNotificationDetailsBinding.broadcastHeader.setText(mFBNotificationsViewModel.getHeading());
            activityTransNotificationDetailsBinding.broadcastSubheader.setText(getFormattedText(mFBNotificationsViewModel.getSubHeading()));
            activityTransNotificationDetailsBinding.broadcastContent.setText(getFormattedText(mFBNotificationsViewModel.getBody()));
            activityTransNotificationDetailsBinding.imgClose.setVisibility(View.VISIBLE);
            List<CompatButton> buttonList = createButtons(mFBNotificationsViewModel.getResponseOptions());
            for (CompatButton button : buttonList) {
                activityTransNotificationDetailsBinding.broadcastDetails.addView(button);
            }
        }
        setUpRichContent();
    }

    private CharSequence getFormattedText(String text) {
        return HtmlCompat.fromHtml(text, HtmlCompat.FROM_HTML_MODE_LEGACY);
    }

    private void setUpRichContent() {
        List<FBNotificationsViewModel.RichContent> richContents = mFBNotificationsViewModel.getRichContent();
        if (richContents != null && !richContents.isEmpty() && richContents.get(0).getContentType().equalsIgnoreCase(NotificationConstants.CONTENT_TYPE.VIDEO)) {
            setRichContentVideo(richContents.get(0).getSourcePath(), richContents.get(0).getDisplayType());
        }
    }


    private void setRichContentVideo(String mediaUrl, String displayType) {
        WebView webView = layoutRichContentLandBinding.webViewVideo;
        if (!isLandscape()) {
            if (displayType.equalsIgnoreCase(NotificationConstants.DISPLAY_TYPE_FOOTER)) {
                activityTransNotificationDetailsBinding.dataUsageAlertContainer.setVisibility(View.VISIBLE);
                webView = activityTransNotificationDetailsBinding.webViewVideoFooter;
                ViewUtils.showViews(activityTransNotificationDetailsBinding.webViewVideoFooter);
            }
        } else {
            webView = layoutRichContentLandBinding.webViewVideo;
            ViewUtils.showViews(layoutRichContentLandBinding.webViewVideo);
        }

        webView.setWebViewClient(new BrowserHome());
        webView.setWebChromeClient(new CustomChromeClient(this));
        WebSettings webSettings = layoutRichContentLandBinding.webViewVideo.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setAllowFileAccess(true);
        webView.setScrollBarStyle(View.SCROLLBARS_INSIDE_OVERLAY);
        webView.getSettings().setJavaScriptCanOpenWindowsAutomatically(true);
        webView.getSettings().setMediaPlaybackRequiresUserGesture(false);
        String path = "<iframe src='" + mediaUrl + "' width='100%' height='100%' style='border: none;'allowfullscreen></iframe>";
        webView.loadData(path, "text/html", "utf-8");
        webView.setVisibility(View.VISIBLE);

    }

    public boolean isLandscape() {
        return getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;
    }

    private class BrowserHome extends WebViewClient {
        BrowserHome() {
        }

    }

    private List<CompatButton> createButtons(List<FBNotificationsViewModel.ResponseOption> responseOptions) {
        List<CompatButton> buttonList = new ArrayList<>();
        if (responseOptions != null) {
            for (int i = 0; i < responseOptions.size(); i++) {

                FBNotificationsViewModel.ResponseOption responseOption = responseOptions.get(i);
                CompatButton button = new CompatButton(this);
                button.setText(responseOption.getLabel());


                button.setTag(responseOption);
                button.setOnClickListener(this);
                LinearLayout.LayoutParams param = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                int leftRight = getResources().getDimensionPixelSize(R.dimen.dimen_20dp);
                int top = getResources().getDimensionPixelSize(R.dimen.dimen_16dp);
                param.setMargins(leftRight, top, leftRight, 0);
                button.setLayoutParams(param);

                CompatButton.ButtonType buttonType;
                String responseType = responseOption.getType();
                if ((responseType.equalsIgnoreCase(NotificationConstants.RESPONSE_TYPES.RESPONSE_PRIMARY_BUTTON)) || (responseType.equalsIgnoreCase(NotificationConstants.RESPONSE_TYPES.RESPONSE_PRIMARY_URL)) ||
                        (responseType.equalsIgnoreCase(NotificationConstants.RESPONSE_TYPES.RESPONSE_PRIMARY_APP_LINK))) {
                    buttonType = CompatButton.ButtonType.PRIMARY;
                    button.setButtonType(buttonType);
                    buttonList.add(0, button);

                } else {
                    buttonType = CompatButton.ButtonType.SECONDARY;
                    button.setButtonType(buttonType);
                    buttonList.add(button);
                }

            }

        }
        if (!buttonList.isEmpty()) {
            CompatButton button = buttonList.get(buttonList.size() - 1);
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) button.getLayoutParams();
            params.bottomMargin = getResources().getDimensionPixelSize(R.dimen.dimen_20dp);
            button.setLayoutParams(params);
        }
        return buttonList;
    }

    private void populateTransactionUI() {
        activityTransNotificationDetailsBinding.title.setText(getResources().getString(R.string.transaction_notification));
        activityTransNotificationDetailsBinding.transDetails.setVisibility(View.VISIBLE);
        activityTransNotificationDetailsBinding.imgClose.setVisibility(View.VISIBLE);
        mFBTransactionNotificationsViewModel = mTransactionNotificationDetailsPresenter.getFBTransactionNotificationViewModel(mFBNotificationsViewModel);
        String amount = FormattingUtil.convertToSouthAfricaFormattedCurrency(mFBTransactionNotificationsViewModel.getMetaAmountInDouble());
        activityTransNotificationDetailsBinding.transactionAmount.setText(amount);
        if (mFBTransactionNotificationsViewModel.getMeta() != null) {
            String date = FormattingUtil.getFormattedDateSAtoLocal(mFBTransactionNotificationsViewModel.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS, FormattingUtil.DATE_FORMAT_DD_SPACE_MMM_SPACE_YYYY);
            String time = FormattingUtil.getFormattedDateSAtoLocal(mFBTransactionNotificationsViewModel.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS, FormattingUtil.TIME_IN_HH_MM);
            activityTransNotificationDetailsBinding.transactionDate.setText(String.format("%s at %S", date, time));
        }

        activityTransNotificationDetailsBinding.details.tvAccountDetail.setText(getDisplayNumber(mFBTransactionNotificationsViewModel.getMetaAccNumber()));
        activityTransNotificationDetailsBinding.details.tvTransactionType.setText(mFBTransactionNotificationsViewModel.getMetaTransType());

        if (mFBTransactionNotificationsViewModel.getMetaChannelName().length() > MAX_LENGTH) {
            activityTransNotificationDetailsBinding.transactionName.setText(String.format("%s%s%s%s", mFBTransactionNotificationsViewModel.getMetaChannelName().substring(0, SUBSTRING_LENGTH), StringUtils.DOT, StringUtils.DOT, StringUtils.DOT));
        } else {
            activityTransNotificationDetailsBinding.transactionName.setText(mFBTransactionNotificationsViewModel.getMetaChannelName());
        }

        //Hide view if value is null or empty
        if (StringUtils.isNullOrEmpty(mFBTransactionNotificationsViewModel.getMetaCardnumber())) {
            ViewUtils.hideViews(activityTransNotificationDetailsBinding.details.lblCardNumber, activityTransNotificationDetailsBinding.details.tvCardNumber);
        } else {
            ViewUtils.showViews(activityTransNotificationDetailsBinding.details.lblCardNumber, activityTransNotificationDetailsBinding.details.tvCardNumber);
            activityTransNotificationDetailsBinding.details.tvCardNumber.setText(getDisplayNumber(mFBTransactionNotificationsViewModel.getMetaCardnumber()));
        }

        if (StringUtils.isNullOrEmpty(mFBTransactionNotificationsViewModel.getMetaLocation())) {
            ViewUtils.hideViews(activityTransNotificationDetailsBinding.details.lblLocation, activityTransNotificationDetailsBinding.details.tvLocation);
        } else {
            ViewUtils.showViews(activityTransNotificationDetailsBinding.details.lblLocation, activityTransNotificationDetailsBinding.details.tvLocation);
            activityTransNotificationDetailsBinding.details.tvLocation.setText(mFBTransactionNotificationsViewModel.getMetaLocation());
        }
        if (StringUtils.isNullOrEmpty(mFBTransactionNotificationsViewModel.getMetaNarrative())) {
            ViewUtils.hideViews(activityTransNotificationDetailsBinding.details.lblNarrative, activityTransNotificationDetailsBinding.details.tvNarrative);
        } else {
            ViewUtils.showViews(activityTransNotificationDetailsBinding.details.lblNarrative, activityTransNotificationDetailsBinding.details.tvNarrative);
            activityTransNotificationDetailsBinding.details.tvNarrative.setText(mFBTransactionNotificationsViewModel.getMetaNarrative());
        }
        addResponseOptions(mFBTransactionNotificationsViewModel);

    }


    private void addResponseOptions(FBTransactionNotificationsViewModel model) {
        if (model.getResponseOptions() != null && !model.getResponseOptions().isEmpty()) {
            for (int i = 0; i < model.getResponseOptions().size(); i++) {
                String txType = mFBTransactionNotificationsViewModel.getMetaTransType();
                switch (txType) {
                    case NotificationConstants.TRANSACTION_TYPES.DEBIT_ORDER:
                        activityTransNotificationDetailsBinding.reportFraudContainer.setVisibility(View.VISIBLE);
                        activityTransNotificationDetailsBinding.infoTxt.setText(this.getResources().getString(R.string.debit_order_info_txt));
                        break;
                    case NotificationConstants.TRANSACTION_TYPES.PAYMENT:
                    case NotificationConstants.TRANSACTION_TYPES.PREPAID:
                    case NotificationConstants.TRANSACTION_TYPES.PURCHASE:
                    case NotificationConstants.TRANSACTION_TYPES.WITHDRAWAL:
                    case NotificationConstants.TRANSACTION_TYPES.MPESA:
                    case NotificationConstants.TRANSACTION_TYPES.INTERBANK_EFT:
                    case NotificationConstants.TRANSACTION_TYPES.PREPAID_AIRTIME:
                    case NotificationConstants.TRANSACTION_TYPES.PREPAID_ELECTRICITY:
                    case NotificationConstants.TRANSACTION_TYPES.LOTTO:
                    case NotificationConstants.TRANSACTION_TYPES.SEND_IMALI:
                    case NotificationConstants.TRANSACTION_TYPES.DEBIT:
                        activityTransNotificationDetailsBinding.reportFraudContainer.setVisibility(View.VISIBLE);
                        activityTransNotificationDetailsBinding.infoTxt.setText(this.getResources().getString(R.string.report_fraud_info_txt));
                        break;
                    default:
                        activityTransNotificationDetailsBinding.reportFraudContainer.setVisibility(View.GONE);

                }
                activityTransNotificationDetailsBinding.navigationLink.setText(model.getResponseOptions().get(0).getLabel());
                activityTransNotificationDetailsBinding.navigationLink.setTag(model.getResponseOptions().get(0));
                activityTransNotificationDetailsBinding.navigationLink.setOnClickListener(this);
            }

        } else {
            activityTransNotificationDetailsBinding.reportFraudContainer.setVisibility(View.GONE);
        }

    }


    private void getDataFromIntent() {
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            mFBNotificationsViewModel = (FBNotificationsViewModel) extras.getSerializable(NavigationTarget.PARAM_TRANS_PUSH_DATA);
        }

    }

    private String getDisplayNumber(final String number) {
        if (number != null && number.trim().length() > 4) {
            String trimmed = number.trim();
            String obfuscated = "**** **** " + trimmed.substring(trimmed.length() - 4, trimmed.length());
            return obfuscated;
        }
        return number;
    }

    @Override
    public void onClick(View v) {
        String action;
        String value;
        String label;
        if (isBroadcastorInfo()) {
            action = ((FBNotificationsViewModel.ResponseOption) v.getTag()).getAction();
            value = ((FBNotificationsViewModel.ResponseOption) v.getTag()).getValue();
            label = ((FBNotificationsViewModel.ResponseOption) v.getTag()).getLabel();
            mTransactionNotificationDetailsPresenter.addInStorage((FBNotificationsViewModel.ResponseOption) v.getTag());
        } else {
            action = ((FBTransactionNotificationsViewModel.ResponseOption) v.getTag()).getAction();
            value = ((FBTransactionNotificationsViewModel.ResponseOption) v.getTag()).getValue();
            label = ((FBTransactionNotificationsViewModel.ResponseOption) v.getTag()).getLabel();
        }
        mTransactionNotificationDetailsPresenter.sendUserResponseToServer(value, label);
        mTransactionNotificationDetailsPresenter.handleOnClick(action);
    }

    @Override
    public void onLoggedIn() {
        navigateToNextScreen();
    }

    public void navigateToNextScreen() {
        if(mFBTransactionNotificationsViewModel == null) return;

        List<FBTransactionNotificationsViewModel.ResponseOption> responseOptions = mFBTransactionNotificationsViewModel.getResponseOptions();
        if (responseOptions != null && !responseOptions.isEmpty() && responseOptions.get(0).getAction() != null) {
            String action = responseOptions.get(0).getAction();
            if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.REPORT_FRAUD)) {
                mTransactionNotificationDetailsPresenter.navigateToReportFraud(mFBTransactionNotificationsViewModel);
            }
        }
    }

    @Override
    public void closeScreen() {
        onCrossClick();
    }

    public void onCrossClick() {
        if (isBroadcastorInfo() && Boolean.TRUE.equals(mFBNotificationsViewModel.getAllowAnonymous())) {
            mTransactionNotificationDetailsPresenter.clearNotificationData(mFBNotificationsViewModel.getNotificationId());
        }
        if (Build.VERSION.SDK_INT >= 16 && Build.VERSION.SDK_INT < 21) {
            finishAffinity();
            close();
        } else if (Build.VERSION.SDK_INT >= 21) {
            finishAndRemoveTask();
            close();
            int pid = android.os.Process.myPid();
            android.os.Process.killProcess(pid);
        }
    }

    @Override
    public void onBackPressed() {
        onCrossClick();
    }
}
