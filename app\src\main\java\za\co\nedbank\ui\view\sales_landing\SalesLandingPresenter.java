package za.co.nedbank.ui.view.sales_landing;

import static za.co.nedbank.core.Constants.FLOW_CONSTANTS.CREDIT_CARD_FLOW;
import static za.co.nedbank.core.data.storage.StorageKeys.IS_NEW_TO_BANK;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_FEATURE_NAME;
import static za.co.nedbank.enroll_v2.Constants.BundleKeys.FLOW_JOURNEY_FLAG;

import java.util.HashMap;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.common.MoreType;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.deeplink.notification.NotificationAdapter;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.view.media_content.DynamicFeatureCardDetailEnum;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.core.deeplink.DeeplinkUtils;

public class SalesLandingPresenter extends NBBasePresenter<SalesLandingView> {
    private final NavigationRouter mNavigationRouter;
    private final Analytics mAnalytics;
     final ApplicationStorage inMemoryStorage;
    private final NotificationAdapter notificationAdapter;
     final FeatureSetController mFeatureSetController;

    private final ApplicationStorage mApplicationStorage;
    @Inject
    SalesLandingPresenter(final NavigationRouter navigationRouter,
                          Analytics mAnalytics, final ApplicationStorage mApplicationStorage,
                          @Named("memory") ApplicationStorage inMemoryStorage,
                          final FeatureSetController featureSetController,
                          final NotificationAdapter notificationAdapter
    ) {
        this.mNavigationRouter = navigationRouter;
        this.mAnalytics = mAnalytics;
        this.mApplicationStorage = mApplicationStorage;
        this.inMemoryStorage = inMemoryStorage;
        this.mFeatureSetController = featureSetController;
        this.notificationAdapter = notificationAdapter;
    }


    @Override
    protected void onBind() {
        super.onBind();
        mApplicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
        HashMap cdata = new HashMap<String, Object>();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_USER_JOURNEY, TrackingParam.VAL_NEW_TO_NEDBANK_ON_BOARDING);
        mAnalytics.sendEventStateWithMap(TrackingEvent.ANALYTICS.TAG_NEDBANK_NTF, cdata);
    }

    void navigateToMore() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.LOGIN_MORE)
                .withParam(Constants.BUNDLE_KEYS.MORE_SCREEN_TYPE, MoreType.pre_login));
    }


    void showNonSaClientUI() {
        if (view != null) {
            view.showNonSAUserUI();
        }
    }

    public void checkAndShowFeatureDeepLinkScreen(String featureName) {
        if (DeeplinkUtils.canMoveToDeeplinkFramework(featureName)) {
            notificationAdapter.fetchDataFromNotification(featureName, null, null);
        } else if (featureName.equalsIgnoreCase(DynamicFeatureCardDetailEnum.CREDIT_CARD.getName())) {
            navigateToApplyCreditCard();
        } else if (featureName.equalsIgnoreCase(DynamicFeatureCardDetailEnum.PRODUCT_ONBOARDING.getName())) {
            navigateToDeepLinkIntermediateScreen(featureName);
        }
        //clear the feature value
        mApplicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW);
    }


    void navigateToDeepLinkIntermediateScreen(String featureName) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.DEEP_LINK_INTERMEDIATE_SCREEN)
                .withParam(PARAM_EXTRA_FEATURE_NAME, featureName);
        mNavigationRouter.navigateWithResult(navigationTarget);// clearing the value
        mApplicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW);
    }

    public void navigateToApplyCreditCard() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.CREDIT_CARD_FACILITY)) {
            NavigationTarget navigationTarget = NavigationTarget.to(EnrollV2NavigatorTarget.BROWSE_CREDIT_CARD_ACTIVITY);
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FROM_APPLY_JOURNEY, CREDIT_CARD_FLOW);
            navigationTarget.withParam(NavigationTarget.PARAM_NTF_SECOND_LOGIN, (mApplicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) != FicaWorkFlow.IN_APP));
            navigationTarget.withParam(NavigationTarget.IS_DEEPLINK, true);
            navigationTarget.withParam(NavigationTarget.PARAM_NTF, inMemoryStorage.getBoolean(IS_NEW_TO_BANK, false));
            mNavigationRouter.navigateTo(navigationTarget);
        } else {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.PREAPPROVED_OFFERS_APPLY_ACTIVITY);
            navigationTarget.withParam(FLOW_JOURNEY_FLAG, CREDIT_CARD_FLOW);
            navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.NFPCreditCardParam.IS_PRE_LOGIN,  (mApplicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) != FicaWorkFlow.IN_APP));
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FROM_APPLY_JOURNEY, CREDIT_CARD_FLOW);
            mNavigationRouter.navigateTo(navigationTarget);
        }
        //add the in app flow
        mApplicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
        inMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
    }
}