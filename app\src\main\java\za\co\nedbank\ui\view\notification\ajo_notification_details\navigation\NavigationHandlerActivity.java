package za.co.nedbank.ui.view.notification.ajo_notification_details.navigation;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.BundleUtils;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.databinding.ActivityNavigationHandlerBinding;
import za.co.nedbank.enroll_v2.view.login.LoginActivity;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;

public class NavigationHandlerActivity extends NBBaseActivity implements NavigationHandlerView {

    ActivityNavigationHandlerBinding mBinding;
    @Inject
    public NavigationHandlerPresenter navigationHandlerPresenter;
    private String target;
    private String from;
    private HashMap<String,String> paramHashmap;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        receiveBundle();
        mBinding = ActivityNavigationHandlerBinding.inflate(getLayoutInflater());
        setContentView(mBinding.getRoot());
        navigationHandlerPresenter.bind(this);
        handleNavigation();
    }

    private void receiveBundle() {
        if (getIntent() != null && getIntent().getExtras() != null) {
            Bundle extras = getIntent().getExtras();
            if (extras.containsKey(NotificationConstants.Navigation.TARGET)) {
                target = extras.getString(NotificationConstants.Navigation.TARGET, StringUtils.EMPTY_STRING);
            }
            if (extras.containsKey(NotificationConstants.Navigation.FROM)) {
                from = extras.getString(NotificationConstants.Navigation.FROM, NotificationConstants.AjoConstants.AJO_IN_APP);
            }
            if (extras.containsKey(NotificationConstants.Navigation.PARAM)) {
                paramHashmap = BundleUtils.getSerializable(extras,NotificationConstants.Navigation.PARAM,HashMap.class);
            }
        }
    }

    public void handleNavigation() {
        mBinding.progressBar.setVisibility(View.VISIBLE);

        if (!StringUtils.isNullOrEmpty(target)) {
            if (navigationHandlerPresenter.isLoggedIn()) {
                AjoPushPayloadDataModel.ActionButton action = new AjoPushPayloadDataModel.ActionButton();
                action.setLink(target);
                navigationHandlerPresenter.handleOnClick(action);
                navigationHandlerPresenter.clearNavigationData();
            } else {
                List<Intent> mIntents = new ArrayList<>();
                final Intent appLaunchIntent = getPackageManager().getLaunchIntentForPackage(getPackageName());
                mIntents.add(appLaunchIntent);
                if (navigationHandlerPresenter.canNavigateToLogin()) {
                    Intent mTargetIntent = new Intent(this, LoginActivity.class);
                    mTargetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    mIntents.add(mTargetIntent);
                }
                memoryApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.AJO_NOTIFICATION_LINK, target);
                startActivities(mIntents.toArray(new Intent[]{}));
                close();
            }
        } else close();
    }

    @Override
    protected void onDestroy() {
        navigationHandlerPresenter.unbind();
        super.onDestroy();
    }

    @Override
    public void startBrowser(String url) {
        IntentUtils.openDefaultBrowser(this, url);
    }

    @Override
    public boolean fromScreen(@NonNull String screenName) {
        if (StringUtils.isNullOrEmpty(from))
            from = NotificationConstants.AjoConstants.AJO_IN_APP;
        return from.equalsIgnoreCase(screenName);
    }

    @Override
    public void setResult(Intent result) {
        setResult(RESULT_OK, result);
        close();
    }

    @Override
    public HashMap<String,String>  getParamHashmap() {
        return paramHashmap;
    }

    @Override
    public String getString(String id) {
        String string=null;
        if (NotificationConstants.NAVIGATION_TARGET.OPEN_AVO_IN_APP.equals(id)) {
            string = getString(R.string.data_usage_name_avo);
        }
        return string;
    }

    @Override
    public String getStringRes(int stringRes) {
        return getResources().getString(stringRes);
    }

    @Override
    public String getActivityLabel() {
        return getString(R.string.data_usage_name_avo);
    }
}
