package za.co.nedbank.ui.view.notification.notification_messages;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.FBNotificationMultipleAnalyticData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsAnalyticData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsGetRequestData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsMetaData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsResponseData;
import za.co.nedbank.core.domain.model.notifications.FBResponseData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.notifications.FBNotificationsAnalyticsUseCase;
import za.co.nedbank.core.domain.usecase.notifications.FBNotificationsMultipleAnalyticsUseCase;
import za.co.nedbank.core.domain.usecase.notifications.GetFBNotificationsDataUseCase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationEvent;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.core.view.mapper.fbnotifications.FBNotificationsDataToViewModelMapper;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;
import za.co.nedbank.ui.view.notification.notification_details.MarkReadNotificationEvent;

public class NotificationMessagesPresenter extends NBBasePresenter<NotificationMessagesView> {

    public static final String TAG = NotificationMessagesPresenter.class.getSimpleName();
    private final GetFBNotificationsDataUseCase mGetNotificationUseCase;
    private final FBNotificationsAnalyticsUseCase mFBNotificationsAnalyticsUseCase;
    private final FBNotificationsMultipleAnalyticsUseCase mFbNotificationsMultipleAnalyticsUseCase;
    private final ErrorHandler mErrorHandler;
    private NavigationRouter mNavigationRouter;
    private FBNotificationsDataToViewModelMapper mFbNotificationsDataToViewModelMapper;
    private ApplicationStorage mMemoryApplicationStorage;
    private final ApplicationStorage applicationStorage;
    private final Analytics mAnalytics;

    @Inject
    public NotificationMessagesPresenter(NavigationRouter navigationRouter,
                                         GetFBNotificationsDataUseCase getNotificationUseCase,
                                         ErrorHandler errorHandler,
                                         FBNotificationsDataToViewModelMapper fbNotificationsDataToViewModelMapper,
                                         FBNotificationsAnalyticsUseCase fbNotificationsAnalyticsUseCase,
                                         FBNotificationsMultipleAnalyticsUseCase fbNotificationsMultipleAnalyticsUseCase,
                                         @Named("memory") ApplicationStorage memoryApplicationStorage,
                                         final ApplicationStorage applicationStorage,
                                         final Analytics analytics) {
        this.mNavigationRouter = navigationRouter;
        this.mGetNotificationUseCase = getNotificationUseCase;
        this.mFBNotificationsAnalyticsUseCase = fbNotificationsAnalyticsUseCase;
        this.mFbNotificationsMultipleAnalyticsUseCase = fbNotificationsMultipleAnalyticsUseCase;
        this.mErrorHandler = errorHandler;
        this.mFbNotificationsDataToViewModelMapper = fbNotificationsDataToViewModelMapper;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.applicationStorage = applicationStorage;
        this.mAnalytics = analytics;
    }

    @Override
    protected void onBind() {
        super.onBind();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onUnbind() {
        super.onUnbind();
        EventBus.getDefault().unregister(this);
    }

    void loadMessages(int pageNo) {
        FBNotificationsGetRequestData fbNotificationsGetRequestData = new FBNotificationsGetRequestData();
        fbNotificationsGetRequestData.setPageNum(pageNo);
        fbNotificationsGetRequestData.setNumOfRecords(NotificationConstants.PAGINATION.DEFAULT_PAGE_SIZE);
        fbNotificationsGetRequestData.setType(NotificationConstants.NOTIFICATION_TYPES.GENERAL);

        mGetNotificationUseCase.execute(fbNotificationsGetRequestData)
                .compose(bindToLifecycle())
                .subscribe(fbNotificationsResponseData -> {
                    handleNotificationResponse(fbNotificationsResponseData, pageNo);
                    view.toggleEmptyView();

                }, throwable -> {
                    if (view != null) {
                        view.showErrorForNotificationMessages(mErrorHandler.getErrorMessage(throwable).getMessage());
                    }
                });

    }


    void loadMessages() {
        FBNotificationsGetRequestData fbNotificationsGetRequestData = new FBNotificationsGetRequestData();
        fbNotificationsGetRequestData.setPageNum(NotificationConstants.PAGINATION.DEFAULT_PAGE_NO);
        fbNotificationsGetRequestData.setNumOfRecords(NotificationConstants.PAGINATION.DEFAULT_PAGE_SIZE);
        fbNotificationsGetRequestData.setType(NotificationConstants.NOTIFICATION_TYPES.GENERAL);

        mGetNotificationUseCase.execute(fbNotificationsGetRequestData)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgress(true);
                    }
                })
                .doFinally(() -> {
                    if (view != null) {
                        view.showProgress(false);
                    }
                })
                .subscribe(fbNotificationsResponseData -> {
                    handleNotificationResponse(fbNotificationsResponseData, NotificationConstants.PAGINATION.DEFAULT_PAGE_NO);
                    view.toggleEmptyView();
                    view.showListLoaded();

                }, throwable -> {
                    if (view != null) {
                        view.showFullScreenError();
                    }
                });
    }

    private void handleNotificationResponse(FBNotificationsResponseData fbNotificationsResponseData, int pageNo) {
        int ONLY_SUCCESS_ELEMENT = 1;
        if (fbNotificationsResponseData != null && fbNotificationsResponseData.getMetaData() != null) {
            FBNotificationsMetaData metaDataModel = fbNotificationsResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && resultDetailList.size() > 0) {
                    if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        // success case
                        handleSuccess(fbNotificationsResponseData, pageNo);
                        break;
                    } else {
                        handleError(resultDetailList);
                    }
                }
            }
        }
    }

    private void handleError(ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                NBLogger.e(TAG, resultDetailViewModel.getReason());
            }
        }
    }

    private void handleSuccess(FBNotificationsResponseData fbNotificationsResponseData, int pageNo) {
        if (view != null) {
            if (fbNotificationsResponseData.getFbNotificationsDataList() != null) {
                List<FBNotificationsViewModel> items = mFbNotificationsDataToViewModelMapper.transform(fbNotificationsResponseData.getFbNotificationsDataList());
                if (pageNo == NotificationConstants.PAGINATION.DEFAULT_PAGE_NO) {
                    view.updateNotificationMessages(items);
                } else {
                    view.updateNotificationMessagesPageData(items);
                }
            }
        }
    }


    public String getDeviceID() {
        return applicationStorage.getString(NotificationConstants.STORAGE_KEYS.UUID, StringUtils.EMPTY_STRING);
    }

    private String getDate() {
        SimpleDateFormat dateFormatForParsing = new SimpleDateFormat(FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
        dateFormatForParsing.setTimeZone(TimeZone.getTimeZone(FormattingUtil.SOUTH_AFRICA_TIMZONE_ID));
        return dateFormatForParsing.format(new Date());
    }


    void navigateToMessageDetails(FBNotificationsViewModel fbNotificationsViewModel) {
        switch (fbNotificationsViewModel.getNotificationType()) {
            case NotificationConstants.NOTIFICATION_TYPES.EMPTY:
                NavigationTarget navigationTargetInviteAndAcceptCondition = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_INVITE_AND_ACCEPT_FAMILY_BANKING)
                        .withParam(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL, fbNotificationsViewModel);
                mNavigationRouter.navigateTo(navigationTargetInviteAndAcceptCondition);
                break;
            case NotificationConstants.NOTIFICATION_TYPES.OPEN_ACCOUNT:
                NavigationTarget navigationTargetInviteAndOpenAccount = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_INVITE_AND_OPEN_ACCOUNT_FAMILY_BANKING)
                        .withParam(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL, fbNotificationsViewModel);
                mNavigationRouter.navigateTo(navigationTargetInviteAndOpenAccount);
                break;
            default:
                NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_DETAILS)
                        .withParam(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL, fbNotificationsViewModel);
                mNavigationRouter.navigateTo(navigationTarget);
                break;
        }
    }

    void markRead(FBNotificationsViewModel fbNotificationsViewModel) {
        if (!fbNotificationsViewModel.isRead()) {
            fbNotificationsViewModel.setIsRead(true);
        }
        view.refreshItem(fbNotificationsViewModel);

    }

    private void changeReadCounterBy(int readCount) {
        int unreadCount = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, 0);
        int unreadTotalCount = applicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, 0);
        mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, unreadCount - readCount);
        applicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, unreadTotalCount - readCount);
    }

    void handleMessageItemClick(FBNotificationsViewModel fbNotificationsViewModel) {
        trackAction(NotificationConstants.TRACKING_PARAMS.VIEW_NOTIFICATION_DETAILS, fbNotificationsViewModel.getNotificationType(), fbNotificationsViewModel.getHeading());
        navigateToMessageDetails(fbNotificationsViewModel);
        markRead(fbNotificationsViewModel);
    }

    void deleteMessage(FBNotificationsViewModel notificationsViewModel) {
        changeReadCounterBy(1);
        if (view != null) {
            view.removeDeletedItem(notificationsViewModel);
            view.showUndoDeleteOption(1);
        }
        mFBNotificationsAnalyticsUseCase.execute(createDeleteAnalyticsRequest(notificationsViewModel))
                .compose(bindToLifecycle())
                .subscribe(analyticsResponseData -> {
                    handleAnalyticsResponse(analyticsResponseData, false);
                }, throwable -> {
                    //  no need to handle background api
                    NBLogger.e(TAG, throwable.getMessage());
                });
    }


    private FBNotificationsAnalyticData createDeleteAnalyticsRequest(FBNotificationsViewModel notificationsViewModel) {

        FBNotificationsAnalyticData fbNotificationsAnalyticData = new FBNotificationsAnalyticData();
        if (notificationsViewModel != null) {
            fbNotificationsAnalyticData.setNotificationId(notificationsViewModel.getNotificationId());
            FBNotificationsAnalyticData.Analytic analytic = new FBNotificationsAnalyticData.Analytic();
            fbNotificationsAnalyticData.setNotificationId(notificationsViewModel.getNotificationId());
            analytic.setDeviceId(getDeviceID());
            analytic.setDeviceDate(getDate());
            analytic.setAnalyticValue(NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_DELETE);
            fbNotificationsAnalyticData.setAnalytic(analytic);
        }

        return fbNotificationsAnalyticData;
    }

    void deleteMessages(List<FBNotificationsViewModel> selectedItems) {

        if (selectedItems.isEmpty()) return;
        changeReadCounterBy(selectedItems.size());
        view.clearDeletedItems();
        view.endSelection();
        view.showUndoDeleteOption(selectedItems.size());
        mFbNotificationsMultipleAnalyticsUseCase.execute(createMultipleDeleteOrUndoRequest(selectedItems, NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_DELETE))

                .compose(bindToLifecycle())
                .subscribe(fbResponseData -> {
                    handleAnalyticsResponse(fbResponseData, true);
                }, throwable -> {
                    //  no need to handle background api
                    NBLogger.e(TAG, throwable.getMessage());
                });
    }

    public void undoDeletedItems(List<FBNotificationsViewModel> recentlyDeletedItems) {
        view.restoreDeletedItems();
        mFbNotificationsMultipleAnalyticsUseCase.execute(createMultipleDeleteOrUndoRequest(recentlyDeletedItems, NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_UNDO))
                .compose(bindToLifecycle())
                .subscribe(fbResponseData -> {
                    //background api no need to handle
                }, throwable -> {
                    //  no need to handle background api
                    NBLogger.e(TAG, throwable.getMessage());
                });
    }

    private void handleAnalyticsResponse(FBResponseData fbResponseData, boolean isMultiple) {
        boolean isFailed = false;
        int ONLY_SUCCESS_ELEMENT = 1;
        if (fbResponseData != null && fbResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = fbResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                isFailed = processResponse(isMultiple, isFailed, ONLY_SUCCESS_ELEMENT, resultData, resultDataModel, resultDetailList);
            }
            if (isFailed && isMultiple) {
                displayErrorMsg();
            }

        }
    }

    private boolean processResponse(boolean isMultiple, boolean isFailed, int ONLY_SUCCESS_ELEMENT, List<ResultDataModel> resultData, ResultDataModel resultDataModel, ArrayList<ResultDetailModel> resultDetailList) {
        if (resultDetailList != null && !resultDetailList.isEmpty()) {
            if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                // success case
                handleSuccess(isMultiple, resultDataModel);

            } else {
                isFailed = handleError(isFailed, resultDataModel, resultDetailList);
            }
        }
        return isFailed;
    }

    private boolean handleError(boolean isFailed, ResultDataModel resultDataModel, ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                NBLogger.e(TAG, "delete analytics api failure, notification id :" + resultDataModel.getResultDetail().get(0).getOperationReference() + " Reason: " + resultDetailViewModel.getReason());
                isFailed = true;
            }
        }
        return isFailed;
    }

    private void handleSuccess(boolean isMultiple, ResultDataModel resultDataModel) {
        if (view != null)
            view.setDeleteSuccess(true);
        trackDeleteApiSuccessAction(isMultiple ? 2 : 1);
        NBLogger.e(TAG, "delete analytics api success, notification id :" + resultDataModel.getResultDetail().get(0).getOperationReference());
    }

    private void displayErrorMsg() {
        if (view != null) {
            view.showErrorForNotificationMessages(view.getDeleteErrorMsg());
        }
    }

    FBNotificationMultipleAnalyticData createMultipleDeleteOrUndoRequest(List<FBNotificationsViewModel> selectedItems, String analyticType) {
        FBNotificationMultipleAnalyticData fbNotificationMultipleAnalyticData = new FBNotificationMultipleAnalyticData();
        FBNotificationMultipleAnalyticData.Analytic analytic = new FBNotificationMultipleAnalyticData.Analytic();
        analytic.setAnalyticValue(analyticType);
        analytic.setDeviceId(getDeviceID());
        analytic.setDeviceDate(getDate());
        fbNotificationMultipleAnalyticData.setAnalytic(analytic);

        List<FBNotificationMultipleAnalyticData.Notification> notifications = new ArrayList<>();
        for (FBNotificationsViewModel notificationsViewModel : selectedItems) {
            FBNotificationMultipleAnalyticData.Notification notification = new FBNotificationMultipleAnalyticData.Notification();
            notification.setNotificationId(notificationsViewModel.getNotificationId());
            notifications.add(notification);
        }
        fbNotificationMultipleAnalyticData.setNotifications(notifications);
        return fbNotificationMultipleAnalyticData;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNotificationReceived(NotificationEvent notificationEvent) {
        if (notificationEvent.getFbNotificationsViewModel().getNotificationType().equals(NotificationConstants.NOTIFICATION_TYPES.MARKETING) || notificationEvent.getFbNotificationsViewModel().getNotificationType().equals(NotificationConstants.NOTIFICATION_TYPES.SECURITY) || notificationEvent.getFbNotificationsViewModel().getNotificationType().equals(NotificationConstants.NOTIFICATION_TYPES.INFO))
            view.onNotificationReceived(notificationEvent.getFbNotificationsViewModel());
    }

    @Subscribe
    public void onMarkReadNotificationEvent(MarkReadNotificationEvent event) {

        if (view != null) {
            view.markReadNotification(event.getFbNotificationsViewModel().getNotificationId());
        }

    }

    void trackAction(String action, String notificationType, String notificationName) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setNotificationType(NotificationUtils.getNotificationType(notificationType));
        adobeContextData.setNotificationName(getValidName(notificationName));
        mAnalytics.sendEventActionWithMap(action, cdata);
    }

    private String getValidName(String notificationName) {
        String notificationNameLowerCase;
        if (StringUtils.isNullOrEmpty(notificationName)) {
            return notificationName;
        } else {
            notificationNameLowerCase = notificationName.toLowerCase();
        }

        if (notificationNameLowerCase.contains(NotificationConstants.BIRTHDAY)) {
            return NotificationConstants.BIRTHDAY_NOTIFICATION;
        } else if (notificationNameLowerCase.contains(NotificationConstants.EASIER_WAY_TO_LOG)) {
            return NotificationConstants.EASY_LOGIN_NOTIFICATION;
        } else {
            return notificationName;
        }

    }

    void trackDeleteApiSuccessAction(int noOfDeletedMessages) {
        if (view != null && view.isUndoTimerComplete() && view.isDeleteSuccess()) {
            view.setDeleteSuccess(false);
            HashMap<String, Object> cdata = new HashMap<>();
            AdobeContextData adobeContextData = new AdobeContextData(cdata);
            adobeContextData.setValue(NotificationConstants.TRACKING_PARAMS.DELETE_MESSAGE_NOTIFICATIONS);
            if (noOfDeletedMessages == 1)
                mAnalytics.sendEventActionWithMap(NotificationConstants.TRACKING_PARAMS.NOTIFICATION_DELETE_SUCCESSFUL, cdata);
            else
                mAnalytics.sendEventActionWithMap(NotificationConstants.TRACKING_PARAMS.NOTIFICATION_DELETE_SELECTED_SUCCESSFUL, cdata);
        }
    }
}
