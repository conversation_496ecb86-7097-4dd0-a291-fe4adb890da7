/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import org.zakariya.stickyheaders.SectioningAdapter;

import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.databinding.CommonSectionHeaderBinding;
import za.co.nedbank.databinding.MoneyRequestReceivedItemLayoutBinding;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsAdapterModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsViewModel;

/**
 * Created by swapnil.gawande on 2/20/2018.
 */

public class ReceivedMoneyRequestsRecyclerAdapter extends SectioningAdapter {
    private final Context mContext;
    private final IReceivedMoneyRequestsRecyclerListener mReceivedMoneyRequestsRecyclerListener;

    private List<MoneyRequestsAdapterModel> mMoneyRequestsAdapterModels;

    ReceivedMoneyRequestsRecyclerAdapter(final Context context,
                                         @NonNull final IReceivedMoneyRequestsRecyclerListener receivedMoneyRequestsRecyclerListener) {
        this.mContext = context;
        this.mReceivedMoneyRequestsRecyclerListener = receivedMoneyRequestsRecyclerListener;
    }

    void setMoneyRequestsAdapterModels(List<MoneyRequestsAdapterModel> moneyRequestsAdapterModels) {
        mMoneyRequestsAdapterModels = moneyRequestsAdapterModels;
        notifyAllSectionsDataSetChanged();
    }

    @Override
    public HeaderViewHolder onCreateHeaderViewHolder(final ViewGroup parent, final int headerUserType) {
        CommonSectionHeaderBinding binding = CommonSectionHeaderBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new HeaderViewHolder(binding);
    }

    @Override
    public ItemViewHolder onCreateItemViewHolder(final ViewGroup parent, final int itemUserType) {
        MoneyRequestReceivedItemLayoutBinding binding = MoneyRequestReceivedItemLayoutBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ItemViewHolder(binding, mReceivedMoneyRequestsRecyclerListener);
    }

    @Override
    public void onBindHeaderViewHolder(SectioningAdapter.HeaderViewHolder viewHolder, int sectionIndex, int headerType) {
        super.onBindHeaderViewHolder(viewHolder, sectionIndex, headerType);
        int headerStringId = mMoneyRequestsAdapterModels.get(sectionIndex).getHeader();
        ((HeaderViewHolder) viewHolder).setHeader(headerStringId);
    }

    @Override
    public void onBindItemViewHolder(SectioningAdapter.ItemViewHolder viewHolder, int sectionIndex, int itemIndex, int itemType) {
        super.onBindItemViewHolder(viewHolder, sectionIndex, itemIndex, itemType);
        MoneyRequestsViewModel moneyRequestsViewModel = getViewModel(sectionIndex, itemIndex);
        ItemViewHolder itemViewHolder = (ItemViewHolder) viewHolder;
        itemViewHolder.tvInitials.setText(StringUtils.getNameInitials(moneyRequestsViewModel.getPartyName()));
        String receivedRequestMsg = String.format(mContext.getString(R.string.view_money_requests_received_item_message),
                FormattingUtil.convertToSouthAfricaFormattedCurrency(moneyRequestsViewModel.getRequestAmount()),
                moneyRequestsViewModel.getPartyName());
        itemViewHolder.tvRequestMsg.setText(receivedRequestMsg);
        String expiryDate = String.format(mContext.getString(R.string.view_money_requests_expiry_date), FormattingUtil.getFormattedDateWithSlash(moneyRequestsViewModel.getExpiryDate()));
        itemViewHolder.tvExpiryDate.setText(expiryDate);
        enableDisablePaymentAction(itemViewHolder, moneyRequestsViewModel);
        itemViewHolder.setSectionPosition(sectionIndex);
        itemViewHolder.setItemPosition(itemIndex);
        itemViewHolder.tvPayNow.setOnClickListener(v -> itemViewHolder.onPayNowClicked());
        itemViewHolder.tvPayLater.setOnClickListener(v -> itemViewHolder.onPayLaterClicked());
        itemViewHolder.tvReject.setOnClickListener(v -> itemViewHolder.onRejectClicked());
    }

    class HeaderViewHolder extends SectioningAdapter.HeaderViewHolder {
        TextView tvHeader;

        HeaderViewHolder(CommonSectionHeaderBinding binding) {
            super(binding.getRoot());
            tvHeader = binding.tvHeader;
        }

        void setHeader(int title) {
            tvHeader.setText(title);
        }
    }

    class ItemViewHolder extends SectioningAdapter.ItemViewHolder {
        private final IReceivedMoneyRequestsRecyclerListener receivedMoneyRequestsRecyclerListener;
        private int sectionPosition;
        private int itemPosition;

        TextView tvInitials;
        TextView tvRequestMsg;
        TextView tvExpiryDate;
        TextView tvReject;
        TextView tvPayLater;
        TextView tvPayNow;

        void setSectionPosition(int sectionPosition) {
            this.sectionPosition = sectionPosition;
        }

        void setItemPosition(int itemPosition) {
            this.itemPosition = itemPosition;
        }

        ItemViewHolder(MoneyRequestReceivedItemLayoutBinding binding, IReceivedMoneyRequestsRecyclerListener receivedMoneyRequestsRecyclerListener) {
            super(binding.getRoot());
            this.tvInitials = binding.tvInitials;
            this.tvRequestMsg = binding.tvRequestMsg;
            this.tvExpiryDate = binding.tvExpiryDate;
            this.tvReject = binding.tvReject;
            this.tvPayLater = binding.tvPayLater;
            this.tvPayNow = binding.tvPayNow;
            this.receivedMoneyRequestsRecyclerListener = receivedMoneyRequestsRecyclerListener;
        }

        void enableRejectAction(boolean enabled) {
            tvReject.setEnabled(enabled);
        }

        void enablePayLaterAction(boolean enabled) {
            tvPayLater.setEnabled(enabled);
        }

        void enablePayNowAction(boolean enabled) {
            tvPayNow.setEnabled(enabled);
        }

        void onRejectClicked() {
            receivedMoneyRequestsRecyclerListener.onRejectClicked(getViewModel(sectionPosition, itemPosition));
        }

        void onPayLaterClicked() {
            receivedMoneyRequestsRecyclerListener.onPayLaterClicked(getViewModel(sectionPosition, itemPosition));
        }

        void onPayNowClicked() {
            receivedMoneyRequestsRecyclerListener.onPayNowClicked(getViewModel(sectionPosition, itemPosition));
        }
    }

    @Override
    public boolean doesSectionHaveHeader(int sectionIndex) {
        return true;
    }

    @Override
    public int getNumberOfSections() {
        return mMoneyRequestsAdapterModels != null&&mMoneyRequestsAdapterModels.size()>0 ?
                mMoneyRequestsAdapterModels.size():0;
    }

    @Override
    public int getNumberOfItemsInSection(int sectionIndex) {
        if (mMoneyRequestsAdapterModels != null && mMoneyRequestsAdapterModels.get(sectionIndex) != null
                && mMoneyRequestsAdapterModels.get(sectionIndex).getMoneyRequestsViewModels() != null) {
            return mMoneyRequestsAdapterModels.get(sectionIndex).getMoneyRequestsViewModels().size();
        }
        return 0;
    }

    private MoneyRequestsViewModel getViewModel(int sectionIndex, int itemIndex) {
        return mMoneyRequestsAdapterModels.get(sectionIndex).getMoneyRequestsViewModels().get(itemIndex);
    }

    private void enableDisablePaymentAction(ItemViewHolder itemViewHolder, MoneyRequestsViewModel viewModel) {
        if (PaymentRequestStatus.PENDING.equals(viewModel.getRequestStatus())) {
            if (viewModel.isReject() || viewModel.isPayLater()) {
                itemViewHolder.enableRejectAction(false);
                itemViewHolder.enablePayLaterAction(false);
                itemViewHolder.enablePayNowAction(false);
            } else {
                itemViewHolder.enableRejectAction(true);
                itemViewHolder.enablePayLaterAction(true);
                itemViewHolder.enablePayNowAction(true);
            }
        } else {
            itemViewHolder.enableRejectAction(false);
            itemViewHolder.enablePayLaterAction(false);
            itemViewHolder.enablePayNowAction(false);
        }
    }

    interface IReceivedMoneyRequestsRecyclerListener {
        void onRejectClicked(MoneyRequestsViewModel moneyRequestsViewModel);

        void onPayLaterClicked(MoneyRequestsViewModel moneyRequestsViewModel);

        void onPayNowClicked(MoneyRequestsViewModel moneyRequestsViewModel);
    }
}
