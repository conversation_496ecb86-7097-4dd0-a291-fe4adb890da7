package za.co.nedbank.ui.view.home.odd_restriction;


import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.BRANCH_LOCATOR_ODD_JURISTIC_COULDNOT_CONFIRM;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.BRANCH_LOCATOR_ODD_JURISTIC_VERIFY_DETAILS;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.BRANCH_LOCATOR_ODD_MISSING_INFORMATION;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.BRANCH_LOCATOR_ODD_NOT_ELIGIBLE;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.BRANCH_LOCATOR_ODD_VERIFY_DETAILS;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.CLOSE_ODD_JURISTIC_COULDNOT_CONFIRM;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.CLOSE_ODD_JURISTIC_VERIFY_DETAILS;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.CLOSE_ODD_MISSING_INFORMATION;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.CLOSE_ODD_NOT_ELIGIBLE;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.CLOSE_ODD_VERIFY_DETAILS;
import static za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorType.JURISTIC;
import static za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorType.JURISTIC_ODD_VERIFYING;
import static za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorType.ODD_VERIFYING;
import static za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorType.REASON_1015;
import static za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorType.REASON_OTHER;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.StringUtils;

public class OddRestrictionErrorPresenter extends NBBasePresenter<OddRestrictionErrorView> {

    private final Analytics mAnalytics;


    @Inject
    OddRestrictionErrorPresenter(Analytics mAnalytics) {
        this.mAnalytics = mAnalytics;
    }

    public void sendCloseTags(OddRestrictionErrorType type) {
        if(type == ODD_VERIFYING) {
            mAnalytics.sendEvent(CLOSE_ODD_VERIFY_DETAILS, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        } else if(type == JURISTIC){
            mAnalytics.sendEvent(CLOSE_ODD_JURISTIC_COULDNOT_CONFIRM, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        } else if(type == REASON_1015) {
            mAnalytics.sendEvent(CLOSE_ODD_MISSING_INFORMATION, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        } else if(type == REASON_OTHER) {
            mAnalytics.sendEvent(CLOSE_ODD_NOT_ELIGIBLE, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        } else if(type == JURISTIC_ODD_VERIFYING) {
            mAnalytics.sendEvent(CLOSE_ODD_JURISTIC_VERIFY_DETAILS, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        }
    }

    public void sendBranchLocatorTags(OddRestrictionErrorType type) {
        if(type == ODD_VERIFYING) {
            mAnalytics.sendEvent(BRANCH_LOCATOR_ODD_VERIFY_DETAILS, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        } else if(type == JURISTIC){
            mAnalytics.sendEvent(BRANCH_LOCATOR_ODD_JURISTIC_COULDNOT_CONFIRM, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        } else if(type == REASON_1015) {
            mAnalytics.sendEvent(BRANCH_LOCATOR_ODD_MISSING_INFORMATION, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        } else if(type == REASON_OTHER) {
            mAnalytics.sendEvent(BRANCH_LOCATOR_ODD_NOT_ELIGIBLE, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        } else if(type == JURISTIC_ODD_VERIFYING) {
            mAnalytics.sendEvent(BRANCH_LOCATOR_ODD_JURISTIC_VERIFY_DETAILS, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        }
        if(view !=null)
            view.onBranchSuccess();
    }

}
