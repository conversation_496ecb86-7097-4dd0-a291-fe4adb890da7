package za.co.nedbank.ui.domain.model.retention;

public enum RetentionFeedbackType {
    VERY_BAD(0,"Very Bad"),
    BAD(1,"Bad"),
    OKAY(2,"Okay"),
    GOOD(3,"Good"),
    EXCELLENT(4,"Excellent");

    private final int value;
    private final String name;

    RetentionFeedbackType(final int value,String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName(){
        return name;
    }
}
