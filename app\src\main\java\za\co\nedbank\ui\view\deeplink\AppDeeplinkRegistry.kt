package za.co.nedbank.ui.view.deeplink

import za.co.nedbank.core.deeplink.DeeplinkMetaModel
import za.co.nedbank.core.deeplink.DeeplinkRegistry
import za.co.nedbank.core.deeplink.DeeplinkUtils
import za.co.nedbank.core.deeplink.IDeeplinkRegistry
import za.co.nedbank.core.navigation.NavigationTarget
import javax.inject.Inject
/**
 * Implementation of [IDeeplinkRegistry] for registering and verifying app-specific deep links.
 *
 * @property registry The [DeeplinkRegistry] used to register deeplink targets.
 */
class AppDeeplinkRegistry
@Inject
constructor(
    private val registry: DeeplinkRegistry
) : IDeeplinkRegistry {

    /**
     * The navigation target for this deeplink registry.
     */
    override var targetRouter = NavigationTarget.APP_DEEPLINK_MANAGER

    /**
     * Registers supported deeplink targets with the [DeeplinkRegistry].
     */
    override fun registerDeeplink() {
        registry.registerDeeplink(
            this,
            DeeplinkUtils.AppDeeplinkTarget.LATEST,
            DeeplinkUtils.AppDeeplinkTarget.CARD_FREEZE,
            DeeplinkUtils.AppDeeplinkTarget.VIRTUAL_CARD,
            DeeplinkUtils.AppDeeplinkTarget.OPEN_AVO,
            DeeplinkUtils.AppDeeplinkTarget.SHOP_AVO,
            DeeplinkUtils.AppDeeplinkTarget.ENOTES_ADD_ON
        )
    }

    /**
     * Verifies if the provided [DeeplinkMetaModel] action is supported by this registry.
     *
     * @param model The deeplink meta model containing the action to verify.
     * @return `true` if the action is supported, `false` otherwise.
     */
    override fun verifyParams(model: DeeplinkMetaModel): Boolean {
        return when (model.action.lowercase()) {
            DeeplinkUtils.AppDeeplinkTarget.LATEST.lowercase(),
            DeeplinkUtils.AppDeeplinkTarget.CARD_FREEZE.lowercase(),
            DeeplinkUtils.AppDeeplinkTarget.VIRTUAL_CARD.lowercase(),
            DeeplinkUtils.AppDeeplinkTarget.OPEN_AVO.lowercase(),
            DeeplinkUtils.AppDeeplinkTarget.SHOP_AVO.lowercase(),
            DeeplinkUtils.AppDeeplinkTarget.ENOTES_ADD_ON.lowercase()
            -> true
            else -> false
        }
    }
}