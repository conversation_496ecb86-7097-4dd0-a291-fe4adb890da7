package za.co.nedbank.ui.view.card_delivery.locker_map;


import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import za.co.nedbank.core.domain.model.location.PlaceDetails;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.map.variants.XLatLng;
import za.co.nedbank.enroll_v2.R;
import za.co.nedbank.enroll_v2.databinding.ItemLockerListBinding;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryUtils;

public class LockerListAdapter extends RecyclerView.Adapter<LockerListAdapter.ViewHolder> {

    private final List<PlaceDetails> places;
    private final XLatLng currentLocation;
    private OnLockerItemClickListener itemClickListener;

    public LockerListAdapter(List<PlaceDetails> lockers, XLatLng currentLocation) {
        this.places = new ArrayList<>();
        places.addAll(lockers);
        this.currentLocation = currentLocation;
    }

    @NonNull
    @NotNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull @NotNull ViewGroup parent, int viewType) {
        ItemLockerListBinding binding = ItemLockerListBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull @NotNull ViewHolder holder, int position) {
        holder.bind(places.get(position));
    }


    @Override
    public int getItemCount() {
        return places != null ? places.size() : 0;
    }

    public void updateItems(List<PlaceDetails> lockers) {
        places.clear();
        places.addAll(lockers);
        notifyDataSetChanged();
    }

    public void setItemClickListener(OnLockerItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        TextView tvLockerName;
        TextView tvLockerAddress;
        TextView tvLockerDistance;
        LinearLayout lockerItemContainer;

        public ViewHolder(@NonNull ItemLockerListBinding binding) {
            super(binding.getRoot());
            tvLockerName = binding.tvLockerName;
            tvLockerAddress = binding.tvLockerAddress;
            tvLockerDistance = binding.tvLockerDistance;
            lockerItemContainer = binding.lockerItemContainer;
        }

        void bind(PlaceDetails place) {
            tvLockerName.setText(place.name);
            tvLockerAddress.setText(place.address);

            if (currentLocation == null) {
                tvLockerDistance.setText(StringUtils.HYPHEN.concat(StringUtils.HYPHEN));
                tvLockerDistance.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO);
            } else {
                String distance = CardDeliveryUtils.calculateDistance(currentLocation.latitude, currentLocation.longitude,
                        place.location.getLatitude(), place.location.getLongitude());
                tvLockerDistance.setText(distance.concat(itemView.getContext().getString(R.string.away)));
                tvLockerDistance.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
            }
            lockerItemContainer.setOnClickListener(v -> onClick());
        }

        void onClick(){
            if (itemClickListener != null){
                itemClickListener.onLockerItemClick(places.get(getAdapterPosition()));
            }
        }
    }

    interface OnLockerItemClickListener {
        void onLockerItemClick(PlaceDetails placeDetails);
    }
}
