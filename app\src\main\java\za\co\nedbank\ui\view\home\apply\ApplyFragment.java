package za.co.nedbank.ui.view.home.apply;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.IFragmentToActivityComListener;
import za.co.nedbank.databinding.FragmentMenuApplyBinding;
import za.co.nedbank.ui.di.AppDI;

/**
 * Created by sreedev.r on 30-Nov-18.
 */

public class ApplyFragment extends NBBaseFragment implements ApplyView {

    @Inject
    ApplyPresenter applyPresenter;
    private IFragmentToActivityComListener mIFragmentToActivityComListener;
    private FragmentMenuApplyBinding binding;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
    }

    public static ApplyFragment getInstance() {
        return new ApplyFragment();
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof IFragmentToActivityComListener) {
            mIFragmentToActivityComListener = (IFragmentToActivityComListener) getActivity();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        applyPresenter.bind(this);
        applyPresenter.getUserDetails();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentMenuApplyBinding.inflate(inflater, container, false);
        binding.llInvest.setOnClickListener(v -> onClickInvest());
        binding.imgApplyClose.setOnClickListener(v -> onClickClose());
        return binding.getRoot();
    }

    @Override
    public void onPause() {
        super.onPause();
        applyPresenter.unbind();
    }

    @Override
    public void showProgressBar(boolean enable) {
        if (enable) {
            ViewUtils.showViews(binding.progressBarPageLoading);
        } else {
            ViewUtils.hideViews(binding.progressBarPageLoading);
        }
    }

    @Override
    public void showGenericError() {
        showError(getString(R.string.something_gone_wrong_heading), getString(R.string.something_gone_wrong_message));
    }

    @Override
    public void showErrorMessage(String message) {
        showError(getString(R.string.something_gone_wrong_heading), message);
    }

    public void onClickInvest() {
        if (!binding.progressBarPageLoading.isShown()) {
            if (applyPresenter.isClientTypeValid()) {
                applyPresenter.getFicaStatus();
            } else {
                applyPresenter.onInvalidClient(Constants.INVONLINE_ONIA_ERROR_INVALID_CLIENT_TYPE);
            }
        }
    }

    public void onClickClose(){
        if (mIFragmentToActivityComListener != null) {
            mIFragmentToActivityComListener.onEvent(IFragmentToActivityComListener.EVENT_CONSTANTS.EVENT_APPLY_CLOSE, null);
        }
    }
}
