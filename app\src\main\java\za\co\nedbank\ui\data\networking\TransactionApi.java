package za.co.nedbank.ui.data.networking;


import io.reactivex.Observable;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;
import za.co.nedbank.ui.data.entity.pop.TransactionHistoryParentEntity;

public interface TransactionApi {


    @GET("contactcards/v1/contactcards/{contactcardid}/transactions")
    Observable<TransactionHistoryParentEntity> getTransactionHistory(@Path("contactcardid") int contactCardId,
                                                                     @Query("pagesize") String pageSize,
                                                                     @Query("page") String page,
                                                                     @Query("fromdate") String startDate,
                                                                     @Query("todate") String endDate);

}

