/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.edit_recipient;

import static za.co.nedbank.core.Constants.LOC_ERROR_HEADER;
import static za.co.nedbank.core.Constants.LOC_TRANSACTION_TYPE;
import static za.co.nedbank.core.utils.LocUtils.LOC_TRANSACTION_TYPE.BENEFICIARY_CHANGE;

import android.annotation.SuppressLint;
import android.app.Activity;

import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.mapper.RecipientResponseDataToViewModelMapper;
import za.co.nedbank.core.domain.model.response.RecipientResponseData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.EditRecipientUseCase;
import za.co.nedbank.core.domain.usecase.enrol.ApproveItFallbackUsecase;
import za.co.nedbank.core.domain.usecase.enrol.FraudApproveItUsecase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.payment.ErrorViewModel;
import za.co.nedbank.core.payment.PaymentsUtility;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.LocUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.AccountValidator;
import za.co.nedbank.core.validation.CreditCardNumberValidator;
import za.co.nedbank.core.validation.ElectricityMeterNumberValidator;
import za.co.nedbank.core.validation.EmailValidator;
import za.co.nedbank.core.validation.MobileNumberValidator;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.validation.RecipientNameCharValidator;
import za.co.nedbank.core.validation.ReferenceFieldValidator;
import za.co.nedbank.core.validation.ShapIdValidator;
import za.co.nedbank.core.view.idvl.VerificationInfoViewModel;
import za.co.nedbank.core.view.mapper.RecipientViewModelToEntityMapper;
import za.co.nedbank.core.view.metadata.ResultDataViewModel;
import za.co.nedbank.core.view.metadata.ResultDetailViewModel;
import za.co.nedbank.core.view.recipient.DeleteRecipientResponseViewModel;
import za.co.nedbank.core.view.recipient.RecipientResponseViewModel;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryNotificationDetailsViewModel;
import za.co.nedbank.nid_sdk.main.interaction.model.ApproveItRequestDto;
import za.co.nedbank.nid_sdk.main.interaction.model.FraudApproveItAcknowledgeDto;
import za.co.nedbank.nid_sdk.main.views.approveit.check.ApproveItWorkFlow;
import za.co.nedbank.payment.common.validation.RecipientNameValidator;
import za.co.nedbank.payment.common.view.tracking.PaymentsTracking;
import za.co.nedbank.profile.view.profile.limits.IDVLProcessType;
import za.co.nedbank.ui.domain.usecase.DeleteRecipientUseCase;
import za.co.nedbank.ui.domain.usecase.GetShapPayDomainUseCase;
import za.co.nedbank.ui.domain.usecase.RecipientStatusUseCase;
import za.co.nedbank.ui.view.home.MatchBackNumberUtils;
import za.co.nedbank.ui.view.home.add_recipient.BaseRecipientPresenter;

/**
 * Created by priyadhingra on 9/12/2017.
 */

public class EditRecipientPresenter extends BaseRecipientPresenter<EditRecipientView> {

    private final EditRecipientUseCase mEditRecipientUseCase;
    private final RecipientStatusUseCase mRecipientStatusUseCase;
    private final DeleteRecipientUseCase mDeleteRecipientUseCase;
    private final FraudApproveItUsecase mFraudApproveItUsecase;
    private final ApproveItFallbackUsecase mApproveItFallbackUsecase;
    private final RecipientViewModelToEntityMapper mEditRecipientViewModelToEntityMapper;
    private final RecipientResponseDataToViewModelMapper mRecipientResponseDataToViewModelMapper;
    private final Analytics mAnalytics;
    private final ApplicationStorage applicationStorage;
    private final @Named("memory") ApplicationStorage inMemoryStorage;
    private final Activity mContext;
    private String verificationReferenceId;
    private RecipientViewModel mEditRecipientViewModel;

    private enum ProcessStepSecurity {
        ITA,
        IDVL,
        NONE
    }
    private ProcessStepSecurity processStepSecurity = ProcessStepSecurity.NONE;

    @Inject
    EditRecipientPresenter(NavigationRouter navigationRouter, RecipientNameValidator recipientNameValidator, RecipientNameCharValidator recipientNameNewValidator, final GetShapPayDomainUseCase getFastPayDomainUseCase, @Named("memory") ApplicationStorage inMemoryStorage, MobileNumberValidator mMobileNumberValidator, NonEmptyTextValidator nonEmptyTextValidator, ReferenceFieldValidator referenceFieldValidator, CreditCardNumberValidator creditCardNumberValidator, AccountValidator accountValidator, ElectricityMeterNumberValidator electricityMeterNumberValidator, EmailValidator emailValidator, EditRecipientUseCase editRecipientUseCase,
                           RecipientStatusUseCase recipientStatusUseCase, ErrorHandler errorHandler, RecipientViewModelToEntityMapper editRecipientViewModelToEntityMapper, RecipientResponseDataToViewModelMapper recipientResponseDataToViewModelMapper, DeleteRecipientUseCase deleteRecipientUseCase, Analytics analytics, FraudApproveItUsecase fraudApproveItUsecase, ApproveItFallbackUsecase approveItFallbackUsecase, ApplicationStorage applicationStorage, Activity context, FeatureSetController featureSetController, ShapIdValidator shapIdValidator) {
        super(navigationRouter, recipientNameValidator, recipientNameNewValidator, mMobileNumberValidator, nonEmptyTextValidator, referenceFieldValidator, creditCardNumberValidator, accountValidator, electricityMeterNumberValidator, emailValidator, errorHandler, featureSetController, shapIdValidator, getFastPayDomainUseCase);
        this.mEditRecipientUseCase = editRecipientUseCase;
        this.mRecipientStatusUseCase = recipientStatusUseCase;
        mEditRecipientViewModelToEntityMapper = editRecipientViewModelToEntityMapper;
        mRecipientResponseDataToViewModelMapper = recipientResponseDataToViewModelMapper;
        this.mDeleteRecipientUseCase = deleteRecipientUseCase;
        this.mAnalytics = analytics;
        this.mFraudApproveItUsecase = fraudApproveItUsecase;
        this.mApproveItFallbackUsecase = approveItFallbackUsecase;
        this.applicationStorage = applicationStorage;
        this.mContext = context;
        this.inMemoryStorage = inMemoryStorage;
    }

    @Override
    protected void onBind() {
        if (null != mNavigationResult && mNavigationResult.isOk() && view != null) {
            view.setResult(mNavigationResult.getParams());
        }
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, TrackingEvent.ANALYTICS.VAL_FEATURE_MANAGE_RECIPIENTS);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_SUBFEATURE, TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_EDIT_RECIPIENT);
        mAnalytics.sendEventActionWithMap(PaymentsTracking.RECIPIENTS_EDIT, cdata);
    }

    void putEditRecipient(String recipientName, int recipientId, List<NbRecyclerViewBaseDataModel> bankAccountViewDataModelList,
                          List<NbRecyclerViewBaseDataModel> mobileNumberViewDataModelList,
                          List<NbRecyclerViewBaseDataModel> electricityMeterViewDataModelList,
                          List<NbRecyclerViewBaseDataModel> emailViewDataModelList, List<NbRecyclerViewBaseDataModel> creditCardViewDataModelList, RecipientViewModel editRecipientDeleteEntryRequestViewModel, List<UserBeneficiaryNotificationDetailsViewModel> userBeneficiaryNotificationDetailsViewModelList, List<NbRecyclerViewBaseDataModel> shapIdViewDataModelList) {
        if ((CollectionUtils.isNotEmpty(bankAccountViewDataModelList) || CollectionUtils.isNotEmpty(mobileNumberViewDataModelList) || CollectionUtils.isNotEmpty(electricityMeterViewDataModelList) || CollectionUtils.isNotEmpty(creditCardViewDataModelList) || CollectionUtils.isNotEmpty(shapIdViewDataModelList))) {
            int lastMatchBackNumber = MatchBackNumberUtils.removeMatchBackNumbers(bankAccountViewDataModelList, creditCardViewDataModelList, mobileNumberViewDataModelList, electricityMeterViewDataModelList, shapIdViewDataModelList, editRecipientDeleteEntryRequestViewModel);
            ((EditRecipientView) view).setMatchBackNumber(lastMatchBackNumber);
            callEditRecipientUsecase(mEditRecipientViewModelToEntityMapper.mapInputDataToRecipientViewModel(recipientName, recipientId, view != null ? view.getDefaultBankNameText() : StringUtils.EMPTY_STRING, bankAccountViewDataModelList, mobileNumberViewDataModelList
                    , electricityMeterViewDataModelList, creditCardViewDataModelList, emailViewDataModelList, editRecipientDeleteEntryRequestViewModel, userBeneficiaryNotificationDetailsViewModelList, shapIdViewDataModelList));
        }
    }

    public boolean isApprovedTransaction() {
        return LocUtils.isTransactionApproved(inMemoryStorage, LocUtils.LOC_TRANSACTION_TYPE.BENEFICIARY_CHANGE, mFeatureSetController);
    }

    public void moveToErrorLocScreen(String errorHeader) {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.LOC_ERROR_SCREEN)
                .withParam(LOC_TRANSACTION_TYPE, BENEFICIARY_CHANGE)
                .withParam(LOC_ERROR_HEADER, errorHeader));
        if (view != null) {
            ((EditRecipientView) view).finishIt();
        }
    }

    @SuppressLint("CheckResult")
    private void callEditRecipientUsecase(RecipientViewModel editRecipientRequestViewModel) {
        mEditRecipientUseCase.execute(editRecipientRequestViewModel)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> preApiCallTasks())
                .subscribe(editRecipientResponseData -> {
                    if (null != editRecipientResponseData) {
                        processStepSecurity = ProcessStepSecurity.NONE;
                        handleEditRecipientResponse(editRecipientResponseData, editRecipientRequestViewModel);
                    }
                }, throwable -> {
                    if (view instanceof EditRecipientView) {
                        ((EditRecipientView) view).showEditRecipientApiError();
                        trackFailure(false, PaymentsTracking.UPDATE_RECIPIENTS_FAILURE, ApiAliasConstants.UPD_REC, mErrorHandler.getErrorMessage(throwable).getMessage(), null, TrackingEvent.ANALYTICS.VAL_STEP_NAME_UPDATE_RECIPIENT_DETAILS);
                    }
                });
    }

    private void sendProcessStepAnalytics(boolean validate) {
        if(!validate) {
            HashMap<String, Object> cdata = new HashMap<>();
            if (ProcessStepSecurity.IDVL.equals(processStepSecurity) || ProcessStepSecurity.ITA.equals(processStepSecurity)) {
                AdobeContextData adobeContextData = new AdobeContextData(cdata);
                if (ProcessStepSecurity.IDVL.equals(processStepSecurity)) {
                    adobeContextData.setContext8(TrackingEvent.ANALYTICS.VAL_PROCESS_STEP_UP);
                } else if (ProcessStepSecurity.ITA.equals(processStepSecurity)) {
                    adobeContextData.setContext8(TrackingEvent.ANALYTICS.VAL_PROCESS_ITA);
                }
            }
            mAnalytics.sendEventActionWithMap(TrackingEvent.ANALYTICS.VAL_RECIPIENTS_UPDATE_PROCESS_STEP_CHECK, cdata);
        }
    }

    public String getVerificationReferenceId() {
        return verificationReferenceId;
    }

    @SuppressLint("CheckResult")
    void callRecipientStatus(String verificationReferenceId) {
        mRecipientStatusUseCase.execute(verificationReferenceId)
                .doOnSubscribe(disposable -> preApiCallTasks())
                .compose(bindToLifecycle())
                .subscribe(editRecipientResponseData -> {
                    if (view != null) {
                        FraudApproveItAcknowledgeDto acknowledgeDto = mRecipientResponseDataToViewModelMapper.mapRecipientResponseToAcknowledgeDto(editRecipientResponseData);
                        handleApproveItResponse(acknowledgeDto, mEditRecipientViewModel);
                    }
                }, throwable -> ((EditRecipientView) view).showEditRecipientApiError(mErrorHandler.getErrorMessage(throwable).getMessage()));
    }

    private void handleEditRecipientResponse(RecipientResponseData editRecipientResponseData,
                                             RecipientViewModel editRecipientRequestViewModel) {
        RecipientResponseViewModel editRecipientResponseViewModel = mRecipientResponseDataToViewModelMapper.mapRecipientResponseDataToViewModel(editRecipientResponseData);
        ErrorViewModel errorViewModel = PaymentsUtility.retrieveApiCallErrors(editRecipientResponseViewModel.getMetadata(), mErrorHandler.getUnknownError(), DtoHelper.BENEFICIARYSAVED);

        if (errorViewModel.isBeneficiarySaveFailed()) {
            handleRecipientResponseError(errorViewModel);
        } else if (errorViewModel.isTransactionSuccess()) {
            sendProcessStepAnalytics(editRecipientRequestViewModel.isValidate());
            handleTransactionSuccess(editRecipientRequestViewModel, editRecipientResponseViewModel, errorViewModel);
        } else if (errorViewModel.isTransactionPending()) {
            callFraudApproveItUsecase(errorViewModel, editRecipientRequestViewModel, mFraudApproveItUsecase);
        } else if (errorViewModel.isTransactionFallback()) {
            callFraudApproveItUsecase(errorViewModel, editRecipientRequestViewModel, mApproveItFallbackUsecase);
        }
        else if (editRecipientResponseViewModel.getData() != null
                && editRecipientResponseViewModel.getData().getSecureTransaction() != null
                && StringUtils.isNotEmpty(editRecipientResponseViewModel.getData().getSecureTransaction().getProcessStep())) {
            processStepSecurity = ProcessStepSecurity.IDVL;
            verificationReferenceId = String.valueOf(editRecipientResponseViewModel.getData().getSecureTransaction().getVerificationReferenceId());
            mEditRecipientViewModel = editRecipientRequestViewModel;
            sendProcessStepAnalytics(editRecipientRequestViewModel.isValidate());
            handleProcessType(editRecipientRequestViewModel, editRecipientResponseViewModel, errorViewModel);
        }
        else {
            handleRecipientResponseError(errorViewModel);
        }
    }

    public void handleProcessType(RecipientViewModel editRecipientRequestViewModel, RecipientResponseViewModel profileLimitsResponseViewModel, ErrorViewModel errorViewModel) {
        if (profileLimitsResponseViewModel != null
                && profileLimitsResponseViewModel.getData() != null
                && profileLimitsResponseViewModel.getData().getSecureTransaction() != null
                && StringUtils.isNotEmpty(profileLimitsResponseViewModel.getData().getSecureTransaction().getProcessStep())) {
            String processStepType = profileLimitsResponseViewModel.getData().getSecureTransaction().getProcessStep();
            String bioSessionToken = profileLimitsResponseViewModel.getData().getSecureTransaction().getBioSessionToken();
            Integer verificationID = profileLimitsResponseViewModel.getData().getSecureTransaction().getVerificationReferenceId();
            IDVLProcessType idvlProcessType = IDVLProcessType.valueOf(processStepType);
            switch (idvlProcessType) {
                case COMPLETED:
                    handleTransactionSuccess(editRecipientRequestViewModel, profileLimitsResponseViewModel, errorViewModel);
                    break;
                case FACIALSTEPUP: {
                    stopLoading();
                    mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SECURITY_ALERT_SCREEN)
                            .withParam(NavigationTarget.PARAM_BIO_SESSION_TOKEN, bioSessionToken)
                            .withParam(NavigationTarget.PARAM_PROCESS_TYPE, processStepType)
                            .withParam(NavigationTarget.NAVIGATION_FROM, NavigationTarget.EDIT_RECIPIENT)
                            .withParam(NavigationTarget.PARAM_VERIFICATION_INFO, getVerificationInfoViewModel(verificationID, StringUtils.EMPTY_STRING, 0))
                    );
                    break;
                }
                case STOP:
                    moveToStopScreen();
                    break;
                default:
                    break;
            }
        }
    }

    public VerificationInfoViewModel getVerificationInfoViewModel(Integer id, String method, Integer otp) {
        VerificationInfoViewModel verificationInfoViewModel=new VerificationInfoViewModel();
        verificationInfoViewModel.setVerificationID(id);
        verificationInfoViewModel.setVerificationMethod(method);
        verificationInfoViewModel.setOtp(otp);
        return verificationInfoViewModel;
    }
    private void handleRecipientResponseError(ErrorViewModel errorViewModel) {
        if (view instanceof EditRecipientView) {
            setMatchBackNumberErrorMap(errorViewModel.getMatchBackNumberMap(), MatchBackNumberUtils.getMatchBackNumberAvoidList());
            ((EditRecipientView) view).showEditRecipientApiError(errorViewModel.getMatchBackNumberMap(), errorViewModel.getErrorMessages());
            trackFailure(true, PaymentsTracking.UPDATE_RECIPIENTS_FAILURE, ApiAliasConstants.UPD_REC, errorViewModel.getErrorMessage(), null, TrackingEvent.ANALYTICS.VAL_STEP_NAME_UPDATE_RECIPIENT_DETAILS);
        }
    }

    public void stopLoading() {
        if (view != null) {
            view.showLoadingOnButton(false);
            view.setLoadingButtonEnabled(true);
            view.setEnabledActivityTouch(true);
        }
    }

    public void moveToStopScreen() {
        stopLoading();
        trackFailure(true, PaymentsTracking.UPDATE_RECIPIENTS_FAILURE,
                ApiAliasConstants.UPD_REC, mErrorHandler.getUnknownError(),
                null, TrackingEvent.ANALYTICS.VAL_STEP_NAME_UPDATE_RECIPIENT_DETAILS);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.STOP_SCREEN)
                .withParam(NavigationTarget.NAVIGATION_FROM, NavigationTarget.EDIT_RECIPIENT));
    }

    private void handleTransactionSuccess(RecipientViewModel editRecipientRequestViewModel,
                                          RecipientResponseViewModel editRecipientResponseViewModel, ErrorViewModel errorViewModel) {
        if (editRecipientRequestViewModel.isValidate()) {
            handleEditRecipientValidated(editRecipientRequestViewModel,editRecipientResponseViewModel);
        } else {
            boolean isSuccessForAllRecipient;
            if (isAddRecipientResultData(editRecipientResponseViewModel)) {
                isSuccessForAllRecipient = getIsSuccessForAllRecipient(editRecipientResponseViewModel.getMetadata().getResultData());
                if (view instanceof EditRecipientView) {
                    if (isSuccessForAllRecipient) {
                        ((EditRecipientView) view).onRecipientEdited(editRecipientRequestViewModel);
                    } else {
                        ((EditRecipientView) view).handleFewRecipientEditionFailedError(errorViewModel.getErrorMessages());
                        trackFailure(true, PaymentsTracking.UPDATE_RECIPIENTS_FAILURE, ApiAliasConstants.UPD_REC,
                                errorViewModel.getErrorMessage(), null, TrackingEvent.ANALYTICS.VAL_STEP_NAME_UPDATE_RECIPIENT_DETAILS);
                    }
                }
            }
        }
    }

    private void handleEditRecipientValidated(RecipientViewModel editRecipientRequestViewModel,
                                              RecipientResponseViewModel editRecipientResponseViewModel) {
        if (editRecipientResponseViewModel.getData() != null) {
            editRecipientRequestViewModel.setValidate(!editRecipientRequestViewModel.isValidate());
            callEditRecipientUsecase(editRecipientRequestViewModel);
        } else {
            if (view instanceof EditRecipientView) {
                ((EditRecipientView) view).showEditRecipientApiError(mErrorHandler.getUnknownError());
                trackFailure(true, PaymentsTracking.UPDATE_RECIPIENTS_FAILURE,
                        ApiAliasConstants.UPD_REC, mErrorHandler.getUnknownError(),
                        null, TrackingEvent.ANALYTICS.VAL_STEP_NAME_UPDATE_RECIPIENT_DETAILS);

            }
        }
    }

    private boolean isAddRecipientResultData(RecipientResponseViewModel editRecipientResponseViewModel) {
        return editRecipientResponseViewModel.getMetadata() != null
                && CollectionUtils.isNotEmpty(editRecipientResponseViewModel.getMetadata().getResultData());
    }

    private boolean getIsSuccessForAllRecipient(List<ResultDataViewModel> addRecipientResultList) {
        boolean isSuccessForAllRecipient = true;
        for (ResultDataViewModel resultDatumViewModel : addRecipientResultList) {
            if (CollectionUtils.isNotEmpty(resultDatumViewModel.getResultDetail())) {
                for (ResultDetailViewModel resultDetailViewModel : resultDatumViewModel.getResultDetail()) {
                    if (StringUtils.isNotEmpty(resultDetailViewModel.getStatus())
                            && !resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        isSuccessForAllRecipient = false;
                    }
                }
            }

        }
        return isSuccessForAllRecipient;
    }

    private void callFraudApproveItUsecase(ErrorViewModel errorViewModel, RecipientViewModel editRecipientViewModel, UseCase<ApproveItRequestDto, FraudApproveItAcknowledgeDto> useCase) {
        processStepSecurity = ProcessStepSecurity.ITA;
        sendProcessStepAnalytics(editRecipientViewModel.isValidate());
        String refrenceURL = Constants.FraudApproveItEndPoint.RECIPIENT;
        ApproveItRequestDto requestDto = new ApproveItRequestDto();
        requestDto.setResult(errorViewModel.getTransactionResult());
        requestDto.setAuthRefernce(errorViewModel.getTransactionId());
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.SENSITIVE_TRANSACTIONS_CALLBACK)) {
            requestDto.setApproveItType(ApproveItWorkFlow.CALLBACK);
        }
        requestDto.setApproveItApiEndPoint(refrenceURL + errorViewModel.getTransactionId() + Constants.FraudApproveItEndPoint.STATUS);
        requestDto.setMobielNumber(StringUtils.obfuscateMobileNumber(applicationStorage.getString(StorageKeys.MOBILE_NUMBER_UNMASKED, StringUtils.EMPTY_STRING)));
        useCase.execute(requestDto)
                .doOnSubscribe(disposable -> preApiCallTasks())
                .subscribe(acknowledgeDto -> {
                    handleApproveItResponse(acknowledgeDto, editRecipientViewModel);
                }, throwable -> {
                    if (view instanceof EditRecipientView) {
                        ((EditRecipientView) view).showEditRecipientApiError(mErrorHandler.getErrorMessage(throwable).getMessage());
                        trackFailure(false, PaymentsTracking.UPDATE_RECIPIENTS_FAILURE, ApiAliasConstants.FR_AU, mErrorHandler.getErrorMessage(throwable).getMessage(), null, TrackingEvent.ANALYTICS.VAL_STEP_NAME_UPDATE_RECIPIENT_DETAILS);
                    }
                });
    }

    private void handleApproveItResponse(FraudApproveItAcknowledgeDto acknowledgeDto, RecipientViewModel editRecipientViewModel) {
        NBLogger.v("KK FraudApproveIt", acknowledgeDto.getResultCode() + "");
        if (view != null) {
            if (acknowledgeDto.getResultCode() != null && acknowledgeDto.getResultCode().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                ((EditRecipientView) view).onRecipientEdited(editRecipientViewModel);
            } else if (acknowledgeDto.getResultCode() != null && acknowledgeDto.getResultCode().equalsIgnoreCase(DtoHelper.RESEND)) {
                ((EditRecipientView) view).putEditRecipient();
            } else if (acknowledgeDto.getResultCode() != null && acknowledgeDto.getResultCode().equalsIgnoreCase(DtoHelper.FAILURE) && acknowledgeDto.showErrorMessage()) {
                ((EditRecipientView) view).showApproveItApiError(acknowledgeDto.getMessage());
                trackFailure(true, PaymentsTracking.UPDATE_RECIPIENTS_FAILURE, ApiAliasConstants.FR_AU, acknowledgeDto.getMessage(), null, TrackingEvent.ANALYTICS.VAL_STEP_NAME_UPDATE_RECIPIENT_DETAILS);
            } else {
                ((EditRecipientView) view).showApproveItApiError(mContext.getString(za.co.nedbank.profile.R.string.request_rejected));
                trackFailure(true, PaymentsTracking.UPDATE_RECIPIENTS_FAILURE, ApiAliasConstants.FR_AU, mContext.getString(za.co.nedbank.profile.R.string.request_rejected), null, TrackingEvent.ANALYTICS.VAL_STEP_NAME_UPDATE_RECIPIENT_DETAILS);
            }
        }
    }

    public void callDeleteRecipientUsecase(int contactCardId) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, TrackingEvent.ANALYTICS.VAL_FEATURE_MANAGE_RECIPIENTS);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_SUBFEATURE, TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_DELETE_RECIPIENT);
        mAnalytics.sendEventActionWithMap(PaymentsTracking.RECIPIENTS_DELETE, cdata);

        mDeleteRecipientUseCase.execute(contactCardId)
                .doOnSubscribe(disposable -> preApiCallTasks())
                .compose(bindToLifecycle())
                .subscribe(deleteRecipientResponseData -> {
                    if (null != deleteRecipientResponseData) {
                        DeleteRecipientResponseViewModel deleteRecipientResponseViewModel = mRecipientResponseDataToViewModelMapper.mapDeleteRecipientResponseEntityToData(deleteRecipientResponseData);
                        ErrorViewModel errorViewModel = PaymentsUtility.retrieveApiCallErrors(deleteRecipientResponseViewModel.getMetadata(), mErrorHandler.getUnknownError(), DtoHelper.BENEFICIARYSAVED);
                        if (errorViewModel.isTransactionSuccess()) {
                            boolean isSuccessForAllRecipient = true;
                            if (deleteRecipientResponseViewModel.getMetadata() != null && deleteRecipientResponseViewModel.getMetadata().getResultData() != null && deleteRecipientResponseViewModel.getMetadata().getResultData().size() > 0) {
                                List<ResultDataViewModel> addRecipientResultList = deleteRecipientResponseViewModel.getMetadata().getResultData();
                                for (ResultDataViewModel resultDatumViewModel : addRecipientResultList) {
                                    if (resultDatumViewModel.getResultDetail() != null && resultDatumViewModel.getResultDetail().size() > 0) {
                                        for (ResultDetailViewModel resultDetailViewModel : resultDatumViewModel.getResultDetail()) {
                                            if (resultDetailViewModel.getStatus() != null && !resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                                                isSuccessForAllRecipient = false;
                                            }
                                        }
                                    }

                                }

                                if (view != null && isSuccessForAllRecipient && view instanceof EditRecipientView) {
                                    ((EditRecipientView) view).onRecipientDeleted();

                                }
                            }
                        } else {
                            if (view != null && view instanceof EditRecipientView) {
                                ((EditRecipientView) view).showDeleteRecipientApiError(errorViewModel.getErrorMessages());
                                trackFailure(true, PaymentsTracking.DELETE_RECIPIENTS_FAILURE, ApiAliasConstants.DEL_REC, errorViewModel.getErrorMessage(), null, TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_DELETE_RECIPIENT);

                            }
                        }
                    }
                }, throwable -> {
                    if (view != null && view instanceof EditRecipientView) {
                        ((EditRecipientView) view).showDeleteRecipientApiError(mErrorHandler.getErrorMessage(throwable).getMessage());
                        trackFailure(false, PaymentsTracking.DELETE_RECIPIENTS_FAILURE, ApiAliasConstants.DEL_REC, mErrorHandler.getErrorMessage(throwable).getMessage(), null, TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_DELETE_RECIPIENT);

                    }
                });
    }

    void trackSuccessAction(String actionName, String stepName) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        adobeContextData.setFeature(TrackingEvent.ANALYTICS.VAL_FEATURE_MANAGE_RECIPIENTS);
        adobeContextData.setSubFeature(TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_EDIT_RECIPIENT);
        adobeContextData.setStepName(stepName);
        if (PaymentsTracking.UPDATE_RECIPIENTS_SUCCESSFUL.equals(actionName) &&
                (ProcessStepSecurity.IDVL.equals(processStepSecurity) || ProcessStepSecurity.ITA.equals(processStepSecurity)))
            adobeContextData.setContext8Count();
        mAnalytics.sendEventActionWithMap(actionName, cdata);
    }

    public void trackFailure(boolean isApiFailure, String tagName, String apiName, String message, String apiErrorCode, String stepName) {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        adobeContextData.setFeature(TrackingEvent.ANALYTICS.VAL_FEATURE_MANAGE_RECIPIENTS);
        adobeContextData.setSubFeature(TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_EDIT_RECIPIENT);
        adobeContextData.setStepName(stepName);
        if (PaymentsTracking.UPDATE_RECIPIENTS_FAILURE.equals(tagName) &&
                (ProcessStepSecurity.IDVL.equals(processStepSecurity) || ProcessStepSecurity.ITA.equals(processStepSecurity)))
            adobeContextData.setContext8Count();
        mAnalytics.trackFailure(isApiFailure, tagName, apiName, message, apiErrorCode, contextData);
    }

}