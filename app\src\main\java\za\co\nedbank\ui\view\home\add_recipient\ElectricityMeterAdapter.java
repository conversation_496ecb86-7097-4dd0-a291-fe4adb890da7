/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.List;

import io.reactivex.annotations.NonNull;
import za.co.nedbank.R;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.sharedui.control.CustomLinearLayout;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewModel;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.view.mapper.ElectricityMeterViewDataModel;
import za.co.nedbank.uisdk.component.CompatEditText;

/**
 * Created by priyadhingra on 9/5/2017.
 */

public class ElectricityMeterAdapter extends NBFlexibleItemCountRecyclerviewAdapter {

    private BankAccountAdapter.IActivityAdapterComListener mIActivityAdapterComListener;

    public void setIActivityAdapterComListener(BankAccountAdapter.IActivityAdapterComListener iActivityAdapterComListener) {
        mIActivityAdapterComListener = iActivityAdapterComListener;
    }

    public ElectricityMeterAdapter(@NonNull final Context context,
                                   @NonNull final NBFlexibleItemCountRecyclerviewModel nbFlexibleItemCountRecyclerviewModel,
                                   @NonNull final List<NbRecyclerViewBaseDataModel> nbRecyclerViewBaseDataModelList,
                                   final IAdapterInteractionListener adapterInteractionListener,
                                   ISectionedListItemSelectedListener itemSelectedListener) {
        super(context, nbFlexibleItemCountRecyclerviewModel, nbRecyclerViewBaseDataModelList, adapterInteractionListener, itemSelectedListener);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(Context context, ViewGroup parent) {
        View v = LayoutInflater.from(context).inflate(mNBFlexibleItemCountRecyclerviewModel.getItemView(), parent, false);
        return new VHElectricityMetreItem(v);
    }

    @Override
    protected void addItem() {
        ElectricityMeterViewDataModel electricityMeterViewDataModel = new ElectricityMeterViewDataModel();
        mNbRecyclerViewBaseDataModelList.add(mNbRecyclerViewBaseDataModelList.size(), electricityMeterViewDataModel);
        notifyItemInserted(mNbRecyclerViewBaseDataModelList.size());
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (position > 0 && (position - 1) < mNbRecyclerViewBaseDataModelList.size() && mNbRecyclerViewBaseDataModelList.get(position - 1) instanceof ElectricityMeterViewDataModel) {
            ElectricityMeterViewDataModel electricityMeterViewDataModel = (ElectricityMeterViewDataModel) mNbRecyclerViewBaseDataModelList.get(position - 1);
            VHElectricityMetreItem vhElectricityMetreItem = ((VHElectricityMetreItem) holder);

            if (vhElectricityMetreItem.meterNumber != null) {
                vhElectricityMetreItem.meterNumber.setText(electricityMeterViewDataModel.getElectricityMeterNumber(), isEditable());
            }
            if (vhElectricityMetreItem.yourRef != null) {
                vhElectricityMetreItem.yourRef.setText(electricityMeterViewDataModel.getYourRef(), isEditable());
            }
            vhElectricityMetreItem.ivRecipientTypeIcon.setVisibility(isEditable() ? View.GONE : View.VISIBLE);
            if (isEditable()) {
                if (!electricityMeterViewDataModel.isExistingItem() && (mMatchBackNumberErrorMap == null || mMatchBackNumberErrorMap.isEmpty())) {
                    if (vhElectricityMetreItem.meterNumber != null) {
                        vhElectricityMetreItem.meterNumber.requestFocus();
                        if (electricityMeterViewDataModel.isSendAccessibilityEvent() && AccessibilityUtils.isAccessibilityServiceEnabled(mContext)) {
                            vhElectricityMetreItem.meterNumber.postDelayed(() ->
                                    vhElectricityMetreItem.meterNumber.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED), 500);
                            electricityMeterViewDataModel.setSendAccessibilityEvent(false);
                        }

                    }
                }
                vhElectricityMetreItem.ivRemove.setVisibility(View.VISIBLE);
            } else {
                if (vhElectricityMetreItem.meterNumber != null) {
                    vhElectricityMetreItem.meterNumber.setBackgroundColor(ContextCompat.getColor(vhElectricityMetreItem.meterNumber.getContext(), android.R.color.transparent));
                }
                if (vhElectricityMetreItem.yourRef != null) {
                    vhElectricityMetreItem.yourRef.setBackgroundColor(ContextCompat.getColor(vhElectricityMetreItem.yourRef.getContext(), android.R.color.transparent));
                }
                vhElectricityMetreItem.ivRemove.setVisibility(View.GONE);
            }
            if (vhElectricityMetreItem.meterNumber != null) {
                vhElectricityMetreItem.meterNumber.setFocusable(isEditable());
                vhElectricityMetreItem.meterNumber.setEnabled(isEditable());
            }
            if (vhElectricityMetreItem.yourRef != null) {
                vhElectricityMetreItem.yourRef.setFocusable(isEditable());
                vhElectricityMetreItem.yourRef.setEnabled(isEditable());
            }
            vhElectricityMetreItem.llRootView.setViewGroupClickListener(mItemSelectedListener != null ? mViewInterceptListener : null);
            vhElectricityMetreItem.addListenerForMetreNumber(vhElectricityMetreItem.meterNumber);
            vhElectricityMetreItem.addListenerForYourReference(vhElectricityMetreItem.yourRef);
            vhElectricityMetreItem.ivRemove.setOnClickListener(v -> vhElectricityMetreItem.onClickOfRemoveImageView());
            vhElectricityMetreItem.llRootView.setOnClickListener(v -> vhElectricityMetreItem.handleItemSelected());
            if (electricityMeterViewDataModel.getMatchBackNumber() == 0 && mIActivityAdapterComListener != null) {
                electricityMeterViewDataModel.setMatchBackNumber(mIActivityAdapterComListener.getMatchBackNumber());
            } else {
                checkForMatchBackNumberError(vhElectricityMetreItem.meterNumber, electricityMeterViewDataModel.getMatchBackNumber());
            }
        } else {
            super.onBindViewHolder(holder, position);
        }
    }

    class VHElectricityMetreItem extends RecyclerView.ViewHolder {

        CompatEditText yourRef;
        CompatEditText meterNumber;
        ImageView ivRemove;
        ImageView ivRecipientTypeIcon;
        CustomLinearLayout llRootView;

        void onClickOfRemoveImageView() {
            int pos = getBindingAdapterPosition() - 1;
            if (pos >= 0 && pos < mNbRecyclerViewBaseDataModelList.size()) {
                removeItem(getBindingAdapterPosition(), mNbRecyclerViewBaseDataModelList.get(pos).isExistingItem());
            }
        }

        public void handleItemSelected() {
            if (mItemSelectedListener != null) {
                mItemSelectedListener.itemSelected(VIEW_TYPE.ELECTRICITY_METER.ordinal(), getBindingAdapterPosition() - 1);
            }
        }

        public VHElectricityMetreItem(View itemView) {
            super(itemView);
            yourRef = itemView.findViewById(R.id.et_your_reference);
            meterNumber = itemView.findViewById(R.id.et_electricity_metre_number);
            ivRemove = itemView.findViewById(R.id.iv_remove);
            ivRecipientTypeIcon = itemView.findViewById(R.id.iv_recipient_icon);
            llRootView = itemView.findViewById(R.id.ll_root_view);
        }

        void addListenerForMetreNumber(CompatEditText compatEdtMetreNumber) {

            RxTextView.textChanges(compatEdtMetreNumber.getInputField()).subscribe(chars -> {
                if (compatEdtMetreNumber.hasError()&& !chars.toString().equals(((ElectricityMeterViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).getElectricityMeterNumber())) {
                    compatEdtMetreNumber.clearErrors();
                    removeMatchBackNumberFromErrorList(mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1).getMatchBackNumber());
                }
                ((ElectricityMeterViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setElectricityMeterNumber(compatEdtMetreNumber.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
                compatEdtMetreNumber.setTag(getBindingAdapterPosition() - 1);
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtMetreNumber.setTag(getBindingAdapterPosition() - 1);
            compatEdtMetreNumber.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtMetreNumber, Validator.ValidatorType.METER_NUMBER_VALIDATOR);
                    if (mNbRecyclerViewBaseDataModelList != null && !mNbRecyclerViewBaseDataModelList.isEmpty() && mNbRecyclerViewBaseDataModelList.size() > (Integer) compatEdtMetreNumber.getTag()) {
                        checkForMatchBackNumberError(compatEdtMetreNumber, mNbRecyclerViewBaseDataModelList.get((Integer) compatEdtMetreNumber.getTag()).getMatchBackNumber());
                    }
                }
            });

        }

        void addListenerForYourReference(CompatEditText compatEdtYourReference) {

            RxTextView.textChanges(compatEdtYourReference.getInputField()).subscribe(chars -> {
                compatEdtYourReference.clearErrors();
                ((ElectricityMeterViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setYourRef(compatEdtYourReference.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtYourReference.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtYourReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });

            RxTextView.editorActions(compatEdtYourReference.getInputField())
                    .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                    .subscribe(chars -> {
                        ViewUtils.hideSoftKeyboard(mContext, compatEdtYourReference);
                        compatEdtYourReference.clearFocus();
                        mIActivityAdapterComListener.validateInput(compatEdtYourReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                    }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        }

    }
}
