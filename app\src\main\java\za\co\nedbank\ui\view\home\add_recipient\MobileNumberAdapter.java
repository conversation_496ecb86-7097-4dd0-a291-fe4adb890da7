/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.List;

import io.reactivex.annotations.NonNull;
import za.co.nedbank.R;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.sharedui.control.CustomLinearLayout;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewModel;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.view.recipient.MobileNumberViewDataModel;
import za.co.nedbank.uisdk.component.CompatEditText;

/**
 * Created by priyadhingra on 9/5/2017.
 */
public class MobileNumberAdapter extends NBFlexibleItemCountRecyclerviewAdapter {

    private BankAccountAdapter.IActivityAdapterComListener mIActivityAdapterComListener;

    public void setIActivityAdapterComListener(BankAccountAdapter.IActivityAdapterComListener iActivityAdapterComListener) {
        mIActivityAdapterComListener = iActivityAdapterComListener;
    }

    public MobileNumberAdapter(@NonNull final Context context,
                               @NonNull final NBFlexibleItemCountRecyclerviewModel nbFlexibleItemCountRecyclerviewModel,
                               final List<NbRecyclerViewBaseDataModel> nbRecyclerViewBaseDataModelList,
                               final IAdapterInteractionListener adapterInteractionListener,
                               ISectionedListItemSelectedListener itemSelectedListener) {
        super(context, nbFlexibleItemCountRecyclerviewModel, nbRecyclerViewBaseDataModelList, adapterInteractionListener, itemSelectedListener);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(Context context, ViewGroup parent) {
        View v = LayoutInflater.from(context).inflate(mNBFlexibleItemCountRecyclerviewModel.getItemView(), parent, false);
        return new VHMobileNumberItem(v);
    }

    @Override
    protected void addItem() {
        MobileNumberViewDataModel mobileNumberViewDataModel = new MobileNumberViewDataModel();
        mNbRecyclerViewBaseDataModelList.add(mNbRecyclerViewBaseDataModelList.size(), mobileNumberViewDataModel);
        notifyItemInserted(mNbRecyclerViewBaseDataModelList.size());
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (position > 0 && (position - 1) < mNbRecyclerViewBaseDataModelList.size() && mNbRecyclerViewBaseDataModelList.get(position - 1) instanceof MobileNumberViewDataModel) {
            MobileNumberViewDataModel mobileNumberViewDataModel = (MobileNumberViewDataModel) mNbRecyclerViewBaseDataModelList.get(position - 1);
            VHMobileNumberItem vhMobileNumberItem = ((VHMobileNumberItem) holder);

            if (vhMobileNumberItem.mobileNumber != null) {
                vhMobileNumberItem.mobileNumber.setText(mobileNumberViewDataModel.getMobileNumber(), isEditable());
            }
            if (vhMobileNumberItem.yourRef != null) {
                vhMobileNumberItem.yourRef.setText(mobileNumberViewDataModel.getYourRef(), isEditable());
            }
            vhMobileNumberItem.ivRecipientTypeIcon.setVisibility(isEditable() ? View.GONE : View.VISIBLE);
            if (isEditable()) {
                if (!mobileNumberViewDataModel.isExistingItem()) {
                    if (vhMobileNumberItem.mobileNumber != null) {
                        vhMobileNumberItem.mobileNumber.requestFocus();
                        if (mobileNumberViewDataModel.isSendAccessibilityEvent() && AccessibilityUtils.isAccessibilityServiceEnabled(mContext)) {
                            vhMobileNumberItem.mobileNumber.postDelayed(() ->
                                    vhMobileNumberItem.mobileNumber.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED), 500);
                            mobileNumberViewDataModel.setSendAccessibilityEvent(false);
                        }
                    }
                }
                vhMobileNumberItem.ivRemove.setVisibility(View.VISIBLE);

            } else {
                if (vhMobileNumberItem.mobileNumber != null) {
                    vhMobileNumberItem.mobileNumber.setBackgroundColor(ContextCompat.getColor(vhMobileNumberItem.mobileNumber.getContext(), android.R.color.transparent));
                }
                if (vhMobileNumberItem.yourRef != null) {
                    vhMobileNumberItem.yourRef.setBackgroundColor(ContextCompat.getColor(vhMobileNumberItem.yourRef.getContext(), android.R.color.transparent));
                }
                vhMobileNumberItem.ivRemove.setVisibility(View.GONE);
            }

            if (vhMobileNumberItem.mobileNumber != null) {
                vhMobileNumberItem.mobileNumber.setFocusable(isEditable());
                vhMobileNumberItem.mobileNumber.setEnabled(isEditable());
            }
            if (vhMobileNumberItem.yourRef != null) {
                vhMobileNumberItem.yourRef.setFocusable(isEditable());
                vhMobileNumberItem.yourRef.setEnabled(isEditable());
            }
            vhMobileNumberItem.llRootView.setViewGroupClickListener(mItemSelectedListener != null ? mViewInterceptListener : null);

            vhMobileNumberItem.addListenerForMobileNumber(vhMobileNumberItem.mobileNumber);
            vhMobileNumberItem.addListenerForYourReference(vhMobileNumberItem.yourRef);
            vhMobileNumberItem.ivRemove.setOnClickListener(v -> vhMobileNumberItem.onClickOfRemoveImageView());
            vhMobileNumberItem.llRootView.setOnClickListener(v -> vhMobileNumberItem.handleItemSelected());
            if (mobileNumberViewDataModel.getMatchBackNumber() == 0 && mIActivityAdapterComListener != null) {
                mobileNumberViewDataModel.setMatchBackNumber(mIActivityAdapterComListener.getMatchBackNumber());
            }
        } else {
            super.onBindViewHolder(holder, position);
        }
    }

    class VHMobileNumberItem extends RecyclerView.ViewHolder {

        CompatEditText yourRef;
        CustomLinearLayout llRootView;
        CompatEditText mobileNumber;
        ImageView ivRemove;
        ImageView ivRecipientTypeIcon;

        void onClickOfRemoveImageView() {
            int pos = getAdapterPosition() - 1;
            if (pos >= 0 && pos < mNbRecyclerViewBaseDataModelList.size()) {
                removeItem(getAdapterPosition(), mNbRecyclerViewBaseDataModelList.get(pos).isExistingItem());
            }
        }

        public void handleItemSelected() {
            if (mItemSelectedListener != null) {
                mItemSelectedListener.itemSelected(VIEW_TYPE.MOBILE_NUMBER.ordinal(), getAdapterPosition() - 1);
            }
        }

        public VHMobileNumberItem(View itemView) {
            super(itemView);
            yourRef = itemView.findViewById(R.id.et_your_reference);
            llRootView = itemView.findViewById(R.id.ll_root_view);
            mobileNumber = itemView.findViewById(R.id.et_mobile_number);
            ivRemove = itemView.findViewById(R.id.iv_remove);
            ivRecipientTypeIcon = itemView.findViewById(R.id.iv_recipient_icon);
        }

        void addListenerForMobileNumber(CompatEditText compatEdtMobileNumber) {

            RxTextView.textChanges(compatEdtMobileNumber.getInputField()).subscribe(chars -> {
                compatEdtMobileNumber.clearErrors();
                ((MobileNumberViewDataModel) mNbRecyclerViewBaseDataModelList.get(getAdapterPosition() - 1)).setMobileNumber(compatEdtMobileNumber.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtMobileNumber.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtMobileNumber, Validator.ValidatorType.MOBILE_NUMBER_VAIDATOR);
                }
            });

            RxTextView.editorActions(compatEdtMobileNumber.getInputField())
                    .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                    .subscribe(chars -> {
                        ViewUtils.hideSoftKeyboard(mContext, compatEdtMobileNumber);
                        compatEdtMobileNumber.clearFocus();
                        mIActivityAdapterComListener.validateInput(compatEdtMobileNumber, Validator.ValidatorType.MOBILE_NUMBER_VAIDATOR);
                    }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        }

        void addListenerForYourReference(CompatEditText compatEdtYourReference) {

            RxTextView.textChanges(compatEdtYourReference.getInputField()).subscribe(chars -> {
                compatEdtYourReference.clearErrors();
                ((MobileNumberViewDataModel) mNbRecyclerViewBaseDataModelList.get(getAdapterPosition() - 1)).setYourRef(compatEdtYourReference.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtYourReference.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtYourReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });

            RxTextView.editorActions(compatEdtYourReference.getInputField())
                    .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                    .subscribe(chars -> {
                        ViewUtils.hideSoftKeyboard(mContext, compatEdtYourReference);
                        compatEdtYourReference.clearFocus();
                        if (mIActivityAdapterComListener != null) {
                            mIActivityAdapterComListener.validateInput(compatEdtYourReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                        }
                    }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
        }
    }
}
