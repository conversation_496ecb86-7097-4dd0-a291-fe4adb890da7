package za.co.nedbank.ui.view.notification;


import za.co.nedbank.core.base.NBBaseView;

public interface NotificationCenterView extends NBBaseView {

    void markReadMessages(boolean isAllRead);

    void markReadOffers(boolean isAllRead);

    void updateMessageCounter(int unreadCount);

    void updateOffersCounter(int unreadCount);

    void receiveTransactionNotificationCount(int count);

    void updateCount(int count);
}
