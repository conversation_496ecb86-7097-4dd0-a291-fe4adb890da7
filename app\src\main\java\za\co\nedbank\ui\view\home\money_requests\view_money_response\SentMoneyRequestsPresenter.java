/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.annotations.NonNull;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.networking.client.ResultErrorCodes;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.services.Constants;
import za.co.nedbank.services.domain.model.metadata.ResultDetailDataModel;
import za.co.nedbank.ui.data.mapper.money_requests.MoneyRequestsDataModelToViewModelMapper;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsActionDataModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsAdapterModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsDataModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsMainDataModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsViewModel;
import za.co.nedbank.ui.domain.model.money_request.PaymentActionDataModel;
import za.co.nedbank.ui.domain.usecase.money_request.MoneyRequestsActionUseCase;
import za.co.nedbank.ui.domain.usecase.money_request.MoneyRequestsUseCase;
import za.co.nedbank.ui.view.tracking.AppTracking;

/**
 * Created by swapnil.gawande on 2/20/2018.
 */

public class SentMoneyRequestsPresenter extends NBBasePresenter<SentMoneyRequestsView> {
    private static final String REQUESTER_ROLE = "RQR";
    private final Analytics mAnalytics;
    private final ErrorHandler mErrorHandler;
    private final MoneyRequestsUseCase moneyRequestsUseCase;
    private final MoneyRequestsActionUseCase moneyRequestsActionUseCase;
    private final MoneyRequestsDataModelToViewModelMapper dataModelToViewModelMapper;

    @Inject
    SentMoneyRequestsPresenter(@NonNull final Analytics analytics,
                               @NonNull final ErrorHandler errorHandler,
                               @NonNull final MoneyRequestsUseCase moneyRequestsUseCase,
                               @NonNull final MoneyRequestsActionUseCase moneyRequestsActionUseCase,
                               @NonNull MoneyRequestsDataModelToViewModelMapper dataModelToViewModelMapper) {
        this.mAnalytics = analytics;
        this.mErrorHandler = errorHandler;
        this.moneyRequestsUseCase = moneyRequestsUseCase;
        this.moneyRequestsActionUseCase = moneyRequestsActionUseCase;
        this.dataModelToViewModelMapper = dataModelToViewModelMapper;
    }

    void getMoneyRequests() {
        moneyRequestsUseCase.execute(REQUESTER_ROLE)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showLoadingView();
                    }
                })
                .doFinally(() -> {
                    if (view != null) {
                        view.hideLoadingView();
                    }
                })
                .subscribe(this::handleMoneyRequestsData, this::handleError);
    }

    void onRemindClicked(MoneyRequestsViewModel moneyRequestsViewModel) {
        mAnalytics.sendEvent(AppTracking.TAG_PAY_ME_SENT_REMIND, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);

        PaymentActionDataModel paymentActionDataModel = new PaymentActionDataModel();
        paymentActionDataModel.setPaymentRequestAction(PaymentRequestActionType.REMIND);
        paymentActionDataModel.setPaymentRequestId(moneyRequestsViewModel.getPaymentRequestId());

        moneyRequestsActionUseCase.execute(paymentActionDataModel)
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showLoadingView();
                    }
                })
                .doFinally(() -> {
                    if (view != null) {
                        view.hideLoadingView();
                    }
                })
                .subscribe(this::handleActionResultData, this::handleError);
    }

    private void handleActionResultData(MoneyRequestsActionDataModel moneyRequestsActionDataModel) {
        if (view != null) {
            ResultDetailDataModel resultDetailModel = moneyRequestsActionDataModel.getMetadataModel().getResultDetailModel();
            if (ResultErrorCodes.VALID_R_RESULT.equals(resultDetailModel.getResult())) {
                view.updateMoneyRequests();
            } else {
                view.showError(resultDetailModel.getReason());
            }
        }
    }

    void handleError(Throwable throwable) {
        if (view != null) {
            view.showError(mErrorHandler.getErrorMessage(throwable).getMessage());
        }
    }

    private void handleMoneyRequestsData(MoneyRequestsMainDataModel moneyRequestsMainDataModel) {
        ArrayList<MoneyRequestsDataModel> moneyRequestsDataModels = moneyRequestsMainDataModel.getMoneyRequestsDataModels();
        if (moneyRequestsDataModels != null && moneyRequestsDataModels.size() > Constants.ZERO) {
            List<MoneyRequestsViewModel> moneyRequestsViewModels = dataModelToViewModelMapper.mapMoneyRequestsDataModelToViewModel(moneyRequestsDataModels);
            List<MoneyRequestsViewModel> pendingMoneyRequestsViewModels = new ArrayList<>();
            List<MoneyRequestsViewModel> paidMoneyRequestsViewModels = new ArrayList<>();
            for (MoneyRequestsViewModel moneyRequestsViewModel : moneyRequestsViewModels) {
                if ((FormattingUtil.checkIfDatesAreEqual(moneyRequestsViewModel.getProcessDate(),
                        moneyRequestsViewModel.getCurrentDate())
                        || (FormattingUtil.compareDates(moneyRequestsViewModel.getProcessDate(), moneyRequestsViewModel.getCurrentDate())
                        && ((!moneyRequestsViewModel.isPaid() && moneyRequestsViewModel.isPayLater()
                        && !moneyRequestsViewModel.isReject()) || PaymentRequestStatus.PENDING.equals(moneyRequestsViewModel.getRequestStatus()))))) {

                    switch (moneyRequestsViewModel.getRequestStatus()) {
                        case PaymentRequestStatus.PENDING:
                            pendingMoneyRequestsViewModels.add(moneyRequestsViewModel);
                            break;
                        case PaymentRequestStatus.PAID:
                            paidMoneyRequestsViewModels.add(moneyRequestsViewModel);
                            break;
                    }
                }
            }
            LinkedHashMap<Integer, List<MoneyRequestsViewModel>> moneyRequestsViewModelMap = new LinkedHashMap<>();
            if (pendingMoneyRequestsViewModels.size() > Constants.ZERO) {
                Collections.sort(pendingMoneyRequestsViewModels, MoneyRequestsViewModel.PAY_LATER_COMPARATOR);
                moneyRequestsViewModelMap.put(MoneyRequestsType.PENDING_REQUESTS, pendingMoneyRequestsViewModels);
            }
            if (paidMoneyRequestsViewModels.size() > Constants.ZERO) {
                moneyRequestsViewModelMap.put(MoneyRequestsType.PAID_REQUESTS, paidMoneyRequestsViewModels);
            }
            if (view != null) {
                List<MoneyRequestsAdapterModel> moneyRequestsAdapterModels = showMoneyRequests(moneyRequestsViewModelMap);
                view.showMoneyRequests(moneyRequestsAdapterModels);
            }
        } else {
            if (view != null) {
                view.showEmptyListIndicator();
            }
        }
    }

    private List<MoneyRequestsAdapterModel> showMoneyRequests(LinkedHashMap<Integer, List<MoneyRequestsViewModel>> moneyRequestsViewModelsMap) {
        List<MoneyRequestsAdapterModel> moneyRequestsAdapterModels = new ArrayList<>();
        for (Map.Entry<Integer, List<MoneyRequestsViewModel>> moneyRequestsViewModelEntry : moneyRequestsViewModelsMap.entrySet()) {
            MoneyRequestsAdapterModel adapterModel = new MoneyRequestsAdapterModel();
            adapterModel.setHeader(moneyRequestsViewModelEntry.getKey());
            adapterModel.setMoneyRequestsViewModels(moneyRequestsViewModelEntry.getValue());
            moneyRequestsAdapterModels.add(adapterModel);
        }
        return moneyRequestsAdapterModels;
    }
}

