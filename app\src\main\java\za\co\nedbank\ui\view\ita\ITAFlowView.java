package za.co.nedbank.ui.view.ita;

import androidx.annotation.StringRes;

import com.entersekt.sdk.Auth;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.nid_sdk.main.interaction.TransaktConfig;
import za.co.nedbank.uisdk.widget.NBSnackbar;

interface ITAFlowView extends NBBaseView {

    void finishAffinity();

    String getITAActionType();

    void setITAActionType(String itaActionType);

    boolean isFlowForLoggedOut();

    void setFlowForLoggedOut(boolean flowForLoggedOut);

    void showCountingProgress(final int value);

    void showHideCountdownTimer(boolean isShow);

    void showError(final String title,
                   final String message,
                   final String actionTitle,
                   final int duration,
                   final NBSnackbar.OnActionClickListener action);

    String getString(@StringRes int resId);

    Auth getAuth();

    TransaktConfig getTransaktConfig();

    int getTimeDifference(Auth auth);
}
