/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.tracking;


public class AppTracking {

    public static final String KEY_BUY = "buy";
    public static final String QUICKPAY = "quickpay";

    public static final String DASHBOARD_VIEW_OFFER = "GAPL_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_OD = "GAOD_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_ODLI = "GAOD_LINC_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_CCLI = "GACC_LINC_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_GOLDEN_GOOSE = "GAUL_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_CCAMEX = "GACCAMEX_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_SAA = "GACCSAA_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_CCBT = "GACCBT_Dash_ViewOffer";

    public static final String DASHBOARD_VIEW_OFFER_INSURANCE = "Ins_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_INVESTMENT = "Inv_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_CC = "GACC_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_EDB_SB = "GASB_Dash_ViewOffer";
    public static final String DASHBOARD_VIEW_OFFER_HL = "GAHL_dash_notifications";

    public static final String SCREEN_NOTIFICATION_DETAILS = "notification_details";
    public static final String MYACCOUNTS_VIEW_MORE_AVO = "myaccounts_view_more_avo";
    public static final String MYACCOUNTS_GO_TO_AVO = "myaccounts_go_to_avo";
    public static final String MY_ACCOUNTS_AVO_SUCCESS = "avo_banner_click_success";
    public static final String MY_ACCOUNTS_AVO_FAILURE = "avo_banner_click_failure";
    public static final String MY_ACCOUNTS_BANNER_SUCCESS = "myaccounts_banner_success";
    public static final String MY_ACCOUNTS_BANNER_FAILURE = "myaccounts_banner_failure";
    public static final String VAL_AVO_FEATURE = "Avo Banner";


    /***********Non TP Tracking Events***************/
    public static final String NON_TP_LOAN_LOAD = "EN_3_Non_TP_Load_Loan";
    public static final String NON_TP_LOAD_INVESTMENT = "EN_3_Non_TP_Load_Investment";
    public static final String NON_TP_ACCOUNT_SCREEN_LOAD = "EN_3_Non_TP_Screen_Load";
    public static final String EN_3_NON_TP_BANK_CARD = "EN_3_Non_TP_Bank_Card";
    public static final String EN_3_NON_TP_BORROW_CARD = "EN_3_Non_TP_Borrow_Card";
    public static final String EN_3_NON_TP_FINANCIAL_PLANNER_CARD = "EN_3_Non_TP_Financial_Planner_Card";
    public static final String EN_3_NON_TP_APPLY_SCREEN_LOAD = "EN_3_Non_TP_Apply_Screen_Load";

    public static final String CLICK_SHARE_POP = "click_share_pop";
    public static final String CLICK_SHARE_POP_PAY_FLOW = "payflow_share_pop";
    public static final String CLICK_PAY_AGAIN = "click_pay_again";
    public static final String PAY_AGAIN_NEW_ONCE_OFF_PAYMENTS = "pay_again_new_once_off_payments";
    public static final String PAY_AGAIN_PAY_FLOW_EXISTING_RECIPIENTS = "pay_again_payflow_existing";

    public static final String OVERVIEW_SHARE_POP_ONCE_OFF = "overview_share_pop_once_off";
    public static final String OVERVIEW_PAY_AGAIN_ONCE_OFF = "overview_pay_again_once_off";

    public static final String MY_ACCOUNTS_MORE = "myaccounts_more";
    public static final String MY_ACCOUNTS_RECIPIENTS = "myaccounts_recipients";
    public static final String NON_TP_APPLICATIONS = "applications";

    public static final String MY_ACCOUNTS_TRANSACT = "myaccounts_transact";
    public static final String MY_ACCOUNTS_CARDS = "myaccounts_cards";
    public static final String MY_ACCOUNTS_OVERVIEW = "myaccounts_overview";
    public static final String MY_ACCOUNTS_WIDGET_APPLY = "myaccounts_widget_apply";
    public static final String MY_ACCOUNTS_WIDGET_SHOP ="myaccounts_widget_shop";
    public static final String MY_ACCOUNTS_WIDGET_REQUEST_TO_PAY = "myaccounts_widget_request_to_pay";

    public static final String SMA_DASHBOARD_CLICK ="sma_dashboard_click";
    public static final String MY_ACCOUNTS_WIDGET_INSURE = "myaccounts_widget_insure";
    public static final String MY_ACCOUNTS_WIDGET_STATEMENTS = "myaccounts_widget_statements";
    public static final String MY_ACCOUNTS_REPORT_FRAUD = "myaccounts_widget_reportfraud";
    public static final String MY_ACCOUNTS_PAY_ME = "myaccounts_widget_payme";
    public static final String MY_ACCOUNTS_QUICK_PAY = "myaccounts_widget_quickpay";
    public static final String MY_ACCOUNTS_CHAT = "myaccounts_chat";
    public static final String MY_ACCOUNTS_NOTIFICATIONS = "myaccounts_notifications";
    public static final String MY_ACCOUNTS_APPLY = "myaccounts_apply";
    public static final String MY_ACCOUNTS_ACCOUNTS = "myaccounts_accounts";
    public static final String MY_ACCOUNTS_LATEST = "myaccounts_latest";
    public static final String MY_ACCOUNTS_PAY_RECEIVE = "float_pay_and_receive_select_action";
    public static final String MY_ACCOUNTS_SELECT_ACCOUNT = "myaccounts_select_account";
    public static final String MY_ACCOUNTS_WIDGET_LATEST = "myaccounts_widget_latest";
    public static final String MY_ACCOUNTS_WIDGET_OFFERS = "myaccounts_widget_offers";
    String VAL_REWARDS_GREENBACKS = "Rewards;Greenbacks;;";
    public static final String HOME_LOAN_WIDGET_CLICKED = "click_homeloan_widget";

    /***********Retention Tracking Events***************/
    public static final String RETENTION_SCREEN_WELCOME = "AR Welcome";
    public static final String RETENTION_CLICK_WELCOME_START = "AR1_Start_Journey";
    public static final String RETENTION_CLICK_WELCOME_SKIP = "AR1_Skip_Journey";
    public static final String RETENTION_SCREEN_TASK_SELECTION = "AR Feature Options";
    public static final String RETENTION_CARD_LOGIN_AND_SECURITY = "AR1_Login_And_Security";
    public static final String RETENTION_CARD_SHARE_ACCOUNT_INFO = "AR1_Share_Account_Info";
    public static final String RETENTION_CARD_PROFILE_LIMITS = "AR1_Profile_Limits";
    public static final String RETENTION_CARD_DEBIT_ORDER = "AR1_Debit_Order_Switching";
    public static final String RETENTION_CLICK_TASK_SELECTION_BACK = "AR1_Options_Back";
    public static final String RETENTION_CLICK_TASK_SELECTION_CLOSE = "AR1_Options_Close";
    public static final String RETENTION_SCREEN_MULTIPLE_SHARE_ACCOUNTS = "AR Share Details";
    public static final String RETENTION_CLICK_MULTIPLE_SHARE_ACCOUNTS_BACK = "AR1_Share_Details_Back";
    public static final String RETENTION_CARD_MULTIPLE_SHARE_ACCOUNTS_SELECT = "AR1_Select_Account";
    public static final String RETENTION_CLICK_MULTIPLE_SHARE_ACCOUNTS_CLOSE = "AR1_Share_Details_Close";
    public static final String RETENTION_SCREEN_FEEDBACK = "AR Feedback";
    public static final String RETENTION_CLICK_FEEDBACK_SUBMIT = "AR1_Feedback_Submit";
    public static final String RETENTION_CLICK_FEEDBACK_SKIP = "AR1_Feedback_Skip";
    public static final String RETENTION_SCREEN_THANK_YOU = "AR Thank You";
    public static final String RETENTION_CLICK_THANK_YOU_DONE = "AR1_Thank_You_Done";
    public static final String RETENTION_SCREEN_NOTIFICATION_FEATURE_OPTIONS = "AR Notification Feature Options";
    public static final String RETENTION_CLICK_NOTIFICATION_FEATURE_OPTIONS_BACK = "AR1_Options_2nd_Back";
    public static final String RETENTION_CARD_TODO_SHARE_ACCOUNT_INFO = "AR1_Share_Account_Info";
    public static final String RETENTION_CARD_TODO_LOGIN_AND_SECURITY = "AR1_Login_And_Security";
    public static final String RETENTION_CARD_TODO_PROFILE_LIMITS = "AR1_Profile_Limits";
    public static final String RETENTION_CARD_TODO_DEBIT_ORDER = "AR1_Debit_Order_Switching";
    public static final String RETENTION_CARD_COMPLETED_SHARE_ACCOUNT_INFO = "AR1_Share_Account_Info_Flow";
    public static final String RETENTION_CARD_COMPLETED_LOGIN_AND_SECURITY = "AR1_Login_And_Security_Flow";
    public static final String RETENTION_CARD_COMPLETED_PROFILE_LIMITS = "AR1_Profile_Limits_Flow";
    public static final String RETENTION_CARD_COMPLETED_DEBIT_ORDER = "AR1_Debit_Order_Switching_Flow";
    public static final String RETENTION_SCREEN_SHARE_ACCOUNT_INFORMATION = "AR Share Account Information";
    public static final String RETENTION_SCREEN_LOGIN_AND_SECURITY_INFORMATION = "AR Login&Security Information";
    public static final String RETENTION_SCREEN_PROFILE_LIMITS_INFORMATION = "AR Profile Limits Information";
    public static final String RETENTION_SCREEN_DEBIT_ORDER_INFORMATION = "AR Debit Order Switching Information";
    public static final String RETENTION_CLICK_SHARE_ACCOUNT_INFORMATION_BACK = "AR1_Share_Account_Info_Details_Back";
    public static final String RETENTION_CLICK_LOGIN_AND_SECURITY_INFORMATION_BACK = "AR1_Login_And_Security_Details_Back";
    public static final String RETENTION_CLICK_PROFILE_LIMITS_INFORMATION_BACK = "AR1_Profile_Limits_Details_Page_Back";
    public static final String RETENTION_CLICK_DEBIT_ORDER_INFORMATION_BACK = "AR1_DOS_Page_Back";

    /***********Pay Me Tracking Events***************/
    public static final String TAG_PAY_ME_SUBMIT = "payme_submit";
    public static final String TAG_PAY_ME_REQUEST_RECEIVED = "payme_view_request_received";
    public static final String TAG_PAY_ME_REQUEST_SENT = "payme_view_request_sent";
    public static final String TAG_PAY_ME_ALLOW_CONTACT_ACCESS = "payme_allow_contactlist_access";
    public static final String TAG_PAY_ME_RESTRICT_CONTACT_ACCESS = "payme_restrict_contactlist_access";
    public static final String TAG_PAY_ME_SUCCESSFUL = "payme_successful";
    public static final String TAG_PAY_ME_FAILURE = "payme_failure";
    public static final String TAG_PAY_ME_RECEIVED_PAY_NOW = "payme_received_pay_now";
    public static final String TAG_PAY_ME_RECEIVED_PAY_LATER = "payme_received_pay_later";
    public static final String TAG_PAY_ME_RECEIVED_REJECT = "payme_received_reject";
    public static final String TAG_PAY_ME_SENT_REMIND = "payme_sent_remind";

    public static final String TAG_PAY_ME_PAY_NOW_PAYMENT_REVIEW = "payme_paynow_payment_review";
    public static final String TAG_PAY_ME_PAY_NOW_PAYMENT_CONFIRM = "payme_paynow_payment_confirm";
    public static final String TAG_PAY_ME_PAY_NOW_PAYMENT_SUCCESSFUL = "payme_paynow_payment_successful";
    public static final String TAG_PAY_ME_PAY_NOW_PAYMENT_FAILURE = "payme_paynow_payment_failure";

    /*AppsFlyer events*/
    public static final String AF_AVO_REDIRECT = "af_avo_redirect";

    public static final String LETS_GO_SCAN_TO_PAY_SECURITY = "click_lets_go_scantopay_security";
    public static final String SKIP_SCAN_TO_PAY_SECURITY = "click_skip_scantopay_security";

    public static final String SHARE_POP_WITHIN90DAYS_SUCCESS = "share_pop_within90days_success";
    public static final String SHARE_POP_FAILURE = "share_pop_failure";

    public static final String VAL_FEATURE_CATEGORY_SHARE_POP = "Document request";
    public static final String VAL_FEATURE_SHARE_POP = "Share proof of payment";
    public static final String VAL_DOCUMENT_TYPE_SHARE_POP = "Proof of payment";
    public static final String VAL_TIME_FRAME_SHARE_POP_WITHIN_90 = "With In 90 days";
    public static final String VAL_TIME_FRAME_SHARE_POP_BEYOND_90 = "Beyond 90 days";
    public static final String AF_FAMILY_BANKING_VIEW = "af_family_banking_view";

    /*Beyond 90 days payment */
    public static final String CLICK_VIEW_MORE_BEYOND_90DAYS = "click_viewmore_Beyond_90days";
    public static final String CLICK_SHARE_POP_BEYOND_90DAYS = "click_share_pop_beyond_90days";
    public static final String SHARE_POP_BEYOND90DAYS_SUCCESS = "Share_pop_beyond90days_success";

    public static final String FAMILY_BANKING_VIEW = "fb_view";
    public static final String FAMILY_BANKING_VIEW_DETAILS = "fb_view_details";

    /*Track Hide/show balance*/
    public static final String MY_ACCNT_BAL_INFO_HIDE = "my_accnt_bal_info_hide";
    public static final String MY_ACCNT_BAL_INFO_SHOW = "my_accnt_bal_info_show";
    public static final String MY_ACCNT_BAL_INFO_UNDO = "my_accnt_bal_info_undo";






}
