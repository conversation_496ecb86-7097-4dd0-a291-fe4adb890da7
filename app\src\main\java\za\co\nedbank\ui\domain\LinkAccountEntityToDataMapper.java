/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain;

import java.util.ArrayList;
import java.util.List;

import za.co.nedbank.ui.data.entity.LinkAccountEntity;
import za.co.nedbank.ui.domain.model.LinkAccountData;

/**
 * Created by piyushgupta01 on 8/2/2017.
 */

public class LinkAccountEntityToDataMapper {

    public static List<LinkAccountData> mapData(List<LinkAccountEntity> linkAccountEntityList) {
        List<LinkAccountData> linkAccountDataList = null;
        if (null != linkAccountEntityList && linkAccountEntityList.size() > 0) {
            linkAccountDataList = new ArrayList<>(linkAccountEntityList.size());
            for (LinkAccountEntity linkAccountEntity : linkAccountEntityList) {
                linkAccountDataList.add(transformLinkAccountEntity(linkAccountEntity));
            }
        }
        return linkAccountDataList;
    }

    private static LinkAccountData transformLinkAccountEntity(LinkAccountEntity linkAccountEntity) {
        LinkAccountData linkAccountData = new LinkAccountData();
        linkAccountData.setHeader(linkAccountEntity.getHeader());
        linkAccountData.setDescription(linkAccountEntity.getDescription());
        linkAccountData.setImageResId(linkAccountEntity.getImageResId());
        linkAccountData.setInstructions(linkAccountEntity.getInstructions());
        linkAccountData.setButtonText(linkAccountEntity.getButtonText());
        return linkAccountData;
    }
}
