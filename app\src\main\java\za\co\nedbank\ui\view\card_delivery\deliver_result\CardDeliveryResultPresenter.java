
package za.co.nedbank.ui.view.card_delivery.deliver_result;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.card_delivery.PostCardDeliveryOptionRequestData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.card_delivery.PostCardDeliveryOptionUseCase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.card_delivery.CardDeliveryOptionsEnum;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics;

import static za.co.nedbank.core.view.card_delivery.CardDeliveryOptionsEnum.DELIVERY;
import static za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics.EVENT_CM_ORDER_CARD_FAILURE;

public class CardDeliveryResultPresenter extends NBBasePresenter<CardDeliveryResultView> {

    private final NavigationRouter navigationRouter;
    private final PostCardDeliveryOptionUseCase postCardDeliveryOptionUseCase;
    private final ApplicationStorage applicationStorage;
    private final Analytics analytics;
    private final ErrorHandler errorHandler;

    @Inject
    public CardDeliveryResultPresenter(NavigationRouter navigationRouter,
                                       PostCardDeliveryOptionUseCase postCardDeliveryOptionUseCase,
                                       @Named("memory") ApplicationStorage applicationStorage,
                                       Analytics analytics, ErrorHandler errorHandler) {
        this.navigationRouter = navigationRouter;
        this.postCardDeliveryOptionUseCase = postCardDeliveryOptionUseCase;
        this.applicationStorage = applicationStorage;
        this.analytics = analytics;
        this.errorHandler = errorHandler;
    }

    public void checkResult(String flow) {
        if (isEfica(flow)) {
            handleEficaFlowResutl();
        } else if (isReplaceCard(flow)) {
            handleReplaceCardFlowResutl();
        }
    }

    private boolean isReplaceCard(String flow) {
        return NavigationTarget.VALUE_CARD_DELIVERY_FLOW_BLOCK_AND_REPLACE.equals(flow);
    }

    private boolean isEfica(String flow) {
        return NavigationTarget.VALUE_CARD_DELIVERY_FLOW_EFICA.equals(flow);
    }

    private void handleReplaceCardFlowResutl() {
        String selectedDeliveryOption = view.getSelectedDeliveryOption();
        if (StringUtils.isNullOrEmpty(selectedDeliveryOption)) {
            //card ordering toggle off/delivery option API error
            sendPageAnalytics(CardDeliveryAnalytics.VAL_DELIVERY_OPTIONS_ERROR, CardDeliveryAnalytics.VAL_CARD_REPLACEMENT, CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            sendEventName();
            view.showReplaceCardServiceUnavailableError();
        } else if (!view.isOperationSuccess() && selectedDeliveryOption.equals(DELIVERY.getValue())) {
            sendPageAnalytics(CardDeliveryAnalytics.VAL_ADDRESS_DELIVERY_ERROR, CardDeliveryAnalytics.VAL_DELIVERY_VIA_COURIER, CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            view.showReplaceCardOrderingFailure();
        } else if (!view.isOperationSuccess()) {
            sendPageAnalytics(CardDeliveryAnalytics.VAL_BRANCH_COLLECTION_ERROR, CardDeliveryAnalytics.VAL_DELIVERY_TO_A_NEDBANK_BRANCH, CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            view.showReplaceCardOrderingFailure();
        } else {
            showSuccess(selectedDeliveryOption);
        }

    }

    private void sendEventName() {

        Map<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);

        contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
        contextData.setFeature(view.getCardActionName());

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductAccount(getProductName());
        contextData.setFeatureCategoryCount();
        contextData.setFeatureCount();

        if (view.isLockerLocationError())
            contextData.setSubFeature(CardDeliveryAnalytics.PICK_IT_UP_FROM_DSV);

        analytics.sendEventActionWithMap(EVENT_CM_ORDER_CARD_FAILURE, contextDataMap);

    }

    private void showSuccess(String selectedDeliveryOption) {
        if (CardDeliveryOptionsEnum.LOCKER.getValue().equals(selectedDeliveryOption)) {
            view.showLockerSuccess();
        } else if (DELIVERY.getValue().equals(selectedDeliveryOption)) {
            view.showDeliverToMeSuccess();
        } else if (CardDeliveryOptionsEnum.BRANCH_PICK.getValue().equals(selectedDeliveryOption)) {
            view.showBranchPickSuccess();
        }
    }

    private void handleEficaFlowResutl() {
        String selectedDeliveryOption = view.getSelectedDeliveryOption();
        if (StringUtils.isNullOrEmpty(selectedDeliveryOption)) {
            //card ordering toggle off/delivery option API error
            sendPageAnalytics(CardDeliveryAnalytics.VAL_CARD_ORDERING_LEAD, CardDeliveryAnalytics.VAL_DELIVERY_OPTIONS, CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            view.showEficaCardServiceUnavailableError();
        } else if (view.isLockerLocationError()) {
            sendPageLockerErrorAnalytics();
            callPostCardDeliverApi();
            view.showEficaLockerListError();
        } else {/*Failure and success has same screen*/
            sendPageAnalytics(CardDeliveryAnalytics.VAL_CARD_ORDERING_LEAD, CardDeliveryAnalytics.VAL_DELIVERY_OPTIONS, CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            showSuccess(selectedDeliveryOption);
        }
    }

    private void sendPageLockerErrorAnalytics() {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);
        contextData.setPageCategory(CardDeliveryAnalytics.VAL_DELIVERY_TO_A_DSV_LOCKER);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductAccount(getProductName());

        contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
        contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
        contextData.setSubFeature(CardDeliveryAnalytics.PICK_IT_UP_FROM_DSV);
        analytics.sendEventStateWithMap(CardDeliveryAnalytics.VAL_CARD_ORDERING_LEAD, contextData.getCdata());

    }

    private void sendPageAnalytics(String pageName, String pageCategory, String featureCategory) {

        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);
        contextData.setPageCategory(pageCategory);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductAccount(getProductName());

        contextData.setFeatureCategory(featureCategory);
        if (isEfica(view.getFlow())) {
            contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
        } else if (isReplaceCard(view.getFlow())) {
            contextData.setFeature(view.getCardActionName());
            contextData.setSubFeature(view.getCardDeliverySubFeature());
        }
        analytics.sendEventStateWithMap(pageName, contextData.getCdata());

    }

    private void callPostCardDeliverApi() {
        postCardDeliveryOptionUseCase
                .execute(createPostDeliveryOptionRequestEntity())
                .compose(bindToLifecycle())
                .subscribe(d -> { /*ignored*/}, throwable -> {
                    sendErrorAnalytics(true, errorHandler.getErrorMessage(throwable).getMessage(), null);
                });
    }

    private void sendErrorAnalytics(boolean isApiFailure, String message, String apiErrorCode) {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductAccount(getProductName());
        contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
        contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
        contextData.setFeatureCategoryCount();
        contextData.setFeatureCount();
        contextData.setSubFeature(view.getCardDeliverySubFeature());
        contextData.setSubFeatureCount();

        analytics.trackFailure(isApiFailure, EVENT_CM_ORDER_CARD_FAILURE, CardDeliveryAnalytics.API_USE_CAR, message, apiErrorCode, contextData.getCdata());

    }

    private PostCardDeliveryOptionRequestData createPostDeliveryOptionRequestEntity() {
        PostCardDeliveryOptionRequestData requestData = new PostCardDeliveryOptionRequestData();
        requestData.setLocationName(StringUtils.EMPTY_STRING);
        requestData.setBranchCode(StringUtils.EMPTY_STRING);
        requestData.setDeliveryOptionCode(CardDeliveryOptionsEnum.LOCKER.getValue());
        String sessionId = applicationStorage.getString(StorageKeys.FICA_SESSION_ID, StringUtils.EMPTY_STRING);
        requestData.setSessionId(sessionId);
        return requestData;
    }

    public void navigateToNextScreen() {
        sendExitFlowAnalytics();
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.CARD_DELIVERY_OPTIONS);
        if (isEfica(view.getFlow())) {
            navigationTarget
                    .withParam(NavigationTarget.PARAM_IS_CARD_ORDERING_SUCCESS, view.isOperationSuccess())
                    .withIntentFlagClearTopSingleTop(true);
        }else{
            navigationTarget
                    .withParam(NavigationTarget.PARAM_IS_CARD_ORDERING_SUCCESS, view.isOperationSuccess())
                    .withParam(NavigationTarget.VALUE_CARD_DELIVERY_FLOW_EFICA, false)
                    .withIntentFlagClearTopSingleTop(true);
        }
        navigationRouter.navigateTo(navigationTarget);
        view.close();
    }

    private void sendExitFlowAnalytics() {
        String selectedDeliveryOption = view.getSelectedDeliveryOption();
        /*in case of card delivery service unavailable*/
        if (StringUtils.isNullOrEmpty(selectedDeliveryOption)) {
            Map<String, Object> contextDataMap = new HashMap<>();
            AdobeContextData contextData = new AdobeContextData(contextDataMap);
            contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
            contextData.setProductAccount(getProductName());

            if (isEfica(view.getFlow())) {
                contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
                contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
            } else if (isReplaceCard(view.getFlow())) {
                contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
                contextData.setFeature(view.getCardActionName());
            }

            contextData.setFeatureCategoryCount();
            contextData.setFeatureCount();
            if (view.isLockerLocationError())
                contextData.setSubFeature(CardDeliveryAnalytics.PICK_IT_UP_FROM_DSV);

            analytics.sendEventActionWithMap(isEfica(view.getFlow()) ? CardDeliveryAnalytics.EVENT_CM_CLOSE_ARRANGE_DELIVERY : CardDeliveryAnalytics.EVENT_CM_CLOSE, contextDataMap);
        }
    }

    private String getProductName() {
        return applicationStorage.getString(StorageKeys.CARD_DELIVERY_SELECTED_PRODUCT, StringUtils.EMPTY_STRING);
    }
}
