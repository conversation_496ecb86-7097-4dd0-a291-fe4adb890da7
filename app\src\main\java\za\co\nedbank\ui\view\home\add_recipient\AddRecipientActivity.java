/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.Nullable;

import com.jakewharton.rxbinding2.widget.RxTextView;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Map;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.recipient.EmailViewDataModel;
import za.co.nedbank.databinding.ActivityAddRecipientBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.component.CompatEditText;
import za.co.nedbank.uisdk.widget.NBSnackbar;


/**
 * Created by priyadhingra on 9/4/2017.
 */

public class AddRecipientActivity extends BaseRecipientActivity implements AddRecipientView, BankAccountAdapter.IActivityAdapterComListener {

    @Inject
    AddRecipientPresenter mAddRecipientPresenter;
    private ActivityAddRecipientBinding binding;
    private int mMatchBackNumber;

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        binding = ActivityAddRecipientBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        super.onCreate(savedInstanceState, mAddRecipientPresenter);
        binding.toolbar.setTitle(getString(R.string.add_new_recipient));
        initToolbar(binding.toolbar, true, true);
        if (getIntent() != null) {
            mRecipientViewModel = getIntent().getParcelableExtra(Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL);
            setUpViewAsPerIntentData();
        }
        setupRecyclerViews();
        setLoadingButtonEnabled(false);
        addRecipientNameListener(etRecipientName);
        setUpAccessibility();
    }

    private void setUpViewAsPerIntentData() {
        if (mRecipientViewModel != null && !TextUtils.isEmpty(mRecipientViewModel.getRecipientName())) {
            binding.tvNameInitial.setText(StringUtils.getNameInitials(mRecipientViewModel.getRecipientName()));
            etRecipientName.setText(mRecipientViewModel.getRecipientName());
        }
    }
    private void setUpAccessibility() {
        if(binding.toolbar.getChildCount() > 0) {
            binding.toolbar.getChildAt(0).setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO);
        }
        if(getSupportActionBar() != null) {
            getSupportActionBar().setHomeActionContentDescription(getString(R.string.back_to_view_recipient));
        }
        binding.etRecipientName.getInputField().setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO);

        addListenerForRecipientName(binding.etRecipientName);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            Bundle bundle = intent.getExtras();
            if (bundle != null && bundle.containsKey(NavigationTarget.IS_ENROLLMENT_SUCCESS)) {
                boolean isEnrollmentSuccess = bundle.containsKey(NavigationTarget.IS_ENROLLMENT_SUCCESS)
                        && bundle.getBoolean(NavigationTarget.IS_ENROLLMENT_SUCCESS, false);
                if (isEnrollmentSuccess) {
                    mAddRecipientPresenter.callRecipientStatus(mAddRecipientPresenter.getVerificationReferenceId());
                } else {
                    mAddRecipientPresenter.moveToStopScreen();
                }
            }
        }
    }

    @Override
    public void handleFewRecipientAdditionFailedError(String... message) {
        Bundle bundle = new Bundle();
        bundle.putBoolean(Constants.EXTRAS.IS_BENEFICIARY_LIST_REFRESH, true);
        Intent intent = new Intent();
        intent.putExtras(bundle);
        intent.putExtra(Constants.EXTRAS.ERROR_MESSAGE,message);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }
    @Override
    protected void onResume() {
        super.onResume();
        mAddRecipientPresenter.bind(this);
        mAddRecipientPresenter.checkToggleForShapId();
        mAddRecipientPresenter.fetchFastPayDomainList();
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
        mAddRecipientPresenter.unbind();
    }

    @Override
    protected void setUpRecyclerViewAdapter() {
        mBankAccountViewDataModelList = new ArrayList<>();
        mMobileNumberViewDataModelList = new ArrayList<>();
        mElectricityMeterViewDataModelList = new ArrayList<>();
        mCreditCardViewDataModelList = new ArrayList<>();
        mShapIDViewDataModelList = new ArrayList<>();
        mEmailViewDataModelList = new ArrayList<>();
        mEmailViewDataModelList.add(new EmailViewDataModel());

        if(mRecipientViewModel != null){
            if (CollectionUtils.isNotEmpty(mRecipientViewModel.getBankAccountViewDataModelList())){
                mBankAccountViewDataModelList = mRecipientViewModel.getBankAccountViewDataModelList();
            }
            if (CollectionUtils.isNotEmpty(mRecipientViewModel.getMobileNumberViewDataModelList())){
                mMobileNumberViewDataModelList = mRecipientViewModel.getMobileNumberViewDataModelList();
            }
            if (CollectionUtils.isNotEmpty(mRecipientViewModel.getElectricityMeterViewDataModelList())){
                mElectricityMeterViewDataModelList = mRecipientViewModel.getElectricityMeterViewDataModelList();
            }
            if (CollectionUtils.isNotEmpty(mRecipientViewModel.getCreditCardViewDataModelList())){
                mCreditCardViewDataModelList = mRecipientViewModel.getCreditCardViewDataModelList();
            }
            if (CollectionUtils.isNotEmpty(mRecipientViewModel.getShapIdViewDataModelList())) {
                mShapIDViewDataModelList = mRecipientViewModel.getShapIdViewDataModelList();
            }
            if (CollectionUtils.isNotEmpty(mRecipientViewModel.getEmailViewDataModelList())){
                mEmailViewDataModelList.clear();
                mEmailViewDataModelList = mRecipientViewModel.getEmailViewDataModelList();
            }
        }
        mBankAccountAdapter = new BankAccountAdapter(AddRecipientActivity.this, mBankAccountNbFlexibleItemCountRecyclerviewModel, mBankAccountViewDataModelList, AddRecipientActivity.this, null);
        mBankAccountAdapter.setINBRecyclerViewListener(this);
        mBankAccountAdapter.setIActivityAdapterComListener(this);
        mBankAccountAdapter.setEditable(true);
        rvAccount.setAdapter(mBankAccountAdapter);

        mMobileNumberAdapter = new MobileNumberAdapter(AddRecipientActivity.this,mMobileNumberNbFlexibleItemCountRecyclerviewModel, mMobileNumberViewDataModelList,AddRecipientActivity.this, null);
        mMobileNumberAdapter.setINBRecyclerViewListener(this);
        mMobileNumberAdapter.setIActivityAdapterComListener(this);
        mMobileNumberAdapter.setEditable(true);
        rvMobileNumber.setAdapter(mMobileNumberAdapter);

        mElectricityMeterViewDataModelList = new ArrayList<>();
        mElectricityMeterAdapter = new ElectricityMeterAdapter(AddRecipientActivity.this,mElectricityNbFlexibleItemCountRecyclerviewModel, mElectricityMeterViewDataModelList,AddRecipientActivity.this, null);
        mElectricityMeterAdapter.setINBRecyclerViewListener(this);
        mElectricityMeterAdapter.setIActivityAdapterComListener(this);
        mElectricityMeterAdapter.setEditable(true);
        rvElectricity.setAdapter(mElectricityMeterAdapter);

        mCreditCardAdapter = new CreditCardAdapter(AddRecipientActivity.this, mCreditCardNbFlexibleItemCountRecyclerviewModel, mCreditCardViewDataModelList, AddRecipientActivity.this, null);
        mCreditCardAdapter.setINBRecyclerViewListener(this);
        mCreditCardAdapter.setIActivityAdapterComListener(this);
        mCreditCardAdapter.setEditable(true);
        rvCreditCard.setAdapter(mCreditCardAdapter);

        mShapIDAdapter = new ShapIDAdapter(AddRecipientActivity.this, mShapIdNbFlexibleItemCountRecyclerviewModel, mShapIDViewDataModelList, AddRecipientActivity.this, null);
        mShapIDAdapter.setINBRecyclerViewListener(this);
        mShapIDAdapter.setIActivityAdapterComListener(this);
        mShapIDAdapter.setEditable(true);
        rvShapeID.setAdapter(mShapIDAdapter);

        mEmailAdapter = new EmailAdapter(AddRecipientActivity.this, mEmailNbFlexibleItemCountRecyclerviewModel, mEmailViewDataModelList, AddRecipientActivity.this, null);
        mEmailAdapter.setEditable(true);
        mEmailAdapter.setIActivityAdapterComListener(this);
        rvEmail.setAdapter(mEmailAdapter);
    }

    @Override
    public void dataValidationSuccess() {
        postAddRecipient();
    }

    @Override
    public void postAddRecipient() {
        binding.nestedScrollView.fullScroll(View.FOCUS_DOWN);
        mAddRecipientPresenter.postAddRecipient(etRecipientName.getValue(),mBankAccountViewDataModelList,
                mCreditCardViewDataModelList, mMobileNumberViewDataModelList,mElectricityMeterViewDataModelList,mEmailViewDataModelList,mShapIDViewDataModelList);
        binding.etFocus.requestFocus();
    }

    @Override
    public void onRecipientAdded() {
       sendBeneficiaryListRefreshIntent();
       mAddRecipientPresenter.trackSuccessAction();
    }

    void addRecipientNameListener(CompatEditText compatEdtRecipientName){
        RxTextView.editorActions(compatEdtRecipientName.getInputField())
                .filter(actionId -> actionId == EditorInfo.IME_ACTION_NEXT)
                .subscribe(chars -> {
                    ViewUtils.hideSoftKeyboard(AddRecipientActivity.this, compatEdtRecipientName);
                    compatEdtRecipientName.clearFocus();
                    if (!TextUtils.isEmpty(compatEdtRecipientName.getValue())) {
                        binding.tvNameInitial.setText(StringUtils.getNameInitials(etRecipientName.getValue()));
                    }
                },throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    @Override
    public void showAddRecipientApiError() {
        showLoadingOnButton(false);
        setEnabledActivityTouch(true);
        setLoadingButtonEnabled(true);
        NBSnackbar.instance().action(getString(R.string.snack_bar_okay), () -> {
        }).build(lnrTopView, getString(R.string.snackbar_recipient_not_saved));
    }

    @Override
    public void showAddRecipientApiError(String... message) {
        showAddRecipientApiError(null, message);
    }

    @SuppressLint("NotifyDataSetChanged")
    public void showAddRecipientApiError(Map<Integer, String> matchBackNumber, String... message) {
        showLoadingOnButton(false);
        setEnabledActivityTouch(true);
         if (matchBackNumber == null || matchBackNumber.isEmpty()) {
            setLoadingButtonEnabled(true);
            NBSnackbar.instance().action(getString(R.string.snackbar_action_retry), this::postAddRecipient).build(lnrTopView, message);
        } else {
            mBankAccountAdapter.setMatchBackNumberErrorMap(matchBackNumber);
            mCreditCardAdapter.setMatchBackNumberErrorMap(matchBackNumber);
            mElectricityMeterAdapter.setMatchBackNumberErrorMap(matchBackNumber);
            mBankAccountAdapter.notifyDataSetChanged();
            mCreditCardAdapter.notifyDataSetChanged();
            mElectricityMeterAdapter.notifyDataSetChanged();
            setLoadingButtonEnabled(false);
            NBSnackbar.instance().action(getString(R.string.snack_bar_okay), () -> {
            }).build(lnrTopView, getString(R.string.snackbar_recipient_save_error));
        }
    }

    @Override
    public void showApproveItApiError(String... message) {
        setLoadingButtonEnabled(true);
        showLoadingOnButton(false);
        setEnabledActivityTouch(true);
        NBSnackbar.instance().header(getString(R.string.action_denied))
                .duration(NBSnackbar.FOREVER)
                .action(getString(R.string.snackbar_action_ok), this::onBackPressed).build(lnrTopView, message);
    }

    @Override
    public int getMatchBackNumber() {
        return ++mMatchBackNumber;
    }

    @Override
    public void setMatchBackNumber(int matchBackNumber) {
        mMatchBackNumber = matchBackNumber;
    }
}
