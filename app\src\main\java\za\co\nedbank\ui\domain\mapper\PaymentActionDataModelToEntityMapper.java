package za.co.nedbank.ui.domain.mapper;

import javax.inject.Inject;

import za.co.nedbank.ui.data.entity.money_request.PaymentActionRequestEntity;
import za.co.nedbank.ui.domain.model.money_request.PaymentActionDataModel;

/**
 * Created by mayuri.birajdar on 31-05-2018.
 */

public class PaymentActionDataModelToEntityMapper {

    @Inject
    PaymentActionDataModelToEntityMapper() {
    }

    public PaymentActionRequestEntity mapPaymentActionViewToData(PaymentActionDataModel paymentActionDataModel) {
        PaymentActionRequestEntity paymentActionRequestEntity = new PaymentActionRequestEntity();
        if (paymentActionDataModel != null) {
            paymentActionRequestEntity.setPayerAccountNumber(paymentActionDataModel.getPayerAccountNumber());
            paymentActionRequestEntity.setPayerAccountType(paymentActionDataModel.getPayerAccountType());
            paymentActionRequestEntity.setPayerDescription(paymentActionDataModel.getPayerDescription());
            paymentActionRequestEntity.setPaymentBatchId(paymentActionDataModel.getPaymentBatchId());
            paymentActionRequestEntity.setPaymentRequestId(paymentActionDataModel.getPaymentRequestId());
            paymentActionRequestEntity.setPaymentRequestAction(paymentActionDataModel.getPaymentRequestAction());
            paymentActionRequestEntity.setProcessAmount(paymentActionDataModel.getProcessAmount());
        }
        return paymentActionRequestEntity;
    }
}
