package za.co.nedbank.ui.view.home.close_account_tab;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.CloseAccountFragmentBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.model.ClosedAccount;

public class CloseAccountFragment extends NBBaseFragment implements CloseAccountView, ClosedAccountAdapter.IViewHolderInteraction {

    @Inject
    CloseAccountPresenter mPresenter;
    private CloseAccountFragmentBinding binding;

    public static CloseAccountFragment newInstance() {
        return new CloseAccountFragment();
    }

    @Override
    public void onActivityCreated(@Nullable final Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mPresenter.bind(this);
    }

    @Override
    public void onResume() {
        super.onResume();
        mPresenter.onClosedAccountsFragmentVisible();
        mPresenter.getClosedInvestmentAccounts();
    }

    public void initAdapter(ArrayList<ClosedAccount> closedAccounts) {
        if (closedAccounts != null && !closedAccounts.isEmpty()) {
            ClosedAccountAdapter closedAccountAdapter = new ClosedAccountAdapter(this, closedAccounts);
            binding.accountRecycler.setAdapter(closedAccountAdapter);
        }

    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = CloseAccountFragmentBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mPresenter.unbind();
    }

    @Override
    public void showProgressBar(boolean enable) {
        if (enable) {
            binding.listLoadingView.showListLoading();
        } else {
            binding.listLoadingView.clearLoading();
        }
    }

    @Override
    public void showEmptyPlaceHolder(boolean enable) {
            // Not required to show empty placeholder
    }

    @Override
    public void showAccountsList(ArrayList<ClosedAccount> closedAccounts) {
        ViewUtils.hideViews(binding.errorScreen.errorScreenParent);
        initAdapter(closedAccounts);
    }

    @Override
    public void showErrorMessage(String message) {
        ViewUtils.showViews(binding.errorScreen.errorScreenParent);
    }

    @Override
    public void onAccountSelected(ClosedAccount accountSummary) {
        mPresenter.navigateToDownloadStatement(accountSummary);
    }
}