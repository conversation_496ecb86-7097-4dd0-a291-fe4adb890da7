/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.List;

import io.reactivex.annotations.NonNull;
import za.co.nedbank.R;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.sharedui.control.CustomLinearLayout;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewModel;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.ShapIdValidator;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.view.recipient.ShapIDViewDataModel;
import za.co.nedbank.uisdk.component.CompatEditText;
import za.co.nedbank.uisdk.component.CompatTextView;

/**
 * Created by amit1829 on 24/01/2023.
 */

public class RppPaymentAdapter extends NBFlexibleItemCountRecyclerviewAdapter {

    private BankAccountAdapter.IActivityAdapterComListener mIActivityAdapterComListener;
    public RppPaymentAdapter(@NonNull final Context context,
                             @NonNull final NBFlexibleItemCountRecyclerviewModel nbFlexibleItemCountRecyclerviewModel,
                             @NonNull final List<NbRecyclerViewBaseDataModel> nbRecyclerViewBaseDataModelList,
                             final IAdapterInteractionListener adapterInteractionListener,
                             ISectionedListItemSelectedListener itemSelectedListener) {
        super(context, nbFlexibleItemCountRecyclerviewModel, nbRecyclerViewBaseDataModelList, adapterInteractionListener, itemSelectedListener);
    }

    public void setIActivityAdapterComListener(BankAccountAdapter.IActivityAdapterComListener iActivityAdapterComListener) {
        mIActivityAdapterComListener = iActivityAdapterComListener;
    }

    @Override
    protected void addItem() {
        ShapIDViewDataModel rppPaymentsViewDataModel = new ShapIDViewDataModel();
        mNbRecyclerViewBaseDataModelList.add(mNbRecyclerViewBaseDataModelList.size(), rppPaymentsViewDataModel);
        notifyItemInserted(mNbRecyclerViewBaseDataModelList.size());
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(Context context, ViewGroup parent) {
        View v = LayoutInflater.from(context).inflate(mNBFlexibleItemCountRecyclerviewModel.getItemView(), parent, false);
        VHRppPaymentItem vHRppPaymentItem = new VHRppPaymentItem(v);

        if (vHRppPaymentItem.etShapID != null) {
            vHRppPaymentItem.etShapID.getInputField().setContentDescription(" ");
        }
        return vHRppPaymentItem;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (position > 0 && (position - 1) < mNbRecyclerViewBaseDataModelList.size() && mNbRecyclerViewBaseDataModelList.get(position - 1) instanceof ShapIDViewDataModel) {
            ShapIDViewDataModel rppPaymentsViewDataModel = (ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(position - 1);
            VHRppPaymentItem vhRPPItem = ((VHRppPaymentItem) holder);
            if (vhRPPItem.etShapID != null) {
                vhRPPItem.etShapID.setText(ShapIdValidator.getProxyNameView(rppPaymentsViewDataModel.getShapid(),rppPaymentsViewDataModel.getProxyDomain()), isEditable());
            }
            if (vhRPPItem.yourRef != null) {
                vhRPPItem.yourRef.setText(rppPaymentsViewDataModel.getYourReference(), isEditable());
            }
            if (vhRPPItem.recipientReference != null) {
                vhRPPItem.recipientReference.setText(rppPaymentsViewDataModel.getRecipientReference(), isEditable());
            }
            vhRPPItem.ivRecipientTypeIcon.setVisibility(isEditable() ? View.GONE:View.VISIBLE);
            handleEditableShapId(rppPaymentsViewDataModel, vhRPPItem);
            makeFieldsFocusable(vhRPPItem);
        } else {
            super.onBindViewHolder(holder, position);
        }
    }

    private void makeFieldsFocusable(VHRppPaymentItem vhRPPItem) {
        if (vhRPPItem.etShapID != null) {
            vhRPPItem.etShapID.setFocusable(isEditable());
            vhRPPItem.etShapID.setEnabled(isEditable());
        }
        if (vhRPPItem.yourRef != null) {
            vhRPPItem.yourRef.setFocusable(isEditable());
            vhRPPItem.yourRef.setEnabled(isEditable());
        }
        if(vhRPPItem.recipientReference!=null) {
            vhRPPItem.recipientReference.setFocusable(isEditable());
            vhRPPItem.recipientReference.setEnabled(isEditable());
        }
        vhRPPItem.llRootView.setViewGroupClickListener(mItemSelectedListener != null ? mViewInterceptListener : null);
        vhRPPItem.addListenerForRppCreditCardNumber(vhRPPItem.etShapID);
        vhRPPItem.addListenerForRppYourReference(vhRPPItem.yourRef);
        vhRPPItem.addListenerForRecipientReference(vhRPPItem.recipientReference);
        vhRPPItem.llRootView.setOnClickListener(v -> vhRPPItem.handleItemSelected());
    }

    private void handleEditableShapId(ShapIDViewDataModel rppPaymentsViewDataModel, VHRppPaymentItem vhRPPItem) {
        if (isEditable()) {
            setShapIdEditable(rppPaymentsViewDataModel, vhRPPItem);
        } else {
            setShapIdNonEditable(vhRPPItem);
        }
    }

    private void setShapIdNonEditable(VHRppPaymentItem vhRPPItem) {
        if (vhRPPItem.etShapID != null) {
            vhRPPItem.etShapID.getInputField().setTransformationMethod(null);
            vhRPPItem.etShapID.setBackgroundColor(ContextCompat.getColor(vhRPPItem.etShapID.getContext(), android.R.color.transparent));
        }
        if (vhRPPItem.yourRef != null) {
            vhRPPItem.yourRef.setBackgroundColor(ContextCompat.getColor(vhRPPItem.yourRef.getContext(), android.R.color.transparent));
        }
        if (vhRPPItem.recipientReference != null) {
            vhRPPItem.recipientReference.setBackgroundColor(ContextCompat.getColor(vhRPPItem.recipientReference.getContext(), android.R.color.transparent));
        }
    }

    private void setShapIdEditable(ShapIDViewDataModel rppPaymentsViewDataModel, VHRppPaymentItem vhRPPItem) {
        if (vhRPPItem.etShapID != null) {
            vhRPPItem.etShapID.getInputField().setTransformationMethod(new CompatTextView.NBBlockCopyMethod());
            if (!rppPaymentsViewDataModel.isExistingItem()) {
                vhRPPItem.etShapID.requestFocus();
                if (rppPaymentsViewDataModel.isSendAccessibilityEvent() && AccessibilityUtils.isAccessibilityServiceEnabled(mContext)) {
                    vhRPPItem.etShapID.postDelayed(() ->
                            vhRPPItem.etShapID.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED), 500);
                    rppPaymentsViewDataModel.setSendAccessibilityEvent(false);
                }
            }
        }
    }

    class VHRppPaymentItem extends RecyclerView.ViewHolder {

        ImageView ivRecipientTypeIcon;
        CustomLinearLayout llRootView;
        CompatEditText etShapID;
        CompatEditText yourRef;
        CompatEditText recipientReference;

        VHRppPaymentItem(View itemView) {
            super(itemView);
            this.ivRecipientTypeIcon = itemView.findViewById(R.id.iv_recipient_icon);
            this.llRootView = itemView.findViewById(R.id.ll_root_view);
            this.etShapID = itemView.findViewById(R.id.et_credit_card_number);
            this.yourRef = itemView.findViewById(R.id.et_your_reference);
            this.recipientReference = itemView.findViewById(R.id.et_recipient_reference);
        }
        public void handleItemSelected() {
            if (mItemSelectedListener != null) {
                mItemSelectedListener.itemSelected(VIEW_TYPE.SHAPID.ordinal(), getBindingAdapterPosition() - 1);
            }
        }
        void addListenerForRppCreditCardNumber(CompatEditText compatEdtRppCreditCardNumber) {

            RxTextView.textChanges(compatEdtRppCreditCardNumber.getInputField()).subscribe(chars -> {
                compatEdtRppCreditCardNumber.clearErrors();
                ((ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setShapid(compatEdtRppCreditCardNumber.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtRppCreditCardNumber.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtRppCreditCardNumber, Validator.ValidatorType.CARD_NUMBER_VALIDATOR);
                }
            });
        }

        void addListenerForRppYourReference(CompatEditText compatEdtRppYourReference) {

            RxTextView.textChanges(compatEdtRppYourReference.getInputField()).subscribe(chars -> {
                compatEdtRppYourReference.clearErrors();
                ((ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setYourReference(compatEdtRppYourReference.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtRppYourReference.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtRppYourReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });
        }

        private void registerRppListener(CompatEditText compatEdtRppRecipientReference) {
            RxTextView.textChanges(compatEdtRppRecipientReference.getInputField()).subscribe(chars -> {
                compatEdtRppRecipientReference.clearErrors();
                ((ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setRecipientReference(compatEdtRppRecipientReference.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtRppRecipientReference.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtRppRecipientReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });

            RxTextView.editorActions(compatEdtRppRecipientReference.getInputField())
                    .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                    .subscribe(chars -> {
                        ViewUtils.hideSoftKeyboard(mContext, compatEdtRppRecipientReference);
                        compatEdtRppRecipientReference.clearFocus();
                        mIActivityAdapterComListener.validateInput(compatEdtRppRecipientReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                    },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
        }
        void addListenerForRecipientReference(CompatEditText compatEdtRppRecipientReference) {
            registerRppListener(compatEdtRppRecipientReference);
        }
    }
}
