/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.List;

import io.reactivex.annotations.NonNull;
import za.co.nedbank.R;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewModel;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.view.recipient.EmailViewDataModel;
import za.co.nedbank.uisdk.component.CompatEditText;

/**
 * Created by priyadhingra on 9/5/2017.
 */

public class EmailAdapter extends NBFlexibleItemCountRecyclerviewAdapter {

    private BankAccountAdapter.IActivityAdapterComListener mIActivityAdapterComListener;

    public void setIActivityAdapterComListener(BankAccountAdapter.IActivityAdapterComListener iActivityAdapterComListener) {
        mIActivityAdapterComListener = iActivityAdapterComListener;
    }

    public EmailAdapter(@NonNull final Context context,
                        @NonNull final NBFlexibleItemCountRecyclerviewModel nbFlexibleItemCountRecyclerviewModel,
                        final List<NbRecyclerViewBaseDataModel> nbRecyclerViewBaseDataModelList,
                        final IAdapterInteractionListener adapterInteractionListener,
                        ISectionedListItemSelectedListener itemSelectedListener) {
        super(context, nbFlexibleItemCountRecyclerviewModel, nbRecyclerViewBaseDataModelList, adapterInteractionListener, itemSelectedListener);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(Context context, ViewGroup parent) {
        View v = LayoutInflater.from(context).inflate(mNBFlexibleItemCountRecyclerviewModel.getItemView(), parent, false);
        return new VHEmailItem(v);
    }

    @Override
    protected void addItem() {
        EmailViewDataModel emailViewDataModel = new EmailViewDataModel();
        mNbRecyclerViewBaseDataModelList.add(mNbRecyclerViewBaseDataModelList.size(), emailViewDataModel);
        notifyItemInserted(mNbRecyclerViewBaseDataModelList.size());
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (position > 0 && (position - 1) < mNbRecyclerViewBaseDataModelList.size() && mNbRecyclerViewBaseDataModelList.get(position - 1) instanceof EmailViewDataModel) {
            EmailViewDataModel emailViewDataModel = (EmailViewDataModel) mNbRecyclerViewBaseDataModelList.get(position - 1);
            VHEmailItem vhEmailItem = ((VHEmailItem) holder);
            vhEmailItem.position = position;
            if (vhEmailItem.etEmail != null) {
                vhEmailItem.etEmail.setText(emailViewDataModel.getEmail(), isEditable());
            }
            vhEmailItem.addListenerForEmailAddress(vhEmailItem.etEmail);

            if (!isEditable()) {
                if (vhEmailItem.etEmail != null) {
                    vhEmailItem.etEmail.setBackgroundColor(ContextCompat.getColor(vhEmailItem.etEmail.getContext(), android.R.color.transparent));
                }
            }

            if (isEditable() && mNBFlexibleItemCountRecyclerviewModel.isDeleteButtonShow()) {
                vhEmailItem.ivRemove.setVisibility(View.VISIBLE);
            } else {
                vhEmailItem.ivRemove.setVisibility(View.GONE);
            }

            if (vhEmailItem.etEmail != null) {
                vhEmailItem.etEmail.setFocusable(isEditable());
                vhEmailItem.etEmail.setEnabled(isEditable());
            }
            vhEmailItem.ivRemove.setOnClickListener(v -> vhEmailItem.onClickOfRemoveImageView());
        } else {
            super.onBindViewHolder(holder, position);
        }
    }

    class VHEmailItem extends RecyclerView.ViewHolder {

        CompatEditText etEmail;
        ImageView ivRemove;
        ViewGroup llRootView;
        int position;

        void onClickOfRemoveImageView() {
            int pos = getAdapterPosition() - 1;
            if (pos >= 0 && pos < mNbRecyclerViewBaseDataModelList.size()) {
                removeItem(getAdapterPosition(), mNbRecyclerViewBaseDataModelList.get(pos).isExistingItem());
            }
        }

        public VHEmailItem(View itemView) {
            super(itemView);
            this.etEmail = itemView.findViewById(R.id.et_email);
            this.ivRemove = itemView.findViewById(R.id.iv_remove);
            this.llRootView = itemView.findViewById(R.id.ll_root_view);
        }

        void addListenerForEmailAddress(CompatEditText compatEdtEmailAddress) {

            RxTextView.textChanges(compatEdtEmailAddress.getInputField()).subscribe(chars -> {
                compatEdtEmailAddress.clearErrors();
                ((EmailViewDataModel) mNbRecyclerViewBaseDataModelList.get(getAdapterPosition() - 1)).setEmail(compatEdtEmailAddress.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtEmailAddress.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtEmailAddress, Validator.ValidatorType.EMAIL_VALIDATOR);
                }
            });

            RxTextView.editorActions(compatEdtEmailAddress.getInputField())
                    .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                    .subscribe(chars -> {
                        ViewUtils.hideSoftKeyboard(mContext, compatEdtEmailAddress);
                        compatEdtEmailAddress.clearFocus();
                        if (mIActivityAdapterComListener != null) {
                            mIActivityAdapterComListener.validateInput(compatEdtEmailAddress, Validator.ValidatorType.EMAIL_VALIDATOR);
                        }
                    }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        }
    }
}
