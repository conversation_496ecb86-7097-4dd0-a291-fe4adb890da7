/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.money_request;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.ui.data.mapper.money_requests.MoneyRequestsResponseEntityToDataMapper;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsMainDataModel;
import za.co.nedbank.ui.domain.repository.IMoneyRequestsRepository;

/**
 * Created by swapnil.gawande on 2/23/2018.
 */

public class MoneyRequestsUseCase extends UseCase<String, MoneyRequestsMainDataModel> {
    private final IMoneyRequestsRepository moneyRequestsRepository;
    private final MoneyRequestsResponseEntityToDataMapper moneyRequestsResponseEntityToDataMapper;

    @Inject
    MoneyRequestsUseCase(final UseCaseComposer useCaseComposer, IMoneyRequestsRepository moneyRequestsRepository,
                         MoneyRequestsResponseEntityToDataMapper moneyRequestsResponseEntityToDataMapper) {
        super(useCaseComposer);
        this.moneyRequestsRepository = moneyRequestsRepository;
        this.moneyRequestsResponseEntityToDataMapper = moneyRequestsResponseEntityToDataMapper;
    }

    @Override
    protected Observable<MoneyRequestsMainDataModel> createUseCaseObservable(String requesterRole) {
        return moneyRequestsRepository.getMoneyRequestList(requesterRole)
                .map(moneyRequestsResponseEntityToDataMapper::mapMoneyRequestsMainResponseEntityToDataModel);
    }
}
