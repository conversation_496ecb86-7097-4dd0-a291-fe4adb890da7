/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.my_finance;


import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.VoidUseCase;
import za.co.nedbank.ui.domain.LinkAccountEntityToDataMapper;
import za.co.nedbank.ui.domain.model.LinkAccountData;
import za.co.nedbank.ui.domain.repository.ILinkAccountInfoRepository;

public class GetLinkAccountInfoUseCase extends VoidUseCase<List<LinkAccountData>> {

    private final ILinkAccountInfoRepository mLinkAccountInfoRepository;

    @Inject
    public GetLinkAccountInfoUseCase(final UseCaseComposer composer
            , final ILinkAccountInfoRepository iLinkAccountInfoRepository) {
        super(composer);
        this.mLinkAccountInfoRepository = iLinkAccountInfoRepository;
    }

    @Override
    protected Observable<List<LinkAccountData>> createUseCaseObservable() {
        return mLinkAccountInfoRepository.getAccountLinkingInfo().map(LinkAccountEntityToDataMapper::mapData);
    }

}
