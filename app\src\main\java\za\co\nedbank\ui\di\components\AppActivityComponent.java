/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.di.components;

import dagger.Subcomponent;
import za.co.nedbank.core.di.modules.ActivityModule;
import za.co.nedbank.core.di.scopes.ActivityScope;
import za.co.nedbank.ui.di.modules.AppActivityModule;
import za.co.nedbank.ui.notifications.NotificationNavigationActivity;
import za.co.nedbank.ui.splashscreen.SplashScreenActivity;
import za.co.nedbank.ui.view.card_delivery.branch_confirmation.CardDeliveryBranchConfirmationActivity;
import za.co.nedbank.ui.view.card_delivery.deliver_result.CardDeliveryResultActivity;
import za.co.nedbank.ui.view.card_delivery.deliver_to_me_confirmation.DeliverToMeConfirmationActivity;
import za.co.nedbank.ui.view.card_delivery.delivery_options.CardDeliveryOptionsActivity;
import za.co.nedbank.ui.view.card_delivery.locker_confirmation.CardDeliveryLockerConfirmationActivity;
import za.co.nedbank.ui.view.card_delivery.locker_map.LockerMapActivity;
import za.co.nedbank.ui.view.deeplink.view.AppDeeplinkActivity;
import za.co.nedbank.ui.view.developer_option.DeveloperOptionAlertActivity;
import za.co.nedbank.ui.view.enbichatbot.ChatbotActivity;
import za.co.nedbank.ui.view.enbichatbot.NonTpLiveAgentErrorDialog;
import za.co.nedbank.ui.view.error.GenericTechnicalErrorActivity;
import za.co.nedbank.ui.view.home.HomeActivity;
import za.co.nedbank.ui.view.home.add_recipient.AddRecipientActivity;
import za.co.nedbank.ui.view.home.add_recipient.BaseRecipientActivity;
import za.co.nedbank.ui.view.home.cms_content.CMSMediaContentActivity;
import za.co.nedbank.ui.view.home.coming_soon.ComingSoonWidget;
import za.co.nedbank.ui.view.home.edit_recipient.EditRecipientActivity;
import za.co.nedbank.ui.view.home.fc_deep_linking.DeepLinkIntermediateActivity;
import za.co.nedbank.ui.view.home.investments.AccountTypeInvestmentActivity;
import za.co.nedbank.ui.view.home.latest.latestWidget.LatestWidgetActivity;
import za.co.nedbank.ui.view.home.money_requests.send_money_request.MoneyRequestActivity;
import za.co.nedbank.ui.view.home.money_requests.send_money_request.MoneyRequestSuccessActivity;
import za.co.nedbank.ui.view.home.money_requests.send_money_request.RecipientContactsActivity;
import za.co.nedbank.ui.view.home.money_requests.send_money_request.ViewMoneyRequestDetailsActivity;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.MoneyPaymentActivity;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.MoneyPaymentReviewActivity;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.MoneyPaymentSuccessActivity;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.ViewMoneyRequestsActivity;
import za.co.nedbank.ui.view.home.my_recipients.MyRecipientsWidget;
import za.co.nedbank.ui.view.home.my_recipients.choose_from_account.ChooseFromAccountDialog;
import za.co.nedbank.ui.view.home.my_recipients.choose_recipient.ChooseRecipientDialog;
import za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorActivity;
import za.co.nedbank.ui.view.home.payme_tab.PayMeTabWidget;
import za.co.nedbank.ui.view.home.product_deep_linking.ProductDeepLinkActivity;
import za.co.nedbank.ui.view.home.quick_pay.QuickPayActivity;
import za.co.nedbank.ui.view.home.recipient_detail.RecipientDetailActivity;
import za.co.nedbank.ui.view.home.recipient_detail.RecipientDetailWithHistoryActivity;
import za.co.nedbank.ui.view.home.tax_certificates_accounts.TaxCertificatesAccountsActivity;
import za.co.nedbank.ui.view.home.verifyme.IDVLVerifyMeActivity;
import za.co.nedbank.ui.view.ita.ITAFlowActivity;
import za.co.nedbank.ui.view.ita.ita_authentication.ITAAuthenticationActivity;
import za.co.nedbank.ui.view.ita.qrcodelogin.QrCodeProcessingActivity;
import za.co.nedbank.ui.view.ita.qrcodelogin.QrLoginResultActivity;
import za.co.nedbank.ui.view.notification.NotificationCenterActivity;
import za.co.nedbank.ui.view.notification.ajo_notification_details.AJONotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.ajo_notification_details.navigation.NavigationHandlerActivity;
import za.co.nedbank.ui.view.notification.contextswitch.ContextSwitchConfirmationActivity;
import za.co.nedbank.ui.view.notification.notification_details.NotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.notification_details.redirect_url.RedirectUrlActivity;
import za.co.nedbank.ui.view.notification.notification_messages.NotificationMessagesActivity;
import za.co.nedbank.ui.view.notification.notification_permission.NotificationEnableActivity;
import za.co.nedbank.ui.view.notification.notification_preferences.NotificationPreferenceActivity;
import za.co.nedbank.ui.view.notification.notification_preferences.account_preference.AccountPreferenceActivity;
import za.co.nedbank.ui.view.notification.notification_preferences.all_accounts_preferences.AllAccountsPreferenceActivity;
import za.co.nedbank.ui.view.notification.notification_preferences.delivery_preferences.DeliveryPreferencesActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.details.TransactionNotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.inbox.TransactionInboxActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.report_fraud.ReportFraudTransactionActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.sort_notifications.SortNotificationsActivity;
import za.co.nedbank.ui.view.pay_me.PayMeActivity;
import za.co.nedbank.ui.view.pop.NewShareProofOfPaymentActivity;
import za.co.nedbank.ui.view.pop.SharePopMethodsActivity;
import za.co.nedbank.ui.view.pop.TransactionDetailsActivity;
import za.co.nedbank.ui.view.pop.failure_pop.FailurePopActivity;
import za.co.nedbank.ui.view.pop.success_pop.SuccessPopActivity;
import za.co.nedbank.ui.view.refica.FicaErrorActivity;
import za.co.nedbank.ui.view.refica.FicaSuccessActivity;
import za.co.nedbank.ui.view.refica.VerifyMeActivity;
import za.co.nedbank.ui.view.retention.MultipleAccountsShareActivity;
import za.co.nedbank.ui.view.retention.RetentionTaskSelectionActivity;
import za.co.nedbank.ui.view.retention.RetentionWelcomeActivity;
import za.co.nedbank.ui.view.retention.feedback.RetentionFeedbackActivity;
import za.co.nedbank.ui.view.retention.feedback.RetentionFeedbackSuccessActivity;
import za.co.nedbank.ui.view.retention.notification_journey.RetentionInformationActivity;
import za.co.nedbank.ui.view.retention.notification_journey.RetentionNotificationJourneyActivity;
import za.co.nedbank.ui.view.sales_landing.SalesLandingActivity;

@ActivityScope
@Subcomponent(modules = {AppActivityModule.class, ActivityModule.class})
public interface AppActivityComponent {
    void inject(SplashScreenActivity activity);

    void inject(DeveloperOptionAlertActivity activity);

    void inject(HomeActivity activity);
    void inject(VerifyMeActivity activity);
    void inject(FicaSuccessActivity activity);
    void inject(FicaErrorActivity activity);

    void inject(MyRecipientsWidget myRecipientsWidget);

    void inject(ComingSoonWidget comingSoonWidget);

    void inject(ChooseRecipientDialog chooseRecipientDialog);

    void inject(ChooseFromAccountDialog chooseFromAccountDialog);

    void inject(AddRecipientActivity activity);

    void inject(EditRecipientActivity activity);

    void inject(BaseRecipientActivity activity);

    void inject(RecipientDetailActivity activity);

    void inject(RecipientDetailWithHistoryActivity activity);

    void inject(MoneyPaymentReviewActivity activity);

    void inject(ViewMoneyRequestsActivity activity);

    void inject(RecipientContactsActivity activity);

    void inject(ViewMoneyRequestDetailsActivity activity);

    void inject(MoneyPaymentActivity activity);

    void inject(MoneyPaymentSuccessActivity activity);

    void inject(MoneyRequestSuccessActivity activity);

    void inject(MoneyRequestActivity activity);

    void inject(PayMeTabWidget activity);

    void inject(NotificationNavigationActivity activity);

    void inject(NotificationCenterActivity activity);

    void inject(NotificationDetailsActivity activity);

    void inject(AJONotificationDetailsActivity activity);

    void inject(NavigationHandlerActivity activity);

    void inject(NotificationMessagesActivity activity);

    void inject(NotificationEnableActivity activity);

    void inject(CMSMediaContentActivity activity);

    void inject(ReportFraudTransactionActivity activity);

    void inject(TransactionInboxActivity activity);

    void inject(TransactionNotificationDetailsActivity activity);

    void inject(NotificationPreferenceActivity activity);

    void inject(AllAccountsPreferenceActivity activity);

    void inject(AccountPreferenceActivity activity);

    void inject(DeliveryPreferencesActivity activity);

    void inject(TransactionDetailsActivity activity);

    void inject(SortNotificationsActivity activity);

    void inject(NewShareProofOfPaymentActivity activity);

    void inject(SharePopMethodsActivity activity);

    void inject(SuccessPopActivity activity);

    void inject(FailurePopActivity activity);

    void inject(QuickPayActivity activity);

    void inject(PayMeActivity activity);

    void inject(TaxCertificatesAccountsActivity activity);

    void inject(LatestWidgetActivity activity);

    void inject(DeepLinkIntermediateActivity activity);

    void inject(RetentionWelcomeActivity activity);

    void inject(RetentionTaskSelectionActivity activity);

    void inject(RetentionFeedbackActivity activity);

    void inject(MultipleAccountsShareActivity activity);

    void inject(RetentionFeedbackSuccessActivity activity);

    void inject(RetentionInformationActivity activity);

    void inject(RetentionNotificationJourneyActivity activity);

    void inject(RedirectUrlActivity activity);

    void inject(ContextSwitchConfirmationActivity activity);

    void inject(ITAFlowActivity activity);

    void inject(QrCodeProcessingActivity activity);

    void inject(QrLoginResultActivity activity);

    void inject(ITAAuthenticationActivity activity);

    void inject(AccountTypeInvestmentActivity activity);

    void inject(CardDeliveryBranchConfirmationActivity activity);

    void inject(DeliverToMeConfirmationActivity activity);

    void inject(CardDeliveryResultActivity activity);

    void inject(CardDeliveryOptionsActivity activity);

    void inject(LockerMapActivity activity);

    void inject(CardDeliveryLockerConfirmationActivity activity);

    void inject(ProductDeepLinkActivity activity);

    void inject(IDVLVerifyMeActivity activity);

    void inject(ChatbotActivity chatbotActivity);

    void inject(GenericTechnicalErrorActivity genericTechnicalErrorActivity);

    void inject(NonTpLiveAgentErrorDialog nonTpLiveAgentErrorDialog);

    void inject(OddRestrictionErrorActivity oddRestrictionErrorActivity);

    void inject(SalesLandingActivity salesLandingActivity);

    void inject(AppDeeplinkActivity activity);

}
