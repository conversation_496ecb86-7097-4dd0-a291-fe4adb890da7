package za.co.nedbank.ui.view.refica;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.usecase.GetClearCacheApiUseCase;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingParam;

public class <PERSON>caSuccessPresenter extends NBBasePresenter<FicaSuccessView> {

    private final GetUserDetailUseCase mGetUserDetailUseCase;
    private final GetClearCacheApiUseCase refreshUserUseCase;
    private final Analytics mAnalytics;

    @Inject
    public FicaSuccessPresenter(final GetUserDetailUseCase mGetUserDetailUseCase,
                                final GetClearCacheApiUseCase refreshUserUseCase,
                                final Analytics mAnalytics){
        this.mGetUserDetailUseCase = mGetUserDetailUseCase;
        this.refreshUserUseCase = refreshUserUseCase;
        this.mAnalytics = mAnalytics;
    }


    public void callRefreshApi() {
        refreshUserUseCase
                .execute(true)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showWidgetProgress(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showWidgetProgress(false);
                    }
                })
                .subscribe(response -> {
                    if (response.isOk()) {
                        mGetUserDetailUseCase.execute(true).compose(bindToLifecycle())
                                .doOnSubscribe(disposable -> {
                                    if (view != null) {
                                        view.showWidgetProgress(true);
                                    }
                                })
                                .doOnTerminate(() -> {
                                    if (view != null) {
                                        view.showWidgetProgress(false);
                                    }
                                })
                                .subscribe(userDetail -> {

                                });

                    }
                });
    }

    public void sendAnalytics() {
        mAnalytics.sendEventActionWithMap(TrackingParam.MDM_SUCCESS_REFICA_VERIFIED, null);
    }
}
