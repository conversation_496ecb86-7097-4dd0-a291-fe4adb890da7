package za.co.nedbank.ui.notifications;

import com.huawei.hms.push.HmsMessageService;
import com.huawei.hms.push.RemoteMessage;

import org.greenrobot.eventbus.EventBus;

import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.R;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.core.notification.NotificationEvent;
import za.co.nedbank.core.notification.mapper.NotificationDataToViewModelMapper;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;

public class NBHmsMessagingService extends HmsMessageService {

    private static final String TAG = "NBHmsMessagingService";
    private static final String UUID = "uuid";
    private static final String ENCRYPTED_PAYLOAD = "encrypted_payload";

    @Inject
    FeatureSetController mFeatureSetController;

    @Inject
    NotificationHelper mNotificationHelper;

    @Inject
    ApplicationStorage mApplicationStorage;

    @Inject
    @Named("memory")
    ApplicationStorage mMemoryApplicationStorage;

    @Inject
    NotificationDataToViewModelMapper mNotificationDataToViewModelMapper;

    @Override
    public void onCreate() {
        super.onCreate();
        AppDI.getServiceComponent(this).inject(this);
    }

    @Override
    public void onNewToken(String s) {
        super.onNewToken(s);
        NBLogger.e("on new token", s);
        mApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.HMS_TOKEN, s);

    }

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        super.onMessageReceived(remoteMessage);
        NBLogger.e("Notification", remoteMessage.toString());
        //check if the push notification feature in disabled in toggle settings
        NBLogger.d(TAG, "From: " + remoteMessage.getFrom());
        handleNotifications(remoteMessage);
    }

    private void handleNotifications(RemoteMessage remoteMessage) {
        Map<String, String> remoteMessageData = remoteMessage.getDataOfMap();
        if (remoteMessageData.size() > 0) {

            if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS)) {
                NotificationData notificationData = NotificationUtils.extractNotificationData(remoteMessageData.get(NotificationConstants.EXTRA.DATA_PAYLOAD));
                if (notificationData != null) {
                    String url = remoteMessageData.get(NotificationConstants.EXTRA.URL);
                    notificationData.setUrl(url);
                    handleNotification(notificationData);
                } else if (isPushAuth(remoteMessageData) && !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ITA)) {
                    NBLogger.d(TAG, "Message data payload from TRANSAKT: " + remoteMessageData);
                    handleTransaktNotifications(remoteMessageData);
                }
            }

        } else if (isPushAuth(remoteMessageData) && !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ITA)) {
            NBLogger.d(TAG, "Message data payload from TRANSAKT: " + remoteMessageData);
            handleTransaktNotifications(remoteMessageData);
        }

    }

    private void handleTransaktNotifications(Map<String, String> remoteMessageData) {
        NotificationData notificationData = getTransaktNotificaton(remoteMessageData);
        handleNotification(notificationData);
    }

    private NotificationData getTransaktNotificaton(Map<String, String> remoteMessage) {
        NotificationData data = new NotificationData();
        data.setNotificationId(0);
        data.setHeading(remoteMessage.get(getResources().getString(R.string.payload)));
        data.setDisplayCategory(NotificationConstants.NOTIFICATION_TYPES.ITA);
        data.setNotificationType(NotificationConstants.NOTIFICATION_TYPES.ITA);
        data.setDistributionType(NotificationConstants.DISTRIBUTION_TYPES.ALL);
        data.setAllowAnonymous(false);

        data.setAuth(true);
        return data;
    }

    private void handleNotification(NotificationData notificationData) {

        mNotificationHelper.generateNotification(this, notificationData);
        //if notification received with
        // 1. distribution type UBC i.e un-tracked broadcast
        // 2. notification type chat
        // then no need for further processing
        if (notificationData == null
                || NotificationConstants.DISTRIBUTION_TYPES.UBC.equals(notificationData.getDistributionType())
                || NotificationConstants.DISTRIBUTION_TYPES.TBC.equals(notificationData.getDistributionType())
                || NotificationConstants.NOTIFICATION_TYPES.CHAT.equalsIgnoreCase(notificationData.getNotificationType())
                || notificationData.isAuth() || isContextSwitchNeeded(notificationData.getClient().getCisNo())) {
            return;
        }
        int unreadCount;
        //increase unread count in memory storage
        if (notificationData.getNotificationType().equals(NotificationConstants.NOTIFICATION_TYPES.TRANSACTION)) {
            unreadCount = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, 0);
            mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, ++unreadCount);
        } else {
            unreadCount = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, 0);
            mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, ++unreadCount);
        }
        int totalUnreadNotificationCount = mApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, 0);
        mApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, ++totalUnreadNotificationCount);

        //broadcast event to be listened when app is in foreground to refresh UI
        FBNotificationsViewModel notificationViewModel = mNotificationDataToViewModelMapper.transform(notificationData);
        NotificationEvent notificationEvent = new NotificationEvent();
        notificationEvent.setFbNotificationsViewModel(notificationViewModel);
        notificationEvent.setUnreadCount(unreadCount);
        EventBus.getDefault().post(notificationEvent);
    }


    private boolean isContextSwitchNeeded(String cisNo) {
        return !cisNo.equals(mMemoryApplicationStorage.getString(StorageKeys.CIS_NUMBER, StringUtils.EMPTY_STRING));
    }

    /**
     * If the data contains only the UUID and no ECRYPTED_PAYLOAD
     * then it is just a normal push notification without the encrypted auth
     */
    private boolean isPushAuth(Map<String, String> data) {
        return data.containsKey(UUID) && data.containsKey
                (ENCRYPTED_PAYLOAD);
    }
}
