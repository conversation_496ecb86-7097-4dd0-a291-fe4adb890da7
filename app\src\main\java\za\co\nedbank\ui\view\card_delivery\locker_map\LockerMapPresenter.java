package za.co.nedbank.ui.view.card_delivery.locker_map;

import android.annotation.SuppressLint;

import java.util.HashMap;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.entity.bip_branch_detail.BIPNearByBranchesRequestEntity;
import za.co.nedbank.core.data.mapper.BIPNearByBranchesRequestModelToEntityMapper;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.Permission;
import za.co.nedbank.core.domain.model.location.ATMAndBranchesProfileFlowIdentifier;
import za.co.nedbank.core.domain.model.location.AddressSuggestion;
import za.co.nedbank.core.domain.model.location.BIPNearByBranchesRequestModel;
import za.co.nedbank.core.domain.model.location.Place;
import za.co.nedbank.core.domain.model.location.PlaceDetails;
import za.co.nedbank.core.domain.model.location.PlaceType;
import za.co.nedbank.core.domain.model.location.SearchCriteriaForNearbyPlaces;
import za.co.nedbank.core.domain.model.warning.DataUsageStatus;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.CheckPermissionUseCase;
import za.co.nedbank.core.domain.usecase.location.FindNearbyPlacesUseCase;
import za.co.nedbank.core.domain.usecase.location.GetAddressSuggestionsUseCase;
import za.co.nedbank.core.domain.usecase.location.GetPlaceProfileDetailsUseCase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.map.variants.XLatLng;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConstants;

import static za.co.nedbank.core.Constants.FROM_BLOCK_REPLACE_SCREEN_ACTIVITY;
import static za.co.nedbank.core.Constants.FROM_MOA_SCREEN_ACTIVITY;
import static za.co.nedbank.core.Constants.ZERO;
import static za.co.nedbank.core.tracking.TrackingEvent.CARDS_CATEGORY;
import static za.co.nedbank.core.tracking.TrackingEvent.CARD_MAINTENANCE;
import static za.co.nedbank.core.tracking.TrackingEvent.DELIVERY_TO_A_DSV_LOCKER;
import static za.co.nedbank.core.tracking.TrackingEvent.PICK_IT_UP_FROM_DSV;
import static za.co.nedbank.core.tracking.TrackingEvent.PRODUCT_ON_BOARDING;
import static za.co.nedbank.core.tracking.TrackingEvent.SALES_AND_ONBOARDING;
import static za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics.EVENT_CM_SEARCH_NEAREST_LOCKER;

public class LockerMapPresenter extends NBBasePresenter<LockerMapView> {

    private final GetAddressSuggestionsUseCase getAddressSuggestionsUseCase;
    private final GetPlaceProfileDetailsUseCase getPlaceProfileDetailsUseCase;
    private final CheckPermissionUseCase checkPermissionUseCase;
    private final ApplicationStorage applicationStorage;
    private final ApplicationStorage inMemoryApplicationStorage;
    private Disposable searchQueryCall;
    private DataUsageStatus dataUsageStatus = DataUsageStatus.UNKNOWN;
    private final NavigationRouter navigationRouter;
    private boolean isInitialSetUpDone;
    private boolean alreadyAskedForPermission;
    private final CompositeDisposable getNearbyCalls = new CompositeDisposable();
    private final FindNearbyPlacesUseCase findNearbyPlacesUseCase;
    private final BIPNearByBranchesRequestModelToEntityMapper bipBipNearByBranchesRequestModelToEntityMapper;
    private Place selectedPlace;
    private final Analytics analytics;
    private final ErrorHandler errorHandler;

    @Inject
    public LockerMapPresenter(final GetAddressSuggestionsUseCase getAddressSuggestionsUseCase,
                              final GetPlaceProfileDetailsUseCase getPlaceProfileDetailsUseCase,
                              final CheckPermissionUseCase checkPermissionUseCase,
                              final ApplicationStorage applicationStorage,
                              @Named("memory") final ApplicationStorage inMemoryApplicationStorage,
                              final NavigationRouter navigationRouter,
                              final FindNearbyPlacesUseCase findNearbyPlacesUseCase,
                              final BIPNearByBranchesRequestModelToEntityMapper bipBipNearByBranchesRequestModelToEntityMapper,
                              Analytics analytics,
                              ErrorHandler errorHandler) {
        this.getAddressSuggestionsUseCase = getAddressSuggestionsUseCase;
        this.getPlaceProfileDetailsUseCase = getPlaceProfileDetailsUseCase;
        this.checkPermissionUseCase = checkPermissionUseCase;
        this.applicationStorage = applicationStorage;
        this.inMemoryApplicationStorage = inMemoryApplicationStorage;
        this.navigationRouter = navigationRouter;
        this.findNearbyPlacesUseCase = findNearbyPlacesUseCase;
        this.bipBipNearByBranchesRequestModelToEntityMapper = bipBipNearByBranchesRequestModelToEntityMapper;
        this.analytics = analytics;
        this.errorHandler = errorHandler;
    }

    public void searchQueryChanged(String query) {
        if (searchQueryCall != null && !searchQueryCall.isDisposed()) {
            searchQueryCall.dispose();
        }
        searchQueryCall = getAddressSuggestionsUseCase
                .execute(query)
                .compose(bindToLifecycle())
                .subscribe(
                        suggestions -> {
                            if (suggestions.size() == ZERO) {
                                view.hideAddressSuggestions();
                                view.showSuggestionFailedError(false);
                            } else {
                                view.showAddressSuggestions(suggestions);
                            }
                        },
                        error -> {
                            view.hideAddressSuggestions();
                            view.showSuggestionFailedError(true);
                        });
    }

    private void loadNearByPlaces(final BIPNearByBranchesRequestEntity bipNearByBranchesRequestEntity, boolean isManualSearch) {

        getNearbyCalls.add(findNearbyPlacesUseCase
                .execute(new SearchCriteriaForNearbyPlaces(PlaceType.LOCKER, ATMAndBranchesProfileFlowIdentifier.LOCKERS_FLOW, bipNearByBranchesRequestEntity, false))
                .compose(bindToLifecycle())
                .doOnSubscribe(d -> view.showLoadingInProgess(true))
                .subscribe(result -> {
                            if (view != null) {
                                view.showResultPlacesOnMap(result);
                                view.showResultPlacesOnBottonSheet(result);
                            }
                        },
                        error -> {
                            sendApiErrorAnalytics(false, errorHandler.getErrorMessage(error).getMessage(), null);
                            if (view != null) {
                                view.showLoadingInProgess(false);
                                if (isManualSearch) {
                                    view.showManualSearchLockerAPIFailedError();
                                } else {
                                    view.showNearByLockerAPIFailedError();
                                }
                            }

                        },
                        () -> {
                            if (view != null)
                                view.showLoadingInProgess(false);
                        }
                ));
    }

    private void sendApiErrorAnalytics(boolean isApiFailure, String message, String apiErrorCode) {

        if (view.isEficaFlow()) {

            HashMap<String, Object> contextDataMap = new HashMap<>();
            AdobeContextData contextData = new AdobeContextData(contextDataMap);

            contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
            contextData.setProductAccount(getProductName());
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
            contextData.setFeatureCategoryCount();
            contextData.setFeatureCount();
            contextData.setSubFeature(view.getCardDeliverySubFeature());
            contextData.setSubFeatureCount();

            analytics.trackFailure(isApiFailure, CardDeliveryAnalytics.EVENT_CM_ORDER_CARD_FAILURE, CardDeliveryAnalytics.API_REF_RET, message, apiErrorCode, contextData.getCdata());
        }
    }

    private String getProductName() {
        return applicationStorage.getString(StorageKeys.CARD_DELIVERY_SELECTED_PRODUCT, StringUtils.EMPTY_STRING);
    }

    @SuppressLint("CheckResult")
    public void addressSuggestionClicked(final AddressSuggestion suggestion) {
        if (view == null)
            return;
        view.hideAddressSuggestions();
        view.finishSearchEditing(suggestion);
        getPlaceProfileDetailsUseCase
                .execute(suggestion.id, PlaceType.ADDRESS)
                .compose(bindToLifecycle())
                .subscribe(placeDetails -> {
                            view.resetUI();
                            view.navigateToPlace(placeDetails, true);
                            loadNearbyLockerLocations(new XLatLng(placeDetails.location.getLatitude(), placeDetails.location.getLongitude()), true);
                        },
                        error -> view.showPlaceLookupFailedError());
    }


    public void initialSetUp() {
        if (view == null)
            return;
        boolean dataUsageAccepted = checkDataUsageAccepted();
        if (!dataUsageAccepted) {
            return;
        }
        if (!isInitialSetUpDone) {
            if (view.isMapServiceAvailable()) {
                view.setMapNotAvailableInfoVisible(false);
                view.initMap();
            } else {
                view.setMapNotAvailableInfoVisible(true);
            }
        }
        isInitialSetUpDone = true;
    }

    public boolean checkDataUsageAccepted() {
        boolean dataWarningAcceptedPermanently = applicationStorage.getBoolean(StorageKeys.DATA_USAGE_NEVER_ASK_AGAIN, false);
        boolean isDemoMode = inMemoryApplicationStorage.getBoolean(StorageKeys.IS_DEMO, false);

        if (dataWarningAcceptedPermanently || isDemoMode) {
            return true;
        }

        switch (dataUsageStatus) {
            case UNKNOWN:
                openDataUsageWarning();
                return false;
            case ACCEPTED:
                return true;
            case DECLINED:
                view.close();
                return false;
        }
        return false;
    }

    private void openDataUsageWarning() {
        if (view == null) {
            return;
        }
        String screenName = FROM_BLOCK_REPLACE_SCREEN_ACTIVITY;
        if (view.isEficaFlow()) {
            screenName = FROM_MOA_SCREEN_ACTIVITY;
        }
        navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.DATA_USAGE_WARNING)
                .withAllData(true)
                .withParam(Constants.FROM_SCREEN, screenName))
                .subscribe(navigationResult -> {
                    boolean accepted = navigationResult.getBooleanParam(NavigationTarget.RESULT_DATA_USAGE_WARNING_ACCEPT);
                    dataUsageStatus = accepted ? DataUsageStatus.ACCEPTED : DataUsageStatus.DECLINED;
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void checkLocationEnabled() {
        if (alreadyAskedForPermission) {
            return;
        }
        checkPermissionUseCase
                .execute(Permission.COURSE_LOCATION, Permission.FINE_LOCATION)
                .compose(bindToLifecycle())
                .subscribe(isPermission -> view.enableGeolocation(isPermission)
                        , throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        alreadyAskedForPermission = true;
    }

    public void loadNearbyLockerLocations(XLatLng latLng, boolean isManualSearch) {
        getNearbyCalls.clear();
        view.setCurrentSearchLatLong(latLng);
        view.setIsManualSearch(isManualSearch);
        //load near by locker API call
        loadNearByPlaces(bipBipNearByBranchesRequestModelToEntityMapper.transform(createRequestModel(latLng)), isManualSearch);
    }


    private BIPNearByBranchesRequestModel createRequestModel(XLatLng latLng) {
        if (latLng == null)
            return null;
        BIPNearByBranchesRequestModel bipNearByBranchesRequestModel = new BIPNearByBranchesRequestModel();
        bipNearByBranchesRequestModel.setLatitude(Double.toString(latLng.latitude));
        bipNearByBranchesRequestModel.setLongitude(Double.toString(latLng.longitude));
        return bipNearByBranchesRequestModel;
    }

    public void mapClicked() {
        clearSelection();
        if (view != null) {
            view.hideAddressSuggestions();
        }
    }

    private void clearSelection() {
        if (selectedPlace != null) {
            selectedPlace = null;
            if (view != null) {
                view.clearPlaceSelection();
            }
        }
    }


    void placeClicked(final PlaceDetails placeDetails) {
        selectedPlace = placeDetails;
        if (view == null)
            return;
        view.navigateToPlace(placeDetails, false);
        view.showPlaceSelected(placeDetails);
        view.showPlaceDetails(placeDetails);
    }


    public void navigateToConfirmation(PlaceDetails placeDetails) {

        NavigationTarget target = NavigationTarget.to(NavigationTarget.CARD_DELIVERY_LOCKER_CONFIRMATION)
                .withParam(NavigationTarget.RESULT_BRANCH_ID, placeDetails.id)
                .withParam(NavigationTarget.RESULT_BRANCH_NAME, placeDetails.name)
                .withParam(NavigationTarget.RESULT_BRANCH_ADDRESS, placeDetails.address)
                .withAllData(true);
        navigationRouter.navigateTo(target);
    }

    public void navigateToLockerLocationError() {
        NavigationTarget target = NavigationTarget.to(NavigationTarget.CARD_DELIVERY_RESULT)
                .withAllData(true)
                .withParam(CardDeliveryConstants.EXTRA_IS_LOCKERS_NOT_LOADED_ERROR, true);
        navigationRouter.navigateTo(target);
    }

    public void sendPageAnalytics() {

        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);

        contextData.setPageCategory(DELIVERY_TO_A_DSV_LOCKER);

        contextData.setCategoryAndProduct(CARDS_CATEGORY, getProductName());

        contextData.setProductAccount(getProductName());

        contextData.setFeatureCategory(view.isEficaFlow() ? SALES_AND_ONBOARDING : CARD_MAINTENANCE);
        contextData.setFeature(view.isEficaFlow() ? PRODUCT_ON_BOARDING : view.getCardActionName());
        contextData.setSubFeature(PICK_IT_UP_FROM_DSV);

        analytics.sendEventStateWithMap(CardDeliveryAnalytics.VAL_LOCKER_MAP, contextData.getCdata());

    }

    public void sendEventAnalytics() {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductCategory(CardDeliveryAnalytics.VAL_CARDS);
        contextData.setProductAccount(getProductName());

        contextData.setFeatureCategory(view.isEficaFlow() ? SALES_AND_ONBOARDING : CARD_MAINTENANCE);
        contextData.setFeature(view.isEficaFlow() ? PRODUCT_ON_BOARDING : view.getCardActionName());
        contextData.setSubFeature(CardDeliveryAnalytics.PICK_IT_UP_FROM_DSV);

        analytics.sendEventActionWithMap(EVENT_CM_SEARCH_NEAREST_LOCKER, contextDataMap);
    }


}
