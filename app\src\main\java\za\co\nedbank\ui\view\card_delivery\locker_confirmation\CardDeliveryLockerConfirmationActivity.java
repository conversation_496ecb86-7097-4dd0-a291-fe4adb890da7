package za.co.nedbank.ui.view.card_delivery.locker_confirmation;

import static za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics.EVENT_CM_CLOSE;

import android.os.Bundle;
import android.text.method.LinkMovementMethod;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;

import androidx.annotation.NonNull;

import com.google.android.material.bottomsheet.BottomSheetBehavior;

import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.databinding.ActivityCardDeliveryLockerConfirmationBinding;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConstants;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryUtils;

public class CardDeliveryLockerConfirmationActivity extends NBBaseActivity implements CardDeliveryLockerConfirmationView {

    @Inject
    CardDeliveryLockerConfirmationPresenter presenter;
    private BottomSheetBehavior<View> bottomSheetBehavior;
    private ActivityCardDeliveryLockerConfirmationBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCardDeliveryLockerConfirmationBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        presenter.bind(this);
        bottomSheetBehavior = BottomSheetBehavior.from(binding.layoutBottomSheetCardDeliveryRetry.cardDeliveryRetryBottomsheet);
        setupAccessibility();
        presenter.sendPageAnalytics();
        presenter.handleLockerDetails();
        binding.layoutCardDeliveryLockerConfirmation.btnConfirm.setOnClickListener(v -> presenter.confirmClick());
        binding.layoutCardDeliveryLockerConfirmation.btnChangeLocker.setOnClickListener(v -> presenter.chooseDifferentLocker());
        binding.layoutBottomSheetCardDeliveryRetry.btnRetry.setOnClickListener(v -> presenter.retry());
        binding.layoutBottomSheetCardDeliveryRetry.btnRetryClose.setOnClickListener(v -> onClickRetryCloseButton());
    }

    private void setupAccessibility() {
        updateRetryCountAccessibility();
        bottomSheetBehavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull @NotNull View bottomSheet, int newState) {
                if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                    binding.layoutCardDeliveryLockerConfirmation.mainContent.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
                    binding.layoutBottomSheetCardDeliveryRetry.tvRetryHeading.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED);
                }
            }

            @Override
            public void onSlide(@NonNull @NotNull View bottomSheet, float slideOffset) {
                //Do Nothing
            }
        });
    }

    @Override
    public String getFlow() {
        return getIntent().getStringExtra(NavigationTarget.PARAM_CARD_DELIVERY_FLOW);
    }

    @Override
    public String getCardDeliverySubFeature() {
        return getIntent().getStringExtra(CardDeliveryConstants.EXTRA_SELECT_CARD_DELIVERY_OPTION_NAME);
    }

    @Override
    public String getLockerBranchCode() {
        return getIntent().getStringExtra(NavigationTarget.RESULT_BRANCH_ID);
    }

    @Override
    public String getCardPlasticId() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_PLASTIC_ID);
    }

    @Override
    public String getLockerName() {
        return getIntent().getStringExtra(NavigationTarget.RESULT_BRANCH_NAME);
    }

    public void disableRetry() {
        binding.layoutBottomSheetCardDeliveryRetry.btnRetry.setEnabled(false);
        binding.layoutCardDeliveryLockerConfirmation.btnConfirm.setEnabled(false);
        binding.layoutBottomSheetCardDeliveryRetry.btnRetry.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
        binding.layoutBottomSheetCardDeliveryRetry.btnRetryClose.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED);
    }

    @Override
    public void updateRetryCountAccessibility() {
        binding.layoutBottomSheetCardDeliveryRetry.btnRetry.setContentDescriptionToButton(String.format(getString(R.string.retry_button_content_description), presenter.getRemainingRetryAttempt()));
    }

    @Override
    public String getCardActionName() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_ACTION_NAME);
    }

    void onClickRetryCloseButton() {
        presenter.sendEventAnalytics(EVENT_CM_CLOSE);
        presenter.retryClose();
    }

    @Override
    public void showConfirmButtonLoading(boolean isLoading) {
        setEnabledActivityTouch(!isLoading);
        binding.layoutCardDeliveryLockerConfirmation.btnConfirm.setLoadingVisible(isLoading);
        binding.layoutBottomSheetCardDeliveryRetry.btnRetry.setLoadingVisible(isLoading);
        /*disable state only works after stetting loading state*/
        if (presenter.getRemainingRetryAttempt() <= 0) {
            disableRetry();
        }
    }

    @Override
    public void updateLockerDetailUI() {
        String lockerName = getIntent().getStringExtra(NavigationTarget.RESULT_BRANCH_NAME);
        String lockerAddress = getIntent().getStringExtra(NavigationTarget.RESULT_BRANCH_ADDRESS);
        binding.layoutCardDeliveryLockerConfirmation.tvLockerName.setText(lockerName);
        binding.layoutCardDeliveryLockerConfirmation.tvLockerLocation.setText(lockerAddress);
    }

    @Override
    public void showRetryScreen() {
        if (bottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED) {
            /* already retry screen is visible*/
            return;
        }
        String phNumber = getString(R.string.user_call_back_contact_number_rrb);
        binding.layoutBottomSheetCardDeliveryRetry.tvRetryHeading.setText(R.string.locker_service_not_available_heading);
        binding.layoutBottomSheetCardDeliveryRetry.tvRetryMessage.setText(CardDeliveryUtils.createPhNumberSpannableRrbTelNo(this, getString(R.string.locker_retry_message)));
        binding.layoutBottomSheetCardDeliveryRetry.tvRetryMessage.setMovementMethod(LinkMovementMethod.getInstance());
        String placeHolder = getString(R.string.cutomer_care_number_place_holder);
        binding.layoutBottomSheetCardDeliveryRetry.tvRetryMessage.setContentDescription(getString(R.string.locker_retry_message).replace(placeHolder, phNumber));
        bottomSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
    }

}