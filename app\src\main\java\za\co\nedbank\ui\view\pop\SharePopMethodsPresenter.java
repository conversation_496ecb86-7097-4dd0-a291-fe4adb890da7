package za.co.nedbank.ui.view.pop;

import static za.co.nedbank.core.Constants.BUNDLE_KEYS.PRICING_GUIDE;

import android.util.Log;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;

public class SharePopMethodsPresenter extends NBBasePresenter<SharePopMethodsView> {

    private final SharePOPMethodListUseCase mSharePOPMethodListUseCase;
    private NavigationRouter navigationRouter;
    private boolean isFromTransactionDetailActivity;

    @Inject
    public SharePopMethodsPresenter(SharePOPMethodListUseCase sharePOPMethodListUseCase, final NavigationRouter navigationRouter) {
        this.mSharePOPMethodListUseCase = sharePOPMethodListUseCase;
        this.navigationRouter = navigationRouter;
    }

    void getSharePOPMethod() {
        if (view == null) {
            return;
        }
        mSharePOPMethodListUseCase.execute(view.isFromPaymentsFlow())
                .compose(bindToLifecycle())
                .subscribe(sharePOPMethodList -> {
                    if (view != null) {
                        view.receiveSharePOPMethodList(sharePOPMethodList);
                    }
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    public void setFromTransactionDetailActivity(boolean fromTransactionDetailActivity) {
        isFromTransactionDetailActivity = fromTransactionDetailActivity;
    }

    void handleSharePOPRowClick(String sharePOPMethod) {
        if (view != null) {
            if (isFromTransactionDetailActivity) {
                NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.NEW_SHARE_PROOF_OF_PAYMENT);
                navigationTarget.withParam(Constants.BUNDLE_KEYS.TRANSACTION_HISTORY_VIEW_MODEL, view.getTransactionHistoryViewModel());
                navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_RECENT_PAYMENT_FLOW, view.isFromRecentPaymentFlow());
                navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_SOP_FROM_PAY_DONE, view.isSopFromPayDoneFlow());
                navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_BEYOND_90_DAYS_TRANSACTION, view.isBeyond90Days());
                navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_LANDING_ON_OVERVIEW, view.isNavigateToOverView());
                navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.SELECTED_SHARE_POP_METHOD, sharePOPMethod);
                navigationRouter.navigateTo(navigationTarget);
            } else {
                view.setActivityResult(sharePOPMethod);
            }
            view.finishScreen();
        }
    }

    void handleOpenPricingGuideline() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_REDIRECT_URL)
                .withParam(NotificationConstants.EXTRA.URL, NotificationConstants.URL.PRICING_GUIDE_URL)
                .withParam(NotificationConstants.EXTRA.SCREEN_TITLE, PRICING_GUIDE));
    }
}
