package za.co.nedbank.ui.view.sales_landing;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.concierge.chat.view.NBChatBaseActivity;
import za.co.nedbank.core.dashboard.HomeWidget;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.ActivitySalesLandingBinding;
import za.co.nedbank.enroll_v2.BuildConfig;
import za.co.nedbank.enroll_v2.view.applications.ApplicationFragment;
import za.co.nedbank.profile.view.more.MoreSettingsFragment;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.sales_latest.SalesLatestFragment;

public class SalesLandingActivity extends NBChatBaseActivity implements SalesLandingView {


    @Inject
    SalesLandingPresenter mPresenter;

    @Inject
    FeatureSetController setController;

    @Inject
    ApplicationStorage mApplicationStorage;

    private static final int LATEST_FRAGMENT = 0;
    private static final int APPLICATION_FRAGMENT = 1;
    private static final int MORE_FRAGMENT = 2;
    private ActivitySalesLandingBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySalesLandingBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        mPresenter.bind(this);
        initToolbar(binding.toolbar, false, false, true, ChatIconColor.GREEN);
        if (isSAUser()) {
            if (getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.FLOW_FOR_APPLICATION, false)) {
                setUpView(APPLICATION_FRAGMENT);
            } else {
                setUpView(LATEST_FRAGMENT);
            }
        } else {
            mPresenter.showNonSaClientUI();
        }
        binding.btnLetsGo.setOnClickListener(v -> letsGo());
        binding.homeMenuItemLatest.setOnClickListener(v -> onLatestOptionClick());
        binding.homeMenuItemSalesApplication.setOnClickListener(v -> onApplicationOptionClick());
        binding.homeMenuItemMore.setOnClickListener(v -> onMoreOptionClick());
    }


    private void setUpView(int tabOption) {
        binding.homeMenuItemLatest.setSelected(false);
        binding.homeMenuItemSalesApplication.setSelected(false);
        binding.homeMenuItemMore.setSelected(false);
        FragmentManager mFragmentManager = getSupportFragmentManager();
        Fragment fragment = mFragmentManager.findFragmentById(R.id.sales_dashboard_content_frame);
        if (tabOption == MORE_FRAGMENT) {
            binding.toolbar.setVisibility(View.GONE);
            binding.homeMenuItemMore.setSelected(true);
            loadMoreFragment(fragment);
        } else if (tabOption == APPLICATION_FRAGMENT) {
            binding.toolbar.setVisibility(View.VISIBLE);
            binding.homeMenuItemSalesApplication.setSelected(true);
            loadApplicationFragment(fragment);
        } else {
            binding.toolbar.setVisibility(View.VISIBLE);
            binding.homeMenuItemLatest.setSelected(true);
            loadLatestFragment(fragment);
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        String featureName = mApplicationStorage.getString(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE, null);
        if (featureName != null) {
            mPresenter.checkAndShowFeatureDeepLinkScreen(featureName);
        }
    }

    @Override
    protected void handleChatMenuClicked(String conversationId) {
        super.handleChatMenuClicked("Sales Passport Landing");
    }

    private boolean isSAUser() {
        if (getIntent() != null) {
            return getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.RSA_CUSTOMER, false);
        }
        return false;
    }


    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        return super.onPrepareOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.menu_item_more) {
            mPresenter.navigateToMore();
            return true;

        }
        return super.onOptionsItemSelected(item);

    }


    @Override
    public void showNonSAUserUI() {
        ViewUtils.hideViews(binding.rlDashboard);
        ViewUtils.showViews(binding.llNonSaClient, binding.btnLetsGo);
    }


    public void letsGo() {
        Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(BuildConfig.CR1_URL));
        startActivity(browserIntent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.unbind();
    }

    protected void onLatestOptionClick() {
        setUpView(LATEST_FRAGMENT);
    }

    protected void onApplicationOptionClick() {
        setUpView(APPLICATION_FRAGMENT);
    }

    protected void onMoreOptionClick() {
        setUpView(MORE_FRAGMENT);
    }


    private void loadMoreFragment(Fragment fragment) {
        MoreSettingsFragment moreSettingsFragment = null;
        if (fragment instanceof MoreSettingsFragment settingsFragment) {
            moreSettingsFragment = settingsFragment;
        }
        if (moreSettingsFragment == null) {
            moreSettingsFragment = MoreSettingsFragment.getInstance(false);
        }
        getSupportFragmentManager().beginTransaction().replace(R.id.sales_dashboard_content_frame, moreSettingsFragment).commit();
    }


    private void loadApplicationFragment(Fragment fragment) {
        ApplicationFragment applicationFragment = null;
        if (fragment instanceof ApplicationFragment appFragment) {
            applicationFragment = appFragment;
        }
        if (applicationFragment == null) {
            applicationFragment = new ApplicationFragment();
        }
        getSupportFragmentManager().beginTransaction().replace(R.id.sales_dashboard_content_frame, applicationFragment).commit();
    }


    private void loadLatestFragment(Fragment fragment) {
        SalesLatestFragment salesLatestFragment = null;
        if (fragment instanceof SalesLatestFragment saleFragment) {
            salesLatestFragment = saleFragment;
        }
        if (salesLatestFragment == null) {
            salesLatestFragment = SalesLatestFragment.getInstance();
        }
        Bundle bundle = new Bundle();
        bundle.putInt(Constants.BUNDLE_KEYS.FROM_ACTIVITY, HomeWidget.ACTION_LATEST);
        salesLatestFragment.setArguments(bundle);
        getSupportFragmentManager().beginTransaction().replace(R.id.sales_dashboard_content_frame, salesLatestFragment).commit();
    }

}
