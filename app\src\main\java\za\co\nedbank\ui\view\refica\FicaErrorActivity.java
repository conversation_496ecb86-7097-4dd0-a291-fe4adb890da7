package za.co.nedbank.ui.view.refica;

import static za.co.nedbank.core.navigation.NavigationTarget.IS_FROM_HOME;
import static za.co.nedbank.core.navigation.NavigationTarget.IS_RETAIL;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.core.content.ContextCompat;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityFicaErrorBinding;
import za.co.nedbank.ui.di.AppDI;

public class FicaErrorActivity extends NBBaseActivity implements FicaErrorView{

    public static final String ACTION = "action";

    @Inject
    FicaErrorPresenter ficaErrorPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getActivityComponent(this).inject(this);
        ActivityFicaErrorBinding binding = ActivityFicaErrorBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        ficaErrorPresenter.bind(this);

        if(getIntent().hasExtra(IS_FROM_HOME) && getIntent().getBooleanExtra(IS_FROM_HOME, false)){
            binding.title.setText(R.string.fica_error_title1);
            binding.tvheader2.setText(R.string.fica_error_header3);
            prepareDesc(binding);
        }
        binding.branchLocatorButton.setOnClickListener(v -> onSecurityNextButtonClicked());
        binding.icIcon.setOnClickListener(v -> onCrossButtonClicked());
    }

    public void onSecurityNextButtonClicked() {
        ficaErrorPresenter.sendAnalytics();
        Intent intent = getIntent();
        intent.putExtra(ACTION, "1");
        goback(intent);
    }

    public void onCrossButtonClicked() {
        ficaErrorPresenter.sendAnalytics();
        goback(getIntent());
    }

    private void goback(Intent intent) {
        setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    public void onBackPressed() {
        ficaErrorPresenter.sendAnalytics();
        goback(getIntent());
    }

    private void prepareDesc(ActivityFicaErrorBinding binding) {
        String tncBaseText = "";
        String remaining = "";

            tncBaseText = getString(R.string.fica_error_header1);
            if(getIntent().hasExtra(IS_RETAIL) && getIntent().getBooleanExtra(IS_RETAIL, false)){
                remaining = getString(R.string.to_verify_yourself);
            } else {
                remaining = getString(R.string.to_verify_yourself_fica);
            }
        String phoneNumber = " 0800 555 111 ";

        SpannableString spannableText = new SpannableString(tncBaseText);
        SpannableString spanLink = new SpannableString(phoneNumber);

        final int colorLink = ContextCompat.getColor(this, R.color.colorPrimary);

        ClickableSpan span = new ClickableSpan() {
            @Override
            public void onClick(final View view) {
                Intent actionIntent = new Intent(Intent.ACTION_DIAL);
                actionIntent.setData(Uri.parse("tel:0800 555 111"));
                startActivity(actionIntent);
            }

            @Override
            public void updateDrawState(TextPaint textPaint) {
                textPaint.setColor(colorLink);
            }
        };
        spanLink.setSpan(span, 0, spanLink.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        binding.tvheader1.setText(TextUtils.concat(spannableText, spanLink, remaining));
        binding.tvheader1.setMovementMethod(LinkMovementMethod.getInstance());
    }

}