/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.di.components;

import dagger.Subcomponent;
import za.co.nedbank.core.di.modules.FragmentModule;
import za.co.nedbank.core.di.scopes.ActivityScope;
import za.co.nedbank.ui.di.modules.AppFragmentModule;
import za.co.nedbank.ui.view.home.active_account_tab.ActiveAccountsFragment;
import za.co.nedbank.ui.view.home.apply.ApplyFragment;
import za.co.nedbank.ui.view.home.close_account_tab.CloseAccountFragment;
import za.co.nedbank.ui.view.home.latest.HomeLatestFragment;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.ReceivedMoneyRequestsFragment;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.SentMoneyRequestsFragment;
import za.co.nedbank.ui.view.home.non_tp_client.non_tp_apply.NonTpApplyFragment;
import za.co.nedbank.ui.view.home.non_tp_client.non_tp_dashboard.NonTPAccountsFragment;
import za.co.nedbank.ui.view.home.overview.OverviewFragment;
import za.co.nedbank.ui.view.home.payme_tab.MoneyRequestsFragment;
import za.co.nedbank.ui.view.home.payme_tab.MyRecipientsTabFragment;
import za.co.nedbank.ui.view.ita.ita_authentication.ITAAuthFragment;
import za.co.nedbank.ui.view.pop.RecipientDetailsFragment;
import za.co.nedbank.ui.view.pop.TransactionHistoryFragment;
import za.co.nedbank.ui.view.sales_latest.SalesLatestFragment;

/**
 * Created by charurani on 03-07-2017.
 */

@ActivityScope
@Subcomponent(modules = { AppFragmentModule.class, FragmentModule.class})
public interface AppFragmentComponent {
    void inject(OverviewFragment fragment);
//    void inject(FinancesFragment fragment);
//    void inject(MyFinancesFragment fragment);
//    void inject(UnlinkedStateFragment fragment);
    void inject(ReceivedMoneyRequestsFragment fragment);
    void inject(SentMoneyRequestsFragment fragment);
    void inject(MoneyRequestsFragment fragment);
    void inject(MyRecipientsTabFragment fragment);
    void inject(ApplyFragment fragment);
    void inject(NonTPAccountsFragment fragment);
    void inject(NonTpApplyFragment fragment);
    void inject(HomeLatestFragment fragment);

    void inject(RecipientDetailsFragment recipientDetailsFragment);

    void inject(TransactionHistoryFragment transactionHistoryFragment);
    void inject(CloseAccountFragment closeAccountFragment);
    void inject(ActiveAccountsFragment activeAccountsFragment);
    void inject(ITAAuthFragment fragment);
    void inject(SalesLatestFragment fragment);

}
