package za.co.nedbank.ui.view.pop;

import za.co.nedbank.core.base.adapter.Section;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;

import static za.co.nedbank.core.utils.FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS;

class TransactionHistorySection extends Section<TransactionHistoryViewModel> {
    private final long startDate;
    private final long endDate;

    public TransactionHistorySection(final String name, final long startDate, final long endDate) {
        super(name);
        this.startDate = startDate;
        this.endDate = endDate;
    }

    @Override
    public boolean fitSectionCriteria(final TransactionHistoryViewModel model) {
        return FormattingUtil.getFormattedDate(model.getStartDate(), DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS) >= startDate && FormattingUtil.getFormattedDate(model.getStartDate(), DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS) <= endDate;
    }
}
