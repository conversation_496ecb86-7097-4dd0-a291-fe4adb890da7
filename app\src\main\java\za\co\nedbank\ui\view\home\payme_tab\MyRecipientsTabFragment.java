/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.payme_tab;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import com.google.android.material.snackbar.BaseTransientBottomBar;
import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.base.dialog.IDialog;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.domain.model.beneficiary.user.UserBeneficiaryData;
import za.co.nedbank.core.domain.model.profile.ProfileLimit;
import za.co.nedbank.core.domain.model.profile.ProfileLimitType;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.payment.PaymentsUtility;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.sharedui.listener.IAlphabetsBarItemSelectionListener;
import za.co.nedbank.core.sharedui.listener.IViewPagerChildClickListener;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.IFragmentToActivityComListener;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryDataViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryDetailsViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryRecentPayDetailViewModel;
import za.co.nedbank.databinding.CardWidgetQuickPayBinding;
import za.co.nedbank.payment.common.domain.data.mapper.BeneficiaryTypeMapper;
import za.co.nedbank.payment.common.domain.data.mapper.BranchCodeToSortCodeMapper;
import za.co.nedbank.payment.common.domain.data.mapper.beneficiary.user.UserBeneficiaryMapper;
import za.co.nedbank.payment.common.view.PaymentPayzarFlowInfoDialog;
import za.co.nedbank.payment.common.view.tracking.PaymentsTrackingEvent;
import za.co.nedbank.payment.pay.view.PayMode;
import za.co.nedbank.payment.pay.view.model.PaymentViewModel;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.my_recipients.IQuickPayView;
import za.co.nedbank.ui.view.home.my_recipients.IRecipientsViewPagerListener;
import za.co.nedbank.ui.view.home.my_recipients.MyRecipientsPagerAdapter;
import za.co.nedbank.ui.view.home.my_recipients.choose_from_account.ChooseFromAccountDialog;
import za.co.nedbank.ui.view.home.my_recipients.choose_from_account.IChooseFromAccountDialogActivityInterface;
import za.co.nedbank.ui.view.home.my_recipients.choose_recipient.ChooseRecipientDialog;
import za.co.nedbank.ui.view.home.my_recipients.choose_recipient.ChooseRecipientsViewModel;
import za.co.nedbank.ui.view.home.my_recipients.choose_recipient.IChooseRecipientDialogActivityInterface;
import za.co.nedbank.uisdk.component.CompatCurrency;
import za.co.nedbank.uisdk.widget.NBSnackbar;


public class MyRecipientsTabFragment extends NBBaseFragment implements IQuickPayView, IAlphabetsBarItemSelectionListener, IRecipientsViewPagerListener, IChooseFromAccountDialogActivityInterface, IViewPagerChildClickListener, IChooseRecipientDialogActivityInterface {

    @Inject
    MyRecipientsPresenter mPresenter;
    @Inject
    UserBeneficiaryMapper mUserBeneficiaryMapper;

    List<ProfileLimit> mLimits;
    Double mPaymentDailyLimit ;
    Double mImaliDailyLimit ;

    @Inject
    FeatureSetController featureSetController;

    private final String TAG = MyRecipientsTabFragment.class.getSimpleName();

    private MyRecipientsPagerAdapter mPagerAdapter;
    private List<UserBeneficiaryDataViewModel> mUserBeneficiaryDataViewModelList;
    private ChooseRecipientsViewModel mChooseRecipientsViewModel;
    private List<AccountViewModel> mPayAccounts;
    private double mLimitToBeCompared;
    private IFragmentToActivityComListener mIFragmentToActivityComListener;
    private String mDefaultAccountIdentifier;
    private int fromAccountSelectedPos = 0;
    private final PaymentsViewModel paymentsViewModel = new PaymentsViewModel();
    private final PaymentViewModel paymentViewModel = new PaymentViewModel();
    private final AccountViewModel accountViewModel = new AccountViewModel();
    private boolean isBeneficiaryUpdated;
    private CardWidgetQuickPayBinding binding;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = CardWidgetQuickPayBinding.inflate(inflater, container, false);
        binding.myRecipientAddARecipientButton.setOnClickListener(v -> handleAddNewRecipientButtonClick());
        binding.ivAddRecipient.setOnClickListener(v -> handleAddRecipient());
        binding.myRecipientCardPayButton.setOnClickListener(v -> onPayClick());
        binding.tvUnableToLoad.setOnClickListener(v -> handleUnableToLoadTextViewClick());
        binding.myRecipientTypesLl.setOnClickListener(v -> onRecipientTypesImageViewClicked());
        binding.llFromAccountType.setOnClickListener(v -> onFromAccountTypeImageViewClicked());
        binding.myRecipientsErrorStateLl.setOnClickListener(v -> onFailedLoadRecipientLayoutClick());
        return binding.getRoot();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initView();
        mPresenter.bind(this);
        updateUserBeneficiaryData();
        mPresenter.getLimits();
    }

    void handleAddNewRecipientButtonClick() {
        if(mPresenter.isKidsProfile()) {
            mPresenter.navigateToMinorRestrictionErrorScreen();
        }else{
            mPresenter.handleAddRecipient();
        }
    }
    @Override
    public void showLimitError(){
        ViewUtils.showViews(binding.limitErrorTv);
        binding.myRecipientCardAmountEt.clearErrors();
        setPayButtonEnabled(false);
        binding.myRecipientCardAmountEt.updateEditextBackground(true,true);
        binding.limitErrorTv.setText(TextUtils.concat(getString(za.co.nedbank.payment.R.string.daily_limit_exceeded), StringUtils.SPACE, getClickableUpdateLimitSpan(getString(za.co.nedbank.payment.R.string.daily_limit_update))));
        binding.limitErrorTv.setMovementMethod(android.text.method.LinkMovementMethod.getInstance());
    }

    @Override
    public void clearLimitError(){
        ViewUtils.hideViews(binding.limitErrorTv);
    }


    private SpannableString getClickableUpdateLimitSpan(String clickablePart) {
        SpannableString span = new SpannableString(clickablePart);
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(View textView) {
                mPresenter.navigateToUpdateProfileLimits(isMobileNumberFlow());
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(Color.RED);
                ds.setUnderlineText(true);
            }
        };
        span.setSpan(clickableSpan, 0, clickablePart.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        return span;

    }

    void handleAddRecipient() {
        if(mPresenter.isKidsProfile()) {
            mPresenter.navigateToMinorRestrictionErrorScreen();
        }else{
            mPresenter.handleAddRecipient();
        }
    }

    @Override
    public boolean isFeatureDisabled(String feature) {
        return featureSetController.isFeatureDisabled(feature);
    }

    private void initView() {
        mPagerAdapter = new MyRecipientsPagerAdapter((getContext()), ((NBBaseActivity) getContext()).getSupportFragmentManager(), this, this);

        int pagePadding = getResources().getDimensionPixelSize(R.dimen.my_recipients_view_padding);
        binding.myRecipientsViewPager.setClipToPadding(false);
        binding.myRecipientsViewPager.setPadding(pagePadding, 0, pagePadding, 0);

        int pageMargin = getResources().getDimensionPixelSize(R.dimen.my_recipients_view_margin);
        binding.myRecipientsViewPager.setPageMargin(-pageMargin);

        binding.myRecipientsViewPager.addOnPageChangeListener(mPagerAdapter);
        binding.myRecipientsViewPager.setOffscreenPageLimit(5);
        binding.myRecipientsViewPager.setAdapter(mPagerAdapter);
        binding.sideBar.setSelectedTextColor(R.color.apple_green);
        binding.sideBar.setListener(this);

        //hide views until data for recipients is received
        ViewUtils.hideViews(binding.sideBar, binding.recipientNameTv, binding.recipientBankOrMobileTv, binding.myRecipientTypesIv);

        binding.myRecipientCardPayButton.setText(getContext().getString(R.string.my_recipients_pay_button_text));
        //disable the pay button until the amount is entered
        setPayButtonEnabled(false);

        if (getContext() instanceof IFragmentToActivityComListener) {
            mIFragmentToActivityComListener = (IFragmentToActivityComListener) getContext();
        }
        fetchAccountDetails();
        addListenerForAmount(binding.myRecipientCardAmountEt);
    }

    private void fetchAccountDetails() {
        binding.tvUnableToLoad.setText(getContext().getString(R.string.loading_ellipse));
        mPresenter.fetchFromAccountDetails();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mPresenter.unbind();
    }

    @Override
    public void updateUserBeneficiaryData() {
        ViewUtils.showViews(binding.progressMyRecipients);
        ViewUtils.setInvisibleAction(binding.myRecipientsViewPager);
        mPresenter.getUserBeneficiaryData(true);
    }

    @Override
    public void selectedCharacter(int charSelected) {
        //set the page selection based on the character selected on the bar
        binding.myRecipientsViewPager.setCurrentItem(mPagerAdapter.getCurrentItem(charSelected));
    }

    @Override
    public void selectedPage(int pagePosition) {
        //set the character selection based on the page selected on viewpager
        binding.sideBar.setAlphabetSelected(mPagerAdapter.getSectionForPosition(pagePosition));
        if (pagePosition == 0 && mPagerAdapter.getSectionedListIndexSet() != null && mPagerAdapter.getSectionedListIndexSet().size() > 0) {
            Iterator<Integer> characterIterator = mPagerAdapter.getSectionedListIndexSet().iterator();
            if (characterIterator.hasNext()) {
                selectedCharacter(characterIterator.next());
            }
        }
        //update the details on the currentRecipient Card
        UserBeneficiaryDetailsViewModel userBeneficiaryDetailsVM = mUserBeneficiaryDataViewModelList.get(pagePosition).getBfDetails().get(0);
        String accountNumberOrMobileNumber = StringUtils.EMPTY_STRING;

        if (BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF.equalsIgnoreCase(userBeneficiaryDetailsVM.getBeneficiaryType())) {
            accountNumberOrMobileNumber = getContext().getString(R.string.bdf_type_beneficiary_second_line);
        } else {
            accountNumberOrMobileNumber = userBeneficiaryDetailsVM.getAccountNumber();
            String bankName = mUserBeneficiaryDataViewModelList.get(pagePosition).getBfDetails().get(0).getBankName();
            updateContentDescriptionAccessibility(mUserBeneficiaryDataViewModelList.get(pagePosition).getContactCardName(), accountNumberOrMobileNumber, bankName);
            if (!TextUtils.isEmpty(bankName)) {
                accountNumberOrMobileNumber = (String.format("%s%s%s%s", accountNumberOrMobileNumber, StringUtils.COMMA, StringUtils.SPACE, bankName));
            }
        }
        List<UserBeneficiaryRecentPayDetailViewModel> recentTransactionList = mUserBeneficiaryDataViewModelList.get(pagePosition).getBfRecentPayDetail();
        UserBeneficiaryRecentPayDetailViewModel recentPayDetailViewModel = null;
        if (CollectionUtils.isNotEmpty(recentTransactionList) && recentTransactionList.get(0) != null) {
            recentPayDetailViewModel = recentTransactionList.get(0);
        }
        updateLastPaidView(recentPayDetailViewModel);
        updateCurrentRecipientDetails(mUserBeneficiaryDataViewModelList.get(pagePosition).getContactCardName(), accountNumberOrMobileNumber);

        if (mUserBeneficiaryDataViewModelList.get(pagePosition).getBfDetails().size() > 1) {
            ViewUtils.showViews(binding.myRecipientTypesIv);
        } else {
            ViewUtils.hideViews(binding.myRecipientTypesIv);
        }
        if (pagePosition > 0) {
            mPresenter.handleRecipientSelected();
        }

        if (!TextUtils.isEmpty(binding.myRecipientCardAmountEt.getValue())) {
            mPresenter.checkInputsOnScreen(binding.myRecipientCardAmountEt, binding.tvFromAccountNumber.getText().toString(), isMobileNumberFlow());
        }
    }

    @Override
    public void onScrolledPage() {
        this.mChooseRecipientsViewModel = null;
        isBeneficiaryUpdated = false;
    }

    @Override
    public void showChooseRecipientDialog() {
        UserBeneficiaryDataViewModel userBeneficiaryDataViewModel = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem());
        if (userBeneficiaryDataViewModel.getBfDetails().size() > 1) {
            ChooseRecipientsViewModel chooseRecipientsViewModel = new ChooseRecipientsViewModel();
            chooseRecipientsViewModel.setCurrentBeneficiaryName(userBeneficiaryDataViewModel.getContactCardName());
            if (mChooseRecipientsViewModel != null && userBeneficiaryDataViewModel.getContactCardName().equalsIgnoreCase(mChooseRecipientsViewModel.getCurrentBeneficiaryName())) {
                chooseRecipientsViewModel.setCurrentBeneficiarySelection(mChooseRecipientsViewModel.getCurrentBeneficiarySelection());
            } else {
                chooseRecipientsViewModel.setCurrentBeneficiarySelection(0);
            }
            chooseRecipientsViewModel.setBfDetails(userBeneficiaryDataViewModel.getBfDetails());
            DialogFragment dialogFragment = ((DialogFragment) ChooseRecipientDialog.getInstance(this.getContext(), chooseRecipientsViewModel));
            ((ChooseRecipientDialog) dialogFragment).setDialogActivityInterface(this);
            dialogFragment.show(((NBBaseActivity) getContext()).getSupportFragmentManager(), ChooseRecipientDialog.TAG);
        }
    }

    @Override
    public void showFromAccountTypeDialog() {
        if (mPayAccounts != null && !mPayAccounts.isEmpty()) {
            DialogFragment dialogFragment = ((DialogFragment) ChooseFromAccountDialog.getInstance(this.getContext(), (ArrayList<AccountViewModel>) mPayAccounts, fromAccountSelectedPos));
            ((ChooseFromAccountDialog) dialogFragment).setDialogActivityInterface(this);
            dialogFragment.show(((NBBaseActivity) getContext()).getSupportFragmentManager(), ChooseFromAccountDialog.TAG);
        }
    }

    void handleUnableToLoadTextViewClick() {
        fetchAccountDetails();
    }

    @Override
    public void showError(String error, @IMyRecipientsViewAPIErrorType int apiErrorType) {
        switch (apiErrorType) {
            case IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API:
                ((NBBaseActivity) getContext()).showError(getContext().getString(za.co.nedbank.core.R.string.snackbar_header_default), error,
                        getContext().getString(R.string.snackbar_action_retry), BaseTransientBottomBar.LENGTH_INDEFINITE, this::updateUserBeneficiaryData);
                break;
            case IMyRecipientsViewAPIErrorType.ERROR_IN_LIMITS_API:
                ((NBBaseActivity) getContext()).showError(getContext().getString(za.co.nedbank.core.R.string.snackbar_header_default), error,
                        getContext().getString(R.string.snackbar_dismiss), BaseTransientBottomBar.LENGTH_INDEFINITE, () -> mPresenter.handleDismissSnackBarActionClick(apiErrorType),
                        () -> mPresenter.handleDismissSnackBarActionClick(apiErrorType));
                break;
            case IMyRecipientsViewAPIErrorType.ERROR_IN_ACCOUNTS_API:
            case IMyRecipientsViewAPIErrorType.ERROR_IN_DEFAULT_ACCOUNTS_API:
                ViewUtils.setInvisibleAction(binding.tvFromAccountNumber);
                binding.tvUnableToLoad.setText(getContext().getString(R.string.unable_to_load));
                ViewUtils.showViews(binding.tvUnableToLoad);
                NBSnackbar.instance().action(getContext().getString(R.string.snackbar_action_retry), this::fetchAccountDetails).build(binding.coordinatorMain, error);
                break;
            case  IMyRecipientsViewAPIErrorType.ERROR_IN_INTERNATIONAL_FLOW:
                ((NBBaseActivity) requireContext()).showError(getString(R.string.error), getString(R.string.something_went_wrong),
                        getString(R.string.snackbar_action_ok), BaseTransientBottomBar.LENGTH_INDEFINITE, () -> NBSnackbar.instance().dismissSnackBar());
                break;
        }
    }

    @Override
    public void receiveUserBeneficiaryBeans(List<UserBeneficiaryData> userBeneficiaryDataList) {
        ViewUtils.showViews(binding.myRecipientsViewPager);
        if (userBeneficiaryDataList != null && !userBeneficiaryDataList.isEmpty()) {
            setVisibilityOnEmptyStateView(false);
            mUserBeneficiaryDataViewModelList = new ArrayList<>();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                mUserBeneficiaryDataViewModelList.addAll(userBeneficiaryDataList.stream().
                        map(userBeneficiaryData -> mUserBeneficiaryMapper.mapUserBeneficiaryDataToViewModel(userBeneficiaryData)).collect(Collectors.toList()));
            } else {
                for (UserBeneficiaryData userBeneficiaryData : userBeneficiaryDataList) {
                    mUserBeneficiaryDataViewModelList.add(mUserBeneficiaryMapper.mapUserBeneficiaryDataToViewModel(userBeneficiaryData));
                }
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                mUserBeneficiaryDataViewModelList.sort((current, next) -> {
                    String currentName = current.getContactCardName();
                    String nextName = next.getContactCardName();
                    return currentName.compareToIgnoreCase(nextName);
                });
            } else {
                Collections.sort(mUserBeneficiaryDataViewModelList, (current, next) -> {
                    String currentName = current.getContactCardName();
                    String nextName = next.getContactCardName();
                    return currentName.compareToIgnoreCase(nextName);
                });
            }

            mPagerAdapter.setList(mUserBeneficiaryDataViewModelList);
            mPagerAdapter.notifyDataSetChanged();

            ViewUtils.showViews(binding.sideBar);
            binding.sideBar.setEnabledCharacters(mPagerAdapter.buildEnabledItemsList());

            ViewUtils.showViews(binding.recipientNameTv, binding.recipientBankOrMobileTv, binding.myRecipientTypesIv);

            //notify the alphabet bar to highlight the first item according to the list
            selectedPage(0);

            //set pay button enabled
            if (!TextUtils.isEmpty(binding.myRecipientCardAmountEt.getValue())) {
                mPresenter.checkInputsOnScreen(binding.myRecipientCardAmountEt, binding.tvFromAccountNumber.getText().toString(), isMobileNumberFlow());
            }
        } else {
            setVisibilityOnEmptyStateView(true);
        }
    }

    @Override
    public void receiveAccounts(List<AccountViewModel> payAccounts) {
        this.mPayAccounts = payAccounts;
    }

    @Override
    public PaymentsViewModel buildPaymentsViewModel(AccountViewModel fromAccountViewModel) {
        if (fromAccountViewModel == null) {
            return null;
        }

        UserBeneficiaryDetailsViewModel userBeneficiaryDetailsViewModel;
        if (mChooseRecipientsViewModel == null) {
            userBeneficiaryDetailsViewModel = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getBfDetails().get(0);
        } else {
            userBeneficiaryDetailsViewModel = this.mChooseRecipientsViewModel.getBfDetails().get(this.mChooseRecipientsViewModel.getCurrentBeneficiarySelection());
        }
        if (userBeneficiaryDetailsViewModel != null) {

            if (!isBeneficiaryUpdated) {
                setBeneficiaryDetails(userBeneficiaryDetailsViewModel);
            }

            try {
                paymentViewModel.setAmount(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.myRecipientCardAmountEt.getValue())));
            } catch (NumberFormatException exception) {
                paymentViewModel.setAmount(0);
            }

            //set from account based on the calculations
            paymentViewModel.setFromAccountViewModel(fromAccountViewModel);

            if (!mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getNotificationDetails().isEmpty()) {
                paymentViewModel.setBeneficiaryNotificationList(mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getNotificationDetails());
            }

            //set validations
            if (!TextUtils.isEmpty(binding.myRecipientCardAmountEt.getValue())) {
                setValidations(fromAccountViewModel);
            }
        }

        if(paymentsViewModel.getPaymentViewModelList() != null && !paymentsViewModel.getPaymentViewModelList().isEmpty()) {
            paymentsViewModel.getPaymentViewModelList().clear();
        }

        if(paymentsViewModel.getPaymentViewModelList() != null) {
            paymentsViewModel.getPaymentViewModelList().add(paymentViewModel);
        }
        setUserAccountType();
        return paymentsViewModel;
    }

    private void setUserAccountType() {
        if (paymentViewModel.getPayMode() == PayMode.MOBILE) {
            paymentsViewModel.setUserAccountType(PaymentsTrackingEvent.VAL_IMALI);
        } else if (paymentViewModel.getPayMode() == PayMode.ACCOUNT) {
            paymentsViewModel.setUserAccountType(PaymentsTrackingEvent.VAL_BANK_ACCOUNT);
        } else if (paymentViewModel.getPayMode() == PayMode.CREDIT_CARD) {
            paymentsViewModel.setUserAccountType(PaymentsTrackingEvent.VAL_CREDIT_CARD);
        }
    }

    private void setValidations(AccountViewModel fromAccountViewModel) {
        if (!mPresenter.isAmountWithinLimits(binding.myRecipientCardAmountEt, mLimitToBeCompared)) {
            paymentsViewModel.setQuickPayValidationError(true);
            paymentsViewModel.setQuickPayValidationErrorTypes(Constants.QUICK_PAY_VALIDATION_ERROR.LIMIT_VALIDATION);
        } else if (!mPresenter.isAmountLessThanAccountBalance(binding.myRecipientCardAmountEt, fromAccountViewModel.getAvailableBalance(), fromAccountViewModel.isViewAvailBal())) {
            paymentsViewModel.setQuickPayValidationError(true);
            paymentsViewModel.setQuickPayValidationErrorTypes(Constants.QUICK_PAY_VALIDATION_ERROR.BALANCE_VALIDATION);
        } else if (!mPresenter.isAmountValid(binding.myRecipientCardAmountEt, isMobileNumberFlow())) {
            paymentsViewModel.setQuickPayValidationError(true);
            paymentsViewModel.setQuickPayValidationErrorTypes(Constants.QUICK_PAY_VALIDATION_ERROR.INVALID_AMOUNT_VALIDATION);
        }
    }

    private void setBeneficiaryDetails(UserBeneficiaryDetailsViewModel userBeneficiaryDetailsViewModel) {
        paymentViewModel.setBeneficiaryID(userBeneficiaryDetailsViewModel.getBeneficiaryID());
        paymentViewModel.setSortCode(new BranchCodeToSortCodeMapper()
                .map(userBeneficiaryDetailsViewModel, userBeneficiaryDetailsViewModel.getBranchCode()));
        paymentViewModel.setBeneficiaryName(userBeneficiaryDetailsViewModel.getBeneficiaryName());
        paymentViewModel.setPayMode(getPayModeByUserBeneficiary(userBeneficiaryDetailsViewModel));
        paymentViewModel.setInstantPayAllowed(userBeneficiaryDetailsViewModel.isInstantPayment());
        paymentViewModel.setRPPPayAllowed(userBeneficiaryDetailsViewModel.isRPPPayment());
        paymentViewModel.setBeneficiaryType(new BeneficiaryTypeMapper()
                .map(userBeneficiaryDetailsViewModel, userBeneficiaryDetailsViewModel.getBeneficiaryType()));
        paymentViewModel.setPickedRecipient(true);
        String userReference = userBeneficiaryDetailsViewModel.getMyReference() != null && !TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getMyReference())
                ? userBeneficiaryDetailsViewModel.getMyReference()
                : getContext().getString(R.string.pay_toolbar_title) + (TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getBeneficiaryName()) ? StringUtils.EMPTY_STRING : StringUtils.SPACE + userBeneficiaryDetailsViewModel.getBeneficiaryName());
        paymentViewModel.setUserReference(userReference);
        String beneficiaryReference = userBeneficiaryDetailsViewModel.getBeneficiaryReference();
        beneficiaryReference = beneficiaryReference != null && !TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getBeneficiaryReference())
                ? beneficiaryReference : userBeneficiaryDetailsViewModel.getBeneficiaryName();
        paymentViewModel.setBeneficiaryReference(beneficiaryReference);
        paymentViewModel.setOldBeneficiaryReference(beneficiaryReference);
        paymentViewModel.setInstantPayment(false);
        paymentViewModel.setStartDate(Calendar.getInstance().getTimeInMillis());
        paymentViewModel.setBankName(userBeneficiaryDetailsViewModel.getBankName());
        accountViewModel.setAccountNumber(userBeneficiaryDetailsViewModel.getAccountNumber());
        accountViewModel.setAccountType(userBeneficiaryDetailsViewModel.getAccountType());
        paymentViewModel.setToAccountViewModel(accountViewModel);
    }

    @Override
    public AccountViewModel provideDefaultFromAccount() {
        final AccountViewModel[] accountViewModels = new AccountViewModel[1];
        if (!TextUtils.isEmpty(mDefaultAccountIdentifier)) {
            Observable.just(mPayAccounts)
                    .flatMapIterable(accountViewModels1 -> accountViewModels1)
                    .filter(accountViewModel -> accountViewModel != null
                            && accountViewModel.getItemAccountId() != null
                            && mDefaultAccountIdentifier != null
                            && accountViewModel.getItemAccountId().equalsIgnoreCase(mDefaultAccountIdentifier)
                            && PaymentsUtility.isValidFromAccount(accountViewModel)
                            && accountViewModel.getAccountRuleViewModel() != null
                            && accountViewModel.getAccountRuleViewModel().isOnceOffPayFrom())
                    .toList()
                    .subscribe(payAccounts -> {
                        if (payAccounts != null && payAccounts.size() > 0) {
                            accountViewModels[0] = payAccounts.get(0);
                        }
                    }, throwable -> {
                        //need not do anything, respective error handled by observables
                    });
        }
        if (accountViewModels[0] == null) {
            Observable.just(mPayAccounts)
                    .compose(bindToLifecycle())
                    .flatMapIterable(mPayAccounts -> mPayAccounts)
                    .filter(accountViewModel -> PaymentsUtility.isValidFromAccount(accountViewModel)
                            && accountViewModel.getAccountRuleViewModel() != null && accountViewModel.getAccountRuleViewModel().isOnceOffPayFrom())
                    .toList()
                    .subscribe(payAccounts -> {
                        if (CollectionUtils.isNotEmpty(payAccounts)) {
                            accountViewModels[0] = Collections.max(payAccounts);
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
        return accountViewModels[0];
    }

    @Override
    public void handleEnterAccountNumber() {
        if (mUserBeneficiaryDataViewModelList != null && mUserBeneficiaryDataViewModelList.size() > 0) {
            mPresenter.checkInputsOnScreen(binding.myRecipientCardAmountEt, binding.tvFromAccountNumber.getText().toString(), isMobileNumberFlow());
        }
    }

    @Override
    public void setPayButtonEnabled(boolean isEnabled) {
        binding.myRecipientCardPayButton.setEnabled(isEnabled);
    }

    @Override
    public void setActivityTouchEnabled(boolean isEnabled) {
        if (isEnabled) {
            ((NBBaseActivity) getContext()).getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        } else {
            ((NBBaseActivity) getContext()).getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        }
    }

    @Override
    public void showLoadingOnButton(boolean inProgress) {
        binding.myRecipientCardPayButton.setLoadingVisible(inProgress);
    }

    @Override
    public void showProgress(boolean inProgress) {
        if (inProgress) {
            ViewUtils.showViews(binding.progressMyRecipients);
        } else {
            ViewUtils.hideViews(binding.progressMyRecipients);
        }
    }

    @Override
    public void hideKeyBoard() {
        ViewUtils.hideSoftKeyboard((getContext()), binding.myRecipientCardAmountEt);
    }

    @Override
    public void setLimitToBeCompared(double limit) {
        this.mLimitToBeCompared = limit;
    }

    @Override
    public void setVisibilityOnEmptyStateView(boolean isVisible) {
        if (isVisible) {
            ViewUtils.showViews(binding.myRecipientsEmptyStateLl);
            ViewUtils.hideViews(binding.myRecipientDetailsLl);
            ViewUtils.hideViews(binding.relFromAcc);
        } else {
            ViewUtils.hideViews(binding.myRecipientsEmptyStateLl);
            ViewUtils.showViews(binding.myRecipientDetailsLl);
            ViewUtils.showViews(binding.relFromAcc);
        }
        ViewUtils.hideViews(binding.progressMyRecipients);
    }

    @Override
    public void showAmountValidationError() {
        if (isMobileNumberFlow()) {
            setPayButtonEnabled(false);
            binding.myRecipientCardAmountEt.showError(getString(R.string.amount_must_be_in_multiple_of_R10));

        }

    }

    @Override
    public String provideNoAccountString() {
        return getContext().getString(R.string.empty_pay_accounts_list);
    }

    @Override
    public void dismissSnackBar() {
        NBSnackbar.instance().dismissSnackBar();
    }


    @Override
    public void setVisibilityOnRecipientLoadLayout(boolean isVisible) {
        if (isVisible) {
            ViewUtils.showViews(binding.myRecipientsErrorStateLl);
            ViewUtils.setInvisibleAction(binding.myRecipientDetailsInnerLl);
        } else {
            ViewUtils.hideViews(binding.myRecipientsErrorStateLl);
            ViewUtils.showViews(binding.myRecipientDetailsInnerLl);
        }
        ViewUtils.hideViews(binding.progressMyRecipients);
    }

    @Override
    public void receiveDefaultAccountIdentifierValue(String value) {
        mDefaultAccountIdentifier = value;
        AccountViewModel accountViewModel = provideDefaultFromAccount();
        if (accountViewModel != null) {
            fromAccountSelectedPos = accountViewModel.getPosition();
            binding.tvFromAccountNumber.setText(accountViewModel.getDisplayAccountName());
            // accessibility
            binding.tvFromAccountNumber.setContentDescription(String.format(getString(R.string.quick_pay_selected_account_acc), accountViewModel.getDisplayAccountName()));
            ViewUtils.setInvisibleAction(binding.tvUnableToLoad);
        }
    }

    @Override
    public void updateEditData(PaymentsViewModel paymentModel) {
        isBeneficiaryUpdated = true;

        paymentsViewModel.setQuickPayValidationError(false);

        PaymentViewModel receivedPaymentViewModel = paymentModel.getPaymentViewModelList().get(0);

        binding.myRecipientCardAmountEt.setText(FormattingUtil.convertToSouthAfricaFormattedCurrency(receivedPaymentViewModel.getAmount()));
        binding.tvFromAccountNumber.setText(receivedPaymentViewModel.getFromAccountViewModel().getDisplayAccountName());

        if (mPayAccounts != null) {
            for (int i = 0; i < mPayAccounts.size(); i++) {
                if (receivedPaymentViewModel.getFromAccountViewModel().getAccountNumber().trim().equals(mPayAccounts.get(i).getAccountNumber().trim())) {
                    fromAccountSelectedPos = i;
                }
            }
        }
        try {
            paymentViewModel.setAmount(receivedPaymentViewModel.getAmount());
        } catch (NumberFormatException exception) {
            paymentViewModel.setAmount(0);
        } finally {
            paymentViewModel.setBeneficiaryReference(receivedPaymentViewModel.getBeneficiaryReference());
            paymentViewModel.setUserReference(receivedPaymentViewModel.getUserReference());
        }
    }

    @Override
    public AccountViewModel getSelectedFromAccountViewModel() {
        if (mPayAccounts != null && fromAccountSelectedPos < mPayAccounts.size()) {
            return mPayAccounts.get(fromAccountSelectedPos);
        }
        return null;
    }

    public void onRecipientTypesImageViewClicked() {
        mPresenter.handleRecipientsTypeClick();
    }

    public void onFromAccountTypeImageViewClicked() {
        mPresenter.handleFromAccountTypeClick();
    }

    public void onPayClick() {
        if (binding.myRecipientCardAmountEt.hasFocus()) {
            binding.myRecipientCardAmountEt.clearFocus();
        }
        if (getSelectedFromAccountViewModel() != null) {
            mPresenter.handlePayButtonClick(getUserBeneficiaryDetailsViewModel().getBeneficiaryType(),mImaliDailyLimit,mPaymentDailyLimit);
        }
    }

    private void addListenerForAmount(CompatCurrency nbCompatCurrency) {
        RxTextView.textChanges(nbCompatCurrency.getInputField()).subscribe(chars -> {
            if (nbCompatCurrency.hasError()) {
                nbCompatCurrency.clearErrors();
            }
            mPresenter.handleAmountTextEnter();
        }, throwable -> NBLogger.e(TAG, throwable.getMessage()));
    }

    public void onFailedLoadRecipientLayoutClick() {
        mPresenter.handleFailedLoadRecipientLayoutClick();
    }

    @Override
    public void onDialogDismiss(String tag) {
        //dialog dismiss not required

    }

    @Override
    public void receiveRecipientData(ChooseRecipientsViewModel chooseRecipientsViewModel) {
        this.mChooseRecipientsViewModel = chooseRecipientsViewModel;
        //update the details on the currentRecipient Card
        String accountOrMobileNumber = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getBfDetails().get(chooseRecipientsViewModel.getCurrentBeneficiarySelection()).getAccountNumber();
        String bankName = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getBfDetails().get(chooseRecipientsViewModel.getCurrentBeneficiarySelection()).getBankName();
        updateContentDescriptionAccessibility(mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getContactCardName(), accountOrMobileNumber, bankName);
        if (!TextUtils.isEmpty(bankName)) {
            accountOrMobileNumber = String.format("%s%s%s%s", accountOrMobileNumber, StringUtils.COMMA, StringUtils.SPACE, bankName);
        }
        updateCurrentRecipientDetails(mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getContactCardName(), accountOrMobileNumber);
        searchLastPaidItem(chooseRecipientsViewModel.getCurrentBeneficiarySelection(), mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()));
    }

    // accessibility
    private void updateContentDescriptionAccessibility(String recipientName, String accountOrMobileNumber, String bankName) {
        String amountContentDescription = String.format(getString(R.string.quick_pay_enter_amount_acc), recipientName, bankName, accountOrMobileNumber);
        binding.myRecipientCardAmountEt.setContentDescription(amountContentDescription);
    }

    private void updateCurrentRecipientDetails(String recipientName, String accountOrMobileNumber) {
        binding.recipientNameTv.setText(recipientName);
        binding.recipientBankOrMobileTv.setText(accountOrMobileNumber);
        if (binding.myRecipientCardAmountEt != null && !TextUtils.isEmpty(binding.myRecipientCardAmountEt.getValue())) {
            mPresenter.checkInputsOnScreen(binding.myRecipientCardAmountEt, binding.tvFromAccountNumber.getText().toString(), isMobileNumberFlow());
        }
    }

    private void searchLastPaidItem(int selectedPosition, UserBeneficiaryDataViewModel userBeneficiaryDataViewModel) {
        UserBeneficiaryDetailsViewModel bfDetails = userBeneficiaryDataViewModel.getBfDetails().get(selectedPosition);
        List<UserBeneficiaryRecentPayDetailViewModel> recentTransactionList = userBeneficiaryDataViewModel.getBfRecentPayDetail();

        UserBeneficiaryRecentPayDetailViewModel recentPayDetailViewModel = null;
        if (CollectionUtils.isNotEmpty(recentTransactionList)) {
            for (UserBeneficiaryRecentPayDetailViewModel beneficiaryRecentPayDetailViewModel : recentTransactionList) {
                if (beneficiaryRecentPayDetailViewModel != null
                        && bfDetails.getBeneficiaryID() != null && bfDetails.getBeneficiaryType() != null
                        && bfDetails.getBeneficiaryID().equals(beneficiaryRecentPayDetailViewModel.getBeneficiaryID())
                        && bfDetails.getBeneficiaryType().equals(beneficiaryRecentPayDetailViewModel.getBeneficiarytype())) {
                    recentPayDetailViewModel = beneficiaryRecentPayDetailViewModel;
                    break;
                }
            }
        }
        updateLastPaidView(recentPayDetailViewModel);

    }

    private void updateLastPaidView(UserBeneficiaryRecentPayDetailViewModel recentPayDetailViewModel) {
        if (recentPayDetailViewModel != null) {
            String amount = String.valueOf(recentPayDetailViewModel.getPaymentAmount());
            if (!StringUtils.isNullOrEmpty(amount)) {
                amount = FormattingUtil.convertToSouthAfricaFormattedCurrency(recentPayDetailViewModel.getPaymentAmount());
            }
            if (!StringUtils.isNullOrEmpty(recentPayDetailViewModel.getPaymentDate())) {
                String paymentDate = String.valueOf(FormattingUtil.getFormattedDate(recentPayDetailViewModel.getPaymentDate(), za.co.nedbank.core.Constants.DATE_FORMAT.SERVER_DATE_FORMAT, FormattingUtil.DATE_FORMAT_DD_SPACE_MMM_SPACE_YYYY));
                binding.tvLastPaymentDetails.setText(String.format(getString(R.string.last_payment), amount, paymentDate));
                ViewUtils.showViews(binding.tvLastPaymentDetails);
            } else {
                ViewUtils.hideViews(binding.tvLastPaymentDetails);
            }
        } else {
            ViewUtils.hideViews(binding.tvLastPaymentDetails);
        }
    }

    private UserBeneficiaryDetailsViewModel getUserBeneficiaryDetailsViewModel() {
        UserBeneficiaryDetailsViewModel userBeneficiaryDetailsViewModel;
        if (mChooseRecipientsViewModel == null) {
            userBeneficiaryDetailsViewModel = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getBfDetails().get(0);
        } else {
            userBeneficiaryDetailsViewModel = this.mChooseRecipientsViewModel.getBfDetails().get(this.mChooseRecipientsViewModel.getCurrentBeneficiarySelection());
        }
        return userBeneficiaryDetailsViewModel;
    }

    private PayMode getPayModeByUserBeneficiary(UserBeneficiaryDetailsViewModel userBeneficiaryDetailsViewModel) {
        if (!TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getBeneficiaryType()) && userBeneficiaryDetailsViewModel.getBeneficiaryType().equalsIgnoreCase(BeneficiaryConstants.BENEFICIARY_TYPE_PREPAID)) {
            return PayMode.MOBILE;
        } else if (!TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getAccountType()) && userBeneficiaryDetailsViewModel.getAccountType().equalsIgnoreCase(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CC.getAccountTypeCode())) {
            return PayMode.CREDIT_CARD;
        } else {
            return PayMode.ACCOUNT;
        }
    }

    private boolean isMobileNumberFlow() {
        return getPayModeByUserBeneficiary(getUserBeneficiaryDetailsViewModel()) == PayMode.MOBILE;
    }

    @Override
    public void onItemClick(Integer... params) {
        binding.myRecipientsViewPager.setCurrentItem(params[0]);
    }

    @Override
    public void onItemSelected(int pos) {
        if (mPayAccounts != null && mPayAccounts.size() > pos) {
            fromAccountSelectedPos = pos;
            binding.tvFromAccountNumber.setText(mPayAccounts.get(pos).getDisplayAccountName());
            // accessibility
            binding.tvFromAccountNumber.setContentDescription(String.format(getString(R.string.quick_pay_selected_account_acc), mPayAccounts.get(pos).getDisplayAccountName()));
        }
    }

    @Override
    public String fetchFromAccountName() {
        PaymentViewModel mPaymentViewModel = getPaymentViewModel();

        String fromAccountName = StringUtils.EMPTY_STRING;
        if (mPaymentViewModel != null && mPaymentViewModel.getFromAccountViewModel() != null) {
            if (za.co.nedbank.core.Constants.POCKETS_ACCOUNT.equalsIgnoreCase(mPaymentViewModel.getFromAccountViewModel().getProductCode())) {
                fromAccountName = PaymentsTrackingEvent.VAL_SAVINGS_POCKET;
            } else if (StringUtils.isNotEmpty(mPaymentViewModel.getFromAccountViewModel().getAccountType())) {
                String fromAccountType = mPaymentViewModel.getFromAccountViewModel().getAccountType();
                za.co.nedbank.core.Constants.ACCOUNT_TYPES accountTypeSelected;
                try {
                    accountTypeSelected = za.co.nedbank.core.Constants.ACCOUNT_TYPES.valueOf(fromAccountType);
                } catch (IllegalArgumentException illegalArgumentException) {
                    accountTypeSelected = za.co.nedbank.core.Constants.ACCOUNT_TYPES.U0;
                }
                fromAccountName = getString(accountTypeSelected.getAccountTypeStringResId());
            }
        }
        return fromAccountName;
    }

    @Override
    public String fetchToAccountName() {
        PaymentViewModel mPaymentViewModel = getPaymentViewModel();

        String toAccountName = StringUtils.EMPTY_STRING;
        if (mPaymentViewModel == null) return toAccountName;

        if (isGovernmentPayment() && !TextUtils.isEmpty(mPaymentViewModel.getBeneficiaryName())) {
            toAccountName = mPaymentViewModel.getBeneficiaryName();
        } else {
            if (mPaymentViewModel.getPayMode() == PayMode.MOBILE) {
                toAccountName = PaymentsTrackingEvent.VAL_CELLPHONE;
            } else if (mPaymentViewModel.getPayMode() == PayMode.FOREIGN) {
                toAccountName = PaymentsTrackingEvent.VAL_ECOBANK;
            } else if (mPaymentViewModel.getPayMode() == PayMode.CREDIT_CARD) {
                toAccountName = PaymentsTrackingEvent.VAL_CREDIT_CARD;
            } else if (BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF.equalsIgnoreCase(mPaymentViewModel.getBeneficiaryType())) {
                toAccountName = BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF;
                if (StringUtils.isNotEmpty(mPaymentViewModel.getBankName()))
                    toAccountName = mPaymentViewModel.getBankName() + StringUtils.HYPHEN + BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF;
            } else if (BeneficiaryConstants.BENEFICIARY_TYPE_EXTERNAL.equalsIgnoreCase(mPaymentViewModel.getBeneficiaryType()) && StringUtils.isNotEmpty(mPaymentViewModel.getBankName())) {
                toAccountName = mPaymentViewModel.getBankName();
            } else {
                toAccountName = fetchMappedToAccountName();
            }
        }
        return toAccountName;
    }

    private String fetchMappedToAccountName() {
        PaymentViewModel mPaymentViewModel = getPaymentViewModel();

        String toAccountName = StringUtils.EMPTY_STRING;
        if (mPaymentViewModel !=null && mPaymentViewModel.getToAccountViewModel() != null && StringUtils.isNotEmpty(mPaymentViewModel.getToAccountViewModel().getAccountType())) {
            String toAccountType = mPaymentViewModel.getToAccountViewModel().getAccountType();
            za.co.nedbank.core.Constants.ACCOUNT_TYPES accountTypeSelected;
            try {
                accountTypeSelected = za.co.nedbank.core.Constants.ACCOUNT_TYPES.valueOf(toAccountType);
            } catch (IllegalArgumentException illegalArgumentException) {
                accountTypeSelected = za.co.nedbank.core.Constants.ACCOUNT_TYPES.U0;
            }
            toAccountName = getString(accountTypeSelected.getAccountTypeStringResId());

            if (BeneficiaryConstants.BENEFICIARY_TYPE_INTERNAL.equalsIgnoreCase(mPaymentViewModel.getBeneficiaryType())) {
                toAccountName = PaymentsTrackingEvent.VAL_NEDBANK + StringUtils.SPACE + toAccountName;
            }
        }
        return toAccountName;
    }

    private boolean isGovernmentPayment() {
        PaymentsViewModel viewModel = buildPaymentsViewModel(getSelectedFromAccountViewModel());
        return viewModel != null && viewModel.isGovernmentPayment();
    }

    private PaymentViewModel getPaymentViewModel() {
        PaymentsViewModel viewModel = buildPaymentsViewModel(getSelectedFromAccountViewModel());
        if (viewModel != null && viewModel.getPaymentViewModelList() != null && !viewModel.getPaymentViewModelList().isEmpty()) {
            return viewModel.getPaymentViewModelList().get(0);
        }
        return null;
    }

    @Override
    public String getBeneficiaryType() {
        PaymentViewModel mPaymentViewModel = getPaymentViewModel();

        if (mPaymentViewModel == null) return PaymentsTrackingEvent.VAL_EXISTING;

        if (mPaymentViewModel.getBeneficiaryType() != null && BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF.equalsIgnoreCase(mPaymentViewModel.getBeneficiaryType())) {
            return PaymentsTrackingEvent.VAL_BANK_APPROVED;
        } else
            return PaymentsTrackingEvent.VAL_EXISTING;
    }

    @Override
    public void showPopForInterNationalPayment(String bankName) {
        mPresenter.trackCmaActionOnOpen(bankName);
        final PaymentPayzarFlowInfoDialog paymentPayzarFlowInfoDialog = PaymentPayzarFlowInfoDialog.getInstance(new IDialog() {
            @Override
            public void onPositiveButtonClick() {
                mPresenter.fetchProfileForInternationalPaymentQuickPayFlow(TrackingEvent.ANALYTICS.CMA_POP_UP);
                mPresenter.trackCmaActionOnContinueClick();
            }

            @Override
            public void onNegativeButtonClick() {
                // Handle cancel event if required
                mPresenter.trackCmaActionOnCancelClick();
            }
        });
        if (!paymentPayzarFlowInfoDialog.isVisible()) {
            paymentPayzarFlowInfoDialog.showAllowingStateLoss(getParentFragmentManager(), PaymentPayzarFlowInfoDialog.TAG);
        }
    }

    @Override
    public void setLimits(List<ProfileLimit> limits) {
        mLimits=limits;
        if(isDailyLimitAvailable()){
            clearLimitError();
            binding.myRecipientCardAmountEt.updateEditextBackground(false,false);
            if (!TextUtils.isEmpty(binding.myRecipientCardAmountEt.getValue())) {
                mPresenter.checkInputsOnScreen(binding.myRecipientCardAmountEt, binding.tvFromAccountNumber.getText().toString(), isMobileNumberFlow());
            }
        }

    }

    @Override
    public boolean isDailyLimitAvailable() {
        if (CollectionUtils.isNotEmpty(mLimits)) {
            for (ProfileLimit limit : mLimits) {
                if (limit.getType() == ProfileLimitType.PAY_TO_A_CELLPHONE) {
                    mImaliDailyLimit = limit.getValue();
                } else if (limit.getType() == ProfileLimitType.PAYMENT) {
                    mPaymentDailyLimit = limit.getValue();
                }
            }
        }
        double remainingLimit;
        double mAmount = 0.0;
        String amount = FormattingUtil.convertCurrencyToAmount(binding.myRecipientCardAmountEt.getValue());
        if( !StringUtils.isNullOrEmpty(amount))
            mAmount = Double.parseDouble(amount);
        if (isMobileNumberFlow()) {
            remainingLimit = mImaliDailyLimit != null ? mImaliDailyLimit - mAmount : -1;
        } else {
            remainingLimit = mPaymentDailyLimit != null ? mPaymentDailyLimit- mAmount : -1;
        }
        return (remainingLimit >= 0);
    }
}
