package za.co.nedbank.ui.view.notification.contextswitch;

import android.os.Bundle;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.databinding.ActivityContextSwitchConfirmBinding;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextFedarationDetailsViewModel;
import za.co.nedbank.ui.di.AppDI;

public class ContextSwitchConfirmationActivity extends NBBaseActivity implements ContextSwitchConfirmationView {

    @Inject
    ContextSwitchConfirmationPresenter mConfirmationPresenter;

    SwitchContextFedarationDetailsViewModel mSwitchContextFedarationDetailsViewModel;
    private ActivityContextSwitchConfirmBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityContextSwitchConfirmBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        receiveBundle();
        initView();
    }


    private void initView() {
        binding.confirmText.setText(String.format(getString(R.string.are_you_sure_you_want_to_context_switch), mSwitchContextFedarationDetailsViewModel.getCustName()));
        binding.continueCs.setOnClickListener(v -> onPositiveButtonClick());
        binding.cancelCs.setOnClickListener(v -> onNegativeButtonClick());
    }


    private void receiveBundle() {
        if (getIntent() != null && getIntent().getExtras() != null) {
            mSwitchContextFedarationDetailsViewModel = getIntent().getExtras().getParcelable(NotificationConstants.EXTRA.CONTEXT_SWITCH_MODEL);
        }
    }

    public void onPositiveButtonClick() {
        mConfirmationPresenter.startContextSwitchingFlow(mSwitchContextFedarationDetailsViewModel);
    }

    public void onNegativeButtonClick() {
        mConfirmationPresenter.clearNotificationData();
        close();
    }



    @Override
    public void onBackPressed() {
        mConfirmationPresenter.clearNotificationData();
        super.onBackPressed();
    }
}
