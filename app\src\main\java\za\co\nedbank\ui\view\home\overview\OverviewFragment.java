/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.overview;

import static android.content.Context.ACCESSIBILITY_SERVICE;
import static za.co.nedbank.core.Constants.FULL_IMAGE_URL;
import static za.co.nedbank.core.Constants.SUB_URL;
import static za.co.nedbank.core.data.storage.StorageKeys.IS_PAYSHAP_WIDGET_ENABLED;
import static za.co.nedbank.core.navigation.NavigationTarget.HOME;
import static za.co.nedbank.core.utils.AppUtility.isValidWebUrl;

import android.accessibilityservice.AccessibilityServiceInfo;
import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;

import com.facetec.sdk.FaceTecSDK;
import com.google.android.material.snackbar.BaseTransientBottomBar;
import com.google.android.material.snackbar.Snackbar;
import com.google.android.material.tabs.TabLayout;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Transformation;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.subjects.BehaviorSubject;
import za.co.nedbank.R;
import za.co.nedbank.core.BuildConfig;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.convochatbot.view.ChatbotConstants;
import za.co.nedbank.core.dashboard.DashboardCardType;
import za.co.nedbank.core.dashboard.HomeWidget;
import za.co.nedbank.core.dashboard.WidgetData;
import za.co.nedbank.core.data.accounts.model.LinkableAccountDto;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.databinding.LayoutOverlayBackgroundBinding;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.enumerations.BackgroundImageTypeEnum;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.HttpStatus;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.sharedui.listener.CircularViewPagerHandler;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.ImageUtils;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.NBAnimationUtils;
import za.co.nedbank.core.utils.PicassoUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.IFragmentToActivityComListener;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.core.view.model.view_banker.ViewBankerDetailsViewModel;
import za.co.nedbank.core.view.model.view_banker.ViewShowBankerResponseViewModel;
import za.co.nedbank.core.view.preapprovedoffers.PreApprovedOffersUtility;
import za.co.nedbank.core.view.view_banker.ContactNedBankCenterDialog;
import za.co.nedbank.databinding.FragmentMenuOverviewBinding;
import za.co.nedbank.eficasdk.view.viewmodel.FicaSDKViewModel;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.insurance.view.other.model.response.offer.offer_details.InsGetOfferItemViewModel;
import za.co.nedbank.services.view.overview.AccountTypeRowInterface;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.core.deeplink.DeeplinkUtils;
import za.co.nedbank.ui.view.home.HomeActivity;
import za.co.nedbank.ui.view.home.IEventListener;
import za.co.nedbank.ui.view.home.overview.dashboard.DashboardAdapter;
import za.co.nedbank.ui.view.home.overview.dashboard.HomeWidgetAdapter;
import za.co.nedbank.ui.view.home.overview.dashboard.MyFinancesDashboardAdapter;
import za.co.nedbank.ui.view.home.overview.dashboard.WidgetDataInterface;
import za.co.nedbank.ui.view.home.payme_tab.PayMeTabWidget;
import za.co.nedbank.ui.view.tracking.AppTracking;


public class OverviewFragment extends NBBaseFragment implements OverviewView, IEventListener, AccountTypeRowInterface, PayMeTabWidget.OnTabSelectListener, WidgetDataInterface {

    public static final String TAG = OverviewFragment.class.getSimpleName();

    private static final int DEFAULT_PAGE = 0;
    private static Handler mHandler;
    @Inject
    OverviewPresenter mPresenter;
    @Inject
    ApplicationStorage mApplicationStorage;
    @Inject
    @Named("memory")
    ApplicationStorage mMemoryApplicationStorage;
    @Inject
    FeatureSetController mFeatureSetController;
    private IFragmentToActivityComListener mIFragmentToActivityComListener;

    private boolean hasTransactableAccount;
    private UserDetailViewModel userDetailViewModel;
    private DashboardAdapter dashboardAdapter;
    private BackgroundImageTypeEnum mSelectedImageType = BackgroundImageTypeEnum.DEFAULT;
    private OverviewToolbarPagerAdapter overviewAdapter;
    private OverviewType targetOverviewType;
    private int mLastSelectedPosition = DEFAULT_PAGE;
    private MyFinancesDashboardAdapter financesDashboardAdapter;
    private MediaCardViewModel mMediaCardViewModel;
    private HomeWidgetAdapter mHomeWidgetAdapter;
    private ContactNedBankCenterDialog mContactNedbankDialog;
    private ViewBankerDetailsViewModel mViewBankerDetailsViewModel;
    private String division;
    private Overview mOverview;
    private boolean mIsEyeIconClicked = false;

    @Inject
    @Named("memory")
    ApplicationStorage memoryApplicationStorage;
    BehaviorSubject<Integer> borrowWidgetIconNotificationCount = BehaviorSubject.create();
    private Activity mActivity;
    private boolean isUnreadChatAvailable;
    private boolean isChatConnected;
    String conversationId = AppTracking.MY_ACCOUNTS_OVERVIEW;
    String navigationTarget = "";
    private int insuranceOfferCounts;
    private FragmentMenuOverviewBinding binding;
    private LayoutOverlayBackgroundBinding overlayBackgroundBinding;

    public OverviewFragment() {
    }

    public static OverviewFragment getInstance() {
        return new OverviewFragment();
    }

    @Override
    public View onCreateView(final LayoutInflater inflater, final ViewGroup container, final Bundle savedInstanceState) {
        binding = FragmentMenuOverviewBinding.inflate(inflater, container, false);
        overlayBackgroundBinding = LayoutOverlayBackgroundBinding.bind(binding.getRoot());
        mPresenter.bind(this);
        mHandler = new Handler();
        if (getContext() != null) {
            binding.toolbarViewProgress.getIndeterminateDrawable().setColorFilter(ContextCompat.getColor(getContext(), R.color.white), PorterDuff.Mode.SRC_IN);
            binding.toolbarViewReloading.getIndeterminateDrawable().setColorFilter(ContextCompat.getColor(getContext(), R.color.white), PorterDuff.Mode.SRC_IN);
        }

        binding.toolbarViewPager.addOnPageChangeListener(new CircularViewPagerHandler(binding.toolbarViewPager) {
            @Override
            protected void selectedPage(int position) {
                onPageChange(position);
            }
        });
        binding.lotteBellView.setOnClickListener(v -> onClickNotificationCount());
        binding.bellIcon.setOnClickListener(v -> onClickNotificationCount());
        binding.eyeIcon.setOnClickListener(v -> onEyeIconClick());
        if (binding.eyeIcon.isChecked()) {
            binding.eyeIcon.setContentDescription(getString(R.string.show_balances));
        } else {
            binding.eyeIcon.setContentDescription(getString(R.string.hide_balances));
        }
        binding.llAvoLifestyleBannerView.avoAppLogo.setOnClickListener(v -> onAVOLifestyleViewMoreClick());
        binding.chatIcon.setOnClickListener(v -> onChatIconClick());
        binding.ivNextNav.setOnClickListener(v -> onNextNavigation());
        binding.ivBackNav.setOnClickListener(v -> onBackNavigation());
        return binding.getRoot();
    }

    @Override
    public void onCreate(@Nullable final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        mActivity = activity;
        if (activity instanceof IFragmentToActivityComListener) {
            mIFragmentToActivityComListener = (IFragmentToActivityComListener) getActivity();
        }

    }

    @Override
    public void onActivityCreated(@Nullable final Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        binding.overviewWelcome.setVisibility(View.INVISIBLE);
        initDashboardAdapter();
        initWidgetAdapter();
        setUpShimmerView();
        checkAvoBannerToggles();
        if (!isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS)
                || !isFeatureDisabled(FeatureConstants.PRE_APPROVED_ACCOUNTS)
                || !isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_TRANSACTION_NOTIFICATIONS)) {
            ViewUtils.showViews(binding.bellIcon);
        }
        mPresenter.updateFinancialWellnessDisplayedOnLandingCount();
        binding.eyeIcon.setChecked(mPresenter.isBalanceHidden());
    }

    private void setUpShimmerView() {
        int targetWidth = (DeviceUtils.getDeviceWidth(mActivity) - (2 * mActivity.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_20dp)));
        int targetHeight = (int) (mActivity.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_70dp));
        ViewUtils.setHeightAsPerAspectRatio(binding.llAvoLifestyleBannerView.avoAppLogo, targetWidth,
                targetHeight, DeviceUtils.getDeviceWidth(mActivity));
    }

    private void checkAvoBannerToggles() {
        //Show AVO Lifestyle banner if toggle is turned on.
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_LIFESTYLE)) {
            if (mMediaCardViewModel != null && mMediaCardViewModel.getMediaUrl() != null) {
                updateDynamicAvoBanner(mMediaCardViewModel);
            } else {
                mPresenter.loadMediaContent();
            }
        } else {
            updateLocalAvoBanner();
        }
    }

    public void initDashboardAdapter() {
        dashboardAdapter = new DashboardAdapter(getContext());
        financesDashboardAdapter
                = new MyFinancesDashboardAdapter(getContext(), this);
        financesDashboardAdapter.setOnTabSelectListener(this);
        financesDashboardAdapter.setDeviceWidth(DeviceUtils.getDeviceWidth(mActivity));
        dashboardAdapter.registerCardAdapter(financesDashboardAdapter);
        binding.dashboardRecycler.setAdapter(dashboardAdapter);
    }

    private void initWidgetAdapter() {
        borrowWidgetIconNotificationCount.onNext(0);
        mHomeWidgetAdapter = new HomeWidgetAdapter(getContext(), this,
                mPresenter.showShopWidgetBadge(), mApplicationStorage);
        addWidgets(borrowWidgetIconNotificationCount.getValue() > 0);
        int span = DeviceUtils.getDeviceDensityNumber(getResources()).endsWith(DeviceUtils.HIGH) ? 4 : 3;
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), span);
        binding.widgetRecycler.setLayoutManager(gridLayoutManager);
        binding.widgetRecycler.setAdapter(mHomeWidgetAdapter);
    }

    private void addWidgets(boolean hasNotificationIconOnBorrow) {
        HomeWidget[] homeWidgets = HomeWidget.class.getEnumConstants();
        if (homeWidgets != null && homeWidgets.length > 0) {
            List<WidgetData> widgetDataList = new ArrayList<>();
            for (HomeWidget homeWidget : homeWidgets) {
                if (homeWidget != null && homeWidget.isWidgetShow()) {
                    if (HomeWidget.REPORT_FRAUD.getWidgetName() == homeWidget.getWidgetName() && isFeatureDisabled(FeatureConstants.DynamicToggle.REPORT_FRAUD)) {
                        continue;
                    }
                    if (HomeWidget.COVID_19.getWidgetName() == homeWidget.getWidgetName() && isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_COVID_19_WIDGET)) {
                        continue;
                    }
                    boolean isBusinessUser = mMemoryApplicationStorage.getBoolean(StorageKeys.IS_BUSINESS_USER, false);
                    boolean isRetailPayReqToggleOff = isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_REQUEST_TO_PAY) && !isBusinessUser;
                    boolean isSbsPayReqToggleOff = isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_REQUEST_TO_PAY_SBS) && isBusinessUser;
                    if (HomeWidget.PAYSHAP_REQUEST.getWidgetName() == homeWidget.getWidgetName() && (isRetailPayReqToggleOff || isSbsPayReqToggleOff)) {
                        continue;
                    }
                    if (HomeWidget.GET_CASH.getWidgetName() == homeWidget.getWidgetName() && isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_GET_CASH_WIDGET)) {
                        continue;
                    }

                    if (HomeWidget.COVID_19.getWidgetName() == homeWidget.getWidgetName() && getNBActivity() != null && getNBActivity().isDemoMode()) {
                        continue;
                    }

                    if (HomeWidget.INSURANCE.getWidgetName() == homeWidget.getWidgetName() && (isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_INSURE_WIDGET))) {
                        continue;
                    }

                    if (HomeWidget.MERCHANT_SERVICES.getWidgetName() == homeWidget.getWidgetName()
                            && !(mPresenter.isMerchantUser() && !isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_MERCHANT_SERVICES))) {
                        continue;
                    }
                    if (HomeWidget.SHOP.getWidgetName() == homeWidget.getWidgetName() && isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_SHOP_WIDGET)) {
                        continue;
                    }
                    if (HomeWidget.DISC_RENEWAL.getWidgetName() == homeWidget.getWidgetName() && (isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DISC_RENEWAL_WIDGET) || !mPresenter.isDiscsAndFinesAvailable())) {
                        continue;
                    }

                    WidgetData widgetData = new WidgetData();
                    widgetData.setWidgetName(mActivity.getResources().getString(homeWidget.getWidgetName()));
                    if (homeWidget.getWidgetName() == R.string.home_widget_apply && !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_SAVE_RESUME)) {
                        widgetData.setWidgetName(mActivity.getResources().getString(R.string.home_widget_applications));
                    }
                    widgetData.setWidgetResource(homeWidget.getWidgetResource());
                    widgetData.setWidgetActionId(homeWidget.getActionId());
                    widgetData.setBgColor(homeWidget.getBgColor());
                    widgetData.setTextColor(homeWidget.getTextColor());
                    if (homeWidget.getWidgetName() == R.string.home_widget_offers_for_you && hasNotificationIconOnBorrow) {
                        widgetData.setHasNotificationIcon(true);
                        widgetData.setNotificationCountText(PreApprovedOffersUtility.obtainNotificationCounterText(borrowWidgetIconNotificationCount.getValue()));
                    }

                    if(homeWidget.getWidgetName() == HomeWidget.INSURANCE.getWidgetName() && insuranceOfferCounts >0){
                        widgetData.setHasNotificationIcon(true);
                        widgetData.setNotificationCountText(PreApprovedOffersUtility.obtainNotificationCounterText(insuranceOfferCounts));
                    }

                    if (homeWidget.getWidgetName() == R.string.shop_widget) {
                        widgetData.setHasNotificationIcon(true);
                        widgetData.setNotificationCountText("1");
                    }

                    //As a Minor LoggedIn then add only QUICK_PAY widget... other wise add above listed widget.
                    if (!mPresenter.isKidsProfile() || homeWidget.getWidgetName() == HomeWidget.QUICK_PAY.getWidgetName()) {
                        widgetDataList.add(widgetData);
                    }
                }
            }
            mHomeWidgetAdapter.swapData(widgetDataList);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        ViewUtils.hideViews(binding.ivNextNav, binding.ivBackNav, binding.toolbarViewPagerDots);
        if (memoryApplicationStorage.getBoolean(Constants.IS_CLIENT_DETAILS_API_CALLING_FROM_DASHBOARD, false)) {
            if (getActivity() != null && getView() != null) {
                getView().postDelayed(() -> {
                    if (getActivity() != null && !((HomeActivity) getActivity()).isFatcaRestrictionScreenAlreadyShown()) {
                        ((HomeActivity) getActivity()).setFatcaRestrictionScreenAlreadyShown(true);
                    }
                }, 1000);
            }
            memoryApplicationStorage.clearValue(Constants.IS_CLIENT_DETAILS_API_CALLING_FROM_DASHBOARD);
        } else {
            mPresenter.getUserInfo();
        }
        if (!isFeatureDisabled(FeatureConstants.FTR_INSURANCE_GET_OFFERS)) {
            mPresenter.getInsuranceOffers();
        }
        mPresenter.loadUserName();
        mPresenter.getDashboardOrder();
        mPresenter.getTotalBankingUnreadMessages();
        if (!memoryApplicationStorage.getBoolean(IS_PAYSHAP_WIDGET_ENABLED, false)) {
            mPresenter.fetchAccountsForShapIdWidget();
        }
        navigationTarget = mApplicationStorage.getString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME, StringUtils.EMPTY_STRING);
        mApplicationStorage.clearValue(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME);
    }

    public void invalidateAccountApiCache() {
        mPresenter.invalidateAccountApiCache();
    }

    public void setTargetOverviewPage(OverviewType overviewType) {
        targetOverviewType = overviewType;
    }

    public void setUserDetailData(boolean isSuccess, UserDetailData userDetailData, Error error) {
        if (isSuccess) {
            mPresenter.getUserInfoData(userDetailData);
        } else {
            if (error.getCode() == HttpStatus.NO_CONTENT) {
                showEmptyView(error.getMessage());
            } else {
                showError(error.getMessage());
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mPresenter.unbind();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        dashboardAdapter.cleanup();
    }

    @Override
    public void onDetach() {
        super.onDetach();
        invalidateAccountApiCache();
    }

    @Override
    public void showPreferredName(final String name) {

        if (StringUtils.isNullOrEmpty(name.trim())) {
            mPresenter.loadCustomerNAme();
        } else {
            mPresenter.saveCustomerPreferredName(name);
            binding.overviewWelcome.setText(name.trim());
        }
        binding.overviewWelcome.setVisibility(View.VISIBLE);
    }

    @Override
    public void showOverviewError(final String message) {
        if (isAdded() && getContext() != null) {
            showError(getContext().getString(R.string.overview_loading_error_title), message);
        }
    }

    @Override
    public void showOverviewLoading(boolean loading) {
        binding.toolbarViewProgress.setVisibility(loading ? View.VISIBLE : View.INVISIBLE);
    }

    @Override
    public void showOverview(final Overview overview) {
        this.mOverview = overview;
        binding.toolbarViewPagerDots.setupWithViewPager(binding.toolbarViewPager);
        overviewAdapter = new OverviewToolbarPagerAdapter((NBBaseActivity) mActivity, this, mSelectedImageType);
        overviewAdapter.swapData(overview.accountsOverviews);
        binding.toolbarViewPager.setOffscreenPageLimit(overview.accountsOverviews.size());
        ViewUtils.showViews(binding.ivNextNav, binding.ivBackNav, binding.toolbarViewPagerDots);
        if (!StringUtils.isNullOrEmpty(navigationTarget)) {
            mLastSelectedPosition = setNavigationIndex(overview, navigationTarget);
        } else if(!mIsEyeIconClicked) {
            mLastSelectedPosition = DEFAULT_PAGE;
        }
        binding.toolbarViewPager.setAdapter(overviewAdapter);
        binding.toolbarViewPager.setCurrentItem(mLastSelectedPosition);

        //Forcing to land on the Everyday Banking carousel (instead of Financial Wellness carousel)
        if (!mIsEyeIconClicked && mPresenter.isFinancialWellnessAvailable()
                && mLastSelectedPosition == DEFAULT_PAGE
                && !mPresenter.isLandOnFinancialWellnessCarousel()) {
            mLastSelectedPosition = mPresenter.getOverviewPosition(overview.accountsOverviews, OverviewType.EVERYDAY_BANKING);
        }
        mIsEyeIconClicked = false;
        updateViewPagerToLastSelectedPage();

        // check if it is being called from feature detail, if yes then show the appropriate screen
        mPresenter.checkAndShowFeatureDeepLinkScreen(overview.accountsOverviews);

        // set overview title as per selected overview account type
        if (overview.accountsOverviews != null && overview.accountsOverviews.size() > mLastSelectedPosition) {
            setEyeIconVisibility(overview.accountsOverviews.get(mLastSelectedPosition).overviewType);
            mPresenter.setOverviewTitleFor(overview.accountsOverviews.get(mLastSelectedPosition).overviewType);
            if (overview.accountsOverviews.get(mLastSelectedPosition).overviewType == OverviewType.FINANCIAL_WELLNESS) {
                mPresenter.trackFinancialWellnessPageLoad();
            }
        }

        if (overview.accountsOverviews != null) {
            int everydayBankingPosition = mPresenter.getOverviewPosition(overview.accountsOverviews, OverviewType.EVERYDAY_BANKING);
            if (overview.accountsOverviews.size() > everydayBankingPosition) {
                AccountSummary currentAccount = mPresenter.getCurrentAccount(everydayBankingPosition, overview.accountsOverviews);
                AccountSummary savingAccount = mPresenter.getSavingsAccount(everydayBankingPosition, overview.accountsOverviews);

                if (mActivity instanceof HomeActivity) {
                    ((HomeActivity) mActivity).unboxingFlowCheckOnAccountDetails(currentAccount, savingAccount);
                }
            }
        }
    }

    private int setNavigationIndex(Overview overview, String navigationTarget) {
        if (navigationTarget.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.VIEW_GREENBACKS)) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if (accountsOverview.overviewType.equals(OverviewType.REWARDS)) {
                    return overview.accountsOverviews.indexOf(accountsOverview);
                }
            }
        } else if (navigationTarget.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.INTENT_CREDIT_SCORE)) {
            return navigationIndexCreditScore(overview);
        } else if (navigationTarget.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.CREDIT_HEALTH)) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if (accountsOverview.overviewType.equals(OverviewType.FINANCIAL_WELLNESS)) {
                    mPresenter.setLandOnFinancialWellnessCarousel(true);
                    return overview.accountsOverviews.indexOf(accountsOverview);
                }
            }
        } else {
            return setNavigationIndexForMore(overview);
        }
        return 0;
    }

    private int setNavigationIndexForMore(Overview overview){
        if (navigationTarget.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.INVESTMENT_DASHBOARD)) {
            return navigationIndexInvestment(overview);
        } else if (navigationTarget.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.INSURANCE_OPTIONS)) {
            return navigationIndexInsuranceoption(overview);
        } else if (navigationTarget.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.INTERNATIONAL_BANKING)) {
            return navigationIndexInternationalBankingOption(overview);
        }
        return 0;
    }
    private int navigationIndexInsuranceoption(Overview overview) {
        for (AccountsOverview accountsOverview : overview.accountsOverviews) {
            if (accountsOverview.overviewType.equals(OverviewType.INSURANCE)) {
                return overview.accountsOverviews.indexOf(accountsOverview);
            }
        }
        return 0;
    }
    private int navigationIndexInternationalBankingOption(Overview overview) {
        for (AccountsOverview accountsOverview : overview.accountsOverviews) {
            if (accountsOverview.overviewType.equals(OverviewType.FOREIGN_CURRENCY_ACCOUNT)) {
                return overview.accountsOverviews.indexOf(accountsOverview);
            }
        }
        return 0;
    }

    private int navigationIndexCreditScore(Overview overview) {
        for (AccountsOverview accountsOverview : overview.accountsOverviews) {
            if (accountsOverview.overviewType.equals(OverviewType.FINANCIAL_WELLNESS)) {
                mPresenter.setmLandOnFinancialWellnessCarousel(true);
                return overview.accountsOverviews.indexOf(accountsOverview);
            }
        }
        return 0;
    }

    private int navigationIndexInvestment(Overview overview) {
        for (AccountsOverview accountsOverview : overview.accountsOverviews) {
            if (accountsOverview.overviewType.equals(OverviewType.INVESTMENTS)) {
                return overview.accountsOverviews.indexOf(accountsOverview);
            }
        }
        return 0;
    }

    @Override
    public void setDashboardOrder(final List<DashboardCardType> dashboardOrder) {
        dashboardAdapter.setDashboardOrder(dashboardOrder);
    }

    @Override
    public boolean isFeatureDisabled(String feature) {
        return mFeatureSetController.isFeatureDisabled(feature);
    }

    @Override
    public void showOverviewReloading(boolean reloading) {
        binding.toolbarViewReloading.setVisibility(reloading ? View.VISIBLE : View.INVISIBLE);
    }

    @Override
    public void setBackgroundImage(String imagePath) {
        overlayBackgroundBinding.backgroundImage.setBackgroundResource(0);
        if (mActivity != null) {
            ImageUtils.setImage(mActivity, imagePath, overlayBackgroundBinding.backgroundImage);
        }
    }

    @Override
    public void loadOverview(BackgroundImageTypeEnum backgroundImageType) {
        mSelectedImageType = backgroundImageType;
        overviewAdapter = new OverviewToolbarPagerAdapter((NBBaseActivity) mActivity, this, mSelectedImageType);
        binding.toolbarViewPager.setAdapter(overviewAdapter);
        overviewAdapter.dummyData(true);

        boolean isContextSwitched = false;
        if (getActivity() != null && getActivity().getIntent() != null) {
            isContextSwitched = getActivity().getIntent().getBooleanExtra(NavigationTarget.PARAM_IS_CONTEXT_SWITCHED, false);
        }
        if (isContextSwitched) {
            mPresenter.refreshAccountsAndLoadOverview();

        } else {
            mPresenter.loadOverview();
        }
    }


    @Override
    public void shouldShowOverlay(boolean showOverlay) {
        overlayBackgroundBinding.overlayImage.setVisibility(showOverlay ? View.VISIBLE : View.GONE);
    }

    @Override
    public String providePreapprovedPersonalOfferText(String loanOfferType) {
        mPresenter.setZoomSDKVersion(FaceTecSDK.version());
        switch (loanOfferType) {
            case Constants.PreApprovedOffersTypes.CONSOLIDATED_LOAN_OFFER:
                return mActivity != null ? mActivity.getString(R.string.consolidated_loan_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.PERSONAL_LOAN_OFFER:
                return mActivity != null ? mActivity.getString(R.string.personal_offer_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.NEW_CREDIT_CARD_OFFER:
                return mActivity != null ? mActivity.getString(R.string.card_offer_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.CREDIT_CARD_BALANCE_TRANSFER:
                return mActivity != null ? mActivity.getString(R.string.ccbt_offer_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.CREDIT_CARD_SAA:
                return mActivity != null ? mActivity.getString(R.string.saa_card_offer_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.CREDIT_CARD_AMEX:
                return mActivity != null ? mActivity.getString(R.string.amex_card_offer_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.CARD_LIMIT_INCREASE_OFFER:
            case Constants.PreApprovedOffersTypes.GOLDEN_GOOSE:
            case Constants.PreApprovedOffersTypes.UGLY_DUCKLING_LIMIT_INCREASE:
                return mActivity != null ? mActivity.getString(R.string.card_limit_increase_offer_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.OVER_DRAFT_OFFER:
                return mActivity != null ? mActivity.getString(R.string.overdraft_offer_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.OVERDRAFT_LIMIT_INCREASE:
                return mActivity != null ? mActivity.getString(R.string.overdraft_limit_increase_offer_text) : StringUtils.EMPTY_STRING;
            case Constants.EverdayBankingOffers.SAVVY_BUNDLE:
                return mActivity != null ? mActivity.getString(R.string.savvy_bundle_offer_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.PRE_NCA_OVERDRAFT:
                return mActivity != null ? mActivity.getString(R.string.pre_nca_overdraft_text) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.INSURANCE_OFFER:
                return mActivity != null ? mActivity.getString(R.string.insure_) : StringUtils.EMPTY_STRING;
            case Constants.PreApprovedOffersTypes.INVESTMENT_OFFER:
                return mActivity != null ? mActivity.getString(R.string.invest_) : StringUtils.EMPTY_STRING;
            default:
                return StringUtils.EMPTY_STRING;
        }
    }

    @Override
    public void setResult(Map<String, Object> params) {
        if (null != params && !params.isEmpty()) {
            boolean priorityUpdateStatus = (boolean) params.get(za.co.nedbank.services.Constants.BUNDLE_PRIORITY_UPDATED);
            if (priorityUpdateStatus)
                invalidateAccountApiCache();
        }
    }

    @Override
    public void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent) {
        binding.chatIcon.setImageResource(unreadChatEvent.isUnreadLifestyleChat() ?
                R.drawable.ic_chat_white_notification_wrapper : R.drawable.ic_chat_icon_white_wrapper);
    }

    @Override
    public void setChatIcon(int totalUnreadMessageCount) {
        binding.chatIcon.setImageResource(totalUnreadMessageCount > 0 ?
                R.drawable.ic_chat_white_notification_wrapper : R.drawable.ic_chat_icon_white_wrapper);

    }

    @Override
    public void showCustomerName(String name, boolean isDefaultProfile) {
        if (StringUtils.isNullOrEmpty(name.trim())) {
            binding.overviewWelcome.setText(StringUtils.EMPTY_STRING);
        } else {
            binding.overviewWelcome.setText(StringUtils.removeTitles(name.trim()));
            if (getNBActivity() != null && !getNBActivity().isDemoMode() && isDefaultProfile) {
                mApplicationStorage.putString(Constants.KEY_USER_CLIENT_NAME, name.trim());
            }
        }
        binding.overviewWelcome.setVisibility(View.VISIBLE);
    }

    @Override
    public String getBirthDate() {
        return mActivity == null ? null : ((HomeActivity) mActivity).getBirthDate();
    }

    @Override
    public boolean isSAResident() {
        return mActivity != null && (mActivity instanceof HomeActivity) && ((HomeActivity) mActivity).isSAResident();
    }

    @Override
    public void setBackgroundImage(int imageId) {
        overlayBackgroundBinding.backgroundImage.setVisibility(View.INVISIBLE);
        overlayBackgroundBinding.backgroundImage.setImageBitmap(null);
        overlayBackgroundBinding.backgroundImage.setBackgroundResource(imageId);
        NBAnimationUtils.fadeIn(overlayBackgroundBinding.backgroundImage);
    }

    @Override
    public void onEvent(final int event) {
        if (event == IEventListener.EVENT_LINK_ACCOUNT_BUTTON_CLICKED) {
            mPresenter.getDashboardOrder();
        }
    }

    public void receiveUpdatedPaymentBundle(final PaymentsViewModel paymentsViewModel) {
        if (isAdded()) {
            dashboardAdapter.passPaymentUpdatedBundleToRecipientCard(paymentsViewModel);
        }
    }

    public void updateUserBeneficiaryData() {
        if (isAdded()) {
            dashboardAdapter.updateUserBeneficiaryData();
        }
    }

    @Override
    public void onAccountClick(final AccountSummary accountSummary) {
        mPresenter.accountTypeClicked(accountSummary, null);
    }

    @Override
    public void onJoinButtonClick(AccountSummary accountSummary, OverviewType overviewType) {
        mPresenter.onJoinButtonClick(accountSummary, overviewType);
    }

    public void onPageChange(final int position) {
        mLastSelectedPosition = position;
        final AccountsOverview accountsOverview = overviewAdapter.getItem(position);
        if (accountsOverview != null) {
            mPresenter.setOverviewTitleFor(accountsOverview.overviewType);
            OverviewType overviewType = accountsOverview.overviewType;
            setEyeIconVisibility(overviewType);
        }
        // accessibility
        AccessibilityManager am = (AccessibilityManager) getContext().getSystemService(ACCESSIBILITY_SERVICE);

        if (am.isEnabled()) {

            if (accountsOverview != null && accountsOverview.overviewType != null) {
                setViewPagerContentDescription(accountsOverview.overviewType, accountsOverview);
            }
            AccessibilityEvent event = AccessibilityEvent.obtain(AccessibilityEvent.TYPE_VIEW_CLICKED);
            ViewParent parent = binding.toolbarViewPager.getParent();
            if (parent != null) {
                parent.requestSendAccessibilityEvent(binding.toolbarViewPager, event);
            }
            binding.toolbarViewPager.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED);
        }
        binding.nsOverview.smoothScrollTo(0,0);
    }

    private void setEyeIconVisibility(OverviewType overviewType) {
        if (overviewType == OverviewType.EVERYDAY_BANKING
                || overviewType == OverviewType.CREDIT_CARDS
                || overviewType == OverviewType.LOANS
                || overviewType == OverviewType.INVESTMENTS
                || overviewType == OverviewType.REWARDS) {
            binding.eyeIcon.setVisibility(View.VISIBLE);
        } else {
            binding.eyeIcon.setVisibility(View.GONE);
        }
    }

    private void setViewPagerContentDescription(@NonNull OverviewType overviewType, @NonNull AccountsOverview accountsOverview) {
        switch (overviewType) {
            case EVERYDAY_BANKING:
                binding.toolbarViewPager.setContentDescription(String.format(getContext().getString(za.co.nedbank.services.R.string.overview_account_detail_acc), accountsOverview.summaryValue1, accountsOverview.summaryValue2));
                break;
            case CREDIT_CARDS:
                binding.toolbarViewPager.setContentDescription(String.format(getContext().getString(za.co.nedbank.services.R.string.overview_credit_card_detail_acc), accountsOverview.summaryValue1, accountsOverview.summaryValue2));
                break;
            case INVESTMENTS:
                binding.toolbarViewPager.setContentDescription(String.format(getContext().getString(za.co.nedbank.services.R.string.overview_investment_detail_acc), accountsOverview.summaryValue1, accountsOverview.summaryValue2));
                break;
            case REWARDS:
                binding.toolbarViewPager.setContentDescription(String.format(getContext().getString(za.co.nedbank.services.R.string.overview_greenbacks_detail_acc), accountsOverview.summaryValue1, accountsOverview.summaryValue2));
                break;
            case INSURANCE:
                binding.toolbarViewPager.setContentDescription(getContext().getString(za.co.nedbank.services.R.string.insurance));
                break;
            case FINANCIAL_WELLNESS:
                binding.toolbarViewPager.setContentDescription(getContext().getString(R.string.overview_financial_wellness));
                break;
            case FOREIGN_CURRENCY_ACCOUNT:
                binding.toolbarViewPager.setContentDescription(getContext().getString(R.string.overview_international_banking_title));
                break;
            case LOANS:
                binding.toolbarViewPager.setContentDescription(getContext().getString(R.string.account_type_loan));
                break;
            case LIFESTYLE:
                binding.toolbarViewPager.setContentDescription(getContext().getString(R.string.overview_lifestyle));
                break;
            case CLUB_ACCOUNTS:
                binding.toolbarViewPager.setContentDescription(getContext().getString(R.string.overview_club_accounts_title));
                break;
            case MERCHANT_SERVICES:
                binding.toolbarViewPager.setContentDescription(getContext().getString(R.string.overview_merchant_services_title));
                break;
            default:
                //do nothing
        }
    }

    public void onClickNotificationCount() {
        mPresenter.handleClickNotificationCount();
    }

    @Override
    public void receivePreApprovedOffersCount(int preApprovedOfferCount) {

        if (preApprovedOfferCount > 0) {
            ViewUtils.hideViews(binding.bellIcon);
            ViewUtils.showViews(binding.lotteBellView);
            binding.lotteBellView.setRepeatCount(2);
            binding.lotteBellView.playAnimation();
        } else {
            ViewUtils.hideViews(binding.lotteBellView);
            ViewUtils.showViews(binding.bellIcon);
        }
    }

    @Override
    public void handleErrorPreApprovedOffers() {
        //do nothing as of current logic - updating view is not required, may be updated in future
    }

    @Override
    public void handleErrorLifecycleDashboard() {
        //do nothing as of current logic - updating view is not required, may be updated in future
    }

    @Override
    public boolean canTransact() {
        return this.hasTransactableAccount;
    }

    @Override
    public String getFicaStatus() {
        return userDetailViewModel == null ? StringUtils.EMPTY_STRING : userDetailViewModel.getFicaStatus();
    }

    @Override
    public void hasTransactableAccount(boolean isTransactable) {
        this.hasTransactableAccount = isTransactable;
    }

    @Override
    public void setUserInfo(UserDetailViewModel userDetailViewModel) {
        this.userDetailViewModel = userDetailViewModel;
        if (!TextUtils.isEmpty(userDetailViewModel.getClientType())) {
            mApplicationStorage.putInteger(Constants.KEY_USER_CLIENT_TYPE, Integer.parseInt(userDetailViewModel.getClientType()));
        }
    }

    @Override
    public void showError(final String message) {
        showError(getString(R.string.error), message);
    }

    @Override
    public void showEmptyView(String errorMessage) {
        ViewUtils.showViews(binding.tvNoContent);
        if (!StringUtils.isNullOrEmpty(errorMessage.trim())) {
            binding.tvNoContent.setText(errorMessage);
        }
    }

    public void onEyeIconClick() {
        mIsEyeIconClicked = true;
        mPresenter.saveShowHideBalance();

        String msg;
        if (binding.eyeIcon.isChecked()) {
            msg = getString(R.string.balances_hidden);
            mPresenter.trackEyeIconAnalytics(AppTracking.MY_ACCNT_BAL_INFO_HIDE);
        } else {
            msg = getString(R.string.balances_shown);
            mPresenter.trackEyeIconAnalytics(AppTracking.MY_ACCNT_BAL_INFO_SHOW);
        }
        Snackbar.make(binding.coordinatorLayout, msg, BaseTransientBottomBar.LENGTH_INDEFINITE)
                .setDuration(5000)
                .setActionTextColor(ContextCompat.getColor(getContext(), R.color.snackbar_action_title))
                .setAction(getString(R.string.undo), v -> handleUndoAction())
                .show();
    }

    private void handleUndoAction() {
        mIsEyeIconClicked = true;
        binding.eyeIcon.setChecked(!binding.eyeIcon.isChecked());
        mPresenter.saveShowHideBalance();
        mPresenter.trackEyeIconAnalytics(AppTracking.MY_ACCNT_BAL_INFO_UNDO);
    }

    @Override
    public void refreshBalances() {
        if (mOverview != null) {
            showOverview(mOverview);
        }
    }

    public void onChatIconClick() {

        if (showFeatureNotAvailableDialog()) return;

        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_FORCE_UPDATE)) {
            mPresenter.handleUpdateApp(getString(R.string.app_update_available_message_chatbot),
                    getString(R.string.app_update_available_title_chatbot));
        } else if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_CHATBOT)) {
            handleEnbiFlow();
        }else
            handleNormalChatFlow();
    }

    private void handleEnbiFlow() {
        if (isChatConnected || isUnreadChatAvailable) {
            mPresenter.navigateToChatActivity();
        } else if (!mPresenter.isBusinessUser()
                || !mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_CHATBOT_SBS)) {
            handleEnbiFlowFirstTimeAndReturningUsers();
        } else {
            handleNormalChatFlow();
        }
    }

    private void handleEnbiFlowFirstTimeAndReturningUsers() {
        if (isChatbotIntoJourneyCompleted())
            mPresenter.navigateToChatBotActivity(conversationId);
        else
            mPresenter.navigateToChatBotIntroductionActivity(conversationId);
    }

    public boolean isChatbotIntoJourneyCompleted() {
        return mApplicationStorage.getBoolean(ChatbotConstants.StorageKeys.CHATBOT_INTRO_DISPLAYED, false);
    }

    private void handleNormalChatFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.APP_CHAT)) {
            mPresenter.navigateToChatActivity();
        } else{
            mPresenter.navigateToChatErrorActivity();
        }
    }

    @Override
    public void navigateToTargetOverviewPage() {
        if (targetOverviewType != null) {
            int index = overviewAdapter.getOverviewPageIndex(targetOverviewType);
            if (index >= 0) {
                binding.toolbarViewPager.setCurrentItem(index, false);
            }
            targetOverviewType = null;
        }
    }

    @Override
    public void onUnreadChatEvent(UnreadChatEvent unreadChatEvent) {
        isUnreadChatAvailable = unreadChatEvent.isUnreadChat();
        binding.chatIcon.setImageResource(unreadChatEvent.isUnreadChat() ?
                R.drawable.ic_chat_white_notification_wrapper : R.drawable.ic_chat_icon_white_wrapper);
    }

    private void updateViewPagerToLastSelectedPage() {
        if (binding.toolbarViewPager.getAdapter() != null
                && binding.toolbarViewPager.getAdapter().getCount() > mLastSelectedPosition) {
            binding.toolbarViewPager.setCurrentItem(mLastSelectedPosition, false);
        }
    }

    @Override
    public void onTabSelected(TabLayout.Tab tab) {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                View targetView = financesDashboardAdapter.getComponent();
                if (targetView != null && targetView.getParent() != null) {
                    try {
                        targetView.getParent().requestChildFocus(targetView, targetView);
                    } catch (NullPointerException e) {
                        NBLogger.e(TAG, Log.getStackTraceString(e));
                    }
                }
            }
        }, 50);
    }

    @Override
    public List<LinkableAccountDto> getLinkableAccountList(LinkableAccountDto linkableAccountDto) {
        List<LinkableAccountDto> linkableAccountList = new ArrayList<>();
        linkableAccountList.add(linkableAccountDto);
        return linkableAccountList;
    }

    @Override
    public String getNewFreeFeature() {
        return getString(R.string.new_free_savings_feature);
    }


    @Override
    public String getMyPocketString() {
        return getString(R.string.my_savings_pocket);
    }

    @Override
    public String getSavingsGoalPocket() {
        return getString(R.string.my_saving_pockets);
    }


    @Override
    public void widgetClicked(WidgetData widgetData) {
        if (DeeplinkUtils.canMoveToDeeplinkFramework(widgetData.getWidgetName())) {
            mPresenter.prepareDataAndSendToWidgetAdapter(widgetData.getWidgetName());
        } else {
            handleWidgets(widgetData);
        }
    }

    private void handleWidgets(WidgetData widgetData) {
        switch (widgetData.getWidgetActionId()) {
            case HomeWidget.ACTION_QUICK_PAY:
                memoryApplicationStorage.putString(Constants.PAYMENT_START_FROM, HOME);
                Bundle b = new Bundle();
                b.putInt(OverviewPresenter.SCREEN_TYPE, OverviewPresenter.QUICK_PAY);
                mPresenter.checkFica(b);
                break;
            case HomeWidget.ACTION_PAY_ME:
                if (mPresenter.isFicaEnabledForPayME()) {
                    Bundle b1 = new Bundle();
                    b1.putInt(OverviewPresenter.SCREEN_TYPE, OverviewPresenter.PAY_ME);
                    mPresenter.checkFica(b1);
                } else {
                    mPresenter.openPayMe();
                }
                break;
            case HomeWidget.ACTION_REPORT_FRAUD:
                mPresenter.openReportFraud();
                break;
            case HomeWidget.ACTION_BUY_PREPAID:
                mPresenter.openBuyPrepaid();
                break;
            case HomeWidget.ACTION_YOUR_BANKER:
                mPresenter.openYourBanker();
                break;
            case HomeWidget.ACTION_BUY_ELECTRICITY:
                mPresenter.openBuyElectricity();
                break;
            case HomeWidget.ACTION_LATEST:
                mPresenter.openCovid19();
                break;
            case HomeWidget.ACTION_BORROW: {
                borrowAction();
                break;
            }
            case HomeWidget.ACTION_HOME_LOAN:
                mPresenter.openHomeLoanToolkit();
                break;
            case HomeWidget.ACTION_GET_CASH:
                if (mIFragmentToActivityComListener != null) {
                    mIFragmentToActivityComListener.onEvent(IFragmentToActivityComListener.EVENT_CONSTANTS.EVENT_GETCASH, null);
                }
                if (mPresenter.isFicaEnabledForPayME()) {
                    Bundle b2 = new Bundle();
                    b2.putInt(OverviewPresenter.SCREEN_TYPE, OverviewPresenter.GET_CASH);
                    mPresenter.checkFica(b2);
                } else {
                    mPresenter.openGetCashWidget();
                }

                break;
            case HomeWidget.ACTION_STATEMENTS:
                mPresenter.navigateToStatementsAndDocuments();
                break;
            case HomeWidget.ACTION_APPLY:
                mPresenter.trackActionForApplyWidget();
                if (mIFragmentToActivityComListener != null) {
                    mIFragmentToActivityComListener.onEvent(IFragmentToActivityComListener.EVENT_CONSTANTS.EVENT_APPLY, null);
                }
                break;
            case HomeWidget.ACTION_INSURE:
                mPresenter.insureWidgetClicked();
                break;
            case HomeWidget.ACTION_MERCHANT:
                mPresenter.smaIntroduction();
                break;
            case HomeWidget.ACTION_SHOP:
                if (getNBActivity() != null && getNBActivity().isDemoMode()) {
                    mPresenter.moveToAvoDemoSplashScreen();
                } else {
                    mPresenter.trackActionForShopWidget();
                    mPresenter.shopDashborad();
                }
                break;
            case HomeWidget.ACTION_DISC_RENEWAL:
                mPresenter.discRenewalWidgetClick();
                break;
            case HomeWidget.ACTION_PAYSHAP_REQUEST:
                mPresenter.trackActionPayRequestWidget();
                mPresenter.moveToMakePayshapRequest();
                break;
            default:
                //do nothing
        }
    }

    private void borrowAction() {
        if(!isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_FOR_YOU_AND_VALUE_ADDS_OFFERS))
        {
            mPresenter.openForYouOfferEnhanceScreen();
        }else {
            mPresenter.openBorrowScreen();
        }
    }

    @Override
    public void showViewBankerIcon(ViewShowBankerResponseViewModel viewBankerDetailsViewModel) {
        if (viewBankerDetailsViewModel != null && viewBankerDetailsViewModel.getData() != null && viewBankerDetailsViewModel.getData().size() > 0 &&
                viewBankerDetailsViewModel.getData().get(0) != null &&
                viewBankerDetailsViewModel.getData().get(0).getDivision() != null) {
            division = viewBankerDetailsViewModel.getData().get(0).getDivision();
            if (!TextUtils.isEmpty(division) && (division.equalsIgnoreCase(Constants.BANKER_RRB) || division.equalsIgnoreCase(Constants.BANKER_BB))) {
                HomeWidget.YOUR_BANKER.setWidgetShow(true);
                addWidgets(borrowWidgetIconNotificationCount.getValue() > 0);
            }
        }
    }

    @Override
    public void showGetCashIcon(boolean b) {
        HomeWidget.GET_CASH.setWidgetShow(b);
        addWidgets(borrowWidgetIconNotificationCount.getValue() > 0);
    }

    @Override
    public void showShopIcon(boolean b) {
        HomeWidget.SHOP.setWidgetShow(b);
        addWidgets(borrowWidgetIconNotificationCount.getValue() > 0);
    }

    @Override
    public void showBankerDetails(ViewBankerDetailsViewModel viewBankerDetailsViewModel) {
        mViewBankerDetailsViewModel = viewBankerDetailsViewModel;
        if (mViewBankerDetailsViewModel != null && mViewBankerDetailsViewModel.getBankerDataViewModel() != null
                && !mViewBankerDetailsViewModel.getBankerDataViewModel().isDefaultBanker()) {
            mPresenter.navigateToViewBanker(mViewBankerDetailsViewModel.getBankerDataViewModel(), division);
        }
    }

    @Override
    public void showBankerError() {
        if (mContactNedbankDialog != null) {
            mContactNedbankDialog.dismissDialog();
        }
    }

    @Override
    public void receiveCountForBorrowWidget(int notificationCount) {
        borrowWidgetIconNotificationCount.onNext(notificationCount);
        addWidgets(borrowWidgetIconNotificationCount.getValue() > 0);
    }

    @Override
    public void moveToDashboardPosition(int position) {
        if (binding.toolbarViewPager.getAdapter() != null
                && binding.toolbarViewPager.getAdapter().getCount() > position) {
            binding.toolbarViewPager.setCurrentItem(position, false);
        }
    }

    @Override
    public void sendAnalytics(String actionName, String entryPoint) {
        mPresenter.trackActionWithEntryPoint(actionName, entryPoint);
    }

    @Override
    public String getOverviewProductGroup() {
        String value = null;
        if (overviewAdapter != null && overviewAdapter.getCount() > mLastSelectedPosition) {
            final AccountsOverview accountsOverview = overviewAdapter.getItem(mLastSelectedPosition);
            if (accountsOverview != null) {
                value = getString(accountsOverview.overviewType.getAccountTypeId());
                return value;
            }
        }
        return value;
    }

    @Override
    public String getOverviewAccountType(OverviewType overviewType, String accountName, String accountCode, boolean isPocketAccount) {
        String value = null;
        try {
            if (overviewType == OverviewType.REWARDS) {
                if (!TextUtils.isEmpty(accountName)) {
                    value = accountName;
                }
            } else {
                if (overviewType == OverviewType.EVERYDAY_BANKING && isPocketAccount) {
                    value = getString(za.co.nedbank.services.R.string.my_savings_pockets);
                } else if (!TextUtils.isEmpty(accountCode)) {
                    int val = mPresenter.fetchAccountType(accountCode);
                    if (val != -1) {
                        value = getString(val);
                    }
                }
            }
        } catch (Exception e) {
            NBLogger.d(TAG, e.getMessage());
        }
        return value;
    }

    void onAVOLifestyleViewMoreClick() {
        if (getNBActivity() != null && getNBActivity().isDemoMode()) {
            mPresenter.moveToAvoDemoSplashScreen();
        } else {
            if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_LIFESTYLE) && mMediaCardViewModel != null)
                mPresenter.navigateToAvoLifestyleApp(mMediaCardViewModel);
        }
    }

    @Override
    public void openAvoInWebBrowser(String url) {
        if (mActivity != null) {
            IntentUtils.openDefaultBrowser(mActivity, url);
        }
    }

    @Override
    public void openAVODeepLinkScreen(String deeplink) {
        if (mActivity instanceof HomeActivity) {
            mApplicationStorage.putString(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE, deeplink);
            ((HomeActivity) mActivity).checkAndShowFeatureDeepLinkScreen();
        }
    }

    @Override
    public String getErrorScreenTitle(){
        return getString(R.string.pay_mybills_deeplink_error_title);
    }

    @Override
    public String getErrorScreenDescription(){
        return getString(R.string.pay_mybills_deeplink_error_description);
    }

    @Override
    public String getErrorScreenButtonTitle(){
        return getString(R.string.pay_mybills_deeplink_error_button);
    }

    @Override
    public void updateDynamicAvoBanner(MediaCardViewModel mediaCardViewModel) {
        if (mediaCardViewModel != null && mediaCardViewModel.getMediaUrl() != null) {
            mMediaCardViewModel = mediaCardViewModel;
            ViewUtils.showViews(binding.llAvoLifestyleBannerView.llAvoLifestyle);
            String imageUrl = mediaCardViewModel.getMediaUrl();
            if (!isValidWebUrl(imageUrl)) {
                if (imageUrl.contains(SUB_URL)) {
                    imageUrl = BuildConfig.BASE_IMAGE_URL + imageUrl;
                } else {
                    imageUrl = FULL_IMAGE_URL + imageUrl;
                }
            }
            if (mActivity != null && !isDetached()) {
                PicassoUtil.get(mActivity).load(imageUrl)
                        .priority(Picasso.Priority.HIGH)
                        .placeholder(R.drawable.gray_color_fill)
                        .error(R.drawable.ic_avo_banner_available_wrapper)
                        .transform(cropPosterTransformation)
                        .into(binding.llAvoLifestyleBannerView.avoAppLogo);
            }
        } else {
            updateLocalAvoBanner();
        }
    }

    @Override
    public void updateLocalAvoBanner() {
        ViewUtils.hideViews(binding.llAvoLifestyleBannerView.llAvoLifestyle);
    }

    @Override
    public void loadBackgroundImage() {
        mPresenter.loadBackgroundImage();
    }

    @Override
    public void setOverviewTitleFor(OverviewType overviewType) {
        switch (overviewType) {
            case FINANCIAL_WELLNESS:
                binding.tvOverviewTitle.setText(getString(R.string.account_type_financial_wellness));
                break;
            case LIFESTYLE:
                binding.tvOverviewTitle.setText(getString(R.string.overview_lifestyle));
                break;
            case REWARDS:
                binding.tvOverviewTitle.setText(getString(R.string.overview_my_rewards));
                break;
            case CREDIT_CARDS:
                binding.tvOverviewTitle.setText(getString(R.string.account_type_credit_cards));
                break;
            case LOANS:
                binding.tvOverviewTitle.setText(getString(R.string.account_type_loan));
                break;
            case INVESTMENTS:
                binding.tvOverviewTitle.setText(getString(R.string.account_type_investments));
                break;
            case FOREIGN_CURRENCY_ACCOUNT:
                binding.tvOverviewTitle.setText(getString(R.string.overview_international_banking_title));
                break;
            case CLUB_ACCOUNTS:
                binding.tvOverviewTitle.setText(getString(R.string.overview_club_accounts_title));
                break;
            case INSURANCE:
                binding.tvOverviewTitle.setText(getString(R.string.overview_insurance));
                break;
            case MERCHANT_SERVICES:
                binding.tvOverviewTitle.setText(getString(R.string.overview_merchant));
                break;
            default:
                binding.tvOverviewTitle.setText(getString(R.string.overview_my_accounts));
                break;
        }
    }

    @Override
    public void onServerStateChanged(boolean chatConnected) {
        isChatConnected = chatConnected;

    }

    @Override
    public void showShapIDIcon() {
        addWidgets(borrowWidgetIconNotificationCount.getValue() > 0);
    }

    @Override
    public void showWidgetProgress(boolean visible) {
        binding.widgetProgress.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
        setEnabledActivityTouch(!visible);
    }

    @Override
    public String getActivityLabel() {
        return getString(R.string.data_usage_name_avo);
    }

    @Override
    public boolean isDashboardAdamOfferVisibleFirstTimeAfterLogin() {
        if (getActivity() instanceof HomeActivity) {
            return  ((HomeActivity) getActivity()).isDashboardAdamOfferVisibleFirstTimeAfterLogin();
        } else {
            return false;
        }
    }

    @Override
    public void setDashboardAdamOfferVisibleFirstTimeAfterLogin(boolean value) {
        if (getActivity() instanceof HomeActivity) {
            ((HomeActivity) getActivity()).setDashboardAdamOfferVisibleFirstTimeAfterLogin(value);
        }
    }

    @Override
    public void showInsuranceOffersCount(List<InsGetOfferItemViewModel> insuranceOfferList) {
        this.insuranceOfferCounts = CollectionUtils.isNotEmpty(insuranceOfferList) ? insuranceOfferList.size() : Constants.ZERO;
        addWidgets(borrowWidgetIconNotificationCount.getValue() > 0);
    }

    @Override
    public String getUserTypeString(boolean isBusinessUser) {
        return isBusinessUser ? getString(R.string.client_type_juristic) : getString(R.string.client_type_individual);
    }

    @Override
    public boolean isAccessibilityEnabled() {
        AccessibilityManager am = (AccessibilityManager) getContext().getSystemService(ACCESSIBILITY_SERVICE);
        return !am.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_SPOKEN).isEmpty();
    }

    private Transformation cropPosterTransformation = new Transformation() {
        int actualImageWidth = 0;
        int targetHeight = 0;

        @Override
        public Bitmap transform(Bitmap source) {
            if (mActivity != null && !isDetached()) {
                actualImageWidth = (DeviceUtils.getDeviceWidth(mActivity) - (2 * mActivity.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_20dp)));
                double aspectRatio = (double) source.getHeight() / (double) source.getWidth();
                targetHeight = (int) (actualImageWidth * aspectRatio) - mActivity.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_10dp);
            }
            Bitmap result = Bitmap.createScaledBitmap(source, actualImageWidth, targetHeight, false);
            if (result != source) {
                source.recycle();
            }
            return result;
        }

        @Override
        public String key() {
            return "cropPosterTransformation" + actualImageWidth;
        }
    };

    public void handleFicaResponse(int feature, FicaSDKViewModel ficaSDKViewModel) {
        if (mPresenter != null) {
            mPresenter.handleFicaResponse(feature, ficaSDKViewModel);
        }
    }

    public void onBackNavigation() {
        if (binding.toolbarViewPager != null) {
            if (binding.toolbarViewPager.getCurrentItem() == 0) {
                binding.toolbarViewPager.setCurrentItem(binding.toolbarViewPager.getAdapter().getCount() - 1);
            } else {
                binding.toolbarViewPager.setCurrentItem(binding.toolbarViewPager.getCurrentItem() - 1);
            }
        }
    }

    public void onNextNavigation() {
        if (binding.toolbarViewPager != null) {
            binding.toolbarViewPager.setCurrentItem((binding.toolbarViewPager.getCurrentItem() + 1) % binding.toolbarViewPager.getAdapter().getCount());
        }
    }
}
