package za.co.nedbank.ui.view.retention;

import android.util.Log;

import java.util.HashMap;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.enrol.LoginSecurityUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.CheckIfUserAdminUseCase;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.tracking.appsflyer.AddContextData;
import za.co.nedbank.core.tracking.appsflyer.AppsFlyerTags;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.profile.view.navigation.ProfileNavigationTarget;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class RetentionTaskSelectionPresenter extends NBBasePresenter<RetentionTaskSelectionView> {

    private final NavigationRouter navigationRouter;
    private final LoginSecurityUseCase loginSecurityUseCase;
    private final CheckIfUserAdminUseCase checkIfUserAdminUseCase;
    private final ApplicationStorage applicationStorage;
    private final Analytics analytics;
    private final AFAnalyticsTracker mAfAnalyticsTracker;

    @Inject
    RetentionTaskSelectionPresenter(NavigationRouter navigationRouter, final LoginSecurityUseCase loginSecurityUseCase,
                                    final CheckIfUserAdminUseCase checkIfUserAdminUseCase, final ApplicationStorage applicationStorage,
                                    final Analytics analytics,
                                    final AFAnalyticsTracker afAnalyticsTracker) {
        this.navigationRouter = navigationRouter;
        this.loginSecurityUseCase = loginSecurityUseCase;
        this.checkIfUserAdminUseCase = checkIfUserAdminUseCase;
        this.applicationStorage = applicationStorage;
        this.analytics = analytics;
        this.mAfAnalyticsTracker = afAnalyticsTracker;
    }

    @Override
    protected void onBind() {
        super.onBind();
        sendPageEvent();
    }

    void handleRetentionShareAccountFlow() {

        analytics.sendEvent(AppTracking.RETENTION_CARD_SHARE_ACCOUNT_INFO, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, true);

        navigateToMultipleAccount(Boolean.FALSE);
    }

    void handleRetentionLoginSecurityFlow() {

        analytics.sendEvent(AppTracking.RETENTION_CARD_LOGIN_AND_SECURITY, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, true);
        loginSecurityUseCase.execute(Boolean.FALSE)
                .compose(bindToLifecycle()).subscribe(o -> {
        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    void handleRetentionProfileLimitFlow() {

        analytics.sendEvent(AppTracking.RETENTION_CARD_PROFILE_LIMITS, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, true);
        checkIfUserAdminUseCase.execute().subscribe(isAdminUser -> {
            if (isBusinessUser() && !isAdminUser)
                navigationRouter.navigateTo(NavigationTarget.to(ProfileNavigationTarget.PROFILE_LIMITS_BUSINESS_USER)
                        .withParam(za.co.nedbank.core.Constants.KEY_FROM_PROFILE_LIMIT, Boolean.TRUE));
            else

                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.PROFILE_LIMITS));
        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public boolean isBusinessUser() {
        int clientType = applicationStorage.getInteger(za.co.nedbank.core.Constants.KEY_USER_CLIENT_TYPE, za.co.nedbank.core.Constants.ZERO);
        return (clientType > 30);
    }

    public void handleCloseButtonClick() {

        analytics.sendEvent(AppTracking.RETENTION_CLICK_TASK_SELECTION_CLOSE, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withIntentFlagClearTopSingleTop(true));
    }

    public void sendPageEvent() {
        analytics.sendState(AppTracking.RETENTION_SCREEN_TASK_SELECTION);
    }

    public void sendBackArrowAnalytics() {
        analytics.sendEvent(AppTracking.RETENTION_CLICK_TASK_SELECTION_BACK, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);

    }

    public void handleDebitOrderRetentionFlow() {

        analytics.sendEvent(AppTracking.RETENTION_CARD_DEBIT_ORDER, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);

        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, true);
        navigateToMultipleAccount(Boolean.TRUE);
    }

    private void navigateToMultipleAccount(boolean isDebitOrderStarted){
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RETENTION_MULTIPLE_SHARE_ACCOUNT_ACTIVITY);
        navigationTarget.withParam(NavigationTarget.IS_IN_RETENTION_DEBIT_ORDER_FLOW, isDebitOrderStarted);
        navigationRouter.navigateWithResult(navigationTarget).subscribe(
                navigationResult -> {
                    if (null != navigationResult && navigationResult.isOk()) {

                        view.showError();
                    }
                }
                , throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
        );
    }

    void trackActivationJourneyOptionsOnAppsFlyer(String featureSelected) {
        HashMap<String, Object> eventMap = new HashMap<>();
        AddContextData addContextData = new AddContextData(eventMap);
        addContextData.setFeatureSelected(featureSelected);

        mAfAnalyticsTracker.sendEvent(AppsFlyerTags.AF_ACTIVATION_ON_LOGON, eventMap);
    }

}
