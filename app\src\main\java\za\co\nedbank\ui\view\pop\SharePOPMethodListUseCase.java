/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.pop;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/2019.
 */

public class SharePOPMethodListUseCase extends UseCase<Boolean, List<String>> {

    @Inject
    public SharePOPMethodListUseCase(UseCaseComposer useCaseComposer) {
        super(useCaseComposer);
    }

    @Override
    protected Observable<List<String>> createUseCaseObservable(Boolean isFromPayments) {
        return Observable.just(getSharePOPMethodList());
    }

    private List<String> getSharePOPMethodList() {
        List<String> sharePOPMethodList = new ArrayList<>();
        sharePOPMethodList.add(Constants.SharePOPMethod.EMAIL);
        sharePOPMethodList.add(Constants.SharePOPMethod.SMS);
        return sharePOPMethodList;
    }
}
