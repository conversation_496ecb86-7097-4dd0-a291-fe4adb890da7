package za.co.nedbank.ui.view.home.non_tp_client.non_tp_dashboard;

import static za.co.nedbank.core.Constants.FULL_IMAGE_URL;
import static za.co.nedbank.core.Constants.SUB_URL;
import static za.co.nedbank.core.utils.AppUtility.isValidWebUrl;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.SectionIndexer;

import com.squareup.picasso.Picasso;
import com.squareup.picasso.Transformation;

import org.zakariya.stickyheaders.SectioningAdapter;

import java.util.ArrayList;
import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.BuildConfig;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.viewholder.SectionedListSectionViewHolder;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.PicassoUtil;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.databinding.ViewNonTpAccountsFooterBinding;
import za.co.nedbank.databinding.ViewNonTpAccountsHeaderBinding;
import za.co.nedbank.databinding.ViewNonTpAccountsItemBinding;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;

public class NonTpAccountsAdapter extends SectioningAdapter implements SectionIndexer {
    private final List<AccountsOverview> mNonTpAccounts = new ArrayList<>();
    private ISectionedListItemSelectedListener mItemSelectedListener;
    private AvoBannerClickListener mAvoBannerClickListener;
    private boolean mShowAvoBanner;
    private Context mContext;
    private ApplicationStorage mApplicationStorage;
    private MediaCardViewModel mMediaCardViewModel;
    private FeatureSetController featureSetController;

    public NonTpAccountsAdapter(Context context, ISectionedListItemSelectedListener itemSelectedListener,
                                AvoBannerClickListener avoBannerClickListener, ApplicationStorage applicationStorage,
                                boolean showAvoBanner, MediaCardViewModel mediaCardViewModel,FeatureSetController featureSetController) {
        mContext = context;
        mItemSelectedListener = itemSelectedListener;
        this.mApplicationStorage = applicationStorage;
        this.mAvoBannerClickListener = avoBannerClickListener;
        this.mShowAvoBanner = showAvoBanner;
        this.mMediaCardViewModel = mediaCardViewModel;
        this.featureSetController = featureSetController;
    }

    public void setMediaCardViewModel(MediaCardViewModel mediaCardViewModel) {
        this.mMediaCardViewModel = mediaCardViewModel;
    }

    public void swapData(List<AccountsOverview> nonTpAccountsOverviews) {
        this.mNonTpAccounts.clear();
        if (nonTpAccountsOverviews != null) {
            this.mNonTpAccounts.addAll(nonTpAccountsOverviews);
        }
    }

    @Override
    public Object[] getSections() {
        return new Object[0];
    }

    @Override
    public int getPositionForSection(int i) {
        return -1;
    }

    @Override
    public int getSectionForPosition(int i) {
        return 0;
    }


    @Override
    public int getNumberOfSections() {
        return mNonTpAccounts.size();
    }

    @Override
    public int getNumberOfItemsInSection(int sectionIndex) {
        if (mNonTpAccounts != null && mNonTpAccounts.size() > 0
                && mNonTpAccounts.get(sectionIndex) != null
                && mNonTpAccounts.get(sectionIndex).accountSummaries != null) {
            return mNonTpAccounts.get(sectionIndex).accountSummaries.size();
        } else {
            return 0;
        }
    }

    @Override
    public GhostHeaderViewHolder onCreateGhostHeaderViewHolder(ViewGroup parent) {
        final View ghostView = new View(parent.getContext());
        ghostView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));

        return new GhostHeaderViewHolder(ghostView);
    }


    @Override
    public boolean doesSectionHaveHeader(int sectionIndex) {
        if (sectionIndex > mNonTpAccounts.size() - 1)
            return false;
        return !TextUtils.isEmpty(mContext.getResources().getString(mNonTpAccounts.get(sectionIndex).overviewType.getAccountTypeId()));
    }

    @Override
    public boolean doesSectionHaveFooter(int sectionIndex) {
        return sectionIndex == mNonTpAccounts.size() - 1;
    }

    @Override
    public HeaderViewHolder onCreateHeaderViewHolder(ViewGroup parent, int headerUserType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        ViewNonTpAccountsHeaderBinding binding = ViewNonTpAccountsHeaderBinding.inflate(inflater, parent, false);
        return new SectionedListSectionViewHolder(binding, binding.sectionCellText);
    }

    @Override
    public ItemViewHolder onCreateItemViewHolder(ViewGroup parent, int itemType) {
        ViewNonTpAccountsItemBinding binding = ViewNonTpAccountsItemBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new NonTpAccountsItemViewHolder(mContext, binding, mItemSelectedListener, mApplicationStorage,featureSetController);
    }

    @Override
    public FooterViewHolder onCreateFooterViewHolder(ViewGroup parent, int footerUserType) {
        ViewNonTpAccountsFooterBinding binding = ViewNonTpAccountsFooterBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new NonTpAccountsFooterHolder(binding);
    }

    @Override
    public void onBindHeaderViewHolder(HeaderViewHolder viewHolder, int sectionIndex, int headerUserType) {
        super.onBindHeaderViewHolder(viewHolder, sectionIndex, headerUserType);
        ((SectionedListSectionViewHolder) viewHolder).setSectionLabel(mContext.getResources().getString(mNonTpAccounts.get(sectionIndex).overviewType.getAccountTypeId()));
    }

    @Override
    public void onBindItemViewHolder(ItemViewHolder viewHolder, int sectionIndex, int itemIndex, int itemType) {
        AccountsOverview accountsOverview = mNonTpAccounts.get(sectionIndex);
        List<AccountSummary> accountSummaries = accountsOverview.accountSummaries;
        if (itemIndex < accountSummaries.size()) {
            AccountSummary accountSummary = accountSummaries.get(itemIndex);
            ((NonTpAccountsItemViewHolder) viewHolder).setSectionPosition(sectionIndex);
            ((NonTpAccountsItemViewHolder) viewHolder).setChildPosition(itemIndex);
            ((NonTpAccountsItemViewHolder) viewHolder).setup(accountSummary);
        }
    }

    @Override
    public void onBindFooterViewHolder(FooterViewHolder viewHolder, int sectionIndex, int footerUserType) {
        super.onBindFooterViewHolder(viewHolder, sectionIndex, footerUserType);
        ((NonTpAccountsFooterHolder) viewHolder).updateDynamicAvoBanner(mMediaCardViewModel);
    }

    public List<AccountsOverview> getNonTpAccounts() {
        return mNonTpAccounts;
    }

    class NonTpAccountsFooterHolder extends SectioningAdapter.FooterViewHolder {
        ImageView mAvoAppBannerView;

        NonTpAccountsFooterHolder(ViewNonTpAccountsFooterBinding binding) {
            super(binding.getRoot());
            mAvoAppBannerView = binding.llAvoLifestyleBannerView.avoAppLogo;
            setUpShimmerView();
            if (mShowAvoBanner) {
                updateDynamicAvoBanner(mMediaCardViewModel);
            } else {
                mAvoAppBannerView.setImageResource(R.drawable.ic_avo_banner_unavailable_wrapper);
            }
            binding.llAvoLifestyleBannerView.avoAppLogo.setOnClickListener(v -> onClick());
        }

        void onClick() {
            if (mAvoBannerClickListener != null)
                mAvoBannerClickListener.onBannerClick();
        }

        private void setUpShimmerView() {
            int targetWidth = (DeviceUtils.getDeviceWidth((Activity) mContext) - (2 * mContext.getResources()
                    .getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_20dp)));
            int targetHeight = (int) (mContext.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_70dp));
            ViewUtils.setHeightAsPerAspectRatio(mAvoAppBannerView, targetWidth,
                    targetHeight, DeviceUtils.getDeviceWidth((Activity) mContext));
        }

        public void updateDynamicAvoBanner(MediaCardViewModel mediaCardViewModel) {
            if (mediaCardViewModel != null && mediaCardViewModel.getMediaUrl() != null) {
                mMediaCardViewModel = mediaCardViewModel;
                String imageUrl = mediaCardViewModel.getMediaUrl();
                if (!isValidWebUrl(imageUrl)) {
                    if (imageUrl.contains(SUB_URL)) {
                        imageUrl = BuildConfig.BASE_IMAGE_URL + imageUrl;
                    } else {
                        imageUrl = FULL_IMAGE_URL + imageUrl;
                    }
                }
                PicassoUtil.get(mContext).load(imageUrl)
                        .priority(Picasso.Priority.HIGH)
                        .placeholder(R.drawable.gray_color_fill)
                        .error(R.drawable.ic_avo_banner_available_wrapper)
                        .transform(cropPosterTransformation)
                        .into(mAvoAppBannerView);
            } else {
                updateLocalAvoBanner();
            }
        }

        private Transformation cropPosterTransformation = new Transformation() {
            int actualImageWidth = 0;

            @Override
            public Bitmap transform(Bitmap source) {
                actualImageWidth = (DeviceUtils.getDeviceWidth((Activity) mContext) - (2 * mContext.getResources()
                        .getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_20dp)));
                double aspectRatio = (double) source.getHeight() / (double) source.getWidth();
                int targetHeight = (int) (actualImageWidth * aspectRatio) - mContext.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_10dp);
                Bitmap result = Bitmap.createScaledBitmap(source, actualImageWidth, targetHeight, false);
                if (result != source) {
                    source.recycle();
                }
                return result;
            }

            @Override
            public String key() {
                return "cropPosterTransformation" + actualImageWidth;
            }
        };

        public void updateLocalAvoBanner() {
            mAvoAppBannerView.setImageResource(R.drawable.ic_avo_banner_available_wrapper);
        }
    }
}
