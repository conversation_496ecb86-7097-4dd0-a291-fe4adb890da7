package za.co.nedbank.ui.view.home.non_tp_client.non_tp_dashboard;

import static android.content.Context.ACCESSIBILITY_SERVICE;
import static za.co.nedbank.core.Constants.FLOW_CONSTANTS.WITHOUT_NED_ID_APPLY_BORROW_PL_FLOW;
import static za.co.nedbank.core.Constants.FULL_IMAGE_URL;
import static za.co.nedbank.core.Constants.SUB_URL;
import static za.co.nedbank.core.utils.AppUtility.isValidWebUrl;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.snackbar.BaseTransientBottomBar;
import com.google.android.material.snackbar.Snackbar;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Transformation;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.BuildConfig;
import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.common.CardType;
import za.co.nedbank.core.common.FeatureCardEnum;
import za.co.nedbank.core.common.ProductType;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.convochatbot.view.ChatbotConstants;
import za.co.nedbank.core.dashboard.HomeWidget;
import za.co.nedbank.core.dashboard.WidgetData;
import za.co.nedbank.core.databinding.LayoutOverlayBackgroundBinding;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.enumerations.BackgroundImageTypeEnum;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.sharedui.listener.CircularViewPagerHandler;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.ImageUtils;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.NBAnimationUtils;
import za.co.nedbank.core.utils.PicassoUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.media_content.MediaContentAdapter;
import za.co.nedbank.core.view.media_content.MediaContentClickListener;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.core.view.model.media_content.ProductOfferViewModel;
import za.co.nedbank.core.view.model.media_content.ProductStatus;
import za.co.nedbank.databinding.FragmentNonTpOverviewBinding;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.view.overview.AccountTypeRowInterface;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.HomeActivity;
import za.co.nedbank.ui.view.home.overview.OverviewToolbarPagerAdapter;
import za.co.nedbank.ui.view.home.overview.dashboard.HomeWidgetAdapter;
import za.co.nedbank.ui.view.home.overview.dashboard.WidgetDataInterface;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class NonTPAccountsFragment extends NBBaseFragment implements
        NonTPAccountsView, AccountTypeRowInterface, WidgetDataInterface, MediaContentClickListener {

    private Activity mActivity;
    private Overview mOverview;
    private boolean isChatConnected;
    private boolean isUnreadChatAvailable;
    private boolean hasTransactableAccount;
    private boolean mIsEyeIconClicked = false;
    private static final int DEFAULT_PAGE = 0;
    private int mLastSelectedPosition = DEFAULT_PAGE;

    @Inject
    ApplicationStorage mApplicationStorage;
    @Inject
    FeatureSetController mFeatureSetController;
    @Inject
    protected NonTpAccountsPresenter mPresenter;
    private MediaCardViewModel mMediaCardViewModel;
    private UserDetailViewModel userDetailViewModel;
    private HomeWidgetAdapter mHomeWidgetAdapter;
    private OverviewToolbarPagerAdapter overviewAdapter;
    private FragmentNonTpOverviewBinding binding;
    private LayoutOverlayBackgroundBinding overlayBackgroundBinding;
    private BackgroundImageTypeEnum mSelectedImageType = BackgroundImageTypeEnum.DEFAULT;

    public static NonTPAccountsFragment getInstance() {
        return new NonTPAccountsFragment();
    }

    @Override
    public void onCreate(@Nullable final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
        mPresenter.bind(this);
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        this.mActivity = activity;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mPresenter.unbind();
    }

    @Override
    public View onCreateView(@NonNull final LayoutInflater inflater, final ViewGroup container, final Bundle savedInstanceState) {
        binding = FragmentNonTpOverviewBinding.inflate(inflater, container, false);
        overlayBackgroundBinding = LayoutOverlayBackgroundBinding.bind(binding.getRoot());
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setProgressBarUI();
        initWidgetAdapter();
        setCarousalViewPager();
        setEyeIconUI();
        setClickListeners();
        setUpShimmerView();
        initProductOffers();
    }

    private void setUpShimmerView() {
        int targetWidth = (DeviceUtils.getDeviceWidth(mActivity) - (2 * mActivity.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_20dp)));
        int targetHeight = mActivity.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_70dp);
        ViewUtils.setHeightAsPerAspectRatio(binding.llAvoLifestyleBannerView.avoAppLogo, targetWidth,
                targetHeight, DeviceUtils.getDeviceWidth(mActivity));
    }

    private void setEyeIconUI() {
        binding.eyeIcon.setChecked(mPresenter.isBalanceHidden());
        if (binding.eyeIcon.isChecked()) {
            binding.eyeIcon.setContentDescription(getString(R.string.show_balances));
        } else {
            binding.eyeIcon.setContentDescription(getString(R.string.hide_balances));
        }
    }

    private void setCarousalViewPager() {
        overviewAdapter = new OverviewToolbarPagerAdapter((NBBaseActivity) mActivity, this, mSelectedImageType);
        binding.toolbarViewPager.setAdapter(overviewAdapter);
        binding.toolbarViewPager.addOnPageChangeListener(new CircularViewPagerHandler(binding.toolbarViewPager) {
            @Override
            protected void selectedPage(int position) {
                onPageChange(position);
            }
        });
    }

    private void setProgressBarUI() {
        if (getContext() != null) {
            binding.toolbarViewProgress.getIndeterminateDrawable().setColorFilter(ContextCompat.getColor(getContext(), R.color.white), PorterDuff.Mode.SRC_IN);
            binding.toolbarViewReloading.getIndeterminateDrawable().setColorFilter(ContextCompat.getColor(getContext(), R.color.white), PorterDuff.Mode.SRC_IN);
        }
    }

    private void setClickListeners() {
        binding.eyeIcon.setOnClickListener(v -> onEyeIconClick());
        binding.ivNextNav.setOnClickListener(v -> onNextNavigation());
        binding.ivBackNav.setOnClickListener(v -> onBackNavigation());
        binding.nonTpChatIcon.setOnClickListener(v -> onChatIconClick());
        binding.nonTpLotteBellView.setOnClickListener(v -> onClickNotificationCount());
        binding.nonTpBellIcon.setOnClickListener(v -> onClickNotificationCount());
        binding.llAvoLifestyleBannerView.avoAppLogo.setOnClickListener(v -> onAvoBannerClick());
    }

    private void initWidgetAdapter() {
        if(mPresenter.isNonTpWidgetFeatureEnabled()) {
            mHomeWidgetAdapter = new HomeWidgetAdapter(getContext(), this, false, mApplicationStorage);
            addWidgets();
            int span = DeviceUtils.getDeviceDensityNumber(getResources()).endsWith(DeviceUtils.HIGH) ? 4 : 3;
            GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), span);
            binding.widgetRecycler.setLayoutManager(gridLayoutManager);
            binding.widgetRecycler.setAdapter(mHomeWidgetAdapter);
            binding.llWidgets.setVisibility(View.VISIBLE);
        }
    }

    private void addWidgets() {
        HomeWidget[] homeWidgets = HomeWidget.class.getEnumConstants();
        if (homeWidgets != null && homeWidgets.length > 0) {
            List<WidgetData> widgetDataList = new ArrayList<>();
            for (HomeWidget homeWidget : homeWidgets) {
                if (homeWidget != null && homeWidget.isWidgetShow()) {
                    //As a Minor LoggedIn then add only QUICK_PAY widget... other wise add above listed widget.
                    if (homeWidget.getWidgetName() == HomeWidget.SHOP.getWidgetName()) {
                        WidgetData widgetData = new WidgetData();
                        widgetData.setWidgetName(mActivity.getResources().getString(homeWidget.getWidgetName()));
                        widgetData.setWidgetResource(homeWidget.getWidgetResource());
                        widgetData.setWidgetActionId(homeWidget.getActionId());
                        widgetData.setBgColor(homeWidget.getBgColor());
                        widgetData.setTextColor(homeWidget.getTextColor());
                        widgetDataList.add(widgetData);
                    }
                }
            }
            mHomeWidgetAdapter.swapData(widgetDataList);
        }
    }

    public void onBackNavigation() {
        if (binding.toolbarViewPager.getCurrentItem() == 0) {
            binding.toolbarViewPager.setCurrentItem(binding.toolbarViewPager.getAdapter().getCount() - 1);
        } else {
            binding.toolbarViewPager.setCurrentItem(binding.toolbarViewPager.getCurrentItem() - 1);
        }
    }

    public void onNextNavigation() {
        binding.toolbarViewPager.setCurrentItem((binding.toolbarViewPager.getCurrentItem() + 1) % binding.toolbarViewPager.getAdapter().getCount());
    }

    public void onPageChange(final int position) {
        mLastSelectedPosition = position;
        final AccountsOverview accountsOverview = overviewAdapter.getItem(position);
        if (accountsOverview != null) {
            setOverviewTitleFor(accountsOverview.overviewType);
            OverviewType overviewType = accountsOverview.overviewType;
            setEyeIconVisibility(overviewType);
        }
        // accessibility
        AccessibilityManager am = (AccessibilityManager) getContext().getSystemService(ACCESSIBILITY_SERVICE);

        if (am.isEnabled()) {

            if (accountsOverview != null && accountsOverview.overviewType != null) {
                setViewPagerContentDescription(accountsOverview.overviewType, accountsOverview);
            }
            AccessibilityEvent event = AccessibilityEvent.obtain(AccessibilityEvent.TYPE_VIEW_CLICKED);
            ViewParent parent = binding.toolbarViewPager.getParent();
            if (parent != null) {
                parent.requestSendAccessibilityEvent(binding.toolbarViewPager, event);
            }
            binding.toolbarViewPager.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED);
        }
        binding.nsOverview.smoothScrollTo(0,0);
    }

    @Override
    public void onActivityCreated(@Nullable final Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        binding.nonTpTitle.setVisibility(View.INVISIBLE);
        if (!isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS)
                || !isFeatureDisabled(FeatureConstants.PRE_APPROVED_ACCOUNTS)) {
            ViewUtils.showViews(binding.nonTpBellIcon);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mMediaCardViewModel == null) {
            mPresenter.loadMediaContent();
        }
        mPresenter.loadPreferredUserName();
        mPresenter.loadBackgroundImage();
        mPresenter.getTotalBankingUnreadMessages();
        boolean ftrNewToFranchiseUser = mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_NEW_TO_FRANCHISE_USER);
        if (!ftrNewToFranchiseUser
                && mPresenter.getClientType() == ClientType.SALES) {
            ViewUtils.showViews(binding.salesAccountsView.rlSalesAccountsTab);
        } else {
            ViewUtils.hideViews(binding.salesAccountsView.rlSalesAccountsTab);
            mPresenter.loadAccountsData();
        }
    }

    @Override
    public boolean isFeatureDisabled(String feature) {
        return mFeatureSetController.isFeatureDisabled(feature);
    }

    @Override
    public boolean canTransact() {
        return this.hasTransactableAccount;
    }

    @Override
    public void hasTransactableAccount(boolean isTransactable) {
        this.hasTransactableAccount = isTransactable;
    }

    @Override
    public boolean isSAResident() {
        return getActivity() != null && (getActivity() instanceof HomeActivity) && ((HomeActivity) getActivity()).isSAResident();
    }

    @Override
    public void showAccountsLoading(boolean loading) {
        binding.toolbarViewProgress.setVisibility(loading ? View.VISIBLE : View.INVISIBLE);
    }

    @Override
    public void handlePreferredNameError() {
        mPresenter.fetchUserDetail(Boolean.TRUE);
    }

    @Override
    public void showAccountsLoadingError(String msg) {
        if (isAdded() && getContext() != null) {
            showError(getContext().getString(R.string.non_tp_accounts_loading_error_title), msg);
        }
    }

    @Override
    public String getFicaStatus() {
        return userDetailViewModel == null ? StringUtils.EMPTY_STRING : userDetailViewModel.getFicaStatus();
    }

    @Override
    public void setUserInfo(UserDetailViewModel userDetailViewModel, boolean updateCustomerName) {
        this.userDetailViewModel = userDetailViewModel;
        if (!TextUtils.isEmpty(userDetailViewModel.getClientType())) {
            mApplicationStorage.putInteger(Constants.KEY_USER_CLIENT_TYPE, Integer.parseInt(userDetailViewModel.getClientType()));
        }
        if (userDetailViewModel != null && updateCustomerName) {
            setCustomerName(userDetailViewModel.getFullNames());
        }
    }

    @Override
    public void showError(String message) {
        showError(getString(R.string.error), message);
    }

    @Override
    public void setPreferredName(String name) {
        if (name == null || name.trim().isEmpty()) {
            mPresenter.loadCustomerNAme();
        } else {
            binding.nonTpTitle.setText(name.trim());
        }
        binding.nonTpTitle.setVisibility(View.VISIBLE);
    }

    @Override
    public void setCustomerName(String customerUserName) {
        if (StringUtils.isNullOrEmpty(customerUserName.trim())) {
            binding.nonTpTitle.setText(StringUtils.EMPTY_STRING);
        } else {
            mApplicationStorage.putString(
                    Constants.KEY_USER_CLIENT_NAME, customerUserName.trim());
            binding.nonTpTitle.setText(StringUtils.removeTitles(customerUserName.trim()));
        }
        binding.nonTpTitle.setVisibility(View.VISIBLE);
    }

    @Override
    public void refreshBalances() {
        if (mOverview != null) {
            setNonTpAccounts(mOverview, true);
        }
    }

    @Override
    public void setNonTpAccounts(Overview overview, boolean isReloading) {
        if (overview != null && overview.accountsOverviews != null && !overview.accountsOverviews.isEmpty()) {
            this.mOverview = overview;
            binding.toolbarViewPagerDots.setupWithViewPager(binding.toolbarViewPager);
            overviewAdapter = new OverviewToolbarPagerAdapter((NBBaseActivity) mActivity, this, mSelectedImageType);
            overviewAdapter.swapData(overview.accountsOverviews);
            binding.toolbarViewPager.setOffscreenPageLimit(overview.accountsOverviews.size());
            ViewUtils.showViews(binding.ivNextNav, binding.ivBackNav, binding.toolbarViewPagerDots);
            if(!mIsEyeIconClicked) {
                mLastSelectedPosition = DEFAULT_PAGE;
            }
            binding.toolbarViewPager.setAdapter(overviewAdapter);
            binding.toolbarViewPager.setCurrentItem(mLastSelectedPosition);

            //Forcing to land on the Everyday Banking carousel (instead of Financial Wellness carousel)
            if (!mIsEyeIconClicked && mPresenter.isFinancialWellnessAvailable()
                    && mLastSelectedPosition == DEFAULT_PAGE
                    && mPresenter.isLandOnFinancialWellnessCarousel()) {
                mLastSelectedPosition = mPresenter.getOverviewPosition(overview.accountsOverviews, OverviewType.LOANS);
            }
            mIsEyeIconClicked = false;
            updateViewPagerToLastSelectedPage();

            // set overview title as per selected overview account type
            if (overview.accountsOverviews != null && overview.accountsOverviews.size() > mLastSelectedPosition) {
                setEyeIconVisibility(overview.accountsOverviews.get(mLastSelectedPosition).overviewType);
                setOverviewTitleFor(overview.accountsOverviews.get(mLastSelectedPosition).overviewType);
                if (overview.accountsOverviews.get(mLastSelectedPosition).overviewType == OverviewType.FINANCIAL_WELLNESS) {
                    mPresenter.trackFinancialWellnessPageLoad();
                }
            }
        }
    }

    public void setOverviewTitleFor(OverviewType overviewType) {
        switch (overviewType) {
            case FINANCIAL_WELLNESS:
                binding.tvOverviewTitle.setText(getString(R.string.account_type_financial_wellness));
                break;
            case LIFESTYLE:
                binding.tvOverviewTitle.setText(getString(R.string.overview_lifestyle));
                break;
            case REWARDS:
                binding.tvOverviewTitle.setText(getString(R.string.overview_my_rewards));
                break;
            case CREDIT_CARDS:
                binding.tvOverviewTitle.setText(getString(R.string.account_type_credit_cards));
                break;
            case LOANS:
                binding.tvOverviewTitle.setText(getString(R.string.account_type_loan));
                break;
            case INVESTMENTS:
                binding.tvOverviewTitle.setText(getString(R.string.account_type_investments));
                break;
            case FOREIGN_CURRENCY_ACCOUNT:
                binding.tvOverviewTitle.setText(getString(R.string.overview_international_banking_title));
                break;
            case CLUB_ACCOUNTS:
                binding.tvOverviewTitle.setText(getString(R.string.overview_club_accounts_title));
                break;
            case INSURANCE:
                binding.tvOverviewTitle.setText(getString(R.string.overview_insurance));
                break;
            case MERCHANT_SERVICES:
                binding.tvOverviewTitle.setText(getString(R.string.overview_merchant));
                break;
            default:
                binding.tvOverviewTitle.setText(getString(R.string.overview_my_accounts));
                break;
        }
    }

    private void setViewPagerContentDescription(@NonNull OverviewType overviewType, @NonNull AccountsOverview accountsOverview) {
        switch (overviewType) {
            case EVERYDAY_BANKING:
                binding.toolbarViewPager.setContentDescription(String.format(getString(za.co.nedbank.services.R.string.overview_account_detail_acc), accountsOverview.summaryValue1, accountsOverview.summaryValue2));
                break;
            case CREDIT_CARDS:
                binding.toolbarViewPager.setContentDescription(String.format(getString(za.co.nedbank.services.R.string.overview_credit_card_detail_acc), accountsOverview.summaryValue1, accountsOverview.summaryValue2));
                break;
            case INVESTMENTS:
                binding.toolbarViewPager.setContentDescription(String.format(getString(za.co.nedbank.services.R.string.overview_investment_detail_acc), accountsOverview.summaryValue1, accountsOverview.summaryValue2));
                break;
            case REWARDS:
                binding.toolbarViewPager.setContentDescription(String.format(getString(za.co.nedbank.services.R.string.overview_greenbacks_detail_acc), accountsOverview.summaryValue1, accountsOverview.summaryValue2));
                break;
            case INSURANCE:
                binding.toolbarViewPager.setContentDescription(getString(za.co.nedbank.services.R.string.insurance));
                break;
            case FINANCIAL_WELLNESS:
                binding.toolbarViewPager.setContentDescription(getString(R.string.overview_financial_wellness));
                break;
            case FOREIGN_CURRENCY_ACCOUNT:
                binding.toolbarViewPager.setContentDescription(getString(R.string.overview_international_banking_title));
                break;
            case LOANS:
                binding.toolbarViewPager.setContentDescription(getString(R.string.account_type_loan));
                break;
            case LIFESTYLE:
                binding.toolbarViewPager.setContentDescription(getString(R.string.overview_lifestyle));
                break;
            case CLUB_ACCOUNTS:
                binding.toolbarViewPager.setContentDescription(getString(R.string.overview_club_accounts_title));
                break;
            case MERCHANT_SERVICES:
                binding.toolbarViewPager.setContentDescription(getString(R.string.overview_merchant_services_title));
                break;
            default:
                //do nothing
        }
    }

    private void setEyeIconVisibility(OverviewType overviewType) {
        if (overviewType == OverviewType.EVERYDAY_BANKING
                || overviewType == OverviewType.CREDIT_CARDS
                || overviewType == OverviewType.LOANS
                || overviewType == OverviewType.INVESTMENTS
                || overviewType == OverviewType.REWARDS) {
            binding.eyeIcon.setVisibility(View.VISIBLE);
        } else {
            binding.eyeIcon.setVisibility(View.GONE);
        }
    }

    private void onEyeIconClick() {
        mIsEyeIconClicked = true;
        mPresenter.saveShowHideBalance();

        String msg;
        if (binding.eyeIcon.isChecked()) {
            msg = getString(R.string.balances_hidden);
            mPresenter.trackEyeIconAnalytics(AppTracking.MY_ACCNT_BAL_INFO_HIDE);
        } else {
            msg = getString(R.string.balances_shown);
            mPresenter.trackEyeIconAnalytics(AppTracking.MY_ACCNT_BAL_INFO_SHOW);
        }
        Snackbar.make(binding.getRoot(), msg, BaseTransientBottomBar.LENGTH_INDEFINITE)
                .setDuration(5000)
                .setActionTextColor(ContextCompat.getColor(getContext(), R.color.snackbar_action_title))
                .setAction(getString(R.string.undo), v -> handleUndoAction())
                .show();
    }

    private void handleUndoAction() {
        mIsEyeIconClicked = true;
        binding.eyeIcon.setChecked(!binding.eyeIcon.isChecked());
        mPresenter.saveShowHideBalance();
        mPresenter.trackEyeIconAnalytics(AppTracking.MY_ACCNT_BAL_INFO_UNDO);
    }

    private void updateViewPagerToLastSelectedPage() {
        if (binding.toolbarViewPager.getAdapter() != null
                && binding.toolbarViewPager.getAdapter().getCount() > mLastSelectedPosition) {
            binding.toolbarViewPager.setCurrentItem(mLastSelectedPosition, false);
        }
    }

    private boolean showAvoBanner() {
        //Show AVO Lifestyle banner if toggle is turned on.
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_LIFESTYLE)
                && !mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_LIFESTYLE_NON_TP);
    }

    @Override
    public String getBirthDate() {
        return getActivity() == null ? null : ((HomeActivity) getActivity()).getBirthDate();
    }

    @Override
    public void receiveNotificationCount(int notificationCount) {

        if (notificationCount > 0) {
            ViewUtils.hideViews(binding.nonTpBellIcon);
            ViewUtils.showViews(binding.nonTpLotteBellView);
            binding.nonTpLotteBellView.setRepeatCount(2);
            binding.nonTpLotteBellView.playAnimation();
        } else {
            ViewUtils.hideViews(binding.nonTpLotteBellView);
            ViewUtils.showViews(binding.nonTpBellIcon);
        }
    }

    @Override
    public void setChatIcon(int totalUnreadMessageCount) {
        binding.nonTpChatIcon.setImageResource(totalUnreadMessageCount > 0 ?
                R.drawable.ic_chat_white_notification_wrapper : R.drawable.ic_chat_icon_white_wrapper);
    }

    @Override
    public void onUnreadChatEvent(UnreadChatEvent unreadChatEvent) {
        isUnreadChatAvailable=unreadChatEvent.isUnreadChat();
        binding.nonTpChatIcon.setImageResource(unreadChatEvent.isUnreadChat() ?
                R.drawable.ic_chat_white_notification_wrapper : R.drawable.ic_chat_icon_white_wrapper);
    }

    @Override
    public void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent) {
        binding.nonTpChatIcon.setImageResource(unreadChatEvent.isUnreadLifestyleChat() ?
                R.drawable.ic_chat_white_notification_wrapper : R.drawable.ic_chat_icon_white_wrapper);
    }

    public void onChatIconClick() {

        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_FORCE_UPDATE)) {
            mPresenter.handleUpdateApp(getString(R.string.app_update_available_message_chatbot),
                    getString(R.string.app_update_available_title_chatbot));
        } else if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_CHATBOT)) {
            handleEnbiFlow();
        } else {
            handleNormalChatFlow();
        }
    }

    public void onClickNotificationCount() {
        mPresenter.handleClickNotificationCount();
        mPresenter.trackActionMyAccountProductGroup(AppTracking.MY_ACCOUNTS_NOTIFICATIONS);
    }

    @Override
    public String getOverviewProductGroup(OverviewType overviewType) {
        if (overviewType != null) {
            return getString(overviewType.getAccountTypeId());
        }
        return null;
    }

    @Override
    public String getOverviewAccountType(OverviewType overviewType, String accountName, String accountCode, boolean isPocketAccount) {
        String value = null;
        try {
            if (overviewType == OverviewType.REWARDS) {
                if (!TextUtils.isEmpty(accountName)) {
                    value = accountName;
                }
            } else {
                if (overviewType == OverviewType.EVERYDAY_BANKING && isPocketAccount) {
                    value = getString(za.co.nedbank.services.R.string.my_savings_pockets);
                } else if (!TextUtils.isEmpty(accountCode)) {
                    int val = mPresenter.fetchAccountType(accountCode);
                    if (val != -1) {
                        value = getString(val);
                    }
                }
            }
        } catch (Exception ignored) {
            //Nothing to do
        }
        return value;
    }

    @Override
    public void shouldShowOverlay(boolean showOverlay) {
        overlayBackgroundBinding.overlayImage.setVisibility(showOverlay ? View.VISIBLE : View.GONE);
    }

    @Override
    public void setBackgroundImage(String imagePath) {
        overlayBackgroundBinding.backgroundImage.setBackgroundResource(0);
        if (mActivity != null) {
            ImageUtils.setImage(mActivity, imagePath, overlayBackgroundBinding.backgroundImage);
        }
    }

    @Override
    public void setBackgroundImage(int imageId) {
        overlayBackgroundBinding.backgroundImage.setVisibility(View.INVISIBLE);
        overlayBackgroundBinding.backgroundImage.setImageBitmap(null);
        overlayBackgroundBinding.backgroundImage.setBackgroundResource(imageId);
        NBAnimationUtils.fadeIn(overlayBackgroundBinding.backgroundImage);
    }

    @Override
    public void loadOverview(BackgroundImageTypeEnum backgroundImageType) {
        mSelectedImageType = backgroundImageType;
    }

    public void onAvoBannerClick() {
        if (showAvoBanner())
            mPresenter.navigateToAvoLifestyleApp();
    }

    @Override
    public void startBrowser(String url) {
        IntentUtils.openDefaultBrowser(getActivity(), url);
    }

    @Override
    public void updateMediaCardViewModel(MediaCardViewModel mediaCardViewModel) {
        mMediaCardViewModel = mediaCardViewModel;
        updateDynamicAvoBanner(mMediaCardViewModel);
    }

    private void initProductOffers() {
        List<AppLayoutViewModel> mProductList = new ArrayList<>();
        mProductList.add(getProductCard());
        binding.rvAppbarContentHome.setHasFixedSize(true);
        binding.rvAppbarContentHome.setLayoutManager(new LinearLayoutManager(getActivity()));
        binding.rvAppbarContentHome.setItemAnimator(null);
        MediaContentAdapter mMediaContentAdapter = new MediaContentAdapter(mActivity, this, mProductList, DeviceUtils.getDeviceWidth(getActivity()),
                mPresenter.isDynamicContentToggleEnabled(), false, mFeatureSetController);
        binding.rvAppbarContentHome.setAdapter(mMediaContentAdapter);
    }

    private AppLayoutViewModel getProductCard(){
        AppLayoutViewModel appLayoutProductCard = new AppLayoutViewModel();
        appLayoutProductCard.setCardType(CardType.PRODUCT_OFFER);
        ProductOfferViewModel productViewModel = new ProductOfferViewModel();

        ProductStatus mInvisibleState = new ProductStatus();
        mInvisibleState.setVisible(false);

        ProductStatus mActiveState = new ProductStatus();
        productViewModel.setBank(mActiveState);
        productViewModel.setLoan(mActiveState);
        productViewModel.setInvestment(mActiveState);

        productViewModel.setFinancial(mInvisibleState);
        productViewModel.setForex(mInvisibleState);
        productViewModel.setInsurance(mInvisibleState);
        productViewModel.setSma(mInvisibleState);
        appLayoutProductCard.setProductOffer(productViewModel);
        return appLayoutProductCard;
    }

    @Override
    public void updateDynamicAvoBanner(MediaCardViewModel mediaCardViewModel) {
        if (mediaCardViewModel != null && mediaCardViewModel.getMediaUrl() != null) {
            mMediaCardViewModel = mediaCardViewModel;
            ViewUtils.showViews(binding.llAvoLifestyleBannerView.llAvoLifestyle);
            String imageUrl = mediaCardViewModel.getMediaUrl();
            if (!isValidWebUrl(imageUrl)) {
                if (imageUrl.contains(SUB_URL)) {
                    imageUrl = BuildConfig.BASE_IMAGE_URL + imageUrl;
                } else {
                    imageUrl = FULL_IMAGE_URL + imageUrl;
                }
            }
            if (mActivity != null && !isDetached()) {
                PicassoUtil.get(mActivity).load(imageUrl)
                        .priority(Picasso.Priority.HIGH)
                        .placeholder(R.drawable.gray_color_fill)
                        .error(R.drawable.ic_avo_banner_available_wrapper)
                        .transform(cropPosterTransformation)
                        .into(binding.llAvoLifestyleBannerView.avoAppLogo);
            }
        } else {
            updateLocalAvoBanner();
        }
    }

    @Override
    public void updateLocalAvoBanner() {
        binding.llAvoLifestyleBannerView.avoAppLogo.setImageResource(R.drawable.ic_avo_banner_available_wrapper);
    }

    private final Transformation cropPosterTransformation = new Transformation() {
        int actualImageWidth = 0;
        int targetHeight = 0;

        @Override
        public Bitmap transform(Bitmap source) {
            if (mActivity != null && !isDetached()) {
                actualImageWidth = (DeviceUtils.getDeviceWidth(mActivity) - (2 * mActivity.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_20dp)));
                double aspectRatio = (double) source.getHeight() / (double) source.getWidth();
                targetHeight = (int) (actualImageWidth * aspectRatio) - mActivity.getResources().getDimensionPixelSize(za.co.nedbank.core.R.dimen.dimen_10dp);
            }
            Bitmap result = Bitmap.createScaledBitmap(source, actualImageWidth, targetHeight, false);
            if (result != source) {
                source.recycle();
            }
            return result;
        }

        @Override
        public String key() {
            return "cropPosterTransformation" + actualImageWidth;
        }
    };

    @Override
    public void onServerStateChanged(boolean chatConnected) {
        isChatConnected=chatConnected;
    }

    private void handleEnbiFlow() {
        if (isChatConnected || isUnreadChatAvailable) {
            mPresenter.navigateToChatActivity();
        } else if (!mPresenter.isBusinessUser()
                || !mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_CHATBOT_SBS)) {
            handleEnbiFlowFirstTimeAndReturningUsers();
        } else {
            handleNormalChatFlow();
        }
    }

    private void handleNormalChatFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.APP_CHAT)) {
            mPresenter.navigateToChatActivity();
            mPresenter.trackChat();
        } else{
            mPresenter.navigateToChatErrorActivity();
        }
    }

    private void handleEnbiFlowFirstTimeAndReturningUsers() {
        if (isChatbotIntoJourneyCompleted())
            mPresenter.navigateToChatBotActivity(AppTracking.NON_TP_ACCOUNT_SCREEN_LOAD);
        else
            mPresenter.navigateToChatBotIntroductionActivity();
    }

    public boolean isChatbotIntoJourneyCompleted() {
        return mApplicationStorage.getBoolean(ChatbotConstants.StorageKeys.CHATBOT_INTRO_DISPLAYED, false);
    }

    @Override
    public void onAccountClick(AccountSummary accountSummary) {
        mPresenter.accountTypeClicked(accountSummary);
    }

    @Override
    public void onJoinButtonClick(AccountSummary accountSummary, OverviewType overviewType) {
        // Nothing to be done
    }

    @Override
    public String getActivityLabel() {
        return getString(R.string.data_usage_name_avo);
    }

    @Override
    public void widgetClicked(WidgetData widgetData) {
        if (widgetData.getWidgetActionId() == HomeWidget.ACTION_SHOP) {
            if (getNBActivity() != null && getNBActivity().isDemoMode()) {
                mPresenter.moveToAvoDemoSplashScreen();
            } else {
                mPresenter.trackActionForShopWidget();
                mPresenter.shopDashborad();
            }
        }
    }

    @Override
    public void showEmptyUserView(String msg) {
        // Nothing to be done
    }

    @Override
    public void onClickMediaContentItem(MediaCardViewModel mediaCardViewModel, int position) {
        // Not required to handle click on media item.
    }

    @Override
    public void onClickProductOfferItem(ProductType productType) {
        if(ProductType.INVESTMENT == productType){
            mPresenter.navigateToProductCardDetail(getProductCard());
        }else if (ProductType.BANK == productType) {
            mPresenter.navigateToBankLayout();
        } else if (ProductType.LOAN == productType) {
            mPresenter.onClickApplyLoan(WITHOUT_NED_ID_APPLY_BORROW_PL_FLOW, true);
        }
    }

    @Override
    public void onClickFeatureCardItem(FeatureCardEnum featureCardEnum) {
        // Not required to handle click on featured item.
    }

    @Override
    public void onClickFeatureCardItem(MediaCardViewModel featureCardItemViewModel) {
        // Not required to handle click on feature item.
    }
}