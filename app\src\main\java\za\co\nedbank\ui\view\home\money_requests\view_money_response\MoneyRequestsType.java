/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import za.co.nedbank.R;


/**
 * Created by swapnil.gawande on 2/25/2018.
 */


@IntDef({MoneyRequestsType.PENDING_REQUESTS, MoneyRequestsType.PAID_REQUESTS, MoneyRequestsType.REJECTED_REQUESTS})
@Retention(RetentionPolicy.RUNTIME)
public @interface MoneyRequestsType {
    int PENDING_REQUESTS = R.string.view_money_requests_pending_requests;
    int PAID_REQUESTS = R.string.view_money_requests_paid_requests;
    int REJECTED_REQUESTS = R.string.view_money_requests_rejected_requests;
}
