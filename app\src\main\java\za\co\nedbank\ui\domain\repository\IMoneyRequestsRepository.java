/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository;

import io.reactivex.Observable;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsMainResponseEntity;

/**
 * Created by swapnil.gawande on 2/23/2018.
 */

public interface IMoneyRequestsRepository {
    Observable<MoneyRequestsMainResponseEntity> getMoneyRequestList(String requesterRole);
}