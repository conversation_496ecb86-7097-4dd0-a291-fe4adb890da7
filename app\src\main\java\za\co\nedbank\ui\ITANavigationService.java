package za.co.nedbank.ui;


import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.Nullable;

import com.entersekt.sdk.Auth;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.AuthManager;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.ita.ITAFlowActivity;


public class ITANavigationService extends Service {


    private static final String TAG = "ITANavigationService";
    @Inject
    @Named("memory")
    ApplicationStorage memoryApplicationStorage;

    @Inject
    ApplicationStorage applicationStorage;

    @Inject
    APIInformation apiInformation;

    @Inject
    NotificationManager notificationManager;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        AppDI.getServiceComponent(this).inject(this);

    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        try {
            notificationManager.cancel(0);
            switch (intent.getAction()) {
                case NotificationConstants.ACTIONS.DEFAULT_NOTIFICATION_NAVIGATION:
                case NotificationConstants.ACTIONS.VIEW:
                    navigateToITAFlow();
                    break;
                case NotificationConstants.ACTIONS.DISMISS:
                    break;
                default:
                    stopSelf(startId);
            }
        } catch (Exception e) {
            NBLogger.d(TAG, e.getMessage());
        }
        stopSelf(startId);
        return super.onStartCommand(intent, flags, startId);
    }

    private void navigateToITAFlow() {
        Auth auth = AuthManager.getInstance().getAuth();
        boolean navigateToITA = true;
        if (auth != null) {
            navigateToITA = !memoryApplicationStorage.getBoolean(auth.getId(), false);
        }
        if (navigateToITA) {
            Intent targetIntent = new Intent(this, ITAFlowActivity.class);
            targetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(targetIntent);
        }
    }
}

