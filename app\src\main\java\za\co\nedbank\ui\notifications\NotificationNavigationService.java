package za.co.nedbank.ui.notifications;

import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.IBinder;

import androidx.annotation.Nullable;

import java.util.ArrayList;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.core.notification.mapper.NotificationDataToViewModelMapper;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.enroll_v2.view.login.LoginActivity;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextDataViewModelMapper;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextFedarationDetailsViewModel;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.notification.NotificationCenterActivity;
import za.co.nedbank.ui.view.notification.contextswitch.ContextSwitchConfirmationActivity;
import za.co.nedbank.ui.view.notification.notification_details.NotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.notification_messages.NotificationMessagesActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.details.TransactionNotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.inbox.TransactionInboxActivity;

public class NotificationNavigationService extends Service {


    @Inject
    Analytics analytics;

    @Inject
    NotificationManager notificationManager;

    @Inject
    @Named("memory")
    ApplicationStorage memoryApplicationStorage;

    @Inject
    ApplicationStorage applicationStorage;

    @Inject
    APIInformation apiInformation;

    @Inject
    NotificationDataToViewModelMapper notificationDataToViewModelMapper;

    @Inject
    GetFedarationListUseCase mGetFedarationListUseCase;

    @Inject
    SwitchContextDataViewModelMapper mSwitchContextDataViewModelMapper;

    private ArrayList<Object> mIntents;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        AppDI.getServiceComponent(this).inject(this);

    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {

        try {
            if (intent != null) {
                Object notificationObject = intent.getSerializableExtra(NotificationConstants.EXTRA.NOTIFICATION_DATA);
                if (notificationObject instanceof NotificationData) {

                    NotificationData notificationData = (NotificationData) notificationObject;
                    notificationManager.cancel(notificationData.getNotificationId());
                    notificationData.setSelectedAction(intent.getAction());

                    switch (intent.getAction()) {
                        case NotificationConstants.ACTIONS.DEFAULT_NOTIFICATION_NAVIGATION:
                        case NotificationConstants.ACTIONS.VIEW:
                            FBNotificationsViewModel notificationViewModel = notificationDataToViewModelMapper.transform(notificationData);
                            handleDefaultNavigation(notificationViewModel);
                            break;
                        case NotificationConstants.ACTIONS.DISMISS:
                            handleDismissAction();
                            break;
                        default:
                            stopSelf(startId);
                    }
                }
            }
        } catch (Exception e) {
            NBLogger.e("Exception:","NotificationNavigationService",e);
        }
        stopSelf(startId);
        return super.onStartCommand(intent, flags, startId);
    }

    //this method can be used in future to handle notification dismissal from the notification tray
    private void handleDismissAction() {
        // No need to handle dismiss as of now
    }


    private void handleDefaultNavigation(FBNotificationsViewModel notificationsViewModel) {

        final PackageManager packageManager = getPackageManager();
        final Intent appLaunchIntent = packageManager.getLaunchIntentForPackage(getPackageName());
        mIntents = new ArrayList<>();

        if (isLoggedIn()) {
            mIntents.add(appLaunchIntent);
            if (isContextSwitchNeeded(notificationsViewModel)) {
                getContextSwitchModel(Long.parseLong(notificationsViewModel.getClient().getCisNo()));
            } else {
                handleNonContextSwitchHandling(notificationsViewModel);
            }

        } else {

            //app not logged in
            if (notificationsViewModel.getNotificationType().equalsIgnoreCase(NotificationConstants.NOTIFICATION_TYPES.TRANSACTION) || notificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.UBC) || notificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.TBC) || notificationsViewModel.getNotificationType().equalsIgnoreCase(NotificationConstants.NOTIFICATION_TYPES.INFO) || notificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.ECERT) || Boolean.TRUE.equals(notificationsViewModel.getAllowAnonymous())) {
                Intent targetIntent = new Intent(this, TransactionNotificationDetailsActivity.class);
                targetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                targetIntent.putExtra(NavigationTarget.PARAM_TRANS_PUSH_DATA, notificationsViewModel);
                mIntents.add(targetIntent);

            } else {
                mIntents.add(appLaunchIntent);
                //if login screen is already in stack open it with clear top
                boolean canNavigateToLogin = memoryApplicationStorage.getBoolean(NotificationConstants.STORAGE_KEYS.CAN_NAVIGATE_TO_LOGIN, false);
                if (canNavigateToLogin) {
                    Intent targetIntent = new Intent(this, LoginActivity.class);
                    targetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    mIntents.add(targetIntent);
                }
            }

            startActivities(mIntents.toArray(new Intent[]{}));
        }
        //saving notification data in memory to be retrieved in home screen for further navigation
        memoryApplicationStorage.putObject(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL, notificationsViewModel);
        NBLogger.e("NotificationService", notificationsViewModel.toString() + "");

    }

    private void handleNonContextSwitchHandling(FBNotificationsViewModel notificationsViewModel) {
        //adding notification center screen
        Intent notificationCenterIntent = new Intent(this, NotificationCenterActivity.class);
        notificationCenterIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        notificationCenterIntent.putExtra(Constants.BUNDLE_KEYS.FLOW_JOURNEY_FLAG, Constants.FLOW_CONSTANTS.IN_APP_NOTIFICATION_FLOW);
        mIntents.add(notificationCenterIntent);

        if (notificationsViewModel.getNotificationType().equals(NotificationConstants.NOTIFICATION_TYPES.ITA)) {
            // Nothing to do here as ITA pushed from Application class
        } else if (!notificationsViewModel.getNotificationType().equals(NotificationConstants.NOTIFICATION_TYPES.TRANSACTION)) {
            //adding notification messages screen
            Intent notificationMessagesIntent = new Intent(this, NotificationMessagesActivity.class);
            notificationMessagesIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            mIntents.add(notificationMessagesIntent);
            //adding notification details screen
            Intent targetIntent = new Intent(this, NotificationDetailsActivity.class);
            targetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            targetIntent.putExtra(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL, notificationsViewModel);
            mIntents.add(targetIntent);
        } else {
            Intent transactionInboxIntent = new Intent(this, TransactionInboxActivity.class);
            transactionInboxIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            transactionInboxIntent.putExtra(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL, notificationsViewModel);
            mIntents.add(transactionInboxIntent);
        }
        startActivities(mIntents.toArray(new Intent[]{}));
    }

    private void getContextSwitchModel(long cis) {
        mGetFedarationListUseCase.execute().subscribe(fedarationList -> {
            ArrayList<SwitchContextFedarationDetailsViewModel> fedarationViewModels =
                    (ArrayList<SwitchContextFedarationDetailsViewModel>) mSwitchContextDataViewModelMapper
                            .mapFedarationDetailResponse(fedarationList);

            for (SwitchContextFedarationDetailsViewModel model : fedarationViewModels) {
                if (cis == model.getEnterpriseCustomerNumber()) {
                    Intent confirmationIntent = new Intent(this, ContextSwitchConfirmationActivity.class);
                    confirmationIntent.putExtra(NotificationConstants.EXTRA.CONTEXT_SWITCH_MODEL, model);
                    mIntents.add(confirmationIntent);
                    startActivities(mIntents.toArray(new Intent[]{}));
                }
            }

        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }


    private boolean isLoggedIn() {
        NBLogger.e("NotificationService", apiInformation.isLoggedOut() + "");
        return !apiInformation.isLoggedOut();
    }

    private boolean isContextSwitchNeeded(FBNotificationsViewModel fbNotificationsViewModel) {
        if (fbNotificationsViewModel.getClient() != null && !StringUtils.isNullOrEmpty(fbNotificationsViewModel.getClient().getCisNo()) && !(fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.UBC) || fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.TBC) || fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.ECERT))) {
            return !fbNotificationsViewModel.getClient().getCisNo().equals(memoryApplicationStorage.getString(StorageKeys.CIS_NUMBER, StringUtils.EMPTY_STRING));
        }
        return false;
    }
}
