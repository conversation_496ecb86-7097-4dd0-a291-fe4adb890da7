package za.co.nedbank.ui.view.home.close_account_tab;

import static za.co.nedbank.payment.Constants.SUCCESS;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.errors.HttpStatus;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.domain.usecase.ClosedAccountUseCase;
import za.co.nedbank.ui.view.mapper.ClosedAccountResponseDataToViewModelMapper;
import za.co.nedbank.ui.view.model.ClosedAccount;
import za.co.nedbank.ui.view.model.ClosedAccountResponseViewModel;

public class CloseAccountPresenter extends NBBasePresenter<CloseAccountView> {

    private final NavigationRouter mNavigationRouter;
    private ClosedAccountUseCase mClosedAccountUseCase;
    private ClosedAccountResponseDataToViewModelMapper mClosedAccountResponseDataToViewModelMapper;
    private Analytics mAnalytics;
    private final ErrorHandler mErrorHandler;

    @Inject
    CloseAccountPresenter(final NavigationRouter navigationRouter
            , ClosedAccountUseCase closedAccountUseCase
            , ClosedAccountResponseDataToViewModelMapper closedAccountResponseDataToViewModelMapper
            , Analytics mAnalytics,ErrorHandler errorHandler) {
        this.mNavigationRouter = navigationRouter;
        this.mClosedAccountUseCase = closedAccountUseCase;
        this.mClosedAccountResponseDataToViewModelMapper = closedAccountResponseDataToViewModelMapper;
        this.mAnalytics = mAnalytics;
        this.mErrorHandler = errorHandler;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

    public void getClosedInvestmentAccounts() {
        if (view != null)
            view.showProgressBar(true);
        mClosedAccountUseCase.execute()
                .compose(bindToLifecycle())
                .map(mClosedAccountResponseDataToViewModelMapper::mapData)
                .subscribe(closedAccountResponseViewModel -> handleClosedAccountResponse(closedAccountResponseViewModel)
                        , throwable -> handleExceptionResponse(throwable));
    }

    private void handleExceptionResponse(Throwable throwable) {
            view.showProgressBar(false);
            Error error = mErrorHandler.getErrorMessage(throwable);
                if (error.getCode() == HttpStatus.NO_CONTENT) {
                    view.showEmptyPlaceHolder(true);
                } else {
                    view.showErrorMessage(throwable.getMessage());
                }
    }

    private void handleClosedAccountResponse(ClosedAccountResponseViewModel closedAccountResponseViewModel) {
        if (view != null) {
            view.showProgressBar(false);
            if (closedAccountResponseViewModel != null
                    && !closedAccountResponseViewModel.getMetaDataModel().getResultData().isEmpty()
                    && closedAccountResponseViewModel.getMetaDataModel().getResultData().get(0).getCode().equalsIgnoreCase(SUCCESS)) {
                view.showAccountsList(closedAccountResponseViewModel.getData());
            } else {
                    view.showEmptyPlaceHolder(true);
            }
        }
    }

    public void onClosedAccountsFragmentVisible() {
        mAnalytics.sendEvent(TrackingEvent.CLICK_CLOSED_TAX_CERT_TAB_TAXCERTINV, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    public void navigateToDownloadStatement(ClosedAccount closedAccount) {
        if (closedAccount != null) {
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.DOWNLOAD_STATEMENTS);
            navigationTarget.withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_NUMBER, closedAccount.getInvestorNumber())
                    .withParam(ServicesNavigationTarget.FROM_DASHBORD_INV_TAX_FLOW, true)
                    .withParam(ServicesNavigationTarget.FROM_DASHBORD_INV_ACTIVE_ACCOUNTS_TAX_FLOW, false)
                    .withParam(ServicesNavigationTarget.DOWNLOAD_STATEMENT_TYPE, Constants.StatementType.TAX_STATEMENT);
            mNavigationRouter.navigateTo(navigationTarget);
        }

    }
}