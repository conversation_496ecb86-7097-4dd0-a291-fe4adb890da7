/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.retention.feedback;


import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class RetentionFeedbackSuccessPresenter extends NBBasePresenter<RetentionFeedbackSuccessView> {

    private final NavigationRouter navigationRouter;
    private final Analytics analytics;

    @Inject
    public RetentionFeedbackSuccessPresenter(NavigationRouter navigationRouter,final Analytics analytics){
        this.navigationRouter = navigationRouter;
        this.analytics = analytics;
    }

    @Override
    protected void onBind() {
        super.onBind();
        sendPageEvent();

    }

    public void doneBtnClicked() {
        if (view != null) {
            if (analytics != null) {
                analytics.sendEvent(AppTracking.RETENTION_CLICK_THANK_YOU_DONE, StringUtils.EMPTY_STRING,StringUtils.EMPTY_STRING);
            }
            navigateToHome();
        }
    }
    private void navigateToHome() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME)
                .withParam(Constants.BUNDLE_KEYS.IS_RETENTION_FLOW, true)
                .withIntentFlagClearTopSingleTop(true));
    }

    private void sendPageEvent() {
        if (analytics != null) {
            analytics.sendState(AppTracking.RETENTION_SCREEN_THANK_YOU);
        }
    }
}
