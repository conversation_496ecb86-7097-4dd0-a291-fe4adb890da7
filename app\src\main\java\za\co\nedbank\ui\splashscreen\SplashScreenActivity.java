/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.splashscreen;

import static za.co.nedbank.core.Constants.APPS_FLYER_RETENTION_TAG;
import static za.co.nedbank.core.Constants.APPS_FLYER_TRUE;
import static za.co.nedbank.core.Constants.KEY_DEEP_LINK_CASE_ID;
import static za.co.nedbank.core.Constants.KEY_DEEP_LINK_PRODUCT_ID;
import static za.co.nedbank.core.Constants.KEY_DEEP_LINK_SUB_PRODUCT_ID;
import static za.co.nedbank.core.Constants.NEDBANK_NGC_SCHEME;
import static za.co.nedbank.core.Constants.RETENTION_DEEPLINK_HOST;
import static za.co.nedbank.core.Constants.RETENTION_DEEPLINK_SCHEME;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.DisplayMetrics;
import android.widget.RelativeLayout;

import com.adobe.marketing.mobile.Identity;
import com.adobe.marketing.mobile.MobileCore;
import com.appsflyer.AppsFlyerConversionListener;
import com.appsflyer.AppsFlyerLib;
import com.appsflyer.deeplink.DeepLink;
import com.appsflyer.deeplink.DeepLinkResult;
import com.entersekt.sdk.push.config.PushConfigType;
import com.google.android.gms.ads.identifier.AdvertisingIdClient;
import com.google.android.material.snackbar.BaseTransientBottomBar;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.FirebaseMessaging;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.inject.Inject;

import io.branch.referral.Branch;
import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.core.pinpoint.PinPointType;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.XmsApiCheck;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.databinding.ActivitySplashScreenBinding;
import za.co.nedbank.enroll_v2.Constants;
import za.co.nedbank.nid_sdk.main.interaction.TransaktConfig;
import za.co.nedbank.ui.NBApplication;
import za.co.nedbank.ui.app_shortcut.AppShortCutConstants;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;

public class SplashScreenActivity extends NBBaseActivity implements SplashScreenView {

    @Inject
    SplashScreenPresenter splashScreenPresenter;
    private boolean viewAnimated = false;
    private int deviceHeight;
    private String fbToken;
    private String hmsSenderId;
    private String hmsToken;
    public static final String LOG_TAG = SplashScreenActivity.class.getSimpleName();
    private ActivitySplashScreenBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getActivityComponent(this).inject(this);
        splashScreenPresenter.bind(this);
        splashScreenPresenter.disconnectEnterSktSDK();

            subscribeForDeepLink();
        splashScreenPresenter.trackAutoLogOut();

        if (XmsApiCheck.isHmsApiPreferred(this)) {
            splashScreenPresenter.getHuaweiConfig();
        } else {
            FirebaseMessaging.getInstance().getToken()
                    .addOnCompleteListener(task -> {
                        if (!task.isSuccessful()) {
                            NBLogger.e("Firebase", "getInstanceId failed", task.getException());
                            return;
                        }
                        NBLogger.e("token", task.getResult());
                        fbToken = task.getResult();
                        splashScreenPresenter.saveFBToken(fbToken);
                    });

        }

        getAdvertisingId();
        handleNotificationPayload();
        if (splashScreenPresenter.getAllowAnonymous()) {
            splashScreenPresenter.navigateToTransactionDetails();
            finish();
        }
        // solve issue with android launcher, on samsung devices sometimes on resuming app launcher
        // activity is launched instead of resuming app
        if (!isTaskRoot() && isLauncherIntent()) {
            // Activity was brought to front and not created,
            // Thus finishing this will get us to the last viewed activity
            finish();
            return;
        }

        binding = ActivitySplashScreenBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        disableLogout();

        DisplayMetrics displayMetrics = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        deviceHeight = displayMetrics.heightPixels;

        initViews();

        handleAJOData();
    }

    private void initViews() {
        binding.txvWelcom.setAlpha(Constants.SPLASH.MIN_ALPHA);
        updateTopMargin(deviceHeight / 2);
    }

    public void handleAJOData() {
        FirebaseMessaging.getInstance().getToken()
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        MobileCore.setPushIdentifier(task.getResult());
                        MobileCore.setMessagingDelegate(((NBApplication)getApplication()).getAjoInAppMessagingDelegate());
                        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
                    }
                });
    }

    private void handleNotificationPayload() {
        Bundle extras = getIntent().getExtras();
        if (extras != null && extras.containsKey(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL)) {
            FBNotificationsViewModel notificationViewModel = (FBNotificationsViewModel) extras.getSerializable(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL);
            splashScreenPresenter.handleNotificationPayload(notificationViewModel);
        } else if (extras != null && extras.containsKey(NotificationConstants.EXTRA.GOOGLE_MESSAGE_ID)
                && extras.containsKey(NotificationConstants.EXTRA.DATA_PAYLOAD)) {
            String dataPayload = extras.getString(NotificationConstants.EXTRA.DATA_PAYLOAD);
            NotificationData notificationData = NotificationUtils.extractNotificationData(dataPayload);
            splashScreenPresenter.handleNotificationPayload(notificationData);
        }
    }

    private boolean isLauncherIntent() {
        return getIntent().hasCategory(Intent.CATEGORY_LAUNCHER)
                && getIntent().getAction() != null
                && getIntent().getAction().equals(Intent.ACTION_MAIN);
    }

    @Override
    public void prepareAnimation() {
        if (viewAnimated) {
            return;
        }

        viewAnimated = true;

        binding.txvWelcom.animate().alpha(Constants.SPLASH.MAX_ALPHA).setDuration(Constants.SPLASH.ALPHA_ANIMATION_DURATION);

        binding.splashLoader.setSpeed(Constants.SPLASH.LOTTIE_SPEED);
        binding.splashLoader.playAnimation();

        ValueAnimator valueAnimator = ValueAnimator.ofInt(deviceHeight / 2, deviceHeight / 4);
        valueAnimator.setDuration(Constants.SPLASH.LOTTIE_ANIMATION_DURATION);
        valueAnimator.addUpdateListener(animation -> updateTopMargin((Integer) animation.getAnimatedValue()));
        valueAnimator.start();
    }


    @Override
    public TransaktConfig getTransaktConfig() {
        TransaktConfig transaktConfig = new TransaktConfig();
        if (XmsApiCheck.isHmsApiPreferred(this)) {
            if (hmsToken == null) hmsToken = splashScreenPresenter.getHMSToken();
            transaktConfig.setCloudMessagingToken(hmsToken);
            transaktConfig.setSenderId(hmsSenderId);
            transaktConfig.setPushConfigType(PushConfigType.HUAWEI_PUSH_KIT);
        } else {
            transaktConfig.setCloudMessagingToken(fbToken);
            transaktConfig.setSenderId(FirebaseOptions.fromResource(getApplicationContext()).getGcmSenderId());
            transaktConfig.setPushConfigType(PushConfigType.FIREBASE_CLOUD_MESSAGING);
        }
        NBLogger.e("huawei token", hmsToken);
        return transaktConfig;
    }

    @Override
    public void setHMSConfigValues(String hmsSenderId,String hmsToken) {
        this.hmsSenderId = hmsSenderId;
        this.hmsToken = hmsToken;
    }


    private void updateTopMargin(int topMargin) {
        RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) binding.txvWelcom.getLayoutParams();
        lp.setMargins(0, topMargin - R.dimen.dimen_40dp, 0, R.dimen.dimen_10dp);
        binding.txvWelcom.setLayoutParams(lp);
    }

    @Override
    protected void onStart() {
        super.onStart();
        splashScreenPresenter.checkPermissions();
        Branch.sessionBuilder(this).withCallback((referringParams, error) -> {
            if (error != null) {
                NBLogger.i("Branch.io", "Branch.io error : " + error.getMessage());
            } else {
                NBLogger.i("Branch.io - ", "Branch.io Referrer details - " + referringParams.toString());
            }
        }).withData(this.getIntent().getData()).init();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        this.setIntent(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        Identity.getExperienceCloudId(cloudId -> splashScreenPresenter.saveECID(cloudId));
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        if (splashScreenPresenter != null) {
            splashScreenPresenter.disposeAllObservers();
            splashScreenPresenter.unbind();
        }
        super.onDestroy();
    }

    @Override
    protected boolean canShowBlockedScreen() {
        return false;
    }


    @Override
    public boolean isComingFromRootedFlow() {
        return getIntent().getBooleanExtra(NavigationTarget.PARAM_IS_COMING_FROM_ROOTED_FLOW, false);
    }

    @Override
    public boolean isAutoLogout(){
        return getIntent().getBooleanExtra(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_AUTOLOGOUT, false);
    }

    @Override
    public boolean isShortcutScanPay() {
        boolean isOpenScanToPay = getIntent() != null && getIntent().getAction() != null && getIntent().getAction().equalsIgnoreCase(AppShortCutConstants.ACTION_SCAN_PAY);
        if (!isOpenScanToPay) {
            isOpenScanToPay = checkNedbankNGCSchema();
        }
        return isOpenScanToPay;
    }

    @Override
    public void showEntersektError() {
        showError(getString(R.string.error_something_wrong), getString(R.string.error_entersekt_initialization), getString(R.string.retry), BaseTransientBottomBar.LENGTH_INDEFINITE, () -> splashScreenPresenter.initializeEntersekt());
    }

    @Override
    public void setResult(Map<String, Object> params) {
        readResult(params);
    }

    private void readResult(Map<String, Object> resultMap) {
        if (null != resultMap && resultMap.size() > 0) {
            boolean isRooted = (Boolean) resultMap.get(NavigationTarget.IS_DEVICE_ROOTED_PARAM);
            boolean isShortcutScanPay = (Boolean) resultMap.get(NavigationTarget.IS_FROM_APP_SHORTCUT);
            boolean isSecondLogin = (Boolean) resultMap.get(NavigationTarget.IS_SECOND_LOGIN);
            splashScreenPresenter.performDeveloperOptionCheck(isRooted, isShortcutScanPay, isSecondLogin);
        }
    }

    private boolean checkNedbankNGCSchema() {
        boolean isScanNGCSchemaAvailable = false;
        String action = getIntent().getAction();
        Uri data;
        if (NEDBANK_NGC_SCHEME.equals(action)) {
            isScanNGCSchemaAvailable = true;
        } else if (action != null && Intent.ACTION_VIEW.equals(action)) {
            data = getIntent().getData();
            if (data != null && data.getScheme() != null && (data.getScheme().equalsIgnoreCase(NEDBANK_NGC_SCHEME))) {
                isScanNGCSchemaAvailable = true;
            }
        }
        return isScanNGCSchemaAvailable;
    }

    @Override
    public void checkAppsFlyerSMSDeeplink() {
        Uri data = getIntent().getData();
        if(data != null && data.getScheme() != null && data.getScheme().equals(RETENTION_DEEPLINK_SCHEME)
                && data.getHost() != null && data.getHost().equals(RETENTION_DEEPLINK_HOST)) {
            splashScreenPresenter.updateSMSRetentionDeepLinkFlag(Boolean.FALSE);
        }
        splashScreenPresenter.initializeEntersekt();
    }

    @Override
    public void initializePinPointSDK() {
        callPinPointSDK(PinPointType.INIT);
        initSurvey();
        getCallState();
    }

    void getAdvertisingId() {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Handler handler = new Handler(Looper.getMainLooper());

        executor.execute(() -> {
            try {
                AdvertisingIdClient.Info adInfo = AdvertisingIdClient.getAdvertisingIdInfo(SplashScreenActivity.this);
                String advertisingId = adInfo != null ? adInfo.getId() : null;
                handler.post(() -> splashScreenPresenter.updateAdvertisingId(advertisingId));
            } catch (Exception e) {
                splashScreenPresenter.updateAdvertisingId(StringUtils.EMPTY_STRING);
            }
        });

        executor.shutdown();
    }

    private void subscribeForDeepLink(){
        splashScreenPresenter.updateDeeplinkFeature(StringUtils.EMPTY_STRING, false, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        splashScreenPresenter.updateSMSRetentionDeepLinkFlag(Boolean.FALSE);
        AppsFlyerLib.getInstance().subscribeForDeepLink(deepLinkResult -> {
                    splashScreenPresenter.updateSMSRetentionDeepLinkFlag(Boolean.FALSE);
                    DeepLinkResult.Status dlStatus = deepLinkResult.getStatus();
                    if (dlStatus == DeepLinkResult.Status.NOT_FOUND || dlStatus == DeepLinkResult.Status.ERROR) {
                        return;
                    }
                    DeepLink deepLinkObj = deepLinkResult.getDeepLink();
                    if (Boolean.TRUE.equals(deepLinkObj.isDeferred())) {
                        return;
                    }
                    deeplinkFound(deepLinkObj);
                }
        );
        AppsFlyerLib.getInstance().registerConversionListener(this, new AppsFlyerConversionListener() {
            @Override
            public void onConversionDataSuccess(Map<String, Object> conversionDataMap) {
                if (conversionDataMap != null && !conversionDataMap.isEmpty()) {
                    String firstLaunch = StringUtils.EMPTY_STRING;
                    if (conversionDataMap.containsKey(za.co.nedbank.core.Constants.KEY_IS_FIRST_LAUNCH)) {
                        firstLaunch = (Objects.requireNonNull(conversionDataMap.get(za.co.nedbank.core.Constants.KEY_IS_FIRST_LAUNCH))).toString();
                    }

                    if (firstLaunch.equals("true") && conversionDataMap.containsKey(za.co.nedbank.core.Constants.KEY_DEEP_LINK_VALUE)) {
                        deeplinkFoundForFirstLaunch(conversionDataMap);
                    }
                }
            }

            @Override
            public void onConversionDataFail(String errorMessage) {
                NBLogger.d(LOG_TAG, "onConversionDataFail: " + errorMessage);
            }

            @Override
            public void onAppOpenAttribution(Map<String, String> map) {
                NBLogger.d(LOG_TAG, "onAppOpenAttribution");
            }

            @Override
            public void onAttributionFailure(String errorMessage) {
                NBLogger.d(LOG_TAG, "onAttributionFailure : " + errorMessage);
            }
        });
    }

    private void deeplinkFound(DeepLink deepLinkObj) {
        String featureName = StringUtils.EMPTY_STRING;
        String productId = StringUtils.EMPTY_STRING;
        String subProductId = StringUtils.EMPTY_STRING;
        String caseId = StringUtils.EMPTY_STRING;
        String retentionTag = StringUtils.EMPTY_STRING;

        try {
            featureName = deepLinkObj.getDeepLinkValue();
            productId = deepLinkObj.getStringValue(KEY_DEEP_LINK_PRODUCT_ID);
            subProductId = deepLinkObj.getStringValue(KEY_DEEP_LINK_SUB_PRODUCT_ID);
            caseId = deepLinkObj.getStringValue(KEY_DEEP_LINK_CASE_ID);
            if (deepLinkObj.getStringValue(APPS_FLYER_RETENTION_TAG) != null) {
                retentionTag = deepLinkObj.getStringValue(APPS_FLYER_RETENTION_TAG);
            }
        } catch (Exception e) {
            NBLogger.d(LOG_TAG, "Fetching Deeplink Param " + e.getMessage());
            return;
        }

        saveDeeplinkParams(retentionTag, featureName, productId, subProductId, caseId);
    }

    private void deeplinkFoundForFirstLaunch(Map<String, Object> conversionDataMap) {
        String featureName = (String) conversionDataMap.get(za.co.nedbank.core.Constants.KEY_DEEP_LINK_VALUE);
        String productId = StringUtils.EMPTY_STRING;
        String subProductId = StringUtils.EMPTY_STRING;
        String caseId = StringUtils.EMPTY_STRING;
        String retentionTag = StringUtils.EMPTY_STRING;

        if (conversionDataMap.containsKey(KEY_DEEP_LINK_PRODUCT_ID))
            productId = (String) conversionDataMap.get(KEY_DEEP_LINK_PRODUCT_ID);

        if (conversionDataMap.containsKey(KEY_DEEP_LINK_SUB_PRODUCT_ID))
            subProductId = (String) conversionDataMap.get(KEY_DEEP_LINK_SUB_PRODUCT_ID);

        if (conversionDataMap.containsKey(KEY_DEEP_LINK_CASE_ID))
            caseId = (String) conversionDataMap.get(KEY_DEEP_LINK_CASE_ID);

        if (conversionDataMap.containsKey(APPS_FLYER_RETENTION_TAG)) {
            retentionTag = (String) conversionDataMap.get(APPS_FLYER_RETENTION_TAG);
        }
        saveDeeplinkParams(retentionTag, featureName, productId, subProductId, caseId);
    }

    private void saveDeeplinkParams(String retentionTag, String featureName, String productId, String subProductId, String caseId) {
        if (APPS_FLYER_TRUE.equals(retentionTag)) {
            splashScreenPresenter.updateSMSRetentionDeepLinkFlag(Boolean.TRUE);
        } else {
            splashScreenPresenter.updateDeeplinkFeature(featureName, StringUtils.isNotEmpty(featureName), productId, subProductId, caseId);
        }
    }

    @Override
    public List<String> getInstalledAppsPackageName() {
        List<String> packageNameList = new ArrayList<>();
        List<PackageInfo> packList = getPackageManager().getInstalledPackages(0);
        for (int i = 0; i < packList.size(); i++) {
            PackageInfo packInfo = packList.get(i);
            if ((packInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                String packageName = packInfo.packageName;
                if (StringUtils.isNotEmpty(packageName))
                    packageNameList.add(packageName.trim());
            }
        }

        return packageNameList;
    }

    @Override
    public Context getActivityContext() {
        return this;
    }
}
