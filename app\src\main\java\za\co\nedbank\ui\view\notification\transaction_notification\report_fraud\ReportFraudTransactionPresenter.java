package za.co.nedbank.ui.view.notification.transaction_notification.report_fraud;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.ui.view.notification.transaction_notification.TransactionAnalytics;
import za.co.nedbank.ui.view.notification.transaction_notification.TransactionTrackingEvent;

public class ReportFraudTransactionPresenter extends NBBasePresenter<ReportFraudTransactionView> {

    private final NavigationRouter mNavigationRouter;
    private final TransactionAnalytics mAnalytics;

    @Inject
    public ReportFraudTransactionPresenter(NavigationRouter mNavigationRouter,
                                           TransactionAnalytics mAnalytics) {
        this.mNavigationRouter = mNavigationRouter;
        this.mAnalytics = mAnalytics;
    }

    void navigateToReportFraud(FBTransactionNotificationsViewModel transactionModel) {
        mAnalytics.sendEvent(TransactionTrackingEvent.ActionName.TRANSACTION_NOTIFICATION_REPORT_FRAUD_CONFIRM, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);

        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MORE_REPORT_FRAUD)
                .withParam(Constants.PARAM_REPORT_SUSPICIOUS, transactionModel)
                .withParam(NavigationTarget.IS_FROM_REPORT, true));
    }


    void handleCancelClick() {
        mAnalytics.sendEvent(TransactionTrackingEvent.ActionName.TRANSACTION_NOTIFICATION_REPORT_FRAUD_CANCEL, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }
}
