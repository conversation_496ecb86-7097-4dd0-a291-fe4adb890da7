package za.co.nedbank.ui.view.ita.qrcodelogin;

import android.os.Bundle;
import android.widget.ImageView;

import org.greenrobot.eventbus.EventBus;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.component.CompatButton;
import za.co.nedbank.uisdk.component.CompatTextView;

public class QrLoginResultActivity extends NBBaseActivity {

    private CompatTextView tvQrLoginTitle;
    private CompatTextView tvQrLoginMsg;
    private ImageView ivQrLoginStatus;
    private CompatButton btnPrimary;
    private CompatButton btnSecondary;
    private int qrLoginResult;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_qrlogin_result);
        initViews();
        qrLoginResult = IntentUtils.getIntegerValue(getIntent(), Constants.TYPE, Constants.QrLoginResult.FAILURE);
        updateView();
    }

    private void initViews() {
        tvQrLoginTitle = findViewById(R.id.tvQrLoginTitle);
        tvQrLoginMsg = findViewById(R.id.tvQrLoginMsg);
        ivQrLoginStatus = findViewById(R.id.ivQrLoginStatus);
        btnPrimary = findViewById(R.id.btnPrimary);
        btnSecondary = findViewById(R.id.btnSecondary);
        findViewById(R.id.btnPrimary).setOnClickListener(v -> close());
    }

    private void updateView() {
        ViewUtils.hideViews(btnSecondary);
        switch (qrLoginResult) {
            case Constants.QrLoginResult.SUCCESS:
                tvQrLoginTitle.setText(R.string.qrlogin_result_success_title);
                tvQrLoginMsg.setText(R.string.qrlogin_result_success_msg);
                ivQrLoginStatus.setImageResource(R.drawable.ic_ita_approve);
                btnPrimary.setText(getString(R.string.es_btn_Done));
                break;
            default:
            case Constants.QrLoginResult.FAILURE:
                tvQrLoginTitle.setText(R.string.qrlogin_result_failure_title);
                tvQrLoginMsg.setText(R.string.qrlogin_result_failure_msg);
                ivQrLoginStatus.setImageResource(R.drawable.ic_ita_decline);
                btnPrimary.setText(getString(R.string.es_btn_Done));
                break;
            case Constants.QrLoginResult.ERROR:
                tvQrLoginTitle.setText(R.string.qrlogin_result_error_title);
                tvQrLoginMsg.setText(R.string.qrlogin_result_error_msg);
                ivQrLoginStatus.setImageResource(R.drawable.ic_qrlogin_error);
                btnPrimary.setText(getString(R.string.qrlogin_btn_close));
                break;
            case Constants.QrLoginResult.UNTRUSTED_ERROR:
                tvQrLoginTitle.setText(R.string.qrlogin_result_untrusted_error_title);
                tvQrLoginMsg.setText(R.string.qrlogin_result_untrusted_error_msg);
                ivQrLoginStatus.setImageResource(R.drawable.ic_qrlogin_untrusted_error);
                btnPrimary.setText(getString(R.string.qrlogin_btn_link_device));
                btnSecondary.setText(getString(R.string.qrlogin_btn_cancel));
                ViewUtils.showViews(btnSecondary);
                break;
        }
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
    }
}