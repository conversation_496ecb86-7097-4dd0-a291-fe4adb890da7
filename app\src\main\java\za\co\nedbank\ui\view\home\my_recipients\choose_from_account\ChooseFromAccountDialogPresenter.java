/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients.choose_from_account;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;

/**
 * Created by priyadhingra on 10-04-2018.
 */

public class ChooseFromAccountDialogPresenter extends NBBasePresenter<ChooseFromAccountDialogView> {

    @Inject
    ChooseFromAccountDialogPresenter(){

    }

    void handleFromAccountSelected(int position){
        if(view != null){
            view.dismissDialog(position);
        }
    }
}
