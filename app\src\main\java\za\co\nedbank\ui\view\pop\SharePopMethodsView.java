package za.co.nedbank.ui.view.pop;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;

public interface SharePopMethodsView extends NBBaseView {

    void receiveSharePOPMethodList(List<String> sharePOPMethodList);

    void setActivityResult(String sharePOPMethod);

    void finishScreen();

    TransactionHistoryViewModel getTransactionHistoryViewModel();

    boolean isFromRecentPaymentFlow();

    boolean isSopFromPayDoneFlow();

    boolean isBeyond90Days();

    boolean isNavigateToOverView();

    boolean isFromPaymentsFlow();
}
