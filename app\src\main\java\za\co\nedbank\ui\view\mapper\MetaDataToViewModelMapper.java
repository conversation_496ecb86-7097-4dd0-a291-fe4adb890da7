/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.mapper;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.view.metadata.MetaDataViewModel;
import za.co.nedbank.core.view.metadata.ResultDataViewModel;
import za.co.nedbank.core.view.metadata.ResultDetailViewModel;


/**
 * Created by sandip.lawate on 01/04/2018.
 */

public class MetaDataToViewModelMapper {

    @Inject
    MetaDataToViewModelMapper() {
    }

    public MetaDataViewModel mapMetaData(MetaDataModel metadata) {
        MetaDataViewModel model = null;
        if (null != metadata) {
            model = new MetaDataViewModel();
            model.setResultData(mapResultData(metadata.getResultData()));
        }
        return model;
    }

    private ArrayList<ResultDataViewModel> mapResultData(List<ResultDataModel> resultData) {
        ArrayList<ResultDataViewModel> resultViewModels = new ArrayList<>();
        if (resultData != null) {
            for (ResultDataModel resultDataEntity : resultData) {
                ResultDataViewModel model = new ResultDataViewModel();
                model.setTransactionID(resultDataEntity.getTransactionID());
                model.setExecEngineRef(resultDataEntity.getExecEngineRef());
                model.setResultDetail(mapMetaDataResultDetail(resultDataEntity.getResultDetail()));
                resultViewModels.add(model);
            }
        }
        return resultViewModels;
    }

    private ArrayList<ResultDetailViewModel> mapMetaDataResultDetail(List<ResultDetailModel> resultDetail) {
        ArrayList<ResultDetailViewModel> accountResultDetailViewModels = new ArrayList<>();
        if (resultDetail != null) {
            for (ResultDetailModel resultDetailDataModel : resultDetail) {
                ResultDetailViewModel model = new ResultDetailViewModel();
                model.setOperationReference(resultDetailDataModel.getOperationReference());
                model.setResult(resultDetailDataModel.getResult());
                model.setStatus(resultDetailDataModel.getStatus());
                model.setReason(resultDetailDataModel.getReason());
                accountResultDetailViewModels.add(model);
            }
        }
        return accountResultDetailViewModels;
    }
}
