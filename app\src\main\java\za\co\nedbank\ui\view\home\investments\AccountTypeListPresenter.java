package za.co.nedbank.ui.view.home.investments;

import static za.co.nedbank.core.Constants.AllowedTransactions.INVESTMENTS_PAYOUT;
import static za.co.nedbank.core.Constants.BUNDLE_KEYS.FROM_TRANSACTIONAL_FLOW;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_BIRTH_DATE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_CLIENT_TYPE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD;

import android.annotation.SuppressLint;

import java.math.BigDecimal;

import javax.inject.Inject;

import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.usecase.investmentonline.GetFicaStatusNowUsecase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;

public class AccountTypeListPresenter extends NBBasePresenter<AccountTypeListView> {

    private final NavigationRouter navigationRouter;
    protected final GetFicaStatusNowUsecase getFicaStatusUseCase;
    protected final ErrorHandler errorHandler;

    @Inject
    AccountTypeListPresenter(final NavigationRouter navigationRouter,
                             final GetFicaStatusNowUsecase getFicaStatusUseCase,
                             final ErrorHandler errorHandler) {
        this.navigationRouter = navigationRouter;
        this.getFicaStatusUseCase = getFicaStatusUseCase;
        this.errorHandler = errorHandler;
    }

    private void navigateToInvestment() {
        if (view != null) {
            if (isClientTypeValid(view.getClientType())) {
                getFicaStatus();
            } else {
                showClientTypeErrorActivity(za.co.nedbank.core.Constants.INVONLINE_ONIA_ERROR_INVALID_CLIENT_TYPE);
            }
        }
    }

    public boolean isClientTypeValid(String clientType) {
        return view != null && clientType != null && (!clientType.equals(za.co.nedbank.core.Constants.CLIENT_TYPE_51) && !clientType.equals(za.co.nedbank.core.Constants.CLIENT_TYPE_52));
    }

    public void showClientTypeErrorActivity(int errorType) {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_ONIA_INVESTMENT_ERROR)
                .withParam(NavigationTarget.KEY_ONIA_ERROR_TYPE, errorType));
    }

    public void getFicaStatus() {
        if (view != null) {
            view.showProgressBar(true);
            getFicaStatusUseCase
                    .execute()
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                    })
                    .doOnTerminate(() -> view.showProgressBar(false))
                    .subscribe(ficaStatusDataModel -> {
                        view.showProgressBar(false);
                        if (ficaStatusDataModel.getIsFica() && view != null) {
                            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_RIGHT_OPTIONS).withParam(PARAM_ONIA_CLIENT_TYPE, view.getClientType())
                                    .withParam(PARAM_ONIA_BIRTH_DATE, view.getBirthDate()).withParam(PARAM_ONIA_SEC_OFFICER_CD, view.getSecOfficerCd()));
                        } else {
                            showClientTypeErrorActivity(za.co.nedbank.core.Constants.INVONLINE_ONIA_ERROR_FICA_ERROR_TYPE);
                        }
                    }, throwable -> view.showError(errorHandler.getErrorMessage(throwable).getMessage()));
        }
    }

    public void onAccountSelected(AccountSummary accountSummary, ClientType clientType) {
        if (StringUtils.isNullOrEmpty(accountSummary.getNumber())) {
            navigateToInvestment();
        } else {
            if (clientType == ClientType.TP) {
                navigateToAccountDetailsTP(accountSummary);
            } else {
                navigateToAccountDetails(accountSummary);
            }
        }
    }

    @SuppressLint("CheckResult")
    public void navigateToAccountDetails(AccountSummary accountSummary) {
        if (accountSummary != null && view != null) {
            accountSummary.setSummaryValue(BigDecimal.valueOf(accountSummary.getAvailableBalance()));
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS);
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_OVERVIEW_ACCOUNT_TYPE, accountSummary.getAccountCode());
            navigationTarget.withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_DEFALUT_ACCOUNT_ID, view.getDefaultAccountId());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, view.canTransact());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, view.getClientType());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, view.getFicaStatus());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true);
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_PRODUCT_TYPE, accountSummary.getProductType());
            navigationTarget.withParam(ServicesNavigationTarget.IS_FROM_CATEGORY_LIST, true);
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_PRODUCT_NGI_PRODUCT_TYPE, accountSummary.getInvestmentProductId());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_ACCOUNT_PLEDGE_STATUS, accountSummary.isPledgeAccount());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_INV_TARGET_NAME, accountSummary.getGoalName());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_INV_TARGET_ID, accountSummary.getGoalId());

            navigationRouter.navigateWithResult(navigationTarget).subscribe(
                    navigationResult -> {
                        if (navigationResult.isOk() && navigationResult.getParams().containsKey(ServicesNavigationTarget.PARAM_INV_TARGET_ID))
                        {
                            view.updateInvestAccountList(navigationResult.getIntParam(ServicesNavigationTarget.PARAM_INV_TARGET_ID));
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage())
            );

        }
    }

    @SuppressLint("CheckResult")
    public void navigateToAccountDetailsTP(AccountSummary accountSummary) {
        if (accountSummary != null && view != null) {
            accountSummary.setSummaryValue(BigDecimal.valueOf(accountSummary.getAvailableBalance()));
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS);
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_DEFALUT_ACCOUNT_ID, view.getDefaultAccountId());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_OVERVIEW_ACCOUNT_TYPE, accountSummary.getAccountCode());
            navigationTarget.withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, view.canTransact());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, view.getClientType());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_FIRST_CONVERSION_DATE, accountSummary.getFirstAvailableConversionDate());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_ADD_EARLY_RELEASE, accountSummary.getAddEarlyRelease());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_DELETE_EARLY_RELEASE, accountSummary.getDeleteEarlyRelease());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, view.getFicaStatus());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true);
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_PRODUCT_TYPE, accountSummary.getProductType());
            navigationTarget.withParam(ServicesNavigationTarget.IS_FROM_CATEGORY_LIST, true);
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_ACCOUNT_PLEDGE_STATUS, accountSummary.isPledgeAccount());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber());
            navigationTarget .withParam(ServicesNavigationTarget.INVONLINE_ADD_RECURRING_PAYMENT, accountSummary.getAddRecurringPayment());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_VIEW_PAYOUT, accountSummary.getViewPayout());
            navigationTarget .withParam(ServicesNavigationTarget.INVONLINE_DELETE_PAYOUT, accountSummary.getDeletePayout());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_VIEW_REINVEST, accountSummary.getViewReinvest());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_DELETE_REINVEST, accountSummary.getDeleteReinvest());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_DRAWN_DOWN_ACCOUNT_STAUS, accountSummary.getStatus());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_IS_SA_RESIDENT, view.isSAResident());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_ONIA_CLIENT_TYPE, view.getClientType());
            navigationTarget .withParam(ServicesNavigationTarget.PARAM_ONIA_BIRTH_DATE, view.getBirthDate());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_ONIA_SEC_OFFICER_CD, view.getSecOfficerCd());
            navigationTarget.withParam(ServicesNavigationTarget.INVONLINE_PRODUCT_NGI_PRODUCT_TYPE, accountSummary.getInvestmentProductId());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_ONIA_CIS_NUMBER, view.getCisNumber());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_ONIA_TFS_INDICATOR, accountSummary.getTfsIndicator());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_INV_TARGET_NAME, accountSummary.getGoalName());
            navigationTarget.withParam(ServicesNavigationTarget.PARAM_INV_TARGET_ID, accountSummary.getGoalId());

            navigationRouter.navigateWithResult(navigationTarget).subscribe(
                    navigationResult -> {
                        if (navigationResult.isOk() && navigationResult.getParams().containsKey(ServicesNavigationTarget.PARAM_INV_TARGET_ID))
                        {
                            view.updateInvestAccountList(navigationResult.getIntParam(ServicesNavigationTarget.PARAM_INV_TARGET_ID));
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage())
            );
        }
    }

    public void navigateToHome() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withIntentFlagNewTask(true));
    }
}
