/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;

import java.lang.ref.WeakReference;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.sharedui.listener.IViewPagerChildClickListener;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.recipient.UserBeneficiaryDataViewModel;
import za.co.nedbank.databinding.ViewMyRecipientSelectBinding;

public class MyRecipientsFragment extends NBBaseFragment {

    private static final String SCALE = "scale";
    private static final String IS_ENABLED = "isEnabled";
    private static final String MODEL = "model";
    private static final String POSITION = "position";
    private boolean mIsEnabled;
    private WeakReference<IViewPagerChildClickListener> mIViewPagerChildClickListenerWeakReference;
    private ViewMyRecipientSelectBinding binding;

    public static Fragment newInstance(Context context, float scale, boolean isEnabled, UserBeneficiaryDataViewModel myRecipientsViewModel, int position) {
        Bundle b = new Bundle();
        b.putFloat(SCALE, scale);
        b.putBoolean(IS_ENABLED, isEnabled);
        b.putParcelable(MODEL, myRecipientsViewModel);
        b.putInt(POSITION, position);
        return Fragment.instantiate(context, MyRecipientsFragment.class.getName(), b);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = ViewMyRecipientSelectBinding.inflate(inflater, container, false);
        if(this.getArguments() != null) {
            float scale = this.getArguments().getFloat(SCALE);
            int position = this.getArguments().getInt(POSITION);
            boolean isEnabled = this.getArguments().getBoolean(IS_ENABLED);
            binding.myRecipientCarouselLl.setScaleBoth(scale);
            UserBeneficiaryDataViewModel myRecipientsViewModel = this.getArguments().getParcelable(MODEL);
            if (myRecipientsViewModel != null && myRecipientsViewModel.getContactCardName() != null && !myRecipientsViewModel.getContactCardName().isEmpty()) {
                String initials = myRecipientsViewModel.getContactCardName().substring(0, 1);
                if (myRecipientsViewModel.getContactCardName().contains(StringUtils.SPACE)) {
                    String[] splitName = myRecipientsViewModel.getContactCardName().split(StringUtils.SPACE);
                    if (splitName.length > 1 && !TextUtils.isEmpty(splitName[1])) {
                        initials += splitName[1].substring(0, 1);
                    }
                }
                binding.myRecipientSelectorNameInitialsTv.setText(initials.toUpperCase());
            }
            binding.myRecipientSelectorRoot.setOnClickListener(view1 -> {
                if (mIViewPagerChildClickListenerWeakReference != null && mIViewPagerChildClickListenerWeakReference.get() != null) {
                    mIViewPagerChildClickListenerWeakReference.get().onItemClick(position);
                }
            });
            //accessibility
            String accountNumber = StringUtils.EMPTY_STRING;
            String bankName = StringUtils.EMPTY_STRING;

            if(myRecipientsViewModel != null && myRecipientsViewModel.getBfDetails()!= null && myRecipientsViewModel.getBfDetails().size()>0){
                if (myRecipientsViewModel.getBfDetails().get(0).getAccountNumber() != null) {
                    accountNumber = myRecipientsViewModel.getBfDetails().get(0).getAccountNumber();
                }
                if (myRecipientsViewModel.getBfDetails().get(0).getBankName() != null) {
                    bankName = myRecipientsViewModel.getBfDetails().get(0).getBankName();
                }
                binding.myRecipientSelectorNameInitialsTv.setContentDescription(String.format(getString(R.string.quick_pay_select_recipient_acc), myRecipientsViewModel.getContactCardName(), accountNumber, bankName));
            }
            binding.myRecipientSelectorRoot.setSelected(isEnabled);
        }
        return binding.getRoot();
    }

    public void setIsEnabled(boolean isEnabled) {
        if(getArguments() != null){
            getArguments().putBoolean(IS_ENABLED,isEnabled);
        }
        this.mIsEnabled = isEnabled;
        binding.myRecipientSelectorRoot.setSelected(mIsEnabled);
    }

    public void setIViewPagerChildClickListenerWeakReference(WeakReference<IViewPagerChildClickListener> IViewPagerChildClickListenerWeakReference) {
        mIViewPagerChildClickListenerWeakReference = IViewPagerChildClickListenerWeakReference;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mIViewPagerChildClickListenerWeakReference = null;
    }
}
