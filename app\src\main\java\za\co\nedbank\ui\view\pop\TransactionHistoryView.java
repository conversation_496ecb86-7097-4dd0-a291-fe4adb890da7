package za.co.nedbank.ui.view.pop;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;

public interface TransactionHistoryView extends NBBaseView {
    void showListLoading();

    void showListReloading();

    void showListLoaded();

    void showEmptyListIndicator();

    void showTransactionHistoryViewModel(List<TransactionHistoryViewModel> transactionHistoryViewModels);

    void showError(String message);

    void setIsLoading(boolean isLoading);

    void showFooterView(TransactionFooterViewType footerViewType);

    boolean isLoading();

    boolean isViewMoreVisible();

    void hideFooterView();
}