/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.model.money_request;

import java.util.List;

import za.co.nedbank.ui.view.home.money_requests.view_money_response.MoneyRequestsType;

/**
 * Created by swapnil.gawande on 2/20/2018.
 */

public class MoneyRequestsAdapterModel {
    private @MoneyRequestsType
    int headerStringId;
    private List<MoneyRequestsViewModel> moneyRequestsViewModels;

    @MoneyRequestsType
    public int getHeader() {
        return headerStringId;
    }

    public void setHeader(@MoneyRequestsType int headerStringId) {
        this.headerStringId = headerStringId;
    }

    public List<MoneyRequestsViewModel> getMoneyRequestsViewModels() {
        return moneyRequestsViewModels;
    }

    public void setMoneyRequestsViewModels(List<MoneyRequestsViewModel> moneyRequestsViewModels) {
        this.moneyRequestsViewModels = moneyRequestsViewModels;
    }
}
