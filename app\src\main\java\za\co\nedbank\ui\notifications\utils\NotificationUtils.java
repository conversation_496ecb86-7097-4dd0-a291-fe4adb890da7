package za.co.nedbank.ui.notifications.utils;

import static za.co.nedbank.core.notification.NotificationConstants.NAVIGATION_TARGET.*;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.os.Build;

import androidx.core.app.NotificationManagerCompat;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import net.danlew.android.joda.DateUtils;

import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.Seconds;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;


public class NotificationUtils {

    private NotificationUtils(){
        // Defining private constructor
    }

    private static final String TAG = NotificationUtils.class.getSimpleName();
    private static final List<String> globalNavigationTagList = new ArrayList<>(Arrays.asList(
            OPEN_MI_GOALS.toLowerCase(),
            OPEN_MI_GOALS_PLUS.toLowerCase(),
            OPEN_MI_GOALS_PREMIUM.toLowerCase(),
            MY_COVER_BUILD_YOUR_OWN_PACKAGE.toLowerCase(),
            MY_COVER_FIXED_FAMILY_PACKAGE.toLowerCase(),
            MY_COVER_R10000_INDIVIDUAL_PACKAGE.toLowerCase(),
            MY_COVER_R30000_INDIVIDUAL_PACKAGE.toLowerCase(),
            COVER_PERSONAL_LINES_BUILDINGS.toLowerCase(),
            COVER_PERSONAL_LINES_MOTOR.toLowerCase(),
            COVER_PERSONAL_LINES_HOUSE_CONTENT.toLowerCase(),
            COVER_VVAPS_COMPREHENSIVE_WARRANTY.toLowerCase(),
            COVER_VVAPS_ESSENTIAL_WARRANTY.toLowerCase(),
            APPLY_LIFE_COVER.toLowerCase(),
            MY_PREPAID_ADVANCE_HISTORY.toLowerCase(),
            INSURANCE_REVIEW_APPLICATIONS.toLowerCase(),
            CREDIT_SHORTFALL_EDUCATION.toLowerCase(),
            COVER_VVAPS_GAP_WARRANTY.toLowerCase(),
            APPLY_PERSONAL_ACCIDENT_COVER.toLowerCase(),
            OPEN_JUST_SAVE_ACCOUNT.toLowerCase(),
            RECEIVED_PAYSHAP_REQUEST.toLowerCase(),
            VIEW_EXPIRY_RTP_REQUEST.toLowerCase(),
            OPEN_PROOF_OF_ACCOUNT.toLowerCase(),
            SHOP_AVO.toLowerCase()
    ));

    public static boolean canMoveToNavigationHandler(String action) {
        return globalNavigationTagList.contains(action.toLowerCase());
    }

    public static NotificationData extractNotificationData(String json) {

        try {
            Gson gson = new Gson();
            return gson.fromJson(json, NotificationData.class);
        } catch (JsonSyntaxException e) {
            NBLogger.e("extractNotificationData", e.getMessage());
        }
        return null;
    }


    public static long secondsRemainingFromNow(String dateText, String format) {

        try {
            DateFormat originalFormat = new SimpleDateFormat(format);
            originalFormat.setTimeZone(TimeZone.getTimeZone(FormattingUtil.SOUTH_AFRICA_TIMZONE_ID));
            Date date = originalFormat.parse(dateText);
            DateTime dateTime = new DateTime(date);
            Date date2 = new Date();
            DateTime dateTime2 = new DateTime(date2);
            return Seconds.secondsBetween(dateTime2, dateTime).getSeconds();
        } catch (ParseException e) {
            NBLogger.e(TAG, e.getMessage());
        }
        return -1;
    }

    public static boolean isToday(String dateText, String format) {

        try {
            DateFormat originalFormat = new SimpleDateFormat(format, Locale.getDefault());
            originalFormat.setTimeZone(TimeZone.getTimeZone(FormattingUtil.SOUTH_AFRICA_TIMZONE_ID));
            Date date = originalFormat.parse(dateText);
            DateTime dateTime = new DateTime(date);
            return DateUtils.isToday(dateTime);
        } catch (ParseException e) {
            NBLogger.e(TAG, "getFormattedDate -> error in parsing date : " + e.getMessage());
        }
        return false;
    }

    public static boolean isToday(Date dateText) {
        return DateUtils.isToday(new DateTime(dateText));
    }

    public static int daysBetween(String dateText, String format) {
        try {
            DateFormat originalFormat = new SimpleDateFormat(format);
            originalFormat.setTimeZone(TimeZone.getTimeZone(FormattingUtil.SOUTH_AFRICA_TIMZONE_ID));
            Date date = originalFormat.parse(dateText);
            DateTime dateTime = new DateTime(date);
            Date date2 = new Date();
            DateTime dateTime2 = new DateTime(date2);
            return Days.daysBetween(dateTime, dateTime2).getDays();
        } catch (ParseException e) {
            NBLogger.e(TAG, "getFormattedDate -> error in parsing date : " + e.getMessage());
        }
        return -1;
    }

    public static int daysBetween(Date dateText) {
        return Days.daysBetween(new DateTime(dateText), new DateTime(new Date())).getDays();
    }

    public static boolean isYesterday(String dateText, String format) {

        DateFormat originalFormat = new SimpleDateFormat(format);
        originalFormat.setTimeZone(TimeZone.getTimeZone(FormattingUtil.SOUTH_AFRICA_TIMZONE_ID));
        Date date = null;
        try {
            date = originalFormat.parse(dateText);
            DateTime dateTime = new DateTime(date);
            DateTime yesterday = new DateTime().withTimeAtStartOfDay().minusDays(1);
            DateTime inputDay = dateTime.withTimeAtStartOfDay();
            return inputDay.isEqual(yesterday);
        } catch (ParseException e) {
            NBLogger.e("Exception:","NotificationUtils",e);
        }
        return false;
    }

    public static boolean isYesterday(Date dateText) {
         DateTime yesterday = new DateTime().withTimeAtStartOfDay().minusDays(1);
         DateTime inputDay = new DateTime(dateText).withTimeAtStartOfDay();
         return inputDay.isEqual(yesterday);
    }

    public static boolean isOlderThanYesterday(String dateText, String format) {
        int daysBetween = daysBetween(dateText, format);
        return daysBetween >= 1;
    }

    public static boolean isOlderThanYesterday(Date dateText) {
        int daysBetween = daysBetween(dateText);
        return daysBetween >= 1;
    }

    public static String getNotificationType(String notificationType) {
        if (StringUtils.isNullOrEmpty(notificationType)) {
            return NotificationConstants.NO_VALUE;
        }

        switch (notificationType) {
            case NotificationConstants.NOTIFICATION_TYPES.INFO:
                return NotificationConstants.NOTIFICATION_TYPES_ADOBE.INFORMATION;
            case NotificationConstants.NOTIFICATION_TYPES.MARKETING:
                return NotificationConstants.NOTIFICATION_TYPES_ADOBE.MARKETING;
            case NotificationConstants.NOTIFICATION_TYPES.CHAT:
                return NotificationConstants.NOTIFICATION_TYPES_ADOBE.CHAT;
            case NotificationConstants.NOTIFICATION_TYPES.TRANSACTION:
                return NotificationConstants.NOTIFICATION_TYPES_ADOBE.TRANSACTION;
            case NotificationConstants.NOTIFICATION_TYPES.SECURITY:
                return NotificationConstants.NOTIFICATION_TYPES_ADOBE.SECURITY;
            default:
                return notificationType;
        }
    }

    public static boolean areNotificationsEnabled(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            if (!manager.areNotificationsEnabled()) {
                return false;
            }
            List<NotificationChannel> channels = manager.getNotificationChannels();
            for (NotificationChannel channel : channels) {
                if (channel.getName().toString().equalsIgnoreCase(NotificationConstants.LEAK_CANARY)) {
                    continue;
                }
                if (channel.getImportance() == NotificationManager.IMPORTANCE_NONE) {
                    return false;
                }
            }
            return true;
        } else {
            return NotificationManagerCompat.from(context).areNotificationsEnabled();
        }
    }
}
