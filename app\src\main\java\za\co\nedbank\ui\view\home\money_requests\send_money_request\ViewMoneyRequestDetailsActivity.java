/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;

import androidx.annotation.Nullable;

import com.jakewharton.rxbinding2.widget.RxTextView;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.accounts.listener.IViewHolderInteraction;
import za.co.nedbank.core.view.accounts.ui.AccountsViewFragment;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.databinding.ActivityMoneyRequestDetailBinding;
import za.co.nedbank.payment.transfer.navigator.TransferNavigationTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.domain.model.money_request.PaymentResponseModel;
import za.co.nedbank.uisdk.widget.NBSnackbar;

/**
 * Created by sandip.lawate on 2/15/2018.
 */
public class ViewMoneyRequestDetailsActivity extends NBBaseActivity implements MoneyRequestDetailsView, IViewHolderInteraction {

    @Inject
    ViewMoneyRequestDetailsPresenter mMoneyRequestsPresenter;

    private AccountsViewFragment accountsViewFragment;
    private Handler mHandler;
    private int mToSelectedPosition = 0;
    private String recipientName, recipientPhoneNumber;
    private ActivityMoneyRequestDetailBinding binding;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityMoneyRequestDetailBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        binding.toolbar.setTitle(R.string.details_of_requests);
        initToolbar(binding.toolbar, true);

        if (getIntent() != null) {
            recipientName = getIntent().getStringExtra(NavigationTarget.PARAM_RECIPIENT_NAME);
            recipientPhoneNumber = getIntent().getStringExtra(NavigationTarget.RECIPIENT_PHONE_NUMBER);

        }
        accountsViewFragment = (AccountsViewFragment) getSupportFragmentManager().findFragmentById(za.co.nedbank.payment.R.id.frgToAccounts);
        mHandler = new Handler(Looper.getMainLooper());
        EditText edtAmount = binding.etMoneyRequestAmt.getInputField();
        if ((edtAmount != null) && TextUtils.isEmpty(edtAmount.getText().toString()) && TextUtils.isEmpty(binding.edtPaymentReason.getInputField().getText())) {
            binding.btnSendNow.setEnabled(false);
        }
        assert edtAmount != null;
        Observable<CharSequence> textChangeObservable = RxTextView.textChanges(edtAmount).skip(1);
        Observable<CharSequence> textChangObservable = RxTextView.textChanges(binding.edtPaymentReason.getInputField()).skip(1);
        textChangObservable.subscribe(chars -> mMoneyRequestsPresenter.checkInputForAllFields(binding.etMoneyRequestAmt, binding.edtPaymentReason.getValue()), throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        textChangeObservable.subscribe(chars -> mMoneyRequestsPresenter.checkInputForAllFields(binding.etMoneyRequestAmt, binding.edtPaymentReason.getValue()), throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        binding.etMoneyRequestAmt.getInputField().setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE && !TextUtils.isEmpty(binding.etMoneyRequestAmt.getInputField().getText().toString())) {
                binding.etMoneyRequestAmt.setText(binding.etMoneyRequestAmt.getInputField().getText().toString());
                binding.etMoneyRequestAmt.getInputField().setSelection(binding.etMoneyRequestAmt.getInputField().getText().toString().length());
            }
            return false;
        });
        mMoneyRequestsPresenter.bind(this);
        binding.btnSendNow.setOnClickListener(v -> onSendButtonClick());
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMoneyRequestsPresenter.getAccounts();
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
        mMoneyRequestsPresenter.unbind();
    }

    @Override
    public void showAccountsErrorView(String... errorMessage) {
        String error = errorMessage != null && errorMessage.length > 0 ? errorMessage[0] : StringUtils.EMPTY_STRING;
        accountsViewFragment.refreshViews(null, true, error);
    }

    @Override
    public void showError(String error) {
        NBSnackbar.instance().action(getString(za.co.nedbank.payment.R.string.snackbar_action_retry), () -> mMoneyRequestsPresenter.sendPaymentRequest(binding.etMoneyRequestAmt, mToSelectedPosition, recipientName, recipientPhoneNumber, binding.edtPaymentReason)).build(binding.etMoneyRequestAmt, error);
    }

    @Override
    public void setAccounts(List<AccountViewModel> accountViewModels) {
        mHandler.postDelayed(() -> {
            if (accountViewModels.size() > 0) {
                accountsViewFragment.refreshViews(accountViewModels, true);
            } else {
                accountsViewFragment.refreshViews(null, true, getString(za.co.nedbank.payment.R.string.no_accounts_available));
            }
        }, TransferNavigationTarget.VALUES.ACCOUNTS_UPDATE_DELAY);
    }


    @Override
    public void setNextButtonEnabled(boolean isEnabled) {
        binding.btnSendNow.setEnabled(isEnabled);
    }

    @Override
    public void sendMoneyRequestSuccess(PaymentResponseModel paymentResponseModel) {
        String referenceNumber = paymentResponseModel.getData().getReferenceNumber();
        showLoadingOnButton(false);
        mMoneyRequestsPresenter.handleSendClick(referenceNumber, recipientName);
    }

    @Override
    public void sendMoneyRequestFailure(PaymentResponseModel paymentResponseEntity) {
        showLoadingOnButton(false);
        mMoneyRequestsPresenter.handleSendClick(StringUtils.EMPTY_STRING, recipientName);
    }

    @Override
    public void showLoadingOnButton(boolean shouldShowLoading) {
        binding.btnSendNow.setLoadingVisible(shouldShowLoading);
    }

    void onSendButtonClick() {
        mMoneyRequestsPresenter.sendPaymentRequest(binding.etMoneyRequestAmt, mToSelectedPosition, recipientName, recipientPhoneNumber, binding.edtPaymentReason);
    }

    @Override
    public void onParentViewSelected(int position, boolean isFromAdapter) {
        if (!isFromAdapter) {
            mToSelectedPosition = position;
            mHandler.postDelayed(() -> accountsViewFragment.refreshViews(mMoneyRequestsPresenter.getToAccounts(), false), TransferNavigationTarget.VALUES.ACCOUNTS_UPDATE_DELAY);
        }
    }

    @Override
    public void trackApiFailure(String apiName) {
        if (!StringUtils.isNullOrEmpty(apiName) && apiName.equalsIgnoreCase(za.co.nedbank.core.ApiAliasConstants.PM_FCH_ACC))
            mMoneyRequestsPresenter.trackApiFailure(getString(R.string.money_request_failure_msg), apiName);
        else if (!StringUtils.isNullOrEmpty(apiName) && apiName.equalsIgnoreCase(za.co.nedbank.core.ApiAliasConstants.PM_RQ))
            mMoneyRequestsPresenter.trackApiFailure(getString(R.string.empty_view_accounts), apiName);
    }
}
