/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.data.mapper.RecipientResponseEntityToDomainDataMapper;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.model.response.RecipientResponseData;
import za.co.nedbank.core.domain.repository.IRecipientRepository;

/**
 * Created by priyadhingra on 28-07-2017.
 */
public class GetSingleRecipientUseCase extends UseCase<String, RecipientResponseData> {
    private final IRecipientRepository mIRecipientRepository;
    private final RecipientResponseEntityToDomainDataMapper mRecipientResponseEntityToDomainDataMapper;

    @Inject
    protected GetSingleRecipientUseCase(final UseCaseComposer useCaseComposer, final IRecipientRepository iRecipientRepository, RecipientResponseEntityToDomainDataMapper recipientResponseEntityToDomainDataMapper) {
        super(useCaseComposer);
        this.mIRecipientRepository = iRecipientRepository;
        this.mRecipientResponseEntityToDomainDataMapper = recipientResponseEntityToDomainDataMapper;
        setCacheObservable(false);
    }

    @Override
    protected Observable<RecipientResponseData> createUseCaseObservable(String contactCardId) {
        return mIRecipientRepository.getContactCard(contactCardId).map(mRecipientResponseEntityToDomainDataMapper::mapRecipientResponseEntityToData);
    }
}
