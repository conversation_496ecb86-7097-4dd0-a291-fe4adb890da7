package za.co.nedbank.ui.view.home.tax_certificates_accounts;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;

public class TaxCertificatesAccountsPresenter extends NBBasePresenter<TaxCertificatesAccountsView> {

    private final NavigationRouter mNavigationRouter;

    @Inject
    TaxCertificatesAccountsPresenter(final NavigationRouter mNavigationRouter) {
        this.mNavigationRouter = mNavigationRouter;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }
}