package za.co.nedbank.ui.domain.usecase.pop;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.TwoArgUseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.ui.domain.mapper.pop.SharePOPResponseEntityToDataMapper;
import za.co.nedbank.ui.domain.mapper.pop.ShareProofOfPaymentDataToEntityMapper;
import za.co.nedbank.ui.domain.model.pop.SharePOPResponseData;
import za.co.nedbank.ui.domain.model.pop.ShareProofOfPaymentRequestData;
import za.co.nedbank.ui.domain.repository.pop.ShareProofOfPaymentRepository;

public class ShareProofOfPaymentUseCase extends TwoArgUseCase<ShareProofOfPaymentRequestData, String, SharePOPResponseData> {

    private ShareProofOfPaymentRepository shareProofOfPaymentRepository;

    private SharePOPResponseEntityToDataMapper sharePOPResponseEntityToDataMapper;
    private ShareProofOfPaymentDataToEntityMapper shareProofOfPaymentDataToEntityMapper;

    @Inject
    protected ShareProofOfPaymentUseCase(final UseCaseComposer useCaseComposer, final ShareProofOfPaymentRepository shareProofOfPaymentRepository, final SharePOPResponseEntityToDataMapper sharePOPResponseEntityToDataMapper, ShareProofOfPaymentDataToEntityMapper shareProofOfPaymentDataToEntityMapper) {
        super(useCaseComposer);
        this.shareProofOfPaymentRepository = shareProofOfPaymentRepository;
        this.sharePOPResponseEntityToDataMapper = sharePOPResponseEntityToDataMapper;
        this.shareProofOfPaymentDataToEntityMapper = shareProofOfPaymentDataToEntityMapper;
        setCacheObservable(false);
    }

    @Override
    protected Observable<SharePOPResponseData> createUseCaseObservable(ShareProofOfPaymentRequestData shareProofOfPaymentRequestData, String contractID) {
        if(shareProofOfPaymentRequestData.isSopFromPayDone() && shareProofOfPaymentRequestData.isSchedulePayment()){
            return shareProofOfPaymentRepository.postShareProofOfPaymentRecipientsForSchedulePayment(shareProofOfPaymentDataToEntityMapper.mapDataMoreThan90Days(shareProofOfPaymentRequestData), contractID).map(sharePOPResponseEntityToDataMapper::mapSharePOPResponseEntityToDataList);
        }
        if(!shareProofOfPaymentRequestData.isFromRecentPayments()) {
            return shareProofOfPaymentRepository.postShareProofOfPaymentRecipients(shareProofOfPaymentDataToEntityMapper.mapDataMoreThan90Days(shareProofOfPaymentRequestData), contractID).map(sharePOPResponseEntityToDataMapper::mapSharePOPResponseEntityToDataList);
        }
        return shareProofOfPaymentRepository.postShareProofOfPayment(shareProofOfPaymentDataToEntityMapper.mapData(shareProofOfPaymentRequestData), contractID).map(sharePOPResponseEntityToDataMapper::mapSharePOPResponseEntityToDataList);
    }
}
