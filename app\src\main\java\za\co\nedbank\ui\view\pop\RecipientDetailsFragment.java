package za.co.nedbank.ui.view.pop;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.base.dialog.IDialog;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.SimpleDividerItemDecoration;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerView;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewModel;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.CMAUtils;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.view.recipient.BankAccountViewDataModel;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryCollectiveDataViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryDetailsViewModel;
import za.co.nedbank.databinding.RecipientDetailsFragmentBinding;
import za.co.nedbank.payment.common.domain.data.mapper.beneficiary.user.UserBeneficiaryMapper;
import za.co.nedbank.payment.common.view.PaymentPayzarFlowInfoDialog;
import za.co.nedbank.payment.common.view.beneficiary.user.model.SelectedUserBeneficiaryViewModel;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.add_recipient.BankAccountAdapter;
import za.co.nedbank.ui.view.home.add_recipient.CreditCardAdapter;
import za.co.nedbank.ui.view.home.add_recipient.ElectricityMeterAdapter;
import za.co.nedbank.ui.view.home.add_recipient.EmailAdapter;
import za.co.nedbank.ui.view.home.add_recipient.MobileNumberAdapter;
import za.co.nedbank.ui.view.home.add_recipient.RppPaymentAdapter;
import za.co.nedbank.uisdk.widget.NBSnackbar;

public class RecipientDetailsFragment extends NBBaseFragment implements RecipientDetailsView {

    @Inject
    RecipientDetailsPresenter recipientDetailsPresenter;

    @Inject
    FeatureSetController featureSetController;
    protected NBFlexibleItemCountRecyclerView rvAccount;
    protected NBFlexibleItemCountRecyclerView rvMobileNumber;
    protected NBFlexibleItemCountRecyclerView rvCreditCard;
    protected NBFlexibleItemCountRecyclerView rvElectricity;
    protected NBFlexibleItemCountRecyclerView rvRppPayment;
    protected NBFlexibleItemCountRecyclerView rvEmail;
    protected RecipientViewModel mRecipientViewModel;
    protected NBFlexibleItemCountRecyclerviewModel mBankAccountNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mMobileNumberNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mElectricityNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mCreditCardNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mShapIdNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mEmailNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mRPPNbFlexibleItemCountRecyclerviewModel;
    protected List<NbRecyclerViewBaseDataModel> mBankAccountViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mMobileNumberViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mElectricityMeterViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mCreditCardViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mShapIdViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mEmailViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mRPPViewDataModelList;
    protected BankAccountAdapter mBankAccountAdapter;
    protected MobileNumberAdapter mMobileNumberAdapter;
    protected ElectricityMeterAdapter mElectricityMeterAdapter;
    private UserBeneficiaryCollectiveDataViewModel mUserBeneficiaryCollectiveDataViewModel;
    protected CreditCardAdapter mCreditCardAdapter;
    protected RppPaymentAdapter mRppPaymentAdapter;
    protected EmailAdapter mEmailAdapter;
    @Inject
    UserBeneficiaryMapper mUserBeneficiaryMapper;
    private ISectionedListItemSelectedListener itemSelectedListener = (sectionPosition, itemPositionInSection) -> {
        if(recipientDetailsPresenter!=null){
            recipientDetailsPresenter.checkFica(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.values()[sectionPosition], itemPositionInSection);
        }

    };
    private RecipientDetailsFragmentBinding binding;

    @Override
    public void onItemSelected(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE value, int itemPositionInSection) {
        if (recipientDetailsPresenter.isFatcaRestrictionsApplicable()) {
            recipientDetailsPresenter.navigateToNextScreen();
        } else {
            setupResult(value, itemPositionInSection);
        }
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = RecipientDetailsFragmentBinding.inflate(inflater, container, false);
        recipientDetailsPresenter.bind(this);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);

    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        initView();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        recipientDetailsPresenter.unbind();
    }

    private void initView() {
        mRecipientViewModel = getmRecipientViewModel();
        mUserBeneficiaryCollectiveDataViewModel = getUserBeneficiaryCollectiveDataViewModel();
        setupRecyclerViews();
    }

    @Override
    public UserBeneficiaryCollectiveDataViewModel getUserBeneficiaryCollectiveDataViewModel() {
        return getArguments() != null ? getArguments().getParcelable(Constants.EXTRAS.USER_BENEFICIARY_VIEW_MODEL) : null;
    }

    @Override
    public void errorSnackBar() {
        showError(getString(R.string.error), getString(R.string.something_went_wrong),
                getString(R.string.snackbar_action_ok), () -> NBSnackbar.instance().dismissSnackBar());
    }

    @Override
    public void handleProgressBar(boolean isVisible) {
        setEnabledActivityTouch(!isVisible);
    }

    @Override
    public RecipientViewModel getmRecipientViewModel() {
        return getArguments() != null ? getArguments().getParcelable(Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL) : null;
    }


    protected boolean isBankApprovedBeneficiary() {
        if (null != mRecipientViewModel && null != mRecipientViewModel.getBankAccountViewDataModelList()
                && mRecipientViewModel.getBankAccountViewDataModelList().size() > 0
                && mRecipientViewModel.getBankAccountViewDataModelList().get(0) instanceof BankAccountViewDataModel) {
            BankAccountViewDataModel bankAccountVM = (BankAccountViewDataModel) mRecipientViewModel.getBankAccountViewDataModelList().get(0);
            return BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF.equalsIgnoreCase(bankAccountVM.getBeneficiaryType());

        }
        return false;
    }

    protected void setupRecyclerViews() {
        boolean isBankApprovedBeneficiary = isBankApprovedBeneficiary();
        //prepare account model
        mBankAccountNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.bank_accounts));
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_bank_account));
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_other_bank_account));
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.BANK);
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_bank_account_item_layout);
        if (isBankApprovedBeneficiary) {
            mBankAccountNbFlexibleItemCountRecyclerviewModel.setFooterShow(false);
        } else {

            //prepare mobile number model
            mMobileNumberNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.mobile_num));
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_mobile_number));
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_other_mobile_number));
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.MOBILE_NUMBER);
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_mobile_number_item_layout);

            //prepare shap id model
            mShapIdNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mShapIdNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.caps_pay_shap));
            mShapIdNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_shapid));
            mShapIdNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_shapid));
            mShapIdNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.SHAPID);
            mShapIdNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_shapid_item_layout);

            //prepare credit card model
            mCreditCardNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.credit_card));
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_credit_card_number));
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_credit_card_number));
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.CREDIT_CARD);
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_credit_card_item_layout);

            //prepare electricity model
            mElectricityNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mElectricityNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.electricity_water_metre));
            mElectricityNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_meter_number));
            mElectricityNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_other_meter_number));
            mElectricityNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.ELECTRICITY_METER);
            mElectricityNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_electricity_item_layout);

            //Prepare RPP model
            mRPPNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mRPPNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.pay_shap));
            mRPPNbFlexibleItemCountRecyclerviewModel.setFooterShow(false);
            mRPPNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_rpp_account));
            mRPPNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.SHAPID);
            mRPPNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_rpp_item_layout);

            //prepare email model
            mEmailNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mEmailNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.email));
            mEmailNbFlexibleItemCountRecyclerviewModel.setFooterShow(false);
            mEmailNbFlexibleItemCountRecyclerviewModel.setDeleteButtonShow(false);
            mEmailNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_email_address));
            mEmailNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_other_email_address));
            mEmailNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.EMAIL);
            mEmailNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_email_item_layout);
        }
        setUpRecyclerViewAdapter();
    }


    protected void setUpRecyclerViewAdapter() {

        if (mRecipientViewModel != null) {
            if (mBankAccountNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getBankAccountViewDataModelList() != null && mRecipientViewModel.getBankAccountViewDataModelList().size() > 0) {
                rvAccount = addRecyclerView();
                mBankAccountViewDataModelList = mRecipientViewModel.getBankAccountViewDataModelList();
                mBankAccountAdapter = new BankAccountAdapter(getContext(), mBankAccountNbFlexibleItemCountRecyclerviewModel, mBankAccountViewDataModelList, null, itemSelectedListener);
                mBankAccountAdapter.setEditable(false);
                rvAccount.addItemDecoration(new SimpleDividerItemDecoration(getContext()));
                rvAccount.setAdapter(mBankAccountAdapter);
            }

            if (mMobileNumberNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getMobileNumberViewDataModelList() != null && mRecipientViewModel.getMobileNumberViewDataModelList().size() > 0) {
                rvMobileNumber = addRecyclerView();
                mMobileNumberViewDataModelList = mRecipientViewModel.getMobileNumberViewDataModelList();
                mMobileNumberAdapter = new MobileNumberAdapter(getContext(), mMobileNumberNbFlexibleItemCountRecyclerviewModel,
                        mMobileNumberViewDataModelList, null, itemSelectedListener);
                mMobileNumberAdapter.setEditable(false);
                rvMobileNumber.addItemDecoration(new SimpleDividerItemDecoration(getContext()));
                rvMobileNumber.setAdapter(mMobileNumberAdapter);
            }

            if (mElectricityNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getElectricityMeterViewDataModelList() != null && mRecipientViewModel.getElectricityMeterViewDataModelList().size() > 0) {
                rvElectricity = addRecyclerView();
                mElectricityMeterViewDataModelList = mRecipientViewModel.getElectricityMeterViewDataModelList();
                mElectricityMeterAdapter = new ElectricityMeterAdapter(getContext(), mElectricityNbFlexibleItemCountRecyclerviewModel,
                        mElectricityMeterViewDataModelList, null, recipientDetailsPresenter.isFatcaRestrictionsApplicable() ? itemSelectedListener : null);
                mElectricityMeterAdapter.setEditable(false);
                rvElectricity.addItemDecoration(new SimpleDividerItemDecoration(getContext()));
                rvElectricity.setAdapter(mElectricityMeterAdapter);
            }

            if (mRPPNbFlexibleItemCountRecyclerviewModel != null && CollectionUtils.isNotEmpty(mRecipientViewModel.getShapIdViewDataModelList())) {
                rvRppPayment = addRecyclerView();
                mRPPViewDataModelList = mRecipientViewModel.getShapIdViewDataModelList();
                mRppPaymentAdapter = new RppPaymentAdapter(getContext(), mRPPNbFlexibleItemCountRecyclerviewModel,
                        mRPPViewDataModelList, null, itemSelectedListener);
                mRppPaymentAdapter.setEditable(false);
                rvRppPayment.addItemDecoration(new SimpleDividerItemDecoration(getContext()));
                rvRppPayment.setAdapter(mRppPaymentAdapter);
            }

           if (mEmailNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getEmailViewDataModelList() != null && mRecipientViewModel.getEmailViewDataModelList().size() > 0) {
                rvEmail = addRecyclerView();
                mEmailViewDataModelList = mRecipientViewModel.getEmailViewDataModelList();
                mEmailAdapter = new EmailAdapter(getContext(), mEmailNbFlexibleItemCountRecyclerviewModel, mEmailViewDataModelList, null, null);
                mEmailAdapter.setEditable(false);
                rvEmail.setAdapter(mEmailAdapter);
            }

            if (mCreditCardNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getCreditCardViewDataModelList() != null && mRecipientViewModel.getCreditCardViewDataModelList().size() > 0) {
                rvCreditCard = addRecyclerView();
                mCreditCardViewDataModelList = mRecipientViewModel.getCreditCardViewDataModelList();
                mCreditCardAdapter = new CreditCardAdapter(getContext(), mCreditCardNbFlexibleItemCountRecyclerviewModel,
                        mCreditCardViewDataModelList, null, itemSelectedListener);
                mCreditCardAdapter.setEditable(false);
                rvCreditCard.addItemDecoration(new SimpleDividerItemDecoration(getContext()));
                rvCreditCard.setAdapter(mCreditCardAdapter);
            }

        }
    }

    private NBFlexibleItemCountRecyclerView addRecyclerView() {
        View view = getLayoutInflater().inflate(R.layout.recycler_view_item, binding.parent, false);
        NBFlexibleItemCountRecyclerView nbFlexibleItemCountRecyclerView = view.findViewById(R.id.rv_item);
        binding.parent.addView(view);
        return nbFlexibleItemCountRecyclerView;
    }


    public void setEditedEmailAddress(RecipientViewModel recipientViewModel) {
        if (recipientViewModel != null && CollectionUtils.isNotEmpty(recipientViewModel.getEmailViewDataModelList())) {
            if (mEmailAdapter == null) {
                mEmailViewDataModelList = recipientViewModel.getEmailViewDataModelList();
                if (mRecipientViewModel != null) {
                    mRecipientViewModel.setEmailViewDataModelList(recipientViewModel.getEmailViewDataModelList());
                    setEmailAdapter();
                }
            } else if (mEmailViewDataModelList != null && CollectionUtils.isNotEmpty(recipientViewModel.getEmailViewDataModelList())) {
                mEmailViewDataModelList.clear();
                if (recipientViewModel.getEmailViewDataModelList().size() > 0) {
                    mEmailViewDataModelList.add(recipientViewModel.getEmailViewDataModelList().get(0));
                }
                mEmailAdapter.notifyDataSetChanged();
            }

        }
    }


    public void setEmailAdapter() {
        if (mEmailNbFlexibleItemCountRecyclerviewModel != null && CollectionUtils.isNotEmpty(mRecipientViewModel.getEmailViewDataModelList())) {
            rvEmail = addRecyclerView();
            mEmailViewDataModelList = mRecipientViewModel.getEmailViewDataModelList();
            mEmailAdapter = new EmailAdapter(getContext(), mEmailNbFlexibleItemCountRecyclerviewModel, mEmailViewDataModelList, null, null);
            mEmailAdapter.setEditable(false);
            rvEmail.setAdapter(mEmailAdapter);
        }
    }

    public void setEditedPhoneNumber(RecipientViewModel recipientViewModel) {
        if (recipientViewModel != null && CollectionUtils.isNotEmpty(recipientViewModel.getMobileNumberViewDataModelList())) {
            if (mMobileNumberAdapter == null) {
                mMobileNumberViewDataModelList = recipientViewModel.getMobileNumberViewDataModelList();
                if (mRecipientViewModel != null) {
                    mRecipientViewModel.setMobileNumberViewDataModelList(recipientViewModel.getMobileNumberViewDataModelList());
                    setMobileAdapter();
                }
            } else if (mMobileNumberViewDataModelList != null) {
                mMobileNumberViewDataModelList.clear();
                if (CollectionUtils.isNotEmpty(recipientViewModel.getMobileNumberViewDataModelList())) {
                    mMobileNumberViewDataModelList.add(recipientViewModel.getMobileNumberViewDataModelList().get(0));
                    mMobileNumberAdapter.notifyDataSetChanged();
                }
            }
        }

    }

    public void setMobileAdapter() {
        if (mMobileNumberNbFlexibleItemCountRecyclerviewModel != null && CollectionUtils.isNotEmpty(mRecipientViewModel.getMobileNumberViewDataModelList())) {
            rvMobileNumber = addRecyclerView();
            mMobileNumberViewDataModelList = mRecipientViewModel.getMobileNumberViewDataModelList();
            mMobileNumberAdapter = new MobileNumberAdapter(getContext(), mMobileNumberNbFlexibleItemCountRecyclerviewModel,
                    mMobileNumberViewDataModelList, null, null);
            mMobileNumberAdapter.setEditable(false);
            rvMobileNumber.addItemDecoration(new SimpleDividerItemDecoration(getContext()));
            rvMobileNumber.setAdapter(mMobileNumberAdapter);
        }
    }

    private void setupResult(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE view_type, int itemPositionInSection) {
        SelectedUserBeneficiaryViewModel userBeneficiaryDetailsViewModel = null;
        if (mUserBeneficiaryCollectiveDataViewModel != null) {
            switch (view_type) {
                case BANK:
                    userBeneficiaryDetailsViewModel = getSelectedUserBeneficiaryViewModel(isValidBankBeneficiary(mUserBeneficiaryCollectiveDataViewModel.getBankBeneficiaryList(), itemPositionInSection), userBeneficiaryDetailsViewModel, mUserBeneficiaryCollectiveDataViewModel.getBankBeneficiaryList(), itemPositionInSection);
                    break;
                case CREDIT_CARD:
                    userBeneficiaryDetailsViewModel = getSelectedUserBeneficiaryViewModel(CollectionUtils.isNotEmpty(mUserBeneficiaryCollectiveDataViewModel.getCreditCardBeneficiaryList()), userBeneficiaryDetailsViewModel, mUserBeneficiaryCollectiveDataViewModel.getCreditCardBeneficiaryList(), itemPositionInSection);
                    break;
                case MOBILE_NUMBER:
                    userBeneficiaryDetailsViewModel = getSelectedUserBeneficiaryViewModel(CollectionUtils.isNotEmpty(mUserBeneficiaryCollectiveDataViewModel.getMobileBeneficiaryList()), userBeneficiaryDetailsViewModel, mUserBeneficiaryCollectiveDataViewModel.getMobileBeneficiaryList(), itemPositionInSection);
                    break;
                case ELECTRICITY_METER:
                    userBeneficiaryDetailsViewModel = getSelectedUserBeneficiaryViewModel(CollectionUtils.isNotEmpty(mUserBeneficiaryCollectiveDataViewModel.getElectricityBeneficiaryList()), userBeneficiaryDetailsViewModel, mUserBeneficiaryCollectiveDataViewModel.getElectricityBeneficiaryList(), itemPositionInSection);
                    break;
                case SHAPID:
                    userBeneficiaryDetailsViewModel = getSelectedUserBeneficiaryViewModel(CollectionUtils.isNotEmpty(mUserBeneficiaryCollectiveDataViewModel.getRppBeneficiaryList()), userBeneficiaryDetailsViewModel, mUserBeneficiaryCollectiveDataViewModel.getRppBeneficiaryList(), itemPositionInSection);
                    break;
                default:
                    //default case.
                    break;
            }

            if (userBeneficiaryDetailsViewModel != null) {
                if (CMAUtils.isCMABranch(featureSetController, userBeneficiaryDetailsViewModel.getUserBeneficiaryDetails().getBranchCode())) {
                    openPopForInterNationalPayment(userBeneficiaryDetailsViewModel.getUserBeneficiaryDetails().getBankName());
                } else {
                    recipientDetailsPresenter.navigateToInitiatePayScreen(userBeneficiaryDetailsViewModel);
                    recipientDetailsPresenter.trackAction();
                }
            }
        }
    }

    private void openPopForInterNationalPayment(String bankName) {
        recipientDetailsPresenter.trackCmaActionOnOpen(bankName);
        final PaymentPayzarFlowInfoDialog paymentPayzarFlowInfoDialog = PaymentPayzarFlowInfoDialog.getInstance(new IDialog() {
            @Override
            public void onPositiveButtonClick() {
                recipientDetailsPresenter.fetchProfileForInternationalPaymentRecipientFlow(TrackingEvent.ANALYTICS.CMA_POP_UP);
                recipientDetailsPresenter.trackCmaActionOnContinueClick();
            }

            @Override
            public void onNegativeButtonClick() {
                // Handle cancel event if required
                recipientDetailsPresenter.trackCmaActionOnCancelClick();
            }
        });
        if (!paymentPayzarFlowInfoDialog.isVisible()) {
            paymentPayzarFlowInfoDialog.showAllowingStateLoss(getActivity().getSupportFragmentManager(), PaymentPayzarFlowInfoDialog.TAG);
        }
    }
    private SelectedUserBeneficiaryViewModel getSelectedUserBeneficiaryViewModel(boolean mUserBeneficiaryCollectiveDataViewModel, SelectedUserBeneficiaryViewModel userBeneficiaryDetailsViewModel,
                                                                                 List<UserBeneficiaryDetailsViewModel> mUserBeneficiaryCollectiveDataViewModel1, int itemPositionInSection) {
        if (mUserBeneficiaryCollectiveDataViewModel) {
            userBeneficiaryDetailsViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel1.get(itemPositionInSection));
        }
        return userBeneficiaryDetailsViewModel;
    }

    private boolean isValidBankBeneficiary(List<UserBeneficiaryDetailsViewModel> beneficiaryDetailsViewModels, int itemPositionInSection) {
        return CollectionUtils.isNotEmpty(beneficiaryDetailsViewModels)
                && itemPositionInSection < beneficiaryDetailsViewModels.size();
    }

    public void handleFicaResponse(Bundle extras) {
        if(recipientDetailsPresenter!=null){
            recipientDetailsPresenter.handleFicaResponse(extras.getParcelable(RecipientDetailsPresenter.FICA_RESPOSNE), (NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE) extras.get(RecipientDetailsPresenter.VIEW_TYPE),extras.getInt(RecipientDetailsPresenter.POSITION) );
        }
    }
}


