/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository.money_request;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.ui.data.datastore.MoneyRequestsFactory;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsActionResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentActionRequestEntity;
import za.co.nedbank.ui.domain.repository.IMoneyRequestsActionRepository;

/**
 * Created by swapnil.gawande on 2/25/2018.
 */

public class MoneyRequestsActionRepository implements IMoneyRequestsActionRepository {
    private final MoneyRequestsFactory moneyRequestsFactory;

    @Inject
     MoneyRequestsActionRepository(final MoneyRequestsFactory moneyRequestsFactory) {
        this.moneyRequestsFactory = moneyRequestsFactory;
    }

    @Override
    public Observable<MoneyRequestsActionResponseEntity> paymentRequestsAction(PaymentActionRequestEntity paymentActionRequestEntity) {
        return moneyRequestsFactory.paymentRequestsAction(paymentActionRequestEntity);
    }
}
