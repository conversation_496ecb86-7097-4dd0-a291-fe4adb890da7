package za.co.nedbank.ui.view.home.investments;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.lang.ref.WeakReference;
import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.databinding.AccountSummeryRowBinding;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.services.Constants;
import za.co.nedbank.services.domain.model.account.AccountSummary;

public class AccountTypeInvestmentViewHolder extends RecyclerView.ViewHolder {

    private AccountSummary accountSummary;
    private int position;
    private WeakReference<AccountTypeRowInterface> rowInterface;
    AccountSummeryRowBinding binding;

    public AccountTypeInvestmentViewHolder(@NonNull AccountSummeryRowBinding binding) {
        super(binding.getRoot());
        this.binding = binding;
    }

    public void bind(AccountSummary accountSummary, WeakReference<AccountTypeRowInterface> rowInterface, int position, List<AccountSummary> items, Context mContext) {
        if (accountSummary != null) {
            this.accountSummary = accountSummary;
            if (!TextUtils.isEmpty(accountSummary.getGoalName())) {
                binding.tvAccountName.setText(TextUtils.concat(accountSummary.getName() + StringUtils.SPACE + StringUtils.OPEN_BRACE + accountSummary.getGoalName() + StringUtils.CLOSE_BRACE));
            } else {
                binding.tvAccountName.setText(accountSummary.getName());
            }
            this.position = position;
            this.rowInterface = rowInterface;
            binding.tvAvailableAmount.setText(FormattingUtil.convertToSouthAfricaFormattedCurrency(accountSummary.getMarketValue()));
            binding.llAccTypeRow.setOnClickListener(v -> onOptionSelected());
            if (accountSummary.getAccountCode().equals(Constants.ACCOUNT_TYPE_NGI)) {
                setListHeader(items, mContext);
            } else {
                binding.tvListHeader.setVisibility(View.GONE);
            }
        }
    }

    private void setListHeader(List<AccountSummary> items, Context mContext) {
        if (position == 0) {
            binding.tvListHeader.setVisibility(View.VISIBLE);
            if (items.get(position).getGoalName() != null) {
                binding.tvListHeader.setText(mContext.getString(R.string.target_funds));
            } else {
                binding.tvListHeader.setText(mContext.getString(R.string.funds));

            }

        } else if (items.get(position - 1).getGoalName() != null && items.get(position).getGoalName() == null) {
            binding.tvListHeader.setVisibility(View.VISIBLE);
            binding.tvListHeader.setText(mContext.getString(R.string.funds));
        } else {
            binding.tvListHeader.setVisibility(View.GONE);
        }
    }


    public void onOptionSelected() {
        if (rowInterface != null) {
            rowInterface.get().onAccountClick(this.accountSummary, position);
        }
    }
}

