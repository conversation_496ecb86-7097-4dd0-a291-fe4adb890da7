package za.co.nedbank.ui.view.pop;

import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import za.co.nedbank.core.base.adapter.Section;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.databinding.ItemTransactionHistorySectionBinding;

class TransactionHistorySectionViewHolder extends RecyclerView.ViewHolder {
    TextView sectionName;

    public TransactionHistorySectionViewHolder(ItemTransactionHistorySectionBinding binding) {
        super(binding.getRoot());
        sectionName = binding.sectionName;
    }

    public void setup(Section section) {
        if (sectionName!=null && section != null && StringUtils.isNotEmpty(section.getName()))
            sectionName.setText(section.getName());
    }
}
