package za.co.nedbank.ui.view.pop;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;
import za.co.nedbank.databinding.ActivitySharePopMethodsBinding;
import za.co.nedbank.ui.di.AppDI;

public class SharePopMethodsActivity extends NBBaseActivity implements SharePopMethodsView, SharePOPMethodListAdapter.IItemClickCallback {
    @Inject
    public SharePopMethodsPresenter mPresenter;
    private String mSelectedSharePOPMethod;
    private ActivitySharePopMethodsBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        super.onCreate(savedInstanceState);
        binding = ActivitySharePopMethodsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        readIntentData();
        if (isFromPaymentsFlow()) {
            initToolbar(binding.toolbar, true, getString(R.string.notifications));
        } else {
            initToolbar(binding.toolbar, true, getString(R.string.share_pop));
        }
        setPricingSpan();
    }

    private void setPricingSpan() {
       String fullString = getString(R.string.share_pop_info_details);
       String clickableString = getString(R.string.latest_price_guide);
       SpannableString tncInfoSpan = new SpannableString(fullString);
       ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(View textView) {
                mPresenter.handleOpenPricingGuideline();
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setUnderlineText(true);
                ds.setTypeface(Typeface.DEFAULT);
                ds.setColor(ContextCompat.getColor(SharePopMethodsActivity.this, R.color.green));
            }
        };
        tncInfoSpan.setSpan(clickableSpan, fullString.indexOf(clickableString),
                fullString.indexOf(clickableString) + clickableString.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        binding.popInfoTab.setText(tncInfoSpan);
        binding.popInfoTab.setMovementMethod(LinkMovementMethod.getInstance());
    }

    @Override
    protected void onResume() {
        super.onResume();
        mPresenter.bind(this);
        mPresenter.getSharePOPMethod();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mPresenter.unbind();
    }

    private void readIntentData() {
        if (null != getIntent() && getIntent().hasExtra(Constants.BUNDLE_KEYS.IS_FROM_TRANSACTION_DETAILS)) {
            mPresenter.setFromTransactionDetailActivity(getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_FROM_TRANSACTION_DETAILS, false));
        }
    }


    @Override
    public void receiveSharePOPMethodList(List<String> sharePOPMethodList) {
        binding.layoutRecyclerViewWithProgressbar.recyclerview.setItemAnimator(null);
        binding.layoutRecyclerViewWithProgressbar.recyclerview.addItemDecoration(new DividerItemDecoration(SharePopMethodsActivity.this, DividerItemDecoration.VERTICAL));
        binding.layoutRecyclerViewWithProgressbar.recyclerview.setHasFixedSize(true);
        binding.layoutRecyclerViewWithProgressbar.recyclerview.setNestedScrollingEnabled(false);
        binding.layoutRecyclerViewWithProgressbar.recyclerview.setLayoutManager(new LinearLayoutManager(SharePopMethodsActivity.this));
        SharePOPMethodListAdapter accountTypeListAdapter = new SharePOPMethodListAdapter(mSelectedSharePOPMethod, sharePOPMethodList, SharePopMethodsActivity.this);
        binding.layoutRecyclerViewWithProgressbar.recyclerview.setAdapter(accountTypeListAdapter);
    }

    @Override
    public void setActivityResult(String sharePOPMethod) {
        Bundle bundle = new Bundle();
        bundle.putString(za.co.nedbank.core.Constants.BUNDLE_KEYS.SELECTED_SHARE_POP_METHOD, sharePOPMethod);
        Intent intent = new Intent();
        intent.putExtras(bundle);
        setResult(Activity.RESULT_OK, intent);
    }

    @Override
    public void finishScreen() {
        finish();
    }

    @Override
    public TransactionHistoryViewModel getTransactionHistoryViewModel() {
        return getIntent() != null && getIntent().hasExtra(Constants.BUNDLE_KEYS.TRANSACTION_HISTORY_VIEW_MODEL) ? getIntent().getParcelableExtra(za.co.nedbank.core.Constants.BUNDLE_KEYS.TRANSACTION_HISTORY_VIEW_MODEL) : null;
    }

    @Override
    public boolean isFromRecentPaymentFlow() {
        return getIntent() != null && getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_RECENT_PAYMENT_FLOW, false);
    }


    @Override
    public boolean isSopFromPayDoneFlow() {
        return getIntent() != null && getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_SOP_FROM_PAY_DONE, false);
    }

    @Override
    public boolean isBeyond90Days() {
        return getIntent() != null && getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_BEYOND_90_DAYS_TRANSACTION, false);
    }

    @Override
    public boolean isNavigateToOverView() {
        return getIntent() != null && getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_LANDING_ON_OVERVIEW, false);
    }

    @Override
    public boolean isFromPaymentsFlow() {
        return getIntent() != null && getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.FROM_PAYMENTS_FLOW, false);
    }


    @Override
    public void onItemClick(int position, String sharePOPMethod) {
        mPresenter.handleSharePOPRowClick(sharePOPMethod);
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
    }
}