/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.non_tp_client.non_tp_apply;

import android.content.Context;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.ViewPager;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;

import za.co.nedbank.R;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.sharedui.listener.IViewPagerChildClickListener;
import za.co.nedbank.core.view.CarouselLinearLayout;
import za.co.nedbank.core.view.accounts.listener.IViewHolderInteraction;

public class NonTpApplyCyclicPagerAdapter extends FragmentStatePagerAdapter implements ViewPager.OnPageChangeListener {

    private float BIG_SCALE;
    private float SMALL_SCALE;
    private float DIFF_SCALE;
    private static final String TAG = NonTpApplyCyclicPagerAdapter.class.getSimpleName();
    private final Context mContext;
    private int mSelectedPosition = 1;

    private final HashMap<Integer, Fragment> mPageReferenceMap;
    private int previousState;
    private boolean isScrolledByUser;
    private IViewPagerChildClickListener mIViewPagerChildClickListener;
    private static final int MAX_PAGES = 300;
    public ArrayList<NonTpCyclicPagerData> fragments = new ArrayList<>();
    @Nullable
    public ArrayList<NonTpCyclicPagerData> fragmentsRaw;
    private boolean isEnable  = false;

    public NonTpApplyCyclicPagerAdapter(Context context, FragmentManager fm, IViewPagerChildClickListener iViewPagerChildClickListener, boolean isEnable) {
        super(fm);
        this.mContext = context;
        mPageReferenceMap = new HashMap<>();
        this.mIViewPagerChildClickListener = iViewPagerChildClickListener;
        BIG_SCALE = 1.0f;
        DIFF_SCALE = 0.15f;
        SMALL_SCALE = BIG_SCALE - DIFF_SCALE;
        this.isEnable = isEnable;

        itemActualList();

    }

    @Override
    public Fragment getItem(int position) {
        // make the first pager bigger than others
        boolean isEnabled;
        float scale;
        if (position == mSelectedPosition) {
            scale = BIG_SCALE;
            isEnabled = true;
        } else {
            scale = SMALL_SCALE;
            isEnabled = false;
        }

        Fragment fragment = getFragment(position);
        if (fragment == null) {
            fragment = NonTpApplyPagerItemFragment.newInstance(mContext, fragments.get(position), scale, isEnabled, getRealPagePosition(position), new WeakReference<>(mIViewPagerChildClickListener), isEnable);
            mPageReferenceMap.put(position, fragment);
        }
        return fragment;
    }

    private Fragment getFragment(int key) {
        return mPageReferenceMap.get(key);
    }

    @Override
    public int getCount() {

        return fragments.size();
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        try {
            if (positionOffset >= 0f && positionOffset <= 1f) {
                CarouselLinearLayout cur = getRootView(position);
                if (cur != null) {
                    cur.setScaleBoth(BIG_SCALE - DIFF_SCALE * positionOffset);
                }

                if (getCount() > position + 1) {
                    CarouselLinearLayout next = getRootView(position + 1);
                    if (next != null) {
                        next.setScaleBoth(SMALL_SCALE + DIFF_SCALE * positionOffset);
                    }
                }
            }
        } catch (Exception e) {
            NBLogger.e(TAG, Log.getStackTraceString(e));
        }
    }

    @Override
    public void onPageSelected(int position) {
        mSelectedPosition = position;
        if (position > 0) {
            View view = getChild(position - 1);
            if (view != null) {
                view.setSelected(false);
            }
        }
        if (position < getCount() - 1) {
            View view = getChild(position + 1);
            if (view != null) {
                view.setSelected(false);
            }
        }

        NBLogger.d(TAG, "isScrolledByUser " + isScrolledByUser);
        if (mContext instanceof IViewHolderInteraction && isScrolledByUser) {
            ((IViewHolderInteraction) mContext).onParentViewSelected(position, true);
        }

        View view = getChild(mSelectedPosition);
        if (view != null) {
            view.setSelected(true);
        }

    }

    @Override
    public void onPageScrollStateChanged(int state) {
        if (previousState == ViewPager.SCROLL_STATE_DRAGGING
                && state == ViewPager.SCROLL_STATE_SETTLING) {
            isScrolledByUser = true;
        } else if (previousState == ViewPager.SCROLL_STATE_SETTLING
                && state == ViewPager.SCROLL_STATE_IDLE) {
            isScrolledByUser = false;
        }
        previousState = state;
    }

    private CarouselLinearLayout getRootView(int position) {
        if (getFragment(position) != null && getFragment(position).getView() != null) {
            return (CarouselLinearLayout) getFragment(position)
                    .getView().findViewById(R.id.clnrContainerNonTpApply);
        }
        return null;
    }

    private View getChild(int position) {
        Fragment fragment = getFragment(position);
        if (null != fragment) {
            View fragmentView = fragment.getView();
            if (null != fragmentView) {
                return fragmentView.findViewById(R.id.ll_parent_login);
            }
        }
        return null;
    }

    @Override
    public float getPageWidth(int position) {
        return .75f;
    }

    public void addItems() {
        if (fragments.isEmpty()) {
            return;
        }

        ArrayList<NonTpCyclicPagerData> arrayList = new ArrayList<>(Arrays.asList(new NonTpCyclicPagerData[MAX_PAGES]));
        arrayList.addAll(MAX_PAGES / 2, fragments);

        int mrPointer = fragments.size() - 1;
        for (int i = MAX_PAGES / 2 - 1; i > -1; --i) {
            arrayList.set(i, fragments.get(mrPointer));
            --mrPointer;
            if (mrPointer < 0) {
                mrPointer = fragments.size() - 1;
            }
        }

        mrPointer = 0;
        for (int i = MAX_PAGES / 2 + fragments.size(); i < arrayList.size(); ++i) {
            arrayList.set(i, fragments.get(mrPointer));
            ++mrPointer;
            if (mrPointer >= fragments.size()) {
                mrPointer = 0;
            }
        }
        fragmentsRaw = fragments;
        fragments = arrayList;
    }

    public int getRealPagePosition(int index) {
        if (fragments.isEmpty() || fragmentsRaw == null) {
            return 0;
        }
        final NonTpCyclicPagerData fragmentCreator = fragments.get(index);
        for (int i = 0; i < fragmentsRaw.size(); ++i) {
            if (fragmentCreator == fragmentsRaw.get(i)) {
                return i;
            }
        }
        return 0;
    }


    public void itemActualList() {
        fragments = new ArrayList<>();
        for (int i = 0; i < NonTpCyclicPagerData.values().length; i++) {
            fragments.add(NonTpCyclicPagerData.values()[i]);
        }
        addItems();
    }
}