package za.co.nedbank.ui.view.notification.notification_details;

import androidx.annotation.StringRes;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.investmentonline.model.NoticesDataViewModel;
import za.co.nedbank.core.investmentonline.model.NoticesResponseViewModel;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.enroll_v2.view.model.fica.ClientDeviceInfoRequestViewModel;

public interface NotificationDetailsView extends NBBaseView {


    void showProgressVisible(boolean visibility);

    void clearNotificationFromTray(FBNotificationsViewModel fbNotificationsViewModel);

    void finishAffinity();


    ClientDeviceInfoRequestViewModel getClientDeviceInfoRequestViewModel();

    void showRichContent(FBNotificationsViewModel.RichContent richContent, String displayType);

    void showImageLoading(boolean isLoading);

    void showImageLoadingFailure();

    void setCorousalData(List<FBNotificationsViewModel.RichContent> urlList, String mDisplayType);

    void showEmbeddedContent(List<FBNotificationsViewModel.RichContent> richContentList);

    NoticesDataViewModel filterNoticesDataViewModel(NoticesResponseViewModel mapDataModelToViewModel, String noticeId);

    String getCMSBasePath(String sourceCms);

    void displayNotificationDetails();

    boolean isLandscape();

    void setRichContentVideo(String videoUrl,String displayType);
}
