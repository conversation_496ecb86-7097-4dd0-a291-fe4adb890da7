package za.co.nedbank.ui.view.card_delivery.branch_confirmation;

import android.annotation.SuppressLint;

import java.util.HashMap;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.card_delivery.PostCardDeliveryOptionRequestData;
import za.co.nedbank.core.domain.model.card_delivery.PostCardDeliveryOptionsResponseData;
import za.co.nedbank.core.domain.model.location.PlaceDetails;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.card_delivery.PostCardDeliveryOptionUseCase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.navigation.NavigationResult;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.card_delivery.CardDeliveryOptionsEnum;
import za.co.nedbank.enroll_v2.domain.usecases.card_delivery.GetDomicileBranchUseCase;
import za.co.nedbank.services.domain.usecase.cards.ReplaceCardUseCase;
import za.co.nedbank.services.domain.usecase.profile.GetDefaultBranchUseCase;
import za.co.nedbank.services.view.mapper.ReplaceCardViewModelToDataMapper;
import za.co.nedbank.services.view.model.ReplaceCardViewModel;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConfirmationBasePresenter;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryUtils;

public class CardDeliveryBranchConfirmationPresenter extends CardDeliveryConfirmationBasePresenter<CardDeliveryBranchConfirmationView> {

    private final GetDomicileBranchUseCase getDomicileBranchUseCase;
    private final GetDefaultBranchUseCase getDefaultBranchUseCase;
    private final ApplicationStorage appStorage;
    private String currentBranchCode;
    private String defaultBranchCode;

    @Inject
    public CardDeliveryBranchConfirmationPresenter(PostCardDeliveryOptionUseCase postCardDeliveryOptionUseCase,
                                                   GetDomicileBranchUseCase domicileBranchUseCase,
                                                   GetDefaultBranchUseCase getDefaultBranchUseCase,
                                                   ReplaceCardUseCase replaceCardUseCase,
                                                   ReplaceCardViewModelToDataMapper replaceCardViewModelToDataMapper,
                                                   NavigationRouter navigationRouter,
                                                   @Named("memory") ApplicationStorage applicationStorage,
                                                   Analytics analytics,
                                                   ErrorHandler errorHandler) {
        super(postCardDeliveryOptionUseCase, replaceCardUseCase, replaceCardViewModelToDataMapper, navigationRouter, applicationStorage, analytics, errorHandler);
        this.getDomicileBranchUseCase = domicileBranchUseCase;
        this.getDefaultBranchUseCase = getDefaultBranchUseCase;
        this.appStorage = applicationStorage;
    }

    public void confirmClick() {
        sendConfirmBranchAnalytics();
        handleConfirmation();
    }


    @SuppressLint("CheckResult")
    public void chooseDifferentBranch() {
        //open branch locator
        navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.BRANCH_PROFILE_SELECT)
                .withAllData(true)
                .withParam(Constants.BUNDLE_KEYS.IS_CARD_DELIVERY_FLOW, true))
                .subscribe(navigationResult -> {
                    if (navigationResult.isOk()) {
                        currentBranchCode = navigationResult.getStringParam(NavigationTarget.RESULT_BRANCH_ID);
                        updateUi(navigationResult);
                        sendPageAnalytics(CardDeliveryAnalytics.VAL_CONFIRM_BRANCH_DETAILS, CardDeliveryAnalytics.VAL_DELIVERY_TO_A_NEDBANK_BRANCH);
                    } else {
                        //in case of no domicile branch or previously selected branch
                        if (StringUtils.isNullOrEmpty(currentBranchCode)) {
                            view.close();
                        }
                    }
                });
    }

    private void updateUi(NavigationResult navigationResult) {
        String branchName = navigationResult.getStringParam(NavigationTarget.RESULT_BRANCH_NAME);
        String branchAddress = navigationResult.getStringParam(NavigationTarget.RESULT_BRANCH_ADDRESS);
        String suburb = navigationResult.getStringParam(NavigationTarget.RESULT_BRANCH_SUBURB);
        String province = navigationResult.getStringParam(NavigationTarget.RESULT_BRANCH_PROVINCE);

        view.updateBranchNameAddress(branchName,
                StringUtils.joinNonEmptyStrings(StringUtils.COMA_SEPARATOR,
                        branchAddress, suburb, province));
    }


    @Override
    protected ReplaceCardViewModel createReplaceCardRequestEntity() {
        return new ReplaceCardViewModel(view.getCardPlasticId(), currentBranchCode);
    }

    @Override
    public PostCardDeliveryOptionRequestData createPostDeliveryOptionRequestEntity() {
        PostCardDeliveryOptionRequestData requestData = new PostCardDeliveryOptionRequestData();
        requestData.setBranchCode(currentBranchCode);
        requestData.setDeliveryOptionCode(CardDeliveryOptionsEnum.BRANCH_PICK.getValue());
        String sessionId = appStorage.getString(StorageKeys.FICA_SESSION_ID, StringUtils.EMPTY_STRING);
        requestData.setSessionId(sessionId);
        return requestData;
    }

    public void loadDefaultBranch() {
        if (isEficaFlow()) {
            loadDomicileBranch();
        } else if (isReplaceCardFlow()) {
            getDefaultBranchById(view.getDefaultBranchId());
        }
    }

    private void loadDomicileBranch() {
        getDomicileBranchUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(d -> view.showLoading(true))
                .doFinally(() -> view.showLoading(false))
                .subscribe(response -> {
                    if (CardDeliveryUtils.isSuccess(response.getMetaDataModel())
                            && StringUtils.isNotEmpty(response.getBranchData().getBranchCode())) {
                        getDefaultBranchById(response.getBranchData().getBranchCode());
                    } else {
                        handleDefaultBranchError(new Throwable());
                    }
                }, this::handleDefaultBranchError);
    }

    public void getDefaultBranchById(String branchCode) {
        getDefaultBranchUseCase.execute(branchCode)
                .compose(bindToLifecycle())
                .doOnSubscribe(d -> view.showLoading(true))
                .doFinally(() -> view.showLoading(false))
                .subscribe(this::handleBranchResult,
                        this::handleDefaultBranchError);
    }

    private void handleDefaultBranchError(Throwable throwable) {
        chooseDifferentBranch();
    }

    private void handleBranchResult(PlaceDetails branch) {
        if (StringUtils.isNotEmpty(branch.id)
                && StringUtils.isNotEmpty(branch.name)) {
            currentBranchCode = branch.id;
            defaultBranchCode = currentBranchCode;
            view.updateBranchNameAddress(branch.name,
                    StringUtils.joinNonEmptyStrings(StringUtils.COMA_SEPARATOR,
                            branch.address, branch.suburb, branch.town, branch.postalCode));
            sendPageAnalytics(CardDeliveryAnalytics.VAL_DEFAULT_BRANCH, CardDeliveryAnalytics.VAL_DELIVERY_TO_A_NEDBANK_BRANCH);
        } else {
            chooseDifferentBranch();
        }
    }

    public void sendChangeBranchAnalytics() {
        AdobeContextData contextData = createConfirmBranchEventContextData();
        analytics.sendEventActionWithMap(CardDeliveryAnalytics.EVENT_CM_CHANGE_BRANCH, contextData.getCdata());
    }

    private void sendConfirmBranchAnalytics() {

        AdobeContextData contextData = createConfirmBranchEventContextData();
        String defaultBranchSelected = StringUtils.equals(defaultBranchCode, currentBranchCode)
                ? CardDeliveryAnalytics.VAL_YES : CardDeliveryAnalytics.VAL_NO;
        contextData.setContext1(String.format(CardDeliveryAnalytics.DEFAULT_BRANCH_CONTEXT, defaultBranchSelected));
        analytics.sendEventActionWithMap(CardDeliveryAnalytics.EVENT_CM_CONFIRM_BRANCH, contextData.getCdata());
    }


    private AdobeContextData createConfirmBranchEventContextData() {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);
        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());

        if (isEficaFlow()) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);

        } else if (isReplaceCardFlow()) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            contextData.setFeature(view.getCardActionName());

        }

        contextData.setSubFeature(view.getCardDeliverySubFeature());
        return contextData;
    }

    @Override
    protected void showEficaCardDeliverConfirmationResult(PostCardDeliveryOptionsResponseData responseData) {
        super.showEficaCardDeliverConfirmationResult(responseData);
        /*analytics for next page*/
        sendPageAnalytics(CardDeliveryAnalytics.VAL_BRANCH_DETAILS_CONFIRMED, CardDeliveryAnalytics.VAL_DELIVERY_TO_A_NEDBANK_BRANCH);
    }
}
