package za.co.nedbank.ui.view.retention.notification_journey;

import android.util.Log;

import java.util.HashMap;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.enrol.LoginSecurityUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.CheckIfUserAdminUseCase;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.tracking.appsflyer.AddContextData;
import za.co.nedbank.core.tracking.appsflyer.AppsFlyerTags;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.profile.view.navigation.ProfileNavigationTarget;
import za.co.nedbank.ui.view.retention.RetentionConstants;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class RetentionNotificationJourneyPresenter extends NBBasePresenter<RetentionNotificationJourneyView> {

    private final NavigationRouter navigationRouter;
    private final ApplicationStorage applicationStorage;
    private final CheckIfUserAdminUseCase checkIfUserAdminUseCase;
    private final LoginSecurityUseCase loginSecurityUseCase;
    private final Analytics mAnalytics;
    private final AFAnalyticsTracker mAfAnalyticsTracker;

    @Inject
    public RetentionNotificationJourneyPresenter(NavigationRouter navigationRouter, final ApplicationStorage applicationStorage,
                                                 final CheckIfUserAdminUseCase checkIfUserAdminUseCase, final LoginSecurityUseCase loginSecurityUseCase
            , final Analytics analytics, final AFAnalyticsTracker afAnalyticsTracker) {
        this.navigationRouter = navigationRouter;
        this.applicationStorage = applicationStorage;
        this.checkIfUserAdminUseCase = checkIfUserAdminUseCase;
        this.loginSecurityUseCase = loginSecurityUseCase;
        this.mAnalytics = analytics;
        this.mAfAnalyticsTracker = afAnalyticsTracker;
    }

    @Override
    protected void onBind() {
        super.onBind();
        sendPageEvent();
    }

    public void handleShareAccountTodoTaskClick() {

        mAnalytics.sendEvent(AppTracking.RETENTION_CARD_TODO_SHARE_ACCOUNT_INFO, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, true);

        navigateToMultipleAccount(Boolean.FALSE);
    }

    public void handleLoginSecurityTodoTaskClick() {

        mAnalytics.sendEvent(AppTracking.RETENTION_CARD_TODO_LOGIN_AND_SECURITY, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, true);
        loginSecurityUseCase.execute(Boolean.FALSE)
                .compose(bindToLifecycle()).subscribe(o -> {
        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void handleProfileLimitTodoTaskClick() {

        mAnalytics.sendEvent(AppTracking.RETENTION_CARD_TODO_PROFILE_LIMITS, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, true);
        checkIfUserAdminUseCase.execute().subscribe(isAdminUser -> {
            if (isBusinessUser() && !isAdminUser)
                navigationRouter.navigateTo(NavigationTarget.to(ProfileNavigationTarget.PROFILE_LIMITS_BUSINESS_USER)
                        .withParam(za.co.nedbank.core.Constants.KEY_FROM_PROFILE_LIMIT, Boolean.TRUE));
            else

                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.PROFILE_LIMITS));
        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void handleDebitOrderSwitchingTodoTaskClick() {

        mAnalytics.sendEvent(AppTracking.RETENTION_CARD_TODO_DEBIT_ORDER, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, true);
        navigateToMultipleAccount(Boolean.TRUE);
    }

    private void navigateToMultipleAccount(boolean isDebitOrderStarted) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RETENTION_MULTIPLE_SHARE_ACCOUNT_ACTIVITY);
        navigationTarget.withParam(NavigationTarget.IS_IN_RETENTION_DEBIT_ORDER_FLOW, isDebitOrderStarted);
        navigationRouter.navigateWithResult(navigationTarget).subscribe(
                navigationResult -> {
                    if (null != navigationResult && navigationResult.isOk()) {

                        view.showError();
                    }
                }
                , throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
        );
    }

    public boolean isBusinessUser() {
        int clientType = applicationStorage.getInteger(za.co.nedbank.core.Constants.KEY_USER_CLIENT_TYPE, za.co.nedbank.core.Constants.ZERO);
        return (clientType > 30);
    }

    public void handleShareAccountCompTaskClick() {

        mAnalytics.sendEvent(AppTracking.RETENTION_CARD_COMPLETED_SHARE_ACCOUNT_INFO, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RETENTION_INFO_ACTIVITY).withParam(RetentionConstants.RETENTION_INFO_TYPE, RetentionConstants.RetentionInfoType.SHARE_ACCOUNT);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void handleLoginSecurityCompTaskClick() {

        mAnalytics.sendEvent(AppTracking.RETENTION_CARD_COMPLETED_LOGIN_AND_SECURITY, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RETENTION_INFO_ACTIVITY).withParam(RetentionConstants.RETENTION_INFO_TYPE, RetentionConstants.RetentionInfoType.LOGIN_SECURITY);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void handleProfileLimitCompTaskClick() {

        mAnalytics.sendEvent(AppTracking.RETENTION_CARD_COMPLETED_PROFILE_LIMITS, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RETENTION_INFO_ACTIVITY).withParam(RetentionConstants.RETENTION_INFO_TYPE, RetentionConstants.RetentionInfoType.PROFILE_LIMIT);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void handleDebitOrderSwitchingCompTaskClick() {

        mAnalytics.sendEvent(AppTracking.RETENTION_CARD_COMPLETED_DEBIT_ORDER, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RETENTION_INFO_ACTIVITY).withParam(RetentionConstants.RETENTION_INFO_TYPE, RetentionConstants.RetentionInfoType.DEBIT_ORDER_SWITCHING);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void sendPageEvent() {
        mAnalytics.sendState(AppTracking.RETENTION_SCREEN_NOTIFICATION_FEATURE_OPTIONS);
    }

    public void sendBackArrowAnalytics() {
        mAnalytics.sendEvent(AppTracking.RETENTION_CLICK_NOTIFICATION_FEATURE_OPTIONS_BACK, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    private void setLabelVisibility(){
        if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_DEBIT_ORDER_DONE, false)
                || applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_PROFILE_LIMIT_DONE, false)
                || applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_LOGIN_SECURITY_DONE, false)
                || applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_SHARE_ACCOUNT_INFO_DONE, false)) {

            view.setVisibilityCompTask();
        }
        if (!applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_DEBIT_ORDER_DONE, false)
                || !applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_PROFILE_LIMIT_DONE, false)
                || !applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_LOGIN_SECURITY_DONE, false)
                || !applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_SHARE_ACCOUNT_INFO_DONE, false)) {

            view.setVisibilityTodoLabel();
        }
    }

    private void handleProfileLimitCompVisibility(){
        if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_SHARE_ACCOUNT_INFO_DONE, false)) {
            view.setShareAccountCompDivider();
        }
        if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_LOGIN_SECURITY_DONE, false)) {
            view.setSecurityCompDivider();
        }
        if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_DEBIT_ORDER_DONE, false)) {
            view.setDebitCompDivider();
        }
        view.setVisibilityProfileGroupComp();
        view.setProfileLimitHeadingComp();
    }

    private void handleProfileLimitTodoVisibility(){
        if (!applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_SHARE_ACCOUNT_INFO_DONE, false)) {
            view.setShareDividerTodo();
        }
        if (!applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_LOGIN_SECURITY_DONE, false)) {
            view.setSecurityDividerTodo();
        }
        if (!applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_DEBIT_ORDER_DONE, false)) {
            view.setDebitDividerTodo();
        }
        view.setProfileGroupTodo();
    }

    private void handleDebitOrderCompVisibility(){
        if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_SHARE_ACCOUNT_INFO_DONE, false)) {
            view.setShareDividerComp();
        }
        if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_LOGIN_SECURITY_DONE, false)) {
            view.setSecurityCompDivider();
        }
        view.setDebitGroupComp();
        view.setDebitCompHeading();
    }

    private void handleDebitOrderTodoVisibility(){
        if (!applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_SHARE_ACCOUNT_INFO_DONE, false)) {
            view.setShareDividerTodo();
        }
        if (!applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_LOGIN_SECURITY_DONE, false)) {
            view.setSecurityDividerTodo();
        }
        view.setDebitGroupTodo();
    }

    private void handleLoginSecurityCompVisibility(){
        if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_SHARE_ACCOUNT_INFO_DONE, false)) {
            view.setShareDividerComp();
        }
        view.setSecurityGroupComp();
        view.setSecurityHeadingComp();
    }

    private void handleLoginSecurityTodoVisibility(){
        if (!applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_SHARE_ACCOUNT_INFO_DONE, false)) {
            view.setShareDividerTodo();
        }
        view.setSecurityGroupTodo();
    }

    public void handleViewVisibility() {
        if (view != null) {
            setLabelVisibility();
            if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_PROFILE_LIMIT_DONE, false)) {
                handleProfileLimitCompVisibility();
            } else {
                handleProfileLimitTodoVisibility();
            }
            if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_DEBIT_ORDER_DONE, false)) {
                handleDebitOrderCompVisibility();
            } else {
                handleDebitOrderTodoVisibility();
            }

            if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_LOGIN_SECURITY_DONE, false)) {
                handleLoginSecurityCompVisibility();
            } else {
                handleLoginSecurityTodoVisibility();
            }

            if (applicationStorage.getBoolean(StorageKeys.IS_RET_TASK_SHARE_ACCOUNT_INFO_DONE, false)) {
                view.setShareGroupComp();
                view.setShareHeadingComp();
            } else
                view.setShareGroupTodo();
        }
    }

    public void trackActivationJourneyOptionsOnAppsFlyer(String featureSelected) {
        HashMap<String, Object> eventMap = new HashMap<>();
        AddContextData addContextData = new AddContextData(eventMap);
        addContextData.setFeatureSelected(featureSelected);

        mAfAnalyticsTracker.sendEvent(AppsFlyerTags.AF_ACTIVATION_FROM_NOTIFICATION, eventMap);
    }
}