package za.co.nedbank.ui.view.retention.feedback;

import android.text.TextUtils;

import java.util.HashMap;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.ui.domain.model.retention.RetentionFeedbackType;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class RetentionFeedbackPresenter extends NBBasePresenter<RetentionFeedbackView> {

    private final NavigationRouter navigationRouter;
    private String feedbackValueTag = StringUtils.EMPTY_STRING;
    private RetentionFeedbackType selectedType;
    private final Analytics mAnalytics;

    @Inject
    public RetentionFeedbackPresenter(NavigationRouter navigationRouter,final Analytics analytics){
        this.navigationRouter = navigationRouter;
        this.mAnalytics = analytics;
    }

    @Override
    protected void onBind(){
        super.onBind();
        if(view==null) {
            return;
        }
        sendPageEvent();
        view.setSubmitButtonEnabled(false);
        view.showSelectedFeedback(selectedType);
    }

    public void ratingClicked(RetentionFeedbackType feedbackType){
        selectedType=feedbackType;
        feedbackValueTag = feedbackType.getName();
        if(view==null) {
            return;
        }
        view.showSelectedFeedback(selectedType);
        updateSubmitButton();
    }


    public void submitClicked(){
        if(view==null) {
            return;
        }
        sendSubmitAnalytics();
        showSuccessScreenAndClose();
    }

    public void noThanksButtonClicked(){
        if(view==null) {
            return;
        }
        if (mAnalytics != null) {
            mAnalytics.sendEvent(AppTracking.RETENTION_CLICK_FEEDBACK_SKIP, StringUtils.EMPTY_STRING,StringUtils.EMPTY_STRING);
        }
        navigateToHome();

    }

    private void navigateToHome() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME)
                .withParam(Constants.BUNDLE_KEYS.IS_RETENTION_FLOW, true)
                .withIntentFlagClearTopSingleTop(true));
    }

    private void showSuccessScreenAndClose(){
        if(view==null) {
            return;
        }
        view.close();
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.RETENTION_FEEDBACK_SUCCESS));
    }

    private void updateSubmitButton(){
        if(view!=null){
            view.setSubmitButtonEnabled(selectedType!=null);
        }
    }

    private void sendPageEvent() {
        if (mAnalytics != null) {
            mAnalytics.sendState(AppTracking.RETENTION_SCREEN_FEEDBACK);
        }
    }

    private void sendSubmitAnalytics(){
        if (mAnalytics != null && !TextUtils.isEmpty(feedbackValueTag)) {
            HashMap<String,String> cData = new HashMap<>();
            cData.put(TrackingEvent.ANALYTICS.KEY_VALUE, feedbackValueTag);
            mAnalytics.sendEventActionWithMap(AppTracking.RETENTION_CLICK_FEEDBACK_SUBMIT, cData);
        }
    }
}


