package za.co.nedbank.ui.view.retention.feedback;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.ui.domain.model.retention.RetentionFeedbackType;

public interface RetentionFeedbackView extends NBBaseView {

    void setSubmitButtonEnabled(boolean enabled);

    void showSelectedFeedback(RetentionFeedbackType type);

    void showSubmitInProgress(boolean inProgress);

    void showError(String message);
}
