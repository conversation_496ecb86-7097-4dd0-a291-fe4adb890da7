package za.co.nedbank.ui.view.retention;

import static com.google.android.material.snackbar.BaseTransientBottomBar.LENGTH_INDEFINITE;

import android.graphics.Typeface;
import android.os.Bundle;

import androidx.annotation.Nullable;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityRetentionTaskSelectionBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.widget.NBSnackbar;

public class RetentionTaskSelectionActivity extends NBBaseActivity implements RetentionTaskSelectionView {

    @Inject
    RetentionTaskSelectionPresenter taskSelectionPresenter;
    private ActivityRetentionTaskSelectionBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRetentionTaskSelectionBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        taskSelectionPresenter.bind(this);
        binding.retOptionsLabel.setTypeface(Typeface.createFromAsset(getAssets(), getString(R.string.font_path_nedbank_sans)));
        binding.retFirstTaskShareAccView.setOnClickListener(v -> handleRetentionShareAccountFlow());
        binding.retFirstTaskLoginView.setOnClickListener(v -> handleRetentionLoginSecurityFlow());
        binding.retFirstTaskProfileView.setOnClickListener(v -> handleRetentionProfileLimitFlow());
        binding.retTaskOptionBackIV.setOnClickListener(v -> handleBackButtonClick());

        binding.retTaskOptionCloseIV.setOnClickListener(v -> handleCloseButtonClick());
        binding.retFirstTaskDebitOrderView.setOnClickListener(v -> handleDebitOrderRetentionFlow());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        taskSelectionPresenter.unbind();
    }

    public void handleRetentionShareAccountFlow(){
        taskSelectionPresenter.handleRetentionShareAccountFlow();
        taskSelectionPresenter.trackActivationJourneyOptionsOnAppsFlyer(binding.CompatTextView.getText().toString().trim());
    }

    public void handleRetentionLoginSecurityFlow(){
        taskSelectionPresenter.handleRetentionLoginSecurityFlow();
        taskSelectionPresenter.trackActivationJourneyOptionsOnAppsFlyer(binding.NBTextView1.getText().toString().trim());
    }

    public void handleRetentionProfileLimitFlow(){
        taskSelectionPresenter.handleRetentionProfileLimitFlow();
        taskSelectionPresenter.trackActivationJourneyOptionsOnAppsFlyer(binding.NBTextView3.getText().toString().trim());
    }

    public void handleBackButtonClick(){
        onBackPressed();
    }

    public void handleCloseButtonClick(){
        taskSelectionPresenter.handleCloseButtonClick();
    }

    public void handleDebitOrderRetentionFlow(){
        taskSelectionPresenter.handleDebitOrderRetentionFlow();
        taskSelectionPresenter.trackActivationJourneyOptionsOnAppsFlyer(binding.NBTextView2.getText().toString().trim());
    }

    @Override
    public void onBackPressed() {
        taskSelectionPresenter.sendBackArrowAnalytics();
        super.onBackPressed();
    }

    @Override
    public void showError() {
        showError(getString(R.string.somethings_wrong), getString(R.string.error_generic),
                getString(R.string.dismiss), LENGTH_INDEFINITE, () -> NBSnackbar.instance().dismissSnackBar());

    }
}
