/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.datastore;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsActionResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsMainResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentActionRequestEntity;
import za.co.nedbank.ui.data.networking.MoneyRequestsApi;

/**
 * Created by swapnil.gawande on 2/23/2018.
 */

public class MoneyRequestsFactory {
    private final NetworkClient mNetworkClient;

    @Inject
    MoneyRequestsFactory(NetworkClient networkClient) {
        this.mNetworkClient = networkClient;
    }

    public Observable<MoneyRequestsMainResponseEntity> getMoneyRequestList(String requesterRole) {
        return mNetworkClient.create(MoneyRequestsApi.class).getMoneyRequestList(requesterRole);
    }

    public Observable<MoneyRequestsActionResponseEntity> paymentRequestsAction(PaymentActionRequestEntity paymentActionRequestEntity) {
        return mNetworkClient.create(MoneyRequestsApi.class).paymentRequestsAction(paymentActionRequestEntity);
    }
}

