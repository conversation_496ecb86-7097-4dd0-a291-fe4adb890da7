/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.payme_tab;

import android.util.Log;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationResult;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.validation.RecipientContactValidator;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.ui.domain.usecase.money_request.GetNotificationsUsecase;
import za.co.nedbank.ui.domain.usecase.money_request.ValidateMobileNumberUseCase;
import za.co.nedbank.ui.view.tracking.AppTracking;
import za.co.nedbank.uisdk.validation.ValidatableInput;

public class MoneyRequestsFragmentPresenter extends NBBasePresenter<PayMoneyRequestView> {
    private final NavigationRouter mNavigationRouter;
    private final GetNotificationsUsecase mGetNotificationsUsecase;
    private final Analytics analytics;
    private NavigationResult mNavigationResult;
    private final RecipientContactValidator mMobileNumberValidator;
    private final NonEmptyTextValidator nonEmptyTextValidator;
    private final ValidateMobileNumberUseCase validateMobileNumberUseCase;
    private final ErrorHandler errorHandler;

    @Inject
    MoneyRequestsFragmentPresenter(@NonNull final NavigationRouter navigationRouter,
                                   final GetNotificationsUsecase getNotificationsUsecase,
                                   final RecipientContactValidator mMobileNumberValidator,
                                   final NonEmptyTextValidator nonEmptyTextValidator, final ValidateMobileNumberUseCase validateMobileNumberUseCase,
                                   final ErrorHandler errorHandler, final Analytics analytics) {
        this.mNavigationRouter = navigationRouter;
        this.mGetNotificationsUsecase = getNotificationsUsecase;
        this.mMobileNumberValidator = mMobileNumberValidator;
        this.nonEmptyTextValidator = nonEmptyTextValidator;
        this.validateMobileNumberUseCase = validateMobileNumberUseCase;
        this.errorHandler = errorHandler;
        this.analytics = analytics;
    }

    @Override
    protected void onBind() {
        super.onBind();
        if (null != mNavigationResult && mNavigationResult.isOk() && view != null) {
            view.setResult(mNavigationResult.getParams());
            mNavigationResult = null;
        }

    }

    void navigateToViewMoneyRequests() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.VIEW_MONEY_REQUESTS));
    }

    void getNotifications() {
        mGetNotificationsUsecase.execute()
                .compose(bindToLifecycle())
                .subscribe(
                        notificationsResponseDataModel -> {
                            if (view != null && notificationsResponseDataModel != null) {
                                view.showNotifications(notificationsResponseDataModel.getNotificationDataModel());
                            }
                        },
                        error -> {
                            if (view != null) {
                                view.showError(error.getMessage());
                            }
                        }
                );
    }

    boolean validateInput(final ValidatableInput<String> input,
                          final Validator.ValidatorType validatorType) {
        if (validatorType == Validator.ValidatorType.RECIPIENT_CONTACT_VALIDATOR) {
            return validate(input, mMobileNumberValidator);
        }
        return false;
    }

    void validateMobileNumber(String mobileNumber) {
        if (view == null) {
            return;
        }
        if (mobileNumber == null) {
            view.showErrorMessage(errorHandler.getUnknownError());
            return;
        }
        validateMobileNumberUseCase.execute(mobileNumber)
                .compose(bindToLifecycle())
                .subscribe(this::validateMobileNumberAPISuccess, throwable -> {
                    Error error = errorHandler.getErrorMessage(throwable);
                    if (view != null) {
                        view.showLoadingOnButton(false);
                        view.showErrorMessage(error.getMessage());
                        view.trackFailure(false, AppTracking.TAG_PAY_ME_FAILURE, za.co.nedbank.core.ApiAliasConstants.VAL_CN, error.getMessage(), null);
                    }
                });
    }

    void validateMobileNumberAPISuccess(MetaDataModel metaDataModel) {
        if (metaDataModel != null && view != null) {

            List<ResultDataModel> validateNumberDataModelList = metaDataModel.getResultData();
            if (validateNumberDataModelList != null && !validateNumberDataModelList.isEmpty()) {
                validateMobNumber(validateNumberDataModelList);
            }
            view.showLoadingOnButton(false);
        }
    }

    private void validateMobNumber(List<ResultDataModel> validateNumberDataModelList) {
        for (ResultDataModel resultDataModel : validateNumberDataModelList) {
            ArrayList<ResultDetailModel> validateNumberDetailDataModelList = resultDataModel.getResultDetail();
            for (ResultDetailModel resultDetailModel : validateNumberDetailDataModelList) {
                if (resultDetailModel.getResult().equalsIgnoreCase(za.co.nedbank.services.Constants.SUCCESS)) {
                    view.navigateToRequestDetailOnSuccess();
                    analytics.sendEvent(TrackingEvent.PAY_ME_SECTION, TrackingParam.PAY_ME_RETURN_STATUS, String.format("%s", true));
                } else {
                    if (view != null)
                        view.trackValidateFailure(resultDetailModel.getResult());
                    handleValidateNumberFailure();
                    analytics.sendEvent(TrackingEvent.PAY_ME_SECTION, TrackingParam.PAY_ME_RETURN_STATUS, String.format("%s", false));
                }
            }
        }
    }

    void trackFailure(String errorMessage, String errorCode) {
        analytics.trackFailure(true, AppTracking.TAG_PAY_ME_FAILURE, za.co.nedbank.core.ApiAliasConstants.VAL_CN, errorMessage, errorCode);
    }
    public void trackFailure(boolean isApiFailure, String tagName, String apiName, String message, String apiErrorCode){
        analytics.trackFailure(isApiFailure, tagName, apiName, message, apiErrorCode);
    }

    private void handleValidateNumberFailure() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.MONEY_REQUEST_SUCCESS);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    String getMobileNumberWithoutCountryCode(String userMobileNumber) {
        String mobileNumber;
        int mobileNumberMaxLength = 9;
        if (userMobileNumber.length() > mobileNumberMaxLength && userMobileNumber.contains(StringUtils.PLUS + StringUtils.COUNTRY_CODE)) {
            int subStringStartIndex = 3;
            mobileNumber = userMobileNumber.substring(subStringStartIndex, userMobileNumber.length());
        } else {
            mobileNumber = userMobileNumber;
        }

        return String.format("%s%s", StringUtils.ZERO, mobileNumber);
    }

    void checkInputsOnScreen(
            final ValidatableInput<String> recipientNameInput,
            final ValidatableInput<String> mobileNumberInput) {
        if (null != view) {
            if (nonEmptyTextValidator.validateInput(recipientNameInput.getValue()).isOk() && nonEmptyTextValidator.validateInput(mobileNumberInput.getValue()).isOk() && validateInput(mobileNumberInput, Validator.ValidatorType.RECIPIENT_CONTACT_VALIDATOR)) {
                view.setNextButtonEnabled(true);
            } else {
                view.setNextButtonEnabled(false);
            }
        }
    }

    void navigateToRequestDetailScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.MONEY_REQUEST_DETAIL);
        if (view != null) {
            navigationTarget.withParam(NavigationTarget.PARAM_RECIPIENT_NAME, view.getRecipientName());
            navigationTarget.withParam(NavigationTarget.RECIPIENT_PHONE_NUMBER, view.getRecipientMobileNumber());
        }
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void handleRecipientIconClick() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.PHONE_CONTACT_BENEFICIARY_LIST);
        navigationTarget.withParam(Constants.BundleKeys.VIEW_PAGER_WTH_TAB_VIEW, Constants.ISearchListViewType.VIEW_TYPE_SEND_MONEY);
        mNavigationRouter.navigateWithResult(navigationTarget).subscribe(
                navigationResult -> mNavigationResult = navigationResult
                , throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
        );
    }

    void handleRecipientNameTextChanged() {
        if (null != view) {
            view.handleEnterRecipientName();
        }
    }

    void handleMobileNumberTextChanged() {
        if (null != view) {
            view.handleEnterMobileNumber();
        }
    }

    void handleNextClick() {
        if (view != null) {
            view.handleNextClick();
        }
        analytics.sendEvent(AppTracking.TAG_PAY_ME_SUBMIT, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }
}
