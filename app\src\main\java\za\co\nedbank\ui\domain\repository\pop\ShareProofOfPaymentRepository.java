package za.co.nedbank.ui.domain.repository.pop;

import java.util.List;

import io.reactivex.Observable;
import za.co.nedbank.ui.data.entity.pop.SharePOPRecipientsRequestEntity;
import za.co.nedbank.ui.data.entity.pop.ShareProofOfPaymentRequestEntity;
import za.co.nedbank.ui.data.entity.pop.ShareProofOfPaymentResponseEntity;

public interface ShareProofOfPaymentRepository {
    Observable<ShareProofOfPaymentResponseEntity> postShareProofOfPayment(List<ShareProofOfPaymentRequestEntity> shareProofOfPaymentRequestEntityList, String contractID);

    Observable<ShareProofOfPaymentResponseEntity> postShareProofOfPaymentRecipients(SharePOPRecipientsRequestEntity sharePOPRecipientsRequestEntity, String transactionId);

    Observable<ShareProofOfPaymentResponseEntity> postShareProofOfPaymentRecipientsForSchedulePayment(SharePOPRecipientsRequestEntity sharePOPRecipientsRequestEntity, String transactionId);
}
