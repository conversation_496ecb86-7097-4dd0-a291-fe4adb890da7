package za.co.nedbank.ui.view.deeplink.view

import io.reactivex.disposables.Disposable
import za.co.nedbank.core.base.NBBasePresenter
import za.co.nedbank.core.data.storage.StorageKeys
import za.co.nedbank.core.deeplink.DeeplinkUtils
import za.co.nedbank.core.deeplink.DeeplinkUtils.AppDeeplinkTarget.CARD_FREEZE
import za.co.nedbank.core.deeplink.DeeplinkUtils.AppDeeplinkTarget.ENOTES_ADD_ON
import za.co.nedbank.core.deeplink.DeeplinkUtils.AppDeeplinkTarget.LATEST
import za.co.nedbank.core.deeplink.DeeplinkUtils.AppDeeplinkTarget.OPEN_AVO
import za.co.nedbank.core.deeplink.DeeplinkUtils.AppDeeplinkTarget.SHOP_AVO
import za.co.nedbank.core.deeplink.DeeplinkUtils.AppDeeplinkTarget.VIRTUAL_CARD
import za.co.nedbank.core.domain.model.UserDetailData
import za.co.nedbank.core.domain.storage.ApplicationStorage
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase
import za.co.nedbank.core.feature.FeatureConstants
import za.co.nedbank.core.feature.FeatureSetController
import za.co.nedbank.core.logging.NBLogger
import za.co.nedbank.core.navigation.NavigationResult
import za.co.nedbank.core.navigation.NavigationRouter
import za.co.nedbank.core.navigation.NavigationTarget
import za.co.nedbank.core.notification.NotificationConstants
import za.co.nedbank.services.Constants
import za.co.nedbank.ui.domain.model.AvoWalletDetailsModel
import za.co.nedbank.ui.domain.usecase.GetAvoWalletDetailsUseCase
import javax.inject.Inject
import javax.inject.Named

class AppDeeplinkPresenter @Inject constructor(
    private val navigationRouter: NavigationRouter,
    private val mGetUserDetailUseCase: GetUserDetailUseCase,
    private val mWalletDetailsUseCase: GetAvoWalletDetailsUseCase,
    private val mFeatureSetController: FeatureSetController,
    private val mApplicationStorage: ApplicationStorage,
    @param:Named("memory") private val mMemoryApplicationStorage: ApplicationStorage,
) : NBBasePresenter<AppDeeplinkView?>() {

    private var mIsBusinessUser = false

    fun clearNavigationData() {
        mMemoryApplicationStorage.clearValue(NotificationConstants.STORAGE_KEYS.AJO_NOTIFICATION_LINK)
    }

    fun handleOnClick(action: String) {
        when {
            action.equals(LATEST, ignoreCase = true) -> openCovid19()
            action.equals(CARD_FREEZE, ignoreCase = true) -> openHomeScreen()
            action.equals(VIRTUAL_CARD, ignoreCase = true) -> handleVirtualCardClick()
            action.equals(OPEN_AVO, ignoreCase = true) -> handleOpenAvoFlow()
            action.equals(SHOP_AVO, ignoreCase = true) -> handleShopAvoFlow()
            action.equals(ENOTES_ADD_ON, ignoreCase = true) -> openNotificationPreferenceScreen()
        }
    }

    private fun handleShopAvoFlow() {
        val doNotShowAgain: Boolean = mApplicationStorage.getBoolean(
            StorageKeys.DATA_USAGE_NEVER_ASK_AGAIN_WIDGET_SHOP,
            false
        )
        if (doNotShowAgain || mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_DATA_USAGE_SCREEN)) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHOP_DASHBOARD))
            close()
            return
        }
        navigationRouter.navigateWithResult(
            NavigationTarget.to(NavigationTarget.DATA_USAGE_WARNING)
                .withParam(
                    za.co.nedbank.core.Constants.FROM_SCREEN,
                    za.co.nedbank.core.Constants.FROM_SHOP_WIDGET
                )
                .withParam(
                    za.co.nedbank.core.Constants.COMPLETE_SCREEN_LABEL,
                    view?.getActivityLabel()
                )
        )
            .subscribe({ navigationResult: NavigationResult ->
                val accepted =
                    navigationResult.getBooleanParam(NavigationTarget.RESULT_DATA_USAGE_WARNING_ACCEPT)
                if (accepted) {
                    navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHOP_DASHBOARD))
                    close()
                }
            },
                { throwable: Throwable ->
                    NBLogger.e(
                        javaClass.canonicalName,
                        throwable.message
                    )
                })
    }

    private fun handleOpenAvoFlow() {
        mGetUserDetailUseCase.execute(false)
            .compose(bindToLifecycle())
            .doOnSubscribe { _: Disposable? ->
                view?.showProgress(true)
            }
            .doOnTerminate {
                view?.showProgress(false)
            }
            .subscribe({ userDetail: UserDetailData? ->
                if (userDetail != null && view != null) {
                    mIsBusinessUser = userDetail.clientType != null && userDetail.clientType
                        .toInt() > Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT
                    getAvoWalletDetails()
                } else {
                    handleNavigationErrorsFlow()
                }
            }, { _: Throwable? ->
                if (view != null) {
                    handleNavigationErrorsFlow()
                }
            })
    }

    private fun getAvoWalletDetails() {
        view?.showProgress(true)
        mWalletDetailsUseCase.execute()
            .compose(bindToLifecycle())
            .subscribe(
                { result: AvoWalletDetailsModel? ->
                    openAvoAppInBrowser(
                        result,
                        mIsBusinessUser
                    )
                },
                { _: Throwable? ->
                    if (view != null) {
                        view?.showProgress(false)
                        handleNavigationErrorsFlow()
                    }
                }
            )
    }

    private fun openAvoAppInBrowser(result: AvoWalletDetailsModel?, isBusinessUser: Boolean) {
        if (view != null) {
            view?.showProgress(false)
            if (isBusinessUser) {
                if (result?.isAvoWalletAccount == true) view?.startBrowser(
                    createUrl(
                        mFeatureSetController.getDynamicFeatureValue(
                            FeatureConstants.AVO_PWA_MERCHANT_LOGIN_URL
                        ), result.avoId
                    )
                )
                else view?.startBrowser(
                    createUrl(
                        mFeatureSetController.getDynamicFeatureValue(
                            FeatureConstants.AVO_PWA_MERCHANT_REGISTER_URL
                        ), result?.avoId
                    )
                )
            } else {
                if (result?.isAvoWalletAccount == true) view?.startBrowser(
                    createUrl(
                        mFeatureSetController.getDynamicFeatureValue(
                            FeatureConstants.AVO_PWA_LOGIN_URL
                        ), result.avoId
                    )
                )
                else view?.startBrowser(
                    createUrl(
                        mFeatureSetController.getDynamicFeatureValue(
                            FeatureConstants.AVO_PWA_REGISTER_URL
                        ), result?.avoId
                    )
                )
            }
        }
    }

    private fun createUrl(url: String, avoId: String?): String {
        var url: String? = url
        if (url != null && !url.startsWith(za.co.nedbank.core.Constants.HTTPS_STRING)) url =
            za.co.nedbank.core.Constants.HTTPS_STRING + url

        url = if (avoId != null) url + za.co.nedbank.core.Constants.AVO_CODE_PARAM + avoId
        else url + za.co.nedbank.core.Constants.CMPID_PARAM

        return url
    }

    private fun handleNavigationErrorsFlow() {
        if (view != null) {
            close()
        }
    }

    private fun openHomeScreen() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME)
            .withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, DeeplinkUtils.AppDeeplinkTarget.CARD_FREEZE)
            .withIntentFlagClearTopSingleTop(true))
        close()
    }

    private fun handleVirtualCardClick() {
        navigationRouter.navigateTo(
            NavigationTarget.to(NavigationTarget.HOME).withParam(
                NotificationConstants.EXTRA.NAVIGATION_TARGET_VIRTUAL_CARD,
                DeeplinkUtils.AppDeeplinkTarget.VIRTUAL_CARD
            ).withIntentFlagClearTopSingleTop(true)
        )
        close()
    }

    fun openCovid19() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.COVID_19))
        close()
    }
    private fun openNotificationPreferenceScreen() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES))
        close()
    }
    fun close() {
        if (view != null) view!!.close()
    }
}