package za.co.nedbank.ui.view.notification.ajo_notification_details;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;

import android.app.NotificationManager;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;

import androidx.core.text.HtmlCompat;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.databinding.ActivityNotificationDetailsBinding;
import za.co.nedbank.enroll_v2.Constants;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;
import za.co.nedbank.uisdk.component.CompatButton;

public class AJONotificationDetailsActivity extends NBBaseActivity implements AJONotificationDetailsView, View.OnClickListener {

    @Inject
    NotificationManager mNotificationManager;
    @Inject
    public AJONotificationDetailsPresenter mAJONotificationDetailsPresenter;

    private AjoPushPayloadDataModel mPushPayload;
    private ActivityNotificationDetailsBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        receiveBundle();
        binding = ActivityNotificationDetailsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        initToolbar(binding.toolbar, true, false);
        mAJONotificationDetailsPresenter.bind(this);
        setUpView();
        clearNotificationFromTray();
    }

    private void receiveBundle() {
        if (getIntent() != null && getIntent().getExtras() != null) {
            String intentData = getIntent().getExtras().getString(NotificationConstants.EXTRA.AJO_NOTIFICATION_DATA);
            mPushPayload = new Gson().fromJson(intentData, AjoPushPayloadDataModel.class);
        }
    }

    private void setUpView() {
        if (mPushPayload != null) {
            setHeadingAndSubHeading();
            if (mPushPayload.getBody() != null && binding.notificationDetails != null) {
                binding.notificationDetails.loadDataWithBaseURL(null, mPushPayload.getBody(), Constants.MIME_TYPE, Constants.UTF_8, null);
                binding.notificationDetails.setVisibility(View.VISIBLE);
            }
            List<CompatButton> buttonList = createButtons(mPushPayload.getActionButtons());
            for (CompatButton button : buttonList) {
                if (binding.notificationLayout != null) {
                    binding.notificationLayout.addView(button);
                }
            }
        }
    }

    private void setHeadingAndSubHeading() {
        if (mPushPayload.getTitle() != null && binding.notificationSubheading != null) {
            binding.notificationSubheading.setText(getFormattedText(mPushPayload.getTitle()));
        }
        if (binding.notificationSubheading != null)
            binding.notificationSubheading.setVisibility(View.GONE);

        if (mPushPayload.getTitle() != null) {
            binding.notificationHeading.setText(getFormattedText(mPushPayload.getTitle()));
        }
    }

    private CharSequence getFormattedText(String text) {
        return HtmlCompat.fromHtml(text, HtmlCompat.FROM_HTML_MODE_LEGACY);
    }

    private List<CompatButton> createButtons(List<AjoPushPayloadDataModel.ActionButton> responseOptions) {
        List<CompatButton> buttonList = new ArrayList<>();
        if (responseOptions != null) {
            for (int i = 0; i < responseOptions.size(); i++) {

                AjoPushPayloadDataModel.ActionButton responseOption = responseOptions.get(i);
                CompatButton button = new CompatButton(this);
                button.setText(responseOption.getLabel());


                button.setTag(responseOption);
                button.setOnClickListener(this);
                LinearLayout.LayoutParams param = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
                int leftRight = getResources().getDimensionPixelSize(R.dimen.dimen_20dp);
                int top = getResources().getDimensionPixelSize(R.dimen.dimen_16dp);
                param.setMargins(leftRight, top, leftRight, 0);
                button.setLayoutParams(param);

                CompatButton.ButtonType buttonType;
                String btnType = responseOption.getLink();
                if (AjoPushPayloadDataModel.ActionButtonType.DISMISS.equalsIgnoreCase(btnType)) {
                    buttonType = CompatButton.ButtonType.SECONDARY;
                    button.setButtonType(buttonType);
                    buttonList.add(button);
                } else {
                    buttonType = CompatButton.ButtonType.PRIMARY;
                    button.setButtonType(buttonType);
                    buttonList.add(0, button);
                }

            }

        }
        if (!buttonList.isEmpty()) {
            CompatButton button = buttonList.get(buttonList.size() - 1);
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) button.getLayoutParams();
            params.bottomMargin = getResources().getDimensionPixelSize(R.dimen.dimen_20dp);
            button.setLayoutParams(params);
        }
        return buttonList;
    }

    @Override
    public void onClick(View v) {
        mAJONotificationDetailsPresenter.handleOnClick((AjoPushPayloadDataModel.ActionButton) v.getTag());
    }

    @Override
    protected void onDestroy() {
        mAJONotificationDetailsPresenter.unbind();
        super.onDestroy();
    }

    @Override
    protected void onResume() {
        super.onResume();
        mAJONotificationDetailsPresenter.clearNotificationData();
    }

    public void clearNotificationFromTray() {
        if (mPushPayload != null) {
            mNotificationManager.cancel(mPushPayload.getMessageId().hashCode());
        }
    }
}
