package za.co.nedbank.ui.view.home.latest;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.airbnb.lottie.LottieDrawable;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.common.CardType;
import za.co.nedbank.core.common.FeatureCardEnum;
import za.co.nedbank.core.common.ProductType;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.dashboard.HomeWidget;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.media_content.MediaContentAdapter;
import za.co.nedbank.core.view.media_content.MediaContentClickListener;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.databinding.FragmentHomeLatestBinding;
import za.co.nedbank.ui.di.AppDI;

public class HomeLatestFragment extends NBBaseFragment implements HomeLatestView, MediaContentClickListener {

    @Inject
    ApplicationStorage mApplicationStorage;
    @Inject
    FeatureSetController mFeatureSetController;
    @Inject
    HomeLatestPresenter mHomeLatestPresenter;
    private HomeWidget widgetFlow;
    private MediaContentAdapter mAdapter;
    private List<AppLayoutViewModel> mMediaContentList = new ArrayList<>();
    private FragmentHomeLatestBinding binding;
    private boolean isNonTPLatest = false;

    public void setNonTpUILatest(boolean isNonTPLatestUI) {
        this.isNonTPLatest = isNonTPLatestUI;
    }

    public static HomeLatestFragment getInstance() {
        return new HomeLatestFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        binding.ngcHomeTitle.setVisibility(View.INVISIBLE);
    }


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentHomeLatestBinding.inflate(inflater, container, false);
        setUpShimmerView();
        setUpRecyclerView();
        getFromWidgetStatus();
        setToolBarVissibility();
        mHomeLatestPresenter.bind(this);
        mHomeLatestPresenter.loadMediaContent();
        return binding.getRoot();
    }


    @Override
    public void onResume() {
        super.onResume();
        mHomeLatestPresenter.loadPreferredUserName();
        mHomeLatestPresenter.getTotalBankingUnreadMessages();
    }

    private void setUpRecyclerView() {
        binding.rvAppbarContentHome.setHasFixedSize(true);
        binding.rvAppbarContentHome.setLayoutManager(new LinearLayoutManager(getActivity()));
        binding.rvAppbarContentHome.setItemAnimator(null);
        mAdapter = new MediaContentAdapter(getActivity(), this, mMediaContentList, DeviceUtils.getDeviceWidth(getActivity()),
                mHomeLatestPresenter.isDynamicFeatureToggleEnabled(), false, mFeatureSetController);
        binding.rvAppbarContentHome.setAdapter(mAdapter);
    }

    @Override
    public void showProgress(boolean progress) {
        if (progress && CollectionUtils.isEmpty(mMediaContentList)) {
            binding.svHomeShimmerContainer.parentShimmer.setVisibility(View.VISIBLE);
        } else {
            binding.svHomeShimmerContainer.parentShimmer.setVisibility(View.GONE);
        }
    }

    private void setUpShimmerView() {
        ViewUtils.setHeightAsPerAspectRatio(binding.svHomeShimmerContainer.ivShimmerMediaCardImage, MediaContentAdapter.aspectW, MediaContentAdapter.aspectH, DeviceUtils.getDeviceWidth(getActivity()));
    }

    @Override
    public void showMediaAndOfferCards(List<AppLayoutViewModel> mediaContentVMList) {
        if (isNonTPLatest && mediaContentVMList != null || (!mHomeLatestPresenter.isNewFeatureTileEnable() && mediaContentVMList != null)) {
            removeProductOffers(mediaContentVMList);
        }
        if (mediaContentVMList == null || mediaContentVMList.isEmpty()) {
            mMediaContentList.clear();
            mAdapter.notifyDataSetChanged();
            ViewUtils.hideViews(binding.nsHomeMediaProductCard);
            ViewUtils.showViews(binding.llEmptyView);
            showEmptyViewAnimation();
        } else {
            ViewUtils.showViews(binding.nsHomeMediaProductCard);
            ViewUtils.hideViews(binding.llEmptyView, binding.svHomeShimmerContainer.parentShimmer);
            mMediaContentList.clear();
            mMediaContentList.addAll(mediaContentVMList);
            mAdapter.notifyDataSetChanged();
        }
    }

    private static void removeProductOffers(List<AppLayoutViewModel> mediaContentVMList) {
        Iterator<AppLayoutViewModel> iterator = mediaContentVMList.iterator();
        while (iterator.hasNext()) {
            AppLayoutViewModel model = iterator.next();
            if (model.getCardType() == CardType.FEATURE_CARD || model.getCardType() == CardType.PRODUCT_OFFER) {
                iterator.remove();
            }
        }
    }

    private void showEmptyViewAnimation() {
        binding.emptyViewAnimation.setRepeatCount(LottieDrawable.INFINITE);
        binding.emptyViewAnimation.playAnimation();
    }


    @Override
    public void showLatestApiError(String errorMsg) {
        showError(getString(za.co.nedbank.enroll_v2.R.string.error), errorMsg, getString(za.co.nedbank.enroll_v2.R.string.retry), () -> mHomeLatestPresenter.loadMediaContent());
    }

    @Override
    public void updateChatIcon(int count) {
        binding.ngcHomeChatIcon.setImageResource(count > 0 ?
                R.drawable.ic_chat_green_notification_wrapper : R.drawable.ic_chat_icon_green_wrapper);
    }

    @Override
    public void onUnreadChatEvent(UnreadChatEvent unreadChatEvent) {
        binding.ngcHomeChatIcon.setImageResource(unreadChatEvent.isUnreadChat() ?
                R.drawable.ic_chat_green_notification_wrapper : R.drawable.ic_chat_icon_green_wrapper);
    }

    @Override
    public void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent) {
        binding.ngcHomeChatIcon.setImageResource(unreadChatEvent.isUnreadLifestyleChat() ?
                R.drawable.ic_chat_green_notification_wrapper : R.drawable.ic_chat_icon_green_wrapper);
    }

    @Override
    public void setPreferredName(String name) {
        if (StringUtils.isNullOrEmpty(name.trim())) {
            mHomeLatestPresenter.loadCustomerNAme();
        } else {
            binding.ngcHomeTitle.setText(name.trim());
        }
        binding.ngcHomeTitle.setVisibility(View.VISIBLE);
    }

    @Override
    public void handlePreferredNameError() {
        mHomeLatestPresenter.fetchUserDetail();
    }


    @Override
    public void setCustomerName(String customerUserName) {
        if (customerUserName == null || customerUserName.trim().isEmpty()) {
            binding.ngcHomeTitle.setText(StringUtils.EMPTY_STRING);
        } else {
            mApplicationStorage.putString(
                    Constants.KEY_USER_CLIENT_NAME, customerUserName.trim());
            binding.ngcHomeTitle.setText(StringUtils.removeTitles(customerUserName.trim()));
        }
        binding.ngcHomeTitle.setVisibility(View.VISIBLE);
    }


    @Override
    public void setUserInfo(UserDetailViewModel userDetailViewModel) {
        if (!TextUtils.isEmpty(userDetailViewModel.getClientType())) {
            mApplicationStorage.putInteger(Constants.KEY_USER_CLIENT_TYPE, Integer.parseInt(userDetailViewModel.getClientType()));
        }
        setCustomerName(userDetailViewModel.getFullNames());

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mHomeLatestPresenter.unbind();
    }

    @Override
    public void onClickMediaContentItem(MediaCardViewModel mediaCardViewModel, int position) {
        mHomeLatestPresenter.onClickContentItem(mediaCardViewModel, widgetFlow);
        position++;
        if (mediaCardViewModel != null && StringUtils.isNotEmpty(mediaCardViewModel.getName())) {
            mHomeLatestPresenter.logEventOnMediaCardClick(mediaCardViewModel.getName(), position);
        } else
            mHomeLatestPresenter.logEventOnMediaCardClick(StringUtils.EMPTY_STRING, position);
    }

    @Override
    public void onClickProductOfferItem(ProductType productType) {
        // Not required to handle click on product offer item
    }

    @Override
    public void onClickFeatureCardItem(FeatureCardEnum featureCardEnum) {
        mHomeLatestPresenter.trackFeatureCards(getString(featureCardEnum.getFeatureInfo()));
        mHomeLatestPresenter.navigateToFeatureCardDetail(featureCardEnum);
    }

    @Override
    public void onClickFeatureCardItem(MediaCardViewModel featureCardItemViewModel) {
        mHomeLatestPresenter.trackFeatureCards(featureCardItemViewModel.getMediaContentInfo());
        mHomeLatestPresenter.navigateToFeatureCardDetail(featureCardItemViewModel, widgetFlow);
    }

    @Override
    public HomeWidget getFromWidgetStatus() {
        if (getArguments() != null && getArguments().containsKey(Constants.BUNDLE_KEYS.FROM_ACTIVITY)) {
            widgetFlow = HomeWidget.fromValue(getArguments().getInt(Constants.BUNDLE_KEYS.FROM_ACTIVITY, 1));
        }
        return widgetFlow;
    }

    private void setToolBarVissibility() {
        if (HomeWidget.COVID_19.equals(widgetFlow)) {
            ViewUtils.hideViews(binding.appBarHomeToolbar, binding.nedbankMoneyIcon);
        }
    }
}
