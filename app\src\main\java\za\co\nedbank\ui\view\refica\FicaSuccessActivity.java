package za.co.nedbank.ui.view.refica;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityFicaSuccessBinding;
import za.co.nedbank.ui.di.AppDI;

public class FicaSuccessActivity extends NBBaseActivity implements FicaSuccessView{

    public static final String ACTION = "action";

    @Inject
    FicaSuccessPresenter ficaSuccessPresenter;
    private ActivityFicaSuccessBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getActivityComponent(this).inject(this);
        binding = ActivityFicaSuccessBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        ficaSuccessPresenter.bind(this);
        ficaSuccessPresenter.callRefreshApi();
        binding.icCrossSecurity.setOnClickListener(v -> goBack(getIntent()));
        binding.nextButtonSecurity.setOnClickListener(v -> onSecurityNextButtonClicked());
    }

    public void onSecurityNextButtonClicked() {
        ficaSuccessPresenter.sendAnalytics();
        Intent intent = getIntent();
        intent.putExtra(ACTION, "1");
        goBack(intent);
    }

    private void goBack(Intent intent) {
        setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    public void onBackPressed() {
        goBack(getIntent());
    }

    @Override
    public void showWidgetProgress(boolean visible) {
        binding.toolbarViewProgress.setVisibility(visible ? View.VISIBLE : View.GONE);
        binding.nextButtonSecurity.setLoadingVisible(visible);
    }
}