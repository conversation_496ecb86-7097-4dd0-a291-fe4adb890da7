package za.co.nedbank.ui.data.entity.money_request;

import com.squareup.moshi.Json;

public class CMSDetailDescriptionEntity {
    @Json(name = "cta")
    private String cta;

    @Json(name = "header")
    private String header;
    @Json(name = "text")
    private String text;

    public String getCta() {
        return cta;
    }

    public void setCta(String cta) {
        this.cta = cta;
    }

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

}
