package za.co.nedbank.ui.view.notification.notification_messages;

import java.util.ArrayList;
import java.util.List;

public class MultipleSelectionHandler {

    private OnSelectionChangeListener mSelectionChangedListener;
    private List<Integer> mSelectedItems;
    private int prevSelectionCount = 0;
    private boolean isSelectable;


    public MultipleSelectionHandler(OnSelectionChangeListener changedListener) {
        mSelectedItems = new ArrayList<>();
        mSelectionChangedListener = changedListener;
    }

    public boolean isSelectable() {
        return isSelectable;
    }

    public void clearSelections() {
        mSelectedItems.clear();
        isSelectable = false;
        onChangeSelectedItems();
    }

    public void handleSelection(SelectableViewHolder selectableViewHolder) {

        int adapterPosition = selectableViewHolder.getAdapterPosition();
        if (!isSelected(adapterPosition)) {
            selectableViewHolder.setSelected(true);
            mSelectedItems.add(adapterPosition);
            onChangeSelectedItems();
        } else {
            selectableViewHolder.setSelected(false);
            if (mSelectedItems.contains(adapterPosition)) {
                mSelectedItems.remove(Integer.valueOf(adapterPosition));
                onChangeSelectedItems();
            }
        }
    }

    private void onChangeSelectedItems() {
        if (!isSelectable && prevSelectionCount == 0 && !mSelectedItems.isEmpty()) {
            if (mSelectionChangedListener != null) {
                mSelectionChangedListener.onSelectionStarted();
                isSelectable = true;
            }
        }

        if (mSelectionChangedListener != null) {
            mSelectionChangedListener.onSelectionChanged(mSelectedItems);
        }

        prevSelectionCount = mSelectedItems.size();
    }

    private boolean isSelected(int adapterPosition) {
        return mSelectedItems.contains(adapterPosition);
    }

    public boolean tapSelection(SelectableViewHolder selectableViewHolder) {

        boolean isSelectable = isSelectable();
        if (isSelectable) {
            handleSelection(selectableViewHolder);
        }
        return isSelectable;
    }

    public void bind(SelectableViewHolder selectableViewHolder) {
        selectableViewHolder.setSelected(isSelected(selectableViewHolder.getAdapterPosition()));
    }

    public List<Integer> getSelectedPositions() {
        return mSelectedItems;
    }

    public void selectAll(boolean selectAll, int itemCount) {

        mSelectedItems.clear();
        if (selectAll) {
            for (int i = 0; i < itemCount; i++) {
                mSelectedItems.add(i);
            }
        }
        onChangeSelectedItems();
    }

    void selectAll(boolean selectAll, List<Integer> positionsToBeSelected) {

        mSelectedItems.clear();
        if (selectAll) {
            int size = positionsToBeSelected.size();
            for (int i = 0; i < size; i++) {
                mSelectedItems.add(positionsToBeSelected.get(i));
            }
        }
        onChangeSelectedItems();
    }

    void handleNewItemAtTop(boolean allSelected) {
        if (isSelectable()) {
            if (mSelectedItems != null) {
                //shift items
                for (int i = 0; i < mSelectedItems.size(); i++) {

                    int selectedPos = mSelectedItems.get(i);
                    mSelectedItems.set(i, ++selectedPos);
                }
                //to automatically select the new item while select all checked uncomment below code
                /*if (allSelected) {
                    mSelectedItems.add(0);
                }*/
                onChangeSelectedItems();
            }
        }
    }

    void shiftItemsIfSelectable(int shiftPositionsBy) {
        if (isSelectable()) {
            if (mSelectedItems != null) {
                //shift items
                for (int i = 0; i < mSelectedItems.size(); i++) {

                    int selectedPos = mSelectedItems.get(i);
                    mSelectedItems.set(i, selectedPos + shiftPositionsBy);
                }
                onChangeSelectedItems();
            }
        }
    }

    public interface SelectableViewHolder {
        void setSelected(boolean isSelected);

        int getAdapterPosition();
    }

    public interface OnSelectionChangeListener {
        void onSelectionStarted();

        void onSelectionChanged(List<Integer> selectedItems);

    }
}
