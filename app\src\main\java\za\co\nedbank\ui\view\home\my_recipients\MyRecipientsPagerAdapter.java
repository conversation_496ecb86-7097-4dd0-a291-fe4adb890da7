/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients;

import android.content.Context;
import android.util.Log;
import android.util.SparseArray;
import android.view.accessibility.AccessibilityEvent;
import android.widget.FrameLayout;
import android.widget.SectionIndexer;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.ViewPager;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.sharedui.listener.IViewPagerChildClickListener;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.CarouselLinearLayout;
import za.co.nedbank.core.view.recipient.UserBeneficiaryDataViewModel;

/**
 * Created by charurani on 17-08-2017.
 */

public class MyRecipientsPagerAdapter extends FragmentStatePagerAdapter implements ViewPager.OnPageChangeListener, SectionIndexer {

    private final static float BIG_SCALE = 1.0f;
    private final static float SMALL_SCALE = 0.85f;
    private final static float DIFF_SCALE = BIG_SCALE - SMALL_SCALE;
    public static final String TAG = MyRecipientsPagerAdapter.class.getSimpleName();

    private final Context mContext;
    private int mSelectedPosition = 0;

    private final SparseArray<Fragment> mPageReferenceMap;
    private final IRecipientsViewPagerListener mIRecipientsViewPagerListener;
    private LinkedHashSet<Integer> mSectionedListIndexSet = new LinkedHashSet<>();
    private List<UserBeneficiaryDataViewModel> mUserBeneficiaryDataViewModelList;
    private IViewPagerChildClickListener mIViewPagerChildClickListener;

    public MyRecipientsPagerAdapter(final Context context, final FragmentManager fm, final IRecipientsViewPagerListener listener, final IViewPagerChildClickListener iViewPagerChildClickListener) {
        super(fm);
        this.mContext = context;
        mPageReferenceMap = new SparseArray<>();
        this.mIRecipientsViewPagerListener = listener;
        this.mIViewPagerChildClickListener = iViewPagerChildClickListener;
    }

    @Override
    public int getItemPosition(Object object) {
        // Causes adapter to reload all Fragments when
        // notifyDataSetChanged is called
        return POSITION_NONE;
    }

    public LinkedHashSet<Integer> getSectionedListIndexSet() {
        return mSectionedListIndexSet;
    }

    public void setList(List<UserBeneficiaryDataViewModel> userBeneficiaryDataViewModelList) {
        if(mUserBeneficiaryDataViewModelList != null){
            mUserBeneficiaryDataViewModelList.clear();
            mSectionedListIndexSet.clear();
            mPageReferenceMap.clear();
        }
        mUserBeneficiaryDataViewModelList = userBeneficiaryDataViewModelList;
        for (UserBeneficiaryDataViewModel model : userBeneficiaryDataViewModelList) {
            int key = model.getContactCardName().toUpperCase().charAt(0);
            if (!(key >= Constants.ASCII_CODE.UPPER_CASE_A && key <= Constants.ASCII_CODE.UPPER_CASE_Z) &&
                    !(key >= Constants.ASCII_CODE.LOWER_CASE_A && key <= Constants.ASCII_CODE.LOWER_CASE_Z)) {
                key = StringUtils.UNSECTIONED_CHARACTER_KEY;
            }
            mSectionedListIndexSet.add(key);
        }
        notifyDataSetChanged();
    }

    public ArrayList<Integer> buildEnabledItemsList() {
        ArrayList<Integer> enabledItemsList = new ArrayList<>();
        if (mSectionedListIndexSet.size() > 0) {
            enabledItemsList.addAll(mSectionedListIndexSet);
        }
        return enabledItemsList;
    }

    @Override
    public Fragment getItem(int position) {
        boolean isEnabled;
        float scale;
        if (position == mSelectedPosition) {
            scale = BIG_SCALE;
            isEnabled = true;
        } else {
            scale = SMALL_SCALE;
            isEnabled = false;
        }

        Fragment fragment = getFragment(position);
        if (fragment != null && fragment instanceof MyRecipientsFragment) {
            ((MyRecipientsFragment) fragment).setIsEnabled(isEnabled);
        } else {
            fragment = MyRecipientsFragment.newInstance(mContext, scale, isEnabled, mUserBeneficiaryDataViewModelList.get(position), position);
            mPageReferenceMap.put(position, fragment);
        }
        ((MyRecipientsFragment) fragment).setIViewPagerChildClickListenerWeakReference(new WeakReference<>(mIViewPagerChildClickListener));
        return fragment;
    }


    @Override
    public int getCount() {
        return mUserBeneficiaryDataViewModelList != null ? mUserBeneficiaryDataViewModelList.size() : 0;
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        try {
            if (positionOffset >= 0f && positionOffset <= 1f) {
                CarouselLinearLayout cur = getRootView(position);
                if (cur != null) cur.setScaleBoth(BIG_SCALE - DIFF_SCALE * positionOffset);
                CarouselLinearLayout next = null;
                if (position != mUserBeneficiaryDataViewModelList.size() - 1) {
                    next = getRootView(position + 1);
                }
                if (next != null) {
                    next.setScaleBoth(SMALL_SCALE + DIFF_SCALE * positionOffset);
                }
            }
        } catch (Exception e) {
            NBLogger.e(TAG, Log.getStackTraceString(e));
        }
        mIRecipientsViewPagerListener.onScrolledPage();
    }

    @Override
    public void onPageSelected(int position) {
        FrameLayout frameLayout = getRootLayout(mSelectedPosition);
        if (frameLayout != null) {
            frameLayout.setSelected(false);
        }
        mSelectedPosition = position;

        frameLayout = getRootLayout(mSelectedPosition);
        if (frameLayout != null) {
            frameLayout.setSelected(true);
            // accessibility
            frameLayout.sendAccessibilityEvent(AccessibilityEvent.TYPE_ANNOUNCEMENT);
            frameLayout.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED);
        }
        mIRecipientsViewPagerListener.selectedPage(position);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    private Fragment getFragment(int key) {
        return mPageReferenceMap.get(key);
    }

    private CarouselLinearLayout getRootView(int position) {
        if (getFragment(position) != null && getFragment(position).getView() != null) {
            return getFragment(position).getView().findViewById(R.id.my_recipient_carousel_ll);
        }
        return null;
    }

    private FrameLayout getRootLayout(int position) {
        if (getFragment(position) != null && getFragment(position).getView() != null) {
            return getFragment(position).getView().findViewById(R.id.my_recipient_selector_root);
        }
        return null;
    }

    @Override
    public Object[] getSections() {
        return new Object[0];
    }

    @Override
    public int getPositionForSection(int i) {
        return -1;
    }

    @Override
    public int getSectionForPosition(int i) {
        int beginningChar = mUserBeneficiaryDataViewModelList.get(i).getContactCardName().toUpperCase().charAt(0);
        if (!(beginningChar >= Constants.ASCII_CODE.UPPER_CASE_A && beginningChar <= Constants.ASCII_CODE.UPPER_CASE_Z)
                && !(beginningChar >= Constants.ASCII_CODE.LOWER_CASE_A && beginningChar <= Constants.ASCII_CODE.LOWER_CASE_Z))
            beginningChar = (int) StringUtils.UNSECTIONED_CHARACTER_KEY;
        return beginningChar;
    }

    public int getCurrentItem(int charSelected) {
        int section = 0;
        boolean found = false;
        for (UserBeneficiaryDataViewModel myRecipientsViewModel : mUserBeneficiaryDataViewModelList) {
            if (myRecipientsViewModel.getContactCardName().toUpperCase().charAt(0) == (char) charSelected) {
                found = true;
                break;
            } else {
                section++;
            }
        }
        if (!found) {
            section = -1;
        }
        return section;
    }
}
