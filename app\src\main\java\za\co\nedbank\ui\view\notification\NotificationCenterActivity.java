package za.co.nedbank.ui.view.notification;

import android.os.Bundle;
import android.view.MenuItem;

import javax.inject.Inject;

import me.leolin.shortcutbadger.ShortcutBadger;
import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.preapprovedoffers.PreApprovedOffersUtility;
import za.co.nedbank.databinding.ActivityNotificationCenterBinding;
import za.co.nedbank.loans.preapprovedaccountsoffers.Constants;
import za.co.nedbank.ui.di.AppDI;

public class NotificationCenterActivity extends NBBaseActivity implements NotificationCenterView {

    @Inject
    NotificationCenterPresenter mNotificationCenterPresenter;
    @Inject
    FeatureSetController featureSetController;

    private String mScreenName;
    private ActivityNotificationCenterBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityNotificationCenterBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        initToolbar(binding.toolbar, true, getResources().getString(R.string.title_notifications));
        getDataFromIntent();
        toggleFeature();
        mNotificationCenterPresenter.bind(this);
        mNotificationCenterPresenter.onLoadAnalytics();
        if (isTransactionNotificationFeatureEnabled()|| isNotificationFeatureEnabled()) {
            mNotificationCenterPresenter.toggleTransactionMessagesReadUnread();
        }
        binding.offersContainer.setOnClickListener(v -> onClickOffers());
        binding.messagesContainer.setOnClickListener(v -> onClickMessages());
        binding.transactionsContainer.setOnClickListener(v -> onClickTransaction());
        binding.preferenceSettings.setOnClickListener(v -> onClickPreferenceSettings());
    }

    private void toggleFeature() {

        if (!isPreApprovedOfferFeatureEnabled()) {
            ViewUtils.hideViews(binding.offersContainer, binding.offersRowDivider);
        }

        if (!isNotificationFeatureEnabled()) {
            ViewUtils.hideViews(binding.messagesContainer, binding.messagesRowDivider);
        }

        if (!isTransactionNotificationFeatureEnabled()) {
            ViewUtils.hideViews(binding.transactionsContainer, binding.transactionRowDivider);
        }

        if (!isNotificationPreferencesFeatureEnabled()) {
            ViewUtils.hideViews(binding.preferenceSettings);
        }

        if (mNotificationCenterPresenter.isNonTpUser()
                || mNotificationCenterPresenter.isSalesUser()) {
            ViewUtils.hideViews(binding.preferenceSettings);
        } else {
            ViewUtils.showViews(binding.preferenceSettings);
        }
    }

    private boolean isPreApprovedOfferFeatureEnabled() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.PRE_APPROVED_ACCOUNTS);
    }

    private boolean isNotificationFeatureEnabled() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS);
    }

    private boolean isTransactionNotificationFeatureEnabled() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_TRANSACTION_NOTIFICATIONS);
    }

    private boolean isNotificationPreferencesFeatureEnabled() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.NOTIFICATIONS_PREFERENCES);
    }

    private void getDataFromIntent() {
        Bundle extras = getIntent().getExtras();
        if (extras != null && extras.containsKey(Constants.BundleKeys.SCREEN_NAME)) {
            mScreenName = extras.getString(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.NOTIFICATION_CENTER_SCREEN);

        }
        if (extras != null && extras.getBoolean(NotificationConstants.EXTRA.SHOULD_SHOW_ERROR, false)) {
            showError(getString(za.co.nedbank.R.string.we_couldnt_open_notification), getString(za.co.nedbank.R.string.error_please_try_again_later),
                    getString(za.co.nedbank.services.R.string.dialog_button_ok), () -> {
                    });
        }

    }


    @Override
    protected void onResume() {
        super.onResume();
        mNotificationCenterPresenter.toggleMessagesReadUnread();
        if (isPreApprovedOfferFeatureEnabled())
            mNotificationCenterPresenter.getPreApprovedOffersCount();
    }

    @Override
    public void receiveTransactionNotificationCount(int count) {
        if (count > 0) {
            ViewUtils.showViews(binding.tvTransactionsCounter);
            binding.tvTransactionsCounter.setText(PreApprovedOffersUtility.obtainNotificationCounterText(count));
        } else {
            ViewUtils.hideViews(binding.tvTransactionsCounter);
        }
    }

    @Override
    public void updateCount(int totalUnreadCount) {
        if (totalUnreadCount > 0) {
            ShortcutBadger.applyCount(getApplicationContext(), totalUnreadCount);
        } else {
            ShortcutBadger.removeCount(getApplicationContext());
        }
    }

    public void onClickOffers() {
        mNotificationCenterPresenter.navigateToNotificationOffers(mScreenName);
    }

    public void onClickMessages() {
        mNotificationCenterPresenter.navigateToNotificationMessages();
    }

    public void onClickTransaction() {
        mNotificationCenterPresenter.navigateToNotificationTransaction();
    }

    public void onClickPreferenceSettings() {
        mNotificationCenterPresenter.navigateToNotificationPreferences();
    }

    @Override
    public void markReadMessages(boolean isAllRead) {
        if (isAllRead) {
            ViewUtils.hideViews(binding.tvMessageCounter);
        } else {
            ViewUtils.showViews(binding.tvMessageCounter);
        }
    }

    @Override
    public void markReadOffers(boolean isAllRead) {
        if (isAllRead) {
            ViewUtils.hideViews(binding.tvOffersCounter);
        } else {
            ViewUtils.showViews(binding.tvOffersCounter);
        }
    }

    @Override
    public void updateMessageCounter(int unreadCount) {
        binding.tvMessageCounter.setText(PreApprovedOffersUtility.obtainNotificationCounterText(unreadCount));

    }

    @Override
    public void updateOffersCounter(int unreadCount) {
        binding.tvOffersCounter.setText(PreApprovedOffersUtility.obtainNotificationCounterText(unreadCount));
    }

    @Override
    protected void onDestroy() {
        mNotificationCenterPresenter.unbind();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        mNotificationCenterPresenter.navigateToHomeActivity(true);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == android.R.id.home) {
            mNotificationCenterPresenter.navigateToHomeActivity(true);
            mNotificationCenterPresenter.handleBackAnalytics();
        }
        return (super.onOptionsItemSelected(menuItem));
    }


}
