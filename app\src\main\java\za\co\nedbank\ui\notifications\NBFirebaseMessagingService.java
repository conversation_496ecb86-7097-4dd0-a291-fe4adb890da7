/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.notifications;

import android.Manifest;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;

import com.adobe.marketing.mobile.Event;
import com.adobe.marketing.mobile.EventSource;
import com.adobe.marketing.mobile.EventType;
import com.adobe.marketing.mobile.MobileCore;
import com.appsflyer.AppsFlyerLib;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import org.greenrobot.eventbus.EventBus;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;

import me.leolin.shortcutbadger.ShortcutBadger;
import za.co.nedbank.R;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.core.notification.NotificationEvent;
import za.co.nedbank.core.notification.mapper.NotificationDataToViewModelMapper;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.services.Constants;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.martec.MessagingConstants;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;


public class NBFirebaseMessagingService extends FirebaseMessagingService {

    private static final String TAG = "NBFirebaseMessagingService";
    private static final String UUID = "uuid";
    private static final String ENCRYPTED_PAYLOAD = "encrypted_payload";
    public static final String DEFAULT_CHANNEL_ID = "default_channel";

    @Inject
    NotificationHelper mNotificationHelper;

    @Inject
    ApplicationStorage mApplicationStorage;

    @Inject
    @Named("memory")
    ApplicationStorage mMemoryApplicationStorage;

    @Inject
    NotificationDataToViewModelMapper mNotificationDataToViewModelMapper;

    @Override
    public void onCreate() {
        super.onCreate();
        AppDI.getServiceComponent(this).inject(this);
    }

    @Override
    public void onNewToken(@NonNull String token) {
        NBLogger.d(TAG, "Refreshed token: " + token);
        // Sending new token to AppsFlyer
        AppsFlyerLib.getInstance().updateServerUninstallToken(getApplicationContext(), token);
        // Sending new token to Martec Adobe
        MobileCore.setPushIdentifier(token);
    }


    @Override
    public void onMessageReceived(@NonNull RemoteMessage remoteMessage) {
        super.onMessageReceived(remoteMessage);
        if (isAJONotification(remoteMessage)) {
            if (mApplicationStorage.getBoolean(StorageKeys.AJO_ADOBE_TOGGLE, false)) {
                final AjoPushPayloadDataModel payload = new AjoPushPayloadDataModel(remoteMessage);
                payload.fillRequiredData();

                // check permission to display notification
                if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                    return;
                }
                // display notification
                mNotificationHelper.generateAJONotification(this, payload);
                sendAJOPushTracking(remoteMessage);
            }
        } else {
            if (!remoteMessage.getData().containsKey("af-uinstall-tracking")) {
                NBLogger.e("Notification", remoteMessage.toString());
                //check if the push notification feature in disabled in toggle settings
                NBLogger.d(TAG, "From: " + remoteMessage.getFrom());
                handleFBNotifications(remoteMessage);
            }
        }
    }

    private static boolean isAJONotification(final @NonNull RemoteMessage remoteMessage) {
        return remoteMessage.getData().containsKey(MessagingConstants.Push.PayloadKeys.ACTION_TYPE);
    }

    private void sendAJOPushTracking(RemoteMessage remoteMessage) {
        // dispatch Push notification displayed event
        final HashMap<String, Object> notificationData = new HashMap<>(remoteMessage.getData());
        final Event pushNotificationReceivedEvent = new Event.Builder("Push Notification Displayed", EventType.MESSAGING, EventSource.RESPONSE_CONTENT).setEventData(notificationData).build();
        MobileCore.dispatchEvent(pushNotificationReceivedEvent);
    }

    private void handleTransaktNotifications(Map<String, String> remoteMessageData) {
        NotificationData notificationData = getTransaktNotificaton(remoteMessageData);
        handleNotification(notificationData);
    }

    private NotificationData getTransaktNotificaton(Map<String, String> remoteMessage) {
        NotificationData data = new NotificationData();
        data.setNotificationId(0);
        data.setHeading(remoteMessage.get(getResources().getString(R.string.payload)));
        data.setDisplayCategory(NotificationConstants.NOTIFICATION_TYPES.ITA);
        data.setNotificationType(NotificationConstants.NOTIFICATION_TYPES.ITA);
        data.setDistributionType(NotificationConstants.DISTRIBUTION_TYPES.ALL);
        data.setAllowAnonymous(false);

        data.setAuth(true);
        return data;
    }

    private void handleFBNotifications(RemoteMessage remoteMessage) {
        Map<String, String> remoteMessageData = remoteMessage.getData();
        if (remoteMessageData.size() > 0) {

            if (isPushAuth(remoteMessageData)) {
                NBLogger.d(TAG, "Message data payload from TRANSAKT: " + remoteMessageData);
                handleTransaktNotifications(remoteMessageData);
            } else {
                NotificationData notificationData = NotificationUtils.extractNotificationData(remoteMessageData.get(NotificationConstants.EXTRA.DATA_PAYLOAD));
                if (notificationData != null) {
                    String url = remoteMessageData.get(NotificationConstants.EXTRA.URL);
                    notificationData.setUrl(url);
                    handleNotification(notificationData);
                }

            }
        }
        // Check if message contains a notification payload.
        else if (remoteMessage.getNotification() != null) {
            sendNotification(remoteMessage.getNotification().getBody(), remoteMessage.getNotification().getTitle());
            NBLogger.d(TAG, "Message Notification Body: " + remoteMessage.getNotification().getBody());
        }
    }

    private int getSmallNotificationIcon() {
        return R.drawable.ic_oreo_notification_wrapper;
    }

    /**
     * Create and show a simple notification containing the received FCM message.
     *
     * @param messageBody - message description
     * @param title - message title
     */
    private void sendNotification(String messageBody, String title) {
        final PackageManager packageManager = getPackageManager();
        final Intent appLaunchIntent = packageManager.getLaunchIntentForPackage(getPackageName());

        // Don't use launchIntent - it'll restart application (rather than bringing it to front)
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        if (appLaunchIntent != null) {
            intent.setComponent(appLaunchIntent.getComponent());
        }

        PendingIntent pendingIntent;

        if ((Build.VERSION.SDK_INT < Build.VERSION_CODES.M)) {
            pendingIntent = PendingIntent.getActivity(this, Constants.ZERO /* Request code */, intent,
                    PendingIntent.FLAG_UPDATE_CURRENT);
        } else {
            pendingIntent = PendingIntent.getActivity(this, Constants.ZERO /* Request code */, intent,
                    PendingIntent.FLAG_IMMUTABLE);
        }


        final Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
        String titleNotification = TextUtils.isEmpty(title) ? getString(R.string.app_name) : title;
        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, DEFAULT_CHANNEL_ID)
                .setSmallIcon(getSmallNotificationIcon())
                .setContentTitle(titleNotification)
                .setContentText(messageBody)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(messageBody))
                .setAutoCancel(true)
                .setSound(defaultSoundUri)
                .setContentIntent(pendingIntent);

        final NotificationManager notificationManager =
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        if (notificationManager != null) {
            notificationManager.notify(Constants.ZERO /* ID of notification */, notificationBuilder.build());
        }
    }

    private void handleNotification(NotificationData notificationData) {

        mNotificationHelper.generateNotification(this, notificationData);
        //if notification received with
        // 1. distribution type UBC i.e un-tracked broadcast
        // 2. notification type chat
        // then no need for further processing
        if (notificationData == null
                || NotificationConstants.DISTRIBUTION_TYPES.UBC.equals(notificationData.getDistributionType())
                || NotificationConstants.DISTRIBUTION_TYPES.TBC.equals(notificationData.getDistributionType())
                || NotificationConstants.NOTIFICATION_TYPES.CHAT.equalsIgnoreCase(notificationData.getNotificationType())
                || notificationData.isAuth() || isContextSwitchNeeded(notificationData.getClient().getCisNo())) {
            return;
        }
        int unreadCountMsg = 0;
        int unreadCountTxn = 0;
        //increase unread count in memory storage
        if (notificationData.getNotificationType().equals(NotificationConstants.NOTIFICATION_TYPES.TRANSACTION)) {
            unreadCountMsg = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, 0);
            mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, ++unreadCountMsg);
        } else {
            unreadCountTxn = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, 0);
            mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, ++unreadCountTxn);
        }
        int totalUnreadNotificationCount = mApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, 0);
        mApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, ++totalUnreadNotificationCount);

        int totalUnreadCount = unreadCountMsg + unreadCountTxn;
        if (totalUnreadCount > 0)
            ShortcutBadger.applyCount(getApplicationContext(), totalUnreadCount);
        //broadcast event to be listened when app is in foreground to refresh UI
        FBNotificationsViewModel notificationViewModel = mNotificationDataToViewModelMapper.transform(notificationData);
        NotificationEvent notificationEvent = new NotificationEvent();
        notificationEvent.setFbNotificationsViewModel(notificationViewModel);
        notificationEvent.setUnreadCount(totalUnreadCount);
        EventBus.getDefault().post(notificationEvent);
    }

    private boolean isContextSwitchNeeded(String cisNo) {
        return !cisNo.equals(mMemoryApplicationStorage.getString(StorageKeys.CIS_NUMBER, StringUtils.EMPTY_STRING));
    }

    /**
     * If the data contains only the UUID and no ECRYPTED_PAYLOAD
     * then it is just a normal push notification without the encrypted auth
     */
    private boolean isPushAuth(Map<String, String> data) {
        return data.containsKey(UUID) && data.containsKey
                (ENCRYPTED_PAYLOAD);
    }

}
