package za.co.nedbank.ui.view.notification.transaction_notification.sort_notifications;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.databinding.ActivitySortNotificationsBinding;
import za.co.nedbank.ui.di.AppDI;

public class SortNotificationsActivity extends NBBaseActivity implements SortNotificationsView {

    private int sortSelection = 0;
    List<String> options = new ArrayList<>();

    @Inject
    SortNotificationsPresenter mSortNotificationsPresenter;
    private ActivitySortNotificationsBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivitySortNotificationsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        initToolbar(binding.toolbar, true, getResources().getString(R.string.sort_transaction_notifications));
        mSortNotificationsPresenter.bind(this);
        receiveBundle();
        setupUI();
        options.add(getString(R.string.most_recent_notif));
        options.add(getString(R.string.oldest_date));
        options.add(getString(R.string.highest_amount));
        options.add(getString(R.string.lowest_amount));
        announceForAccessibility(String.format("%s%s%s%s%s", getString(R.string.sort_by), options.get(0), options.get(1), options.get(2), options.get(3)));

        binding.highestAmountContainer.setOnClickListener(this::onClickSortChange);
        binding.oldestDateContainer.setOnClickListener(this::onClickSortChange);
        binding.mostRecentContainer.setOnClickListener(this::onClickSortChange);
        binding.lowestAmountContainer.setOnClickListener(this::onClickSortChange);
    }

    private void setupUI() {
        switch (sortSelection) {
            case 0:
                binding.mostRecentCheck.setVisibility(View.VISIBLE);
                binding.oldestDateCheck.setVisibility(View.GONE);
                binding.highestAmountCheck.setVisibility(View.GONE);
                binding.lowestAmountCheck.setVisibility(View.GONE);
                break;
            case 1:
                binding.oldestDateCheck.setVisibility(View.VISIBLE);
                binding.mostRecentCheck.setVisibility(View.GONE);
                binding.highestAmountCheck.setVisibility(View.GONE);
                binding.lowestAmountCheck.setVisibility(View.GONE);
                break;
            case 2:
                binding.lowestAmountCheck.setVisibility(View.VISIBLE);
                binding.oldestDateCheck.setVisibility(View.GONE);
                binding.mostRecentCheck.setVisibility(View.GONE);
                binding.highestAmountCheck.setVisibility(View.GONE);
                break;
            case 3:
                binding.highestAmountCheck.setVisibility(View.VISIBLE);
                binding.oldestDateCheck.setVisibility(View.GONE);
                binding.mostRecentCheck.setVisibility(View.GONE);
                binding.lowestAmountCheck.setVisibility(View.GONE);
                break;
            default:
        }
    }

    private void receiveBundle() {
        if (getIntent() != null) {
            sortSelection = IntentUtils.getIntegerValue(getIntent(), NotificationConstants.EXTRA.SORT_INDEX, 0);
        }
    }

    public void onClickSortChange(View view) {
        Intent intent = new Intent();

        switch (view.getId()) {
            case R.id.most_recent_container:
                binding.mostRecentCheck.setVisibility(View.VISIBLE);
                binding.oldestDateCheck.setVisibility(View.GONE);
                binding.highestAmountCheck.setVisibility(View.GONE);
                binding.lowestAmountCheck.setVisibility(View.GONE);
                sortSelection = 0;
                break;
            case R.id.oldest_date_container:
                binding.oldestDateCheck.setVisibility(View.VISIBLE);
                binding.mostRecentCheck.setVisibility(View.GONE);
                binding.highestAmountCheck.setVisibility(View.GONE);
                binding.lowestAmountCheck.setVisibility(View.GONE);
                sortSelection = 1;
                break;
            case R.id.lowest_amount_container:
                binding.lowestAmountCheck.setVisibility(View.VISIBLE);
                binding.oldestDateCheck.setVisibility(View.GONE);
                binding.mostRecentCheck.setVisibility(View.GONE);
                binding.highestAmountCheck.setVisibility(View.GONE);
                sortSelection = 2;
                break;
            case R.id.highest_amount_container:
                binding.highestAmountCheck.setVisibility(View.VISIBLE);
                binding.oldestDateCheck.setVisibility(View.GONE);
                binding.mostRecentCheck.setVisibility(View.GONE);
                binding.lowestAmountCheck.setVisibility(View.GONE);
                sortSelection = 3;
                break;
            default:
        }
        announceForAccessibility(getString(R.string.you_have_selected_sort_by) + options.get(sortSelection));
        intent.putExtra(NotificationConstants.EXTRA.SORT, sortSelection);
        setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    private void announceForAccessibility(String accesiblitycontent) {
        Observable.timer(2, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result -> AccessibilityUtils.announceForAccessibility(this, accesiblitycontent));

    }
}
