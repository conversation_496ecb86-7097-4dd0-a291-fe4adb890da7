/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.entity.money_request;

import com.squareup.moshi.Json;

public class PaymentRequestEntity {
    @Json(name = "amount")
    private double amount;
    @<PERSON><PERSON>(name = "description")
    private String description;
    @<PERSON><PERSON>(name = "itemAccountId")
    private String itemAccountId;
    @<PERSON><PERSON>(name = "payerPhoneNumber")
    private String payerPhoneNumber;
    @<PERSON><PERSON>(name = "payerName")
    private String payerName;

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setItemAccountId(String itemAccountId) {
        this.itemAccountId = itemAccountId;
    }

    public void setPayerPhoneNumber(String payerPhoneNumber) {
        this.payerPhoneNumber = payerPhoneNumber;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }
}
