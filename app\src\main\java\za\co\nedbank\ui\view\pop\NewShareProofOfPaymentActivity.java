package za.co.nedbank.ui.view.pop;

import static com.google.android.material.snackbar.BaseTransientBottomBar.LENGTH_INDEFINITE;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.content.ContextCompat;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.Map;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.dialog.IDialog;
import za.co.nedbank.core.base.dialog.UserConsentDialog;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;
import za.co.nedbank.databinding.ActivityNewShareProofOfPaymentBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.domain.model.pop.SharePopNotificationTypeData;
import za.co.nedbank.uisdk.component.CompatEditText;

public class NewShareProofOfPaymentActivity extends NBBaseActivity implements NewShareProofOfPaymentView {

    @Inject
    NewShareProofOfPaymentPresenter mPresenter;
    String mSelectedSharePOPMethod;
    boolean showFailureScreen;
    String transactionId;
    ArrayList<CompatEditText> inputs = new ArrayList<>();
    private TransactionHistoryViewModel transactionHistoryViewModel;
    private boolean isFromRecentPaymentFlow = false;
    private boolean isSopFromPayDone=false;
    private boolean isBeyond90daysTransaction = false;
    private boolean isLandingOnOverView = false;
    private int notificationTypeCount = 0;
    private UserConsentDialog mUserConsentDialog;
    private ActivityNewShareProofOfPaymentBinding binding;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityNewShareProofOfPaymentBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        mPresenter.bind(this);
        initToolbar(binding.toolbar, true, true);

        if (notificationTypeCount == 0) {
            binding.notificationContainer.removeAllViews();
        }
        if (getIntent() != null) {
            transactionHistoryViewModel = getIntent().getParcelableExtra(Constants.BUNDLE_KEYS.TRANSACTION_HISTORY_VIEW_MODEL);
            isFromRecentPaymentFlow = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_RECENT_PAYMENT_FLOW, false);
            isSopFromPayDone = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_SOP_FROM_PAY_DONE, false);
            isBeyond90daysTransaction = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_BEYOND_90_DAYS_TRANSACTION, false);
            isLandingOnOverView = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_LANDING_ON_OVERVIEW, false);
            mSelectedSharePOPMethod = getIntent().getStringExtra(Constants.BUNDLE_KEYS.SELECTED_SHARE_POP_METHOD);
            addNewNotificationView(mSelectedSharePOPMethod);
        }
        binding.tvAddNotification.setOnClickListener(v -> addNotificationClick());
        binding.shareBtn.setOnClickListener(v -> shareBtnClick());
    }

    private void changeDeleteButtonState() {
        int size = binding.notificationContainer.getChildCount();
        if (size == 1) {
            enableDisableDeleteButtons(false, 0);
        } else {
            for (int i = 0; i < size; i++) {
                enableDisableDeleteButtons(true, i);
            }
        }
        setVisibilityOfAddNotificationButton();
    }

    private void enableDisableDeleteButtons(boolean state, int position) {
        ViewGroup view = (ViewGroup) binding.notificationContainer.getChildAt(position);
        View deleteIcon = view.findViewById(R.id.delete_icon);
        ViewUtils.enableDisableViews(state, deleteIcon);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.unbind();
    }


    private void onTextChanges(CompatEditText nbCompatEditText) {
        RxTextView.textChanges(nbCompatEditText.getInputField()).subscribe(chars -> {
            checkField(nbCompatEditText);
        }, throwable -> NBLogger.e(Constants.TAG, Log.getStackTraceString(throwable)));

    }

    private void checkField(CompatEditText nbCompatEditText) {
        if (nbCompatEditText.hasError()) nbCompatEditText.clearErrors();
        mSelectedSharePOPMethod = (String) nbCompatEditText.getTag();
        checkInputs();
    }

    private void onFocusChanges(CompatEditText nbCompatEditText) {
       nbCompatEditText.setOnFocusChangeListener((v, hasFocus) -> {
           checkField(nbCompatEditText);
       });
    }

    private void checkInputs() {
        if (mPresenter != null) {
            inputs.clear();
            int size = binding.notificationContainer.getChildCount();
            for (int i = 0; i < size; i++) {
                CompatEditText editText = (CompatEditText) binding.notificationContainer.getChildAt(i).findViewById(R.id.edt_item);
                inputs.add(editText);
            }
            mPresenter.checkInputsOnScreen(inputs);
        }
    }

    public void addNotificationClick() {
        mPresenter.navigateToSharePOPMethodSelectionScreen();
    }

    public void shareBtnClick() {

        if (transactionHistoryViewModel != null) {
            transactionId = String.valueOf(transactionHistoryViewModel.getTransactionID());
            postSharePOP();
        }
    }

    @Override
    public void setResult(Map<String, Object> params) {
        readResult(params);
    }

    private void readResult(Map<String, Object> resultMap) {
        if (null != resultMap && resultMap.size() > 0) {
            mSelectedSharePOPMethod = (String) resultMap.get(Constants.BUNDLE_KEYS.SELECTED_SHARE_POP_METHOD);
        }
        addNewNotificationView(mSelectedSharePOPMethod);
    }

    @Override
    public String getSelectedSharePOPMethod() {
        return mSelectedSharePOPMethod;
    }

    @Override
    public void setSharedButtonEnabled(boolean isEnabled) {
        binding.shareBtn.setEnabled(isEnabled);
        changeStateOfAddNotificationButton(isEnabled);
    }

    private void changeStateOfAddNotificationButton(boolean isEnabled) {
        binding.tvAddNotification.setEnabled(isEnabled);
        binding.tvAddNotification.setTextColor(isEnabled ? ContextCompat.getColor(NewShareProofOfPaymentActivity.this, R.color.green_009639) : ContextCompat.getColor(NewShareProofOfPaymentActivity.this, R.color.disable_gray));
    }

    private void setVisibilityOfAddNotificationButton() {
        if (binding.notificationContainer.getChildCount() == 3) {
            ViewUtils.hideViews(binding.tvAddNotification);
        } else {
            ViewUtils.showViews(binding.tvAddNotification);
        }

    }

    @Override
    public void trackFailure(boolean isApiFailure, String errorCode) {
        mPresenter.trackSharePOPFailure(isApiFailure, getString(R.string.we_couldnt_share_pop), errorCode);
        if(transactionHistoryViewModel!=null && transactionHistoryViewModel.isFromSinglePayFlow()){
            mPresenter.trackSinglePayFlowSharePOPFailure(isApiFailure, getString(R.string.we_couldnt_share_pop), errorCode);
        }
        else if(transactionHistoryViewModel!=null && transactionHistoryViewModel.isFromPayAgainFlow()){
            mPresenter.trackPayAgainFlowSharePOPFailure(isApiFailure, getString(R.string.we_couldnt_share_pop), errorCode);
        }
        else if(transactionHistoryViewModel!=null && transactionHistoryViewModel.isQuickPayFlow()){
            mPresenter.trackQuickPayFlowSharePOPFailure(isApiFailure, getString(R.string.we_couldnt_share_pop), errorCode);
        }
    }

    @Override
    public void sharePOPFailure() {
        if (showFailureScreen)
            mPresenter.navigateToSharePOPFailureScreen(isSopFromPayDone,isFromRecentPaymentFlow, isLandingOnOverView);
        else {
            showFailureScreen = true;
            showSnackBar();
        }
    }

    @Override
    public void sharePOPSuccess() {
        mPresenter.navigateToSharePOPSuccessScreen(isSopFromPayDone,isFromRecentPaymentFlow, isBeyond90daysTransaction, isLandingOnOverView, getNotificationTypeListForAdobe(), transactionId);

        if(transactionHistoryViewModel!=null && transactionHistoryViewModel.isFromSinglePayFlow()){
            mPresenter.trackSinglePayFlowSharePOPSuccess();
        }
        else if(transactionHistoryViewModel!=null && transactionHistoryViewModel.isFromPayAgainFlow()){
            mPresenter.trackPayAgainFlowSharePOPSuccess();
        }
        else if(transactionHistoryViewModel!=null && transactionHistoryViewModel.isQuickPayFlow()){
            mPresenter.trackQuickPayFlowSharePOPSuccess();
        }
    }

    @Override
    public void showLoading(boolean isLoading) {
        binding.toolbarViewProgress.setVisibility(isLoading ? View.VISIBLE : View.INVISIBLE);
        setEnabledActivityTouch(!isLoading);
    }

    @Override
    public boolean isFromRecipientHistory() {
        return transactionHistoryViewModel.isFromRecipientHistory();
    }

    private void showSnackBar() {
        showError(getString(R.string.we_couldnt_share_pop), getString(R.string.try_again), getString(za.co.nedbank.core.R.string.snackbar_action_retry),
                LENGTH_INDEFINITE, () -> {
                    if (transactionHistoryViewModel != null) {
                        postSharePOP();
                    }
                });
    }


    private void postSharePOP() {
        mPresenter.postShareProofOfPayment(isSopFromPayDone,getNotificationTypeList(), transactionId,isFromRecentPaymentFlow, transactionHistoryViewModel.getTransactionType(), FormattingUtil.getFormattedDateWithOutTime(transactionHistoryViewModel.getStartDate()),transactionHistoryViewModel.isSchedulePayment());
    }

    private ArrayList<SharePopNotificationTypeData> getNotificationTypeList() {
        ArrayList<SharePopNotificationTypeData> sharePopNotificationTypeData = new ArrayList<>();
        for (CompatEditText nbCompatEditText : inputs) {
            SharePopNotificationTypeData sharePopNotificationTypeData1 = new SharePopNotificationTypeData();
            String type = (String) nbCompatEditText.getTag();
            sharePopNotificationTypeData1.setNotificationType(type.toUpperCase());
            sharePopNotificationTypeData1.setNotificationAddress(nbCompatEditText.getValue());
            sharePopNotificationTypeData.add(sharePopNotificationTypeData1);
        }
        return sharePopNotificationTypeData;
    }

    private String getNotificationTypeListForAdobe() {
        StringBuilder notificationsType = new StringBuilder();

        LinkedHashSet<String> set = new LinkedHashSet<>();
        for (CompatEditText nbCompatEditText : inputs) {
            String type = (String) nbCompatEditText.getTag();
            set.add(type);
        }

        boolean isNotFirstElement = false;
        for (String s : set) {
            if (isNotFirstElement) {
                notificationsType.append(" | ");
            }
            isNotFirstElement = true;
            notificationsType.append(s);
        }
        return notificationsType.toString();
    }

    private void addNewNotificationView(String type) {
        notificationTypeCount++;
        LayoutInflater inflater = LayoutInflater.from(this);
        int layoutID = -1;
        switch (type) {
            case Constants.SharePOPMethod.EMAIL:
                layoutID = R.layout.view_add_notification_type_email_item;
                break;
            case Constants.SharePOPMethod.SMS:
                layoutID = R.layout.view_add_notification_type_sms_item;
                break;
            case Constants.SharePOPMethod.FAX:
                layoutID = R.layout.view_add_notification_type_fax_item;
                break;
            default:
                layoutID = R.layout.view_add_notification_type_email_item;
        }
        View layout = inflater.inflate(layoutID, binding.notificationContainer, false);

        CompatEditText nbCompatEditText = (CompatEditText) layout.findViewById(R.id.edt_item);
        View deleteIcon = layout.findViewById(R.id.delete_icon);

        layout.setTag(notificationTypeCount - 1);
        nbCompatEditText.setTag(type);
        onTextChanges(nbCompatEditText);
        onFocusChanges(nbCompatEditText);
        deleteIcon.setTag(notificationTypeCount - 1);

        deleteIcon.setOnClickListener(view -> showDialogForUserConsent(view));
        binding.notificationContainer.addView(layout);
        updateHeaderCount(notificationTypeCount);
        changeDeleteButtonState();
        setSharedButtonEnabled(false);
    }


    public void showDialogForUserConsent(View view) {
        mUserConsentDialog = UserConsentDialog.getInstance(getString(R.string.remove_share_pop_title), getString(R.string.remove_share_pop_message),
                getString(R.string.share_pop_remove), getString(R.string.cancel));
        mUserConsentDialog.setCancelable(true);
        mUserConsentDialog.setiDialog(new IDialog() {
            @Override
            public void onPositiveButtonClick() {
                mUserConsentDialog.dismiss();
                int position = (Integer) view.getTag();
                deleteNotificationTypeFromList(position);
            }

            @Override
            public void onNegativeButtonClick() {
                mUserConsentDialog.dismiss();
            }
        });
        if (!mUserConsentDialog.isVisible()) {
            mUserConsentDialog.show(NewShareProofOfPaymentActivity.this.getSupportFragmentManager(), UserConsentDialog.TAG);
        }
    }

    private void updateHeaderCount(int count) {
        binding.notificationsCountHeader.setText(String.format(getResources().getString(R.string.notifications_1_3), count));
    }

    private void deleteNotificationTypeFromList(int position) {
        notificationTypeCount--;
        updateHeaderCount(notificationTypeCount);
        binding.notificationContainer.removeViewAt(position);
        int size = binding.notificationContainer.getChildCount();
        for (int i = 0; i < size; i++) {
            ViewGroup view = (ViewGroup) binding.notificationContainer.getChildAt(i);
            View deleteIcon = view.findViewById(R.id.delete_icon);
            view.setTag(i);
            deleteIcon.setTag(i);
        }
        changeDeleteButtonState();
        checkInputs();
    }
}