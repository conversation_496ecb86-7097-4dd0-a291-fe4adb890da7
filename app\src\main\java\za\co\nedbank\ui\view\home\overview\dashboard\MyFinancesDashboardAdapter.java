/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.overview.dashboard;


import android.content.Context;
import android.view.View;

import androidx.collection.ArrayMap;

import za.co.nedbank.core.dashboard.CardAdapter;
import za.co.nedbank.core.dashboard.DashboardCard;
import za.co.nedbank.core.dashboard.DashboardCardType;
import za.co.nedbank.core.sharedui.ui.NBCardWidget;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.ui.view.home.IEventListener;
import za.co.nedbank.ui.view.home.coming_soon.ComingSoonDashboardCard;
import za.co.nedbank.ui.view.home.coming_soon.ComingSoonWidget;
import za.co.nedbank.ui.view.home.payme_tab.PayMeTabWidget;

public class MyFinancesDashboardAdapter extends CardAdapter {

    private final IEventListener mIEventListener;

    private final ArrayMap<DashboardCardType, NBCardWidget> mDashboardCardSparseArray;

    private int mDeviceWidth;

    private PayMeTabWidget.OnTabSelectListener mOnTabSelectListener;

    public MyFinancesDashboardAdapter(final Context context
            , final IEventListener iEventListener) {
        super(context);
        this.mIEventListener = iEventListener;
        this.mDashboardCardSparseArray = new ArrayMap<>();
    }

    @Override
    public DashboardCard getCardForType(final DashboardCardType cardType) {
        if (cardType == DashboardCardType.COMING_SOON) {
            return getComingSoon();
        }

        return null;
    }

    public void setDeviceWidth(int deviceWidth) {
        this.mDeviceWidth = deviceWidth;
    }

    public void setOnTabSelectListener(PayMeTabWidget.OnTabSelectListener listener){
        this.mOnTabSelectListener = listener;
    }

    private DashboardCard getComingSoon() {
        ComingSoonWidget comingSoonWidget = new ComingSoonWidget(context);
        comingSoonWidget.setTitle(StringUtils.EMPTY_STRING);
        return new ComingSoonDashboardCard(DashboardCardType.COMING_SOON, comingSoonWidget);
    }

    @Override
    protected DashboardCardType[] getSupportedTypes() {
        return new DashboardCardType[]{DashboardCardType.PIE_CHART, DashboardCardType.MY_RECIPIENTS,
                DashboardCardType.MONEY_REQUESTS, /*DashboardCardType.LINK_MY_FINANCES,*/ DashboardCardType.COMING_SOON, DashboardCardType.PRE_APPROVED_ACCOUNTS};
    }

    @Override
    public void cleanup() {
        super.cleanup();
        mDashboardCardSparseArray.clear();
    }

    public View getComponent(){
        if(mDashboardCardSparseArray != null && mDashboardCardSparseArray.get(DashboardCardType.MY_RECIPIENTS) != null) {
            return mDashboardCardSparseArray.get(DashboardCardType.MY_RECIPIENTS);
        }
        return null;
    }
}
