package za.co.nedbank.ui.view.home.quick_pay;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;

public class QuickPayPresenter extends NBBasePresenter<QuickPayView> {

    private final NavigationRouter mNavigationRouter;

    @Inject
    QuickPayPresenter(final NavigationRouter mNavigationRouter) {
        this.mNavigationRouter = mNavigationRouter;
    }
}
