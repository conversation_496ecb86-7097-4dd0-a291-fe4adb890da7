/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import androidx.annotation.NonNull;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;

/**
 * Created by sandip.lawate on 2/15/2018.
 */

class MoneyRequestSuccessPresenter extends NBBasePresenter<MoneyRequestSuccessView> {

    private final NavigationRouter mNavigationRouter;

    @Inject
    MoneyRequestSuccessPresenter(@NonNull NavigationRouter navigationRouter) {
        this.mNavigationRouter = navigationRouter;
    }

    void navigateToDashboardScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.HOME);
        navigationTarget.withIntentFlagClearTop(true);
        mNavigationRouter.navigateTo(navigationTarget);
    }
}
