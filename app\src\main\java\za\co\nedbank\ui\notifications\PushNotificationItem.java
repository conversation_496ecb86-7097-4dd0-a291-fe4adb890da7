package za.co.nedbank.ui.notifications;

import android.app.NotificationChannel;
import android.app.PendingIntent;

import androidx.core.app.NotificationCompat;

import java.util.List;

import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;

public class PushNotificationItem {

    private NotificationData notificationData;
    private AjoPushPayloadDataModel ajoNotificationData;
    private PendingIntent pendingIntent;
    private List<NotificationCompat.Action> actions;
    private PendingIntent deletePendingIntent;
    private NotificationChannel channel;

    public List<NotificationCompat.Action> getActions() {
        return actions;
    }

    public void setActions(List<NotificationCompat.Action> actions) {
        this.actions = actions;
    }

    public AjoPushPayloadDataModel getAjoNotificationData() {
        return ajoNotificationData;
    }

    public void setAjoNotificationData(AjoPushPayloadDataModel ajoNotificationData) {
        this.ajoNotificationData = ajoNotificationData;
    }

    public NotificationData getNotificationData() {
        return notificationData;
    }

    public void setNotificationData(NotificationData notificationData) {
        this.notificationData = notificationData;
    }

    public PendingIntent getPendingIntent() {
        return pendingIntent;
    }

    public PendingIntent getDeletePendingIntent() {
        return deletePendingIntent;
    }

    public void setPendingIntent(PendingIntent pendingIntent) {
        this.pendingIntent = pendingIntent;
    }

    public NotificationChannel getChannel() {
        return channel;
    }

    public void setChannel(NotificationChannel channel) {
        this.channel = channel;
    }

    public void setDeletePendingIntent(PendingIntent deletePendingIntent) {
        this.deletePendingIntent = deletePendingIntent;
    }
}
