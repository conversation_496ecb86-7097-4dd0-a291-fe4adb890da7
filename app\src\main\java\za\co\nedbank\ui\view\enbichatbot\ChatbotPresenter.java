package za.co.nedbank.ui.view.enbichatbot;

import static za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_FOR_APPLICATION;
import static za.co.nedbank.core.convochatbot.view.ChatbotConstants.INTENT_IDENTIFIERS.INTENT_DISCS_FINES;
import static za.co.nedbank.core.convochatbot.view.ChatbotConstants.INTENT_IDENTIFIERS.INTENT_HOME_LOAN;
import static za.co.nedbank.core.convochatbot.view.ChatbotConstants.INTENT_IDENTIFIERS.INTENT_PAYSHAPP;
import static za.co.nedbank.core.convochatbot.view.ChatbotConstants.INTENT_IDENTIFIERS.INTENT_POS_APPLICATIONS;
import static za.co.nedbank.core.data.storage.StorageKeys.TRAVEL_CARD_MINOR;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_BIRTH_DATE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_CLIENT_TYPE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD;
import static za.co.nedbank.core.notification.NotificationConstants.NAVIGATION_TARGET.PAY_MY_BILLS_SCHEDULE;
import static za.co.nedbank.core.notification.NotificationConstants.Navigation.ENBI_CHAT;
import static za.co.nedbank.core.tracking.ChatEducationTrackingValue.VAL_EDUCATION_CONNECTION_SCREEN_STATE;
import static za.co.nedbank.core.tracking.ChatEducationTrackingValue.VAL_EDUCATION_SCREEN_ENTRY_POINT;
import static za.co.nedbank.core.tracking.ChatEducationTrackingValue.VAL_EDUCATION_SCREEN_FEATURE;
import static za.co.nedbank.core.tracking.ChatEducationTrackingValue.VAL_EDUCATION_SCREEN_FEATURE_CATEGORY;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_CHAT_SCREEN_LEAVE;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_CHAT_SCREEN_NAME;
import static za.co.nedbank.core.tracking.TrackingEvent.ANALYTICS.VAL_QUIT_MODAL;
import static za.co.nedbank.services.view.navigation.ServicesNavigationTarget.INVONLINE_CLIENT_TYPE;

import android.text.TextUtils;
import android.util.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.R;
import za.co.nedbank.core.ResponseStorageKey;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.convochatbot.chatbotutil.CapiRequest;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotAuthenticatedPayloadDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotAuthenticatedPayloadSBSDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotBranchCodeViewModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotCardDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotCardInfoDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotHistoryResponseModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotListContentPayloadButtonDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotMessageContentsResponseDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotPayloadResponseDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotQuickRepliesResponseDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotSessionMainRequestDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotSessionMainResponseDataModel;
import za.co.nedbank.core.convochatbot.domain.mapper.ChatbotBranchCodeDataModelToBranchCodeViewModelMapper;
import za.co.nedbank.core.convochatbot.domain.mapper.ChatbotCardDataModelToViewModelMapper;
import za.co.nedbank.core.convochatbot.domain.mapper.ChatbotCardInfoToDataMapper;
import za.co.nedbank.core.convochatbot.domain.usecase.ChatbotBranchCodeUseCase;
import za.co.nedbank.core.convochatbot.domain.usecase.ChatbotGetCardsUseCase;
import za.co.nedbank.core.convochatbot.domain.usecase.ChatbotGetOverviewUseCase;
import za.co.nedbank.core.convochatbot.domain.usecase.ChatbotHistoryUseCase;
import za.co.nedbank.core.convochatbot.domain.usecase.ChatbotPostUserMessageUseCase;
import za.co.nedbank.core.convochatbot.domain.usecase.ChatbotStartSessionUseCase;
import za.co.nedbank.core.convochatbot.domain.usecase.ForgotPasswordWorkFlowUseCase;
import za.co.nedbank.core.convochatbot.view.ChatbotAccountSummary;
import za.co.nedbank.core.convochatbot.view.ChatbotAccountsOverview;
import za.co.nedbank.core.convochatbot.view.ChatbotConstants;
import za.co.nedbank.core.convochatbot.view.ChatbotErrorCodes;
import za.co.nedbank.core.convochatbot.view.ChatbotOnlineSavingsAccounts;
import za.co.nedbank.core.convochatbot.view.ChatbotOverview;
import za.co.nedbank.core.convochatbot.view.ChatbotView;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.accounts.AccountDetailData;
import za.co.nedbank.core.domain.model.profile.UserProfile;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetAppointmentListUseCase;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.accounts.GetAccountDetailUseCase;
import za.co.nedbank.core.domain.usecase.enrol.LoginSecurityUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.CheckIfUserAdminUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.domain.usecase.ita.ITADeviceManagementUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetMdmProfileUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetProfileUseCase;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseData;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseDataToViewModelMapper;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseViewModel;
import za.co.nedbank.core.payment.recent.RecentPaymentUseCase;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.ChatTracking;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.ApplicantIdForeignCheckValidator;
import za.co.nedbank.core.view.mapper.AppointmentResponseDataToViewModelMapper;
import za.co.nedbank.core.view.model.booking.AppointmentsResponseViewModel;
import za.co.nedbank.core.view.model.booking.BookingEntryType;
import za.co.nedbank.nid_sdk.base.utils.DecodeTokenUtils;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextDataViewModelMapper;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextFedarationDetailsViewModel;
import za.co.nedbank.services.greenbackapp_v2.view.util.GBConstants;
import za.co.nedbank.services.greenbackapp_v2.view.util.GBUtils;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.domain.model.AvoWalletDetailsModel;
import za.co.nedbank.ui.domain.usecase.GetAvoWalletDetailsUseCase;
import za.co.nedbank.ui.view.home.AvoToggleUtils;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class ChatbotPresenter extends NBBasePresenter<ChatbotView> {
    private static final String TAG = ChatbotPresenter.class.getCanonicalName();

    private final Analytics analytics;
    private final NavigationRouter navigationRouter;
    private final FeatureSetController featureSetController;
    private final ApplicationStorage memoryApplicationStorage;
    private final ChatbotStartSessionUseCase chatbotStartSessionUseCase;
    private final ChatbotPostUserMessageUseCase chatbotPostUserMessageUseCase;
    private final ChatbotHistoryUseCase chatbotHistoryUseCase;
    final ErrorHandler errorHandler;
    private final APIInformation apiInformation;
    private final ForgotPasswordWorkFlowUseCase forgotPasswordWorkFlowUseCase;
    private final LoginSecurityUseCase loginSecurityUseCase;
    private final ITADeviceManagementUseCase mItaDeviceManagementUseCase;

    private final GetAccountDetailUseCase mGetAccountDetailUseCase;
    private final GetUserDetailUseCase getUserDetailUseCase;
    private final ChatbotBranchCodeUseCase chatbotBranchCodeUseCase;
    private final ChatbotBranchCodeDataModelToBranchCodeViewModelMapper modelToBranchCodeViewModelMapper;
    private final ChatbotGetOverviewUseCase mGetOverviewUseCase;
    private int apiRetryCounter = 0;
    boolean chatbotCameFromLogin = false;
    private final GetUserDetailUseCase mGetUserDetailUseCase;
    private final ApplicationStorage applicationStorage;
    private final ChatbotGetCardsUseCase mGetCardsUseCase;
    private final ChatbotCardInfoToDataMapper mCardInfoToDataMapper;
    private Map<String, ChatbotCardInfoDataModel> mCardInfoMap = new HashMap<>();

    private final CheckIfUserAdminUseCase checkIfUserAdminUseCase;
    private final ApplicantIdForeignCheckValidator mApplicantIdForeignCheckValidator;
    private final GetProfileUseCase mGetProfileUseCase;
    private final RecentPaymentUseCase mChatbotRecentPaymentUseCase;
    private RecentPaymentResponseViewModel mRecipientPaymentViewModel;
    private RecentPaymentResponseViewModel mOnceOffPaymentsViewModel;
    private final RecentPaymentResponseDataToViewModelMapper mChatbotRecentPaymentResponseDataToViewModelMapper;
    private String mScOfficerCD;
    private String conversationId = "";
    boolean isPockets = false;

    private final GetAvoWalletDetailsUseCase mWalletDetailsUseCase;
    private boolean mIsBusinessUser;

    @Inject
    ChatbotPresenter(final Analytics analytics, final NavigationRouter navigationRouter,
                     FeatureSetController featureSetController,
                     @Named("memory") final ApplicationStorage memoryApplicationStorage,
                     ChatbotStartSessionUseCase chatbotStartSessionUseCase,
                     ChatbotPostUserMessageUseCase chatbotPostUserMessageUseCase,
                     final ErrorHandler errorHandler, final LoginSecurityUseCase loginSecurityUseCase,
                     final ITADeviceManagementUseCase itaDeviceManagementUseCase, ChatbotHistoryUseCase chatbotHistoryUseCase,
                     final APIInformation apiInformation, ForgotPasswordWorkFlowUseCase forgotPasswordWorkFlowUseCase,
                     GetAccountDetailUseCase mGetAccountDetailUseCase, final GetUserDetailUseCase getUserDetailUseCase,
                     final ChatbotBranchCodeUseCase chatbotBranchCodeUseCase,
                     final ChatbotBranchCodeDataModelToBranchCodeViewModelMapper modelToBranchCodeViewModelMapper,
                     ChatbotGetOverviewUseCase mGetOverviewUseCase,
                     final ApplicationStorage applicationStorage, final CheckIfUserAdminUseCase checkIfUserAdminUseCase,
                     final ApplicantIdForeignCheckValidator applicantIdValidator,
                     final ChatbotGetCardsUseCase getCardsUseCase,
                     final ChatbotCardInfoToDataMapper cardInfoToDataMapper,
                     final ChatbotCardDataModelToViewModelMapper cardDataModelToViewModelMapper,
                     final GetMdmProfileUseCase getMdmProfileUseCase,
                     final GetFedarationListUseCase getFedarationListUseCase,
                     final SwitchContextDataViewModelMapper switchContextDataViewModelMapper,
                     final GetProfileUseCase getProfileUseCase,
                     final RecentPaymentUseCase recentPaymentUseCase,
                     GetAppointmentListUseCase getAppointmentListUseCase,
                     final RecentPaymentResponseDataToViewModelMapper recentPaymentResponseDataToViewModelMapper,
                     AppointmentResponseDataToViewModelMapper appointmentResponseDataToViewModelMapper,
                     final GetAvoWalletDetailsUseCase walletDetailsUseCase
    ) {
        this.analytics = analytics;
        this.navigationRouter = navigationRouter;
        this.featureSetController = featureSetController;
        this.mGetUserDetailUseCase = getUserDetailUseCase;
        this.memoryApplicationStorage = memoryApplicationStorage;
        this.chatbotStartSessionUseCase = chatbotStartSessionUseCase;
        this.chatbotPostUserMessageUseCase = chatbotPostUserMessageUseCase;
        this.errorHandler = errorHandler;
        this.loginSecurityUseCase = loginSecurityUseCase;
        this.mItaDeviceManagementUseCase = itaDeviceManagementUseCase;
        this.apiInformation = apiInformation;
        this.forgotPasswordWorkFlowUseCase = forgotPasswordWorkFlowUseCase;
        this.mGetAccountDetailUseCase = mGetAccountDetailUseCase;
        this.getUserDetailUseCase = getUserDetailUseCase;
        this.chatbotBranchCodeUseCase = chatbotBranchCodeUseCase;
        this.modelToBranchCodeViewModelMapper = modelToBranchCodeViewModelMapper;
        this.mGetOverviewUseCase = mGetOverviewUseCase;
        this.chatbotHistoryUseCase = chatbotHistoryUseCase;
        this.applicationStorage = applicationStorage;
        this.checkIfUserAdminUseCase = checkIfUserAdminUseCase;
        this.mApplicantIdForeignCheckValidator = applicantIdValidator;
        this.mGetCardsUseCase = getCardsUseCase;
        this.mCardInfoToDataMapper = cardInfoToDataMapper;
        this.mGetMdmProfileUseCase = getMdmProfileUseCase;
        this.mGetFedarationListUseCase = getFedarationListUseCase;
        this.mSwitchContextDataViewModelMapper = switchContextDataViewModelMapper;
        this.mGetProfileUseCase = getProfileUseCase;
        this.mChatbotRecentPaymentUseCase = recentPaymentUseCase;
        this.mGetAppointmentListUseCase = getAppointmentListUseCase;
        this.mChatbotRecentPaymentResponseDataToViewModelMapper = recentPaymentResponseDataToViewModelMapper;
        this.mAppointmentResponseDataToViewModelMapper = appointmentResponseDataToViewModelMapper;
        this.mWalletDetailsUseCase = walletDetailsUseCase;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void decideThecall() {

        setNormalSession();
        clearOnboardingSession();
        clearComplimentComplaintSession();

        String postloginToken = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID, StringUtils.EMPTY_STRING);

        if (isAuthenticated()) {
            startSessionRequest(true, !StringUtils.isNullOrEmpty(postloginToken));
        } else {
            if (StringUtils.isNullOrEmpty(postloginToken)) {
                startSessionRequest(false, false);
            } else {
                historyRequest(false);
            }
        }
    }

    // If current session is normal enbi and last session was onboarding
    public void clearOnboardingSession() {
        boolean isOnboardingSession = memoryApplicationStorage.getBoolean(ResponseStorageKey.CHATBOT_ONBOARDING_SESSION, false);
        if (isOnboardingSession && !conversationId.equals(VAL_QUIT_MODAL)) {
            memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
        }

    }

    // If enbi is called from all the channels except compliment_or_complaint
    public void setNormalSession() {
        if (!conversationId.equals(ResponseStorageKey.CHATBOT_COMPLIMENT_COMPLAINT_SESSION)) {
            memoryApplicationStorage.putBoolean(ResponseStorageKey.CHATBOT_NORMAL_ENBI_SESSION, true);
        }
    }

    // If current session is normal enbi and last session was compliment_or_complaint
    public void clearComplimentComplaintSession() {
        boolean isNormalSession = memoryApplicationStorage.getBoolean(ResponseStorageKey.CHATBOT_NORMAL_ENBI_SESSION, false);
        boolean isComplimentComplaintSession = memoryApplicationStorage.getBoolean(ResponseStorageKey.CHATBOT_COMPLIMENT_COMPLAINT_SESSION, false);
        if (isComplimentComplaintSession && isNormalSession) {
            memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
            memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_COMPLIMENT_COMPLAINT_SESSION);
        }
    }

    public void checkStartSessionAnalyticsTobeSentOrNot(String sessionID) {
        String postloginToken = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID, StringUtils.EMPTY_STRING);
        if (StringUtils.isNullOrEmpty(postloginToken)) {
            long startTime = System.currentTimeMillis() / 1000;
            memoryApplicationStorage.putLong(ChatbotConstants.PARAMS.PARAM_CHAT_BOT_TIME_SPENT, startTime);
            startSessionAnalytics(sessionID);
        }
    }

    private void startSessionAnalytics(String sessionId) {
        HashMap<String, Object> contextData = new HashMap<>();

        AdobeContextData adobeContextData = new AdobeContextData(contextData);

        long timestamp = System.currentTimeMillis() / 1000;
        String randomStr = UUID.randomUUID().toString();
        String sessionRefrence = timestamp + StringUtils.UNDERSCORE + randomStr;

        adobeContextData.setChatbotSessionReference(sessionRefrence);
        adobeContextData.setChatBotSessionId(sessionId);
        String dateTime = FormattingUtil.formatDate(FormattingUtil.convertMilliesToDate(timestamp * 1000), FormattingUtil.DATE_FORMAT_DAY_MONTH_DATE_YYYY_HH_MM_SS);
        adobeContextData.setChatBotDateTime(dateTime);
        adobeContextData.setChatBotInitialize();
        adobeContextData.setChatAgentTypeBot();
        analytics.sendEventActionWithMap(ChatTracking.AdobeEventName.EVENT_NAME_CB_CONNECTION_SUCCESSFUL, contextData);

    }


    public void startSessionRequest(boolean authenticated, boolean isPostLoginToken) {

        memoryApplicationStorage.putBoolean(ResponseStorageKey.CHATBOT_ONBOARDING_SESSION, conversationId.equals(VAL_QUIT_MODAL));

        ChatbotSessionMainRequestDataModel chatbotSessionMainRequestDataModel = createStartSessionRequest(authenticated);
        chatbotStartSessionUseCase.execute(authenticated, chatbotSessionMainRequestDataModel)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {

                })
                .doOnTerminate(() -> {

                })
                .subscribe(getFederatedUserDataReponseDataModel -> {
                    if (getFederatedUserDataReponseDataModel.getStatus().isOk()) {

                        //adobe analytics
                        checkStartSessionAnalyticsTobeSentOrNot(getFederatedUserDataReponseDataModel.getSessionId());
                        apiRetryCounter = 0;
                        if (authenticated && isPostLoginToken)
                            historyRequest(false);
                        else {
                            if (view != null)
                                view.setChatMessages(getFederatedUserDataReponseDataModel);
                        }
                        memoryApplicationStorage.putString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID,
                                getFederatedUserDataReponseDataModel.getSessionId());
                        memoryApplicationStorage.putBoolean(ResponseStorageKey.CHATBOT_CONTEXT_SWITCHED, false);

                    } else
                        handleError(getFederatedUserDataReponseDataModel.getStatus().getCode(), ApiAliasConstants.CB_CM_ET, false, "Start Session Api", null);

                }, throwable -> {
                    //errors already passed to view above
                    NBLogger.e(TAG, throwable.getMessage());
                    handleException(throwable, ApiAliasConstants.CB_CM_ET, "Start Session Api", null);
                });
    }

    public ChatbotSessionMainRequestDataModel createStartSessionRequest(boolean authenticated) {
        ChatbotSessionMainRequestDataModel chatbotSessionMainRequestDataModel;

        if (authenticated) {
            ChatbotAuthenticatedPayloadDataModel chatbotAuthenticatedPayloadDataModelFinal =
                    new ChatbotAuthenticatedPayloadDataModel();
            String token = DecodeTokenUtils.getLatestSessionId(APIInformation.getInstance().getToken()).replaceAll("(^\")|(\"$)", "");
            if (featureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_CHATBOT_SBS)) {
                chatbotAuthenticatedPayloadDataModelFinal.setToken(token);
            } else {
                ChatbotAuthenticatedPayloadSBSDataModel chatbotAuthenticatedPayloadDataModel =
                        new ChatbotAuthenticatedPayloadSBSDataModel();
                chatbotAuthenticatedPayloadDataModel.setToken(token);
                chatbotAuthenticatedPayloadDataModel.setContextswitched(memoryApplicationStorage.getBoolean(ResponseStorageKey.CHATBOT_CONTEXT_SWITCHED, false));

                chatbotAuthenticatedPayloadDataModelFinal.setToken(token);
                chatbotAuthenticatedPayloadDataModelFinal.setContextSwitched(memoryApplicationStorage.getBoolean(ResponseStorageKey.CHATBOT_CONTEXT_SWITCHED, false));

            }

            String sessionId = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID
                    , StringUtils.EMPTY_STRING);
            chatbotSessionMainRequestDataModel = CapiRequest.getRequestModel(authenticated, sessionId,
                    ChatbotConstants.PARAMS.PARAM_ENTER_TOKEN_VALUE, chatbotAuthenticatedPayloadDataModelFinal, featureSetController, conversationId);
        } else
            chatbotSessionMainRequestDataModel = CapiRequest.getRequestModel(authenticated, StringUtils.EMPTY_STRING, ChatbotConstants.PARAMS.PARAM_START_SESSION_VALUE,
                    new ChatbotAuthenticatedPayloadDataModel(), featureSetController, conversationId);
        return chatbotSessionMainRequestDataModel;
    }

    public void historyRequest(boolean chatToAgent) {
        if (view != null) {
            view.handleAgentTyping(true);
        }

        ChatbotSessionMainRequestDataModel model;
        String sessionId = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID
                , StringUtils.EMPTY_STRING);

        model = CapiRequest.getRequestModel(isAuthenticated(), sessionId,
                ChatbotConstants.PARAMS.PARAM_EVENT_HISTORY, new ChatbotAuthenticatedPayloadDataModel(), featureSetController, conversationId);

        chatbotHistoryUseCase.execute(model)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgressBar(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgressBar(false);
                    }
                })
                .subscribe(getFederatedUserDataReponseDataModel -> {
                    if (getFederatedUserDataReponseDataModel.getStatus().isOk()) {
                        memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_MESSAGE_FOR_AVAYA);
                        if (!chatToAgent) {
                            setAdapter(getFederatedUserDataReponseDataModel);
                            memoryApplicationStorage.putString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID,
                                    getFederatedUserDataReponseDataModel.getSessionId());

                        } else {
                            handleChattoAgentCAllfromHistory(getFederatedUserDataReponseDataModel);
                        }

                    } else if (getFederatedUserDataReponseDataModel.getStatus().isSessionOut()) {
                        memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                        decideThecall();

                    } else
                        handleError(getFederatedUserDataReponseDataModel.getStatus().getCode(), ApiAliasConstants.CB_CM_ET, false, "History Api", null);

                }, throwable -> {
                    NBLogger.e(TAG, throwable.getMessage());
                    handleException(throwable, ApiAliasConstants.CB_CM_ET, "History Api", null);
                });
    }

    public void handleChattoAgentCAllfromHistory(ChatbotHistoryResponseModel getFederatedUserDataReponseDataModel) {
        memoryApplicationStorage.putString(ResponseStorageKey.CHATBOT_MESSAGE_FOR_AVAYA,
                getFederatedUserDataReponseDataModel.getChatBotMessageForAvaya());
        logoutRequest();

        if (isAuthenticated())
            navigationRouter.navigateTo(
                    NavigationTarget.to(NavigationTarget.CHAT_SCREEN).withIntentSingleTop(true));
        else
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.INITIATE_CHAT_SCREEN));

    }

    public void setAdapter(ChatbotHistoryResponseModel getFederatedUserDataReponseDataModel) {
        if (getFederatedUserDataReponseDataModel.getEntries().isEmpty()) {
            memoryApplicationStorage.putString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID, StringUtils.EMPTY_STRING);
            decideThecall();
        } else {
            for (int i = 0; i < getFederatedUserDataReponseDataModel.getEntries().size(); i++) {
                ChatbotSessionMainResponseDataModel chatbotSessionMainResponseDataModel = new ChatbotSessionMainResponseDataModel();
                ArrayList<ChatbotMessageContentsResponseDataModel> chatbotMessageContentsResponseDataModels = new ArrayList<>();
                chatbotMessageContentsResponseDataModels.addAll(getFederatedUserDataReponseDataModel.getEntries()
                        .get(i).getMessageContents());
                chatbotSessionMainResponseDataModel.setMessageContents(chatbotMessageContentsResponseDataModels);
                view.setChatMessages(chatbotSessionMainResponseDataModel);
            }
        }
    }

    private void handleErrorAnalytics(String apiAlias, String errorCode) {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.setChatBotFail();
        analytics.trackFailure(true, ChatTracking.AdobeEventName.EVENT_NAME_CB_CONNECTION_FAILURE, apiAlias, R.string.something_went_wrong + "", errorCode, contextData);
    }

    public <T> void handleError(final String errorCode, String apiAlias, boolean isFromException, String apiName, T modelName) {

        if (view != null) {
            view.closeKeyboard();
        }
        if (!isFromException && !apiAlias.equalsIgnoreCase(ChatbotConstants.PARAMS.LOGOUT_API)
                && !errorCode.equalsIgnoreCase("0")) {
            handleErrorAnalytics(apiAlias, errorCode);
        }
        NBLogger.e(TAG, errorCode);

        if (view != null && !apiAlias.equalsIgnoreCase(ChatbotConstants.PARAMS.LOGOUT_API)) {
            view.showChatError(errorCode, apiRetryCounter, apiAlias, apiName, modelName);
        }
    }

    private void handleExceptionAnalytics(String apiAlias, Error error) {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.setChatBotFail();
        analytics.trackFailure(false, ChatTracking.AdobeEventName.EVENT_NAME_CB_CONNECTION_FAILURE, apiAlias, getErrorMessage(error.getCode()), error.getCode() + "", contextData);
    }


    public String getErrorMessage(int errorCode) {
        switch (errorCode) {
            case 500:
                return "Something got in our way.";
            case 0:
                return "You’re not connected.";
            case 201:
                return "Server error.";
            case 204:
                return "Operation succeeded. However, there are no contents to be processed.";
            case 400:
                return "Invalid request.";
            case 401:
                return "Operation succeeded. Access denied.";
            case 403:
                return "Invalid secret.";
            case 404:
                return "Page not found.";
            case 450:
                return "Operation succeeded. OTP is required.";
            case 451:
                return "Operation succeeded. Invalid OTP. Please retry.";
            case 452:
                return "Operation succeeded. Expired OTP";
            case 203:
                return "Operation succeeded. Too many OTP failures";
            default:
                return "Please try another question.";
        }

    }

    public <T> void handleException(final Throwable throwable, String apiAlias, String apiName, T modelName) {
        final Error error = errorHandler.getErrorMessage(throwable);
        if (error.getCode() == ChatbotErrorCodes.SESSION_OUT_CODE) {
            if (!apiAlias.equalsIgnoreCase(ChatbotConstants.PARAMS.LOGOUT_API)) {
                memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                decideThecall();
            } else
                memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
        } else {
            if (!apiAlias.equalsIgnoreCase(ChatbotConstants.PARAMS.LOGOUT_API)
                    && error.getCode() != 0) {

                handleExceptionAnalytics(apiAlias, error);
            }
            handleError(error.getCode() + "", apiAlias, true, apiName, modelName);

        }
    }


    public <T> void retryApi(String apiAlias, String apiName, T modelName) {
        apiRetryCounter = apiRetryCounter + 1;
        if (apiRetryCounter == 1) {
            if (apiAlias.equalsIgnoreCase(ApiAliasConstants.CB_CM_ET)) {
                decideThecall();
            } else if (apiAlias.equalsIgnoreCase(ApiAliasConstants.CB_CM_UM)) {
                if (modelName != null) {
                    if (modelName instanceof ChatbotQuickRepliesResponseDataModel) {
                        postQuickReply((ChatbotQuickRepliesResponseDataModel) modelName);
                    } else if (modelName instanceof ChatbotListContentPayloadButtonDataModel) {
                        postListReply((ChatbotListContentPayloadButtonDataModel) modelName);
                    } else if (modelName instanceof ChatbotMessageContentsResponseDataModel) {
                        handleButtonClickPostback((ChatbotMessageContentsResponseDataModel) modelName);
                    }
                } else {
                    callUserMessage(apiName);
                }
            }

        } else {
            apiRetryCounter = 0;
        }
    }

    public void getAccountIdandInitiateFlow(String accountNo, String action) {

        mGetOverviewUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(overviewCachableValue -> {
                    ChatbotOverview overviewValue = ChatbotOverview.newInstance(overviewCachableValue.get());
                    setAccountDetails(overviewValue, accountNo, action);


                }, throwable -> {

                });
    }


    public void setAccountDetails(ChatbotOverview overview, String accountNo, String action) {
        ChatbotAccountSummary accountSummary = new ChatbotAccountSummary();
        for (ChatbotAccountsOverview accountsOverview : overview.getAccountsOverviews()) {
            for (ChatbotAccountSummary summary : accountsOverview.getChatbotAccountSummaries()) {
                if (summary.getChatbotNumber() != null && summary.getChatbotNumber().equalsIgnoreCase(accountNo)) {
                    accountSummary = summary;
                    break;
                }
            }
        }
        startAccountNavigation(accountSummary, accountNo, action);
    }

    private void startAccountNavigation(ChatbotAccountSummary summary, String accountNo, String action) {
        String accountId = summary.getChatId();
        String accountHolderName = summary.getChatAccountHolderName();
        String accountType = summary.getChatAccountType().name();

        switch (action) {
            case ChatbotConstants.INTENT_IDENTIFIERS.INTENT_DEBIT_ORDER_LIST,
                 ChatbotConstants.INTENT_IDENTIFIERS.INTENT_TRANSACTION_HISTORY,
                 ChatbotConstants.INTENT_IDENTIFIERS.INTENT_DETAILED_BALANCES ->
                    getUserDetails(accountNo, accountId, action);

            case ChatbotConstants.INTENT_IDENTIFIERS.INTENT_ACCOUNT_DETAILS ->
                    getAccountDetail(accountNo, accountId, accountHolderName);

            case ChatbotConstants.INTENT_IDENTIFIERS.INTENT_E_STATEMENTS ->
                    eStatementsNavigation(accountNo, accountType);

            case ChatbotConstants.INTENT_IDENTIFIERS.INTENT_GREENBACKS -> {
                String rewardProgram = "";
                if (!StringUtils.isNullOrEmpty(summary.getChatbotRewardsProgram()))
                    rewardProgram = summary.getChatbotRewardsProgram();
                greenbackDashboardNavigation(accountNo, accountId, accountType, rewardProgram);
            }
            default -> NBLogger.e("default", "default case handled");
        }
    }

    private void getUserDetails(String accountNo, String accountId, String action) {
        mGetUserDetailUseCase.execute(false).compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    if (userDetail != null) {
                        debitOrderFragmentNavigation(accountNo, accountId, action, userDetail.getClientType());
                    }
                }, error -> NBLogger.e(TAG, error.getMessage()));
    }

    private void debitOrderFragmentNavigation(String accountNo, String accountId, String intentType, String clientType) {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.DEBIT_ORDER_FRAGMENT)

                .withParam(ChatbotConstants.PARAMS.PARAM_SEND_FROM_ACTIVITY, ChatbotConstants.PARAMS.PARAM_SENDING_FROM_CHATBOT_ACTIVITY)
                .withParam(ChatbotConstants.PARAMS.PARAM_ACCOUNT_ID, "" + accountId)
                .withParam(ChatbotConstants.PARAMS.PARAM_INTENT_TYPE, intentType)
                .withParam(ChatbotConstants.PARAMS.PARAM_ACCOUNT_NUMBER_ACCOUNT_STATEMENT, accountNo)
                .withParam(INVONLINE_CLIENT_TYPE, clientType));
    }

    private void eStatementsNavigation(String accountNo, String accountType) {
        if (accountType.equalsIgnoreCase("CREDIT_CARDS"))
            getPlasticIdandInitiateFlow("" + accountNo);
        else if (accountType.equalsIgnoreCase("INVESTMENTS"))
            navigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.DOWNLOAD_STATEMENTS)
                    .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_NUMBER, accountNo)
                    .withParam(ServicesNavigationTarget.DOWNLOAD_STATEMENT_TYPE, Constants.StatementType.INVESTMENT_BANK_STATEMENT)
                    .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_TYPE, accountType));

        else
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ACCOUNT_STATEMENTS_ACTIVITY)
                    .withParam(ChatbotConstants.PARAMS.PARAM_SEND_FROM_ACTIVITY, ChatbotConstants.PARAMS.PARAM_SENDING_FROM_CHATBOT_ACTIVITY)
                    .withParam(ChatbotConstants.PARAMS.PARAM_STATEMENT_TYPE, Constants.StatementType.BANK_STATEMENT)
                    .withParam(ChatbotConstants.PARAMS.PARAM_ACCOUNT_NUMBER_ACCOUNT_STATEMENT, "" + accountNo));

    }

    private void greenbackDashboardNavigation(String accountNo, String accountId, String accountType, String rewardsProgram) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.HOME)
                .withParam(ChatbotConstants.EXTRA.NAVIGATION_TARGET, "");
        GBUtils.setGreenbackV2Data(accountNo, featureSetController, isBusinessUser());
        if (GBUtils.isGreenBackV2Allowed() && rewardsProgram.equalsIgnoreCase(FormattingUtil.GREENBACKS_CURRENCY)) {
            boolean isJuristic = isBusinessUser() && !featureSetController.isFeatureDisabled(FeatureConstants.FTR_GREENBACKS_JURISTIC);
            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.GREENBACKS_APP_DASHBOARD)
                    .withParam(GBConstants.BUNDLE_IS_JURISTIC, isJuristic);
        }
        navigationTarget.withIntentFlagClearTopSingleTop(true)
                .withParam(ChatbotConstants.PARAMS.PARAM_SEND_FROM_ACTIVITY, ChatbotConstants.PARAMS.PARAM_SENDING_FROM_CHATBOT_ACTIVITY)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountId)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountNo)
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_ACCOUNT_TYPE, accountType);
        navigationRouter.navigateTo(navigationTarget);
    }


    public void getBranchCodeList(String accountType, String accountNumber, String accountHolderName, String accountId, String cisNumber) {
        chatbotBranchCodeUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(branchCodeDataModelList ->
                        getBranchCodeValue(modelToBranchCodeViewModelMapper.mapBranchCodeViewModel(branchCodeDataModelList), accountType, accountNumber, accountHolderName, accountId, cisNumber
                        ), throwable -> {

                });
    }

    public void getBranchCodeValue(List<ChatbotBranchCodeViewModel> branchCodeViewModelList, String accountType, String accountNumber, String accountHolderName, String accountId, String cisNumber) {
        String branchCode = "";
        if (branchCodeViewModelList != null && !branchCodeViewModelList.isEmpty()) {
            for (int i = 0; i < branchCodeViewModelList.size(); i++) {
                if (accountType.contains(branchCodeViewModelList.get(i).getAccountType())) {
                    branchCode = branchCodeViewModelList.get(i).getBranchCode();
                    break;
                }
            }
        }
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHARE_ACCOUNT_INFO_ACTIVITY)

                .withParam(ChatbotConstants.PARAMS.PARAM_SEND_FROM_ACTIVITY, ChatbotConstants.PARAMS.PARAM_SENDING_FROM_CHATBOT_ACTIVITY)
                .withParam(ChatbotConstants.PARAMS.PARAM_ACCOUNT_ID, "" + accountId)
                .withParam(ChatbotConstants.PARAMS.PARAM_ACCOUNT_TYPE_SHARE_ACCOUNT_INFO, accountTypeFullName(accountType))
                .withParam(ChatbotConstants.PARAMS.PARAM_ACCOUNT_NAME_SHARE_ACCOUNT_INFO, accountHolderName)
                .withParam(ChatbotConstants.PARAMS.PARAM_BRANCH_CODE_SHARE_ACCOUNT_INFO, branchCode)
                .withParam(ChatbotConstants.PARAMS.PARAM_CIS_NUMBER_SHARE_ACCOUNT_INFO, cisNumber)
                .withParam(ChatbotConstants.PARAMS.PARAM_ACCOUNT_NUMBER_SHARE_ACCOUNT_INFO, accountNumber));

    }


    void getAccountDetail(String accountNumber, String accountId, String accountHolderName) {


        mGetAccountDetailUseCase.execute(accountId)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {

                })
                .subscribe(accountDetailData -> showAccountDetails(accountDetailData, accountNumber, accountHolderName, accountId), throwable -> NBLogger.e("Details", throwable.getMessage()));
    }

    private void showAccountDetails(final AccountDetailData accountDetailData, String accountNumber, String accountHolderName, String accountId) {

        Log.e("", "" + accountDetailData.getAccountType());
        if (view != null) {
            getUserDetailUseCase.execute(false).compose(bindToLifecycle())
                    .subscribe(userDetailData -> getAccountHolderName(userDetailData, accountNumber, "" + accountDetailData.getAccountType(), accountHolderName, accountId)
                            , throwable -> NBLogger.e("Details", throwable.getMessage()));
        }
    }

    private void getAccountHolderName(UserDetailData userDetailData, String accountNumber, String accountType, String accountHolderName, String accountId) {
        NBLogger.e("", "" + String.format("%s %s", userDetailData.getFirstName(), userDetailData.getSurname()));

        getBranchCodeList(accountType, accountNumber, accountHolderName, accountId, userDetailData.getCisNumber());
    }

    public String accountTypeFullName(String accountShortName) {

        final String investmentAccount = "Investment Account";
        return switch (accountShortName) {
            case "CA" -> "Current Account";
            case "SA" -> "Savings Account";
            case "CC" -> "Credit Card";
            case "DS", "TD", "INV" -> investmentAccount;
            case "HL" -> "Home Loan Account";
            case "PL" -> "Personal Loan Account";
            case "BDF" -> "Beneficiary directory file";
            case "U0" -> "Unknown";
            case "UD" -> "Please select";
            case "IS" -> "MFC Account";
            case "IR" -> "Greenbacks";
            case "LR" -> "Greenbacks_clm";
            default -> "";
        };

    }

    // nedbank id
    public void sendMessage(String message) {
        if (!TextUtils.isEmpty(message)) {
            ChatbotSessionMainResponseDataModel chatMessage = new ChatbotSessionMainResponseDataModel();
            List<ChatbotMessageContentsResponseDataModel> messageContents = new ArrayList<>();
            ChatbotMessageContentsResponseDataModel chatbotMessageContentsResponseDataModel
                    = new ChatbotMessageContentsResponseDataModel();
            chatbotMessageContentsResponseDataModel.setType("TEXT");
            ChatbotPayloadResponseDataModel payload = new ChatbotPayloadResponseDataModel();
            payload.setText(message);
            chatbotMessageContentsResponseDataModel.setPayload(payload);
            messageContents.add(chatbotMessageContentsResponseDataModel);
            chatMessage.setMessageContents(messageContents);

            if (view != null) {
                view.setChatMessages(chatMessage);
            }
            callUserMessage(message);
        }
    }

    // after
    public void callUserMessage(String message) {
        if (view != null && apiRetryCounter != 1) {
            view.handleAgentTyping(true);
        }
        ChatbotAuthenticatedPayloadDataModel chatbotPayloadRequestDataModel = new ChatbotAuthenticatedPayloadDataModel();

        chatbotPayloadRequestDataModel.setText(message);
        String sessionId = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID
                , StringUtils.EMPTY_STRING);

        ChatbotSessionMainRequestDataModel model;

        model = CapiRequest.getRequestModel(isAuthenticated(), sessionId,
                ChatbotConstants.PARAMS.PARAM_EVENT_TEXT, chatbotPayloadRequestDataModel, featureSetController, conversationId);
        chatbotPostUserMessageUseCase.execute(!apiInformation.isLoggedOut(), model)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {

                })
                .doOnTerminate(() -> {

                })
                .subscribe(getFederatedUserDataReponseDataModel -> {

                    if (getFederatedUserDataReponseDataModel.getStatus().isOk()) {
                        apiRetryCounter = 0;
                        if (view != null) {
                            view.setChatMessages(getFederatedUserDataReponseDataModel);
                            memoryApplicationStorage.putString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID,
                                    getFederatedUserDataReponseDataModel.getSessionId());

                        }
                    } else if (getFederatedUserDataReponseDataModel.getStatus().isSessionOut()) {
                        memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                        decideThecall();

                    } else
                        handleError(getFederatedUserDataReponseDataModel.getStatus().getCode(), ApiAliasConstants.CB_CM_UM, false, message, null);
                }, throwable -> {
                    //errors already passed to view above
                    NBLogger.e(TAG, throwable.getMessage());
                    handleException(throwable, ApiAliasConstants.CB_CM_UM, message, null);
                });
    }

    public void postEngagedUserAnalytics() {
        boolean engagedUserAlreadySent = memoryApplicationStorage.getBoolean(ResponseStorageKey.CHATBOT_ENGAGED_USER_SENT, false);
        if (!engagedUserAlreadySent) {
            HashMap<String, Object> contextData = new HashMap<>();
            AdobeContextData adobeContextData = new AdobeContextData(contextData);
            String sessionId = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID
                    , StringUtils.EMPTY_STRING);
            adobeContextData.setChatAgentTypeBot();
            adobeContextData.setChatBotSessionId(sessionId);
            analytics.sendEventActionWithMap(ChatTracking.AdobeEventName.EVENT_NAME_CB_ENGAGEDUSER, contextData);
            memoryApplicationStorage.putBoolean(ResponseStorageKey.CHATBOT_ENGAGED_USER_SENT
                    , true);
        }
    }

    public void postQuickReply(ChatbotQuickRepliesResponseDataModel quickreply) {

        ChatbotSessionMainResponseDataModel chatMessageQuickReply = new ChatbotSessionMainResponseDataModel();
        List<ChatbotMessageContentsResponseDataModel> chatMessageContents = new ArrayList<>();
        ChatbotMessageContentsResponseDataModel chatbotMessageContentsResponseDataModel
                = new ChatbotMessageContentsResponseDataModel();
        chatbotMessageContentsResponseDataModel.setType("TEXT");
        ChatbotPayloadResponseDataModel payload = new ChatbotPayloadResponseDataModel();
        payload.setText(quickreply.getLabel());
        chatbotMessageContentsResponseDataModel.setPayload(payload);
        chatMessageContents.add(chatbotMessageContentsResponseDataModel);
        chatMessageQuickReply.setMessageContents(chatMessageContents);


        if (view != null && apiRetryCounter != 1) {
            view.setChatMessages(chatMessageQuickReply);
            view.handleAgentTyping(true);

        }

        ChatbotAuthenticatedPayloadDataModel chatbotPayloadRequestDataModel = new ChatbotAuthenticatedPayloadDataModel();
        chatbotPayloadRequestDataModel.setState(quickreply.getPayload());
        String sessionId = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID
                , StringUtils.EMPTY_STRING);

        ChatbotSessionMainRequestDataModel chatbotSessionMainRequestDataModelQuickReply = CapiRequest.getRequestModel(isAuthenticated(), sessionId,
                quickreply.getType(), chatbotPayloadRequestDataModel, featureSetController, conversationId);

        chatbotPostUserMessageUseCase.execute(!apiInformation.isLoggedOut(), chatbotSessionMainRequestDataModelQuickReply)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {

                })
                .doOnTerminate(() -> {

                })
                .subscribe(getFederatedUserDataReponsePostQuickReplyDataModel -> {
                    if (getFederatedUserDataReponsePostQuickReplyDataModel.getStatus().isOk()) {
                        if (view != null) {
                            view.setChatMessages(getFederatedUserDataReponsePostQuickReplyDataModel);
                            memoryApplicationStorage.putString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID,
                                    getFederatedUserDataReponsePostQuickReplyDataModel.getSessionId());

                        }
                    } else if (getFederatedUserDataReponsePostQuickReplyDataModel.getStatus().isSessionOut()) {
                        memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                        decideThecall();

                    } else
                        handleError(getFederatedUserDataReponsePostQuickReplyDataModel.getStatus().getCode(), ApiAliasConstants.CB_CM_UM, false, quickreply.getLabel(), quickreply);

                }, throwable -> {
                    //errors already passed to view above
                    NBLogger.e(TAG, throwable.getMessage());
                    handleException(throwable, ApiAliasConstants.CB_CM_UM, quickreply.getLabel(), quickreply);
                });
    }

    public void handlePostUserMessageResponseforList(
            ChatbotSessionMainResponseDataModel getFederatedUserDataReponsePostListDataModel,
            String label) {
        String type = "";
        ChatbotMessageContentsResponseDataModel messageContent = null;
        String payload = "";
        if (!getFederatedUserDataReponsePostListDataModel.getMessageContents().isEmpty()) {
            messageContent = getFederatedUserDataReponsePostListDataModel.getMessageContents().get(0);
            payload = getFederatedUserDataReponsePostListDataModel.getMessageContents().get(0).getPayload().getPayload();
            type = getFederatedUserDataReponsePostListDataModel.getMessageContents().get(0).getPayload().getType();
        }
        if (messageContent != null && messageContent.getType().equals("REDIRECT")) {
            handleDeeplinkHyperlinkForList(payload, type);
        } else {
            setTextOnChatWindow(label);
            if (view != null)
                view.setChatMessages(getFederatedUserDataReponsePostListDataModel);
        }
    }


    /**
     * List item postback handling
     *
     * @param quickreply
     */
    public void postListReply(ChatbotListContentPayloadButtonDataModel quickreply) {
        ChatbotSessionMainRequestDataModel chatbotSessionMainRequestDataModelPostListReply;
        ChatbotAuthenticatedPayloadDataModel chatbotPayloadRequestDataModelPostListReply = new ChatbotAuthenticatedPayloadDataModel();
        chatbotPayloadRequestDataModelPostListReply.setState(quickreply.getPayload());
        String sessionId = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID
                , StringUtils.EMPTY_STRING);
        chatbotSessionMainRequestDataModelPostListReply = CapiRequest.getRequestModel(isAuthenticated(), sessionId,
                quickreply.getType(), chatbotPayloadRequestDataModelPostListReply, featureSetController, conversationId);

        chatbotPostUserMessageUseCase.execute(!apiInformation.isLoggedOut(), chatbotSessionMainRequestDataModelPostListReply)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgressBar(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgressBar(false);
                    }
                })
                .subscribe(getFederatedUserDataReponsePostListDataModel -> {
                    if (getFederatedUserDataReponsePostListDataModel.getStatus().isOk()) {
                        memoryApplicationStorage.putString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID,
                                getFederatedUserDataReponsePostListDataModel.getSessionId());
                        handlePostUserMessageResponseforList(getFederatedUserDataReponsePostListDataModel, quickreply.getLabel());

                    } else if (getFederatedUserDataReponsePostListDataModel.getStatus().isSessionOut()) {
                        memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                        decideThecall();

                    } else
                        handleError(getFederatedUserDataReponsePostListDataModel.getStatus().getCode(), ApiAliasConstants.CB_CM_UM, false, quickreply.getLabel(), quickreply);
                }, throwable -> {
                    NBLogger.e(TAG, throwable.getMessage());
                    handleException(throwable, ApiAliasConstants.CB_CM_UM, quickreply.getLabel(), quickreply);
                });
    }


    private void handleCloseChatMenuClickedAnalytics() {
        long startTime = memoryApplicationStorage.getLong(ChatbotConstants.PARAMS.PARAM_CHAT_BOT_TIME_SPENT, 0);
        long endTime = System.currentTimeMillis() / 1000;
        long timeSpent = endTime - startTime;

        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.setCloseActions();
        adobeContextData.setTimeSpentPerFeature(timeSpent + "");
        analytics.sendEventActionWithMap(ChatTracking.AdobeEventName.EVENT_NAME_CLIENT_TERMINATED_CHAT, contextData);
    }

    public void handleCloseChatMenuClicked() {
        handleCloseChatMenuClickedAnalytics();
        logoutRequest();
    }

    public void logoutRequest() {

        ChatbotAuthenticatedPayloadDataModel chatbotPayloadRequestDataModel = new ChatbotAuthenticatedPayloadDataModel();
        String sessionId = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID
                , StringUtils.EMPTY_STRING);

        if (!StringUtils.isNullOrEmpty(sessionId)) {

            ChatbotSessionMainRequestDataModel chatbotSessionMainRequestDataModel = CapiRequest.getRequestModel(isAuthenticated(), sessionId,
                    ChatbotConstants.PARAMS.PARAM_LOGOUT_VALUE, chatbotPayloadRequestDataModel, featureSetController, conversationId);

            chatbotStartSessionUseCase.execute(!apiInformation.isLoggedOut(), chatbotSessionMainRequestDataModel)
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {

                    })
                    .doOnTerminate(() -> {

                    })
                    .subscribe(getFederatedUserDataReponseDataModel -> {
                                if (getFederatedUserDataReponseDataModel.getStatus().isOk()) {
                                    memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                                    memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_ENGAGED_USER_SENT);

                                    if (view != null) {
                                        view.close();
                                    }
                                } else if (getFederatedUserDataReponseDataModel.getStatus().isSessionOut()) {
                                    memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                                    memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_ENGAGED_USER_SENT);

                                } else {
                                    handleError(getFederatedUserDataReponseDataModel.getStatus().getCode(), ChatbotConstants.PARAMS.LOGOUT_API, false, ChatbotConstants.PARAMS.LOGOUT_API, null);
                                }
                            }, throwable ->
                                    handleException(throwable, ChatbotConstants.PARAMS.LOGOUT_API, ChatbotConstants.PARAMS.LOGOUT_API, null)

                    );
        }
    }

    private void handleInvestmentAccountFlow() {
        mGetUserDetailUseCase.execute(false).compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgressBar(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgressBar(false);
                    }
                })
                .subscribe(userDetail -> {
                    if (userDetail != null) {
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NGI_GET_STARTED)
                                .withParam(PARAM_ONIA_CLIENT_TYPE, userDetail.getClientType())
                                .withParam(PARAM_ONIA_BIRTH_DATE, userDetail.getBirthDate())
                                .withParam(PARAM_ONIA_SEC_OFFICER_CD, userDetail.getSecOfficerCd())
                                .withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, userDetail.getCisNumber()));
                    }
                }, error -> NBLogger.e(TAG, error.getMessage()));
    }

    private void handleOpenAvoFlow() {
        mGetUserDetailUseCase.execute(false).compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgressBar(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgressBar(false);
                    }
                })
                .subscribe(userDetail -> {
                            if (userDetail != null && view != null) {
                                mIsBusinessUser = userDetail.getClientType() != null && Integer.parseInt(userDetail.getClientType())
                                        > za.co.nedbank.services.Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;
                                getAvoWalletDetails();
                            }
                        }, error ->
                                NBLogger.e(TAG, error.getMessage())
                );
    }

    private void getAvoWalletDetails() {
        mWalletDetailsUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.showProgressBar(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.showProgressBar(false);
                })
                .subscribe(
                        result -> openAvoAppInBrowser(result, mIsBusinessUser),
                        throwable -> NBLogger.e(TAG, throwable.getMessage())
                );
    }

    private void openAvoAppInBrowser(AvoWalletDetailsModel result, boolean isBusinessUser) {
        Map<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setContext10(result.getAvoId());
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_AVO_SUCCESS, cdata);
        AvoToggleUtils.AvoToggleRedirection avoToggleRedirection = AvoToggleUtils.getAvoAppRederictionURL(featureSetController, result, isBusinessUser);
        if (view != null) {
            if (avoToggleRedirection.shouldOpenInWebView()) {
                openAvoInWebView(avoToggleRedirection.getAvoAppRedirectionUrl());
            } else {
                view.startBrowser(avoToggleRedirection.getAvoAppRedirectionUrl());
            }
        }
    }

    private void openAvoInWebView(String redirectUrl) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_REDIRECT_URL);
        navigationTarget = navigationTarget.withParam(NotificationConstants.EXTRA.URL, redirectUrl)
                .withParam(NotificationConstants.EXTRA.IS_AVO_APP_URL, true);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void onListButtonDeepLinkClickedAnalytics(String label) {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.setChatIntent(label);
        adobeContextData.setChatAgentTypeBot();
        analytics.sendEventActionWithMap(ChatTracking.AdobeEventName.EVENT_NAME_CB_SELECT_LISTCOMP, contextData);

    }

    /**
     * List item click implementation
     *
     * @param debitOrderItem
     */
    public void onListButtonDeepLinkClicked(ChatbotListContentPayloadButtonDataModel debitOrderItem) {

        handleDeeplinkHyperlinkForList(debitOrderItem.getPayload(), debitOrderItem.getType());
        if (debitOrderItem.getType().equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_TYPE_POSTBACK)) {
            postListReply(debitOrderItem);
        }

    }

    public void handleDeeplinkHyperlinkForList(String payload, String type) {
        if (type.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_TYPE_DEEPLINK)) {

            if (payload.contains("#")) {
                handleAccountIntents(payload);
                onListButtonDeepLinkClickedAnalytics(ChatbotConstants.ChatIntent.VAL_CHAT_LIST_ITEM_CLICK
                        + ChatbotConstants.PARAMS.PARAM_UNDERSCORE + payload.substring(0, payload.indexOf("#")));
            } else {
                handleButtonClickDeepLink(payload);
                onListButtonDeepLinkClickedAnalytics(ChatbotConstants.ChatIntent.VAL_CHAT_LIST_ITEM_CLICK
                        + ChatbotConstants.PARAMS.PARAM_UNDERSCORE + payload);

            }
        } else if (type.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_HYPERLINK)) {
            callHyperLink(payload.toLowerCase());
        }
    }

    public void callHyperLink(String url) {
        buttonClickAnalytics(ChatbotConstants.ChatIntent.VAL_CHAT_INTENT_HYPERLINK +
                ChatbotConstants.PARAMS.PARAM_UNDERSCORE + url);
        view.handleHyperlink(url);
    }

    public void handleAccountIntents(String payload) {
        if (payload.substring(0, payload.indexOf("#")).equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_DEBIT_ORDER_LIST)) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                getAccountIdandInitiateFlow("" + payload.substring(payload.indexOf("#") + 1), ChatbotConstants.INTENT_IDENTIFIERS.INTENT_DEBIT_ORDER_LIST);
            }
        } else if (payload.substring(0, payload.indexOf("#")).equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_ACCOUNT_DETAILS)) {

            getAccountIdandInitiateFlow("" + payload.substring(payload.indexOf("#") + 1), ChatbotConstants.INTENT_IDENTIFIERS.INTENT_ACCOUNT_DETAILS);

        } else if (payload.substring(0, payload.indexOf("#")).equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_TRANSACTION_HISTORY)) {

            getAccountIdandInitiateFlow("" + payload.substring(payload.indexOf("#") + 1), ChatbotConstants.INTENT_IDENTIFIERS.INTENT_TRANSACTION_HISTORY);

        } else if (payload.substring(0, payload.indexOf("#")).equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_DETAILED_BALANCES)) {

            getAccountIdandInitiateFlow("" + payload.substring(payload.indexOf("#") + 1), ChatbotConstants.INTENT_IDENTIFIERS.INTENT_DETAILED_BALANCES);

        } else if (payload.substring(0, payload.indexOf("#")).equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_E_STATEMENTS)) {
            getAccountIdandInitiateFlow("" + payload.substring(payload.indexOf("#") + 1), ChatbotConstants.INTENT_IDENTIFIERS.INTENT_E_STATEMENTS);

        } else if (payload.substring(0, payload.indexOf("#")).equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_GREENBACKS)) {
            getAccountIdandInitiateFlow("" + payload.substring(payload.indexOf("#") + 1), ChatbotConstants.INTENT_IDENTIFIERS.INTENT_GREENBACKS);
        }
    }

    public void handleEndChatClick() {

        if (view != null) {
            view.showExitDialog();
        }
    }


    public void buttonClickAnalytics(String label) {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        if (label.substring(12).equalsIgnoreCase("CHAT_TO_AGENT")) {
            handleChatbotButtonLiveagentAnalytics();
            adobeContextData.setChatIntent(ChatbotConstants.ChatIntent.VAL_CHAT_INTENT_DEEPLINK + ChatbotConstants.PARAMS.PARAM_UNDERSCORE + ChatbotConstants.ChatIntent.VAL_CHAT_TO_AN_AGENT);
        } else {
            adobeContextData.setChatIntent(label);
        }

        adobeContextData.setChatAgentTypeBot();
        analytics.sendEventActionWithMap(ChatTracking.AdobeEventName.EVENT_NAME_CB_SELECT_INTENT, contextData);
    }

    void handleChatbotButtonLiveagentAnalytics() {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.setChatAgentTypeLiveAgent();
        adobeContextData.setEnbiLiveAgentHandOff();
        analytics.sendEventActionWithMap(ChatTracking.AdobeEventName.EVENT_NAME_START_CHAT, contextData);

    }

    void handleChatbotVoiceToTextAnalytics() {
        analytics.sendEvent(ChatTracking.AdobeEventName.EVENT_NAME_VOICE_TO_TEXT,
                StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    public void handleButtonClick(ChatbotMessageContentsResponseDataModel item) {
        if (item.getPayload().getType().equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_HYPERLINK)) {
            callHyperLink(item.getPayload().getPayload());
        } else if (item.getPayload().getType().equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_TYPE_POSTBACK)) {

            handleButtonClickPostback(item);
        } else {
            if (item.getPayload().getPayload().equalsIgnoreCase("CHAT_TO_AGENT")) {
                handleChatToAnAgentNotPostback();

            } else
                handleButtonClickDeepLink(item.getPayload().getPayload());

            buttonClickAnalytics(ChatbotConstants.ChatIntent.VAL_CHAT_INTENT_DEEPLINK + ChatbotConstants.PARAMS.PARAM_UNDERSCORE + item.getPayload().getPayload());
        }
    }

    private void handleChatToAnAgentNotPostback() {
        view.checkLiveAgentIsUp();
    }

    public void callHistory() {
        historyRequest(true);
    }

    public void handlePostUserMessageResponseForButton(ChatbotSessionMainResponseDataModel getFederatedUserDataReponseDataModel, String label) {
        String type = "";
        ChatbotMessageContentsResponseDataModel messageContent = null;
        if (!getFederatedUserDataReponseDataModel.getMessageContents().isEmpty()) {
            messageContent = getFederatedUserDataReponseDataModel.getMessageContents().get(0);
            type = messageContent.getType();
        }
        if (type.equals("REDIRECT")) {
            handleButtonClick(messageContent);
        } else {
            setTextOnChatWindow(label);
            if (view != null)
                view.setChatMessages(getFederatedUserDataReponseDataModel);
        }
    }

    public void handleButtonClickPostback(ChatbotMessageContentsResponseDataModel item) {


        ChatbotSessionMainRequestDataModel chatbotSessionMainRequestDataModelClickPostback;

        ChatbotAuthenticatedPayloadDataModel chatbotPayloadRequestDataModelClickPostback = new ChatbotAuthenticatedPayloadDataModel();

        chatbotPayloadRequestDataModelClickPostback.setState(item.getPayload().getPayload());

        String sessionId = memoryApplicationStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID
                , StringUtils.EMPTY_STRING);

        chatbotSessionMainRequestDataModelClickPostback = CapiRequest.getRequestModel(isAuthenticated(), sessionId,
                item.getPayload().getType(), chatbotPayloadRequestDataModelClickPostback, featureSetController, conversationId);

        chatbotPostUserMessageUseCase.execute(!apiInformation.isLoggedOut(), chatbotSessionMainRequestDataModelClickPostback)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.showProgressBar(true);
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgressBar(false);
                    }
                })
                .subscribe(getFederatedUserDataReponseDataModel -> {
                    if (getFederatedUserDataReponseDataModel.getStatus().isOk()) {
                        memoryApplicationStorage.putString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID,
                                getFederatedUserDataReponseDataModel.getSessionId());

                        handlePostUserMessageResponseForButton(getFederatedUserDataReponseDataModel, item.getPayload().getLabel());

                    } else if (getFederatedUserDataReponseDataModel.getStatus().isSessionOut()) {
                        memoryApplicationStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                        decideThecall();

                    } else
                        handleError(getFederatedUserDataReponseDataModel.getStatus().getCode(), ApiAliasConstants.CB_CM_UM, false, item.getPayload().getLabel(), item);

                }, throwable -> {
                    //errors already passed to view above
                    NBLogger.e(TAG, throwable.getMessage());
                    handleException(throwable, ApiAliasConstants.CB_CM_UM, item.getPayload().getLabel(), item);
                });
    }

    public void setTextOnChatWindow(String label) {
        ChatbotSessionMainResponseDataModel chatMessage = new ChatbotSessionMainResponseDataModel();
        List<ChatbotMessageContentsResponseDataModel> messageContents = new ArrayList<>();
        ChatbotMessageContentsResponseDataModel chatbotMessageContentsResponseDataModel
                = new ChatbotMessageContentsResponseDataModel();
        chatbotMessageContentsResponseDataModel.setType("TEXT");
        ChatbotPayloadResponseDataModel payloadMessage = new ChatbotPayloadResponseDataModel();
        payloadMessage.setText(label);
        chatbotMessageContentsResponseDataModel.setPayload(payloadMessage);
        messageContents.add(chatbotMessageContentsResponseDataModel);
        chatMessage.setMessageContents(messageContents);

        if (view != null && apiRetryCounter != 1) {
            view.setChatMessages(chatMessage);
            view.handleAgentTyping(true);
        }
    }

    private void handleButtonClickDeepLink(String payload) {
        if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_BRANCH_LOCATOR)) {
            showAtmAndBranchLocations();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_LOGIN_AND_SECURITY)) {
            loginAndSecurityClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_PROFILE_LIMITS)) {
            profileLimitsClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_MORE)) {
            moreClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_CARDS)) {
            mycardsClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_SETTINGS)) {
            settingsClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_BUY)) {
            buyClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_INSURANCE)) {
            insuranceClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_INSURANCE_CLAIM)) {
            insuranceClaimClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_PAYMENTS_RECENT)) {
            handleRecentPaymentsFlow();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_SAVED_RECIPIENT)) {
            savedRecipientClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_INTERNATIONAL_PAYMENTS)) {
            internationPaymentsClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_MY_POCKET)) {
            myPocketClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_TRANSFER_DASHBOARD)) {
            transferDashboardClicked();
        } else
            handleButtonClickdeeplinkMore(payload);

    }

    private void handleButtonClickdeeplinkMore(String payload) {
        if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_PROFILE_DEATILS)) {
            handleProfileDetailsFlow();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_DEVICE_MANAGEMENT)) {
            handleITASettingsFlow();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_QUICK_PAY)) {
            handleQuickPayClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_INVESTMENT_DASHBOARD)) {
            investmentClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_SEND_MONEY)) {
            navigateToCashSendMoney();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_BOOK_BANKER)) {
            navigateToBookBanker();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_TAX_DOCUMENTS)) {
            taxCertificatesClicked();
        } else {
            handleButtonClickDeepLinkComplexity(payload);
        }
    }

    private void navigateToBookBanker() {
        if (isAuthenticated())
            fetchBankerDetail();
    }

    public void handleQuickPayClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.QUICK_PAY));
            }
        }
    }

    private void handleButtonClickDeepLinkComplexity(String payload) {
        if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_APPLY)) {
            applyClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_FORGOT_PASSWORD)) {
            if (!isAuthenticated() && !apiInformation.getShowEnrolment())
                forgotPasswordWorkFlowUseCase.execute()
                        .compose(bindToLifecycle())
                        .subscribe(o -> {
                        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
            else
                loginAndSecurityClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_LOGIN)) {
            if (apiInformation.getShowEnrolment()) {
                chatbotCameFromLogin = true;
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ENROLL_V2_LANDING));

            } else {
                chatbotCameFromLogin = true;
                navigationRouter.navigateTo(
                        NavigationTarget.to(NavigationTarget.LOGIN).withParam(NavigationTarget.KEY_FROM_CONVOCHATBOT, true)
                                .withClearStack(true));
            }
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_PAY)) {
            payClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_GREENBACKS)) {
            greebBacksClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_CREDIT_SCORE)) {
            creditScoreClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_INVESTMENT_APPLY)) {
            handleInvestmentAccountFlow();
        } else {
            handleButtonClickDeepLinkComplexityMore(payload);
        }
    }

    private void handleButtonClickDeepLinkComplexityMore(String payload) {
        if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_OPEN_AVO)) {
            handleOpenAvoFlow();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_ADD_RECIPIENT)) {
            addRecipientClicked();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_NOTIFICATION_PREFERENCE)) {
            handleNotificationPreference();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_APPLICATIONS)) {
            handleResumeApplications();
        } else if (payload.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_INTERNATIONAL_BANKING)) {
            internationalBankingClicked();
        } else {
            handleButtonClickNavigationHandler(payload);
        }
    }

    public void handleButtonClickNavigationHandler(String payload) {
        switch (payload) {
            case INTENT_PAYSHAPP, PAY_MY_BILLS_SCHEDULE, INTENT_DISCS_FINES,
                 INTENT_POS_APPLICATIONS -> navigateToNavigationHandler(payload);
            case INTENT_HOME_LOAN -> navigateToNavigationHandlerForAllUsers(payload);
            default -> handleUpdateApp();
        }
    }

    public void navigateToNavigationHandler(String payload) {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.TARGET_GLOBAL_NAVIGATION_HANDLER)
                        .withParam(NotificationConstants.Navigation.TARGET, payload)
                        .withParam(NotificationConstants.Navigation.FROM, ENBI_CHAT);
                navigationRouter.navigateTo(targetScreen);
            }
        }
    }

    public void navigateToNavigationHandlerForAllUsers(String payload) {
        if (isAuthenticated()) {
            NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.TARGET_GLOBAL_NAVIGATION_HANDLER)
                    .withParam(NotificationConstants.Navigation.TARGET, payload)
                    .withParam(NotificationConstants.Navigation.FROM, ENBI_CHAT);
            navigationRouter.navigateTo(targetScreen);

        }
    }

    public void handleResumeApplications() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME)
                        .withParam(ChatbotConstants.EXTRA.NAVIGATION_TARGET,
                                ChatbotConstants.NAVIGATION_TARGET.APPLICATIONS).withIntentFlagClearTopSingleTop(true));
            } else {
                memoryApplicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FICA_SELECT_INTENT)
                        .withParam(za.co.nedbank.enroll_v2.Constants.NFPCreditCardParam.IS_PRE_LOGIN, true)
                        .withParam(Constants.BUNDLE_KEYS.FLOW_JOURNEY_FLAG, Constants.FLOW_CONSTANTS
                                .PRE_LOGIN_APPLY_BORROW_FLOW)
                        .withParam(FLOW_FOR_APPLICATION, true));

            }
        }
    }

    private void handleNotificationPreference() {
        if (isNonTpUser()) {
            handleNonTpError();
        } else {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES));
        }
    }

    private void handleUpdateApp() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.APP_SOFT_UPDATE_AVAILABLE);
        navigationTarget
                .withParam(NavigationTarget.IS_DEVICE_ROOTED_PARAM, false)
                .withParam(NavigationTarget.IS_FROM_APP_SHORTCUT, false)
                .withParam(NavigationTarget.IS_SECOND_LOGIN, true)
                .withParam(NavigationTarget.UPDATE_APP_DESC, view.getString(R.string.app_update_available_message_chatbot))
                .withParam(NavigationTarget.UPDATE_APP_TITLE, view.getString(R.string.app_update_available_title_chatbot));
        navigationRouter.navigateTo(navigationTarget);

        analytics.sendEvent(ChatTracking.AdobeEventName.EVENT_NAME_UPDATE_APP,
                StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    private void handleNonTpError() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.APP_SOFT_UPDATE_AVAILABLE);
        navigationTarget
                .withParam(NavigationTarget.IS_DEVICE_ROOTED_PARAM, false)
                .withParam(NavigationTarget.IS_FROM_APP_SHORTCUT, false)
                .withParam(NavigationTarget.IS_SECOND_LOGIN, true)
                .withParam(NavigationTarget.UPDATE_APP_DESC, view.getString(R.string.non_tp_error_msg_message_chatbot))
                .withParam(NavigationTarget.UPDATE_APP_TITLE, view.getString(R.string.non_tp_error_msg_title_chatbot))
                .withParam(NavigationTarget.IS_NON_TP_ERROR, true);
        navigationRouter.navigateTo(navigationTarget);

        analytics.sendEvent(ChatTracking.AdobeEventName.EVENT_NAME_NON_TP_ERROR,
                StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    void showAtmAndBranchLocations() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ATM_AND_BRANCHES_PROFILE).withParam(NavigationTarget.PARAM_IS_ATM_VISIBLE, true));
    }

    private final GetMdmProfileUseCase mGetMdmProfileUseCase;

    public void loginAndSecurityClicked() {

        if (isAuthenticated())
            loginSecurityUseCase.execute(Boolean.FALSE)
                    .compose(bindToLifecycle()).subscribe(o -> {
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    private void handleITASettingsFlow() {
        if (!featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ITA)) {
            mItaDeviceManagementUseCase.execute()
                    .compose(bindToLifecycle()).subscribe(o -> {
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        } else {
            if (view != null) view.close();
        }
    }


    public void profileLimitsClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                checkIfUserAdminUseCase.execute().subscribe(isAdminUser -> {
                    if (Boolean.TRUE.equals(isBusinessUser() && !isAdminUser))
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.PROFILE_LIMITS_BUSINESS_USER)
                                .withParam(Constants.KEY_FROM_PROFILE_LIMIT, Boolean.TRUE));
                    else
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.PROFILE_LIMITS));
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
            }
        }
    }

    public boolean isBusinessUser() {
        int clientTypeUser = applicationStorage.getInteger(Constants.KEY_USER_CLIENT_TYPE, Constants.ZERO);
        return (clientTypeUser > 30);
    }

    public void settingsClicked() {
        if (isAuthenticated())
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SETTINGS));
    }

    public void buyClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                fetchUserBirthDetail();
            }
        }
    }

    public void insuranceClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN)
                        .withIntentFlagClearTopSingleTop(true));
            }
        }
    }

    public void savedRecipientClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME)
                        .withParam(ChatbotConstants.EXTRA.NAVIGATION_TARGET,
                                ChatbotConstants.NAVIGATION_TARGET.RECIPIENT).withIntentFlagClearTopSingleTop(true));
            }
        }
    }

    public void addRecipientClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.RECIPIENT_OPTION_SELECTION));
            }
        }
    }

    public void myPocketClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                mGetOverviewUseCase
                        .execute()
                        .compose(bindToLifecycle())
                        .subscribe(overviewCachableValue -> {
                            ChatbotOverview overviewValue = ChatbotOverview.newInstance(overviewCachableValue.get());
                            isPockets = false;
                            for (ChatbotAccountsOverview accountsOverview : overviewValue.getAccountsOverviews()) {
                                ChatbotOnlineSavingsAccounts savingsAccounts = accountsOverview.getChatbotOnlineSavingsAccounts();
                                if (savingsAccounts != null && savingsAccounts.getChatbotPocketsCounter() > 0) {
                                    isPockets = true;
                                    break;
                                }
                            }
                            navigationToPocket(isPockets);
                        }, throwable -> {
                        });
            }
        }
    }

    public void transferDashboardClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TRANSFER));
            }
        }
    }

    public void navigationToPocket(boolean isPockets) {
        if (isPockets) {
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget
                            .SAVINGS_POCKET_LIST_SCREEN)
                    .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true);
            navigationRouter.navigateTo(navigationTarget);
        } else {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME)
                    .withParam(ChatbotConstants.EXTRA.NAVIGATION_TARGET,
                            StringUtils.EMPTY_STRING).withIntentFlagClearTopSingleTop(true));
        }
    }

    public void internationPaymentsClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.INTERNATIONAL_PAYMENT_OPTIONS));
            }
        }
    }

    public void taxCertificatesClicked() {
        if (isAuthenticated()) {
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.SELECT_CERTIFICATE_TYPE_ACTIVITY)
                    .withParam(ServicesNavigationTarget.PARAM_NOTIFICATION_ACCOUNT_NUMBER, "");
            navigationRouter.navigateTo(navigationTarget);
        }
    }

    public void fetchUserBirthDetail() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {

                    NavigationTarget navigationTarget = getBuyLandingNavigationTarget(featureSetController);
                    navigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, userDetail.getBirthDate());
                    navigationRouter.navigateTo(navigationTarget);

                }, throwable -> {

                });
    }


    public NavigationTarget getBuyLandingNavigationTarget(FeatureSetController featureSetController) {
        boolean isVasEnabled = !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS);
        NavigationTarget navigationTarget;
        if (isVasEnabled) {
            navigationTarget = NavigationTarget.to(NavigationTarget.VAS);
        } else {
            navigationTarget = NavigationTarget.to(NavigationTarget.BUY_LANDING);
        }
        return navigationTarget;
    }

    public void moreClicked() {
        if (isAuthenticated())
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME)
                    .withParam(ChatbotConstants.EXTRA.NAVIGATION_TARGET,
                            ChatbotConstants.NAVIGATION_TARGET.MORE).withIntentFlagClearTopSingleTop(true));
    }

    public void mycardsClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME)
                        .withParam(ChatbotConstants.EXTRA.NAVIGATION_TARGET,
                                ChatbotConstants.NAVIGATION_TARGET.MY_CARDS).withIntentFlagClearTopSingleTop(true));
            }
        }
    }

    public void applyClicked() {
        if (isAuthenticated())
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME)
                    .withParam(ChatbotConstants.EXTRA.NAVIGATION_TARGET,
                            ChatbotConstants.NAVIGATION_TARGET.APPLY).withIntentFlagClearTopSingleTop(true));
    }

    public void payClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.PAY_LANDING)
                        .withParam(NavigationTarget.PARAM_SHOW_ITT, toShowItt()));
            }
        }
    }

    public void greebBacksClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                applicationStorage.putString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME, NotificationConstants.NAVIGATION_TARGET.VIEW_GREENBACKS);
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, NotificationConstants.NAVIGATION_TARGET.VIEW_GREENBACKS));
            }
        }
    }

    public void internationalBankingClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                applicationStorage.putString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME, NotificationConstants.NAVIGATION_TARGET.INTERNATIONAL_BANKING);
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, NotificationConstants.NAVIGATION_TARGET.INTERNATIONAL_BANKING));
            }
        }
    }

    public void insuranceClaimClicked() {
        if (isAuthenticated()) {
            if (isNonTpUser()) {
                handleNonTpError();
            } else {
                fetchUserDetail();
            }
        }
    }

    public void creditScoreClicked() {
        if (isAuthenticated()) {
            applicationStorage.putString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME, ChatbotConstants.INTENT_IDENTIFIERS.INTENT_CREDIT_SCORE);
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, ChatbotConstants.INTENT_IDENTIFIERS.INTENT_CREDIT_SCORE));

        }
    }

    public void investmentClicked() {
        if (isAuthenticated()) {
            applicationStorage.putString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME, NotificationConstants.NAVIGATION_TARGET.INVESTMENT_DASHBOARD);
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, NotificationConstants.NAVIGATION_TARGET.INVESTMENT_DASHBOARD));

        }
    }


    public boolean toShowItt() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.INTERNATIONAL_PAYMENTS_TOGGLE)
                && !memoryApplicationStorage.getBoolean(TRAVEL_CARD_MINOR, false);
    }

    public boolean isAuthenticated() {
        return !apiInformation.isLoggedOut() && apiInformation.getNedbankIdUsername() != null;
    }

    private String mAccountNo;

    private void getPlasticIdandInitiateFlow(String cardNumber) {
        mGetCardsUseCase.execute().compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {

                })
                .doOnTerminate(() -> {

                })
                .subscribe(cardDataModels -> {
                    if (cardDataModels != null && view != null) {
                        createCardInfoMap(cardDataModels);
                        mCardDataModel = getSelectedCardDataModel(cardDataModels, cardNumber);
                        mAccountNo = mCardDataModel.getAccountNumber();
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ACCOUNT_STATEMENTS_ACTIVITY)
                                .withParam(ChatbotConstants.PARAMS.PARAM_SEND_FROM_ACTIVITY, ChatbotConstants.PARAMS.PARAM_SENDING_FROM_CHATBOT_ACTIVITY)
                                .withParam(ChatbotConstants.PARAMS.PARAM_STATEMENT_TYPE, Constants.StatementType.BANK_STATEMENT)
                                .withParam(ChatbotConstants.PARAMS.PARAM_ACCOUNT_NUMBER_ACCOUNT_STATEMENT, "" + mAccountNo));

                    }
                }, error -> NBLogger.e(TAG, "Plastic Api Failed"));
    }

    private ChatbotCardDataModel mCardDataModel;

    private void createCardInfoMap(List<ChatbotCardDataModel> cardDataModels) {
        if (cardDataModels != null && !cardDataModels.isEmpty()) {
            for (ChatbotCardDataModel cardDataModel : cardDataModels) {
                if (cardDataModel.getActionListDataModelList() != null &&
                        !cardDataModel.getActionListDataModelList().isEmpty()) {
                    ChatbotCardInfoDataModel cardInfo = mCardInfoToDataMapper.map(cardDataModel.getActionListDataModelList());
                    mCardInfoMap.put(cardDataModel.getCardNumber(), cardInfo);
                }
            }
        }
    }

    private ChatbotCardDataModel getSelectedCardDataModel(List<ChatbotCardDataModel> cardDataModelList, String cardNumber) {
        ChatbotCardDataModel cardDataModel = new ChatbotCardDataModel();
        for (ChatbotCardDataModel dataModel : cardDataModelList) {
            if (dataModel.getCardNumber().equalsIgnoreCase(cardNumber)) {
                cardDataModel = dataModel;
                break;
            }
        }
        return cardDataModel;
    }


    public void backActionAnalytics() {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.setBackActions();
        adobeContextData.setChatAgentTypeBot();
        analytics.sendEventActionWithMap(ChatTracking.AdobeEventName.EVENT_NAME_CB_BACK_ACTION, contextData);
    }

    boolean isInsuranceAvailable() {
        //view object is not null here. This method is called after view object null check
        boolean isValidRSAId = mApplicantIdForeignCheckValidator.validateInput(idOrTaxIdNo).isOk();
        boolean isBusinessUser = StringUtils.isNotEmpty(clientType) && Integer.parseInt(clientType)
                > Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;
        int age = StringUtils.isNotEmpty(birthDate) ? FormattingUtil.getAgeDifference(birthDate) : Constants.ZERO;
        return (!featureSetController.isFeatureDisabled(FeatureConstants.INSURANCE)
                && age >= FormattingUtil.AGE_LIMIT && !isBusinessUser && isValidRSAId);
    }

    String idOrTaxIdNo;
    String clientType;
    String birthDate;

    public void fetchUserDetail() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {

                    idOrTaxIdNo = userDetail.getIdOrTaxIdNumber();
                    clientType = userDetail.getClientType();
                    birthDate = userDetail.getBirthDate();

                    NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN)
                            .withParam(ChatbotConstants.PARAM_VALID_INSURANCE_POLICY, isInsuranceAvailable())
                            .withParam(ChatbotConstants.PARAM_VALID_INSURANCE_POLICY_DEEPLINK, true);
                    navigationRouter.navigateTo(navigationTarget);
                }, throwable -> {

                });
    }

    boolean isNonTpUser() {
        String clientTypeProfile = applicationStorage.getString(StorageKeys.CLIENT_TYPE, ClientType.TP.toString());
        return ClientType.getEnum(clientTypeProfile) == ClientType.NON_TP;
    }

    private final SwitchContextDataViewModelMapper mSwitchContextDataViewModelMapper;
    private final GetFedarationListUseCase mGetFedarationListUseCase;

    private void getFederationList(UserProfile profile, String rsaIdOrPassport) {
        mGetFedarationListUseCase.execute().subscribe(fedarationList -> {
            ArrayList<SwitchContextFedarationDetailsViewModel> fedarationViewModels =
                    (ArrayList<SwitchContextFedarationDetailsViewModel>) mSwitchContextDataViewModelMapper
                            .mapFedarationDetailResponse(fedarationList);

            SwitchContextFedarationDetailsViewModel profileModel = null;
            Boolean isDefaultFederation = false;
            if (!fedarationList.isEmpty()) {
                profileModel = fedarationViewModels.get(0);
                isDefaultFederation = profileModel.isDefaultFederation();
            }
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NEW_PROFILE_DETAILS)
                    .withParam(Constants.BUNDLE_KEYS.USER_PROFILE, profile)
                    .withParam(Constants.BUNDLE_KEYS.IS_DEFAULT_PROFILE, isDefaultFederation)
                    .withParam("profile_model", profileModel)
                    .withParam(Constants.BUNDLE_KEYS.IS_NON_TP_USER, isNonTpUser())
                    .withParam(Constants.RSA_ID_OR_PASSPORT, rsaIdOrPassport)
                    .withAllData(Boolean.TRUE));

        }, throwable -> {

        });

    }

    private void handleProfileDetailsFlow() {
        if (isMDMDisable()) {
            getProfileDetails();
        } else {
            getMdmProfileDetails();
        }
    }

    public boolean isMDMDisable() {
        return featureSetController.isFeatureDisabled(FeatureConstants.MDMPROFILEDETAILS);
    }

    void getProfileDetails() {
        mGetProfileUseCase.execute(false)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                })
                .doOnTerminate(() -> {
                })
                .subscribe(profile -> {
                    if (view != null) {
                        String rsaIdOrPassport;
                        if (!StringUtils.isNullOrEmpty(profile.getResident()) && profile.getResident().equalsIgnoreCase(FormattingUtil.SOUTH_AFRICA_CODE)) {
                            rsaIdOrPassport = profile.getRsaId();
                        } else {
                            rsaIdOrPassport = profile.getPassportNumber();
                        }
                        getFederationList(profile, rsaIdOrPassport);
                    }
                }, throwable -> {

                });

    }

    void getMdmProfileDetails() {
        mGetMdmProfileUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {

                })
                .doOnTerminate(() -> {

                })
                .subscribe(profile -> {
                    if (view != null) {
                        String rsaIdOrPassportmdm;
                        if (!StringUtils.isNullOrEmpty(profile.getResident()) && profile.getResident().equalsIgnoreCase(FormattingUtil.SOUTH_AFRICA_CODE)) {
                            rsaIdOrPassportmdm = profile.getRsaId();
                        } else {
                            rsaIdOrPassportmdm = profile.getPassportNumber();
                        }
                        getFederationList(profile, rsaIdOrPassportmdm);
                    }
                }, throwable -> {

                });

    }


    private void handleRecentPaymentsFlow() {
        if (isNonTpUser()) {
            handleNonTpError();
        } else {
            if (!featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_RECENT_PAYMENT)) {
                getRecentPayments();
            } else {
                payClicked();
            }
        }
    }

    public void getRecentPayments() {
        mChatbotRecentPaymentUseCase.execute(za.co.nedbank.core.payment.recent.Constants.ONE,
                        za.co.nedbank.core.payment.recent.Constants.SAVED)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.showProgressBar(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.showProgressBar(false);
                })
                .subscribe(this::processRecentPaymentResponse
                        , throwable -> payClicked());

    }

    public void getOnceOffPayments() {
        mChatbotRecentPaymentUseCase.execute(za.co.nedbank.core.payment.recent.Constants.ONE,
                        za.co.nedbank.core.payment.recent.Constants.ONCE_OFF)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.showProgressBar(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.showProgressBar(false);
                })
                .subscribe(onceOffPaymentResponseData -> {
                            if (onceOffPaymentResponseData != null)
                                mOnceOffPaymentsViewModel = mChatbotRecentPaymentResponseDataToViewModelMapper.mapData(onceOffPaymentResponseData);
                            handleViewMorePaymentClick();
                        }
                        , throwable -> handleViewMorePaymentClick());

    }

    private void processRecentPaymentResponse(RecentPaymentResponseData chatbotRecentPaymentResponseData) {
        if (chatbotRecentPaymentResponseData != null) {
            mRecipientPaymentViewModel = mChatbotRecentPaymentResponseDataToViewModelMapper.mapData(chatbotRecentPaymentResponseData);
            if (mRecipientPaymentViewModel != null && mRecipientPaymentViewModel.getData() != null && !mRecipientPaymentViewModel.getData().isEmpty()) {
                getOnceOffPayments();
            } else {
                payClicked();
            }
        } else {
            payClicked();
        }
    }

    public void handleViewMorePaymentClick() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RECENT_PAYMENT_HISTORY);

        if (mRecipientPaymentViewModel != null && CollectionUtils.isNotEmpty(mRecipientPaymentViewModel.getData()))
            navigationTarget.withParam(Constants.BUNDLE_KEYS.RECIPIENT_PAYMENT_DATA, mRecipientPaymentViewModel.getData());
        if (mOnceOffPaymentsViewModel != null && mOnceOffPaymentsViewModel.getMetaDataViewModel() != null)
            navigationTarget.withParam(Constants.BUNDLE_KEYS.RECIPIENT_DATA_PAGE_LIMIT, mOnceOffPaymentsViewModel.getMetaDataViewModel().getPageLimit());
        if (mOnceOffPaymentsViewModel != null && CollectionUtils.isNotEmpty(mOnceOffPaymentsViewModel.getData())) {
            navigationTarget.withParam(Constants.BUNDLE_KEYS.ONCEOFF_PAYMENT_DATA, mOnceOffPaymentsViewModel.getData());
        }
        navigationTarget.withParam(Constants.BUNDLE_KEYS.CURRENT_PAGE, 0);
        if (mOnceOffPaymentsViewModel != null && mOnceOffPaymentsViewModel.getMetaDataViewModel() != null)
            navigationTarget.withParam(Constants.BUNDLE_KEYS.ONCEOFF_DATA_PAGE_LIMIT, mOnceOffPaymentsViewModel.getMetaDataViewModel().getPageLimit());

        navigationRouter.navigateTo(navigationTarget);

    }

    private void navigateToCashSendMoney() {
        if (isNonTpUser()) {
            handleNonTpError();
        } else {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SEND_CASH_LANDING));
        }
    }

    private final GetAppointmentListUseCase mGetAppointmentListUseCase;
    private final AppointmentResponseDataToViewModelMapper mAppointmentResponseDataToViewModelMapper;

    void getAppointments() {
        mGetAppointmentListUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.showProgressBar(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.showProgressBar(false);
                })
                .subscribe(response -> {
                    if (view != null) {
                        checkIfAppointmentsAreAvailable(mAppointmentResponseDataToViewModelMapper.transform(response));
                    }
                }, throwable -> {

                });
    }

    public void checkIfAppointmentsAreAvailable(AppointmentsResponseViewModel appointmentsResponseViewModel) {
        if (appointmentsResponseViewModel != null && appointmentsResponseViewModel.getAppointmentsViewModelList() != null && view != null) {
            if (!appointmentsResponseViewModel.getAppointmentsViewModelList().isEmpty())
                handleManageAppointmentClick(appointmentsResponseViewModel);
            else
                handleBookAppointmentClick();
        }

    }

    public void handleBookAppointmentClick() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NEW_APPOINTMENT)
                .withParam(NavigationTarget.PARAM_SEC_OFFICER_CD, mScOfficerCD)
                .withParam(NavigationTarget.PARAM_BOOKING_ENTRY_TYPE, BookingEntryType.GET_IN_TOUCH));
    }

    public void handleManageAppointmentClick(AppointmentsResponseViewModel appointmentsResponseViewModel) {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MANAGE_APPOINTMENT)
                .withParam(NavigationTarget.PARAM_SEC_OFFICER_CD, mScOfficerCD)
                .withParam(NavigationTarget.PARAM_APPOINTMENT_LIST_VIEW_MODEL, appointmentsResponseViewModel));
    }

    public void fetchBankerDetail() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {

                    mScOfficerCD = userDetail.getSecOfficerCd();
                    getAppointments();
                }, throwable -> {

                });
    }

    public void trackAnalyticsOnPageLoad() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setEntryPoint(VAL_EDUCATION_SCREEN_ENTRY_POINT);
        adobeContextData.setFeatureCategory(VAL_EDUCATION_SCREEN_FEATURE_CATEGORY);
        adobeContextData.setFeature(VAL_EDUCATION_SCREEN_FEATURE);
        analytics.sendEventStateWithMap(VAL_EDUCATION_CONNECTION_SCREEN_STATE, cdata);
    }

    public void sendAnalyticsOnLeaveChat() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setScreenName(VAL_CHAT_SCREEN_NAME);
        adobeContextData.setFeatureCategoryCount();
        adobeContextData.setFeatureCount();
        analytics.sendEventActionWithMap(VAL_CHAT_SCREEN_LEAVE, cdata);
    }

}
