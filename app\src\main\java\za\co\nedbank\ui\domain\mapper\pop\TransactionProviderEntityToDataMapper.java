package za.co.nedbank.ui.domain.mapper.pop;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.data.mapper.MetaDataEntityToDataMapper;
import za.co.nedbank.core.payment.recent.NotificationDetailsEntity;
import za.co.nedbank.payment.common.domain.data.model.NotificationDetailsData;
import za.co.nedbank.ui.data.entity.pop.TransactionHistoryEntity;
import za.co.nedbank.ui.data.entity.pop.TransactionHistoryParentEntity;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryData;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryParentData;

public class TransactionProviderEntityToDataMapper {
    private MetaDataEntityToDataMapper mMetadataEntityToDataMapper;

    @Inject
    TransactionProviderEntityToDataMapper(MetaDataEntityToDataMapper transactionMetadataEntityToDataMapper) {
        mMetadataEntityToDataMapper = transactionMetadataEntityToDataMapper;

    }

    public TransactionHistoryParentData mapTransactionHistoryParentEntityToData(TransactionHistoryParentEntity transactionHistoryParentEntity) {
        if (transactionHistoryParentEntity == null) {
            return null;
        }
        TransactionHistoryParentData transactionHistoryParentData = new TransactionHistoryParentData();
        transactionHistoryParentData.setTransactionHistoryData(mapTransactionHistoryParentEntityToDataList(transactionHistoryParentEntity));
        if (transactionHistoryParentEntity.getMetaDataEntity() != null) {
            transactionHistoryParentData.setTransactionMetadataModel(mMetadataEntityToDataMapper.mapMetaData(transactionHistoryParentEntity.getMetaDataEntity()));
        }
        return transactionHistoryParentData;
    }

    private List<TransactionHistoryData> mapTransactionHistoryParentEntityToDataList(TransactionHistoryParentEntity transactionHistoryParentEntity) {
        List<TransactionHistoryData> transactionHistoryDataList = new ArrayList<>();
        if (transactionHistoryParentEntity != null) {
            for (TransactionHistoryEntity transactionHistoryEntity : transactionHistoryParentEntity.getData()) {
                if (transactionHistoryEntity != null)
                    transactionHistoryDataList.add(mapTransactionHistoryEntityToData(transactionHistoryEntity));
            }
        }
        return transactionHistoryDataList;
    }

    private TransactionHistoryData mapTransactionHistoryEntityToData(TransactionHistoryEntity transactionHistoryEntity) {
        TransactionHistoryData transactionHistoryData = new TransactionHistoryData();
        transactionHistoryData.setTransactionType(transactionHistoryEntity.getTransactionType());
        transactionHistoryData.setContactCardID(transactionHistoryEntity.getContactCardID());
        transactionHistoryData.setMyReference(transactionHistoryEntity.getMyReference());
        transactionHistoryData.setAmount(transactionHistoryEntity.getAmount());
        transactionHistoryData.setBatchID(transactionHistoryEntity.getBatchID());
        transactionHistoryData.setStatus(transactionHistoryEntity.getStatus());
        transactionHistoryData.setInstantPayment(transactionHistoryEntity.isInstantPayment());
        transactionHistoryData.setBeneficiaryID(transactionHistoryEntity.getBeneficiaryID());
        transactionHistoryData.setBfName(transactionHistoryEntity.getBfName());
        transactionHistoryData.setCapturedDate(transactionHistoryEntity.getCapturedDate());
        transactionHistoryData.setDestinationNumber(transactionHistoryEntity.getDestinationNumber());
        transactionHistoryData.setMyDescription(transactionHistoryEntity.getMyDescription());
        transactionHistoryData.setNextTransDate(transactionHistoryEntity.getNextTransDate());
        transactionHistoryData.setBeneficiaryAccount(transactionHistoryEntity.getBeneficiaryAccount());
        transactionHistoryData.setPrepaidStatus(transactionHistoryEntity.getPrepaidStatus());
        transactionHistoryData.setProductCode(transactionHistoryEntity.getProductCode());
        transactionHistoryData.setPurchaseReferenceNumber(transactionHistoryEntity.getPurchaseReferenceNumber());
        transactionHistoryData.setServiceProvider(transactionHistoryEntity.getServiceProvider());
        transactionHistoryData.setStartDate(transactionHistoryEntity.getStartDate());
        transactionHistoryData.setTransactionID(transactionHistoryEntity.getTransactionID());
        transactionHistoryData.setNotificationDetailsData(mapNotification(transactionHistoryEntity.getNotificationDetails()));
        transactionHistoryData.setRapidPayment(transactionHistoryEntity.isRapidPayment());
        transactionHistoryData.setProxyDomain(transactionHistoryEntity.getProxyDomain());
        transactionHistoryData.setProxyName(transactionHistoryEntity.getProxyName());
        transactionHistoryData.setProxyType(transactionHistoryEntity.getProxyType());
        transactionHistoryData.setBeneficiaryType(transactionHistoryEntity.getBeneficiaryType());
        return transactionHistoryData;
    }

    private List<NotificationDetailsData> mapNotification(List<NotificationDetailsEntity> notificationDetailsEntities) {
        List<NotificationDetailsData> notificationDetailsData = null;
        if (notificationDetailsEntities != null && !notificationDetailsEntities.isEmpty()) {
            notificationDetailsData = new ArrayList<>();
            for (NotificationDetailsEntity notificationEntity : notificationDetailsEntities) {
                NotificationDetailsData notificationData = new NotificationDetailsData();
                notificationData.setNotificationType(notificationEntity.getNotificationType());
                notificationData.setNotificationAddress(notificationEntity.getNotificationAddress());
                notificationDetailsData.add(notificationData);
            }
        }
        return notificationDetailsData;
    }
}
