package za.co.nedbank.ui.view.notification.transaction_notification.inbox;

import static com.google.android.material.snackbar.BaseTransientBottomBar.LENGTH_LONG;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.tabs.TabLayout;
import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.adapter.SectionAdapterItem;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.databinding.ActivityTransactionInboxBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.NotificationHelper;
import za.co.nedbank.ui.view.notification.notification_messages.MultipleSelectionHandler;
import za.co.nedbank.ui.view.notification.notification_messages.RecyclerItemTouchHelper;
import za.co.nedbank.ui.view.notification.transaction_notification.sort_notifications.SortNotificationsActivity;
import za.co.nedbank.uisdk.component.CompatSearch;

public class TransactionInboxActivity extends NBBaseActivity implements TransactionInboxView, TransactionsInboxAdapter.OnMessageItemClickListener, TransactionInboxRowInterface, RecyclerItemTouchHelper.RecyclerItemTouchHelperListener, MultipleSelectionHandler.OnSelectionChangeListener, FilterOptionFragment.OnFragmentInteractionListener {

    @Inject
    TransactionInboxPresenter mTransactionInboxPresenter;

    @Inject
    NotificationHelper mNotificationHelper;

    @Inject
    APIInformation apiInformation;

    private TransactionsInboxAdapter mTransactionsInboxAdapter;
    private List<FBTransactionNotificationsViewModel> mTransactionList;
    private int notificationID = -1;
    private ItemTouchHelper mItemTouchHelper;
    private boolean mSelectionStarted = false;

    private int pageNumber = 0;
    private ArrayList<String> sortOptions;
    private ArrayList<String> groupOptions;
    private ArrayList<String> typeOptions;

    private boolean isFilterShowing = false;
    private boolean isSearchingTransaction = false;

    private String filterItem = "";
    private String filterType = "";

    private static int SORT_SELECTION_REQUEST = 2;
    private int sortIndex = 0;
    Handler adobeTaskHandler;
    Runnable runTask;
    private static final long MILLI_SECONDS_2000 = 2000;
    private boolean isUndoTimerComplete = false;
    private boolean isDeleteSuccess = false;
    private ActivityTransactionInboxBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityTransactionInboxBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        mTransactionInboxPresenter.bind(this);
        receiveBundle();
        initToolbar();
        init();
    }

    void onClickFilterClose() {
        binding.advanceSearchText.clearFocus();
        Intent intent = new Intent(this, SortNotificationsActivity.class);
        intent.putExtra(NotificationConstants.EXTRA.SORT_INDEX, sortIndex);
        startActivityForResult(intent, SORT_SELECTION_REQUEST);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && (requestCode == SORT_SELECTION_REQUEST && data != null)) {
                sortIndex = data.getIntExtra(NotificationConstants.EXTRA.SORT, 0);
                String sortParam = Constants.FilterTypes.MOST_RECENT_DATE;
                switch (sortIndex) {
                    case 0:
                        sortParam = Constants.FilterTypes.MOST_RECENT_DATE;
                        break;
                    case 1:
                        sortParam = Constants.FilterTypes.OLDEST_DATE;
                        break;
                    case 2:
                        sortParam = Constants.FilterTypes.LOWEST_AMOUNT;
                        break;
                    case 3:
                        sortParam = Constants.FilterTypes.HIGHEST_AMOUNT;
                        break;
                    default:
                }
                mTransactionInboxPresenter.filterTransactionList(sortParam, mTransactionList);

        }
    }

    private void receiveBundle() {
        if (getIntent() != null && getIntent().getExtras() != null &&
                (getIntent().getExtras().containsKey(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL))) {
                FBNotificationsViewModel notificationData = (FBNotificationsViewModel) getIntent().getExtras().get(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL);
                if (notificationData != null) {
                    notificationID = notificationData.getNotificationId().intValue();
                }

        }
    }

    private void initToolbar() {
        if (isLoggedIn()) {
            initToolbar(binding.toolbar, true, true);
        } else {
            initToolbar(binding.toolbar, false, true);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public boolean onOptionsItemSelected(final MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            if (mSelectionStarted) {
                endSelection();
            } else {
                supportFinishAfterTransition();
            }
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private boolean isLoggedIn() {
        return !apiInformation.isLoggedOut();
    }

    private void init() {
        mTransactionsInboxAdapter = new TransactionsInboxAdapter(this, false);
        mTransactionsInboxAdapter.setSelected(notificationID);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        linearLayoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        linearLayoutManager.setAutoMeasureEnabled(true);
        binding.recyclerView.setLayoutManager(linearLayoutManager);
        binding.recyclerView.setAdapter(mTransactionsInboxAdapter);
        binding.recyclerView.setNestedScrollingEnabled(false);
        mTransactionsInboxAdapter.attachToRecycler(binding.recyclerView);
        mTransactionsInboxAdapter.setTransactionRowInterface(this);
        mTransactionsInboxAdapter.setOnSelectionChangedListener(this);
        mTransactionsInboxAdapter.setOnMessageItemClickListener(this);
        mTransactionsInboxAdapter.setLoadMoreListener(pageNumber -> mTransactionInboxPresenter.loadMessages(pageNumber));

        // attaching the touch helper to recycler view
        RecyclerItemTouchHelper mItemTouchHelperCallback = new RecyclerItemTouchHelper(R.id.notification_content_view, 0, ItemTouchHelper.LEFT, this);
        mItemTouchHelper = new ItemTouchHelper(mItemTouchHelperCallback);
        mItemTouchHelper.attachToRecyclerView(binding.recyclerView);

        mTransactionsInboxAdapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
            @Override
            public void onChanged() {
                changeEmptyView();
            }

            @Override
            public void onItemRangeRemoved(int positionStart, int itemCount) {
                changeEmptyView();
            }

            @Override
            public void onItemRangeInserted(int positionStart, int itemCount) {
                changeEmptyView();
            }
        });
        mTransactionInboxPresenter.loadMessages();

        binding.nestedScrollView.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            if ((scrollY >= (v.getChildAt(v.getChildCount() - 1).getMeasuredHeight() - v.getMeasuredHeight())) &&
                    scrollY > oldScrollY) {
                pageNumber = pageNumber + 1;
                mTransactionInboxPresenter.loadMessages(pageNumber);
            }
        });

        binding.filterTabLayout.addTab(binding.filterTabLayout.newTab().setText(getString(R.string.sort)));
        binding.filterTabLayout.addTab(binding.filterTabLayout.newTab().setText(getString(R.string.group)));
        binding.filterTabLayout.addTab(binding.filterTabLayout.newTab().setText(getString(R.string.type)));
        binding.filterTabLayout.setTabGravity(TabLayout.GRAVITY_FILL);

        sortOptions = new ArrayList<>();
        groupOptions = new ArrayList<>();
        typeOptions = new ArrayList<>();
        sortOptions.addAll(Arrays.asList(getResources().getStringArray(R.array.filter_sort_array)));
        groupOptions.addAll(Arrays.asList(getResources().getStringArray(R.array.filter_group_array)));
        typeOptions.addAll(Arrays.asList(getResources().getStringArray(R.array.filter_type_array)));

        binding.filterTabLayout.setOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                mTransactionsInboxAdapter.swapSectionedData(mTransactionList, null);
                mTransactionsInboxAdapter.notifyDataSetChanged();
                setCurrentTabFragment(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                // Override method - Implementation not required
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
                // Override method - Implementation not required
            }
        });
        addListenerForCategorySearchInput(binding.advanceSearchText);
        binding.advanceSearchText.clearErrors();
        mTransactionInboxPresenter.clearNotificationModelFromMemory();
        binding.ivDelete.setOnClickListener(v -> onDeleteIconClick());
        binding.cbSelectAll.setOnClickListener(v -> onSelectAllClick());
        binding.tvFilter.setOnClickListener(v -> onClickFilterClose());
    }

    void addListenerForCategorySearchInput(CompatSearch compatEdtCategorySearchInput) {
        RxTextView.textChanges(compatEdtCategorySearchInput.getInputField()).subscribe(chars -> handleSearchField(compatEdtCategorySearchInput.getValue().trim()), throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    private void handleSearchField(String charSequence) {
        isSearchingTransaction = true;
        if (StringUtils.isNullOrEmpty(charSequence)) {
            isSearchingTransaction = false;
        }
        if (mTransactionList != null && !mTransactionList.isEmpty()) {
            mTransactionInboxPresenter.filter(charSequence.trim(), filterItem, mTransactionList);
        }
    }

    private void setCurrentTabFragment(int tabPosition) {
        switch (tabPosition) {
            case 0:
                FilterOptionFragment sortOptionFragment = FilterOptionFragment.newInstance(sortOptions, Constants.SORT);
                replaceFragment(sortOptionFragment);
                break;
            case 1:
                FilterOptionFragment groupOptionFragment = FilterOptionFragment.newInstance(groupOptions, Constants.GROUP);
                replaceFragment(groupOptionFragment);
                break;
            case 2:
                FilterOptionFragment typeOptionFragment = FilterOptionFragment.newInstance(typeOptions, Constants.TYPE);
                replaceFragment(typeOptionFragment);
                break;
            default:
                break;
        }
    }

    public void replaceFragment(Fragment fragment) {
        FragmentManager fm = getSupportFragmentManager();
        FragmentTransaction ft = fm.beginTransaction();
        ft.replace(R.id.frame_container, fragment);
        ft.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN);
        ft.commit();
    }

    @Override
    public void onTransactionClicked(FBTransactionNotificationsViewModel transaction) {
        String transType = transaction.getMetaTransType();
        if (transType.equalsIgnoreCase(NotificationConstants.TRANSACTION_TYPES.DEBIT_ORDER)) {
            if (transaction.getMetaAccNumber() != null) {
                mTransactionInboxPresenter.handleDebitOrderListFlow(transaction.getMetaAccNumber());
            }
        } else {
            mTransactionInboxPresenter.navigateToReportPage(transaction);
        }

    }

    @Override
    public void onTransactionDetailsShow(FBTransactionNotificationsViewModel transaction) {
        mTransactionInboxPresenter.markRead(transaction);
    }

    @Override
    public void onSearchFilter(List<FBTransactionNotificationsViewModel> transactions) {
        if (mTransactionsInboxAdapter != null) {
            mTransactionsInboxAdapter.setSelected(-1);
            mTransactionsInboxAdapter.swapSectionedData(transactions, null);
            mTransactionsInboxAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onSearchFilter(List<FBTransactionNotificationsViewModel> transactions, String sortParameter) {
        if (mTransactionsInboxAdapter != null) {
            mTransactionsInboxAdapter.setSelected(-1);
            if (sortParameter == null)
                mTransactionsInboxAdapter.swapSectionedData(transactions, null);
            else
                mTransactionsInboxAdapter.swapSectionedData(transactions, sortParameter);
            mTransactionsInboxAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void updateNotificationMessagesPageData(List<FBTransactionNotificationsViewModel> items) {
        if (items.isEmpty()) {
            mTransactionsInboxAdapter.endReached();
        } else {
            mTransactionList.addAll(items);
            if (mTransactionsInboxAdapter != null) {
                onFragmentInteraction(filterItem, filterType);
                mTransactionsInboxAdapter.notifyDataSetChanged();
            } else {
                mTransactionsInboxAdapter = new TransactionsInboxAdapter(this, false);
                changeEmptyView();
            }
        }
    }

    @Override
    public void showErrorForNotificationMessages(String message) {
        changeEmptyView();
        showError(getString(za.co.nedbank.loans.R.string.snackbar_header_default), message);
    }

    @Override
    public void showProgress(boolean isVisible) {
        binding.notificationMessagesProgressBar.setVisibility(isVisible ? View.VISIBLE : View.GONE);
    }

    public void onSelectAllClick() {
        mTransactionsInboxAdapter.selectAll(binding.cbSelectAll.isChecked());
    }

    @Override
    public void markReadMessage(FBTransactionNotificationsViewModel fbTransactionNotificationsViewModel, boolean isRead) {
        if (isRead) {
            fbTransactionNotificationsViewModel.setRead(isRead);
            mTransactionsInboxAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void changeEmptyView() {
        if (mTransactionsInboxAdapter.getItemCount() > 0) {
            ViewUtils.hideViews(binding.emptyStateInbox);
            ViewUtils.showViews(binding.recyclerView);
        } else {
            if (isFilterShowing || isSearchingTransaction) {
                binding.imgEmptyImage.setImageResource(R.drawable.ic_no_maching);
                binding.tvEmptyTextHeading.setText(R.string.no_maching_transaction_alerts_found);
                binding.tvEmptyTextMessage.setText(R.string.please_refine_your_search_and_try_again);
            } else {
                binding.imgEmptyImage.setImageResource(R.drawable.ic_notification_empty_bell_wrapper);
                binding.tvEmptyTextHeading.setText(R.string.you_don_t_have_any_transaction);
                binding.tvEmptyTextMessage.setText(R.string.keep_a_lookout_for_further_alert_on_your_device);
            }
            ViewUtils.showViews(binding.emptyStateInbox);
            ViewUtils.hideViews(binding.recyclerView);
            if (isFilterShowing) {
                binding.searchDivider.setBackgroundResource(R.color.eeeeee);
            } else {
                binding.searchDivider.setBackgroundResource(R.color.transparent);
            }
        }
    }

    @Override
    public void showListLoaded() {
        mTransactionsInboxAdapter.setLoadMoreEnabled(true);

    }

    public void onDeleteIconClick() {
        setDeleteSuccess(false);
        mTransactionInboxPresenter.deleteMessages(mTransactionsInboxAdapter.getSelectedItems());
    }

    @Override
    public String getDeleteErrorMsg() {
        return getString(R.string.delete_error);

    }

    @Override
    public void showGenericError() {
        showError(getString(R.string.something_went_wrong), getString(R.string.try_again_later));
    }

    @Override
    public void updateNotificationMessages(List<FBTransactionNotificationsViewModel> items) {
        mTransactionList = items;
        if (mTransactionsInboxAdapter != null && mTransactionList != null) {
            mTransactionsInboxAdapter.setSelected(notificationID);
            mTransactionsInboxAdapter.swapSectionedData(mTransactionList, null);
            mTransactionsInboxAdapter.notifyDataSetChanged();
        } else {
            mTransactionsInboxAdapter = new TransactionsInboxAdapter(this, false);
            changeEmptyView();
        }
    }

    @Override
    public void updateTransactionNotificationPageData(List<Object> items) {
            // Override method - Implementation not required
    }

    @Override
    public void onNotificationReceived(Object notificationModal) {
            // Override method - Implementation not required
    }

    @Override
    public void showSelected() {
        // Override method - Implementation not required
    }

    @Override
    public void restoreDeletedItems() {
        mTransactionsInboxAdapter.restoreDeletedItems();
        ViewUtils.showViews(binding.layoutSearch);
    }

    @Override
    public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction, int position) {
        if (viewHolder instanceof TransactionInboxViewHolder) {
            setDeleteSuccess(false);
            mTransactionInboxPresenter.deleteMessage(mTransactionsInboxAdapter.getItemAt(position));
            mTransactionsInboxAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void showUndoDeleteOption(int noOfDeletedMessages) {
        String message = getResources().getQuantityString(R.plurals.no_of_messages_deleted, noOfDeletedMessages, noOfDeletedMessages);
        showError(message, getString(R.string.press_undo_to_cancel), getString(R.string.snackbar_action_undo), LENGTH_LONG, () -> undoClickAction());
        sendDeleteMessageSuccesTag(noOfDeletedMessages);
    }

    private void undoClickAction() {
        cancelAdobeTaskHandler();
        mTransactionInboxPresenter.undoDeletedItems(mTransactionsInboxAdapter.recentDeletedItems());
    }

    private void sendDeleteMessageSuccesTag(int noOfDeletedMessages) {
        adobeTaskHandler = new Handler();
        isUndoTimerComplete = false;
        runTask = () -> {
            isUndoTimerComplete = true;
            if (mTransactionInboxPresenter != null)
                mTransactionInboxPresenter.trackDeleteApiSuccessAction(noOfDeletedMessages);
        };
        //The delay used is NBSnackbar.SHORT_DURATION + 500
        adobeTaskHandler.postDelayed(runTask, MILLI_SECONDS_2000);
    }

    private void cancelAdobeTaskHandler() {
        isUndoTimerComplete = false;
        setDeleteSuccess(false);
        if (runTask != null && adobeTaskHandler != null)
            adobeTaskHandler.removeCallbacks(runTask);
    }

    @Override
    public void removeDeletedItem(SectionAdapterItem<FBTransactionNotificationsViewModel> notificationsViewModel) {
        mTransactionsInboxAdapter.removeItemAt(notificationsViewModel);
    }

    @Override
    public void clearDeletedItems() {
        mTransactionsInboxAdapter.deleteSelectedItems();
    }

    @Override
    public void endSelection() {
        mSelectionStarted = false;
        //enable swipe to delete
        mItemTouchHelper.attachToRecyclerView(binding.recyclerView);
        //hide and reset delete options
        binding.cbSelectAll.setChecked(false);
        ViewUtils.hideViews(binding.selectAllContainer);
        ViewUtils.showViews(binding.layoutSearch);
        mTransactionsInboxAdapter.clearSelections();
        mTransactionsInboxAdapter.setLoadMoreEnabled(true);
    }

    @Override
    public void onSelectionStarted() {
        mSelectionStarted = true;
        mItemTouchHelper.attachToRecyclerView(null);
        ViewUtils.showViews(binding.selectAllContainer);
        ViewUtils.hideViews(binding.layoutSearch);
        ViewUtils.hideViews(binding.layoutFilter);
        ViewUtils.hideSoftKeyboard(this, binding.advanceSearchText);
        isFilterShowing = false;
        mTransactionsInboxAdapter.setLoadMoreEnabled(false);
    }

    @Override
    public void onSelectionChanged(List<Integer> selectedItems) {
        boolean isAllSelected = mTransactionsInboxAdapter.isAllSelected();
        binding.cbSelectAll.setChecked(isAllSelected);
        if (selectedItems.isEmpty()) {
            binding.ivDelete.setTextColor(getResources().getColor(R.color.color_bbbbbb));
            binding.ivDelete.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_trash_disable_wrapper, 0);
        } else {
            binding.ivDelete.setTextColor(getResources().getColor(R.color.gray_666666));
            binding.ivDelete.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_trash_enable_wrapper, 0);
        }
    }

    @Override
    public void onMessageItemClick(SectionAdapterItem<FBTransactionNotificationsViewModel> notificationsViewModel, int pos) {
        mTransactionInboxPresenter.handleMessageItemClick(notificationsViewModel);
        mTransactionsInboxAdapter.notifyItemChanged(pos);
    }

    @Override
    public void onFragmentInteraction(String value, String type) {
        filterItem = value;
        filterType = type;
        if (mTransactionList != null && !mTransactionList.isEmpty()) {
            switch (type) {
                case Constants.SORT:
                case Constants.GROUP:
                case Constants.TYPE:
                    mTransactionInboxPresenter.filterTransactionList(value, mTransactionList);
                    break;
                default:
                    mTransactionsInboxAdapter.swapSectionedData(mTransactionList, null);
                    mTransactionsInboxAdapter.notifyDataSetChanged();
                    break;
            }
        }
    }

    @Override
    public void onBackPressed() {
        if (mSelectionStarted) {
            endSelection();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        cancelAdobeTaskHandler();
        super.onDestroy();
    }

    @Override
    public boolean isUndoTimerComplete() {
        return isUndoTimerComplete;
    }

    @Override
    public void setDeleteSuccess(boolean isDeleteSuccess) {
        this.isDeleteSuccess = isDeleteSuccess;
    }

    @Override
    public boolean isDeleteSuccess() {
        return isDeleteSuccess;
    }
}
