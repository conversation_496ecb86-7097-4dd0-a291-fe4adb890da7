/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.link_finance;

import android.content.Context;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.R;
import za.co.nedbank.ui.data.entity.LinkAccountEntity;
import za.co.nedbank.ui.domain.repository.ILinkAccountInfoRepository;

/**
 * Created by piyushgupta01 on 8/1/2017.
 */

public class NBLinkAccountInfoRepository implements ILinkAccountInfoRepository {

    private final Context mContext;

    @Inject
    public NBLinkAccountInfoRepository(Context context) {
        mContext = context;
    }

    @Override
    public Observable<List<LinkAccountEntity>> getAccountLinkingInfo() {
        List<LinkAccountEntity> linkAccountEntityList = new ArrayList<>();
        LinkAccountEntity linkAccountEntity = new LinkAccountEntity();
        linkAccountEntity.setHeader(mContext.getResources().getString(R.string.compare_your_inflow_and_outflow));
        linkAccountEntity.setDescription(mContext.getResources().getString(R.string.see_how_your_expense_stack_up));
        linkAccountEntity.setImageResId(R.drawable.img_link_account_graph);
        linkAccountEntityList.add(linkAccountEntity);

        linkAccountEntity = new LinkAccountEntity();
        linkAccountEntity.setHeader(mContext.getResources().getString(R.string.see_where_your_money));
        linkAccountEntity.setDescription(mContext.getResources().getString(R.string.all_your_transactions));
        linkAccountEntity.setImageResId(R.drawable.img_link_account_pie_chart);
        linkAccountEntityList.add(linkAccountEntity);

        return Observable.defer(() -> Observable.just(linkAccountEntityList));
    }
}
