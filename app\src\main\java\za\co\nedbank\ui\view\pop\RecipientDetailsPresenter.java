package za.co.nedbank.ui.view.pop;

import static za.co.nedbank.core.Constants.BaseInfoIntentForFicaSDK.FICA;
import static za.co.nedbank.core.navigation.NavigationTarget.IS_FROM_HOME;
import static za.co.nedbank.core.navigation.NavigationTarget.IS_RETAIL;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_SHOW_INTERNATIONAL_ACCOUNT_TILE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_SHOW_PAY_TO_BUSINESS;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_WHICH_IP_FLOW;
import static za.co.nedbank.core.navigation.NavigationTarget.RECIPIENT_DETAIL_WITH_HISTORY;
import static za.co.nedbank.payment.internationalpayment.Constants.CLIENT_NUMBER_TYPE;

import java.util.HashMap;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.profile.UserProfile;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetClearCacheApiUseCase;
import za.co.nedbank.core.domain.usecase.GetOddReviewableReasonUseCase;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.eficasdk.FicaSDKUseCase;
import za.co.nedbank.core.domain.usecase.fatca.FatcaMissingInfoUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetMdmProfileUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.FatcaRestrictionUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.mapper.GetOddReviewableResponseDataToViewModelMapper;
import za.co.nedbank.core.view.model.mdm.response.odd.OddReviewableViewModel;
import za.co.nedbank.eficasdk.view.viewmodel.FicaSDKViewModel;
import za.co.nedbank.payment.common.view.IPayChildScreenTypes;
import za.co.nedbank.payment.common.view.beneficiary.user.model.SelectedUserBeneficiaryViewModel;
import za.co.nedbank.payment.common.view.tracking.PaymentsTracking;
import za.co.nedbank.payment.common.view.tracking.PaymentsTrackingEvent;
import za.co.nedbank.payment.internationalpayment.domain.model.request.RequestHeaderDataModel;
import za.co.nedbank.payment.internationalpayment.domain.usecase.GetForexClientDetailUseCase;
import za.co.nedbank.payment.internationalpayment.view.IPType;
import za.co.nedbank.payment.internationalpayment.view.mapper.AddressOTTDataToViewMapper;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.profile.view.navigation.ProfileNavigationTarget;
import za.co.nedbank.profile.view.profile.fatca_unrestriction.FatcaUnrestrictionOnboardingActivity;
import za.co.nedbank.profile.view.profile.fatca_unrestriction.FatcaUnrestrictionType;
import za.co.nedbank.ui.view.home.HomePresenter;
import za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorActivity;
import za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorType;
import za.co.nedbank.ui.view.refica.FicaErrorActivity;
import za.co.nedbank.ui.view.refica.FicaSuccessActivity;

public class RecipientDetailsPresenter  extends NBBasePresenter<RecipientDetailsView> {
    private final NavigationRouter mNavigationRouter;
    private final Analytics mAnalytics;
    private final ApplicationStorage mMemoryApplicationStorage;
    private final FeatureSetController mFtrSetController;
    private final GetUserDetailUseCase mGetUserDetailUseCase;
    private final FicaSDKUseCase mFicaSDKUseCase;
    public static final String IS_FROM_FICA = "is_from_fica";
    public static final String VIEW_TYPE = "view_type";
    public static final String POSITION = "position";
    public static final String FICA_RESPOSNE = "fica_response";
    private final ApplicationStorage inMemoryStorage;

    private final GetMdmProfileUseCase mGetMdmProfileUseCase;
    private final GetForexClientDetailUseCase mGetForexClientDetailInfoUseCase;
    private final AddressOTTDataToViewMapper mAddressOTTDataToViewMapper;
    private final GetOddReviewableReasonUseCase getOddReviewableReasonUseCase;
    private final GetOddReviewableResponseDataToViewModelMapper oddMapper;
    private final FatcaMissingInfoUseCase fatcaMissingInfoUseCase;
    private final GetClearCacheApiUseCase refreshCleanCacheUserUseCase;

    @Inject
    public RecipientDetailsPresenter(NavigationRouter navigationRouter, Analytics analytics,
                                     @Named("memory") final ApplicationStorage memoryApplicationStorage,
                                     final FeatureSetController featureSetController,
                                     final GetUserDetailUseCase mGetUserDetailUseCase,
                                     final FicaSDKUseCase mFicaSDKUseCase,
                                     @Named("memory") ApplicationStorage inMemoryStorage,
                                     AddressOTTDataToViewMapper mAddressOTTDataToViewMapper ,
                                     GetMdmProfileUseCase getMdmProfileUseCase,
                                     GetForexClientDetailUseCase mGetForexClientDetailInfoUseCase,
                                     final GetOddReviewableReasonUseCase getOddReviewableReasonUseCase,
                                     final GetOddReviewableResponseDataToViewModelMapper oddMapper,
                                     final FatcaMissingInfoUseCase fatcaMissingInfoUseCase,
                                     final GetClearCacheApiUseCase refreshCleanCacheUserUseCase) {
        this.mNavigationRouter = navigationRouter;
        this.mAnalytics = analytics;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.mFtrSetController = featureSetController;
        this.mGetUserDetailUseCase = mGetUserDetailUseCase;
        this.mFicaSDKUseCase = mFicaSDKUseCase;
        this.inMemoryStorage = inMemoryStorage;
        this.mGetMdmProfileUseCase = getMdmProfileUseCase;
        this.mAddressOTTDataToViewMapper = mAddressOTTDataToViewMapper;
        this.mGetForexClientDetailInfoUseCase = mGetForexClientDetailInfoUseCase;
        this.oddMapper = oddMapper;
        this.getOddReviewableReasonUseCase = getOddReviewableReasonUseCase;
        this.fatcaMissingInfoUseCase = fatcaMissingInfoUseCase;
        this.refreshCleanCacheUserUseCase = refreshCleanCacheUserUseCase;
    }

    public void navigateToInitiatePayScreen(SelectedUserBeneficiaryViewModel userBeneficiaryDetailsViewModel) {
        if (view != null) {
            mMemoryApplicationStorage.putString(za.co.nedbank.core.Constants.PAYMENT_START_FROM, RECIPIENT_DETAIL_WITH_HISTORY);
            NavigationTarget navigationTarget = NavigationTarget.to(PayNavigatorTarget.PAY);
            navigationTarget.withParam(Constants.EXTRAS.USER_BENEFICIARY_DETAILS_VIEW_MODEL, userBeneficiaryDetailsViewModel);
            navigationTarget.withParam(Constants.EXTRAS.SCREEN_TYPE, IPayChildScreenTypes.BENEFICIARY_SCREEN);
            mNavigationRouter.navigateTo(navigationTarget);
        }
    }

    public void navigateToInternationalPayment(String entryPoint) {
        inMemoryStorage.putBoolean(StorageKeys.FOREX_IS_PAY_FRM_RECIPIENT, true);
        inMemoryStorage.putBoolean(StorageKeys.FOREX_GO_TO_HOME, true);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.INTERNATIONAL_PAYMENT_ACCOUNT_SELECTION)
                .withParam(PARAM_EXTRA_WHICH_IP_FLOW, IPType.CMA)
                .withParam(PARAM_EXTRA_SHOW_PAY_TO_BUSINESS, true)
                .withParam(NavigationTarget.ENTRY_POINT_FOR_INTERNATION_PAYMENT, entryPoint)
                .withParam(PARAM_EXTRA_SHOW_INTERNATIONAL_ACCOUNT_TILE, false));
    }

    void trackAction() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        adobeContextData.setFeature(TrackingEvent.ANALYTICS.VAL_FEATURE_MANAGE_RECIPIENTS);
        adobeContextData.setSubFeature(TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_PAY_RECIPIENT);
        adobeContextData.setEntryPoint(TrackingEvent.ANALYTICS.VAL_ENTRY_POINT_PAY_RECIPIENT);
        adobeContextData.setFeatureCategoryCount();
        adobeContextData.setFeatureCount();
        adobeContextData.setSubFeatureCount();
        mAnalytics.sendEventActionWithMap(PaymentsTracking.PAY_RECIPIENT, cdata);
    }

    public boolean isFatcaRestrictionsApplicable() {
     return  FatcaRestrictionUtil.isFatcaRestrictionsApplicable(mMemoryApplicationStorage, mFtrSetController);
    }

    public void navigateToNextScreen() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FATCA_RESTRICTION_MESSAGE_SCREEN));
    }

    public void checkFica(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE value, int itemPositionInSection) {
        if(!mFtrSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DIGITAL_ENABLE_REFICA)) {
            mGetUserDetailUseCase.execute(false).compose(bindToLifecycle())
                    .subscribe(userDetail -> restrictionsCheck(value, itemPositionInSection, userDetail));
        } else {
            view.onItemSelected(value, itemPositionInSection);
        }
    }

    private void restrictionsCheck(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE value, int itemPositionInSection, UserDetailData userDetail) {
        boolean isFicaVerified = userDetail.isFicaVerified();
        boolean hasIdOrTaxIdNumber = !StringUtils.isNullOrEmpty(userDetail.getIdOrTaxIdNumber());
        boolean isRetailUser = (!StringUtils.isNullOrEmpty(userDetail.getClientType()) && Integer.parseInt(userDetail.getClientType())<=30);
        boolean oddRestricted = userDetail.getOddRestricted();

                        if(!isFicaVerified) {
                            checkFicaConditons(value, itemPositionInSection, isFicaVerified, hasIdOrTaxIdNumber, isRetailUser);
                        }
                        else if(oddRestricted && isOddRestrictionEnabled()){
                            if(!userDetail.getOddRequired() && !isRetailUser){
                                navigateToOddError(OddRestrictionErrorType.JURISTIC_ODD_VERIFYING);
                            }else {
                                checkODDFLow(userDetail, isRetailUser);
                            }
                        }
                        else if (FatcaRestrictionUtil.isNewFatcaRestrictionApplicable(mMemoryApplicationStorage, mFtrSetController,userDetail)) {
                            checkFatcaUnrestriction(isRetailUser,value, itemPositionInSection);
                        }
                        else{
                            view.onItemSelected(value, itemPositionInSection);
                        }
    }

    private void callRefreshApi() {
        refreshCleanCacheUserUseCase.execute(true).compose(bindToLifecycle()).subscribe(res -> mGetUserDetailUseCase.execute(true).compose(bindToLifecycle())
                .subscribe(userDetail -> navigateToFatcaUnRestrictions(FatcaUnrestrictionType.VERIFYING, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING), this::handleException));

    }

    public void checkFatcaUnrestriction(boolean isRetailUser, NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE value, int itemPositionInSection) {
        if(!isRetailUser){
            navigateToFatcaUnRestrictions(FatcaUnrestrictionType.JURISTIC, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        }else{
            fatcaMissingInfoUseCase.execute().compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                    })
                    .doOnTerminate(() -> {
                    })
                    .subscribe(data -> {
                                if(data.getData()!=null){
                                    if(data.getData().getRequiredData()!=null && !data.getData().getRequiredData().isEmpty() && data.getData().getRequiredDocuments() != null && !data.getData().getRequiredDocuments().isEmpty() ){
                                        navigateToFatcaUnRestrictions(FatcaUnrestrictionType.MISSING_INFO_FORM, data.getData().getDocumentLocation(), data.getData().getFormStr());
                                    }else if(data.getData().getRequiredData()!=null && !data.getData().getRequiredData().isEmpty() ){
                                        navigateToFatcaUnRestrictions(FatcaUnrestrictionType.MISSING_INFO, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
                                    }else if(data.getData().getRequiredDocuments() != null && !data.getData().getRequiredDocuments().isEmpty()){
                                        navigateToFatcaUnRestrictions(FatcaUnrestrictionType.MISSING_FORM, data.getData().getDocumentLocation(), data.getData().getFormStr());
                                    }else {
                                        callRefreshApi();
                                    }
                                }
                            }
                            , e -> view.errorSnackBar());
        }
    }

    private void navigateToFatcaUnRestrictions(@FatcaUnrestrictionType int type, String url, String forms){
        mNavigationRouter.navigateWithResult(NavigationTarget.to(ProfileNavigationTarget.FATCA_UNRESTRICTION_ONBOARDING_SCREEN)
                .withParam(HomePresenter.SCREEN_TYPE, type)
                .withParam("forms", forms)
                .withParam("url", url)
        ).subscribe(
                navigationResult -> {
                    if (navigationResult != null && navigationResult.isOk() &&
                            navigationResult.getParams().containsKey(FatcaUnrestrictionOnboardingActivity.ACTION) && navigationResult.getStringParam(FatcaUnrestrictionOnboardingActivity.ACTION).equals("1")) {
                        openBranchScreen();
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));

    }

    boolean isOddRestrictionEnabled() {
        return !mFtrSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DIGITAL_ENABLE_ODD_RESTRICTION);
    }

    private void checkODDFLow(UserDetailData userDetailVM, boolean isRetailUser){
        if(isRetailUser){
            if(userDetailVM.getOddRequired()) {
                callOddRulesApi();
            }else{
                navigateToOddError(OddRestrictionErrorType.ODD_VERIFYING);
            }
        }else{
            navigateToOddError(OddRestrictionErrorType.JURISTIC);
        }
    }

    private void navigateToOddError(OddRestrictionErrorType type) {
        mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.ODD_RESTRICTION_ERROR)
                .withParam(za.co.nedbank.core.Constants.TYPE,  type)
                .withAllData(Boolean.TRUE)).subscribe(
                navigationResult -> {
                    if (navigationResult != null && navigationResult.isOk() &&
                            navigationResult.getParams().containsKey(OddRestrictionErrorActivity.ACTION_KEY) && navigationResult.getStringParam(OddRestrictionErrorActivity.ACTION_KEY).equals("1")) {
                        openBranchScreen();
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void callOddRulesApi() {
        getOddReviewableReasonUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.handleProgressBar(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.handleProgressBar(false);
                })
                .subscribe(data -> {
                            OddReviewableViewModel oddReviewableData = oddMapper.mapData(data).getViewModel();
                            if(data.getOddReviewableClientDataResponse().isSelfServiceIsReviewable()){
                                getMdmProfileDetails(oddReviewableData);
                            }else {
                                boolean is1015 = data.getOddReviewableClientDataResponse().getUnReviewableReason().equals("1015");
                                navigateToOddError(is1015? OddRestrictionErrorType.REASON_1015 : OddRestrictionErrorType.REASON_OTHER);
                            }
                        }
                        , this::handleException);

    }


    void getMdmProfileDetails(OddReviewableViewModel oddReviewableData) {
        mGetMdmProfileUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.handleProgressBar(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.handleProgressBar(false);
                })
                .subscribe(profile -> {
                    if(profile!=null){
                        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CONFIRM_ODD_DETAILS)
                                .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.USER_PROFILE, profile)
                                .withParam(za.co.nedbank.core.Constants.RSA_ID_OR_PASSPORT, profile.getRsaId())
                                .withParam(za.co.nedbank.core.Constants.IS_FROM_ODD_RESTRICTION, true)
                                .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.ODD_RULES_DATA, oddReviewableData)
                                .withAllData(Boolean.TRUE));
                    }

                }, this::handleException);

    }


    private void checkFicaConditons(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE value, int itemPositionInSection, boolean isFicaVerified, boolean hasIdOrTaxIdNumber, boolean isRetailUser) {
       if(!isFicaVerified) {
           if (hasIdOrTaxIdNumber && isRetailUser) {
               mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_VERIFY_ME)).subscribe(
                       navigationResult -> {
                           if (navigationResult != null && navigationResult.isOk()) {
                               callFica(value, itemPositionInSection);
                           }
                       }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
           } else  {
               navigateToFicaError(isRetailUser);
           }
       }
        else {
            view.onItemSelected(value, itemPositionInSection);
        }
    }

    public void navigateToFicaError(boolean isRetailUser){
        mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_ERROR)
                .withParam(IS_FROM_HOME, true)
                .withParam(IS_RETAIL, isRetailUser)).subscribe(
                navigationResult -> {
                    if (navigationResult != null && navigationResult.isOk() &&
                            navigationResult.getParams().containsKey(FicaErrorActivity.ACTION) && navigationResult.getStringParam(FicaErrorActivity.ACTION).equals("1")) {
                        openBranchScreen();
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));

    }

    private void callFica(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE value, int itemPositionInSection) {
        mFicaSDKUseCase.execute(FICA).compose(bindToLifecycle())
                .subscribe(ficaSDKViewModel ->{
                    NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RECIPIENT_DETAIL_WITH_HISTORY).withIntentFlagClearTopSingleTop(true);
                    navigationTarget.withParam(za.co.nedbank.core.payment.recent.Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL, view.getUserBeneficiaryCollectiveDataViewModel());
                    navigationTarget.withParam(RecipientDetailsPresenter.IS_FROM_FICA, true);
                    navigationTarget.withParam(RecipientDetailsPresenter.VIEW_TYPE, value);
                    navigationTarget.withParam(RecipientDetailsPresenter.POSITION, itemPositionInSection);
                    navigationTarget.withParam(RecipientDetailsPresenter.FICA_RESPOSNE, ficaSDKViewModel);
                    mNavigationRouter.navigateTo(navigationTarget);
                });

    }

    public void handleFicaResponse(FicaSDKViewModel ficaSDKViewModel, NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE value, int itemPositionInSection) {
        if (ficaSDKViewModel.isFicaed()) {
            mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_SUCCESS)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk() &&
                                navigationResult.getParams().containsKey(FicaSuccessActivity.ACTION) && navigationResult.getStringParam(FicaSuccessActivity.ACTION).equals("1")) {
                            checkFica(value, itemPositionInSection);
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        } else {
            mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_ERROR)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk() &&
                                navigationResult.getParams().containsKey(FicaErrorActivity.ACTION) && navigationResult.getStringParam(FicaErrorActivity.ACTION).equals("1")) {
                            openBranchScreen();
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
    }

    public void openBranchScreen() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ATM_AND_BRANCHES_PROFILE).
                withParam(NavigationTarget.PARAM_IS_ATM_VISIBLE, true));
    }

    void fetchProfileForInternationalPaymentRecipientFlow() {
        fetchProfileForInternationalPaymentRecipientFlow(null);
    }
    void fetchProfileForInternationalPaymentRecipientFlow(String entryPoint) {
        Object ob = inMemoryStorage.getObject(StorageKeys.MDM_USER_PROFILE);
        if (ob == null) {
            mGetMdmProfileUseCase.execute()
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> handleProgressBar(true))
                    .doOnTerminate(() -> handleProgressBar(false))
                    .subscribe(profile -> {
                        if (profile != null) {
                            inMemoryStorage.putObject(StorageKeys.MDM_USER_PROFILE, profile);
                            inMemoryStorage.putString(StorageKeys.MDM_CIS_NUMBER, profile.getCisNumber());
                            getForexDetailInfoRecipientFlow(profile.getCisNumber(), entryPoint);
                        }
                    }, e -> {
                        if (view != null) {
                            view.errorSnackBar();
                        }
                    });
        } else {
            if (ob instanceof UserProfile) {
                getForexDetailInfoRecipientFlow(((UserProfile) ob).getCisNumber(), entryPoint);
            }
        }
    }
    public void handleException(Throwable throwable) {
        if (view != null) {
            view.errorSnackBar();
        }
    }

    void getForexDetailInfoRecipientFlow(String cisNumber, String entryPoint) {
        Object object = inMemoryStorage.getObject(StorageKeys.FOREX_CLIENT_DETAIL);
        if (object == null) {
            RequestHeaderDataModel headerDataModel = new RequestHeaderDataModel();
            headerDataModel.setChannelCode(za.co.nedbank.payment.internationalpayment.Constants.CHANNEL_CODE);
            headerDataModel.setClientNumber(cisNumber);
            headerDataModel.setClientNumberType(CLIENT_NUMBER_TYPE);
            mGetForexClientDetailInfoUseCase.execute(headerDataModel)
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> handleProgressBar(true))
                    .doOnTerminate(() -> handleProgressBar(false))
                    .subscribe(addressOTTModel -> {
                        if (addressOTTModel != null) {
                            inMemoryStorage.putObject(StorageKeys.FOREX_CLIENT_DETAIL, mAddressOTTDataToViewMapper.map(addressOTTModel));
                            navigateToInternationalPayment(entryPoint);
                        }
                    }, e -> {
                        if (view != null) {
                            view.errorSnackBar();
                        }
                    });
        } else {
            navigateToInternationalPayment(entryPoint);
        }
    }

    private void handleProgressBar(boolean isVisible) {
        if (view != null) {
            view.handleProgressBar(isVisible);
        }
    }
    public void trackCmaActionOnContinueClick() {
        HashMap<String, Object> cdata = new HashMap<>();
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.CMA_POPUP_CONTINUE, cdata);
    }
    public void trackCmaActionOnCancelClick() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setContext1Count();
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.CMA_POPUP_CANCEL, cdata);
    }
    public void trackCmaActionOnOpen(String bankName) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setContext1(bankName);
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.CMA_POPUP_OPEN, cdata);
    }


}
