package za.co.nedbank.ui.data.entity.pop;

import com.squareup.moshi.Json;

import java.util.List;

import za.co.nedbank.core.data.metadata.MetaDataEntity;


public class TransactionHistoryParentEntity {
    @Json(name = "data")
    private List<TransactionHistoryEntity> data;

    @Json(name = "metadata")
    private MetaDataEntity metaDataEntity;

    public List<TransactionHistoryEntity> getData() {
        return data;
    }

    public void setData(List<TransactionHistoryEntity> data) {
        this.data = data;
    }

    public MetaDataEntity getMetaDataEntity() {
        return metaDataEntity;
    }

}
