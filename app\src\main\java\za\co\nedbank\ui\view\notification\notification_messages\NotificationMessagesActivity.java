package za.co.nedbank.ui.view.notification.notification_messages;

import static com.google.android.material.snackbar.BaseTransientBottomBar.LENGTH_LONG;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.view.MenuItem;
import android.view.View;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.databinding.ActivityNotificationMessagesBinding;
import za.co.nedbank.ui.di.AppDI;

public class NotificationMessagesActivity extends NBBaseActivity implements NotificationMessagesView, RecyclerItemTouchHelper.RecyclerItemTouchHelperListener, NotificationMessagesSectionAdapter.OnMessageItemClickListener, MultipleSelectionHandler.OnSelectionChangeListener {

    @Inject
    NotificationMessagesPresenter mNotificationMessagesPresenter;
    private NotificationMessagesSectionAdapter mNotificationMessagesAdapter;
    private ItemTouchHelper mItemTouchHelper;
    private boolean mSelectionStarted = false;
    Handler adobeTaskHandler;
    Runnable runTask;
    private static final long MILLI_SECONDS_2000 =2000;
    private boolean isUndoTimerComplete = false;
    private boolean isDeleteSuccess = false;
    private ActivityNotificationMessagesBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityNotificationMessagesBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        initToolbar(binding.toolbar, true, getResources().getString(R.string.title_messages));
        setUpRecyclerView();
        mNotificationMessagesPresenter.bind(this);
        mNotificationMessagesPresenter.loadMessages();
        mNotificationMessagesAdapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
            @Override
            public void onChanged() {
                toggleEmptyView();
            }

            @Override
            public void onItemRangeRemoved(int positionStart, int itemCount) {
                toggleEmptyView();
            }

            @Override
            public void onItemRangeInserted(int positionStart, int itemCount) {
                toggleEmptyView();
            }
        });
        binding.cbSelectAll.setOnClickListener(v -> onSelectAllClick());
        binding.ivDelete.setOnClickListener(v -> onDeleteIconClick());
    }

    @Override
    public void toggleEmptyView() {
        if (mNotificationMessagesAdapter.getActualItemCount() > 0) {
            ViewUtils.hideViews(binding.emptyStateMessage);
            ViewUtils.showViews(binding.recyclerViewMessages);
        } else {
            ViewUtils.showViews(binding.emptyStateMessage);
            ViewUtils.hideViews(binding.recyclerViewMessages);
        }
    }

    @Override
    public void removeDeletedItem(FBNotificationsViewModel notificationsViewModel) {
        mNotificationMessagesAdapter.removeItemAt(notificationsViewModel);
    }

    @Override
    public void showUndoDeleteOption(int noOfDeletedMessages) {
        String message = getResources().getQuantityString(R.plurals.no_of_messages_deleted, noOfDeletedMessages, noOfDeletedMessages);
        showError(message, getString(R.string.press_undo_to_cancel), getString(R.string.snackbar_action_undo), LENGTH_LONG, () -> undoAction());
        sendDeleteMessageSuccesTag(noOfDeletedMessages);
    }

    private void undoAction() {
        cancelAdobeTaskHandler();
        mNotificationMessagesPresenter.undoDeletedItems(mNotificationMessagesAdapter.recentDeletedItems());
    }

    private void sendDeleteMessageSuccesTag(int noOfDeletedMessages) {
        adobeTaskHandler = new Handler();
        isUndoTimerComplete = false;
        runTask = () -> {
            isUndoTimerComplete = true;
            if (mNotificationMessagesPresenter != null)
                mNotificationMessagesPresenter.trackDeleteApiSuccessAction(noOfDeletedMessages);
        };
        //The delay used is NBSnackbar.SHORT_DURATION + 500
        adobeTaskHandler.postDelayed(runTask, MILLI_SECONDS_2000);
    }

    private void cancelAdobeTaskHandler() {
        isUndoTimerComplete = false;
        setDeleteSuccess(false);
        if (runTask != null && adobeTaskHandler != null)
            adobeTaskHandler.removeCallbacks(runTask);
    }

    @Override
    public void restoreDeletedItems() {
        mNotificationMessagesAdapter.restoreDeletedItems();
    }

    @Override
    public String getDeleteErrorMsg() {
        return getString(R.string.delete_error);

    }

    @Override
    public void showListLoaded() {
        mNotificationMessagesAdapter.setLoadMoreEnabled(true);
    }

    @Override
    public void updateNotificationMessagesPageData(List<FBNotificationsViewModel> items) {
        if (items.isEmpty()) {
            mNotificationMessagesAdapter.endReached();
        } else {
            mNotificationMessagesAdapter.addSectionedData(items);
        }
    }

    @Override
    public void showFullScreenError() {
        ViewUtils.showViews(binding.fullScreenErrorContainer);
    }

    @Override
    public void markReadNotification(int notificationId) {
        FBNotificationsViewModel fbNotificationViewModel = mNotificationMessagesAdapter.getItemByNotificationId(notificationId);
        mNotificationMessagesPresenter.markRead(fbNotificationViewModel);
    }

    @Override
    public void refreshItem(FBNotificationsViewModel fbNotificationsViewModel) {
        mNotificationMessagesAdapter.refreshItem(fbNotificationsViewModel);
    }

    private void setUpRecyclerView() {
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        binding.recyclerViewMessages.setLayoutManager(linearLayoutManager);
        DividerItemDecoration itemDecoration = new DividerItemDecoration(this, DividerItemDecoration.VERTICAL);
        Drawable dividerDrawable = ContextCompat.getDrawable(this, R.drawable.horizontal_divider);
        if (dividerDrawable != null) {
            itemDecoration.setDrawable(dividerDrawable);
        }
        binding.recyclerViewMessages.addItemDecoration(itemDecoration);
        mNotificationMessagesAdapter = new NotificationMessagesSectionAdapter(this);
        mNotificationMessagesAdapter.setOnSelectionChangedListener(this);
        mNotificationMessagesAdapter.setOnMessageItemClickListener(this);
        binding.recyclerViewMessages.setAdapter(mNotificationMessagesAdapter);
        mNotificationMessagesAdapter.attachToRecycler(binding.recyclerViewMessages);

        mNotificationMessagesAdapter.setLoadMoreListener(pageNumber -> {
            boolean shouldApiCall = (pageNumber * NotificationConstants.PAGINATION.DEFAULT_PAGE_SIZE) == mNotificationMessagesAdapter.getActualItemCount();
            if (shouldApiCall) {
                mNotificationMessagesPresenter.loadMessages(pageNumber);
            }
            else{
                mNotificationMessagesAdapter.setLoadMoreEnabled(false);
            }
        });

        // attaching the touch helper to recycler view
        RecyclerItemTouchHelper mItemTouchHelperCallback = new RecyclerItemTouchHelper(R.id.notification_messages_foreground_view, 0, ItemTouchHelper.LEFT, this);
        mItemTouchHelper = new ItemTouchHelper(mItemTouchHelperCallback);
        mItemTouchHelper.attachToRecyclerView(binding.recyclerViewMessages);
    }


    @Override
    public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction, int position) {
        setDeleteSuccess(false);
        mNotificationMessagesPresenter.deleteMessage(mNotificationMessagesAdapter.getItemAt(position));

    }

    @Override
    protected void onDestroy() {
        mNotificationMessagesPresenter.unbind();
        cancelAdobeTaskHandler();
        super.onDestroy();
    }

    @Override
    public void showProgress(boolean isVisible) {
        if (isVisible) {
            ViewUtils.showViews(binding.notificationMessagesProgressBar);
        } else {
            ViewUtils.hideViews(binding.notificationMessagesProgressBar);
        }
    }

    @Override
    public void showErrorForNotificationMessages(String message) {
        toggleEmptyView();
        showError(getString(za.co.nedbank.loans.R.string.snackbar_header_default), message);
    }

    @Override
    public void updateNotificationMessages(List<FBNotificationsViewModel> notificationsViewModels) {
        if (notificationsViewModels != null) {
            mNotificationMessagesAdapter.resetAdapter();
            mNotificationMessagesAdapter.addSectionedData(notificationsViewModels);
        }
    }

    @Override
    public void clearDeletedItems() {
        mNotificationMessagesAdapter.deleteSelectedItems();
    }

    @Override
    public void onNotificationReceived(FBNotificationsViewModel fbNotificationsViewModel) {
        mNotificationMessagesAdapter.addItemAtTop(fbNotificationsViewModel);
    }

    @Override
    public void onMessageItemClick(FBNotificationsViewModel fbNotificationsViewModel, int pos) {
        mNotificationMessagesPresenter.handleMessageItemClick(fbNotificationsViewModel);
    }

    @Override
    public void onSelectionStarted() {
        mSelectionStarted = true;
        mItemTouchHelper.attachToRecyclerView(null);
        ViewUtils.showViews(binding.selectAllContainer, binding.divider);
        mNotificationMessagesAdapter.setLoadMoreEnabled(false);
    }

    @Override
    public void onSelectionChanged(List<Integer> selectedItems) {
        boolean isAllSelected = mNotificationMessagesAdapter.isAllSelected();
        binding.cbSelectAll.setChecked(isAllSelected);
        binding.ivDelete.setVisibility(selectedItems.isEmpty() ? View.INVISIBLE : View.VISIBLE);
    }

    @Override
    public void endSelection() {
        mSelectionStarted = false;
        //enable swipe to delete
        mItemTouchHelper.attachToRecyclerView(binding.recyclerViewMessages);
        //hide and reset delete options
        binding.cbSelectAll.setChecked(false);
        ViewUtils.hideViews(binding.selectAllContainer, binding.divider);
        mNotificationMessagesAdapter.clearSelections();
        mNotificationMessagesAdapter.setLoadMoreEnabled(true);
    }

    public void onSelectAllClick() {
        mNotificationMessagesAdapter.selectAll(binding.cbSelectAll.isChecked());
    }

    public void onDeleteIconClick() {
        setDeleteSuccess(false);
        mNotificationMessagesPresenter.deleteMessages(mNotificationMessagesAdapter.getSelectedItems());
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            if (mSelectionStarted) {
                endSelection();
                return true;
            }
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean isUndoTimerComplete() {
        return isUndoTimerComplete;
    }

    @Override
    public void setDeleteSuccess(boolean isDeleteSuccess) {
        this.isDeleteSuccess = isDeleteSuccess;
    }

    @Override
    public boolean isDeleteSuccess() {
        return isDeleteSuccess;
    }
}
