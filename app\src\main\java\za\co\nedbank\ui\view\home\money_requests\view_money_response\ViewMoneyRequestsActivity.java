/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.graphics.Typeface;
import android.os.Bundle;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import org.greenrobot.eventbus.EventBus;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.databinding.ActivityViewMoneyRequestsBinding;
import za.co.nedbank.ui.di.AppDI;

public class ViewMoneyRequestsActivity extends NBBaseActivity implements ViewMoneyRequestsView {

    @Inject
    ViewMoneyRequestsPresenter mMoneyRequestsPresenter;

    private boolean mIsNeverTracked = true;
    private ActivityViewMoneyRequestsBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        super.onCreate(savedInstanceState);
        binding = ActivityViewMoneyRequestsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        initToolbar(binding.toolbar, true, true);

        AppDI.getActivityComponent(this).inject(this);

        setUpView();
    }

    private void setUpView() {
        ViewMoneyRequestsPagerAdapter pagerAdapter = new ViewMoneyRequestsPagerAdapter(this, getSupportFragmentManager());
        binding.viewPagerViewMoneyRequests.setAdapter(pagerAdapter);
        binding.tabLayoutViewMoneyRequests.setupWithViewPager(binding.viewPagerViewMoneyRequests);
        binding.viewPagerViewMoneyRequests.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                if (position == 0 && mIsNeverTracked) {
                    mIsNeverTracked = false;
                    mMoneyRequestsPresenter.trackMoneyRequest(position);
                }
            }

            @Override
            public void onPageSelected(int position) {
                mMoneyRequestsPresenter.trackMoneyRequest(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                //do nothing
            }
        });
        binding.tabLayoutViewMoneyRequests.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {

            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                TextView text = (TextView)(((LinearLayout)((LinearLayout)binding.tabLayoutViewMoneyRequests.getChildAt(0)).getChildAt(tab.getPosition())).getChildAt(1));
                text.setTypeface(null, Typeface.BOLD);
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                TextView text = (TextView)(((LinearLayout)((LinearLayout)binding.tabLayoutViewMoneyRequests.getChildAt(0)).getChildAt(tab.getPosition())).getChildAt(1));
                text.setTypeface(null, Typeface.NORMAL);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }

        });
        TextView text = (TextView)(((LinearLayout)((LinearLayout)binding.tabLayoutViewMoneyRequests.getChildAt(0)).getChildAt(0)).getChildAt(1));
        text.setTypeface(null, Typeface.BOLD);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMoneyRequestsPresenter.bind(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        mMoneyRequestsPresenter.unbind();
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
    }

    @Override
    public void showError(String error) {
        showError(getString(R.string.error), error);
    }


    @Override
    public void onBackPressed() {
        mMoneyRequestsPresenter.navigateToDashboardScreen();
    }
}
