/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.overview;


import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.dashboard.DashboardCardType;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.model.request.DashboardFeatureVisibilityData;
import za.co.nedbank.core.domain.repository.UserInfoRepository;

public class GetDashboardOrderUseCase extends UseCase<DashboardFeatureVisibilityData, List<DashboardCardType>> {

    private final UserInfoRepository userInfoRepository;

    @Inject
    protected GetDashboardOrderUseCase(final UseCaseComposer useCaseComposer, final UserInfoRepository userInfoRepository) {
        super(useCaseComposer);
        this.userInfoRepository = userInfoRepository;
    }

    @Override
    protected Observable<List<DashboardCardType>> createUseCaseObservable(DashboardFeatureVisibilityData dashboardFeatureVisibilityData) {
        return userInfoRepository.getDashboardOrder(dashboardFeatureVisibilityData);
    }
}
