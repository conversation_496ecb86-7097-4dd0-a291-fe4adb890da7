package za.co.nedbank.ui.notifications.martec;

import android.app.PendingIntent;
import android.app.TaskStackBuilder;
import android.content.Context;
import android.content.Intent;

import com.adobe.marketing.mobile.Messaging;
import com.adobe.marketing.mobile.util.StringUtils;

import java.util.Random;

import za.co.nedbank.ui.splashscreen.SplashScreenActivity;

/**
 * Class for building push notification.
 * <p>
 * The build method in this class takes {@link AjoPushPayloadDataModel} received from the push notification and builds the notification.
 * This class is used internally by MessagingService to build the push notification.
 */
public class MessagingPushBuilder {

    private static final Random random = new Random();

    private MessagingPushBuilder() {
        // Nothing to do for now
    }

    /**
     * Creates a pending intent for the notification.
     *
     * @param payload            {@link AjoPushPayloadDataModel} the payload received from the push notification
     * @param context            the application {@link Context}
     * @param notificationAction the notification action
     * @param actionUri          the action uri
     * @param actionID           the action ID
     * @return the pending intent
     */
    public static PendingIntent createPendingIntent(final AjoPushPayloadDataModel payload,
                                                    final Context context,
                                                    final String notificationAction,
                                                    final String actionUri,
                                                    final String actionID) {
        final Intent intent = new Intent(notificationAction);
        intent.setClass(context.getApplicationContext(), SplashScreenActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
        addActionDetailsToIntent(intent, actionUri, actionID);
        Messaging.addPushTrackingDetails(intent, payload.getMessageId(), payload.getData());

        int rValue = random.nextInt();
        // adding tracking details
        return TaskStackBuilder.create(context)
                .addNextIntentWithParentStack(intent)
                .getPendingIntent(rValue, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
    }

    /**
     * Adds action details to the intent.
     *
     * @param intent    the intent
     * @param actionUri the action uri
     * @param actionId  the action ID
     */
    private static void addActionDetailsToIntent(final Intent intent, final String actionUri, final String actionId) {
        if (!StringUtils.isNullOrEmpty(actionUri)) {
            intent.putExtra(MessagingConstants.Tracking.Keys.ACTION_URI, actionUri);
        }

        if (!StringUtils.isNullOrEmpty(actionId)) {
            intent.putExtra(MessagingConstants.Tracking.Keys.ACTION_ID, actionId);
        }
    }
}
