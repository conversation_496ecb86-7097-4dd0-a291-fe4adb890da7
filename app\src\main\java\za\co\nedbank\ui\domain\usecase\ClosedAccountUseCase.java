package za.co.nedbank.ui.domain.usecase;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.VoidUseCase;
import za.co.nedbank.ui.domain.mapper.ClosedAccountResponseEntityToDataMapper;
import za.co.nedbank.ui.domain.model.closed_account.ClosedAccountResponseData;
import za.co.nedbank.ui.domain.repository.closed_account.IClosedAccountsRepository;

public class ClosedAccountUseCase extends VoidUseCase<ClosedAccountResponseData> {
    private final IClosedAccountsRepository mRepository;
    private final ClosedAccountResponseEntityToDataMapper mClosedAccountResponseEntityToDataMapper;

    @Inject
    protected ClosedAccountUseCase(final UseCaseComposer useCaseComposer,
                                   final IClosedAccountsRepository repository,
                                   final ClosedAccountResponseEntityToDataMapper ClosedAccountResponseEntityToDataMapper) {
        super(useCaseComposer);
        mRepository = repository;
        this.mClosedAccountResponseEntityToDataMapper = ClosedAccountResponseEntityToDataMapper;
    }

    @Override
    protected Observable<ClosedAccountResponseData> createUseCaseObservable() {
        return mRepository.getClosedAccounts().map(mClosedAccountResponseEntityToDataMapper::mapData);
    }
}
