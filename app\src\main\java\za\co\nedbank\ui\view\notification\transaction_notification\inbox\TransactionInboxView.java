package za.co.nedbank.ui.view.notification.transaction_notification.inbox;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.base.adapter.SectionAdapterItem;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;

public interface TransactionInboxView extends NBBaseView {

    void showProgress(boolean isVisible);

    void markReadMessage(FBTransactionNotificationsViewModel model, boolean isRead);

    void changeEmptyView();

    void showListLoaded();

    void updateNotificationMessages(List<FBTransactionNotificationsViewModel> items);

    void updateTransactionNotificationPageData(List<Object> items);

    void onNotificationReceived(Object notificationModal);

    void showSelected();

    void restoreDeletedItems();

    void onSearchFilter(List<FBTransactionNotificationsViewModel> transactions);

    void showUndoDeleteOption(int noOfDeletedMessages);

    void removeDeletedItem(SectionAdapterItem<FBTransactionNotificationsViewModel> notificationsViewModel);

    void clearDeletedItems();

    void endSelection();

    void onSearchFilter(List<FBTransactionNotificationsViewModel> filterList, String sortParameter);

    void updateNotificationMessagesPageData(List<FBTransactionNotificationsViewModel> items);

    void showErrorForNotificationMessages(String message);

    String getDeleteErrorMsg();

    void showGenericError();

    boolean isUndoTimerComplete();

    void setDeleteSuccess(boolean isDeleteSuccess);

    boolean isDeleteSuccess();
}