package za.co.nedbank.ui.view.retention;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import za.co.nedbank.databinding.RowMultipleShareAccountBinding;
import za.co.nedbank.services.domain.model.account.AccountSummary;

public class MultipleAccountsShareAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private final List<AccountSummary> mAccountList;
    private final IItemClickCallback itemClickCallback;

    MultipleAccountsShareAdapter(List<AccountSummary> mAccountList, IItemClickCallback itemClickCallback) {
        this.mAccountList = mAccountList;
        this.itemClickCallback = itemClickCallback;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        RowMultipleShareAccountBinding binding = RowMultipleShareAccountBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new MultipleAccountsShareViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof MultipleAccountsShareViewHolder) {
            MultipleAccountsShareViewHolder accountTypeViewHolder = (MultipleAccountsShareViewHolder) holder;
            accountTypeViewHolder.mTvAccountName.setText(mAccountList.get(position).getName());
            accountTypeViewHolder.mAccountNo.setText(mAccountList.get(position).getNumber());
            accountTypeViewHolder.position = holder.getAdapterPosition();
        }
    }

    @Override
    public int getItemCount() {
        return mAccountList == null ? 0 : mAccountList.size();
    }

    interface IItemClickCallback {
        void onItemClick(int position, AccountSummary mAccount);
    }

    class MultipleAccountsShareViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        TextView mTvAccountName;
        TextView mAccountNo;
        int position;

        MultipleAccountsShareViewHolder(RowMultipleShareAccountBinding binding) {
            super(binding.getRoot());
            binding.getRoot().setOnClickListener(this);
            mTvAccountName = binding.tvAccountName;
            mAccountNo = binding.tvAccountNo;
        }

        @Override
        public void onClick(View v) {
            if (null != itemClickCallback) {
                itemClickCallback.onItemClick(position, mAccountList.get(position));
            }
        }
    }
}
