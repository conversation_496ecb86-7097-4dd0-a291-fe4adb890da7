/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients.choose_recipient;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

import za.co.nedbank.core.view.recipient.UserBeneficiaryDetailsViewModel;

/**
 * Created by charurani on 23-08-2017.
 */

public class ChooseRecipientsViewModel implements Parcelable{

    private List<UserBeneficiaryDetailsViewModel> bfDetails;
    private int mCurrentBeneficiarySelection;
    private String mCurrentBeneficiaryName;

    public ChooseRecipientsViewModel(){
        this.bfDetails = new ArrayList<>();
    }

    private ChooseRecipientsViewModel(Parcel in) {
        bfDetails = in.createTypedArrayList(UserBeneficiaryDetailsViewModel.CREATOR);
        mCurrentBeneficiarySelection = in.readInt();
        mCurrentBeneficiaryName = in.readString();
    }

    public static final Creator<ChooseRecipientsViewModel> CREATOR = new Creator<ChooseRecipientsViewModel>() {
        @Override
        public ChooseRecipientsViewModel createFromParcel(Parcel in) {
            return new ChooseRecipientsViewModel(in);
        }

        @Override
        public ChooseRecipientsViewModel[] newArray(int size) {
            return new ChooseRecipientsViewModel[size];
        }
    };

    public List<UserBeneficiaryDetailsViewModel> getBfDetails() {
        return bfDetails;
    }

    public void setBfDetails(List<UserBeneficiaryDetailsViewModel> bfDetails) {
        this.bfDetails = bfDetails;
    }

    public int getCurrentBeneficiarySelection() {
        return mCurrentBeneficiarySelection;
    }

    public void setCurrentBeneficiarySelection(int currentBeneficiarySelection) {
        mCurrentBeneficiarySelection = currentBeneficiarySelection;
    }

    public String getCurrentBeneficiaryName() {
        return mCurrentBeneficiaryName;
    }

    public void setCurrentBeneficiaryName(String currentBeneficiaryName) {
        mCurrentBeneficiaryName = currentBeneficiaryName;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeTypedList(bfDetails);
        parcel.writeInt(mCurrentBeneficiarySelection);
        parcel.writeString(mCurrentBeneficiaryName);
    }
}
