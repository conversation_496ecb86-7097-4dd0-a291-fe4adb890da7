<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="100dp"
    android:height="100dp"
    android:viewportWidth="100"
    android:viewportHeight="100">
  <path
      android:pathData="M-138,-120h360v640h-360z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M72.069,59.937C72.069,56.721 74.676,54.115 77.891,54.115L86.354,54.115L86.354,54.115L86.354,65.76L77.891,65.76C74.676,65.76 72.069,63.153 72.069,59.937Z"
      android:strokeWidth="1"
      android:fillColor="#E4F2D2"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M28.265,31.155m-18.92,0a18.92,18.92 0,1 1,37.84 0a18.92,18.92 0,1 1,-37.84 0"
      android:strokeWidth="1"
      android:fillColor="#E4F2D2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M6.667,9.333C8.692,9.333 10.333,7.692 10.333,5.667C10.333,3.642 8.692,2 6.667,2C4.642,2 3,3.642 3,5.667C3,7.692 4.642,9.333 6.667,9.333ZM6.667,7.333C5.746,7.333 5,6.587 5,5.667C5,4.746 5.746,4 6.667,4C7.587,4 8.333,4.746 8.333,5.667C8.333,6.587 7.587,7.333 6.667,7.333Z"
      android:strokeWidth="1"
      android:fillColor="#99D5B0"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M8.5,89C9.052,89 9.5,89.448 9.5,90L9.5,90L9.499,91.5L11,91.5C11.513,91.5 11.936,91.886 11.993,92.383L12,92.5C12,93.052 11.552,93.5 11,93.5L11,93.5L9.499,93.5L9.5,95C9.5,95.513 9.114,95.936 8.617,95.993L8.5,96C7.948,96 7.5,95.552 7.5,95L7.5,95L7.499,93.5L6,93.5C5.487,93.5 5.064,93.114 5.007,92.617L5,92.5C5,91.948 5.448,91.5 6,91.5L6,91.5L7.499,91.5L7.5,90C7.5,89.487 7.886,89.064 8.383,89.007Z"
      android:strokeWidth="1"
      android:fillColor="#B2E0C4"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M93.5,3C94.052,3 94.5,3.448 94.5,4L94.5,4L94.499,5.5L96,5.5C96.513,5.5 96.936,5.886 96.993,6.383L97,6.5C97,7.052 96.552,7.5 96,7.5L96,7.5L94.499,7.5L94.5,9C94.5,9.513 94.114,9.936 93.617,9.993L93.5,10C92.948,10 92.5,9.552 92.5,9L92.5,9L92.499,7.5L91,7.5C90.487,7.5 90.064,7.114 90.007,6.617L90,6.5C90,5.948 90.448,5.5 91,5.5L91,5.5L92.499,5.5L92.5,4C92.5,3.487 92.886,3.064 93.383,3.007Z"
      android:strokeWidth="1"
      android:fillColor="#B2E0C4"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M92,98C94.761,98 97,95.761 97,93C97,90.239 94.761,88 92,88C89.239,88 87,90.239 87,93C87,95.761 89.239,98 92,98ZM92,96C90.343,96 89,94.657 89,93C89,91.343 90.343,90 92,90C93.657,90 95,91.343 95,93C95,94.657 93.657,96 92,96Z"
      android:strokeWidth="1"
      android:fillColor="#7FCB9C"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M83.007,36.007L83.006,53.114L86.354,53.115C86.907,53.115 87.354,53.562 87.354,54.115L87.354,65.76C87.354,66.312 86.907,66.76 86.354,66.76L83.006,66.759L83.007,84.379L23.964,84.379C20.246,84.379 17.27,81.626 17.145,78.003L17.141,77.765L17.141,52.745C17.141,52.192 17.589,51.745 18.141,51.745C18.654,51.745 19.077,52.131 19.135,52.628L19.141,52.745L19.141,77.765C19.141,80.304 21.132,82.267 23.739,82.374L23.964,82.379L81.007,82.379L81.006,66.759L77.891,66.76C74.123,66.76 71.069,63.705 71.069,59.937C71.069,56.169 74.123,53.115 77.891,53.115L81.006,53.114L81.007,38.007L50.297,38.007C49.745,38.007 49.297,37.56 49.297,37.007C49.297,36.495 49.683,36.072 50.18,36.014L50.297,36.007L83.007,36.007ZM85.354,55.115L77.891,55.115C75.228,55.115 73.069,57.274 73.069,59.937C73.069,62.6 75.228,64.76 77.891,64.76L85.354,64.76L85.354,55.115ZM28.265,11.235C39.266,11.235 48.185,20.153 48.185,31.155C48.185,42.156 39.266,51.075 28.265,51.075C22.643,51.075 17.565,48.746 13.943,45L8.5,45C7.948,45 7.5,44.552 7.5,44C7.5,43.487 7.886,43.064 8.383,43.007L8.5,43L12.248,43.001C11.552,42.061 10.936,41.057 10.412,40.001L7.5,40C6.948,40 6.5,39.552 6.5,39C6.5,38.487 6.886,38.064 7.383,38.007L7.5,38L9.553,38.001C8.771,35.866 8.345,33.56 8.345,31.155C8.345,20.153 17.263,11.235 28.265,11.235ZM28.265,13.235C18.368,13.235 10.345,21.258 10.345,31.155C10.345,41.052 18.368,49.075 28.265,49.075C38.162,49.075 46.185,41.052 46.185,31.155C46.185,21.258 38.162,13.235 28.265,13.235ZM5.001,43C5.553,43 6.001,43.448 6.001,44C6.001,44.552 5.553,45 5.001,45L5.001,45L4.001,45C3.448,45 3.001,44.552 3.001,44C3.001,43.448 3.448,43 4.001,43L4.001,43ZM35.781,25.108C36.172,25.435 36.249,25.998 35.979,26.415L35.905,26.516L25.836,38.515L20.882,34.359C20.459,34.004 20.404,33.373 20.759,32.95C21.087,32.56 21.65,32.483 22.067,32.752L22.168,32.827L25.59,35.698L34.372,25.231C34.727,24.808 35.358,24.753 35.781,25.108ZM82.189,27.274C82.741,27.274 83.189,27.721 83.189,28.274C83.189,28.787 82.803,29.209 82.306,29.267L82.189,29.274L50.832,29.274C50.28,29.274 49.832,28.826 49.832,28.274C49.832,27.761 50.218,27.338 50.716,27.28L50.832,27.274L82.189,27.274ZM82.189,18.32C82.741,18.32 83.189,18.768 83.189,19.32C83.189,19.833 82.803,20.256 82.306,20.313L82.189,20.32L48.24,20.32C47.688,20.32 47.24,19.872 47.24,19.32C47.24,18.807 47.626,18.384 48.123,18.327L48.24,18.32L82.189,18.32Z"
      android:strokeWidth="1"
      android:fillColor="#009639"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
</vector>
