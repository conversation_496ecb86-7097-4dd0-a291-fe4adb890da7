package za.co.nedbank.ui.view.retention.notification_journey;


import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;

import androidx.core.text.HtmlCompat;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.ActivityRetentionFlowsInformationBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.retention.RetentionConstants;
import za.co.nedbank.uisdk.component.CompatTextView;

public class RetentionInformationActivity extends NBBaseActivity implements RetentionInformationView {

    @Inject
    RetentionInformationPresenter mInformationPresenter;
    RetentionConstants.RetentionInfoType retentionInfoType;
    private ActivityRetentionFlowsInformationBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRetentionFlowsInformationBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        getBundleData(getIntent());
        mInformationPresenter.bind(this);
        mInformationPresenter.setHeaderAndInfoText();
        mInformationPresenter.sendPageEvent(retentionInfoType);
    }

    void getBundleData(Intent intent) {
        if (intent.hasExtra(RetentionConstants.RETENTION_INFO_TYPE)) {
            retentionInfoType = (RetentionConstants.RetentionInfoType) intent.getSerializableExtra(RetentionConstants.RETENTION_INFO_TYPE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mInformationPresenter.bind(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mInformationPresenter.unbind();
    }

    @Override
    public void setHeaderAndInfoText() {
        String header = StringUtils.EMPTY_STRING;
        switch (retentionInfoType) {
            case SHARE_ACCOUNT:
                header = getString(R.string.retention_information_header_share_account);
                initToolbar(binding.toolbar, true, getString(R.string.share_account_info_title));
                setInfoText(getString(R.string.retention_information_detail_share_account));
                break;
            case LOGIN_SECURITY:
                header = getString(R.string.retention_information_header_login_security);
                initToolbar(binding.toolbar, true, getString(R.string.more_login_and_security));
                setInfoText(getString(R.string.retention_information_detail_login_security));
                break;
            case PROFILE_LIMIT:
                header = getString(R.string.retention_information_header_profile_limit);
                initToolbar(binding.toolbar, true, getString(R.string.more_profile_limits));
                setInfoText(getString(R.string.retention_information_detail_profile_limit));
                break;
            case DEBIT_ORDER_SWITCHING:
                header = getString(R.string.retention_information_header_debit_order);
                initToolbar(binding.toolbar, true, getString(R.string.debit_order_switching_title));
                setInfoText(getString(R.string.retention_information_detail_debit_order_switching));
                break;
            default:
                break;
        }
        if (getSupportActionBar() != null) {
            getSupportActionBar().setHomeAsUpIndicator(ViewUtils.changeBackArrowColor(this, R.color.black_color));
        }
        binding.retInfoHeaderTextTv.setText(HtmlCompat.fromHtml(header, HtmlCompat.FROM_HTML_MODE_LEGACY));
    }


    public void setInfoText(String infoDetail) {
        String[] dataArray = infoDetail.split("\n");
        if (dataArray != null && dataArray.length > 0) {
            for (int i = 0; i < dataArray.length; i++) {
                View infoLayout = LayoutInflater.from(this).inflate(R.layout.layout_retention_info_view, binding.retInfoLl, false);
                LinearLayout infoLL = infoLayout.findViewById(R.id.infoLL);
                CompatTextView retInfoNumberLabelTV = infoLayout.findViewById(R.id.retInfoNumberLabelTV);
                CompatTextView retInfoLabelTV = infoLayout.findViewById(R.id.retInfoLabelTV);
                retInfoNumberLabelTV.setText("" + (i + 1) + StringUtils.DOT);
                retInfoLabelTV.setText(HtmlCompat.fromHtml(dataArray[i], HtmlCompat.FROM_HTML_MODE_LEGACY));
                infoLL.setContentDescription("" + (i + 1) + StringUtils.NEW_LINE + HtmlCompat.fromHtml(dataArray[i], HtmlCompat.FROM_HTML_MODE_LEGACY).toString());
                binding.retInfoLl.addView(infoLayout);
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(final MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            mInformationPresenter.sendBackArrowAnalytics(retentionInfoType);
            super.onOptionsItemSelected(item);
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        mInformationPresenter.sendBackArrowAnalytics(retentionInfoType);
        super.onBackPressed();
    }

}
