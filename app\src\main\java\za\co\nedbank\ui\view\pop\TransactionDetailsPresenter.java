package za.co.nedbank.ui.view.pop;

import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_SHOW_INTERNATIONAL_ACCOUNT_TILE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_SHOW_PAY_TO_BUSINESS;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_WHICH_IP_FLOW;
import static za.co.nedbank.core.payment.recent.Constants.ERROR_HEADER_REFERENCE_PAYMENTS;
import static za.co.nedbank.payment.internationalpayment.Constants.CLIENT_NUMBER_TYPE;

import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.profile.UserProfile;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.profile.GetMdmProfileUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.payment.recent.NotificationDetailsViewModel;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseDataToViewModelMapper;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseDetailsViewModel;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.tracking.appsflyer.AddContextData;
import za.co.nedbank.core.tracking.appsflyer.AppsFlyerTags;
import za.co.nedbank.core.utils.CMAUtils;
import za.co.nedbank.core.utils.FatcaRestrictionUtil;
import za.co.nedbank.core.utils.LocUtils;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;
import za.co.nedbank.core.view.model.TransactionsNotificationDetailsViewModel;
import za.co.nedbank.payment.common.view.tracking.PaymentsTrackingEvent;
import za.co.nedbank.payment.internationalpayment.domain.model.request.RequestHeaderDataModel;
import za.co.nedbank.payment.internationalpayment.domain.usecase.GetForexClientDetailUseCase;
import za.co.nedbank.payment.internationalpayment.view.IPType;
import za.co.nedbank.payment.internationalpayment.view.mapper.AddressOTTDataToViewMapper;
import za.co.nedbank.payment.pay.domain.usecase.RecentPaymentDetailUseCase;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class TransactionDetailsPresenter extends NBBasePresenter<TransactionDetailsView> {

    private final NavigationRouter mNavigationRouter;
    private final FeatureSetController mFeatureSetController;
    private final Analytics mAnalytics;
    private final RecentPaymentDetailUseCase mRecentPaymentDetailUseCase;
    private final RecentPaymentResponseDataToViewModelMapper mRecentPaymentResponseDataToViewModelMapper;
    private final ApplicationStorage inMemoryStorage;
    private final AFAnalyticsTracker mAfAnalyticsTracker;
    private final GetMdmProfileUseCase mGetMdmProfileUseCase;
    private final GetForexClientDetailUseCase mGetForexClientDetailInfoUseCase;
    private final AddressOTTDataToViewMapper mAddressOTTDataToViewMapper;

    @Inject
    TransactionDetailsPresenter(final NavigationRouter navigationRouter, final FeatureSetController featureSetController, final Analytics analytics,
                                RecentPaymentDetailUseCase mRecentPaymentDetailUseCase, RecentPaymentResponseDataToViewModelMapper mRecentPaymentResponseDataToViewModelMapper,
                                @Named("memory") ApplicationStorage inMemoryStorage, final AFAnalyticsTracker afAnalyticsTracker,  AddressOTTDataToViewMapper mAddressOTTDataToViewMapper ,
                                GetMdmProfileUseCase getMdmProfileUseCase, GetForexClientDetailUseCase mGetForexClientDetailInfoUseCase) {
        this.mNavigationRouter = navigationRouter;
        this.mFeatureSetController = featureSetController;
        this.mAnalytics = analytics;
        this.mRecentPaymentDetailUseCase = mRecentPaymentDetailUseCase;
        this.mRecentPaymentResponseDataToViewModelMapper = mRecentPaymentResponseDataToViewModelMapper;
        this.inMemoryStorage = inMemoryStorage;
        this.mAfAnalyticsTracker = afAnalyticsTracker;
        this.mGetMdmProfileUseCase = getMdmProfileUseCase;
        this.mAddressOTTDataToViewMapper = mAddressOTTDataToViewMapper;
        this.mGetForexClientDetailInfoUseCase = mGetForexClientDetailInfoUseCase;
    }

    void navigateToSharePOPScreen(TransactionHistoryViewModel transactionHistoryViewModel, boolean isFromRecentPaymentFlow, boolean isNavigateToOverView, boolean isBeyond90Days) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(AppTracking.VAL_FEATURE_CATEGORY_SHARE_POP);
        adobeContextData.setFeature(AppTracking.VAL_FEATURE_SHARE_POP);
        if (isBeyond90Days) {
            mAnalytics.sendEventActionWithMap(AppTracking.CLICK_SHARE_POP_BEYOND_90DAYS, cdata);
        } else if (isFromRecentPaymentFlow && !isNavigateToOverView) {
            mAnalytics.sendEventActionWithMap(AppTracking.CLICK_SHARE_POP_PAY_FLOW, cdata);
        } else if (isNavigateToOverView) {
            mAnalytics.sendEventActionWithMap(AppTracking.OVERVIEW_SHARE_POP_ONCE_OFF, cdata);
        } else {
            mAnalytics.sendEventActionWithMap(AppTracking.CLICK_SHARE_POP, cdata);
        }
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.NEW_SHARE_PROOF_OF_PAYMENT_METHOD);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.TRANSACTION_HISTORY_VIEW_MODEL, transactionHistoryViewModel);
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_RECENT_PAYMENT_FLOW, isFromRecentPaymentFlow);
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_BEYOND_90_DAYS_TRANSACTION, isBeyond90Days);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_LANDING_ON_OVERVIEW, isNavigateToOverView);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_FROM_TRANSACTION_DETAILS, true);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void handleButtonVisibility() {
        boolean isPayAgainDisable = mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_PAY_AGAIN_FLOW)
                || FatcaRestrictionUtil.isFatcaRestrictionsApplicable(inMemoryStorage, mFeatureSetController);
        boolean isSharePOPBeyond90Disable = mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_SHARE_POP_BEYOND90);

        if (view != null) {
            view.handleButtonVisibility(isPayAgainDisable, view.isBeyond90Days(), isSharePOPBeyond90Disable);
        }
    }

    protected void startPaymentTime(long timestamp) {
        inMemoryStorage.putLong(StorageKeys.PAYMENT_FLOW_START_TIME, timestamp);
    }

    void navigateToPaymentAmount(TransactionHistoryViewModel transactionHistoryViewModel, boolean isOnceOffFromTransaction) {
        if (transactionHistoryViewModel != null) {

            transactionHistoryViewModel.setUserAccountType(view.getAccountType(transactionHistoryViewModel));

            long timestamp = System.currentTimeMillis() / 1000;
            startPaymentTime(timestamp);

            HashMap<String, Object> cdata = new HashMap<>();
            AdobeContextData adobeContextData = new AdobeContextData(cdata);
            adobeContextData.setStep1();
            adobeContextData.setInitiations();
            adobeContextData.setStepName(PaymentsTrackingEvent.VAL_STEP_NAME_RECIPIENT_SELECTION);
            adobeContextData.setToAccount(view.getDataToAccountName());
            adobeContextData.setFeature(TrackingParam.VAL_FEATURE_PAY_AGAIN);
            adobeContextData.setContext2(view.fetchRecipientType());

            if (transactionHistoryViewModel.isFromRecentPayment() && transactionHistoryViewModel.getBeneficiaryID() == 0) {
                mAnalytics.sendEventActionWithMap(AppTracking.PAY_AGAIN_NEW_ONCE_OFF_PAYMENTS, cdata);
            } else if (transactionHistoryViewModel.isFromRecentPayment() && transactionHistoryViewModel.getBeneficiaryID() != 0) {
                mAnalytics.sendEventActionWithMap(AppTracking.PAY_AGAIN_PAY_FLOW_EXISTING_RECIPIENTS, cdata);
            } else {
                mAnalytics.sendEventActionWithMap(AppTracking.CLICK_PAY_AGAIN, cdata);
            }

            if (transactionHistoryViewModel.isFromOverViewOnceOff()) {
                mAnalytics.sendEventActionWithMap(AppTracking.OVERVIEW_PAY_AGAIN_ONCE_OFF, cdata);
                transactionHistoryViewModel.setBeneficiaryType(null);
            }

            HashMap<String, Object> appsFlyerContextMap = new HashMap<>();
            AddContextData addContextData = new AddContextData(appsFlyerContextMap);
            addContextData.setAccountType(transactionHistoryViewModel.getUserAccountType());
            mAfAnalyticsTracker.sendEvent(AppsFlyerTags.AF_PAYMENT_INITIATION, appsFlyerContextMap);

            transactionHistoryViewModel.setFromPayAgainFlow(true);
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.PAY_AMOUNT);
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_ONCE_OFF_FROM_TRANSACTIONS, isOnceOffFromTransaction);
            navigationTarget.withParam(Constants.BUNDLE_KEYS.TRANSACTION_HISTORY_VIEW_MODEL, transactionHistoryViewModel);
            mNavigationRouter.navigateTo(navigationTarget);
        }
    }


    void checkFeaturePayAgainRecentTransaction() {
        boolean isPayAgainRecentTransactionDisable = mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_PAY_AGAIN_RECENT_PAYMENT)
                || FatcaRestrictionUtil.isFatcaRestrictionsApplicable(inMemoryStorage, mFeatureSetController);
        if (view != null) {
            view.handleButtonVisibility(isPayAgainRecentTransactionDisable, false, false);
        }
    }

    void fetchProfileForInternationalPaymentTnxFlow() {
        fetchProfileForInternationalPaymentTnxFlow(null);
    }
    void fetchProfileForInternationalPaymentTnxFlow(String entryPoint) {
        Object obj = inMemoryStorage.getObject(StorageKeys.MDM_USER_PROFILE);
        if (obj == null) {
            mGetMdmProfileUseCase.execute()
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> handleProgressBar(true))
                    .doOnTerminate(() -> handleProgressBar(false))
                    .subscribe(profile -> {
                        if (profile != null) {
                            inMemoryStorage.putObject(StorageKeys.MDM_USER_PROFILE, profile);
                            inMemoryStorage.putString(StorageKeys.MDM_CIS_NUMBER, profile.getCisNumber());
                            getForexDetailInfoForTnxFlow(profile.getCisNumber(),entryPoint);
                        }
                    }, e -> {
                        if (view != null) {
                            view.errorSnackBar();
                        }
                    });
        } else {
            if (obj instanceof UserProfile) {
                getForexDetailInfoForTnxFlow(((UserProfile) obj).getCisNumber(),entryPoint);
            }
        }
    }

    private void handleProgressBar(boolean isVisible) {
        if (view != null) {
            view.handleProgressBar(isVisible);
        }
    }

    void getRecentTransactionDetails(long transactionId) {
        mRecentPaymentDetailUseCase.execute(transactionId)
                .compose(bindToLifecycle()).doOnSubscribe(disposable -> {
            if (null != view) {
                view.handleProgressBar(true);
            }
        }).subscribe(recentPaymentResponseData -> {
            if (view != null) {
                view.handleProgressBar(false);
                if (recentPaymentResponseData != null && recentPaymentResponseData.getData() != null) {
                    RecentPaymentResponseDetailsViewModel recentPaymentResponseDetailsViewModel = mRecentPaymentResponseDataToViewModelMapper.mapDetailData(recentPaymentResponseData);
                    view.setTransactionDetailData(recentPaymentResponseDetailsViewModel);
                } else {
                    view.errorSnackBar();
                }
            }
        }, throwable -> {
            if (view != null) {
                view.handleProgressBar(false);
                view.errorSnackBar();
            }

        });

    }

    public List<TransactionsNotificationDetailsViewModel> buildNotificationModel(List<NotificationDetailsViewModel> notificationDetailsViewModels) {
        return mRecentPaymentResponseDataToViewModelMapper.mapNotificationListViewModelToTransactionsViewModel(notificationDetailsViewModels);
    }

    public void checkLocForOnceOff() {
        boolean isLocNotAllowed = !LocUtils.isTransactionApproved(inMemoryStorage, LocUtils.LOC_TRANSACTION_TYPE.ONCE_OFF_PAYMENTS, mFeatureSetController);
        if (isLocNotAllowed) {
            navigateToLocErrorForOnceOff(ERROR_HEADER_REFERENCE_PAYMENTS, LocUtils.LOC_TRANSACTION_TYPE.ONCE_OFF_PAYMENTS);
        } else {
            if (view != null) {
                view.handlePayAgainClick();
            }
        }
    }

    private void navigateToLocErrorForOnceOff(String errorHeader, String transactionType) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.LOC_ERROR_SCREEN);
        if (view != null) {
            navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_OVERVIEW_TRANSACTION_ONCEOFF_FLOW, view.isOnceOffFromOverViewTransaction());
        }
        navigationTarget.withParam(za.co.nedbank.core.Constants.LOC_ERROR_HEADER, errorHeader);
        navigationTarget.withParam(za.co.nedbank.core.Constants.LOC_TRANSACTION_TYPE, transactionType);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    public void navigateToInternationalPayment(String entryPoint) {
        inMemoryStorage.putBoolean(StorageKeys.FOREX_IS_PAY_FRM_RECIPIENT, true);
        inMemoryStorage.putBoolean(StorageKeys.FOREX_GO_TO_HOME, true);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.INTERNATIONAL_PAYMENT_ACCOUNT_SELECTION)
                .withParam(PARAM_EXTRA_WHICH_IP_FLOW, IPType.CMA)
                .withParam(PARAM_EXTRA_SHOW_PAY_TO_BUSINESS, true)
                .withParam(NavigationTarget.ENTRY_POINT_FOR_INTERNATION_PAYMENT, entryPoint)
                .withParam(PARAM_EXTRA_SHOW_INTERNATIONAL_ACCOUNT_TILE, false));
    }

    void getForexDetailInfoForTnxFlow(String cisNumber,String entryPoint) {
        Object obj = inMemoryStorage.getObject(StorageKeys.FOREX_CLIENT_DETAIL);
        if (obj == null) {
            RequestHeaderDataModel headerDataModel = new RequestHeaderDataModel();
            headerDataModel.setChannelCode(za.co.nedbank.payment.internationalpayment.Constants.CHANNEL_CODE);
            headerDataModel.setClientNumber(cisNumber);
            headerDataModel.setClientNumberType(CLIENT_NUMBER_TYPE);
            mGetForexClientDetailInfoUseCase.execute(headerDataModel)
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> handleProgressBar(true))
                    .doOnTerminate(() -> handleProgressBar(false))
                    .subscribe(addressOTTModel -> {
                        if (addressOTTModel != null) {
                            inMemoryStorage.putObject(StorageKeys.FOREX_CLIENT_DETAIL, mAddressOTTDataToViewMapper.map(addressOTTModel));
                            navigateToInternationalPayment(entryPoint);
                        }
                    }, e -> {
                        if (view != null) {
                            view.errorSnackBar();
                        }
                    });
        } else {
            navigateToInternationalPayment(entryPoint);
        }
    }

    public void trackCmaActionOnContinueClick() {
        HashMap<String, Object> cdata = new HashMap<>();
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.CMA_POPUP_CONTINUE, cdata);
    }
    public void trackCmaActionOnCancelClick() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setContext1Count();
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.CMA_POPUP_CANCEL, cdata);
    }
    public void trackCmaActionOnOpen(String bankName) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setContext1(bankName);
        mAnalytics.sendEventActionWithMap(PaymentsTrackingEvent.CMA_POPUP_OPEN, cdata);
    }

    public void checkForCmaFlow(String branchCode, String bankName){
        if (view == null) {
            return;
        }
        if  (CMAUtils.isCMABranch(mFeatureSetController,branchCode)) {
            view.openPopForInterNationalPayment(bankName);
        } else {
            view.checkRecentPaymentTransaction();
        }
    }


}