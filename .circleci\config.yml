version: 2

references:
  gradle_options: &gradle_options
                    '-Dorg.gradle.jvmargs="-Xmx3072m -XX:+HeapDumpOnOutOfMemoryError -XX:MaxPermSize=512m"'
  gradle_cache_key: &gradle_cache_key
                      jars-{{ checksum "build.gradle" }}-{{ checksum  "app/build.gradle" }}
  save_gradle_cache: &save_gradle_cache
    save_cache:
      paths:
        - ~/.gradle
      key: *gradle_cache_key
  restore_gradle_cache: &restore_gradle_cache
    restore_cache:
      key: *gradle_cache_key
  run_unit_tests: &run_unit_tests
    run:
      name: Run Test
      command: ./gradlew test --console=plain
  run_coverage: &run_coverage
    run:
      name: Run Coverage
      command: |
        ./gradlew jacocoTestReport --console=plain
        ./.circleci/handleCoverage.sh
  run_coverage_verification: &run_coverage_verification
    run:
      name: Run Test Coverage Verification
      command: ./gradlew jacocoTestCoverageVerificationdebug
  save_workspace_artifacts: &save_workspace_artifacts
    store_artifacts:
      path: outputs/outputs/apk
  attach_workspace_artifacts: &attach_workspace_artifacts
    attach_workspace:
      at: outputs
  save_artifacts: &save_artifacts
    store_artifacts:
      path: app/build/reports
      destination: reports
    store_artifacts:
      path: app/build/outputs
      destination: apk
  save_test_results: &save_test_results
    store_test_results:
      path: app/build/test-results
  persist_workspace: &persist_workspace
    persist_to_workspace:
      root: app/build
      paths:
        - outputs
  accept_android_sdk_licence: &accept_android_sdk_licence
    run: yes | /opt/android/sdk/tools/bin/sdkmanager --licenses || exit 0


jobs:
  build_prod:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Inhouse build
          command: ./gradlew -PreleaseStorePass="${STORE_PASS}" -PreleaseKeyPass="${KEY_PASS}" -PentersektServiceId="90da353b-515c-49b4-af14-fdec46c60848" assembleRelease --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_qa:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create QA build
          # QA SERVICE_ID: 5d444578-b657-4f93-89f9-ba1404085b11
          # DEV SERVICE_ID: 8262bc73-1a53-45ce-963a-6254e315fdd9
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" assembleQa --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_bundle:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create QA Bundle
          # QA SERVICE_ID: 5d444578-b657-4f93-89f9-ba1404085b11
          # DEV SERVICE_ID: 8262bc73-1a53-45ce-963a-6254e315fdd9
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" bundleQa --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_ete:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create ETE build
          command: ./gradlew -PentersektServiceId="2d00d807-7e6b-4e9a-971b-f82e6ad052fc" assembleEte --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_qa_release:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create QA Release build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" assembleQaRelease --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_de:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Digital Enablement Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-de" -PlauncherIcon="@mipmap/ic_launcher_enroll" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_fica:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Fica Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-fica" -PlauncherIcon="@mipmap/ic_launcher_fica" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_investment:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Investment Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-invest" -PlauncherIcon="@mipmap/ic_launcher_investment" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_referrals:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Referrals Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-referrals" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_mae:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create MAE Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-mae" -PlauncherIcon="@mipmap/ic_launcher_enhancement" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_sba:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create SBA Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-sba" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_onlineSavings:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Home Loan Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-onlineSavings" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_homeloan:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Home Loan Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-homeloan" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_smartQing:
      resource_class: medium+
      docker:
        - image: circleci/android:api-27-alpha
      environment:
        GRADLE_OPTS: *gradle_options
      steps:
        - checkout
        - *restore_gradle_cache
        - *accept_android_sdk_licence
        - run:
            name: Create Home Loan Squad build
            command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-smartQing" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
        - *save_gradle_cache
        - *save_artifacts
        - *save_test_results
        - *persist_workspace

  build_squad_insurance_v1:
          resource_class: medium+
          docker:
            - image: circleci/android:api-27-alpha
          environment:
            GRADLE_OPTS: *gradle_options
          steps:
            - checkout
            - *restore_gradle_cache
            - *accept_android_sdk_licence
            - run:
                name: Create Home Loan Squad build
                command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-insurance_v1" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
            - *save_gradle_cache
            - *save_artifacts
            - *save_test_results
            - *persist_workspace

  build_feature_appbundle:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create App Bundle Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-appbundle" -PlauncherIcon="@mipmap/ic_launcher_enhancement" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_forex_mvp2:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create forex mvp2 Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-forex_mvp2" -PlauncherIcon="@mipmap/ic_launcher_enhancement" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_forex_itt:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create forex itt Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-forex_itt" -PlauncherIcon="@mipmap/ic_launcher_enhancement" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_ghost-v2:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Ghost v2 Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-ghost-v2" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_notifications:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Notifications Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-notifications" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_transaction-notification:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create transaction-notification Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-transaction-notification" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_lnr:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create LnR Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-lnr" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_loanoffers:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Loan Offers Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-loanoffers" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_ghost-pl-v2:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create Loan Offers V2 Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-ghost-pl-v2" -PlauncherIcon="@mipmap/ic_launcher_qa" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  build_squad_vas:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - run:
          name: Create VAS Squad build
          command: ./gradlew -PentersektServiceId="5d444578-b657-4f93-89f9-ba1404085b11" -PversionNameSuffix="-vas" -PlauncherIcon="@mipmap/ic_launcher_vas" assembleSquad --console=plain
      - *save_gradle_cache
      - *save_artifacts
      - *save_test_results
      - *persist_workspace

  unit_test:
    resource_class: medium+
    docker:
      - image: circleci/android:api-27-alpha
    environment:
      GRADLE_OPTS: *gradle_options
    steps:
      - checkout
      - *restore_gradle_cache
      - *accept_android_sdk_licence
      - *run_unit_tests
      - *save_gradle_cache
      - *run_coverage_verification
      - *run_coverage

  deploy_to_hockey_qa:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to Hockey
          command: ./deploy.sh 539914e75ca74b0789d2bc108635cdb5 outputs/outputs/apk/qa/app-qa.apk

  deploy_to_hockey_prod:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy Inhouse to Hockey
          command: ./deploy.sh 13a572d63d374477b785a5c1416672b8 outputs/outputs/apk/release/app-release.apk

  deploy_to_hockey_qa_release:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA Release version to Hockey
          command: ./deploy.sh 208d1e02ba23411aba147fbab336ea81 outputs/outputs/apk/qaRelease/app-qaRelease.apk

  deploy_to_hockey_ete:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy ETE version to Hockey
          command: ./deploy.sh 750063395e87429786325db81ac68372 outputs/outputs/apk/ete/app-ete.apk

  deploy_to_hockey_de:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to Squad Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk de

  deploy_to_hockey_fica:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to Fica Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk fica

  deploy_to_hockey_investment:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to Digital Conceirge Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk invest

  deploy_to_hockey_referrals:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to app referrals Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk referrals

  deploy_to_hockey_mae:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to mae Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk mae

  deploy_to_hockey_sba:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to ScanToPay Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk sba

  deploy_to_hockey_onlineSavings:
      docker:
        - image: circleci/node:latest
      steps:
        - checkout
        - *attach_workspace_artifacts
        # storing to be visible on api as an outcome of the last build
        - *save_workspace_artifacts
        - deploy:
            name: Deploy QA version to onlineSavings Hockey
            command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk onlineSavings

  deploy_to_hockey_homeloan:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to HomeLoan Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk homeloan

  deploy_to_hockey_insurance_v1:
      docker:
        - image: circleci/node:latest
      steps:
        - checkout
        - *attach_workspace_artifacts
        # storing to be visible on api as an outcome of the last build
        - *save_workspace_artifacts
        - deploy:
            name: Deploy QA version to Insurance_v1 Hockey
            command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk insurance_v1

  deploy_to_hockey_appbundle:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to App Bundle Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk appbundle

  deploy_to_hockey_forex_mvp2:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to Forex MVP2 Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk forex_mvp2

  deploy_to_hockey_forex_itt:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to Forex ITT Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk forex_itt

  deploy_to_hockey_ghost-v2:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to ghost-v2 Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk ghost-v2

  deploy_to_hockey_notifications:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to Notifications Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk notifications

  deploy_to_hockey_transaction-notification:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to transaction Notifications Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk transaction-notification

  deploy_to_hockey_lnr:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to LnR Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk lnr

  deploy_to_hockey_smartQing:
        docker:
          - image: circleci/node:latest
        steps:
          - checkout
          - *attach_workspace_artifacts
          # storing to be visible on api as an outcome of the last build
          - *save_workspace_artifacts
          - deploy:
              name: Deploy QA version to Smart queue Hockey
              command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk smartQing

  deploy_to_hockey_loanoffers:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to Loanoffers Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk loanoffers

  deploy_to_hockey_ghost-pl-v2:
    docker:
      - image: circleci/node:latest
    steps:
      - checkout
      - *attach_workspace_artifacts
      # storing to be visible on api as an outcome of the last build
      - *save_workspace_artifacts
      - deploy:
          name: Deploy QA version to ghost-pl-v2 Hockey
          command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk ghost-pl-v2

  deploy_to_hockey_vas:
        docker:
          - image: circleci/node:latest
        steps:
          - checkout
          - *attach_workspace_artifacts
          # storing to be visible on api as an outcome of the last build
          - *save_workspace_artifacts
          - deploy:
              name: Deploy QA version to VAS Hockey
              command: ./deploy.sh c87f88d15a714d6e9beda4b52d38f56a outputs/outputs/apk/squad/app-squad.apk vas

  push_to_sauce:
    docker:
      - image: circleci/node:latest
    environment:
      TEST_ENVIRONMENT: sauce
    steps:
      - checkout
      - attach_workspace:
          at: outputs
      # storing to be visible on api as an outcome of the last build
      - store_artifacts:
          path: outputs/outputs/apk
      - run: npm install
      - run:
          name: Push to sauce
          command: |
            CURRENT_DIR=$(pwd)
            cd node_modules/banking-functional-tests
            npm run sauce-push-android -- "@${CURRENT_DIR}/outputs/outputs/apk/qa/app-qa.apk"

workflows:
  version: 2
  qa-build:
    jobs:
      - unit_test:
          filters:
            branches:
              ignore: master
      - build_qa:
          filters:
            branches:
              ignore: [master, eFICA, squad/investments, squad/sbs_development, squad/onlineSavings, squad/homeLoan, squad/vas, squad/lnr, squad/notifications, squad/transaction-notification, squad/loanoffers, squad/govt_payment, squad/de_1_aug, squad/ghost-v2, squad/ghost-pl-v2, squad/govt_payment,  squad/sbs_context_v2, squad/referrals, squad/smartQing, squad/insurance_v1]
      # we do build prod on feature branches only to make sure that it builds with new changes
      - build_prod:
          filters:
            branches:
              ignore: [master, develop]
      - build_bundle:
          filters:
            branches:
              ignore: [master, develop]
      - deploy_to_hockey_qa:
          requires:
            - build_qa
          filters:
            branches:
              only: &qa_build_branch
                      develop
      - push_to_sauce:
          requires:
            - build_qa
          filters:
            branches:
              only:
                - *qa_build_branch
                - refactor/bump-test-repo-version
  prod-release:
    jobs:
      - build_prod:
          filters:
            tags:
              only: &inhouse_build_tag
                      /^prod-release\/.*/
            branches:
              ignore: /.*/
      - deploy_to_hockey_prod:
          requires:
            - build_prod
          filters:
            tags:
              only: *inhouse_build_tag
            branches:
              ignore: /.*/
  qa-release:
    jobs:
      - build_qa_release:
          filters:
            tags:
              only: &qa_release_build_tag
                      /^qa-release\/.*$/
            branches:
              ignore: /.*/
      - deploy_to_hockey_qa_release:
          requires:
            - build_qa_release
          filters:
            tags:
              only: *qa_release_build_tag
            branches:
              ignore: /.*/
  ete-build:
    jobs:
      - build_ete:
          filters:
            branches:
              only: &qa_build_branch
                      develop
      - deploy_to_hockey_ete:
          requires:
            - build_ete
          filters:
            branches:
              only: &qa_build_branch
                      develop
  fica-build:
    jobs:
      - build_squad_fica:
          filters:
            branches:
              only:
                eFICA
      - deploy_to_hockey_fica:
          requires:
            - build_squad_fica
          filters:
            branches:
              only:
                eFICA
  invest-build:
    jobs:
      - build_squad_investment:
          filters:
            branches:
              only:
                squad/investments
      - deploy_to_hockey_investment:
          requires:
            - build_squad_investment
          filters:
            branches:
              only:
                squad/investments
  referrals-build:
    jobs:
      - build_squad_referrals:
          filters:
            branches:
              only:
                squad/referrals
      - deploy_to_hockey_referrals:
          requires:
            - build_squad_referrals
          filters:
            branches:
              only:
                squad/referrals
  mae-build:
    jobs:
      - build_squad_mae:
          filters:
            branches:
              only:
                feature/squadmae
      - deploy_to_hockey_mae:
          requires:
            - build_squad_mae
          filters:
            branches:
              only:
                feature/squadmae
  sba-build:
      jobs:
        - build_squad_sba:
            filters:
              branches:
                only:
                  squad/sbs_context_v2
        - deploy_to_hockey_sba:
            requires:
              - build_squad_sba
            filters:
              branches:
                only:
                  squad/sbs_context_v2

  de-build:
    jobs:
      - build_squad_de:
          filters:
            branches:
              only:
                squad/de_1_aug
      - deploy_to_hockey_de:
          requires:
            - build_squad_de
          filters:
            branches:
              only:
                squad/de_1_aug

  onlineSavings-build:
      jobs:
        - build_squad_onlineSavings:
            filters:
              branches:
                only:
                  squad/onlineSavings
        - deploy_to_hockey_onlineSavings:
            requires:
              - build_squad_onlineSavings
            filters:
              branches:
                only:
                  squad/onlineSavings

  homeloan-build:
    jobs:
      - build_squad_homeloan:
          filters:
            branches:
              only:
                squad/homeLoan
      - deploy_to_hockey_homeloan:
          requires:
            - build_squad_homeloan
          filters:
            branches:
              only:
                squad/homeLoan

  smartQing-build:
        jobs:
          - build_squad_smartQing:
              filters:
                branches:
                  only:
                    squad/smartQing
          - deploy_to_hockey_smartQing:
              requires:
                - build_squad_smartQing
              filters:
                branches:
                  only:
                    squad/smartQing

  insurance_v1-build:
      jobs:
        - build_squad_insurance_v1:
            filters:
              branches:
                only:
                  squad/insurance_v1
        - deploy_to_hockey_insurance_v1:
            requires:
              - build_squad_insurance_v1
            filters:
              branches:
                only:
                  squad/insurance_v1

  govtpay-build:
    jobs:
      - build_squad_govtpay:
          filters:
            branches:
              only:
                squad/govt_payment
      - deploy_to_hockey_govtpay:
          requires:
            - build_squad_govtpay
          filters:
            branches:
              only:
                squad/govt_payment
  forex_mvp2-build:
    jobs:
      - build_squad_forex_mvp2:
          filters:
            branches:
              only:
                squad/forex_mvp2
      - deploy_to_hockey_forex_mvp2:
          requires:
            - build_squad_forex_mvp2
          filters:
            branches:
              only:
                squad/forex_mvp2
  forex_itt-build:
    jobs:
      - build_squad_forex_itt:
          filters:
            branches:
              only:
                squad/forex_itt
      - deploy_to_hockey_forex_itt:
          requires:
            - build_squad_forex_itt
          filters:
            branches:
              only:
                squad/forex_itt
  ghost-v2-build:
    jobs:
      - build_squad_ghost-v2:
          filters:
            branches:
              only:
                squad/ghost-v2
      - deploy_to_hockey_ghost-v2:
          requires:
            - build_squad_ghost-v2
          filters:
            branches:
              only:
                squad/ghost-v2
  appbundle-build:
    jobs:
      - build_feature_appbundle:
          filters:
            branches:
              only:
                feature/app_bundle
      - deploy_to_hockey_appbundle:
          requires:
            - build_feature_appbundle
          filters:
            branches:
              only:
                feature/app_bundle

  ghost-v2-build:
    jobs:
      - build_squad_ghost-v2:
          filters:
            branches:
              only:
                squad/ghost-v2
      - deploy_to_hockey_ghost-v2:
          requires:
            - build_squad_ghost-v2
          filters:
            branches:
              only:
                squad/ghost-v2

  lnr-build:
    jobs:
      - build_squad_lnr:
          filters:
            branches:
              only:
                squad/lnr
      - deploy_to_hockey_lnr:
          requires:
            - build_squad_lnr
          filters:
            branches:
              only:
                squad/lnr

  notifications-build:
    jobs:
      - build_squad_notifications:
          filters:
            branches:
              only:
                squad/notifications
      - deploy_to_hockey_notifications:
          requires:
            - build_squad_notifications
          filters:
            branches:
              only:
                squad/notifications

  transaction-notification-build:
    jobs:
    - build_squad_transaction-notification:
        filters:
          branches:
            only:
              squad/transaction-notification
    - deploy_to_hockey_transaction-notification:
        requires:
          - build_squad_transaction-notification
        filters:
          branches:
            only:
              squad/transaction-notification

  loanoffers-build:
    jobs:
      - build_squad_loanoffers:
          filters:
            branches:
              only:
                squad/loanoffers
      - deploy_to_hockey_loanoffers:
          requires:
            - build_squad_loanoffers
          filters:
            branches:
              only:
                squad/loanoffers

  ghost-pl-v2-build:
    jobs:
      - build_squad_ghost-pl-v2:
          filters:
            branches:
              only:
                squad/ghost-pl-v2
      - deploy_to_hockey_ghost-pl-v2:
          requires:
            - build_squad_ghost-pl-v2
          filters:
            branches:
              only:
                squad/ghost-pl-v2

  vas-build:
        jobs:
          - build_squad_vas:
              filters:
                branches:
                  only:
                    squad/vas
          - deploy_to_hockey_vas:
              requires:
                - build_squad_vas
              filters:
                branches:
                  only:
                    squad/vas
