package za.co.nedbank.ui.domain.model.closed_account;

public class ClosedAccountDataModel {
    private String investorNumber;
    private String accountNumber;
    private String accountName;

    public String getInvestorNumber() {
        return investorNumber;
    }

    public void setInvestorNumber(String investorNumber) {
        this.investorNumber = investorNumber;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
}
