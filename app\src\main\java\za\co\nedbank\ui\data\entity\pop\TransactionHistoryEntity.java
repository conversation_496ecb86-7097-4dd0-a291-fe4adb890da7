package za.co.nedbank.ui.data.entity.pop;


import com.squareup.moshi.Json;

import java.util.List;

import za.co.nedbank.core.payment.recent.NotificationDetailsEntity;

public class TransactionHistoryEntity {
    @Json(name = "myReference")
    private String myReference;
    @Json(name = "batchID")
    private long batchID;
    @Json(name = "transactionID")
    private long transactionID;
    @Json(name = "capturedDate")
    private String capturedDate;
    @<PERSON>son(name = "transactionDate")
    private String startDate;
    @Json(name = "nextTransDate")
    private String nextTransDate;
    @Json(name = "beneficiaryID")
    private int beneficiaryID;
    @Json(name = "destinationNumber")
    private String destinationNumber;
    @Json(name = "serviceProvider")
    private String serviceProvider;
    @Json(name = "productCode")
    private String productCode;
    @Json(name = "amount")
    private double amount;
    @Json(name = "notificationDetails")
    private List<NotificationDetailsEntity> notificationDetails;
    @Json(name = "notificationDetail")
    private List<NotificationDetailsEntity> notificationDetail;
    @Json(name = "prepaidStatus")
    private String prepaidStatus;
    @<PERSON>son(name = "purchaseReferenceNumber")
    private String purchaseReferenceNumber;
    @Json(name = "beneficiaryName")
    private String bfName;
    @Json(name = "myDescription")
    private String myDescription;
    @Json(name = "beneficiaryAccount")
    private String beneficiaryAccount;
    @Json(name = "contactCardID")
    private int contactCardID;
    @Json(name = "transactionKind")
    private String transactionType;
    @Json(name ="instantPayment")
    private boolean instantPayment;
    @Json(name ="status")
    private String status;

    @Json(name ="beneficiaryType")
    private String beneficiaryType;

    @Json(name ="rapidPayment")
    private boolean rapidPayment;
    @Json(name ="proxyName")
    private String proxyName;
    @Json(name ="proxyDomain")
    private String proxyDomain;

    public String getBeneficiaryType() {
        return beneficiaryType;
    }

    public void setBeneficiaryType(String beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    @Json(name ="proxyType")
    private String proxyType;

    public boolean isRapidPayment() {
        return rapidPayment;
    }

    public void setRapidPayment(boolean rapidPayment) {
        this.rapidPayment = rapidPayment;
    }

    public String getProxyName() {
        return proxyName;
    }

    public void setProxyName(String proxyName) {
        this.proxyName = proxyName;
    }

    public String getProxyDomain() {
        return proxyDomain;
    }

    public void setProxyDomain(String proxyDomain) {
        this.proxyDomain = proxyDomain;
    }

    public String getProxyType() {
        return proxyType;
    }

    public void setProxyType(String proxyType) {
        this.proxyType = proxyType;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public int getContactCardID() {
        return contactCardID;
    }

    public String getBeneficiaryAccount() {
        return beneficiaryAccount;
    }

    public String getMyReference() {
        return myReference;
    }

    public long getBatchID() {
        return batchID;
    }

    public long getTransactionID() {
        return transactionID;
    }

    public String getCapturedDate() {
        return capturedDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public String getNextTransDate() {
        return nextTransDate;
    }

    public int getBeneficiaryID() {
        return beneficiaryID;
    }

    public String getDestinationNumber() {
        return destinationNumber;
    }

    public String getServiceProvider() {
        return serviceProvider;
    }

    public String getProductCode() {
        return productCode;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public List<NotificationDetailsEntity> getNotificationDetails() {
        return notificationDetails;
    }

    public List<NotificationDetailsEntity> getNotificationDetail() {
        return notificationDetail;
    }

    public String getPrepaidStatus() {
        return prepaidStatus;
    }

    public String getPurchaseReferenceNumber() {
        return purchaseReferenceNumber;
    }

    public String getBfName() {
        return bfName;
    }

    public String getMyDescription() {
        return myDescription;
    }

    public boolean isInstantPayment() {
        return instantPayment;
    }

    public String getStatus() {
        return status;
    }
}
