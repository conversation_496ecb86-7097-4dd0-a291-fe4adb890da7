/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import static za.co.nedbank.core.Constants.BUNDLE_KEYS.SELECTED_BANK_BRANCH_LIST;
import static za.co.nedbank.core.Constants.BUNDLE_KEYS.SELECTED_BANK_ID;

import android.util.Log;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationResult;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.AccountValidator;
import za.co.nedbank.core.validation.CreditCardNumberValidator;
import za.co.nedbank.core.validation.ElectricityMeterNumberValidator;
import za.co.nedbank.core.validation.EmailValidator;
import za.co.nedbank.core.validation.MobileNumberValidator;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.validation.RecipientNameCharValidator;
import za.co.nedbank.core.validation.ReferenceFieldValidator;
import za.co.nedbank.core.validation.ShapIdValidator;
import za.co.nedbank.core.validation.ValidationResult;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.view.banklist.model.BankBranchViewModel;
import za.co.nedbank.core.view.mapper.ElectricityMeterViewDataModel;
import za.co.nedbank.core.view.recipient.BankAccountViewDataModel;
import za.co.nedbank.core.view.recipient.CreditCardViewDataModel;
import za.co.nedbank.core.view.recipient.EmailViewDataModel;
import za.co.nedbank.core.view.recipient.MobileNumberViewDataModel;
import za.co.nedbank.core.view.recipient.ShapIDViewDataModel;
import za.co.nedbank.payment.common.validation.RecipientNameValidator;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.payment.pay.view.model.SelectedBankViewModel;
import za.co.nedbank.ui.domain.usecase.GetShapPayDomainUseCase;
import za.co.nedbank.uisdk.validation.ValidatableInput;

/**
 * Created by priyadhingra on 9/14/2017.
 */

public class BaseRecipientPresenter<V extends BaseRecipientView> extends NBBasePresenter<BaseRecipientView> {
    protected final String TAG = BaseRecipientPresenter.class.getSimpleName();
    protected final NavigationRouter mNavigationRouter;
    private final RecipientNameValidator mRecipientNameValidator;
    private final RecipientNameCharValidator mRecipientNameCharValidator;
    private final MobileNumberValidator mMobileNumberValidator;
    private final AccountValidator mAccountValidator;
    private final NonEmptyTextValidator nonEmptyTextValidator;
    private final ReferenceFieldValidator referenceFieldValidator;
    private final ElectricityMeterNumberValidator mElectricityMeterNumberValidator;
    private final CreditCardNumberValidator mCreditCardNumberValidator;
    private final ShapIdValidator mShapIdValidator;
    private final EmailValidator mEmailValidator;
    protected NavigationResult mNavigationResult;
    protected final ErrorHandler mErrorHandler;
    protected final FeatureSetController mFeatureSetController;
    private final GetShapPayDomainUseCase mGetFastPayDomainUseCase;
    protected Map<Integer, String> mMatchBackNumberErrorMap;

    @Inject
    protected BaseRecipientPresenter(final NavigationRouter navigationRouter, final RecipientNameValidator recipientNameValidator,
                                     final RecipientNameCharValidator recipientNameNewValidator,
                                     final MobileNumberValidator mMobileNumberValidator, final NonEmptyTextValidator nonEmptyTextValidator,
                                     final ReferenceFieldValidator referenceFieldValidator, final CreditCardNumberValidator creditCardNumberValidator,
                                     final AccountValidator accountValidator, final ElectricityMeterNumberValidator electricityMeterNumberValidator,
                                     final EmailValidator emailValidator, ErrorHandler errorHandler, FeatureSetController featureSetController, ShapIdValidator shapIdValidator,
                                     final GetShapPayDomainUseCase getFastPayDomainUseCase) {
        this.mNavigationRouter = navigationRouter;
        this.mRecipientNameValidator = recipientNameValidator;
        this.mRecipientNameCharValidator = recipientNameNewValidator;
        this.mMobileNumberValidator = mMobileNumberValidator;
        this.mCreditCardNumberValidator = creditCardNumberValidator;
        this.nonEmptyTextValidator = nonEmptyTextValidator;
        this.referenceFieldValidator = referenceFieldValidator;
        this.mAccountValidator = accountValidator;
        this.mElectricityMeterNumberValidator = electricityMeterNumberValidator;
        this.mEmailValidator = emailValidator;
        this.mErrorHandler = errorHandler;
        this.mFeatureSetController = featureSetController;
        this.mShapIdValidator = shapIdValidator;
        this.mGetFastPayDomainUseCase = getFastPayDomainUseCase;
    }

    public void fetchFastPayDomainList() {
        if (view == null) {
            return;
        }
        if (CollectionUtils.isEmpty(view.fetchFastPayDomainNameList())) {
            mGetFastPayDomainUseCase.execute()
                    .subscribe(
                            data -> {
                                if (view != null && data != null && data.getFastPayDomainList() != null && !data.getFastPayDomainList().isEmpty()) {
                                    view.setFastPayDomainNameMap(data.getFastPayDomainList());
                                }
                            }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
    }

    boolean validateInput(final ValidatableInput<String> input,
                          final Validator.ValidatorType validatorType) {
        switch (validatorType) {
            case MOBILE_NUMBER_VAIDATOR:
                return validate(input, mMobileNumberValidator);
            case RECIPIENT_NAME_VALIDATOR:
                return validate(input, mRecipientNameValidator);
            case RECIPIENT_NAME_CHAR_VALIDATOR:
                return validate(input, mRecipientNameCharValidator);
            case EMAIL_VALIDATOR:
                return validate(input, mEmailValidator);
            case CARD_NUMBER_VALIDATOR:
                return validate(input, mCreditCardNumberValidator);
            case SHAP_ID_VALIDATOR:
                ValidationResult localResult = mShapIdValidator.validateInputForSimpleAndComplex(input.getValue());
                ValidationResult domainResult = mShapIdValidator.validateDomainInputRecipient(input.getValue(), view.fetchFastPayDomainNameMap());

                if (!localResult.isOk() || !domainResult.isOk()) {

                    String errorMessage = StringUtils.EMPTY_STRING;
                    if (!localResult.isOk())
                        errorMessage = localResult.getErrorsMessage();
                    if (!domainResult.isOk())
                        errorMessage = domainResult.getErrorsMessage();

                    input.showError(errorMessage);
                }

                return localResult.isOk() && domainResult.isOk();
            case METER_NUMBER_VALIDATOR:
                return validate(input, mElectricityMeterNumberValidator);
            case ACCOUNT_NUMBER_VALIDATOR:
                return validate(input, mAccountValidator);
            case NON_EMPTY_FIELD_VALIDATOR:
                return validate(input, nonEmptyTextValidator);
            case REFERENCE_VALIDATOR:
                return validate(input, nonEmptyTextValidator) && validate(input, referenceFieldValidator);
            default:
        }
        return false;
    }

    protected void preApiCallTasks(){
        if(view != null){
            view.setLoadingButtonEnabled(false);
            view.showLoadingOnButton(true);
            view.setEnabledActivityTouch(false);
        }
    }

    private boolean isBankAccountFieldEmpty(BankAccountViewDataModel bankAccountViewDataModel) {
        return bankAccountViewDataModel.getBankName() != null && bankAccountViewDataModel.getBankName().equalsIgnoreCase(view.getDefaultBankNameText()) &&
                !nonEmptyTextValidator.validateInput(bankAccountViewDataModel.getAccountNumber()).isOk()
                && !nonEmptyTextValidator.validateInput(bankAccountViewDataModel.getRecipientRef()).isOk()
                && !nonEmptyTextValidator.validateInput(bankAccountViewDataModel.getYourRef()).isOk();
    }

    private boolean isMobileNumberFieldEmpty(MobileNumberViewDataModel mobileNumberViewDataModel) {
        return !nonEmptyTextValidator.validateInput(mobileNumberViewDataModel.getMobileNumber()).isOk() &&
                !nonEmptyTextValidator.validateInput(mobileNumberViewDataModel.getYourRef()).isOk();

    }

    private boolean isCreditCardFieldEmpty(CreditCardViewDataModel creditCardViewDataModel) {
        return !nonEmptyTextValidator.validateInput(creditCardViewDataModel.getCardNumber()).isOk() &&
                !nonEmptyTextValidator.validateInput(creditCardViewDataModel.getYourRef()).isOk() &&
                !nonEmptyTextValidator.validateInput(creditCardViewDataModel.getRecipientRef()).isOk();

    }

    public boolean isShapIdFieldEmpty(ShapIDViewDataModel shapIDViewDataModel) {
        return !nonEmptyTextValidator.validateInput(shapIDViewDataModel.getShapid()).isOk() &&
                !nonEmptyTextValidator.validateInput(shapIDViewDataModel.getYourReference()).isOk() &&
                !nonEmptyTextValidator.validateInput(shapIDViewDataModel.getRecipientReference()).isOk();

    }

    private boolean isElectricityMeterFieldEmpty(ElectricityMeterViewDataModel electricityMeterViewDataModel) {
        return !nonEmptyTextValidator.validateInput(electricityMeterViewDataModel.getElectricityMeterNumber()).isOk() &&
                !nonEmptyTextValidator.validateInput(electricityMeterViewDataModel.getYourRef()).isOk();

    }

    private boolean isBankAccountFieldFullyFilled(BankAccountViewDataModel bankAccountViewDataModel) {
        return nonEmptyTextValidator.validateInput(bankAccountViewDataModel.getBankName()).isOk() &&
                !bankAccountViewDataModel.getBankName().equalsIgnoreCase(view.getDefaultBankNameText()) &&
                nonEmptyTextValidator.validateInput(bankAccountViewDataModel.getAccountNumber()).isOk()
                && nonEmptyTextValidator.validateInput(bankAccountViewDataModel.getRecipientRef()).isOk()
                && nonEmptyTextValidator.validateInput(bankAccountViewDataModel.getYourRef()).isOk();
    }

    private boolean isBankAccountFieldFullyFilledForBDF(BankAccountViewDataModel bankAccountViewDataModel) {
        return  nonEmptyTextValidator.validateInput(bankAccountViewDataModel.getRecipientRef()).isOk()
                && nonEmptyTextValidator.validateInput(bankAccountViewDataModel.getYourRef()).isOk();
    }

    private boolean isMobileNumberFieldFullyFilled(MobileNumberViewDataModel mobileNumberViewDataModel) {
        return nonEmptyTextValidator.validateInput(mobileNumberViewDataModel.getMobileNumber()).isOk() &&
                nonEmptyTextValidator.validateInput(mobileNumberViewDataModel.getYourRef()).isOk();

    }

    public boolean isEmailFieldFullyFilled(EmailViewDataModel emailViewDataModel) {
        return nonEmptyTextValidator.validateInput(emailViewDataModel.getEmail()).isOk();

    }

    private boolean isElectricityMeterFieldFullyFilled(ElectricityMeterViewDataModel electricityMeterViewDataModel) {
        return nonEmptyTextValidator.validateInput(electricityMeterViewDataModel.getElectricityMeterNumber()).isOk() &&
                nonEmptyTextValidator.validateInput(electricityMeterViewDataModel.getYourRef()).isOk();
    }

    private boolean isCreditCardFieldFullyFilled(CreditCardViewDataModel creditCardViewDataModel) {
        return nonEmptyTextValidator.validateInput(creditCardViewDataModel.getCardNumber()).isOk() &&
                nonEmptyTextValidator.validateInput(creditCardViewDataModel.getYourRef()).isOk() &&
                nonEmptyTextValidator.validateInput(creditCardViewDataModel.getRecipientRef()).isOk();
    }

    private boolean isShapIdFullyFilled(ShapIDViewDataModel shapIDViewDataModel) {
        return nonEmptyTextValidator.validateInput(shapIDViewDataModel.getShapid()).isOk() &&
                mShapIdValidator.validateInputForSimpleAndComplex(shapIDViewDataModel.getShapid()).isOk() &&
                mShapIdValidator.validateDomainInputRecipient(shapIDViewDataModel.getShapid(), view.fetchFastPayDomainNameMap()).isOk() &&
                nonEmptyTextValidator.validateInput(shapIDViewDataModel.getYourReference()).isOk() &&
                nonEmptyTextValidator.validateInput(shapIDViewDataModel.getRecipientReference()).isOk();
    }

    private boolean isAtleastOneCompleteRecipientEntryExists(List<NbRecyclerViewBaseDataModel> bankAccountDataModelList,
                                                             List<NbRecyclerViewBaseDataModel> mobileNumberDataModelList,
                                                             List<NbRecyclerViewBaseDataModel> electricityDataModelList,
                                                             List<NbRecyclerViewBaseDataModel> creditCardDataModelList,
                                                             List<NbRecyclerViewBaseDataModel> shapIdDataModelList
                                                             ) {
        for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : bankAccountDataModelList) {
            if(nbRecyclerViewBaseDataModel instanceof BankAccountViewDataModel){
                BankAccountViewDataModel bankAccountViewDataModel = (BankAccountViewDataModel) nbRecyclerViewBaseDataModel;
                if (isBankAccountFieldFullyFilled(bankAccountViewDataModel)) {
                    return true;
                }
            }
        }

        for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : mobileNumberDataModelList) {
            if(nbRecyclerViewBaseDataModel instanceof MobileNumberViewDataModel){
                MobileNumberViewDataModel mobileNumberViewDataModel = (MobileNumberViewDataModel) nbRecyclerViewBaseDataModel;
                if (isMobileNumberFieldFullyFilled(mobileNumberViewDataModel)) {
                    return true;
                }
            }
        }

        for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : electricityDataModelList) {
            if(nbRecyclerViewBaseDataModel instanceof ElectricityMeterViewDataModel){
                ElectricityMeterViewDataModel electricityMeterViewDataModel = (ElectricityMeterViewDataModel) nbRecyclerViewBaseDataModel;
                if (isElectricityMeterFieldFullyFilled(electricityMeterViewDataModel)) {
                    return true;
                }
            }
        }

        for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : creditCardDataModelList) {
            if(nbRecyclerViewBaseDataModel instanceof CreditCardViewDataModel){
                CreditCardViewDataModel creditCardViewDataModel = (CreditCardViewDataModel) nbRecyclerViewBaseDataModel;
                if (isCreditCardFieldFullyFilled(creditCardViewDataModel)) {
                    return true;
                }
            }
        }
        for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : shapIdDataModelList) {
            if(nbRecyclerViewBaseDataModel instanceof ShapIDViewDataModel){
                ShapIDViewDataModel shapIDViewDataModel = (ShapIDViewDataModel) nbRecyclerViewBaseDataModel;
                if (isShapIdFullyFilled(shapIDViewDataModel)) {
                    return true;
                }
            }
        }

        return false;
    }

    private boolean isInCompleteDataExists(List<NbRecyclerViewBaseDataModel> bankAccountDataModelList,
                                           List<NbRecyclerViewBaseDataModel> mobileNumberDataModelList,
                                           List<NbRecyclerViewBaseDataModel> electricityDataModelList,
                                           List<NbRecyclerViewBaseDataModel> creditCardDataModelList,
                                           List<NbRecyclerViewBaseDataModel> shapIdDataModelList

    ) {
        boolean isBankAccountInCompleteData = false;
        boolean isMobileNumberInCompleteData = false;
        boolean isElectricityInCompleteData = false;
        boolean isCreditCardInCompleteData = false;
        boolean isEmailInputListInCompleteData=false;
        boolean isShapIdInCompleteData=false;

        if (bankAccountDataModelList != null) {

            for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : bankAccountDataModelList) {
                if(nbRecyclerViewBaseDataModel instanceof BankAccountViewDataModel){
                    BankAccountViewDataModel bankAccountViewDataModel = (BankAccountViewDataModel) nbRecyclerViewBaseDataModel;
                    if (!isBankAccountFieldFullyFilled(bankAccountViewDataModel) && !isBankAccountFieldEmpty(bankAccountViewDataModel)) {
                        isBankAccountInCompleteData = true;
                        break;
                    }
                }
            }
        }


        if (mobileNumberDataModelList != null) {

            for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : mobileNumberDataModelList) {
                if(nbRecyclerViewBaseDataModel instanceof MobileNumberViewDataModel){
                    MobileNumberViewDataModel mobileNumberViewDataModel = (MobileNumberViewDataModel) nbRecyclerViewBaseDataModel;
                    if (!isMobileNumberFieldFullyFilled(mobileNumberViewDataModel) && !isMobileNumberFieldEmpty(mobileNumberViewDataModel)) {
                        isMobileNumberInCompleteData = true;
                        break;
                    }
                }
            }
        }

        if (electricityDataModelList != null) {

            for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : electricityDataModelList) {
                if(nbRecyclerViewBaseDataModel instanceof ElectricityMeterViewDataModel){
                    ElectricityMeterViewDataModel electricityMeterViewDataModel = (ElectricityMeterViewDataModel) nbRecyclerViewBaseDataModel;
                    if (!isElectricityMeterFieldFullyFilled(electricityMeterViewDataModel) && !isElectricityMeterFieldEmpty(electricityMeterViewDataModel)) {
                        isElectricityInCompleteData = true;
                        break;
                    }
                }
            }
        }

        if (creditCardDataModelList != null) {

            for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : creditCardDataModelList) {
                if(nbRecyclerViewBaseDataModel instanceof CreditCardViewDataModel){
                    CreditCardViewDataModel creditCardViewDataModel = (CreditCardViewDataModel) nbRecyclerViewBaseDataModel;
                    if (!isCreditCardFieldFullyFilled(creditCardViewDataModel) && !isCreditCardFieldEmpty(creditCardViewDataModel)) {
                        isCreditCardInCompleteData  = true;
                        break;
                    }
                }
            }
        }

        if (shapIdDataModelList != null) {

            for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : shapIdDataModelList) {
                if(nbRecyclerViewBaseDataModel instanceof ShapIDViewDataModel){
                    ShapIDViewDataModel shapIDViewDataModel = (ShapIDViewDataModel) nbRecyclerViewBaseDataModel;
                    if (!isShapIdFullyFilled(shapIDViewDataModel) && !isShapIdFieldEmpty(shapIDViewDataModel)) {
                        isShapIdInCompleteData  = true;
                        break;
                    }
                }
            }
        }
        return (isEmailInputListInCompleteData || isBankAccountInCompleteData || isMobileNumberInCompleteData || isElectricityInCompleteData || isCreditCardInCompleteData || isShapIdInCompleteData );
    }

     public void checkInputsOnScreen(final ValidatableInput<String> recipientNameInput, List<NbRecyclerViewBaseDataModel> bankAccountNumberInputList,
                                     List<NbRecyclerViewBaseDataModel> mobileNumberInputList, List<NbRecyclerViewBaseDataModel> electricityMeterInputList,
                                     List<NbRecyclerViewBaseDataModel> creditCardNumberInputList,
                                     List<NbRecyclerViewBaseDataModel> shapIdInputList
                                     ) {

         if (view == null || bankAccountNumberInputList == null || mobileNumberInputList == null || electricityMeterInputList == null || creditCardNumberInputList == null || shapIdInputList == null) {
             return;
         }

         if (mMatchBackNumberErrorMap != null && !mMatchBackNumberErrorMap.isEmpty()) {
             view.setLoadingButtonEnabled(false);
             return;
         }

        if (recipientNameInput != null && !nonEmptyTextValidator.validateInput(recipientNameInput.getValue()).isOk()) {
            view.setLoadingButtonEnabled(false);
            return;
        }

         if (CollectionUtils.isEmpty(bankAccountNumberInputList) && CollectionUtils.isEmpty(mobileNumberInputList) && CollectionUtils.isEmpty(electricityMeterInputList) && CollectionUtils.isEmpty(creditCardNumberInputList) && CollectionUtils.isEmpty(shapIdInputList)) {
             view.setLoadingButtonEnabled(false);
             return;
        }

         if (isInCompleteDataExists(bankAccountNumberInputList, mobileNumberInputList, electricityMeterInputList, creditCardNumberInputList,shapIdInputList)) {
             view.setLoadingButtonEnabled(false);
             return;
         }

         view.setLoadingButtonEnabled(isAtleastOneCompleteRecipientEntryExists(bankAccountNumberInputList, mobileNumberInputList, electricityMeterInputList, creditCardNumberInputList, shapIdInputList));
    }

    public void disableLoadingButton(){
        if(view != null) {
            view.setLoadingButtonEnabled(false);
        }
    }

    public void checkInputsOnScreenForBDF(final ValidatableInput<String> recipientNameInput, List<NbRecyclerViewBaseDataModel> bankAccountNumberInputList) {
        if (view == null || bankAccountNumberInputList == null ) {
            return;
        }
        if (recipientNameInput != null && !nonEmptyTextValidator.validateInput(recipientNameInput.getValue()).isOk()) {
            view.setLoadingButtonEnabled(false);
            return;
        }

        if (bankAccountNumberInputList.size() == 0) {
            view.setLoadingButtonEnabled(false);
            return;
        }

        boolean isBankAccountInCompleteData = false;
        for (NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel : bankAccountNumberInputList) {
            if(nbRecyclerViewBaseDataModel instanceof BankAccountViewDataModel){
                BankAccountViewDataModel bankAccountViewDataModel = (BankAccountViewDataModel) nbRecyclerViewBaseDataModel;
                if (!isBankAccountFieldFullyFilledForBDF(bankAccountViewDataModel)) {
                    isBankAccountInCompleteData = true;
                    break;
                }
            }
        }
        if (isBankAccountInCompleteData) {
            view.setLoadingButtonEnabled(false);
        } else {
            view.setLoadingButtonEnabled(true);
        }
    }

    public void handleSelectBankLayoutClick() {
        NavigationTarget navigationTarget = NavigationTarget.to(PayNavigatorTarget.BANK_LIST);
        navigationTarget.withParam(Constants.BundleKeys.VIEW_PAGER_WTH_TAB_VIEW, Constants.ISearchListViewType.VIEW_TYPE_PAY);
        mNavigationRouter.navigateWithResult(navigationTarget).subscribe(
                navigationResult -> mNavigationResult = navigationResult
                ,throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
        );
    }

    @Override
    protected void onBind() {
        if (null != mNavigationResult && mNavigationResult.isOk() && view != null) {
            view.setResult(mNavigationResult.getParams());
        }
    }

    public void validateInput(final ValidatableInput<String> recipientNameInput,
                              List<RecipientValidatableViewModel> bankAccountRecyclerViewBaseDataModelList,
                              List<RecipientValidatableViewModel> creditCardRecyclerViewBaseDataModelList,
                              List<RecipientValidatableViewModel> mobileNumberRecyclerViewBaseDataModelList,
                              List<RecipientValidatableViewModel> electricityRecyclerViewBaseDataModelList,
                              List<RecipientValidatableViewModel> emailIdValidatableInputsInputList,
                              List<RecipientValidatableViewModel> shapIdValidatableInputsList
    ) {
        boolean isDataCorrect = true;

        if (recipientNameInput != null) {
            isDataCorrect = validateInput(recipientNameInput, Validator.ValidatorType.RECIPIENT_NAME_VALIDATOR);
        }
        for (RecipientValidatableViewModel bankAccountInput : bankAccountRecyclerViewBaseDataModelList) {
            isDataCorrect = validateInput(bankAccountInput.getRecipientNumber(), Validator.ValidatorType.ACCOUNT_NUMBER_VALIDATOR) && isDataCorrect;
            isDataCorrect = validateInput(bankAccountInput.getYourRef(), Validator.ValidatorType.REFERENCE_VALIDATOR) && isDataCorrect;
            isDataCorrect = validateInput(bankAccountInput.getRecipientRef(), Validator.ValidatorType.REFERENCE_VALIDATOR) && isDataCorrect;
        }

        for (RecipientValidatableViewModel creditCardInput : creditCardRecyclerViewBaseDataModelList) {
            isDataCorrect = validateInput(creditCardInput.getRecipientNumber(), Validator.ValidatorType.CARD_NUMBER_VALIDATOR) && isDataCorrect;
            isDataCorrect = validateInput(creditCardInput.getYourRef(), Validator.ValidatorType.REFERENCE_VALIDATOR) && isDataCorrect;
            isDataCorrect = validateInput(creditCardInput.getRecipientRef(), Validator.ValidatorType.REFERENCE_VALIDATOR) && isDataCorrect;
        }

        for (RecipientValidatableViewModel shapInput : shapIdValidatableInputsList) {
            isDataCorrect = validateInput(shapInput.getRecipientNumber(), Validator.ValidatorType.SHAP_ID_VALIDATOR) && isDataCorrect;
            isDataCorrect = validateInput(shapInput.getYourRef(), Validator.ValidatorType.REFERENCE_VALIDATOR) && isDataCorrect;
            isDataCorrect = validateInput(shapInput.getRecipientRef(), Validator.ValidatorType.REFERENCE_VALIDATOR) && isDataCorrect;
        }

        for (RecipientValidatableViewModel mobileNumberInput : mobileNumberRecyclerViewBaseDataModelList) {
            isDataCorrect = validateInput(mobileNumberInput.getRecipientNumber(), Validator.ValidatorType.MOBILE_NUMBER_VAIDATOR) && isDataCorrect;
            isDataCorrect = validateInput(mobileNumberInput.getYourRef(), Validator.ValidatorType.REFERENCE_VALIDATOR) && isDataCorrect;
        }

        for (RecipientValidatableViewModel electricityMeterNumberInput : electricityRecyclerViewBaseDataModelList) {
            isDataCorrect = validateInput(electricityMeterNumberInput.getRecipientNumber(), Validator.ValidatorType.METER_NUMBER_VALIDATOR) && isDataCorrect;
            isDataCorrect = validateInput(electricityMeterNumberInput.getYourRef(), Validator.ValidatorType.REFERENCE_VALIDATOR) && isDataCorrect;
        }

        for (RecipientValidatableViewModel emailInput : emailIdValidatableInputsInputList) {
            isDataCorrect = validateInput(emailInput.getRecipientNumber(), Validator.ValidatorType.EMAIL_VALIDATOR) && isDataCorrect;
        }

        if (isDataCorrect && view != null && (mMatchBackNumberErrorMap ==null || mMatchBackNumberErrorMap.isEmpty())) {
            view.dataValidationSuccess();
        }
    }

    void handleSelectAccountTypeClick() {
        if (null != view) {
            NavigationTarget navigationTarget = NavigationTarget.to(PayNavigatorTarget.ACCOUNT_TYPE_LIST);
            navigationTarget.withParam(Constants.BundleKeys.SELECTED_ACCOUNT_TYPE_CODE, view.getSelectedAccountTypeCode());
            mNavigationRouter.navigateWithResult(navigationTarget).subscribe(
                    navigationResult -> mNavigationResult = navigationResult
                    ,throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
            );
        }
    }

    void handleSelectBranchCodeClick() {
        NavigationTarget navigationTarget = NavigationTarget.to(PayNavigatorTarget.BRANCH_LIST);
        SelectedBankViewModel selectedBankViewModel = null;
        if (view != null) {
            selectedBankViewModel = view.getSelectedBankViewModel();
        }
        if (null != selectedBankViewModel) {
            List<BankBranchViewModel> branchList = selectedBankViewModel.getBranchList();
            navigationTarget.withParam(SELECTED_BANK_ID, selectedBankViewModel.getBankCode());
            if (null != branchList && branchList.size() > 0) {
                navigationTarget.withParam(SELECTED_BANK_BRANCH_LIST, branchList);
            }
            mNavigationRouter.navigateWithResult(navigationTarget).subscribe(
                    navigationResult -> mNavigationResult = navigationResult
                    ,throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
            );
        }
    }

    public void checkToggleForShapId() {
        boolean isRPPFeatureDisabled = mFeatureSetController.isFeatureDisabled(FeatureConstants.PAY_SHAP_ID);
        if (isRPPFeatureDisabled && view!=null) {
            view.hideAddShapIdSection();
        }
    }

    public void setMatchBackNumberErrorMap(Map<Integer, String> matchBackNumberErrorMap, List<Integer> matchBackNumberAvoidList) {
        matchBackNumberAvoidList.forEach(matchBackNumberErrorMap.keySet()::remove);
        mMatchBackNumberErrorMap = matchBackNumberErrorMap;
    }

}
