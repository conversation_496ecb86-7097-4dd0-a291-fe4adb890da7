package za.co.nedbank.ui.view.retention;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class RetentionWelcomePresenter extends NBBasePresenter<RetentionWelcomeView> {

    private NavigationRouter navigationRouter;
    private final ApplicationStorage applicationStorage;
    private final Analytics analytics;

    @Inject
    RetentionWelcomePresenter(NavigationRouter navigationRouter, ApplicationStorage applicationStorage, final Analytics analytics) {
        this.navigationRouter = navigationRouter;
        this.applicationStorage = applicationStorage;
        this.analytics = analytics;
    }

    @Override
    protected void onBind() {
        super.onBind();
        sendPageEvent();
    }

    public void handleReadyClick() {
        analytics.sendEvent(AppTracking.RETENTION_CLICK_WELCOME_START, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.RETENTION_TASK_SELECTION_ACTIVITY));
    }

    public void handleSkipClick() {
        analytics.sendEvent(AppTracking.RETENTION_CLICK_WELCOME_SKIP, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        if(view!=null) {
            view.close();
        }
    }

    public void updateRetentionStatus(){
        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, false);
    }

    private void sendPageEvent() {
        analytics.sendState(AppTracking.RETENTION_SCREEN_WELCOME);
    }

}
