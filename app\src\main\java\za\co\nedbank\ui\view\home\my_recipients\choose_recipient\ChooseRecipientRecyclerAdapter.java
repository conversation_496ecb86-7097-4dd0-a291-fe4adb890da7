/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients.choose_recipient;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import za.co.nedbank.R;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.FragmentDialogChooseRecipientCellViewBinding;

/**
 * Created by charurani on 23-08-2017.
 */

public class ChooseRecipientRecyclerAdapter extends RecyclerView.Adapter<ChooseRecipientRecyclerAdapter.ChooseRecipientItemViewHolder> {

    private final ChooseRecipientsViewModel mChooseRecipientsViewModel;
    private final IChooseRecipientListItemListener mIChooseRecipientListItemListener;

    public ChooseRecipientRecyclerAdapter(ChooseRecipientsViewModel chooseRecipientsViewModel, IChooseRecipientListItemListener iChooseRecipientListItemListener) {
        this.mChooseRecipientsViewModel = chooseRecipientsViewModel;
        this.mIChooseRecipientListItemListener = iChooseRecipientListItemListener;
    }

    @Override
    public ChooseRecipientItemViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        FragmentDialogChooseRecipientCellViewBinding binding = FragmentDialogChooseRecipientCellViewBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ChooseRecipientItemViewHolder(binding, mIChooseRecipientListItemListener);
    }

    @Override
    public void onBindViewHolder(ChooseRecipientItemViewHolder holder, int position) {
        String beneficiaryType = mChooseRecipientsViewModel.getBfDetails().get(position).getBeneficiaryType();
        if (beneficiaryType.equalsIgnoreCase(BeneficiaryConstants.BENEFICIARY_TYPE_PREPAID)) {
            holder.getRecipientTypeImageView().setImageResource(R.drawable.ic_my_recipient_mobile_type);
            holder.getRecipientMobileOrBankTextView().setText(mChooseRecipientsViewModel.getBfDetails().get(position).getAccountNumber());
        } else if (AppUtility.isCreditCardAccount(mChooseRecipientsViewModel.getBfDetails().get(position).getAccountType())) {
            holder.getRecipientTypeImageView().setImageResource(R.drawable.ic_my_recipient_credit_card_type);
            holder.getRecipientMobileOrBankTextView().setText(mChooseRecipientsViewModel.getBfDetails().get(position).getAccountNumber());
        } else if (beneficiaryType.equalsIgnoreCase(BeneficiaryConstants.BENEFICIARY_TYPE_INTERNAL)
                || beneficiaryType.equalsIgnoreCase(BeneficiaryConstants.BENEFICIARY_TYPE_EXTERNAL)) {
            holder.getRecipientTypeImageView().setImageResource(R.drawable.my_recipient_bank_type);
            holder.getRecipientMobileOrBankTextView().setText(mChooseRecipientsViewModel.getBfDetails().get(position).getAccountNumber()
                    + StringUtils.COMMA + StringUtils.SPACE + mChooseRecipientsViewModel.getBfDetails().get(position).getBankName());
        }
        if (mChooseRecipientsViewModel.getCurrentBeneficiarySelection() == position) {
            ViewUtils.showViews(holder.getSelectorImageView());
        } else {
            ViewUtils.setInvisibleAction(holder.getSelectorImageView());
        }
        holder.mRootLayout.setOnClickListener(v -> holder.onRootLayoutSelected());
    }

    @Override
    public int getItemCount() {
        return mChooseRecipientsViewModel != null && mChooseRecipientsViewModel.getBfDetails() != null
                ? mChooseRecipientsViewModel.getBfDetails().size() : 0;
    }

    public class ChooseRecipientItemViewHolder extends RecyclerView.ViewHolder {

        ImageView mRecipientTypeImageView;
        TextView mRecipientMobileOrBankTextView;
        LinearLayout mRootLayout;
        ImageView mSelectorImageView;

        private final IChooseRecipientListItemListener mIChooseRecipientListItemListener;

        public ChooseRecipientItemViewHolder(FragmentDialogChooseRecipientCellViewBinding binding, IChooseRecipientListItemListener iChooseRecipientListItemListener) {
            super(binding.getRoot());
            this.mIChooseRecipientListItemListener = iChooseRecipientListItemListener;
            this.mRecipientTypeImageView = binding.chooseRecipientListRecipientTypeIv;
            this.mRecipientMobileOrBankTextView = binding.chooseRecipientListRecipientAccountOrMobileTv;
            this.mRootLayout = binding.chooseRecipientCellRootLl;
            this.mSelectorImageView = binding.chooseRecipientSelectorIv;
        }

        public ImageView getRecipientTypeImageView() {
            return mRecipientTypeImageView;
        }

        public TextView getRecipientMobileOrBankTextView() {
            return mRecipientMobileOrBankTextView;
        }

        public ImageView getSelectorImageView() {
            return mSelectorImageView;
        }

        public void onRootLayoutSelected() {
            ViewUtils.showViews(this.getSelectorImageView());
            if (mIChooseRecipientListItemListener != null) {
                mIChooseRecipientListItemListener.selectedListItem(getAdapterPosition());
            }
        }
    }
}
