package za.co.nedbank.ui.view.retention.feedback;

import android.os.Bundle;
import android.view.View;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityRetentionFeedbackBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.domain.model.retention.RetentionFeedbackType;

public class RetentionFeedbackActivity extends NBBaseActivity implements RetentionFeedbackView {

    @Inject
    RetentionFeedbackPresenter presenter;
    private ActivityRetentionFeedbackBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRetentionFeedbackBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        binding.ivRatingVeryBad.setOnClickListener(this::ratingClicked);
        binding.ivRatingBad.setOnClickListener(this::ratingClicked);
        binding.ivRatingOkay.setOnClickListener(this::ratingClicked);
        binding.ivRatingGood.setOnClickListener(this::ratingClicked);
        binding.ivRatingExcellent.setOnClickListener(this::ratingClicked);
        binding.feedbackSubmitButton.setOnClickListener(v -> presenter.submitClicked());
        binding.feedbackNoThanks.setOnClickListener(v -> presenter.noThanksButtonClicked());
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.bind(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        presenter.unbind();
    }

    void ratingClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_rating_very_bad:
                presenter.ratingClicked(RetentionFeedbackType.VERY_BAD);
                break;
            case R.id.iv_rating_bad:
                presenter.ratingClicked(RetentionFeedbackType.BAD);
                break;
            case R.id.iv_rating_okay:
                presenter.ratingClicked(RetentionFeedbackType.OKAY);
                break;
            case R.id.iv_rating_good:
                presenter.ratingClicked(RetentionFeedbackType.GOOD);
                break;
            case R.id.iv_rating_excellent:
                presenter.ratingClicked(RetentionFeedbackType.EXCELLENT);
                break;
            default:
                break;

        }
    }

    @Override
    public void setSubmitButtonEnabled(boolean enabled) {
        binding.feedbackSubmitButton.setEnabled(enabled);
    }

    @Override
    public void showSelectedFeedback(RetentionFeedbackType type) {
        binding.ivRatingVeryBad.setSelected(type == RetentionFeedbackType.VERY_BAD);
        binding.ivRatingBad.setSelected(type == RetentionFeedbackType.BAD);
        binding.ivRatingOkay.setSelected(type == RetentionFeedbackType.OKAY);
        binding.ivRatingGood.setSelected(type == RetentionFeedbackType.GOOD);
        binding.ivRatingExcellent.setSelected(type == RetentionFeedbackType.EXCELLENT);
    }

    @Override
    public void showSubmitInProgress(boolean inProgress) {
        binding.feedbackSubmitButton.setLoadingVisible(inProgress);
    }

    @Override
    public void showError(String message) {
        //TO DO
    }

    @Override
    public void onBackPressed() {
        // Do nothing to prevent device back button
    }
}
