package za.co.nedbank.ui.view.pop.success_pop;

import android.os.Bundle;
import android.view.View;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivitySuccessPopBinding;
import za.co.nedbank.ui.di.AppDI;

public class SuccessPopActivity extends NBBaseActivity implements SuccessPopView {

    @Inject
    SuccessPopPresenter mPresenter;

    String screenType;
    private boolean isFromRecentPaymentFlow = false;
    private  boolean isSopFromPayDone=false;
    private boolean isLandingToOverView=false;
    private boolean isFromRecipientHistory = false;


    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivitySuccessPopBinding binding = ActivitySuccessPopBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        mPresenter.bind(this);

        if (getIntent() != null) {
            screenType = getIntent().getStringExtra(Constants.EXTRAS.SCREEN_TYPE);
            isFromRecentPaymentFlow = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_RECENT_PAYMENT_FLOW, false);
            isSopFromPayDone = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_SOP_FROM_PAY_DONE, false);
            isLandingToOverView=getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_LANDING_ON_OVERVIEW, false);
            isFromRecipientHistory = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_FROM_RECIPIENT_HISTORY, false);

        }

        if (Constants.API_SUCCESS.equalsIgnoreCase(screenType)) {
            binding.llSuccess.setVisibility(View.VISIBLE);
            binding.llFailure.setVisibility(View.GONE);
            binding.doneBtn.setText(getResources().getString(R.string.done));
        } else {
            binding.llSuccess.setVisibility(View.GONE);
            binding.llFailure.setVisibility(View.VISIBLE);
            binding.doneBtn.setText(getResources().getString(R.string.close));
        }
        binding.doneBtn.setOnClickListener(v -> onDoneBtnClick());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.unbind();
    }

    public void onDoneBtnClick() {
        if (isFromRecentPaymentFlow && !isLandingToOverView) {
            mPresenter.navigateToPayLandingScreen();

        }
        else if(isSopFromPayDone){
            mPresenter.navigateToPayDoneScreen();
        }
        else {
            mPresenter.navigateToHomeScreen(isLandingToOverView, isFromRecipientHistory);
        }
    }
}