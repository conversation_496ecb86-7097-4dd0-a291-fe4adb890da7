/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.mapper.money_requests;

import javax.inject.Inject;

import za.co.nedbank.services.domain.mapper.MetaDataEntityToDataMapper;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestDataEntity;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsActionResponseEntity;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestDataModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsActionDataModel;

/**
 * Created by swapnil.gawande on 2/25/2018.
 */

public class MoneyRequestsActionResponseEntityToDataMapper {
    private final MetaDataEntityToDataMapper mMetadataEntityToDataMapper;

    @Inject
    MoneyRequestsActionResponseEntityToDataMapper(MetaDataEntityToDataMapper metadataEntityToDataMapper) {
        this.mMetadataEntityToDataMapper = metadataEntityToDataMapper;
    }

    private MoneyRequestDataModel mapMoneyRequestDataEntityToDataModel(MoneyRequestDataEntity moneyRequestDataEntity) {
        MoneyRequestDataModel moneyRequestDataModel = new MoneyRequestDataModel();
        if (moneyRequestDataEntity != null) {
            moneyRequestDataModel.setReferenceNumber(moneyRequestDataEntity.getReferenceNumber());
            moneyRequestDataModel.setRequestStatus(moneyRequestDataEntity.getRequestStatus());
        }
        return moneyRequestDataModel;
    }


    public MoneyRequestsActionDataModel mapMoneyRequestsMainResponseEntityToDataModel(MoneyRequestsActionResponseEntity moneyRequestsActionResponseEntity) {
        MoneyRequestsActionDataModel moneyRequestsActionDataModel = new MoneyRequestsActionDataModel();
        if (moneyRequestsActionResponseEntity != null) {
            MoneyRequestDataModel moneyRequestDataModel = mapMoneyRequestDataEntityToDataModel(moneyRequestsActionResponseEntity.getMoneyRequestDataEntity());
            moneyRequestsActionDataModel.setData(moneyRequestDataModel);
            moneyRequestsActionDataModel.setMetadataModel(mMetadataEntityToDataMapper.mapMetaData(moneyRequestsActionResponseEntity.getMetadataEntity()));
        }
        return moneyRequestsActionDataModel;
    }
}
