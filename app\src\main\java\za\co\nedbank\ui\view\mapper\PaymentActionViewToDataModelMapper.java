package za.co.nedbank.ui.view.mapper;

import javax.inject.Inject;

import za.co.nedbank.ui.domain.model.money_request.PaymentActionDataModel;
import za.co.nedbank.ui.view.model.PaymentActionViewModel;

/**
 * Created by mayuri.birajdar on 31-05-2018.
 */

public class PaymentActionViewToDataModelMapper {
    @Inject
    PaymentActionViewToDataModelMapper() {
    }

    public PaymentActionDataModel mapPaymentActionViewToData(PaymentActionViewModel paymentActionViewModel) {
        PaymentActionDataModel paymentActionDataModel = new PaymentActionDataModel();
        paymentActionDataModel.setPayerAccountNumber(paymentActionViewModel.getPayerAccountNumber());
        paymentActionDataModel.setPayerAccountType(paymentActionViewModel.getPayerAccountType());
        paymentActionDataModel.setPayerDescription(paymentActionViewModel.getPayerDescription());
        paymentActionDataModel.setPaymentBatchId(paymentActionViewModel.getPaymentBatchId());
        paymentActionDataModel.setPaymentRequestId(paymentActionViewModel.getPaymentRequestId());
        paymentActionDataModel.setPaymentRequestAction(paymentActionViewModel.getPaymentRequestAction());
        paymentActionDataModel.setProcessAmount(paymentActionViewModel.getProcessAmount());
        return paymentActionDataModel;
    }

}
