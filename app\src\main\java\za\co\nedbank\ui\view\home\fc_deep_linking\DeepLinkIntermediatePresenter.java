package za.co.nedbank.ui.view.home.fc_deep_linking;

import static java.lang.Long.parseLong;
import static za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA;
import static za.co.nedbank.core.Constants.IS_FROM_NTF_FLOW;
import static za.co.nedbank.core.Constants.POCKETS_ACCOUNT;
import static za.co.nedbank.core.Constants.PRODUCT_ID;
import static za.co.nedbank.core.Constants.ZERO_TIME_FORMAT;
import static za.co.nedbank.core.data.storage.StorageKeys.CLIENT_TYPE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_BIRTH_DATE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_CLIENT_TYPE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_PRE_LOGIN_DEEP_LINK;
import static za.co.nedbank.core.payment.recent.Constants.YES_CODE;
import static za.co.nedbank.core.view.media_content.DynamicFeatureCardDetailEnum.INSURANCE_PERSONAL_ACCIDENT_COVER;
import static za.co.nedbank.core.view.media_content.DynamicFeatureCardDetailEnum.INSURANCE_PERSONAL_ACCIDENT_COVER_QUOTES;
import static za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingValue.ANALYTICS.DIGITAL_ADOPTION_PRODUCT;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.APPLY_BUSINESS_ALL_PRODUCTS_PARAM;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.PRODUCT_FAMILY_ZERO;
import static za.co.nedbank.enroll_v2.view.fica.error.FicaErrorHandler.FicaResultErrorCodes.SALARY_NOT_UPDATED;
import static za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountConstants.INVESTMENTS;
import static za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountConstants.NTF_CLIENT_TYPE;
import static za.co.nedbank.payment.opennewinvaccount.view.suggestion.SuggestionPresenter.IS_NOTICE_DEPOSIT;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.ACCIDENTAL_INSURANCE_PRODUCT_ID;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.BundleKeys.FROM_PERSONAL_ACCIDENT_DEEPLINK;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.Observable;
import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.FicaProductFlow;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.ResponseStorageKey;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.convochatbot.view.ChatbotConstants;
import za.co.nedbank.core.data.accounts.model.AccountDto;
import za.co.nedbank.core.data.accounts.model.AccountsContainerDto;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.data.storage.StorageUtility;
import za.co.nedbank.core.domain.CachableValue;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.accounts.AccountDetailData;
import za.co.nedbank.core.domain.model.profile.UserProfile;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetEmcertIdUseCase;
import za.co.nedbank.core.domain.usecase.GetTermsAndConditionsSectionDetailsUseCase;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.accounts.GetAccountDetailUseCase;
import za.co.nedbank.core.domain.usecase.accounts.GetSingleAccountUseCase;
import za.co.nedbank.core.domain.usecase.enrol.LoginSecurityUseCase;
import za.co.nedbank.core.domain.usecase.investmentonline.GetFicaStatusUseCase;
import za.co.nedbank.core.domain.usecase.moa.ProductShortDescriptionUseCase;
import za.co.nedbank.core.domain.usecase.moa.ProductUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetProfileUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.terms.TermsAndConditionClassification;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.ApplicantIdForeignCheckValidator;
import za.co.nedbank.core.view.mapper.AccountDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.moa.ProductDescriptionResponseDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.moa.ProductRequestViewModelToDataMapper;
import za.co.nedbank.core.view.mapper.moa.ProductResponseDataToViewModelMapper;
import za.co.nedbank.core.view.media_content.DynamicFeatureCardDetailEnum;
import za.co.nedbank.core.view.model.AccountDetailViewModel;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.core.view.model.InvestmentSwitchingViewModel;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.moa.ProductDataViewModel;
import za.co.nedbank.core.view.model.moa.ProductRequestViewModel;
import za.co.nedbank.core.view.model.moa.ProductResponseDataViewModel;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.domain.usecases.apply_flow_sbs.products.GetProductsUseCase;
import za.co.nedbank.enroll_v2.domain.usecases.fica.AcquisitionUseCase;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingValue;
import za.co.nedbank.enroll_v2.utils.ProductsUtil;
import za.co.nedbank.enroll_v2.view.applications.model.applicationList.ApplicationListResponseViewModel;
import za.co.nedbank.enroll_v2.view.applications.model.applicationList.CaseViewModel;
import za.co.nedbank.enroll_v2.view.applications.usecases.ApplicationListUseCase;
import za.co.nedbank.enroll_v2.view.mapper.apply_flow.products.response.GetProductsDataToViewModelMapper;
import za.co.nedbank.enroll_v2.view.model.apply_flow.products.response.ProductResponseViewModel;
import za.co.nedbank.enroll_v2.view.model.fica.ClientDeviceInfoRequestViewModel;
import za.co.nedbank.loans.common.navigator.LoansNavigatorTarget;
import za.co.nedbank.loans.preapprovedaccountsoffers.navigator.PreApprovedOffersNavigationTarget;
import za.co.nedbank.payment.atm.navigator.AtmNavigatorTarget;
import za.co.nedbank.payment.common.domain.usecases.GetDefaultAccountUseCase;
import za.co.nedbank.payment.common.view.tracking.PaymentsTrackingEvent;
import za.co.nedbank.payment.ngi.navigator.NgiNavigatorTarget;
import za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountConstants;
import za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountTarget;
import za.co.nedbank.payment.opennewinvaccount.domain.OpenNewAccUserEntriesViewModel;
import za.co.nedbank.payment.opennewinvaccount.domain.datamodel.ProductDataModel;
import za.co.nedbank.payment.opennewinvaccount.domain.mapper.AllProductResponseDataModelToViewModelMapper;
import za.co.nedbank.payment.opennewinvaccount.domain.usecase.GetAllEverydayAndTaxfreeProductsUseCase;
import za.co.nedbank.payment.opennewinvaccount.view.model.AllProductsResponseViewModel;
import za.co.nedbank.payment.opennewinvaccount.view.model.AllProductsViewModel;
import za.co.nedbank.payment.vas.common.domain.usecase.GetVasOfferingsUseCase;
import za.co.nedbank.payment.vas.common.utils.VasConstants;
import za.co.nedbank.payment.vas.common.view.mapper.response.tag.TagItemDataToViewModelMapper;
import za.co.nedbank.payment.vas.common.view.model.request.tag.TagIdentifierRequestViewModel;
import za.co.nedbank.payment.vas.common.view.model.response.tag.TagItemViewModel;
import za.co.nedbank.payment.vas.vouchers.model.VoucherPurchaseViewModel;
import za.co.nedbank.payment.vas.vouchers.navigator.VouchersNavigatorTarget;
import za.co.nedbank.payment.vas.vouchers.utils.VoucherConstants;
import za.co.nedbank.profile.view.ProfileConstants;
import za.co.nedbank.profile.view.navigation.ProfileNavigationTarget;
import za.co.nedbank.services.domain.model.branchcode.BranchCodeDataModel;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.usecase.branchcode.BranchCodeUseCase;
import za.co.nedbank.services.domain.usecase.cards.GetAccountsUseCase;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.services.insurance.domain.usecase.common.GetAllowedCoverAmountUseCase;
import za.co.nedbank.services.insurance.domain.usecase.common.GetAppVersionsUseCase;
import za.co.nedbank.services.insurance.domain.usecase.my_cover.NIFPMyCoverAllowCoverUseCase;
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants;
import za.co.nedbank.services.insurance.view.other.enums.generic.InsuranceProductType;
import za.co.nedbank.services.insurance.view.other.mapper.dashboard.amount.AllowedAmountResponseDataToViewMapper;
import za.co.nedbank.services.insurance.view.other.mapper.dashboard.cover_amount.AllowedCoverAmountResponseDataToViewModelMapper;
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.amount.AllowedAmountResponseViewModel;
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.cover_amount.AllowedCoverAmountResponseViewModel;
import za.co.nedbank.services.insurance.view.other.model.response.entry.app_update.AppVersionViewModel;
import za.co.nedbank.services.insurance.view.other.model.response.generic.insured_property.NonNedbankInsuredPropertiesViewModel;
import za.co.nedbank.services.view.account.branchcode.model.BranchCodeViewModel;
import za.co.nedbank.services.view.mapper.BranchCodeDataModelToBranchCodeViewModelMapper;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class DeepLinkIntermediatePresenter extends NBBasePresenter<DeepLinkIntermediateView> {

    private String mDefaultAccountId;
    private boolean isSavingPocketFlowEnabled;
    private boolean isSalarySwitchFlowEnabled;
    private final ApplicationStorage mMemoryStorage;
    private UserDetailViewModel mUserDetailViewModel;
    private final Analytics mAnalytics;
    private final NavigationRouter mNavigationRouter;
    private final BranchCodeUseCase mBranchCodeUseCase;
    private final GetProductsUseCase mGetProductsUseCase;
    private final GetProfileUseCase mGetProfileUseCase;
    private final AcquisitionUseCase mAcquisitionUseCase;
    private final GetAccountsUseCase mGetAccountsUseCase;
    private final ApplicationStorage mApplicationStorage;
    private final GetEmcertIdUseCase mGetEmcertIdUseCase;
    private final LoginSecurityUseCase mLoginSecurityUseCase;
    private final GetFicaStatusUseCase mGetFicaStatusUseCase;
    private final FeatureSetController mFeatureSetController;
    private final StorageUtility storageUtility;
    private final GetUserDetailUseCase mGetUserDetailUseCase;
    private List<BranchCodeViewModel> mBranchCodeViewModelList;
    private final GetVasOfferingsUseCase mGetVasOfferingsUseCase;
    private final GetAccountDetailUseCase mGetAccountDetailUseCase;
    private final GetDefaultAccountUseCase mGetDefaultAccountUseCase;
    private final ProductShortDescriptionUseCase productShortDescriptionUseCase;
    private final GetSingleAccountUseCase mGetSingleAccountUseCase;
    private final TagItemDataToViewModelMapper mTagItemDataToViewModelMapper;
    private final UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper;
    private final AccountDetailDataToViewModelMapper mAccountDetailDataToViewModelMapper;
    private final BranchCodeDataModelToBranchCodeViewModelMapper mModelToBranchCodeViewModelMapper;
    private final OpenNewAccUserEntriesViewModel userEntriesViewModel = new OpenNewAccUserEntriesViewModel();
    private final GetAllEverydayAndTaxfreeProductsUseCase mGetAllEverydayAndTaxfreeProductsUseCase;
    private final AllProductResponseDataModelToViewModelMapper mAllProductResponseDataModelToViewModelMapper;
    private final ProductRequestViewModelToDataMapper mProductRequestViewModelToDataMapper;
    private final ProductUseCase mProductUseCase;
    private final ProductDescriptionResponseDataToViewModelMapper productDescriptionResponseDataToViewModelMapper;
    private final ProductResponseDataToViewModelMapper mProductResponseDataToViewModelMapper;
    private final GetOverviewUseCase mGetOverviewUseCase;
    private final NIFPMyCoverAllowCoverUseCase mNifpMyCoverAllowCoverUseCase;
    private final AllowedAmountResponseDataToViewMapper mNifpMyCoverAmountResponseDataToViewModelMapper;
    private final GetTermsAndConditionsSectionDetailsUseCase mTermsAndConditionsUseCase;
    private static final String TAG = DeepLinkIntermediatePresenter.class.getSimpleName();

    private GetAllowedCoverAmountUseCase mGetAllowedCoverAmountUseCase;
    private AllowedCoverAmountResponseDataToViewModelMapper mAllowedCoverAmountResponseDataToViewModelMapper;

    private final GetProductsDataToViewModelMapper mProductsDataToViewModelMapper;
    private final ApplicantIdForeignCheckValidator mApplicantIdForeignCheckValidator;
    private int myPocketCount = 0;
    private int maxPocketCount = 0;
    List<AccountViewModel> mInsuranceAccountList;

    String conversationId = AppTracking.MY_ACCOUNTS_OVERVIEW;

    private AppVersionViewModel appVersionViewModel;
    List<NonNedbankInsuredPropertiesViewModel> nonNedbankInsuredPropertiesViewModels;
    private final ApplicationListUseCase applicationListUseCase;
    private final ProductsUtil productsUtil;
    @Inject
    DeepLinkIntermediatePresenter(final ApplicationStorage storage,
                                  final Analytics analytics,
                                  final StorageUtility storageUtility,
                                  final NavigationRouter navigationRouter,
                                  final GetProfileUseCase getProfileUseCase,
                                  final BranchCodeUseCase branchCodeUseCase,
                                  final AcquisitionUseCase acquisitionUseCase,
                                  final GetEmcertIdUseCase getEmcertIdUseCase,
                                  final GetAccountsUseCase getAccountsUseCase,
                                  final GetAppVersionsUseCase getAppVersionsUseCase,
                                  final GetUserDetailUseCase getUserDetailUseCase,
                                  final GetFicaStatusUseCase getFicaStatusUseCase,
                                  final FeatureSetController featureSetController,
                                  final LoginSecurityUseCase loginSecurityUseCase,
                                  final GetVasOfferingsUseCase getVasOfferingsUseCase,
                                  final GetAccountDetailUseCase getAccountDetailUseCase,
                                  final GetDefaultAccountUseCase getDefaultAccountUseCase,
                                  final GetSingleAccountUseCase getSingleAccountUseCase,
                                  @Named("memory") final ApplicationStorage memoryStorage,
                                  ProductShortDescriptionUseCase productShortDescriptionUseCase, final TagItemDataToViewModelMapper tagItemDataToViewModelMapper,
                                  final UserDetailDataToViewModelMapper userDetailDataToViewModelMapper,
                                  final AccountDetailDataToViewModelMapper accountDetailDataToViewModelMapper,
                                  final BranchCodeDataModelToBranchCodeViewModelMapper modelToBranchCodeViewModelMapper,
                                  final GetAllEverydayAndTaxfreeProductsUseCase getAllEverydayAndTaxfreeProductsUseCase,
                                  final AllProductResponseDataModelToViewModelMapper allProductResponseDataModelToViewModelMapper,
                                  final ProductRequestViewModelToDataMapper productRequestViewModelToDataMapper,
                                  final ProductUseCase productUseCase,
                                  final GetOverviewUseCase getOverviewUseCase,
                                  final NIFPMyCoverAllowCoverUseCase nifpMyCoverAllowCoverUseCase,
                                  final AllowedAmountResponseDataToViewMapper nifpMyCoverAmountResponseDataToViewModelMapper,
                                  final GetProductsUseCase getProductsUseCase,
                                  final GetProductsDataToViewModelMapper productsDataToViewModelMapper,
                                  final ProductDescriptionResponseDataToViewModelMapper productDescriptionResponseDataToViewModelMapper,
                                  final ProductResponseDataToViewModelMapper productResponseDataToViewModelMapper,
                                  final GetAllowedCoverAmountUseCase mGetAllowedCoverAmountUseCase,
                                  final AllowedCoverAmountResponseDataToViewModelMapper mAllowedCoverAmountResponseDataToViewModelMapper,
                                  GetTermsAndConditionsSectionDetailsUseCase mTermsAndConditionsUseCase, ApplicantIdForeignCheckValidator mApplicantIdForeignCheckValidator, ApplicationListUseCase applicationListUseCase, ProductsUtil productsUtil) {
        this.mApplicationStorage = storage;
        this.mAnalytics = analytics;
        this.mMemoryStorage = memoryStorage;
        this.mNavigationRouter = navigationRouter;
        this.storageUtility = storageUtility;
        this.mGetProfileUseCase = getProfileUseCase;
        this.mBranchCodeUseCase = branchCodeUseCase;
        this.mGetAccountsUseCase = getAccountsUseCase;
        this.mAcquisitionUseCase = acquisitionUseCase;
        this.mGetEmcertIdUseCase = getEmcertIdUseCase;
        this.mGetUserDetailUseCase = getUserDetailUseCase;
        this.mLoginSecurityUseCase = loginSecurityUseCase;
        this.mGetFicaStatusUseCase = getFicaStatusUseCase;
        this.mGetSingleAccountUseCase = getSingleAccountUseCase;
        this.mFeatureSetController = featureSetController;
        this.mGetVasOfferingsUseCase = getVasOfferingsUseCase;
        this.mGetAccountDetailUseCase = getAccountDetailUseCase;
        this.mGetDefaultAccountUseCase = getDefaultAccountUseCase;
        this.productShortDescriptionUseCase = productShortDescriptionUseCase;
        this.mTagItemDataToViewModelMapper = tagItemDataToViewModelMapper;
        this.mUserDetailDataToViewModelMapper = userDetailDataToViewModelMapper;
        this.mModelToBranchCodeViewModelMapper = modelToBranchCodeViewModelMapper;
        this.mAccountDetailDataToViewModelMapper = accountDetailDataToViewModelMapper;
        this.mGetAllEverydayAndTaxfreeProductsUseCase = getAllEverydayAndTaxfreeProductsUseCase;
        this.mAllProductResponseDataModelToViewModelMapper = allProductResponseDataModelToViewModelMapper;
        this.mProductRequestViewModelToDataMapper = productRequestViewModelToDataMapper;
        this.mProductUseCase = productUseCase;
        this.productDescriptionResponseDataToViewModelMapper = productDescriptionResponseDataToViewModelMapper;
        this.mProductResponseDataToViewModelMapper = productResponseDataToViewModelMapper;
        this.mGetOverviewUseCase = getOverviewUseCase;
        this.mNifpMyCoverAllowCoverUseCase = nifpMyCoverAllowCoverUseCase;
        this.mNifpMyCoverAmountResponseDataToViewModelMapper = nifpMyCoverAmountResponseDataToViewModelMapper;
        this.mGetProductsUseCase = getProductsUseCase;
        this.mProductsDataToViewModelMapper = productsDataToViewModelMapper;
        this.mTermsAndConditionsUseCase = mTermsAndConditionsUseCase;
        this.mApplicantIdForeignCheckValidator = mApplicantIdForeignCheckValidator;
        this.mAllowedCoverAmountResponseDataToViewModelMapper = mAllowedCoverAmountResponseDataToViewModelMapper;
        this.mGetAllowedCoverAmountUseCase = mGetAllowedCoverAmountUseCase;
        this.applicationListUseCase = applicationListUseCase;
        this.productsUtil = productsUtil;
    }

    public void handleFlowForFeature(String featureName,boolean isPrelogin) {
        DynamicFeatureCardDetailEnum featureCardDetailEnum = null;
        try {
            featureCardDetailEnum = DynamicFeatureCardDetailEnum.getEnum(featureName.toUpperCase());
        } catch (IllegalArgumentException exception) {
            NBLogger.d("handleFlowFroFeature", "DynamicFeatureCardDetailEnum can't get value");
        }

        if (featureCardDetailEnum != null) {
            NavigationTarget navigationTarget;
            // check where to take the user
            switch (featureCardDetailEnum) {
                case BUY_VOUCHER:
                    handlingBuyVoucherFlow();
                    break;

                case MOABUSINESS:
                    getBundleProducts(isPrelogin);
                    break;

                case PAY_ME:
                    navigationTarget = NavigationTarget.to(NavigationTarget.PAY_ME);
                    mNavigationRouter.navigateTo(navigationTarget);

                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                    break;

                case INSURANCE:
                    navigationTarget = NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN);
                    mNavigationRouter.navigateTo(navigationTarget);

                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                    break;

                case INSURANCE_FUNERAL:
                    getUserDetailForHouseAndFuneralInsuranceType(InsuranceConstants.InsuranceTypeConstant.FUNERAL);
                    break;

                case INVESTMENT:
                    getUserInfo();
                    break;

                case SALARY_SWITCH:
                    isSalarySwitchFlowEnabled = true;
                    handleSalarySwitch();
                    break;

                case MONEY_TRACKER:
                    mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MONEY_TRACKER_FLOW_NAVIGATION_ACTIVITY));
                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                    break;

                case SAVING_POCKET:
                    isSavingPocketFlowEnabled = true;
                    getAccounts("Bank");
                    break;

                case GREENBACKS:
                    mApplicationStorage.putString(Constants.DEEP_LINK_DASHBOARD_SELECTION, DynamicFeatureCardDetailEnum.GREENBACKS.getName());
                    closeDeepLinkView();
                    break;

                case JOIN_GREENBACKS:
                    //saving notification data in memory to be retrieved in home screen for further navigation
                    mApplicationStorage.putString(Constants.DEEP_LINK_DASHBOARD_SELECTION, DynamicFeatureCardDetailEnum.JOIN_GREENBACKS.getName());
                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                    break;

                case REDEEM_GREENBACKS:
                    //saving notification data in memory to be retrieved in home screen for further navigation
                    mApplicationStorage.putString(Constants.DEEP_LINK_DASHBOARD_SELECTION, DynamicFeatureCardDetailEnum.REDEEM_GREENBACKS.getName());

                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                    break;

                case HOME_BUYING_TOOLKIT:
                    navigationTarget = NavigationTarget.to(NavigationTarget.HOME_LOAN_TOOLKIT)
                            .withIntentFlagClearTopSingleTop(true);
                    mNavigationRouter.navigateTo(navigationTarget);

                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                    break;

                case SHARE_MONEY_APP:
                    handleShareMoneyAppFlow();
                    break;

                case BUY_VAS:
                    handleBuyVasFlow(false);
                    break;

                case LOGIN_AND_SECURITY:
                    mLoginSecurityUseCase.execute(Boolean.FALSE)
                            .compose(bindToLifecycle()).subscribe(o ->
                                            closeDeepLinkView()
                                    , throwable -> {
                                        if (view != null) {
                                            view.showError();
                                        }
                                    }
                            );
                    break;

                case INTERNATIONAL_PAYMENTS:
                    handleInternationalPayments();
                    break;

                case GET_CASH:
                    handleGetCashFlow();
                    break;

                case OPEN_CASA_ACCOUNT:
                    handleOpenCasaAccountFlow();
                    break;

                case LOGIN:
                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                    break;

                case INVESTMENT_PRODUCTS:
                    handleInvestment();
                    break;

                case NEDBANK4ME:
                    handleNedbank4Me();
                    break;

                case OPEN_CASA_ACCOUNT_DEEPLINK:
                    handleOpenCasaAccountFlowForDeepLink();
                    break;

                case BUY_VAS_DEEPLINK:
                    handleBuyVasFlow(true);
                    break;

                case PERSONAL_LOAN:
                    //add the in app flow
                    mApplicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
                    mMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);

                    navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.PERSONAL_LOAN_WISHLIST_ACTIVITY)
                            .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                            .withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.IS_NON_NEDBANK_ID_FLOW, false)
                            .withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.IS_PRE_LOGIN, false);
                    mNavigationRouter.navigateTo(navigationTarget);
                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                    break;

                case LOAN_AND_CREDIT_OPTIONS:
                    mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BORROW_INTENT).withParam(Constants.BUNDLE_KEYS.FLOW_JOURNEY_FLAG, Constants.FLOW_CONSTANTS.POST_LOGIN_MORE_APPLY_BORROW_FLOW)
                            .withParam(za.co.nedbank.enroll_v2.Constants.NFPCreditCardParam.IS_PRE_LOGIN, false));

                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                    break;

                case MY_POCKET:
                    getMyPocketCounts("Bank");
                    break;

                case CREDIT_HEALTH:
                    handleCreditHealthFlow();
                    break;
                case MYCOVER_LIFE:
                    getAccountsForInsuranceFLow(StringUtils.EMPTY_STRING, InsuranceProductType.MY_COVER_LIFE);
                    break;

                case TRANSACTION_PRODUCT:
                    handleTransactionProductFlow();
                    break;

                case EXPAND_CAMPAIGN:
                    handleExpandCampaignDeepLinkFlow();
                    break;

                case INVESTMENT_PRODUCTS_NEW:
                    if(APIInformation.getInstance().isLoggedOut()) {
                        getAllProducts(true);
                    }else{
                        handleInvestment();
                    }
                    break;

                case OFFER_FOR_YOU:
                    handleOfferForYouDeepLinkFlow();
                    break;
                case PAY_MY_BILLS:
                    navigateToBillPayments();
                    break;

                case MYCOVER_PL:
                    getAccountsForInsuranceFLow(InsuranceProductType.PERSONAL_LINES.getProductName(), InsuranceProductType.PERSONAL_LINES);
                    break;

                case INSURANCE_FUNERAL_BUILD_PACKAGE:
                    getAccountsForInsuranceFLow(InsuranceProductType.FUNERAL_OWN_COVER.getProductName(), InsuranceProductType.FUNERAL_OWN_COVER);
                    break;

                case FAMILY_BANKING:
                    navigateToFamilyBanking();
                    break;
                case INSURANCE_QUOTES:
                    getInsuranceAndUserDetail();
                    break;

                case INSURANCE_VEHICLE_HOME_VALUABLES:
                    getUserDetailForHouseAndFuneralInsuranceType(InsuranceConstants.InsuranceTypeConstant.VALUABLES);
                    break;

                case INSURANCE_PERSONAL_ACCIDENT_COVER_QUOTES:
                    getAllowedPersonalAccidentCoverAmount(ACCIDENTAL_INSURANCE_PRODUCT_ID, INSURANCE_PERSONAL_ACCIDENT_COVER_QUOTES.getName());
                    break;
                case INSURANCE_PERSONAL_ACCIDENT_COVER:
                    getAllowedPersonalAccidentCoverAmount(ACCIDENTAL_INSURANCE_PRODUCT_ID, INSURANCE_PERSONAL_ACCIDENT_COVER.getName());
                    break;

                case INSURANCE_HOUSE_CONTENTS:
                    navigateToInsuranceHomeProduct();
                    break;

                case PRODUCT_ONBOARDING:
                    getCaseDetails();
                    break;
                case ENBI_CHAT:
                    openEnbiChatScreen();
                    break;
                case MOA_ONBOARDING:
                    handleMoaRrbProductFlow();
                    break;
                default:
                    closeDeepLinkView();
                    break;
            }
        } else {
            // fallback to home page
            closeDeepLinkView();
        }
    }

    private void openEnbiChatScreen() {
        boolean isChatBotIntroJourneyCompleted = mApplicationStorage.getBoolean(ChatbotConstants.StorageKeys.CHATBOT_INTRO_DISPLAYED, false);
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_DCCHAT)) {
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CHAT_SCREEN)
                    .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true));
        } else {
            if (isChatBotIntroJourneyCompleted) {
                mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CONVO_CHAT_SCREEN)
                        .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                        .withParam(NavigationTarget.CONVERSATION_ID, conversationId)
                        .withIntentSingleTop(true));

            } else {
                mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CHATBOT_INTRODUCTION_SCREEN)
                        .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                        .withParam(NavigationTarget.CONVERSATION_ID, conversationId)
                        .withIntentSingleTop(true));
            }
        }
        closeDeepLinkView();
    }


    private void navigateToInsuranceHomeProduct() {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_EDUCATION_SCREEN)
                .withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, InsuranceProductType.PERSONAL_LINES_HOME_CONTENT)
                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW_FOR_HOME_CONTENT, true)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, InsuranceProductType.PERSONAL_LINES_HOME_CONTENT.getProductId());
        mNavigationRouter.navigateTo(navigationTarget);
        closeDeepLinkView();
    }

    private void handleInsuranceAndUserDetail(CachableValue<Overview> overview){
        if (overview.get() != null && CollectionUtils.isNotEmpty(overview.get().accountsOverviews)) {
            for (AccountsOverview accountsOverview : overview.get().accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.insuranceAccountList != null) {
                    mInsuranceAccountList = accountsOverview.insuranceAccountList;
                }
            }
        }
    }

    public void getInsuranceAndUserDetail() {

        Observable.zip(mGetOverviewUseCase.execute(), mGetUserDetailUseCase.execute(false),
                        (overview, userDetailData) -> {
                            if (overview != null && userDetailData!=null) {
                                handleInsuranceAndUserDetail(overview);
                            }
                            return userDetailData;
                        }).compose(bindToLifecycle())
                .subscribe(this::navigateToInsuranceQuotes,
                        throwable ->
                                closeDeepLinkView()
                );

    }
    private void navigateToInsuranceQuotes(UserDetailData userDetailData) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN)
                .withParam(ServicesNavigationTarget.PARAM_VALID_INSURANCE_POLICY, isInsuranceExist(userDetailData))
                .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, mInsuranceAccountList)
                .withParam(InsuranceConstants.BundleKeys.FROM_DEEPLINK,true);
        mNavigationRouter.navigateTo(navigationTarget);
        closeDeepLinkView();
    }

    boolean isInsuranceExist(UserDetailData userDetailData) {
        boolean isValidRSAId = mApplicantIdForeignCheckValidator.validateInput(userDetailData.getIdOrTaxIdNumber()).isOk();
        boolean isBusinessUser = StringUtils.isNotEmpty(userDetailData.getClientType()) && Integer.parseInt(userDetailData.getClientType())
                > za.co.nedbank.core.Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;
        int age = getAgeFromRsaId(userDetailData.getIdOrTaxIdNumber());
        return (!mFeatureSetController.isFeatureDisabled(FeatureConstants.INSURANCE)
                && age >= FormattingUtil.AGE_LIMIT && !isBusinessUser && isValidRSAId);
    }

    int getAgeFromRsaId(String idOrTaxIdNoStr) {
        if (StringUtils.isNotEmpty(idOrTaxIdNoStr) && idOrTaxIdNoStr.length() > 6) {
            String mBirthDate = AppUtility.getDateOfBirthFromRsaId(idOrTaxIdNoStr);
            return AppUtility.calculateAge(mBirthDate + ZERO_TIME_FORMAT);
        }

        return za.co.nedbank.services.Constants.ZERO;
    }

    private void navigateToFamilyBanking() {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.FAMILY_BANKING_STATUS_LOADING_SCREEN);
        navigationTarget.withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true);
        mNavigationRouter.navigateTo(navigationTarget);
        closeDeepLinkView();
    }


    public void closeDeepLinkView(){
        if (view != null) {
            view.finishActivity();
        }
    }

    public String getRsaId() {
        UserDetailViewModel userData = (UserDetailViewModel) mMemoryStorage
                .getObject(ResponseStorageKey.USERDETAILDATA);
        if (userData != null) {
            return userData.getIdOrTaxIdNumber();
        }
        return null;
    }

    public void getUserDetailForInsurance(int productId, List<AccountViewModel> insuranceAccountList, String insuranceType) {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                            if (userDetail != null) {
                                mMemoryStorage.putObject(ResponseStorageKey.USERDETAILDATA,
                                        mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail));
                                String partyId = userDetail.getIdOrTaxIdNumber();
                                List<String> arrayOfRsaId = new ArrayList<>();
                                if (StringUtils.isNotEmpty(partyId)) {
                                    arrayOfRsaId.add(partyId);
                                }
                                if (INSURANCE_PERSONAL_ACCIDENT_COVER_QUOTES.getName().equalsIgnoreCase(insuranceType) || INSURANCE_PERSONAL_ACCIDENT_COVER.getName().equalsIgnoreCase(insuranceType)) {
                                    getAllowedCoverAmount(productId, partyId, insuranceType);
                                } else {
                                    getNIFPMyCoverAllowedCoverAmountDeepLink(arrayOfRsaId, productId, insuranceAccountList);
                                }
                            }
                        }, throwable -> handleNavigationErrorFlow()
                );
    }

    void getNIFPMyCoverAllowedCoverAmountDeepLink(List<String> arrayOfRsaId, int productId, List<AccountViewModel> insuranceAccountList) {
        mNifpMyCoverAllowCoverUseCase.execute(productId, arrayOfRsaId)
                .compose(bindToLifecycle())
                .subscribe(allowedCoverAmountResponseData -> setNifpMycoverLifeAmount(mNifpMyCoverAmountResponseDataToViewModelMapper
                                .map(allowedCoverAmountResponseData), insuranceAccountList, productId),
                        throwable -> handleNavigationErrorFlow()
                );
    }


    public void setNifpMycoverLifeAmount(List<AllowedAmountResponseViewModel> allowedCoverList, List<AccountViewModel> insuranceAccountList, int productId) {
        if (CollectionUtils.isNotEmpty(allowedCoverList)) {
            AllowedCoverAmountResponseViewModel allowedCover = allowedCoverList.get(za.co.nedbank.core.Constants.ZERO).getAllowedCoverAmountResponseViewModel();
            setMyCoverAllowedCoverAmount(allowedCover, insuranceAccountList,productId);
        } else {
            handleNavigationErrorFlow();
        }
    }

    private void handleNavigationErrorFlow() {
        if (view != null) {
            view.showError();
        }
    }

    public void setMyCoverAllowedCoverAmount(AllowedCoverAmountResponseViewModel allowedCoverAmountResponseViewModel, List<AccountViewModel> insuranceAccountList, int productId) {
        if (allowedCoverAmountResponseViewModel.getAllowedcoveramount() >= InsuranceConstants.InsuranceCoverConstants.MY_COVER_MIN_VALUE) {
            double allowedMaxCoverAmount = allowedCoverAmountResponseViewModel.getAllowedcoveramount();
            double existingCoverAmount = allowedCoverAmountResponseViewModel.getExistingcoveramount();
            if (productId == InsuranceConstants.BUILD_OWN_COVER) {
                navigateToMyCoverLifeInsurance(InsuranceProductType.FUNERAL_OWN_COVER,
                        insuranceAccountList, InsuranceConstants.BUILD_OWN_COVER, allowedMaxCoverAmount, existingCoverAmount);
            } else {
                navigateToMyCoverLifeInsurance(InsuranceProductType.MY_COVER_LIFE,
                        insuranceAccountList, InsuranceConstants.MY_COVER_LIFE_PRODUCT_ID, allowedMaxCoverAmount, existingCoverAmount);
            }
        } else {
            handleNavigationErrorFlow();
        }
    }

    void navigateToMyCoverLifeInsurance(InsuranceProductType insuranceProduct, List<AccountViewModel> accountList,
                                        int productId, double allowedCoverAmount, double existingCoverAmount) {
        NavigationTarget navigationTarget = null;
        if (insuranceProduct == InsuranceProductType.MY_COVER_LIFE) {
            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_NEW_EDUCATION_ACTIVITY);
        } else {
            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_EDUCATION_SCREEN);
        }
        navigationTarget.withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, insuranceProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, productId)
                .withParam(InsuranceConstants.ParamKeys.PARAM_ALLOWED_COVER_AMOUNT, allowedCoverAmount)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EXISTING_COVER_AMOUNT, existingCoverAmount)
                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, accountList);

        mNavigationRouter.navigateTo(navigationTarget);
        closeDeepLinkView();

    }

    private void handleSalarySwitch() {
        mGetDefaultAccountUseCase.execute()
                .compose(bindToLifecycle())
                .flatMap(clientPreferenceDto -> {
                    if (clientPreferenceDto != null && clientPreferenceDto.value != null
                            && !clientPreferenceDto.value.equalsIgnoreCase(GetDefaultAccountUseCase.DEFAULT_ACCOUNT_INVALID_VALUE)) {
                        mDefaultAccountId = clientPreferenceDto.value;
                        NBLogger.d("Default account", mDefaultAccountId);
                    }
                    if (mDefaultAccountId == null)
                        getAccounts("Bank");
                    return mGetSingleAccountUseCase.execute(mDefaultAccountId);
                })
                .subscribe(accountDetails -> {
                            if (mDefaultAccountId != null && accountDetails != null && !za.co.nedbank.services.Constants.SAVINGS_POCKET_PRODUCT_TYPE.equalsIgnoreCase(accountDetails.getProductType())) {
                                handleSalarySwitchLetterFlow(mDefaultAccountId);
                            } else {
                                getAccounts("Bank");
                            }
                        },
                        throwable -> {
                            NBLogger.d("Default account", throwable.getLocalizedMessage());
                        });
    }

    private void getAccounts(String accountType) {
        mGetAccountsUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(accountsContainerDtoList -> {

                    if (isSavingPocketFlowEnabled) {
                        AccountDto accountDto = getSavingPocketAccount(accountsContainerDtoList, accountType);
                        handleSavingPocketFlow(accountDto);
                    } else if (isSalarySwitchFlowEnabled) {
                        AccountDto accountDto = getMaxBalanceAccount(accountsContainerDtoList, accountType);
                        NBLogger.d("Account", accountDto.toString());
                        handleSalarySwitchLetterFlow(accountDto.getId());
                    }
                }, throwable -> {
                    if (view != null) {
                        view.showError();
                    }
                });
    }

    private AccountDto getMaxBalanceAccount(List<AccountsContainerDto>
                                                    accountsContainerDtoList, String accountType) {
        AccountDto maxBalanceAccount = null;
        double maxBalance = 0.0;

        for (AccountsContainerDto accountContainer : accountsContainerDtoList) {

            if (accountContainer != null && accountType != null && accountContainer.name != null
                    && accountContainer.name.equals(accountType)) {
                for (AccountDto accountDto : accountContainer.getAccounts()) {
                    if (accountDto.availableBalance >= maxBalance) {
                        maxBalance = accountDto.availableBalance;
                        maxBalanceAccount = accountDto;
                    }
                }
            }
        }
        return maxBalanceAccount;
    }

    private AccountDto getSavingPocketAccount(List<AccountsContainerDto>
                                                      accountsContainerDtoList, String accountType) {
        AccountDto dummyAccount = null;
        AccountDto savingPocketAccount = null;
        int pocketsCounter = 0;
        int tpAccountCounter = 0;

        for (AccountsContainerDto accountContainer : accountsContainerDtoList) {

            if (accountContainer != null && accountType != null && accountContainer.name.equals(accountType)) {
                for (AccountDto accountDto : accountContainer.getAccounts()) {
                    if (accountDto.getAccountType() != null && accountDto.getProductType() != null
                            && accountDto.accountType.equals(Constants.ACCOUNT_TYPES.SA.getAccountTypeCode())
                            && accountDto.getProductType().equals(POCKETS_ACCOUNT)) {
                        savingPocketAccount = accountDto;
                        pocketsCounter++;
                        break;
                    } else if (savingPocketAccount == null && accountDto.getAccountType() != null && accountDto.getAccountType().equalsIgnoreCase(Constants.ACCOUNT_TYPES.CA.getAccountTypeCode())
                            && !za.co.nedbank.services.Constants.CA_ACCOUNT_PRODUCT_TYPE_011.equals(accountDto.getProductType())
                            && (za.co.nedbank.services.Constants.ACCOUNT_STATUS_TYPE_0.equalsIgnoreCase(accountDto.getAccountStatusCode())
                            || za.co.nedbank.services.Constants.ACCOUNT_STATUS_TYPE_00.equalsIgnoreCase(accountDto.getAccountStatusCode())
                            || za.co.nedbank.services.Constants.ACCOUNT_STATUS_TYPE_09.equalsIgnoreCase(accountDto.getAccountStatusCode())
                            || za.co.nedbank.services.Constants.ACCOUNT_STATUS_TYPE_oo.equalsIgnoreCase(accountDto.getAccountStatusCode()))) {
                        dummyAccount = accountDto;
                        tpAccountCounter++;
                    }
                }

                if (savingPocketAccount == null && !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ONLINE_SAVINGS_POCKET_BUTTON)
                        && pocketsCounter < (za.co.nedbank.services.Constants.MAX_SAVINGS_POCKET * tpAccountCounter)) {
                    savingPocketAccount = dummyAccount;
                }
            }
        }
        return savingPocketAccount;
    }

    public void handleSalarySwitchLetterFlow(String accountId) {
        Observable<UserDetailData> userDetailDataObservable = mGetUserDetailUseCase.execute(false);
        Observable<AccountDetailData> accountDetailDataObservable = mGetAccountDetailUseCase.execute(accountId);
        Observable<List<BranchCodeDataModel>> listObservable = mBranchCodeUseCase.execute();

        if (null != userDetailDataObservable && null != accountDetailDataObservable && listObservable != null) {
            Observable.zip(userDetailDataObservable, accountDetailDataObservable, listObservable,
                            (userDetailData, accountDetailData, branchCodeDataModelList) -> {
                                if (null != userDetailData && null != accountDetailData) {
                                    this.mBranchCodeViewModelList = mModelToBranchCodeViewModelMapper.mapBranchCodeViewModel(branchCodeDataModelList);
                                    this.mUserDetailViewModel = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData);
                                    return mAccountDetailDataToViewModelMapper.mapAccountDetailDataToViewModel(accountDetailData);
                                }
                                return null;
                            }).compose(bindToLifecycle())
                    .subscribe(accountDetailViewModel -> {
                                if (null != view && null != accountDetailViewModel) {
                                    NBLogger.d("Salary Switch Account", accountDetailViewModel.getAccountNumber());
                                    String name = StringUtils.EMPTY_STRING;

                                    if (mUserDetailViewModel != null) {
                                        if (!StringUtils.isNullOrEmpty(mUserDetailViewModel.getFirstName())) {
                                            name = mUserDetailViewModel.getFirstName();
                                        }

                                        if (!StringUtils.isNullOrEmpty(mUserDetailViewModel.getSurname())) {
                                            name = name + StringUtils.SPACE + mUserDetailViewModel.getSurname();
                                        }
                                    }
                                    accountDetailViewModel.setAccountHolderName(name);
                                    navigateToSalarySwitch(accountId, accountDetailViewModel);

                                    // fallback to home page
                                    closeDeepLinkView();
                                } else {
                                    if (view != null)
                                        view.showError();
                                }
                            },
                            error -> {
                                if (view != null)
                                    view.showError();
                            });
        }
    }

    private void navigateToSalarySwitch(String accountId, AccountDetailViewModel accountDetailViewModel) {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.SELECT_SALARY_SWITCH_ACCOUNT_REQUEST);
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_NUMBER, accountDetailViewModel.getAccountNumber());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_NAME, accountDetailViewModel.getAccountHolderName());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_TYPE, accountDetailViewModel.getAccountType());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_BRANCH_CODE, getBranchCodeValue(accountDetailViewModel.getAccountType()));
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_EMAIL, mUserDetailViewModel.getEmailAddress());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ID_OR_TAX_ID_NUMBER, mUserDetailViewModel.getIdOrTaxIdNumberString());
        navigationTarget.withParam(za.co.nedbank.services.Constants.ITEM_ACCOUNT_ID, accountId);
        navigationTarget.withParam(za.co.nedbank.core.Constants.NAV_TO_DASHBOARD, true);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    public String getBranchCodeValue(String accountType) {
        if (mBranchCodeViewModelList != null && mBranchCodeViewModelList.size() > 0) {
            for (int i = 0; i < mBranchCodeViewModelList.size(); i++) {
                if (accountType.contains(mBranchCodeViewModelList.get(i).getAccountType())) {
                    return mBranchCodeViewModelList.get(i).getBranchCode();
                }
            }
        }
        return StringUtils.EMPTY_STRING;
    }

    public void handlingBuyVoucherFlow() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    UserDetailViewModel userDetailVM = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail);
                    openBuyVoucherFlow(userDetailVM.getBirthDate());
                }, throwable -> {
                    if (view != null) {
                        view.showError();
                    }
                });

    }

    private void openBuyVoucherFlow(String birthDateParam) {
        mGetVasOfferingsUseCase.execute()
                .compose(bindToLifecycle())
                .map(apiDataResponse -> mTagItemDataToViewModelMapper.mapTagItemDataListToViewModelList(apiDataResponse.getData()))
                .subscribe(vasOfferingsList -> {
                    if (null != view) {
                        TagItemViewModel tagItemViewModel = getVoucherViewModel(vasOfferingsList);
                        if (tagItemViewModel != null) {
                            handleVouchersItemClick(tagItemViewModel.getId(), birthDateParam);
                        } else {
                            view.showError();
                        }
                    }
                }, throwable -> {
                    if (view != null) {
                        view.showError();
                    }
                });
    }

    public void handleVouchersItemClick(Integer id, String birthDateParam) {
        VoucherPurchaseViewModel voucherPurchaseViewModel = new VoucherPurchaseViewModel();
        voucherPurchaseViewModel.setTagIdentifierRequestViewModel(new TagIdentifierRequestViewModel());
        voucherPurchaseViewModel.getTagIdentifierRequestViewModel().addIdToRequestList(id);
        NavigationTarget navigationTarget = null;
        if (!mApplicationStorage.getBoolean(VoucherConstants.StorageKeys.VOUCHER_INTRO_DISPLAYED, false)) {
            navigationTarget = NavigationTarget.to(VouchersNavigatorTarget.VOUCHER_INTRODUCTION);
        } else {
            navigationTarget = NavigationTarget.to(VouchersNavigatorTarget.VOUCHER_DASHBOARD);
        }
        navigationTarget.withParam(VoucherConstants.VOUCHER_PURCHASE_VIEW_MODEL, voucherPurchaseViewModel);
        navigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDateParam);
        mNavigationRouter.navigateTo(navigationTarget);

        // fallback to home page or remove the current activity
        closeDeepLinkView();
    }

    private TagItemViewModel getVoucherViewModel(List<TagItemViewModel> vasOfferingsList) {
        if (CollectionUtils.isNotEmpty(vasOfferingsList)) {
            for (int i = 0; i < vasOfferingsList.size(); i++) {
                if (VasConstants.VasOfferingsCodes.VOUCHER.equalsIgnoreCase(vasOfferingsList.get(i).getCode())) {
                    return vasOfferingsList.get(i);
                }
            }
        }
        return null;
    }

    public void handleSavingPocketFlow(AccountDto accountDto) {

        if (accountDto != null && accountDto.getAccountType() != null) {

            if (accountDto.getAccountType().equals(Constants.ACCOUNT_TYPES.CA.getAccountTypeCode())) {
                mGetFicaStatusUseCase
                        .execute()
                        .compose(bindToLifecycle())
                        .doOnSubscribe(disposable -> {
                        }).subscribe(ficaStatusDataModel -> {
                            if (ficaStatusDataModel.getIsFica()) {
                                NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.ONLINE_SAVINGS_EDUCATIONAL_SCREEN)
                                        .withParam(za.co.nedbank.services.Constants.SAVINGS_POCKETS_TP_ACCOUNT, accountDto.getNumber());
                                mNavigationRouter.navigateTo(navigationTarget);

                                // fallback to home page or remove the current activity
                                closeDeepLinkView();
                            } else {
                                if (view != null) {
                                    view.showError();
                                }
                            }
                        }, throwable -> {
                            if (view != null) {
                                view.showError();
                            }
                        });
            } else if (accountDto.getAccountType().equals(Constants.ACCOUNT_TYPES.SA.getAccountTypeCode())) {
                NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.SAVINGS_POCKET_LIST_SCREEN)
                        .withParam(za.co.nedbank.services.Constants.SAVINGS_POCKETS_TP_ACCOUNT, accountDto.getNumber());
                mNavigationRouter.navigateTo(navigationTarget);

                // fallback to home page or remove the current activity
                closeDeepLinkView();
            }
        } else {
            if (view != null) {
                view.getClientDeviceInfoRequestViewModel();
            }
        }
    }
    void clearDeepLinkingValue() {
        mApplicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
    }

    void getUserInfo() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(this::handleInvestmentFlow,
                        throwable -> {
                            if (view != null) {
                                view.showError();
                            }
                        });
    }

    void getUserInfoForSavingPocket(ClientDeviceInfoRequestViewModel
                                            clientDeviceInfoRequestViewModel) {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(data ->  mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MOA_PRODUCT_LIST)),
                        throwable -> {
                            if (view != null) {
                                view.showError();
                            }
                        });
    }

    private void handleInvestmentFlow(final UserDetailData userDetailData) {
        if (userDetailData != null) {
            NavigationTarget target = NavigationTarget.to(NavigationTarget.TARGET_INVESTMENT_OPTION_LANDING)
                    .withParam(PARAM_ONIA_CLIENT_TYPE, userDetailData.getClientType())
                    .withParam(PARAM_ONIA_BIRTH_DATE, userDetailData.getBirthDate())
                    .withParam(PARAM_ONIA_SEC_OFFICER_CD, userDetailData.getSecOfficerCd())
                    .withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, userDetailData.getCisNumber());
            mNavigationRouter.navigateTo(target);

            // fallback to home page or remove the current activity
            closeDeepLinkView();
        } else if (view != null) {
            view.showError();
        }
    }

    public void handleShareMoneyAppFlow() {
        mGetProfileUseCase
                .execute(false)
                .compose(bindToLifecycle())
                .subscribe(
                        userProfile -> {
                            if (userProfile != null) {
                                getEmcertId(userProfile);
                            } else if (view != null) {
                                view.showError();
                            }
                        },
                        error -> {
                            if (view != null) {
                                view.showError();
                            }
                        }
                );
    }

    private void getEmcertId(UserProfile userProfile) {
        mGetEmcertIdUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(emcertId -> {
                    if (view != null && emcertId != null) {
                        String userName = String.valueOf(TextUtils.concat(userProfile.getFirstName(), StringUtils.SPACE, userProfile.getLastName()));
                        navigateToShareMoneyApp(userName, emcertId);
                    } else if (view != null) {
                        view.showError();
                    }
                }, throwable -> {
                    if (view != null) {
                        view.showError();
                    }
                });
    }

    private void navigateToShareMoneyApp(String userName, String emcertId) {
        mNavigationRouter.navigateTo(NavigationTarget.to(ProfileNavigationTarget.SHARE_THE_MONEY_APP)
                .withParam(NavigationTarget.PARAM_NAME, userName)
                .withParam(ProfileConstants.EMCERT_ID, emcertId));

        // fallback to home page or remove the current activity
        closeDeepLinkView();
    }

    public void handleBuyVasFlow(boolean isDeeplink) {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    UserDetailViewModel userDetailVM = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail);
                    openBuyScreen(userDetailVM.getBirthDate(), isDeeplink);
                }, throwable -> {
                    if (view != null) {
                        if (isDeeplink)
                            view.finishActivity();
                        else
                            view.showError();
                    }
                });
    }

    public void handleTransactionProductFlow() {
        if(isEficaFlowDisabled()){
            mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.FICA_USER_TRY_AGAIN)
                    .withParam(Constants.BUNDLE_KEYS.IS_TOGGLE_OFF, true));
            closeDeepLinkView();
        }
        else {
            String productID = mMemoryStorage.getString(StorageKeys.DEEP_LINK_PRODUCT_ID, StringUtils.EMPTY_STRING);
            productShortDescriptionUseCase.execute(productID).compose(bindToLifecycle()).map(productDescriptionResponseDataToViewModelMapper::mapData).subscribe(productDescriptionResponseViewModel -> {
                if (productDescriptionResponseViewModel.getMetadata() != null && productDescriptionResponseViewModel.getData() != null && productDescriptionResponseViewModel.getMetadata().isSuccess() && null != view) {
                    view.finishActivity();
                    mMemoryStorage.putString(StorageKeys.DEEP_LINK_PRODUCT_CAMPAIGN_NAME, productDescriptionResponseViewModel.getData().getProductName());
                    mMemoryStorage.putString(StorageKeys.PRODUCT_SHORT_DESCRIPTION, productDescriptionResponseViewModel.getData().getProductShortDescription());
                    mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.DEEP_LINK_PRODUCT_SHORT_DETAIL)
                            .withParam(Constants.BUNDLE_KEYS.FROM_TRANSACTIONAL_FLOW, true));
                }
            }, throwable -> {
                if (view != null)
                    view.showError();
            });
        }
    }

    public void handleExpandCampaignDeepLinkFlow() {
        if(isEficaFlowDisabled()){
            mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.FICA_USER_TRY_AGAIN)
                    .withParam(Constants.BUNDLE_KEYS.IS_TOGGLE_OFF, true));
        }
        else{
            mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.DEEP_LINK_PRODUCT_SHORT_DETAIL)
                    .withParam(Constants.BUNDLE_KEYS.FROM_TRANSACTIONAL_FLOW, false));
        }
        closeDeepLinkView();
    }

    public void handleOfferForYouDeepLinkFlow() {
        if (ClientType.TP == getClientType()) {
            mNavigationRouter.navigateTo(NavigationTarget.to(LoansNavigatorTarget.LOANS_BORROW_SCREEN)
                    .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
            );
        }else{
            mApplicationStorage.clearValue(StorageKeys.IS_PREAPPROVED_OFFERS_AVAILABLE);
            mMemoryStorage.clearValue(StorageKeys.PRE_APPROVED_OFFERS_COUNT);
            mNavigationRouter.navigateTo(NavigationTarget.to(PreApprovedOffersNavigationTarget.PRE_APPROVED_OFFERS_NOTIFICATIONS)
                    .withParam(za.co.nedbank.loans.preapprovedaccountsoffers.Constants.BundleKeys.SCREEN_NAME, za.co.nedbank.loans.preapprovedaccountsoffers.Constants.ScreenIdentifiers.DASHBOARD_SCREEN)
                    .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true));
        }
        closeDeepLinkView();

    }

    public void handleCreditHealthFlow() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetailData -> {
                    if (isCreditHealthAvailable(userDetailData)) {
                        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CREDIT_HEALTH_FLOW));
                    }
                    // fallback to home page or remove the current activity
                    closeDeepLinkView();
                }, throwable -> closeDeepLinkView());
    }

    private boolean isCreditHealthAvailable(UserDetailData userDetailData) {
        boolean isBusinessUser = userDetailData.getClientType() != null && Integer.parseInt(userDetailData.getClientType())
                > za.co.nedbank.services.Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;

        return (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CREDIT_HEALTH)
                && !mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_FINANCIAL_WELLNESS)
                && !isBusinessUser
                && StringUtils.isValidRSAId(storageUtility.getIdOrTaxNumberString())
                && (StringUtils.isNullOrEmpty(userDetailData.getBirthDate())
                || FormattingUtil.getAgeDifference(userDetailData.getBirthDate()) >= za.co.nedbank.loans.preapprovedaccountsoffers.Constants.CREDIT_HEALTH_MIN_AGE
        )
        );
    }

    public void handleInternationalPayments() {
        mMemoryStorage.clearValue(StorageKeys.FOREX_IS_PAY_FRM_RECIPIENT);
        mMemoryStorage.clearValue(StorageKeys.FOREX_GO_TO_HOME);
        sendEventAnalytics(PaymentsTrackingEvent.ITT_LANDING);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.INTERNATIONAL_PAYMENT_OPTIONS));
        // fallback to home page or remove the current activity
        closeDeepLinkView();
    }

    public void handleGetCashFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(AtmNavigatorTarget.ATM_TUTORIAL_SCREEN));
        closeDeepLinkView();
    }


    public void handleOpenCasaAccountFlow() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    UserDetailViewModel userDetailVM = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail);
                    mMemoryStorage.putString(StorageKeys.FICA_ID_NUMBER, userDetailVM.getIdOrTaxIdNumber());
                    mMemoryStorage.putString(StorageKeys.CIS_NUMBER, userDetail.getCisNumber());
                    mMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
                    mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BANK_INTENT));
                    closeDeepLinkView();
                }, throwable -> {
                    if (view != null) {
                        view.showError();
                    }
                });
    }

    private void sendEventAnalytics(String name) {
        Map<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_MONETARY_TRANSACTIONS);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_INTERACTION, TrackingEvent.ANALYTICS.VAL_ONE);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, PaymentsTrackingEvent.INTERNATIONAL_PAYMENT);
        mAnalytics.sendEventActionWithMap(name, cdata);
    }

    private void openBuyScreen(String birthDate, boolean isDeeplink) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.VAS);
        navigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDate);
        navigationTarget.withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, isDeeplink);
        mNavigationRouter.navigateTo(navigationTarget);

        // fallback to home page or remove the current activity
        closeDeepLinkView();
    }

    public void handleInvestmentProductDetailsFlow() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(this::setUserData,
                        throwable -> closeDeepLinkView()
                );
    }

    private void setUserData(UserDetailData userDetailData) {
        userEntriesViewModel.setBirthDate(userDetailData.getBirthDate());
        userEntriesViewModel.setSecOfficerCd(userDetailData.getSecOfficerCd());
        userEntriesViewModel.setCisNumber(userDetailData.getCisNumber());
        userEntriesViewModel.setClientType(userDetailData.getClientType());
        userEntriesViewModel.setInvestmentProductType(StringUtils.EMPTY_STRING);
        getAllProducts(false);
    }

    void getAllProducts(boolean isNTF) {
        ProductDataModel productDataModel = getProductDataModel();
        if(isNTF){
            productDataModel.setNTF(true);
            productDataModel.setClientAge(54);
            productDataModel.setClientType(NTF_CLIENT_TYPE);
        }
        if (view != null) {
            mGetAllEverydayAndTaxfreeProductsUseCase
                    .execute(productDataModel)
                    .compose(bindToLifecycle())
                    .subscribe(allProductResponseDataModel -> {
                                String productID = mMemoryStorage.getString(StorageKeys.DEEP_LINK_PRODUCT_ID, StringUtils.EMPTY_STRING);
                                mMemoryStorage.clearValue(StorageKeys.DEEP_LINK_PRODUCT_ID);
                                AllProductsViewModel productsViewModel = filterInvestData(mAllProductResponseDataModelToViewModelMapper.mapAllProductsDataModelToViewModel(allProductResponseDataModel), productID);
                                if (productsViewModel != null) {
                                    navigateToMoreInfo(productsViewModel.getIsNoticeDeposit(), productsViewModel, userEntriesViewModel, false);
                                }
                                closeDeepLinkView();
                            }
                            ,
                            error -> closeDeepLinkView()

                    );
        }
    }

    private AllProductsViewModel filterInvestData(AllProductsResponseViewModel allProductsResponseViewModel, String option) {
        for (AllProductsViewModel allProductsViewModel : allProductsResponseViewModel.getAllProductsViewModels()) {
            if (Long.toString(allProductsViewModel.getProductId()).equals(option)) {
                return allProductsViewModel;
            }

        }
        return null;
    }

    private ProductDataModel getProductDataModel() {
        ProductDataModel productDataModel = new ProductDataModel();
        productDataModel.setType(INVESTMENTS);
        int clientAge = AppUtility.calculateAge(userEntriesViewModel.getBirthDate());
        productDataModel.setClientAge(clientAge);
        if (clientAge == 0) {
            productDataModel.setClientType(userEntriesViewModel.getClientType());
        } else {
            productDataModel.setClientType(userEntriesViewModel.getSecOfficerCd());
        }
        return productDataModel;
    }

    void navigateToMoreInfo(String isNotice, AllProductsViewModel allProductsViewModel, OpenNewAccUserEntriesViewModel userEntriesViewModel, boolean isNTF) {
        if (view != null) {
            NavigationTarget navigationTarget = NavigationTarget.to(OpenNewInvAccountTarget.TARGET_ONIA_DETAILS);
            navigationTarget.withParam(IS_NOTICE_DEPOSIT, isNotice);
            navigationTarget.withParam(IS_FROM_NTF_FLOW, isNTF);
            if (isNotice.equals(YES_CODE)) {
                navigationTarget.withParam(OpenNewInvAccountConstants.KEY_TO_NOTICEORFIXED_ACCOUNT, OpenNewInvAccountConstants.VAL_TO_NOTICEACCOUNT);
            } else {
                navigationTarget.withParam(OpenNewInvAccountConstants.KEY_TO_NOTICEORFIXED_ACCOUNT, OpenNewInvAccountConstants.VAL_TO_FIXEDACCOUNT);
            }
            navigationTarget.withParam(NavigationTarget.PARAM_NTF, isNTF);
            navigationTarget.withParam(OpenNewInvAccountConstants.KEY_IS_FILTER_ENABLED_SUGGESION, false);
            navigationTarget.withParam(OpenNewInvAccountConstants.KEY_PRODUCT_MODEL, allProductsViewModel);
            navigationTarget.withParam(OpenNewInvAccountConstants.KEY_USERENTRIES_MODEL, userEntriesViewModel);
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_PROJECTED_AMOUNT, userEntriesViewModel.getProjectedAmount());
            navigationTarget.withParam(NgiNavigatorTarget.PARAM_INVESTMENT_SWITCHING_FLOW, za.co.nedbank.core.Constants.ZERO);
            navigationTarget.withParam(NavigationTarget.PARAM_SWITCHING_INVESTMENT_VIEW_MODEL, new InvestmentSwitchingViewModel());
            navigationTarget.withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true);
            mNavigationRouter.navigateTo(navigationTarget);
        }
    }

    public void getBundleProducts(boolean isPrelogin) {
        mGetProductsUseCase.execute(PRODUCT_FAMILY_ZERO)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                })
                .doOnTerminate(() -> {
                }).map(mProductsDataToViewModelMapper::mapEntityToData)
                .subscribe(productResponseViewModel -> navigateToStartupBundle(productResponseViewModel, isPrelogin),
                        throwable -> closeDeepLinkView()
                );
    }

    public void navigateToStartupBundle(ProductResponseViewModel productResponseViewModel,boolean isPrelogin) {
        String productID = mMemoryStorage.getString(StorageKeys.DEEP_LINK_PRODUCT_ID, StringUtils.EMPTY_STRING);
        mMemoryStorage.clearValue(StorageKeys.DEEP_LINK_PRODUCT_ID);
        mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.APPLY_BUSINESS_PAY)
                .withParam(APPLY_BUSINESS_ALL_PRODUCTS_PARAM, productResponseViewModel)
                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                .withParam(PARAM_PRE_LOGIN_DEEP_LINK,isPrelogin)
                .withParam(PRODUCT_ID, productID)
                .withAllData(true));

        closeDeepLinkView();

    }

    private void handleInvestment() {
        String productID = mMemoryStorage.getString(StorageKeys.DEEP_LINK_PRODUCT_ID, StringUtils.EMPTY_STRING);
        if (StringUtils.isNotEmpty(productID))
            handleInvestmentProductDetailsFlow();
        else
            getUserInfo();
    }

    public void handleNedbank4Me() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                            UserDetailViewModel userDetailVM = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail);
                            mMemoryStorage.putString(StorageKeys.FICA_ID_NUMBER, userDetailVM.getIdOrTaxIdNumber());
                            mMemoryStorage.putString(StorageKeys.CIS_NUMBER, userDetail.getCisNumber());
                            mMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
                            navigateToNedbank4MeDetailsScreen();
                        }, throwable ->
                                closeDeepLinkView()
                );
    }

    public void navigateToNedbank4MeDetailsScreen() {
        mMemoryStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, EnrollV2TrackingEvent.ANALYTICS.VAL_BANK_NEDBANK_4ME);
        mMemoryStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT, za.co.nedbank.enroll_v2.Constants.MINOR_PRODUCT_NAME);
        mMemoryStorage.putBoolean(StorageKeys.IS_PROFESSIONAL_PRODUCT, false);
        ProductRequestViewModel productRequestViewModel = new ProductRequestViewModel();
        productRequestViewModel.setMonthlySalary(0);
        productRequestViewModel.setDateofbirth(new SimpleDateFormat(za.co.nedbank.enroll_v2.Constants.DATE_FORMAT, Locale.getDefault()).format(new Date()));
        productRequestViewModel.setProductType(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.getAccountTypeCode());

        mProductUseCase.execute(mProductRequestViewModelToDataMapper.mapData(productRequestViewModel))
                .compose(bindToLifecycle())
                .map(mProductResponseDataToViewModelMapper::mapData)
                .subscribe(productResponseViewModel -> {
                            if (productResponseViewModel.getMetadata().isSuccess()
                                    && productResponseViewModel.getProductDataResponseDataViewModel() != null
                                    && productResponseViewModel.getProductDataResponseDataViewModel().getProductDataViewModels() != null
                                    && !productResponseViewModel.getProductDataResponseDataViewModel().getProductDataViewModels().isEmpty()) {
                                mMemoryStorage.putString(StorageKeys.FICA_SESSION_ID,
                                        productResponseViewModel.getProductDataResponseDataViewModel().getSessionId());
                                mMemoryStorage.putInteger(StorageKeys.FICA_PRODUCT_FLOW,
                                        FicaProductFlow.MINOR);
                                mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.MOA_PRODUCT_DETAIL)
                                        .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                                        .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SELECTED_PRODUCT, productResponseViewModel
                                                .getProductDataResponseDataViewModel()
                                                .getProductDataViewModels()
                                                .get(0)));
                            } else {
                                closeDeepLinkView();
                            }
                        }, throwable ->
                                closeDeepLinkView()
                );
    }


    public void handleOpenCasaAccountFlowForDeepLink() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                            UserDetailViewModel userDetailVM = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail);
                            mMemoryStorage.putString(StorageKeys.FICA_ID_NUMBER, userDetailVM.getIdOrTaxIdNumber());
                            mMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
                            mMemoryStorage.putString(StorageKeys.CIS_NUMBER, userDetail.getCisNumber());
                            mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BANK_INTENT));
                            closeDeepLinkView();
                        }, throwable ->
                                closeDeepLinkView()
                );
    }

    public void getMyPocketCounts(String accountType) {
        mGetOverviewUseCase.invalidateCache();
        Observable.zip(mGetOverviewUseCase.execute(), mGetAccountsUseCase.execute(),
                        (overview, accountsContainerDtoList) -> {
                            if (overview != null) {
                                filterSavingPocketsForCurrentAccount(overview.get());
                            }
                            return getSavingPocketAccount(accountsContainerDtoList, accountType);
                        }).compose(bindToLifecycle())
                .subscribe(this::handleMyPocketFlow,
                        throwable ->
                                closeDeepLinkView()
                );
    }

    private void filterSavingPocketsForCurrentAccount(Overview overview) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.accountSummaries != null) {
                    myPocketCount = accountsOverview.onlineSavingsAccounts.getPocketsCounter();
                    maxPocketCount = accountsOverview.onlineSavingsAccounts.getTpAccountCounter() * za.co.nedbank.services.Constants.MAX_SAVINGS_POCKET;
                }
            }
        }
    }

    public void handleMyPocketFlow(AccountDto accountDto) {
        if (accountDto != null) {
            if (myPocketCount < maxPocketCount) {
                navigateToEducationalScreen(accountDto);
            } else {
                navigateToPocketListScreen(accountDto);
            }
        }
    }

    private void navigateToEducationalScreen(AccountDto accountDto) {
        mGetFicaStatusUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(ficaStatusDataModel -> {
                            if (ficaStatusDataModel.getIsFica()) {
                                NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.ONLINE_SAVINGS_EDUCATIONAL_SCREEN)
                                        .withParam(za.co.nedbank.services.Constants.SAVINGS_POCKETS_TP_ACCOUNT, accountDto.getNumber())
                                        .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true);
                                mNavigationRouter.navigateTo(navigationTarget);

                                // fallback to home page or remove the current activity
                                closeDeepLinkView();
                            } else {
                                closeDeepLinkView();
                            }
                        }, throwable ->
                                closeDeepLinkView()
                );
    }

    private void navigateToPocketListScreen(AccountDto accountDto) {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.SAVINGS_POCKET_LIST_SCREEN)
                .withParam(za.co.nedbank.services.Constants.SAVINGS_POCKETS_TP_ACCOUNT, accountDto.getNumber())
                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true);
        mNavigationRouter.navigateTo(navigationTarget);

        // fallback to home page or remove the current activity
        closeDeepLinkView();
    }
    // fallback to home page or remove the current activity
    private void getAccountsForInsuranceFLow(String insuranceType, InsuranceProductType insuranceProduct) {
        mGetOverviewUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(overviewCachableValue -> {
                            Overview overviewValue = overviewCachableValue.get().clone();
                            handleApplyMyLifeOrFuneralDeepLinkFlow(overviewValue, insuranceType, insuranceProduct);
                        },
                        throwable -> NBLogger.e(TAG, throwable.getMessage()));
    }

    private void handleApplyMyLifeOrFuneralDeepLinkFlow(Overview overview, String insuranceType, InsuranceProductType insuranceProduct) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.insuranceAccountList != null) {
                    getInsuranceListAndNavigate(accountsOverview, insuranceType, insuranceProduct);
                }
            }
        }


    }

    public void getInsuranceListAndNavigate(AccountsOverview accountsOverview, String insuranceType, InsuranceProductType insuranceProduct) {
        mInsuranceAccountList = accountsOverview.insuranceAccountList;
        String partyId = getRsaId();
        List<String> arrayOfRsaId = new ArrayList<>();
        if (StringUtils.isNotEmpty(partyId)) {
            arrayOfRsaId.add(partyId);
        }
        if (insuranceProduct == InsuranceProductType.PERSONAL_LINES) {
            navigateToMyCoverLifeInsurance(InsuranceProductType.PERSONAL_LINES,
                    mInsuranceAccountList, InsuranceConstants.MY_COVER_INSURANCE_COMBO_ID, 0.0, 0.0);
        } else if (StringUtils.isNotEmpty(partyId)) {
            if (insuranceProduct == InsuranceProductType.FUNERAL_OWN_COVER) {
                getNIFPMyCoverAllowedCoverAmountDeepLink(arrayOfRsaId, InsuranceConstants.BUILD_OWN_COVER, mInsuranceAccountList);
            } else {
                getNIFPMyCoverAllowedCoverAmountDeepLink(arrayOfRsaId, InsuranceConstants.MY_COVER_LIFE_PRODUCT_ID, mInsuranceAccountList);
            }
        } else {
            if (insuranceProduct == InsuranceProductType.FUNERAL_OWN_COVER) {
                getUserDetailForInsurance(InsuranceProductType.FUNERAL_OWN_COVER.getEpcCode(), mInsuranceAccountList, insuranceType);
            } else {
                getUserDetailForInsurance(InsuranceProductType.MY_COVER_LIFE.getProductId(), mInsuranceAccountList, insuranceType);
            }
        }
    }

    private void navigateToBillPayments() {
        mTermsAndConditionsUseCase
                .execute(TermsAndConditionClassification.BILL_PAYMENT_TNC)
                .compose(bindToLifecycle())
                .subscribe(termsAndConditions -> {
                    if (termsAndConditions != null && termsAndConditions.isAccepted()) {
                        mApplicationStorage.putBoolean(StorageKeys.BILL_PAYMENT_TNC_SELECTED, Boolean.TRUE);

                        mNavigationRouter.navigateTo(NavigationTarget
                                .to(NavigationTarget.BILL_PAYMENT_TYPE)
                                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, Boolean.TRUE)
                                .withParam(NavigationTarget.BILL_PAYMENTS_OPTION, Boolean.FALSE));
                    } else {
                        mApplicationStorage.putBoolean(StorageKeys.BILL_PAYMENT_TNC_SELECTED, Boolean.FALSE);
                        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.BILL_PAYMENTS_TUTORIAL)
                                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, Boolean.TRUE));
                    }
                    closeDeepLinkView();
                }, throwable -> closeDeepLinkView());

    }
    public ClientType getClientType() {
        //Check for NTF and set ClientType = NTP
        String clientType = mApplicationStorage.getString(CLIENT_TYPE, ClientType.TP.getValue());
        return ClientType.getEnum(clientType);
    }

    boolean isEficaFlowDisabled() {
        boolean isFlowDisabled = false ;
        int ficaWorkFlow = mMemoryStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER);
        if( (ficaWorkFlow == FicaWorkFlow.IN_APP || ficaWorkFlow == FicaWorkFlow.PRE_LOGIN)
                && mMemoryStorage.getInteger(StorageKeys.FICA_PRODUCT_FLOW, FicaProductFlow.DEFAULT) == FicaProductFlow.MINOR
                && mFeatureSetController.isFeatureDisabled(FeatureConstants.EFICA_MINOR_FLOW)||
                ((ficaWorkFlow == FicaWorkFlow.IN_APP || ficaWorkFlow == FicaWorkFlow.PRE_LOGIN)
                        &&  mFeatureSetController.isFeatureDisabled(FeatureConstants.EFICA_ENROLLED_FLOW)) ||
                (ficaWorkFlow == FicaWorkFlow.NEW_CUSTOMER && mFeatureSetController.isFeatureDisabled(FeatureConstants.EFICA))){
            isFlowDisabled = true ;
        }
        return isFlowDisabled;
    }

    public void getUserDetailForHouseAndFuneralInsuranceType(String productName) {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                            if (userDetail != null) {
                                mMemoryStorage.putObject(ResponseStorageKey.USERDETAILDATA,
                                        mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail));
                                NavigationTarget navigationTarget =
                                        NavigationTarget.to(ServicesNavigationTarget.PRODUCT_SELECTION_LIST_ACTIVITY)
                                                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                                                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_NAME, productName);
                                mNavigationRouter.navigateTo(navigationTarget);
                                closeDeepLinkView();
                            }

                        }, throwable ->
                                handleNavigationErrorFlow()
                );
    }

    public void personalAccidentCover(double allowedCoverAmount, List<AccountViewModel> mInsuranceAccountList, String insuranceType) {
        if (INSURANCE_PERSONAL_ACCIDENT_COVER.getName().equalsIgnoreCase(insuranceType)) {
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_EDUCATION_SCREEN)
                    .withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, InsuranceProductType.PERSONAL_ACCIDENTAL)
                    .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW_FOR_HOME_CONTENT, true)
                    .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, ACCIDENTAL_INSURANCE_PRODUCT_ID)
                    .withParam(InsuranceConstants.ParamKeys.PARAM_ALLOWED_COVER_AMOUNT, allowedCoverAmount)
                    .withParam(FROM_PERSONAL_ACCIDENT_DEEPLINK,true)
                    .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, mInsuranceAccountList);
            mNavigationRouter.navigateTo(navigationTarget);

        } else {
            mNavigationRouter.navigateTo(
                    NavigationTarget.to(ServicesNavigationTarget.INSURANCE_STEPPER_SCREEN)
                            .withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, InsuranceProductType.PERSONAL_ACCIDENTAL)
                            .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, ACCIDENTAL_INSURANCE_PRODUCT_ID)
                            .withParam(InsuranceConstants.ParamKeys.PARAM_ALLOWED_COVER_AMOUNT, allowedCoverAmount)
                            .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, mInsuranceAccountList)
                            .withParam(InsuranceConstants.BundleKeys.FROM_DEEPLINK, true)
            );
        }
        closeDeepLinkView();

    }

    private void getAllowedPersonalAccidentCoverAmount(int productId, String insuranceType) {
        String partyId = storageUtility.getIdOrTaxNumber();
        if (StringUtils.isNullOrEmpty(partyId)) {
            getUserDetailForInsurance(InsuranceProductType.PERSONAL_ACCIDENTAL.getProductId(), null, insuranceType);
        } else {
            getAllowedCoverAmount(productId, partyId, insuranceType);
        }
    }

    private void getAllowedCoverAmount(int productId, String partyId, String insuranceType) {
        mGetAllowedCoverAmountUseCase.execute(productId, partyId)
                .compose(bindToLifecycle())
                .subscribe(allowedCoverAmountResponseData -> {
                            AllowedCoverAmountResponseViewModel allowedCover = mAllowedCoverAmountResponseDataToViewModelMapper
                                    .map(allowedCoverAmountResponseData);
                    getInsuranceAccounts(allowedCover.getAllowedcoveramount(), insuranceType);
                        }, throwable -> closeDeepLinkView()
                );
    }

    private void getInsuranceAccounts(double allowedcoveramount, String insuranceType) {
        mGetOverviewUseCase.invalidateCache();
        mGetOverviewUseCase.execute()
                .subscribe(overviewCachableValue -> {
                            Overview overview = overviewCachableValue.get();
                            if (overview.accountsOverviews != null) {
                                for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                                    mInsuranceAccountList =
                                            getInsuranceAccountList(mInsuranceAccountList, accountsOverview);

                                }
                                personalAccidentCover(allowedcoveramount, mInsuranceAccountList, insuranceType);
                            }
                        },
                        throwable ->
                                closeDeepLinkView()
                );

    }

    private List<AccountViewModel> getInsuranceAccountList(List<AccountViewModel> insuranceAccountList, AccountsOverview accountsOverview) {
        List<AccountViewModel> tempInsuranceList = insuranceAccountList;
        if (accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING && accountsOverview.insuranceAccountList != null) {
            tempInsuranceList = accountsOverview.insuranceAccountList;
        }
        return tempInsuranceList;
    }



    @SuppressLint("CheckResult")
    private void getCaseDetails() {
        String caseId = mMemoryStorage.getString(StorageKeys.DEEP_LINK_CASE_ID, null);
        AtomicReference<String> partyId = new AtomicReference<>("");
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                })
                .flatMap(userDetail -> {
                    if (userDetail.getCisNumber() != null) {
                        partyId.set(userDetail.getCisNumber());
                        return applicationListUseCase.execute(partyId.get());
                    }
                    return null;
                }).subscribe(applicationListResponseViewModel -> {
                    if (applicationListResponseViewModel != null) {
                        mMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
                        openApplicationInWebView(applicationListResponseViewModel,caseId,partyId.get());
                    }
                }, throwable -> {
                    handleNavigationErrorFlow();
                    trackDigitalAdoptionOnFailure(caseId, throwable.getMessage());
                });
    }

    private void openApplicationInWebView(ApplicationListResponseViewModel applicationListResponseViewModel, String caseId, String partyId){
        boolean caseMatched = false;
        try {
            if (applicationListResponseViewModel.getCases() != null) {
                for (CaseViewModel caseViewModel : applicationListResponseViewModel.getCases()) {
                    if (Objects.equals(caseViewModel.getCaseID(), caseId)) {
                        caseMatched = true;
                        String workItemId = caseViewModel.getResumableWorkItems().get(0).getWorkItemID();
                        String productId = caseViewModel.getArrangements().get(0).getProductID();
                        String productName = caseViewModel.getArrangements().get(0).getProductName();
                        String productType = caseViewModel.getArrangements().get(0).getProductType();
                        navigateToWebView(caseId,workItemId, productId, productName, partyId, productType);
                        break;
                    }
                }
            }
        }catch (Exception e){
            NBLogger.e("openApplicationInWebView",e.getLocalizedMessage());
            handleNavigationErrorFlow();
            trackDigitalAdoptionOnFailure(caseId, e.getMessage());
        }
        if(!caseMatched){
            handleNavigationErrorFlow();
            trackDigitalAdoptionOnFailure(caseId, "Case not found");
        }
    }

    private void navigateToWebView(String caseNumber, String workItemId, String productId, String productName, String partyId, String productType) {
        trackDigitalAdoptionOnPageLoad(caseNumber,productId, productName,productType);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.NTF_WEBVIEW_ACTIVITY);
        navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.TOKEN, APIInformation.getInstance().getToken());
        navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.NTFCRParams.WORK_ITEM_ID, workItemId);
        navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.NTFCRParams.PARTY_ID, partyId);
        navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.CR_FLOW, za.co.nedbank.enroll_v2.Constants.CrFlow.DEEP_LINK);
        navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.NTFCRParams.PRODUCT_ID, productId);
        navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.NTFCRParams.PRODUCT_NAME, productName);
        navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.NTFCRParams.PRODUCT_TYPE, productType);
        mNavigationRouter.navigateWithResult(navigationTarget);
    }
    public void trackDigitalAdoptionOnPageLoad(String caseNumber,String productId, String productName, String productType) {
        ClientType userType = ClientType.getEnum(mApplicationStorage.getString(StorageKeys.CLIENT_TYPE, ClientType.TP.getValue()));
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setEntryPoint(EnrollV2TrackingValue.ANALYTICS.DEEP_LINK_DIGITAL_ADOPTION);
        adobeContextData.setCategoryAndProduct(productsUtil.getProductValue(productId,productName.toLowerCase(),productType));
        adobeContextData.setScreenName(userType+" "+DIGITAL_ADOPTION_PRODUCT);
        adobeContextData.setInitiations();
        adobeContextData.setFeatureCategory(EnrollV2TrackingValue.ANALYTICS.VAL_SALES_AND_ON_BOARDING);
        adobeContextData.setFeature(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_ON_BOARDING);
        adobeContextData.setProductAccount(productsUtil.getProductName(productId,productName));
        adobeContextData.setProductCategory(productsUtil.getProductCategory(productType));
        adobeContextData.setMetadataCaseNumber(caseNumber);
        mAnalytics.sendEventStateWithMap(EnrollV2TrackingValue.ANALYTICS.DIGITAL_ADOPTION, cdata);
    }

    public void trackDigitalAdoptionOnFailure(String caseNumber, String errorMessage) {
        errorMessage = EnrollV2TrackingValue.ANALYTICS.AM_QR_DEEPLINK+":"+errorMessage;
        ClientType userType = ClientType.getEnum(mApplicationStorage.getString(StorageKeys.CLIENT_TYPE, ClientType.TP.getValue()));
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setEntryPoint(EnrollV2TrackingValue.ANALYTICS.DEEP_LINK_DIGITAL_ADOPTION);
        adobeContextData.setScreenName(userType+" "+DIGITAL_ADOPTION_PRODUCT);
        adobeContextData.setInitiations();
        adobeContextData.setFeatureCategory(EnrollV2TrackingValue.ANALYTICS.VAL_SALES_AND_ON_BOARDING);
        adobeContextData.setFeature(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_ON_BOARDING);
        adobeContextData.setMetadataCaseNumber(caseNumber);
        adobeContextData.setApiError(errorMessage);
        adobeContextData.setApiErrorCount();
        mAnalytics.sendEventStateWithMap(EnrollV2TrackingValue.ANALYTICS.DEEP_LINK_DIGITAL_ADOPTION, cdata);
    }

    @SuppressLint("CheckResult")
    public void handleMoaRrbProductFlow() {
        ClientType clientType = ClientType.getEnum(mApplicationStorage.getString(StorageKeys.CLIENT_TYPE, ClientType.SALES.getValue()));
        if (clientType == ClientType.TP) {
            mMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
            ProductRequestViewModel productRequestViewModel = createProductRequestViewModel(0,StringUtils.EMPTY_STRING);
            executeProductUseCase(productRequestViewModel);
        } else{
            NavigationTarget navTarget = NavigationTarget.to(NavigationTarget.MOA_BASIC_INFORMATION)
                    .withParam(za.co.nedbank.core.Constants.IS_FROM_DEEPLINK, true);
            mNavigationRouter.navigateWithResult(navTarget)
                    .compose(bindToLifecycle())
                    .subscribe(
                            navigationResult -> { // Received callback from the previous activity.
                                if (navigationResult.isOk()) {
                                    // Do not close the deep link view; it will show the intermediate screen again after obtaining the basic info.
                                    double monthlySalary = FormattingUtil.formatCurrencyToNumber(navigationResult.getStringParam(Constants.EXTRA_SALARY_PARAM));
                                    String dob = navigationResult.getStringParam(Constants.EXTRA_DOB_PARAM);
                                    ProductRequestViewModel productRequestViewModel = createProductRequestViewModel(monthlySalary,dob);
                                    mMemoryStorage.putObject(Constants.EXTRA_PROD_REQ_MODEL,productRequestViewModel);
                                    executeProductUseCase(productRequestViewModel);
                                } else {
                                    // Clicking back closes the deep link.
                                    closeDeepLinkView();
                                }
                            }, throwable ->{
                                navigateToMoaDeeplinkRedirectScreen();
                                closeDeepLinkView();
                            }
                    );
        }
    }

    @SuppressLint("CheckResult")
    private void executeProductUseCase(ProductRequestViewModel productRequestViewModel) {
        /*
         * APIS: https://nedmobile-api-q.nedsecure.co.za/nedbank/nedmobile/mobile/clientonboarding/v1/products/availableproducts/CA?dateofbirth=&monthlysalary=0.0&includedaeproduct=true
         *
         */
        String targetProductID = mMemoryStorage.getString(StorageKeys.DEEP_LINK_PRODUCT_ID, StringUtils.EMPTY_STRING);
        mProductUseCase.execute(mProductRequestViewModelToDataMapper.mapData(productRequestViewModel))
                .compose(bindToLifecycle())
                .map(mProductResponseDataToViewModelMapper::mapData)
                .subscribe(productResponseDataViewModel -> {
                    var productResponse = productResponseDataViewModel.getProductDataResponseDataViewModel();
                    String sessionId = productResponse != null ? productResponse.getSessionId() : null;

                    if (sessionId != null) {
                        mMemoryStorage.putString(StorageKeys.FICA_SESSION_ID, sessionId);
                    }

                    if (productResponseDataViewModel.getMetadata().isSuccess() && productResponse != null
                            && productResponse.getProductDataViewModels() != null
                            && !productResponse.getProductDataViewModels().isEmpty()) {

                        handleProductMatching(productResponseDataViewModel, targetProductID);
                        closeDeepLinkView();
                    } else if (productResponseDataViewModel.getMetadata().getResultCode().equalsIgnoreCase(SALARY_NOT_UPDATED)) {
                        navigateToUpdateSalaryScreen(productResponseDataViewModel);
                    } else {
                        handleNavigationErrorFlow();
                        navigateToMoaDeeplinkRedirectScreen();//navigate to Moa deep link screen...
                        closeDeepLinkView();
                    }
                }, throwable -> {
                    handleNavigationErrorFlow();
                    navigateToMoaDeeplinkRedirectScreen();
                    closeDeepLinkView();
                });
    }

    private ProductRequestViewModel createProductRequestViewModel(double monthlySalary, String dateOfBirth) {
        ProductRequestViewModel productRequestViewModel = new ProductRequestViewModel();
        productRequestViewModel.setMonthlySalary(monthlySalary);
        productRequestViewModel.setDateofbirth(dateOfBirth);
        productRequestViewModel.setProductType(CA.getAccountTypeCode());

        boolean tp2Toggle = mMemoryStorage.getBoolean(FeatureConstants.DynamicToggle.FTR_ONBOARDINGTP2, false);
        productRequestViewModel.setIncludedaeproduct(tp2Toggle);

        return productRequestViewModel;
    }

    private void navigateToMoaDeeplinkRedirectScreen() {
        mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.MOA_DEEPLINK_PRODUCT_REDIRECT));
    }

    private void handleProductMatching(ProductResponseDataViewModel productResponseDataViewModel, String targetProductID) {
        List<ProductDataViewModel> products = productResponseDataViewModel.getProductDataResponseDataViewModel().getProductDataViewModels();
        ProductDataViewModel productDataViewModel = null;

        for (ProductDataViewModel product : products) {
            if (product.getProductId().equals(targetProductID)) {
                productDataViewModel = product;
                break;
            }
        }

        if (productDataViewModel != null) {
            navigateToProductDetailsScreen(productDataViewModel); // Navigate if a match is found
        } else {
            // Navigate to MOA redirect screen if no matching product is found
            navigateToMoaDeeplinkRedirectScreen();
        }
    }
    private void navigateToProductDetailsScreen(ProductDataViewModel productDataViewModel) {
        mMemoryStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT, productDataViewModel.getProductName());
        mMemoryStorage.putString(StorageKeys.MOA_SELECTED_PRODUCT_CODE, productDataViewModel.getProductCode());
        mMemoryStorage.putBoolean(StorageKeys.IS_PROFESSIONAL_PRODUCT, productDataViewModel.isProfessionalProduct());
        mMemoryStorage.putBoolean(StorageKeys.IS_DAE_PRODUCT, productDataViewModel.isDaeProduct());
        mMemoryStorage.putBoolean(StorageKeys.IS_FACILITY_SELECTED, false);
        mMemoryStorage.putBoolean(StorageKeys.IS_FROM_OVERDRAFT, false);
        mMemoryStorage.putBoolean(StorageKeys.IS_FROM_CREDIT_CARD, false);

        if(productDataViewModel.isDaeProduct()){
            long timestamp = System.currentTimeMillis() / 1000;
            mMemoryStorage.putLong(StorageKeys.NTF_ON_BOARDING_TIME, timestamp);
            mMemoryStorage.putString(StorageKeys.SELECTED_PRODUCT_GROUP_AND_PRODUCT, productDataViewModel.getProductName());
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NTF_WEBVIEW_ACTIVITY).withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SELECTED_PRODUCT, productDataViewModel)
                    .withParam(za.co.nedbank.enroll_v2.Constants.KEY_PRODUCT_ID,  parseLong(productDataViewModel.getProductId())));
        } else {
            mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.MOA_PRODUCT_DETAIL)
                    .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                    .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SELECTED_PRODUCT, productDataViewModel));
        }
    }

    @SuppressLint("CheckResult")
    private void navigateToUpdateSalaryScreen(ProductResponseDataViewModel productResponseDataViewModel) {
            NavigationTarget navTarget;
            String monthlySalary = productResponseDataViewModel.getProductDataResponseDataViewModel().getMonthlySalary();
            if (monthlySalary == null) {
                navTarget = NavigationTarget.to(EnrollV2NavigatorTarget.MOA_UPDATE_CUSTOMER_SALARY)
                        .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.IS_SALARY_AVAILABLE, false);
            } else {
                navTarget = NavigationTarget.to(EnrollV2NavigatorTarget.MOA_UPDATE_CUSTOMER_SALARY_PREP)
                        .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SALARY, monthlySalary);
            }
            navTarget.withParam(za.co.nedbank.core.Constants.IS_FROM_DEEPLINK, true);
            mNavigationRouter.navigateWithResult(navTarget)
                    .compose(bindToLifecycle())
                    .subscribe(
                            navigationResult -> { // Received callback from the previous activity.
                                if (navigationResult.isOk()) {
                                    // Do not close the deep link view; it will show the intermediate screen again after updating the salary.
                                    handleMoaRrbProductFlow();
                                } else {
                                    // Clicking back closes the deep link.
                                    closeDeepLinkView();
                                }
                            }, throwable ->{
                                navigateToMoaDeeplinkRedirectScreen();
                                closeDeepLinkView();
                            }
                    );
    }

}
