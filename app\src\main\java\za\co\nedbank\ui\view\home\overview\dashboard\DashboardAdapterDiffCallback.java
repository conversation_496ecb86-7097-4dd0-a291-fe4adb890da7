/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.overview.dashboard;


import androidx.recyclerview.widget.DiffUtil;

import java.util.List;

import za.co.nedbank.core.dashboard.DashboardCard;

class DashboardAdapterDiffCallback extends DiffUtil.Callback {

    private final List<DashboardCard> oldList;
    private final List<DashboardCard> newList;

    public DashboardAdapterDiffCallback(final List<DashboardCard> oldList, final List<DashboardCard> newList) {
        this.oldList = oldList;
        this.newList = newList;
    }

    @Override
    public int getOldListSize() {
        return oldList.size();
    }

    @Override
    public int getNewListSize() {
        return newList.size();
    }

    @Override
    public boolean areItemsTheSame(final int oldItemPosition, final int newItemPosition) {
        return newList.get(newItemPosition).shouldReloadContent() ?
                oldList.get(oldItemPosition).getCardType() == newList.get(newItemPosition).getCardType() : true;
    }

    @Override
    public boolean areContentsTheSame(final int oldItemPosition, final int newItemPosition) {
        return true;
    }
}
