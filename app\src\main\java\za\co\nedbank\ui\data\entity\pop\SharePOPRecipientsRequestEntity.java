package za.co.nedbank.ui.data.entity.pop;

import com.squareup.moshi.Json;

import java.util.List;

public class SharePOPRecipientsRequestEntity {

    @J<PERSON>(name = "transactionDate")
    private String transactionDate;
    @Json(name = "transactionKind")
    private String transactionKind;
    @Json(name = "notificationDetails")
    private List<ShareProofOfPaymentRequestEntity> notificationDetails ;

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public void setTransactionKind(String transactionKind) {
        this.transactionKind = transactionKind;
    }

    public void setNotificationDetails(List<ShareProofOfPaymentRequestEntity> notificationDetails) {
        this.notificationDetails = notificationDetails;
    }
}
