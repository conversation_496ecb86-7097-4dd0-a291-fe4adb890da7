package za.co.nedbank.ui.notifications.martec;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.webkit.WebView;

import com.adobe.marketing.mobile.Message;
import com.adobe.marketing.mobile.MessagingEdgeEventType;
import com.adobe.marketing.mobile.services.MessagingDelegate;
import com.adobe.marketing.mobile.services.ui.FullscreenMessage;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.ui.view.notification.ajo_notification_details.navigation.NavigationHandlerActivity;

public class AjoInAppMessagingDelegateManager implements MessagingDelegate {

    private static final String TAG = AjoInAppMessagingDelegateManager.class.getName();
    private Message currentMessage = null;
    private WebView webview = null;
    private final List<String> showFlagSet = new ArrayList<>();
    boolean showMessages = true;
    ApplicationStorage mApplicationStorage;

    public AjoInAppMessagingDelegateManager(ApplicationStorage applicationStorage) {
        mApplicationStorage = applicationStorage;
    }

    @Override
    public void onShow(FullscreenMessage message) {
        if (message == null || message.getParent() == null) return;
        this.currentMessage = (Message) message.getParent();
        if (currentMessage.getWebView() == null) return;
        this.webview = currentMessage.getWebView();
    }

    @Override
    public void onDismiss(FullscreenMessage message) {
        if (message != null && message.getParent() != null) {
            this.currentMessage = (Message) message.getParent();
        }
    }

    @Override
    public boolean shouldShowMessage(FullscreenMessage fullscreenMessage) {
        boolean toggle = mApplicationStorage.getBoolean(StorageKeys.AJO_ADOBE_TOGGLE, false);
        if (!toggle) showMessages = false;
        // access to the whole message from the parent
        if (fullscreenMessage != null) {
            this.currentMessage = (Message) fullscreenMessage.getParent();
            this.webview = currentMessage.getWebView();

            currentMessage.handleJavascriptMessage("deepLinkHandler", content -> {
                if (content != null) {
                    currentMessage.dismiss(true);
                    currentMessage.track(content, MessagingEdgeEventType.IN_APP_INTERACT);
                    Context context = webview.getContext();
                    navigateToNavigationHandler(context, content);
                }
            });

            // if we're not showing the message now, we can save it for later
            if (!showMessages) {
                NBLogger.d(TAG, "message was suppressed: ${currentMessage?.id}");
                currentMessage.track("message suppressed", MessagingEdgeEventType.IN_APP_TRIGGER);
            }
        }
        return showMessages;
    }

    /**
     * @noinspection CharsetObjectCanBeUsed
     */
    @Override
    public void urlLoaded(String url, FullscreenMessage message) {
        MessagingDelegate.super.urlLoaded(url, message);
        Uri uri = Uri.parse(url);
        if (uri != null) {
            String link = uri.getQueryParameter(Constants.PARAM_NAME_LINK);
            if (!StringUtils.isNullOrEmpty(link)) {
                try {
                    Context context = message.getWebView().getContext();
                    String deeplink = URLDecoder.decode(link, Constants.UTF_8);
                    navigateToNavigationHandler(context, deeplink);
                } catch (UnsupportedEncodingException e) {
                    //nothing to do here
                }
            }
        }
    }

    private void navigateToNavigationHandler(Context context, String link) {
        Intent target = new Intent(context, NavigationHandlerActivity.class);
        target.putExtra(NotificationConstants.Navigation.TARGET, link);
        target.putExtra(NotificationConstants.Navigation.FROM, NotificationConstants.AjoConstants.AJO_IN_APP);
        target.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(target);
    }

    public void shouldShowInAppMessage(AjoInAppEvent event) {
        String className = event.getClassName();
        NBLogger.e(TAG, "Class: " + className + ", Show: " + event.isShow() + ", Force: " + event.isForceShow());
        if (event.isShow()) {
            showFlagSet.remove(className);
            if (showFlagSet.isEmpty() || event.isForceShow()) {
                showFlagSet.clear();
                showMessages = true;
            }
        } else {
            showFlagSet.add(className);
            showMessages = false;
        }
    }
}
