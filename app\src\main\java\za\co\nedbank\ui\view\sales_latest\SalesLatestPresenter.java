package za.co.nedbank.ui.view.sales_latest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.common.CardType;
import za.co.nedbank.core.common.MoreType;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.media_content.AppState;
import za.co.nedbank.core.domain.usecase.media_content.MediaContentUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.tracking.appsflyer.AddContextData;
import za.co.nedbank.core.tracking.appsflyer.AppsFlyerTags;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.core.view.model.media_content.ProductOfferViewModel;
import za.co.nedbank.core.view.model.media_content.ProductStatus;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingValue;

import static za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.ENABLE_JAVA_SCRIPT;
import static za.co.nedbank.core.feature.FeatureConstants.DynamicToggle.FTR_SBS_ACCOUNT_OPENING;
import static za.co.nedbank.enroll_v2.Constants.BundleKeys.CMS_URL;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.APPLY_TYPE_ENTRY_FLOW_PARAM;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.ApplyFlowType.SALES_APPLY_FLOW;

public class SalesLatestPresenter extends NBBasePresenter<SalesLatestView> {

    private final NavigationRouter mNavigationRouter;
    private final FeatureSetController featureSetController;
    private ApplicationStorage mApplicationStorage;
    private final ApplicationStorage mMemoryApplicationStorage;
    private final MediaContentUseCase mMediaContentUseCase;
    private final Analytics mAnalytics;
    private final AFAnalyticsTracker mAfAnalyticsTracker;

    @Inject
    SalesLatestPresenter(final NavigationRouter navigationRouter, FeatureSetController featureSetController,
                         final ApplicationStorage applicationStorage,
                         @Named("memory") final ApplicationStorage mMemoryApplicationStorage,
                         MediaContentUseCase mediaContentUseCase,
                         Analytics mAnalytics,
                         final AFAnalyticsTracker afAnalyticsTracker
    ) {
        this.mNavigationRouter = navigationRouter;
        this.featureSetController = featureSetController;
        this.mApplicationStorage = applicationStorage;
        this.mMemoryApplicationStorage = mMemoryApplicationStorage;
        this.mMediaContentUseCase = mediaContentUseCase;
        this.mAnalytics = mAnalytics;
        this.mAfAnalyticsTracker = afAnalyticsTracker;
    }


    @Override
    protected void onBind() {
        super.onBind();
        HashMap<String, Object> cdata = new HashMap<>();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_USER_JOURNEY, TrackingParam.VAL_NEW_TO_NEDBANK_ON_BOARDING);
        mAnalytics.sendEventStateWithMap(TrackingEvent.ANALYTICS.TAG_NEDBANK_NTF, cdata);
    }

    void navigateToMore() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.LOGIN_MORE)
                .withParam(Constants.BUNDLE_KEYS.MORE_SCREEN_TYPE, MoreType.pre_login));
    }


    void navigateToBankLayout() {
        if (featureSetController.isFeatureDisabled(FTR_SBS_ACCOUNT_OPENING)) {
            mAnalytics.sendEventAnalytics(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_SELECTED, TrackingEvent.ANALYTICS.KEY_PRODUCTS, EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_BANK + ";;;");
            mMemoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, StringUtils.EMPTY_STRING);
            if (mMemoryApplicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) == FicaWorkFlow.IN_APP
                    && featureSetController.isFeatureDisabled(FeatureConstants.EFICA_ENROLLED_FLOW)) {
                mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.BECOME_CLIENT));
            } else {
                mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BANK_INTENT));
            }
        } else {
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.APPLY_TYPE)
                    .withParam(APPLY_TYPE_ENTRY_FLOW_PARAM, SALES_APPLY_FLOW));
        }
    }

    void loadMediaContent() {
        if (!featureSetController.isFeatureDisabled(FeatureConstants.APP_LAYOUT_CMS_CONTENT)) {
            mMediaContentUseCase.execute(AppState.PRE_LOGIN)
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        if (view != null) {
                            view.showProgress(true);
                        }
                    })
                    .doOnTerminate(() -> {
                        if (view != null) {
                            view.showProgress(false);
                        }
                    })
                    .subscribe(mediaContentList -> {
                        if (view != null) {
                            view.showMediaAndOfferCards(addProduct(mediaContentList));
                        }
                    }, throwable -> {
                        if (view != null) {
                            view.showMediaAndOfferCards(addProduct(null));
                        }
                    });
        } else if (view != null) {
            view.showMediaAndOfferCards(addProduct(null));
        }
    }

    private List<AppLayoutViewModel> addProduct(List<AppLayoutViewModel> layoutModel) {
        boolean isNewFeatureTileEnable = !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_NEWFEATURES_TILE_CONTROL);
        if (!isNewFeatureTileEnable) {
            return layoutModel;
        }
        AppLayoutViewModel appLayoutProductCard = getProductCard();
        if (layoutModel != null && !layoutModel.isEmpty()) {
            layoutModel.add(0, appLayoutProductCard);
        } else {
            layoutModel = new ArrayList<>();
            layoutModel.add(appLayoutProductCard);
        }
        return layoutModel;

    }

    private AppLayoutViewModel getProductCard() {
        AppLayoutViewModel appLayoutProductCard = new AppLayoutViewModel();
        appLayoutProductCard.setCardType(CardType.PRODUCT_OFFER);
        ProductOfferViewModel productViewModel = new ProductOfferViewModel();

        ProductStatus mInvisibleState = new ProductStatus();
        mInvisibleState.setVisible(false);

        ProductStatus mDisableState = new ProductStatus();
        mDisableState.setDisable(true);


        ProductStatus mActiveState = new ProductStatus();
        productViewModel.setBank(mActiveState);
        productViewModel.setLoan(mActiveState);

        productViewModel.setFinancial(mActiveState);
        productViewModel.setForex(mDisableState);
        productViewModel.setInsurance(mDisableState);
        productViewModel.setInvestment(mActiveState);
        productViewModel.setSma(mInvisibleState);
        appLayoutProductCard.setProductOffer(productViewModel);

        return appLayoutProductCard;
    }

    void navigateToProductCardDetail() {

        AppLayoutViewModel viewModel = getProductCard();
        ProductStatus mInvisible = new ProductStatus();
        mInvisible.setVisible(false);

        viewModel.getProductOffer().setFinancial(mInvisible);

        mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.PRODUCT_CARD_DETAIL)
                .withParam(za.co.nedbank.enroll_v2.Constants.NFPCreditCardParam.IS_PRE_LOGIN, true)
                .withParam(Constants.PRODUCT_CARD_STATUS, viewModel));
    }


    void navigateToLoanLayout() {
        mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BORROW_INTENT));
    }

    void navigateToMediaDetail(String link) {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CMS_MEDIA_CONTENT).withParam(ENABLE_JAVA_SCRIPT, true).withParam(CMS_URL, link));
    }

    void navigateToInsureLayout() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN));
    }

    void navigateToInvestLayout() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.BECOME_CLIENT));
    }

    void navigateToFinanceLayout() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FINANCIAL_PLANNER_HOME));
    }


    public void navigateToExpendedContentPage(MediaCardViewModel mediaCardViewModel) {
        if (mediaCardViewModel != null) {

            boolean doNotShowAgain = mApplicationStorage.getBoolean(StorageKeys.DATA_USAGE_NEVER_ASK_AGAIN_SALES_LANDING_SCREEN, false);

            if (StringUtils.isNullOrEmpty(mediaCardViewModel.getVideoId()) || doNotShowAgain) {
                showExpandedContent(mediaCardViewModel);
                return;
            }

            mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.DATA_USAGE_WARNING)
                            .withParam(Constants.FROM_SCREEN, Constants.SALES_LANDING))
                    .subscribe(navigationResult -> {
                        boolean accepted = navigationResult.getBooleanParam(NavigationTarget.RESULT_DATA_USAGE_WARNING_ACCEPT);
                        if (accepted) {
                            showExpandedContent(mediaCardViewModel);
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
    }

    private void showExpandedContent(MediaCardViewModel mediaCardViewModel) {
        if (mediaCardViewModel == null) {
            return;
        }
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.EXPENDED_CONTENT_PAGE)
                .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.EXTRA_MEDIA_CARD, mediaCardViewModel));
    }

    public void onClickContentItem(MediaCardViewModel mediaCardViewModel) {
        if (mediaCardViewModel != null && mediaCardViewModel.getRedirectUrl() != null && (AppUtility.isValidWebUrl(mediaCardViewModel.getRedirectUrl()))) {
            navigateToMediaDetail(mediaCardViewModel.getRedirectUrl());
        } else {
            navigateToExpendedContentPage(mediaCardViewModel);
        }
    }

    public void logMediaCardClickEvent(String mediaCardName, int position) {
        trackCompaignInitiationOnAppsflyer(mediaCardName);
        HashMap<String, Object> cData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_MARKETING_COMMUNICATION);
        adobeContextData.setFeature(TrackingParam.VAL_COMPAIGN);
        adobeContextData.setSubFeature(TrackingParam.VAL_MEDIA_CARDS);
        adobeContextData.setInternalCompaign(mediaCardName);
        adobeContextData.setImpressions();
        adobeContextData.setSequence(String.format(TrackingEvent.ANALYTICS.VAL_MEDIA_TILE_SEQUENCE, position));
        mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.KEY_NEDBANK_MEDIA_CARD, cData);
    }

    public void trackCompaignInitiationOnAppsflyer(String compaignName) {
        HashMap<String, Object> cdata = new HashMap<>();
        AddContextData addContextData = new AddContextData(cdata);
        addContextData.setCompaignName(compaignName);

        mAfAnalyticsTracker.sendEvent(AppsFlyerTags.AF_COMPAIGN_INITIATION, cdata);
    }
}