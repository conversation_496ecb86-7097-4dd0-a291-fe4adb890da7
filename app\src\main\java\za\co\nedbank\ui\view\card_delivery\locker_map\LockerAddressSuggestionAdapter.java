package za.co.nedbank.ui.view.card_delivery.locker_map;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import za.co.nedbank.core.domain.model.location.AddressSuggestion;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.enroll_v2.databinding.ItemLockerAddressBinding;
import za.co.nedbank.uisdk.adapter.BaseAdapter;

public class LockerAddressSuggestionAdapter extends BaseAdapter<AddressSuggestion> {


    private AddressSelectionListener addressSelectionListener;

    public LockerAddressSuggestionAdapter(Context context) {
        super(context);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        ItemLockerAddressBinding binding = ItemLockerAddressBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new LockerAddressViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, AddressSuggestion model, int position) {
        ((LockerAddressViewHolder) holder).bind(model);
    }

    public void setAddressSelectionListener(AddressSelectionListener listener) {
        addressSelectionListener = listener;
    }

    public class LockerAddressViewHolder extends RecyclerView.ViewHolder {

        TextView tvAddress;
        LinearLayout addressSuggestionContainer;

        public LockerAddressViewHolder(@NonNull ItemLockerAddressBinding binding) {
            super(binding.getRoot());
            tvAddress = binding.tvAddress;
            addressSuggestionContainer = binding.addressSuggestionContainer;
        }

        void bind(AddressSuggestion model) {
            tvAddress.setText(String.format("%s%s%s", model.primary, StringUtils.NEW_LINE, model.secondary));
            addressSuggestionContainer.setOnClickListener(v -> onClick());
        }

        void onClick() {
            if (addressSelectionListener != null) {
                addressSelectionListener.onAddressSelected(getItem(getAdapterPosition()));
            }
        }
    }

    interface AddressSelectionListener {
        void onAddressSelected(AddressSuggestion addressSuggestion);
    }
}
