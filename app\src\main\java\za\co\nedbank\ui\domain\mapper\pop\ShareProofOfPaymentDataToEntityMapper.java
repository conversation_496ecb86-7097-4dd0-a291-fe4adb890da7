package za.co.nedbank.ui.domain.mapper.pop;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.ui.data.entity.pop.SharePOPRecipientsRequestEntity;
import za.co.nedbank.ui.data.entity.pop.ShareProofOfPaymentRequestEntity;
import za.co.nedbank.ui.domain.model.pop.SharePopNotificationTypeData;
import za.co.nedbank.ui.domain.model.pop.ShareProofOfPaymentRequestData;

public class ShareProofOfPaymentDataToEntityMapper {

    @Inject
    public ShareProofOfPaymentDataToEntityMapper() {
        // Need this empty method for injection
    }

    public List<ShareProofOfPaymentRequestEntity> mapData(ShareProofOfPaymentRequestData shareProofOfPaymentData) {
        if (shareProofOfPaymentData == null) return new ArrayList<>();
        else return mapNotificationsData(shareProofOfPaymentData.getSharePopNotificationTypeData());
    }

    public SharePOPRecipientsRequestEntity mapDataMoreThan90Days(ShareProofOfPaymentRequestData shareProofOfPaymentData) {
        SharePOPRecipientsRequestEntity sharePOPRecipientsRequestEntity = new SharePOPRecipientsRequestEntity();
        if (null != shareProofOfPaymentData) {
            sharePOPRecipientsRequestEntity.setTransactionDate(shareProofOfPaymentData.getTransactionDate());
            sharePOPRecipientsRequestEntity.setTransactionKind(shareProofOfPaymentData.getTransactionKind());
            sharePOPRecipientsRequestEntity.setNotificationDetails(mapNotificationsData(shareProofOfPaymentData.getSharePopNotificationTypeData()));
        }
        return sharePOPRecipientsRequestEntity;
    }

    private List<ShareProofOfPaymentRequestEntity> mapNotificationsData(List<SharePopNotificationTypeData> sharePopNotificationTypeData) {
        if (sharePopNotificationTypeData == null) return new ArrayList<>();
        List<ShareProofOfPaymentRequestEntity> shareProofOfPaymentRequestEntityList = new ArrayList<>();
        for (SharePopNotificationTypeData sharePopNotificationTypeData1 : sharePopNotificationTypeData) {
            ShareProofOfPaymentRequestEntity shareProofOfPaymentEntity = new ShareProofOfPaymentRequestEntity();
            shareProofOfPaymentEntity.setNotificationType(sharePopNotificationTypeData1.getNotificationType());
            shareProofOfPaymentEntity.setNotificationAddress(sharePopNotificationTypeData1.getNotificationAddress());
            shareProofOfPaymentRequestEntityList.add(shareProofOfPaymentEntity);
        }
        return shareProofOfPaymentRequestEntityList;
    }
}
