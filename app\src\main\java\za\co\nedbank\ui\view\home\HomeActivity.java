/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home;

import static za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_APP_PIN_GUIDE;
import static za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_DEFAULT_CARD_GUIDE;
import static za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_FINGERPRINT_ALTERED;
import static za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_FINGERPRINT_GUIDE;
import static za.co.nedbank.core.Constants.BUNDLE_KEYS.UNBOXING_MOVE_TO_SCREEN;
import static za.co.nedbank.core.Constants.IS_CLIENT_DETAILS_API_CALLING_FROM_DASHBOARD;
import static za.co.nedbank.core.navigation.NavigationTarget.CARD_TAB_SELECTED;
import static za.co.nedbank.services.Constants.IS_TAG_YOUR_BANKER_FLOW;
import static za.co.nedbank.services.moneymanager.MoneyManagerConstants.HomeParams.IS_APP_OVERVIEW_SHOWN;
import static za.co.nedbank.services.moneymanager.MoneyManagerConstants.HomeParams.IS_MONEY_TRACKER_FLOW;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_CARDS;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_LATEST;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_MORE;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_NON_TP_ACCOUNTS;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_NON_TP_APPLY;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_OVERVIEW;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_RECIPIENTS;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_TRANSACT;
import static za.co.nedbank.ui.view.home.HomeActivity.IMenuPopState.CLOSED;
import static za.co.nedbank.ui.view.home.HomeActivity.IMenuPopState.IN_TRANSITION;
import static za.co.nedbank.ui.view.home.HomeActivity.IMenuPopState.OPENED;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.graphics.PorterDuff;
import android.graphics.drawable.Icon;
import android.os.Build;
import android.os.Build.VERSION_CODES;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;

import androidx.annotation.RequiresApi;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.google.firebase.messaging.FirebaseMessaging;
import com.ogaclejapan.arclayout.ArcLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.R;
import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.base.dialog.IDialog;
import za.co.nedbank.core.base.dialog.UserConsentDialog;
import za.co.nedbank.core.concierge.chat.view.NBChatBaseActivity;
import za.co.nedbank.core.convochatbot.view.ChatbotConstants;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.deeplink.DeeplinkUtils;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.notification.ErrorMsgEvent;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.tracking.ScanToPayTrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.NBAnimationUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.utils.XmsApiCheck;
import za.co.nedbank.core.view.IFragmentToActivityComListener;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.voc.SurveyType;
import za.co.nedbank.databinding.ActivityHomeBinding;
import za.co.nedbank.nid_sdk.main.views.pin_biometric.PinBiometricFlows;
import za.co.nedbank.payment.common.view.IFragmentToParentFragmentComListener;
import za.co.nedbank.payment.common.view.TabbedPagerFragment;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;
import za.co.nedbank.profile.view.more.MoreSettingsFragment;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.familybanking.view.createfamily.spouse.DeleteInviteSpouseDialog;
import za.co.nedbank.services.familybanking.view.member.details.delete.FBGroupDeletedDialog;
import za.co.nedbank.services.familybanking.view.member.members.exit.FBGroupExitedDialog;
import za.co.nedbank.services.view.account.branchcode.model.BranchCodeViewModel;
import za.co.nedbank.services.view.cards.virtual_card.AllCardsFragment;
import za.co.nedbank.ui.MenuOptions;
import za.co.nedbank.ui.app_shortcut.AppShortcutModel;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;
import za.co.nedbank.ui.view.home.latest.HomeLatestFragment;
import za.co.nedbank.ui.view.home.menu_animation.HomeMenuConstants;
import za.co.nedbank.ui.view.home.menu_animation.MENU;
import za.co.nedbank.ui.view.home.non_tp_client.non_tp_apply.NonTpApplyFragment;
import za.co.nedbank.ui.view.home.non_tp_client.non_tp_dashboard.NonTPAccountsFragment;
import za.co.nedbank.ui.view.home.overview.OverviewFragment;
import za.co.nedbank.ui.view.home.overview.OverviewPresenter;
import za.co.nedbank.uisdk.component.CompatButton;
import za.co.nedbank.uisdk.widget.NBSnackbar;

public class HomeActivity extends NBChatBaseActivity implements HomeView, IDialog, IFragmentToActivityComListener {

    private boolean isDashboardAdamOfferVisibleFirstTimeAfterLogin=true;
    private static final String TAG = HomeActivity.class.getCanonicalName();
    private final boolean mIsRecipientFlow = true;
    private FragmentManager mFragmentManager;
    private List<BranchCodeViewModel> branchCodeViewModelList;
    @Inject
    FeatureSetController featureController;
    @Inject
    HomePresenter mHomePresenter;
    private ActivityHomeBinding binding;


    public boolean isFatcaRestrictionScreenAlreadyShown() {
        return isFatcaRestrictionScreenAlreadyShown;
    }

    public void setFatcaRestrictionScreenAlreadyShown(boolean fatcaRestrictionScreenAlreadyShown) {
        isFatcaRestrictionScreenAlreadyShown = fatcaRestrictionScreenAlreadyShown;
    }

    private boolean isFatcaRestrictionScreenAlreadyShown = false;

    int menuPopState = CLOSED;
    private ShortcutManager mShortcutManager;
    private boolean fromScanToPayScreen = false;
    private boolean isDefaultCardGuide = false;
    private boolean isDemoModeDialog = false;
    private String birthDate;
    private String usedID;
    //targeted fragment in home screen to be navigated from push detail
    private String mFragmentToBeNavigated;
    private String mCardsFragmentToBeNavigated;
    private ClientType mClientType;
    private boolean isSAResident;
    private String mFbToken;

    //targeted fragment in home screen to be navigated from chatbot
    private String mChatbotFragmentToBeNavigated;

    //Media Deeplink
    private String mApplicationFragmentToBeNavigated;

    @Inject
    ApplicationStorage mApplicationStorage;

    @Inject
    @Named("memory")
    ApplicationStorage mMemoryApplicationStorage;

    @Override
    @RequiresApi(VERSION_CODES.JELLY_BEAN)
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityHomeBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);

        AppDI.getActivityComponent(this).inject(this);
        mHomePresenter.getBranchCodeListIfShareAccount(IntentUtils.getIntegerValue(getIntent(),
                UNBOXING_MOVE_TO_SCREEN, 0));
        mHomePresenter.handleFicaFlow();
        mHomePresenter.clearEdbFicaStorage();
        if (!featureController.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS)) {
            mHomePresenter.unsubscribeUnenrolledNotification();
            mHomePresenter.subscribeEnrolledNotification();
        }
        mFragmentManager = getSupportFragmentManager();
        mClientType = mHomePresenter.getClientType();
        Logger.getLogger("CLIENT_TYPE" + mClientType.getValue());
        initializeFragmentViews();
        boolean ftrNewToFranchiseUser = featureController.isFeatureDisabled(FeatureConstants.FTR_NEW_TO_FRANCHISE_USER);
        if (mClientType == ClientType.NON_TP || (!ftrNewToFranchiseUser && mClientType == ClientType.SALES)) {
            handleNonTpSalesUserDashboard();
        } else {
            binding.fabBuy.setElevation(10.0f);
            binding.fabPay.setElevation(10.0f);
            binding.fabTransfer.setElevation(10.0f);

            setMenuBasedOnSettings();
            setupGuide();
            setupScanToPayGuide();
            mHomePresenter.setupUUID();
            if (!featureController.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS)) {
                updateFirebaseTokenToServer();
                mHomePresenter.handleNotificationFlow(null);
            }
        }
        handlePinBiometricFlow();
        mHomePresenter.setLoginTimeStamp();
        mHomePresenter.getTrustedDeviceEcert();
        if (!NotificationUtils.areNotificationsEnabled(this)) {
            mHomePresenter.handleNotificationEnableFlow();
        }

        if (getIntent() != null && getIntent().getBooleanExtra(CARD_TAB_SELECTED, false)) {
            showCardsUI(false);
        }

        if (!featureController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_SAVE_RESUME)) {
            binding.homeMenuItemNonTpApply.setText(getString(R.string.applications));
        }
        setClickListeners();
    }

    private void setClickListeners() {
        binding.fabPay.setOnClickListener(v -> onPayMenuClick());
        binding.lnrPay.setOnClickListener(v -> onPayMenuClick());

        binding.fabTransfer.setOnClickListener(v -> onTransferMenuClick());
        binding.lnrTransfer.setOnClickListener(v -> onTransferMenuClick());

        binding.fabBuy.setOnClickListener(v -> onBuyMenuClick());
        binding.lnrBuy.setOnClickListener(v -> onBuyMenuClick());

        binding.frameBg.setOnClickListener(v -> onFabMenuClicked());
        binding.imgTransactActive.setOnClickListener(v -> onFabMenuClicked());

        binding.homeMenuItemLatest.setOnClickListener(v -> onHomeLatestClick());
        binding.homeMenuItemNonTpApply.setOnClickListener(v -> onApplyItemClick());
        binding.homeMenuItemNonTpAccounts.setOnClickListener(v -> onAccountsItemClick());
        binding.homeMenuItemMore.setOnClickListener(v -> onMoreMenuItemClick());
        binding.homeMenuItemRecipients.setOnClickListener(v -> onRecipientMenuItemClick());
        binding.homeMenuItemTransact.setOnClickListener(v -> onTransactClick());
        binding.homeMenuItemCards.setOnClickListener(v -> onCardsMenuItemClick());
        binding.homeMenuItemOverview.setOnClickListener(v -> onOverviewMenuItemClick());
        binding.overlayTouchAppPinGuide.skipGuide.setOnClickListener(v -> handleSkipGuide());
        binding.overlayTouchAppPinGuide.confirmGuide.setOnClickListener(v -> handleConfirmGuide());
    }

    @Override
    protected void handleChatMenuClicked(String conversationId) {
        if (conversationId.isEmpty())
            super.handleChatMenuClicked("Home");
        else
            super.handleChatMenuClicked(conversationId);
    }

    private void setMenuBasedOnSettings() {
        if (mIsRecipientFlow) {
            binding.homeMenuItemRecipients.setVisibility(View.VISIBLE);
        } else {
            binding.homeMenuItemRecipients.setVisibility(View.GONE);
        }
    }

    private void handleNonTpSalesUserDashboard() {
        binding.homeMenuItemTransact.setVisibility(View.GONE);
        binding.homeMenuItemOverview.setVisibility(View.GONE);
        binding.homeMenuItemCards.setVisibility(View.GONE);
        binding.homeMenuItemRecipients.setVisibility(View.GONE);
        binding.homeMenuItemMore.setVisibility(View.VISIBLE);
        binding.homeMenuItemNonTpAccounts.setVisibility(View.VISIBLE);
        binding.homeMenuItemNonTpApply.setVisibility(View.VISIBLE);
        binding.homeMenuItemLatest.setVisibility(View.VISIBLE);

        binding.homeMenuItemLatest.setNotificationDotVisibility(mApplicationStorage.getBoolean(za.co.nedbank.core.Constants.MediaUpdateIndicator.DASHBOARD_INDICATOR_VISIBILITY, true));
        binding.homeMenuItemMore.setTextColorSelector(R.color.home_menu_item_textview_selector_non_tp);
        binding.homeMenuItemMore.setIconSelectorDrawable(R.drawable.home_menu_non_tp_more_icon_selector);
        if (!featureController.isFeatureDisabled(FeatureConstants.FTR_NEW_TO_FRANCHISE_USER) && mClientType == ClientType.SALES) {
            showApplyNonTPUI();
        }
    }

    private void setupGuide() {
        fromScanToPayScreen = IntentUtils.getBooleanValue(getIntent(), ScanToPayTrackingEvent.SCAN_TO_PAY, false);
        boolean fromSecondLoginScreen = IntentUtils.getBooleanValue(getIntent(), NavigationTarget.FROM_SECOND_LOGIN_SCREEN, false);
        if (fromScanToPayScreen) {
            return;
        }
        boolean isFingerPrintAltered = IntentUtils.getBooleanValue(getIntent(), IS_FINGERPRINT_ALTERED, false);
        boolean isShowTutorialScreen = mHomePresenter.getStatusTutorialGuide(fromSecondLoginScreen, isFingerPrintAltered);
        if (isFingerPrintAltered) {
            showMoreOptionsUI(isFingerPrintAltered);
        } else if (isShowTutorialScreen) {
            ViewUtils.hideViews(binding.overlayTouchAppPinGuide.background);
        }
    }

    private void setupScanToPayGuide() {
        fromScanToPayScreen = IntentUtils.getBooleanValue(getIntent(), ScanToPayTrackingEvent.SCAN_TO_PAY, false);
        if (!fromScanToPayScreen) {
            return;
        }
        showMoreOptionsUI(false);
        boolean isFingerPrintGuide = IntentUtils.getBooleanValue(getIntent(), IS_FINGERPRINT_GUIDE, false);
        boolean isAppPinGuide = IntentUtils.getBooleanValue(getIntent(), IS_APP_PIN_GUIDE, false);
        isDefaultCardGuide = IntentUtils.getBooleanValue(getIntent(), IS_DEFAULT_CARD_GUIDE, false);

        binding.overlayTouchAppPinGuide.overlayBackground.setVisibility(View.VISIBLE);
        binding.overlayTouchAppPinGuide.heading.setText(getString(R.string.overlay_app_pin_guide_heading_stp));
        binding.overlayTouchAppPinGuide.nedbankLoader.getIndeterminateDrawable().setColorFilter(ContextCompat.getColor(this, R.color.white), PorterDuff.Mode.SRC_IN);
        ViewUtils.showViews(binding.overlayTouchAppPinGuide.background);
        binding.overlayTouchAppPinGuide.confirmGuide.setButtonType(CompatButton.ButtonType.GHOST);
        if (isFingerPrintGuide) {
            binding.overlayTouchAppPinGuide.subInfo.setText(getString(R.string.overlay_app_touch_guide_sub_info_stp));
            binding.overlayTouchAppPinGuide.guideTypeIcon.setImageResource(R.drawable.ic_wrapper_scanttopay_1);
            binding.overlayTouchAppPinGuide.heading.setTextColor(ContextCompat.getColor(this, R.color.black_333333));
            binding.overlayTouchAppPinGuide.subInfo.setTextColor(ContextCompat.getColor(this, R.color.gray_666666));
            binding.overlayTouchAppPinGuide.skipGuide.setButtonType(CompatButton.ButtonType.GHOST);
            binding.overlayTouchAppPinGuide.confirmGuide.setButtonType(CompatButton.ButtonType.PRIMARY);
            binding.overlayTouchAppPinGuide.backgroundFadeImage.setImageDrawable(ContextCompat.getDrawable(this, R.drawable.white_rectangle_fill));
        } else if (isAppPinGuide) {
            binding.overlayTouchAppPinGuide.guideTypeIcon.setImageResource(R.drawable.ic_app_pin);
            binding.overlayTouchAppPinGuide.subInfo.setText(getString(R.string.overlay_app_pin_guide_sub_info));
        } else if (isDefaultCardGuide) {
            binding.overlayTouchAppPinGuide.guideTypeIcon.setImageResource(R.drawable.ic_cards_block);
            binding.overlayTouchAppPinGuide.subInfo.setText(getString(R.string.overlay_set_default_card_guide_sub_info_stp));
        }
    }

    private void handlePinBiometricFlow() {
        if (getIntent() != null) {
            PinBiometricFlows pinBiometricFlow = (PinBiometricFlows) getIntent().getSerializableExtra(za.co.nedbank.core.Constants.BUNDLE_KEYS.PIN_BIOMETRIC_FLOW);
            if (pinBiometricFlow != null) {
                if (pinBiometricFlow == PinBiometricFlows.APP_PIN_ONLY) {
                    NBSnackbar.instance().header(getString(R.string.toast_app_pin_header)).action(getString(R.string.snackbar_action_done), () -> {
                    }).duration(NBSnackbar.FOREVER).build(binding.homeMenuPanel, getString(R.string.toast_app_pin_message));
                } else if (pinBiometricFlow == PinBiometricFlows.BIOMETRIC_ONLY || pinBiometricFlow == PinBiometricFlows.BIOMETRIC_THEN_APP_PIN || pinBiometricFlow == PinBiometricFlows.APP_PIN_THEN_BIOMETRIC) {
                    NBSnackbar.instance().header(getString(R.string.toast_biometric_header)).action(getString(R.string.snackbar_action_done), () -> {
                    }).duration(NBSnackbar.FOREVER).build(binding.homeMenuPanel, getString(R.string.toast_biometric_message));
                } else if (pinBiometricFlow == PinBiometricFlows.SKIP_PIN_N_BIOMETRIC) {
                    NBSnackbar.instance().header(getString(R.string.toast_nid_header)).action(getString(R.string.snackbar_action_ok), () -> {
                    }).duration(NBSnackbar.FOREVER).build(binding.homeMenuPanel, getString(R.string.toast_nid_message));
                }
            }
        }
    }

    @TargetApi(Build.VERSION_CODES.N_MR1)
    private void initializeShortcutManager() {
        mShortcutManager = (ShortcutManager) getSystemService(Context.SHORTCUT_SERVICE);

        if (!isFeatureDisabled(FeatureConstants.SCAN_TO_PAY_SCREEN) && mClientType == ClientType.TP && !mHomePresenter.isDemoMode()) {
            if (mShortcutManager.getDynamicShortcuts().size() == 0) {
                mHomePresenter.fetchDynamicShortCuts();
            }
        } else {
            if (mShortcutManager.getDynamicShortcuts().size() > 0) {
                mShortcutManager.removeAllDynamicShortcuts();
            }
        }
    }

    @TargetApi(Build.VERSION_CODES.N_MR1)
    public void publishShortcuts(List<AppShortcutModel> appShortcutList) {
        List<ShortcutInfo> shortcutList = new ArrayList<>();
        for (AppShortcutModel appShortcutModel : appShortcutList) {

            ShortcutInfo shortcutInfo = new ShortcutInfo.Builder(this, appShortcutModel.getId())
                    .setShortLabel(appShortcutModel.getShortLabel())
                    .setLongLabel(appShortcutModel.getLongLabel())
                    .setIcon(Icon.createWithResource(this, appShortcutModel.getIconResId()))
                    .setIntent(new Intent(appShortcutModel.getIntentAction()).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP |
                            Intent.FLAG_ACTIVITY_CLEAR_TASK |
                            Intent.FLAG_ACTIVITY_NEW_TASK))
                    .build();

            shortcutList.add(shortcutInfo);
        }

        mShortcutManager.setDynamicShortcuts(shortcutList);
    }

    protected void handleConfirmGuide() {
        mHomePresenter.handleConfirmGuide(fromScanToPayScreen, isDefaultCardGuide);
    }

    protected void handleSkipGuide() {
        if (fromScanToPayScreen) {
            mHomePresenter.trackSkipFromScanToPay();
            mHomePresenter.handleBackButtonClick();
            return;
        }
        mHomePresenter.handleSkipGuide();
    }

    @Override
    public void setUserInfo(UserDetailViewModel userDetailViewModel) {
        birthDate = userDetailViewModel.getBirthDate();
        isSAResident = !StringUtils.isNullOrEmpty(userDetailViewModel.getResident()) && userDetailViewModel.getResident().equalsIgnoreCase(FormattingUtil.SOUTH_AFRICA_CODE);
    }

    @Override
    public String getDeviceID() {
        return String.format("%s%s%s", Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID), StringUtils.UNDERSCORE, this.getPackageName());
    }

    @Override
    public void setUserId(String usedID) {
        this.usedID = usedID;
        mHomePresenter.saveUUID();
    }

    @Override
    public String getUserID() {
        return usedID;
    }

    @Override
    public void showError(final String errorMessage) {
        showError(getString(za.co.nedbank.profile.R.string.more_settings_error_title), errorMessage);
    }

    @Override
    public void showErrorWithOutTitle(final String errorMessage) {
        showError(StringUtils.EMPTY_STRING, errorMessage);
    }

    @Override
    public void hideTutorialGuide() {
        binding.overlayTouchAppPinGuide.overlayBackground.setVisibility(View.GONE);
    }

    @Override
    protected void onStart() {
        super.onStart();
        mHomePresenter.bind(this);
        if (!StringUtils.isNullOrEmpty(mFragmentToBeNavigated)) {
            mHomePresenter.navigateTo(mFragmentToBeNavigated);
            mFragmentToBeNavigated = null;
        }
        if (!StringUtils.isNullOrEmpty(mCardsFragmentToBeNavigated)) {
            mHomePresenter.navigateTo(mCardsFragmentToBeNavigated);
            mCardsFragmentToBeNavigated = null;
        }

        if (!StringUtils.isNullOrEmpty(mChatbotFragmentToBeNavigated)) {
            mHomePresenter.navigateToAsperChatBot(mChatbotFragmentToBeNavigated);
            mChatbotFragmentToBeNavigated = null;
        }

        Bundle bundle = getIntent().getExtras();
        if (bundle == null)
            return;
        if (bundle.containsKey(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_FOR_APPLICATION)) {
            mApplicationFragmentToBeNavigated = bundle.getString(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_FOR_APPLICATION);
            mHomePresenter.navigateToAsperChatBot(mApplicationFragmentToBeNavigated);
            mApplicationFragmentToBeNavigated = null;
        }

        mHomePresenter.getUpdatedTrustedDeviceEcert();
        checkAndShowFeatureDeepLinkScreen();
        mHomePresenter.handleRetentionFlow();
        mHomePresenter.handleITAEnrolmentFlow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            initializeShortcutManager();
        }
    }
    //Method created for calling from child fragment.
    public void checkAndShowFeatureDeepLinkScreen(){
        // checking if it is being called from feature detail,
        // if yes then show the appropriate screen
        mHomePresenter.checkAndShowFeatureDeepLinkScreen();
    }

    @Override
    protected void onResume() {
        super.onResume();
        AjoInAppEvent event = new AjoInAppEvent(this.getClass(), true);
        event.setForceShow(true);
        EventBus.getDefault().post(event);
        setEnabledActivityTouch(true);
        if (mHomePresenter != null) {
            mMemoryApplicationStorage.clearValue(za.co.nedbank.core.Constants.PAYMENT_START_FROM);
            mMemoryApplicationStorage.putBoolean(IS_CLIENT_DETAILS_API_CALLING_FROM_DASHBOARD, true);
            mHomePresenter.fetchUserDetail();
            mHomePresenter.updateRetentionStatus();

            mHomePresenter.checkUnboxingFlow(getIntent().getExtras(), null, null, null, null);
        }
        disableScreenShot(false);
    }

    public void unboxingFlowCheckOnAccountDetails(AccountSummary currentAccount, AccountSummary savingsAccount) {
        String accountType = StringUtils.EMPTY_STRING;

        if (currentAccount != null) {
            accountType = getString(za.co.nedbank.services.R.string.current_account_sa);
        } else if (savingsAccount != null) {
            accountType = getString(za.co.nedbank.services.R.string.savings_account_sa);
        }

        mHomePresenter.checkUnboxingFlow(getIntent().getExtras(), currentAccount, savingsAccount, accountType, this.branchCodeViewModelList);
    }

    @Override
    public void onBackPressed() {
        if (mClientType == ClientType.NON_TP) {
            Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
            if (!(fragment instanceof NonTPAccountsFragment)) {
                showAccountsNonTPUI();
                return;
            }
        } else if (mClientType == ClientType.TP) {
            if (menuPopState == OPENED) {
                mHomePresenter.toggleTransactionFabMenu();
                return;
            } else {
                Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
                if (!(fragment instanceof OverviewFragment)) {
                    showOverviewUI();
                    return;
                }
            }
        }

        if (mHomePresenter.isDemoMode()) {
            mHomePresenter.handleMemoryClearWhnEndDemo();
            super.onBackPressed();
            return;
        }
        isDemoModeDialog = false;
        UserConsentDialog.getInstance(getString(R.string.logout), getString(R.string.logout_dialog_message),
                getString(R.string.ok), getString(R.string.cancel)).show(getSupportFragmentManager(), UserConsentDialog.TAG);

    }

    @Override
    public void showDemoModeEnabledDialog() {
        isDemoModeDialog = true;
        UserConsentDialog.getInstance(null, getString(R.string.feature_not_available),
                getString(R.string.ok), null).show(getSupportFragmentManager(), UserConsentDialog.TAG);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent == null) return;
        Bundle bundle = intent.getExtras();
        if (bundle == null)
            return;

        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        if (isTabbedFragmentAdded(fragment)) {
            tabbedPagerFragment(bundle);
        } else if (isOverViewFragmentAdded(fragment)) {
            overviewFragment(bundle, fragment);
        }

        if (bundle.containsKey(ChatbotConstants.EXTRA.NAVIGATION_TARGET)) {
            mChatbotFragmentToBeNavigated = bundle.getString(ChatbotConstants.EXTRA.NAVIGATION_TARGET);
            mHomePresenter.navigateToAsperChatBot(mChatbotFragmentToBeNavigated);
            mFragmentToBeNavigated = bundle.getString(NotificationConstants.EXTRA.NAVIGATION_TARGET);

        }

        if (bundle.containsKey(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_FOR_APPLICATION)) {
            mApplicationFragmentToBeNavigated = bundle.getString(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_FOR_APPLICATION);
            mHomePresenter.navigateToAsperChatBot(mApplicationFragmentToBeNavigated);
            mFragmentToBeNavigated = bundle.getString(NotificationConstants.EXTRA.NAVIGATION_TARGET);
        }

        if (bundle.containsKey(NotificationConstants.EXTRA.NAVIGATION_TARGET_VIRTUAL_CARD)) {
            mCardsFragmentToBeNavigated = bundle.getString(NotificationConstants.EXTRA.NAVIGATION_TARGET_VIRTUAL_CARD);
            mHomePresenter.navigateTo(mCardsFragmentToBeNavigated);
        }

        if (bundle.containsKey(IS_MONEY_TRACKER_FLOW)) {
            handleMoneyTrackerFlow(bundle);
        }
        if (bundle.containsKey(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_RETENTION_FLOW) ||
                bundle.containsKey(za.co.nedbank.core.Constants.BUNDLE_KEYS.LOAD_OVERVIEW)) {
            if (!(fragment instanceof OverviewFragment)) {
                showOverviewUI();
            }
        }
        handleNavigationOnNewIntent(bundle);
    }

    private void handleNavigationOnNewIntent(Bundle bundle){
        if (isBankerFlowOrMiGoalsFlow(bundle)) {
            if (mClientType == ClientType.TP) {
                showOverviewUI();
            } else if (mClientType == ClientType.NON_TP) {
                initializeFragmentViews();
            }
        }
        if (bundle.getBoolean(za.co.nedbank.core.Constants.BUNDLE_KEYS.EXIT_FAMILY_GROUP_SUCCESSFULL)) {
            showExitFamilyGroupSuccess();
        }
        if (bundle.getBoolean(za.co.nedbank.core.Constants.BUNDLE_KEYS.DELETE_FAMILY_GROUP_SUCCESSFULL)) {
            deleteFamilyGroupSuccess(bundle);
        }
        if (bundle.containsKey(HomePresenter.DATA)) {
            mHomePresenter.handleFicaResponse(bundle.getBundle(HomePresenter.SCREEN_TYPE), bundle.getParcelable(HomePresenter.DATA));
        }
    }
    private boolean isTabbedFragmentAdded(Fragment fragment) {
        return fragment instanceof TabbedPagerFragment && fragment.isAdded();
    }

    private boolean isOverViewFragmentAdded(Fragment fragment) {
        return fragment instanceof OverviewFragment && fragment.isAdded();
    }

    private boolean isBankerFlowOrMiGoalsFlow(Bundle bundle) {
        return bundle.containsKey(IS_TAG_YOUR_BANKER_FLOW) || bundle.containsKey(Constants.EXTRAS.IS_MIGOALS_FLOW);
    }

    private void deleteFamilyGroupSuccess(Bundle bundle) {
        String deleteTitle = null;
        String deleteMsg = null;
        if (bundle.containsKey(za.co.nedbank.core.Constants.BUNDLE_KEYS.DELETE_FAMILY_GROUP_SUCCESSFULL_TITLE))
            deleteTitle = bundle.getString(za.co.nedbank.core.Constants.BUNDLE_KEYS.DELETE_FAMILY_GROUP_SUCCESSFULL_TITLE);
        if (bundle.containsKey(za.co.nedbank.core.Constants.BUNDLE_KEYS.DELETE_FAMILY_GROUP_SUCCESSFULL_MSG))
            deleteMsg = bundle.getString(za.co.nedbank.core.Constants.BUNDLE_KEYS.DELETE_FAMILY_GROUP_SUCCESSFULL_MSG);

        showDeleteFamilyGroupSuccess(deleteTitle, deleteMsg);
    }

    private void overviewFragment(Bundle bundle, Fragment fragment) {
        PaymentsViewModel paymentsViewModel = null;
        boolean isBeneficiaryListRefresh = false;
        boolean isQuickPayFlow = false;
        boolean isTransactionDone = false;
        boolean isInvalidateCache = false;

        if (bundle.containsKey(OverviewPresenter.DATA)) {
            ((OverviewFragment) fragment).handleFicaResponse(bundle.getInt(OverviewPresenter.FEATURE),
                    bundle.getParcelable(OverviewPresenter.DATA));
        }

        if (bundle.containsKey(PayNavigatorTarget.EXTRAS.PAY_MODEL)) {
            paymentsViewModel = bundle.getParcelable(PayNavigatorTarget.EXTRAS.PAY_MODEL);
        }
        if (bundle.containsKey(Constants.EXTRAS.IS_BENEFICIARY_LIST_REFRESH)) {
            isBeneficiaryListRefresh = bundle.getBoolean(Constants.EXTRAS.IS_BENEFICIARY_LIST_REFRESH);
        }
        if (bundle.containsKey(Constants.EXTRAS.IS_QUICK_PAY_FLOW)) {
            isQuickPayFlow = bundle.getBoolean(Constants.EXTRAS.IS_QUICK_PAY_FLOW);
        }
        if (bundle.containsKey(Constants.EXTRAS.IS_TRANSACTION_DONE)) {
            isTransactionDone = bundle.getBoolean(Constants.EXTRAS.IS_TRANSACTION_DONE);
            refreshDashBoardAdapter(isTransactionDone);
        }
        if (bundle.containsKey(za.co.nedbank.core.Constants.IS_INVALIDATE_CACHE)) {
            isInvalidateCache = bundle.getBoolean(za.co.nedbank.core.Constants.IS_INVALIDATE_CACHE);
        }
        if (isQuickPayFlow) {
            ((OverviewFragment) fragment).receiveUpdatedPaymentBundle(paymentsViewModel);
        }
        if (isBeneficiaryListRefresh) {
            handleBeneficiaryListRefreshEvent();
        }
        if (isTransactionDone || isInvalidateCache) {
            ((OverviewFragment) fragment).invalidateAccountApiCache();
        }
        if (bundle.containsKey(za.co.nedbank.profile.common.Constants.Extras.OVERVIEW_PAGE_KEY)) {
            String targetOverviewPage = bundle.getString(za.co.nedbank.profile.common.Constants.Extras.OVERVIEW_PAGE_KEY);
            if (targetOverviewPage != null && targetOverviewPage.equals(za.co.nedbank.profile.common.Constants.Extras.OVERVIEW_PAGE_REWARDS)) {
                ((OverviewFragment) fragment).setTargetOverviewPage(OverviewType.REWARDS);
            }
        }
    }

    private void tabbedPagerFragment(Bundle bundle) {
        handleRecipientListRefresh(bundle);
        if (bundle.containsKey(Constants.EXTRAS.IS_PAYAGAIN_FLOW)) {
            boolean isPayAgainFlow = bundle.getBoolean(Constants.EXTRAS.IS_PAYAGAIN_FLOW);
            clearRecipientSearch(isPayAgainFlow);
        }
    }

    private void handleRecipientListRefresh(Bundle bundle) {
        if (bundle.containsKey(Constants.EXTRAS.BENEFICIARY_LIST_REFRESH_MAP)) {
            Map<String, Object> resultMap = (Map<String, Object>) bundle.getSerializable(Constants.EXTRAS.BENEFICIARY_LIST_REFRESH_MAP);
            setResult(resultMap);
        }
    }

    private void showExitFamilyGroupSuccess() {
        FBGroupExitedDialog fbGroupExitedDialog = (FBGroupExitedDialog) FBGroupExitedDialog.getInstance();
        fbGroupExitedDialog.show(this.getSupportFragmentManager(), DeleteInviteSpouseDialog.TAG);
    }

    private void showDeleteFamilyGroupSuccess(String deleteTitle, String deleteMsg) {
        FBGroupDeletedDialog fbGroupDeletedDialog = (FBGroupDeletedDialog) FBGroupDeletedDialog.getInstance(deleteTitle, deleteMsg);
        fbGroupDeletedDialog.show(this.getSupportFragmentManager(), DeleteInviteSpouseDialog.TAG);
    }

    public void onOverviewMenuItemClick() {
        mHomePresenter.loadOverviewView();
    }

    public void onCardsMenuItemClick() {
        mHomePresenter.loadCardsView(false);
    }

    public void onTransactClick() {
        mHomePresenter.trackTransact();
        mHomePresenter.toggleTransactionFabMenu();
    }

    public void onRecipientMenuItemClick() {
        mHomePresenter.loadRecipientView();
    }

    public void onMoreMenuItemClick() {
        mHomePresenter.loadMoreOptionsView();
    }

    public void onAccountsItemClick() {
        mHomePresenter.loadAccountsNonTPOptionsView();
    }

    public void onApplyItemClick() {
        mHomePresenter.loadApplyNonTPOptionsView();
    }


    public void onPayMenuClick() {
        setEnabledActivityTouch(false);
        Bundle bundle = new Bundle();
        bundle.putInt(HomePresenter.SCREEN_TYPE, HomePresenter.PAY);
        mHomePresenter.checkFica(bundle);
    }

    public void onTransferMenuClick() {
        setEnabledActivityTouch(false);
        Bundle bundle = new Bundle();
        bundle.putInt(HomePresenter.SCREEN_TYPE, HomePresenter.TRANSFER);
        mHomePresenter.checkFica(bundle);
    }

    public void onBuyMenuClick() {
        setEnabledActivityTouch(false);
        Bundle bundle = new Bundle();
        bundle.putInt(HomePresenter.SCREEN_TYPE, HomePresenter.BUY);
        mHomePresenter.checkFica(bundle);
    }

    public void onFabMenuClicked() {
        if (menuPopState == OPENED) {
            mHomePresenter.toggleTransactionFabMenu();
        }
    }

    public void onHomeLatestClick() {
        mApplicationStorage.putBoolean(za.co.nedbank.core.Constants.MediaUpdateIndicator.DASHBOARD_INDICATOR_VISIBILITY, false);
        binding.homeMenuItemLatest.setNotificationDotVisibility(false);
        mHomePresenter.loadHomeLatestNonTpSalesUI();
    }

    private void updateFirebaseTokenToServer() {
        FirebaseMessaging.getInstance().getToken()
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        mFbToken = task.getResult();
                    } else {
                        NBLogger.e("Firebase", "getInstanceId failed", task.getException());
                    }
                });
    }

    private void initializeFragmentViews() {
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        Fragment fragmentToReplace = null;
        if (mClientType == ClientType.NON_TP) {
            showActiveMenuItem(MENU_OPTION_NON_TP_ACCOUNTS);
            if (fragment instanceof NonTPAccountsFragment)
                fragmentToReplace = fragment;
            if (fragmentToReplace == null)
                fragmentToReplace = NonTPAccountsFragment.getInstance();
        } else {
            showActiveMenuItem(MENU_OPTION_OVERVIEW);
            if (fragment instanceof OverviewFragment)
                fragmentToReplace = fragment;
            if (fragmentToReplace == null)
                fragmentToReplace = OverviewFragment.getInstance();
        }
        fragmentToReplaceInfo(fragmentToReplace);
    }

    private void fragmentToReplaceInfo(Fragment fragmentToReplace) {

        if (getIntent() != null && za.co.nedbank.core.Constants.FLOW_CONSTANTS.SHARE_POP.equalsIgnoreCase(getIntent().getStringExtra(za.co.nedbank.core.Constants.EXTRAS.SCREEN_TYPE))) {
            TabbedPagerFragment recipientFragment = TabbedPagerFragment.newInstance(Constants.ISearchListViewType.VIEW_TYPE_HOME, true, true,
                    true, true, true, false, false, getResources().getString(R.string.recipients), false, null, null, false, false, null, null, false);
            fragmentToReplace = recipientFragment;
            showActiveMenuItem(MENU_OPTION_RECIPIENTS);
            boolean isFromRecipientHistory = getIntent().getBooleanExtra(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_FROM_RECIPIENT_HISTORY, false);
            if (!isFromRecipientHistory) {
                showError(getString(R.string.snackbar_msg_recipient_added), null, getString(za.co.nedbank.enroll_v2.R.string.snackbar_action_done), () -> {
                });
            }
        }
        if (getIntent() != null && za.co.nedbank.core.Constants.FLOW_CONSTANTS.INTERNATIONAL_RECIPIENT.equalsIgnoreCase(getIntent().getStringExtra(za.co.nedbank.core.Constants.EXTRAS.SCREEN_TYPE))) {
            TabbedPagerFragment recipientFragment = TabbedPagerFragment.newInstance(Constants.ISearchListViewType.VIEW_TYPE_HOME, true, true,
                    true, true, true, false, false, getResources().getString(R.string.recipients), false, null, null, false, true, null, null, false);
            fragmentToReplace = recipientFragment;
            showActiveMenuItem(MENU_OPTION_RECIPIENTS);
            showRecipientMessage();
        }
        addFragment(mFragmentManager, fragmentToReplace, R.id.home_contentFrame);

    }

    private void showRecipientMessage() {
        String msg = getIntent().getStringExtra(NavigationTarget.PARAM_EXTRA_RECIPIENT_UPDATED_STATUS);
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    if (msg != null) {
                        showError(msg, StringUtils.EMPTY_STRING, getString(R.string.snackbar_action_done), () -> { //Do Nothing
                        });
                    }
                }
                , za.co.nedbank.payment.internationalpayment.Constants.UI_HANDLE_TIME);


    }

    @Override
    public void showOverviewUI() {
        showActiveMenuItem(MENU_OPTION_OVERVIEW);
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        OverviewFragment overviewFragment = null;
        if (fragment instanceof OverviewFragment) {
            overviewFragment = (OverviewFragment) fragment;
        }
        if (overviewFragment == null) {
            overviewFragment = OverviewFragment.getInstance();
        }
        replaceFragment(mFragmentManager, overviewFragment, R.id.home_contentFrame);
        updateOverviewUserDetailData();
        disableScreenShot(false);
    }

    private void updateOverviewUserDetailData() {
        mMemoryApplicationStorage.putBoolean(IS_CLIENT_DETAILS_API_CALLING_FROM_DASHBOARD, true);
        mHomePresenter.fetchUserDetail();
    }

    @Override
    public void showCardsUI(boolean isFromDeepLink) {
        showActiveMenuItem(MENU_OPTION_CARDS);
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        AllCardsFragment cardsFragment = null;
        if (fragment instanceof AllCardsFragment)
            cardsFragment = (AllCardsFragment) fragment;
        if (cardsFragment == null)
            cardsFragment = AllCardsFragment.getInstance(true, isFromDeepLink);
        replaceFragment(mFragmentManager, cardsFragment, R.id.home_contentFrame);
        binding.homeMenuItemCards.performAccessibilityAction(
                AccessibilityNodeInfo.ACTION_CLEAR_ACCESSIBILITY_FOCUS, null);
        disableScreenShot(true);
    }

    public void disableScreenShot(boolean isDisable) {
        if (!featureController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_SCREEN_SHARE)) {
            ViewUtils.disableScreenShot(getWindow(), isDisable);
        }
    }

    @Override
    public void toggleTransactionMenu() {
        if (menuPopState == CLOSED) {
            menuPopState = IN_TRANSITION;
            Animation fadeIn = new AlphaAnimation(HomeMenuConstants.ALPHA_MIN, HomeMenuConstants.ALPHA_MAX);
            fadeIn.setInterpolator(new DecelerateInterpolator());
            fadeIn.setDuration(HomeMenuConstants.FADE_ANIMATION_DURATION);

            fadeIn.setAnimationListener(new NBAnimationUtils.AnimationAdapter() {
                @Override
                public void onAnimationStart(Animation animation) {
                    super.onAnimationStart(animation);
                    binding.frameBg.setVisibility(View.VISIBLE);
                    binding.homeMenuPanel.setVisibility(View.INVISIBLE);
                    binding.imgTransactActive.setVisibility(View.VISIBLE);
                }

            });
            binding.frameBg.startAnimation(fadeIn);
            valueShowAnimateViews();

        } else if (menuPopState == OPENED) {
            menuPopState = IN_TRANSITION;
            toggleTextViews(false);
            valueHideAnimateViews();

            Animation fadeOut = new AlphaAnimation(HomeMenuConstants.ALPHA_MAX, HomeMenuConstants.ALPHA_MIN);
            fadeOut.setInterpolator(new AccelerateInterpolator());
            fadeOut.setDuration(HomeMenuConstants.FADE_ANIMATION_DURATION);

            fadeOut.setAnimationListener(new NBAnimationUtils.AnimationAdapter() {
                @Override
                public void onAnimationEnd(Animation animation) {
                    super.onAnimationEnd(animation);
                    binding.frameBg.setVisibility(View.INVISIBLE);
                    binding.homeMenuPanel.setVisibility(View.VISIBLE);
                    binding.imgTransactActive.setVisibility(View.INVISIBLE);
                    binding.homeMenuItemTransact.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED);
                    binding.homeMenuItemTransact.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED);
                }
            });
            binding.frameBg.startAnimation(fadeOut);
        }
    }

    private void valueShowAnimateViews() {

        for (int i = 0; i < binding.arcMenu.getChildCount(); i++) {
            ArcLayout.LayoutParams params = (ArcLayout.LayoutParams) (binding.arcMenu.getChildAt(i)).getLayoutParams();
            params.angle = HomeMenuConstants.DEFAULT_ANGLE;
            binding.arcMenu.getChildAt(i).setLayoutParams(params);
            binding.arcMenu.getChildAt(i).invalidate();
            binding.arcMenu.invalidate();
        }

        View transferView = binding.arcMenu.getChildAt(MENU.TRANSFER.getChildPosition());
        ArcLayout.LayoutParams transParams = (ArcLayout.LayoutParams) transferView.getLayoutParams();
        ValueAnimator transAnimator = ValueAnimator.ofFloat(MENU.TRANSFER.getStartAngle(), MENU.TRANSFER.getEndAngle());
        transAnimator.addUpdateListener(valueAnimator -> {
            transParams.angle = (Float) valueAnimator.getAnimatedValue();
            transferView.requestLayout();
        });
        transAnimator.setDuration(MENU.TRANSFER.getAnimateDuration());

        View payView = binding.arcMenu.getChildAt(MENU.PAY.getChildPosition());
        ArcLayout.LayoutParams payParams = (ArcLayout.LayoutParams) payView.getLayoutParams();
        ValueAnimator payAnimator = ValueAnimator.ofFloat(MENU.PAY.getStartAngle(), MENU.PAY.getEndAngle());
        payAnimator.addUpdateListener(valueAnimator -> {
            payParams.angle = (Float) valueAnimator.getAnimatedValue();
            payView.requestLayout();
        });
        payAnimator.setDuration(MENU.PAY.getAnimateDuration());

        View buyView = binding.arcMenu.getChildAt(MENU.BUY.getChildPosition());
        ArcLayout.LayoutParams butParams = (ArcLayout.LayoutParams) buyView.getLayoutParams();
        ValueAnimator buyAnimator = ValueAnimator.ofFloat(MENU.BUY.getStartAngle(), MENU.BUY.getEndAngle());
        buyAnimator.addUpdateListener(valueAnimator -> {
            butParams.angle = (Float) valueAnimator.getAnimatedValue();
            buyView.requestLayout();
        });
        buyAnimator.setDuration(MENU.BUY.getAnimateDuration());


        List<Animator> animList = new ArrayList<>();
        animList.add(transAnimator);
        animList.add(payAnimator);
        animList.add(buyAnimator);

        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new NBAnimationUtils.AnimatorAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                menuPopState = OPENED;
                toggleTextViews(true);
            }
        });
        animSet.setInterpolator(new LinearInterpolator());
        animSet.playSequentially(animList);
        animSet.start();
    }

    private void valueHideAnimateViews() {

        View transferView = binding.arcMenu.getChildAt(MENU.TRANSFER.getChildPosition());
        ArcLayout.LayoutParams transParams = (ArcLayout.LayoutParams) transferView.getLayoutParams();
        ValueAnimator transAnimator = ValueAnimator.ofFloat(MENU.TRANSFER.getEndAngle(), MENU.TRANSFER.getStartAngle());
        transAnimator.addUpdateListener(valueAnimator -> {
            transParams.angle = (Float) valueAnimator.getAnimatedValue();
            transferView.requestLayout();
        });
        transAnimator.addListener(new NBAnimationUtils.AnimatorAdapter() {
            @Override
            public void onAnimationEnd(Animator animator) {
                super.onAnimationEnd(animator);
                transParams.angle = HomeMenuConstants.DEFAULT_ANGLE;
                transferView.requestLayout();
            }
        });
        transAnimator.setDuration(MENU.TRANSFER.getAnimateDuration());

        View payView = binding.arcMenu.getChildAt(MENU.PAY.getChildPosition());
        ArcLayout.LayoutParams payParams = (ArcLayout.LayoutParams) payView.getLayoutParams();
        ValueAnimator payAnimator = ValueAnimator.ofFloat(MENU.PAY.getEndAngle(), MENU.PAY.getStartAngle());
        payAnimator.addUpdateListener(valueAnimator -> {
            payParams.angle = (Float) valueAnimator.getAnimatedValue();
            payView.requestLayout();
        });
        payAnimator.addListener(new NBAnimationUtils.AnimatorAdapter() {
            @Override
            public void onAnimationEnd(Animator animator) {
                super.onAnimationEnd(animator);
                payParams.angle = HomeMenuConstants.DEFAULT_ANGLE;
                payView.requestLayout();
            }
        });
        payAnimator.setDuration(MENU.PAY.getAnimateDuration());

        View buyView = binding.arcMenu.getChildAt(MENU.BUY.getChildPosition());
        ArcLayout.LayoutParams butParams = (ArcLayout.LayoutParams) buyView.getLayoutParams();
        ValueAnimator buyAnimator = ValueAnimator.ofFloat(MENU.BUY.getEndAngle(), MENU.BUY.getStartAngle());
        buyAnimator.addUpdateListener(valueAnimator -> {
            butParams.angle = (Float) valueAnimator.getAnimatedValue();
            buyView.requestLayout();
        });
        buyAnimator.addListener(new NBAnimationUtils.AnimatorAdapter() {
            @Override
            public void onAnimationEnd(Animator animator) {
                super.onAnimationEnd(animator);
                butParams.angle = HomeMenuConstants.DEFAULT_ANGLE;
                buyView.requestLayout();
            }
        });
        buyAnimator.setDuration(MENU.BUY.getAnimateDuration());

        List<Animator> animList = new ArrayList<>();
        animList.add(buyAnimator);
        animList.add(payAnimator);
        animList.add(transAnimator);

        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new NBAnimationUtils.AnimatorAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                menuPopState = CLOSED;
            }
        });
        animSet.setInterpolator(new LinearInterpolator());
        animSet.playSequentially(animList);
        animSet.start();
    }


    @Override
    public void resetMenuWithoutAnimation() {
        for (int i = 0; i < binding.arcMenu.getChildCount(); i++) {
            ArcLayout.LayoutParams params = (ArcLayout.LayoutParams) (binding.arcMenu.getChildAt(i)).getLayoutParams();
            params.angle = HomeMenuConstants.DEFAULT_ANGLE;
            binding.arcMenu.getChildAt(i).setLayoutParams(params);
            binding.arcMenu.getChildAt(i).invalidate();
        }
        menuPopState = CLOSED;
        binding.frameBg.setVisibility(View.INVISIBLE);
        binding.homeMenuPanel.setVisibility(View.VISIBLE);
        binding.imgTransactActive.setVisibility(View.INVISIBLE);
        toggleTextViews(false);
    }

    private void toggleTextViews(boolean isShow) {
        findViewById(R.id.txvPay).setVisibility(isShow ? View.VISIBLE : View.INVISIBLE);
        findViewById(R.id.txvBuy).setVisibility(isShow ? View.VISIBLE : View.INVISIBLE);
        findViewById(R.id.txvTransfer).setVisibility(isShow ? View.VISIBLE : View.INVISIBLE);

        // accessibility
        if (!isShow) {
            binding.homeMenuPanel.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
            binding.llParentLayout.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
            binding.overlayTouchAppPinGuide.overlayBackground.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
            binding.frameBg.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
        } else {
            binding.lnrTransfer.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED);
            binding.lnrTransfer.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED);
            if (Build.VERSION.SDK_INT >= VERSION_CODES.KITKAT) {
                binding.homeMenuPanel.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
                binding.llParentLayout.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
                binding.overlayTouchAppPinGuide.overlayBackground.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
                binding.frameBg.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
            }
        }
    }

    @Override
    public void showRecipientUI() {
        showActiveMenuItem(MENU_OPTION_RECIPIENTS);
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        if (fragment instanceof TabbedPagerFragment) {
            // if fragment is already added then skip adding it again to same container
            return;
        }
        TabbedPagerFragment recipientFragment = TabbedPagerFragment.newInstance(Constants.ISearchListViewType.VIEW_TYPE_HOME, true, true,
                true, true, true, false, false, getResources().getString(R.string.recipients), false, null, null, false, false, null, null, false);
        replaceFragment(mFragmentManager, recipientFragment, R.id.home_contentFrame);
        disableScreenShot(false);
    }

    private void handleMoneyTrackerFlow(Bundle bundle) {

        boolean isAppOverViewShown = false;
        if (bundle.containsKey(IS_APP_OVERVIEW_SHOWN)) {
            isAppOverViewShown = bundle.getBoolean(IS_APP_OVERVIEW_SHOWN);
        }
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        if (isAppOverViewShown && !(fragment instanceof OverviewFragment)) {
            showOverviewUI();
        }
    }

    @Override
    public void showApplyNonTPUI() {
        mMemoryApplicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
        showActiveMenuItem(MENU_OPTION_NON_TP_APPLY);
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        NonTpApplyFragment nonTPApplyFragment = null;
        if (fragment instanceof NonTpApplyFragment) {
            nonTPApplyFragment = (NonTpApplyFragment) fragment;
        }
        if (nonTPApplyFragment == null) {
            nonTPApplyFragment = NonTpApplyFragment.getInstance(false);
        }
        replaceFragment(mFragmentManager, nonTPApplyFragment, R.id.home_contentFrame);
        disableScreenShot(false);
    }

    @Override
    public void showAccountsNonTPUI() {
        showActiveMenuItem(MENU_OPTION_NON_TP_ACCOUNTS);
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        NonTPAccountsFragment nonTPAccountsFragment = null;
        if (fragment instanceof NonTPAccountsFragment) {
            nonTPAccountsFragment = (NonTPAccountsFragment) fragment;
        }
        if (nonTPAccountsFragment == null) {
            nonTPAccountsFragment = NonTPAccountsFragment.getInstance();
        }
        replaceFragment(mFragmentManager, nonTPAccountsFragment, R.id.home_contentFrame);
        disableScreenShot(false);
    }

    @Override
    public void showLatestNonTpSalesUI() {
        showActiveMenuItem(MENU_OPTION_LATEST);
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        HomeLatestFragment homeLatestFragment = null;
        if (fragment instanceof HomeLatestFragment) {
            homeLatestFragment = (HomeLatestFragment) fragment;
        }
        if (homeLatestFragment == null) {
            homeLatestFragment = HomeLatestFragment.getInstance();
        }
        homeLatestFragment.setNonTpUILatest(true);
        replaceFragment(mFragmentManager, homeLatestFragment, R.id.home_contentFrame);
        disableScreenShot(false);
    }

    @Override
    public void showMoreOptionsUI(boolean isFingerPrintAltered) {
        showActiveMenuItem(MENU_OPTION_MORE);
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        MoreSettingsFragment moreFragment = null;
        if (fragment instanceof MoreSettingsFragment)
            moreFragment = (MoreSettingsFragment) fragment;
        if (moreFragment == null)
            moreFragment = MoreSettingsFragment.getInstance(isFingerPrintAltered);
        replaceFragment(mFragmentManager, moreFragment, R.id.home_contentFrame);
        disableScreenShot(false);
    }

    @Override
    public void setResult(Map<String, Object> resultMap) {
        if (resultMap != null && resultMap.size() > 0) {
            Object isBeneficiaryListRefresh = resultMap.get(Constants.EXTRAS.IS_BENEFICIARY_LIST_REFRESH);
            if (isBeneficiaryListRefresh instanceof Boolean && (Boolean) isBeneficiaryListRefresh) {
                handleBeneficiaryListRefreshEvent();
            }
            Object errorModel = resultMap.get(Constants.EXTRAS.ERROR_MESSAGE);
            if (errorModel instanceof String[]) {
                String[] errorStrings = (String[]) errorModel;
                if (errorStrings.length > 0) {
                    NBSnackbar.instance().action(getString(R.string.snackbar_dismiss), () -> {
                    }).build(binding.homeMenuPanel, getString(R.string.few_recipient_failed_error));
                }
            }

            Object isTransactionDone = resultMap.get(Constants.EXTRAS.IS_TRANSACTION_DONE);
            refreshDashBoardAccountData(isTransactionDone);
        }
    }

    private void refreshDashBoardAccountData(Object isTransactionDone) {
        if (isTransactionDone instanceof Boolean && (Boolean) isTransactionDone) {
            Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
            if (fragment instanceof OverviewFragment) {
                OverviewFragment overviewFragment = (OverviewFragment) fragment;
                if (overviewFragment.isAdded() && overviewFragment.isVisible()) {
                    overviewFragment.invalidateAccountApiCache();
                }
            }
        }
    }

    private void refreshDashBoardAdapter(Object isTransactionDone) {
        if (isTransactionDone instanceof Boolean && (Boolean) isTransactionDone) {
            Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
            if (fragment instanceof OverviewFragment) {
                OverviewFragment overviewFragment = (OverviewFragment) fragment;
                if (overviewFragment.isAdded() && overviewFragment.isVisible()) {
                    overviewFragment.initDashboardAdapter();
                }
            }
        }
    }

    private void handleBeneficiaryListRefreshEvent() {
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        if (fragment instanceof TabbedPagerFragment) {
            TabbedPagerFragment tabbedPagerFragment = (TabbedPagerFragment) fragment;
            if (tabbedPagerFragment.isAdded() && tabbedPagerFragment.isVisible()) {
                //refresh entire view after adding the recipient
                try {
                    tabbedPagerFragment.setUpViewPager(Constants.ISearchListViewType.VIEW_TYPE_HOME);
                } catch (Exception e) {
                    NBLogger.e("Exception", e.getMessage());
                }
            }
        } else if (fragment instanceof OverviewFragment) {
            OverviewFragment overviewFragment = (OverviewFragment) fragment;
            if (overviewFragment.isAdded() && overviewFragment.isVisible()) {
                overviewFragment.updateUserBeneficiaryData();
            }
        }
    }

    private void clearRecipientSearch(boolean isPayAgainFlow) {
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        if (isPayAgainFlow && fragment instanceof TabbedPagerFragment) {
            TabbedPagerFragment tabbedPagerFragment = (TabbedPagerFragment) fragment;
            if (tabbedPagerFragment.isAdded() && tabbedPagerFragment.isVisible()) {
                //refresh entire view after adding the recipient
                tabbedPagerFragment.clearSearch();
            }
        }
    }

    @Override
    public boolean isFeatureDisabled(String feature) {
        return featureController.isFeatureDisabled(feature);
    }

    private void showActiveMenuItem(@MenuOptions int menuItem) {
        binding.homeMenuItemOverview.setSelected(false);
        binding.homeMenuItemCards.setSelected(false);
        binding.homeMenuItemTransact.setSelected(false);
        binding.homeMenuItemRecipients.setSelected(false);
        binding.homeMenuItemMore.setSelected(false);
        binding.homeMenuItemNonTpAccounts.setSelected(false);
        binding.homeMenuItemNonTpApply.setSelected(false);
        binding.homeMenuItemLatest.setSelected(false);

        switch (menuItem) {
            case MENU_OPTION_OVERVIEW:
                binding.homeMenuItemOverview.setSelected(true);
                break;
            case MENU_OPTION_CARDS:
                binding.homeMenuItemCards.setSelected(true);
                break;
            case MENU_OPTION_TRANSACT:
                binding.homeMenuItemTransact.setSelected(true);
                break;
            case MENU_OPTION_RECIPIENTS:
                binding.homeMenuItemRecipients.setSelected(true);
                break;
            case MENU_OPTION_MORE:
                binding.homeMenuItemMore.setSelected(true);
                break;

            case MENU_OPTION_NON_TP_ACCOUNTS:
                binding.homeMenuItemNonTpAccounts.setSelected(true);
                break;
            case MENU_OPTION_NON_TP_APPLY:
                binding.homeMenuItemNonTpApply.setSelected(true);
                break;
            case MENU_OPTION_LATEST:
                binding.homeMenuItemLatest.setSelected(true);
                break;
            default:
        }
    }

    @Override
    public void onEvent(int event, Bundle bundle) {
        switch (event) {
            case IFragmentToParentFragmentComListener.EVENT_CONSTANTS.EVENT_USER_BENEFICIARY_SELECTED:
                if (null != bundle) {
                    mHomePresenter.handleRecipientItemClick(bundle);
                }
                break;
            case IFragmentToParentFragmentComListener.EVENT_CONSTANTS.EVENT_INTERNATIONAL_BENEFICIARY_SELECTED:
                mHomePresenter.handleInternationalRecipientDetailClick(bundle);
                break;
            case za.co.nedbank.core.Constants.EVENT_CONSTANTS.EVENT_ADD_RECIPIENT_CLICKED:
                mHomePresenter.handleAddRecipient();
                break;
            case EVENT_CONSTANTS.EVENT_NOTIFICATIONS_MORE:
                mHomePresenter.handleNotificationsFromMoreClicked();
                break;
            case EVENT_CONSTANTS.EVENT_NAVIGATE_TO_OVERVIEW:
                showOverviewUI();
                break;
            case EVENT_CONSTANTS.EVENT_APPLY:
                mHomePresenter.handleApplyItemClick(true);
                break;
            case EVENT_CONSTANTS.EVENT_APPLY_CLOSE:
                mHomePresenter.loadMoreOptionsView();
                break;
            case EVENT_CONSTANTS.EVENT_GETCASH:
                EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
                break;
            default:
        }
    }

    @Override
    public void onPositiveButtonClick() {
        if (!isDemoModeDialog)
            mHomePresenter.handleBackButtonClick();
    }

    @Override
    public void onNegativeButtonClick() {
        // Nothing required on negative buttoon click
    }

    @Override
    public String getBirthDate() {
        return birthDate;
    }

    public boolean isSAResident() {
        return isSAResident;
    }

    public boolean isDashboardAdamOfferVisibleFirstTimeAfterLogin() {
        return isDashboardAdamOfferVisibleFirstTimeAfterLogin;
    }

    public void setDashboardAdamOfferVisibleFirstTimeAfterLogin(boolean dashboardAdamOfferVisibleFirstTimeAfterLogin) {
        isDashboardAdamOfferVisibleFirstTimeAfterLogin = dashboardAdamOfferVisibleFirstTimeAfterLogin;
    }

    interface IMenuPopState {
        int OPENED = 1;
        int CLOSED = 2;
        int IN_TRANSITION = 3;
    }

    @Override
    @Deprecated
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == za.co.nedbank.services.Constants.REQUEST_CODE_PUSH_TOKENIZE) {
            if (resultCode == RESULT_OK)
                mHomePresenter.navigateToGPaySuccessScreen();
            else
                mHomePresenter.navigateToGPayFailureScreen();
        } else if (requestCode == za.co.nedbank.services.Constants.REQUEST_CODE_CREATE_GOOGLE_WALLET) {
            Fragment fragment = getSupportFragmentManager().findFragmentById(R.id.home_contentFrame);
            if (fragment != null) {
                fragment.onActivityResult(requestCode, resultCode, data);
            }
        } else if (resultCode == RESULT_OK && data != null) {
            String errorTitle = data.getStringExtra(za.co.nedbank.core.Constants.DEEP_LINKING_RESULT_TITLE);
            String errorMessage = data.getStringExtra(za.co.nedbank.core.Constants.DEEP_LINKING_RESULT_MESSAGE);

            if (errorMessage != null) {
                showError(errorTitle, errorMessage);
            }

        }
    }

    @Override
    public boolean isITAEnrolled() {
        return APIInformation.getInstance().isITAEnrolled();
    }

    @Override
    public boolean isShowITA() {
        return APIInformation.getInstance().isShowITA();
    }

    @Override
    public void setOverviewUserInfo(boolean isSuccess, UserDetailData userDetailData, Error error) {
        Fragment fragment = mFragmentManager.findFragmentById(R.id.home_contentFrame);
        boolean isBusinessUser = userDetailData!=null && userDetailData.getClientType() != null && Integer.parseInt(userDetailData.getClientType())
                > za.co.nedbank.services.Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;
        mMemoryApplicationStorage.putBoolean(StorageKeys.IS_BUSINESS_USER, isBusinessUser);
        if (fragment instanceof OverviewFragment) {
            OverviewFragment overviewFragment = (OverviewFragment) fragment;
            if (overviewFragment.isAdded() && overviewFragment.isVisible()) {
                overviewFragment.setUserDetailData(isSuccess, userDetailData, error);
            }
        }
        if (userDetailData != null) {
            mHomePresenter.trackMyAccountScreen(isSuccess ? userDetailData.getClientType() : null, isSuccess ? getClientIdType(userDetailData.getPassportNumber(), userDetailData.getIdOrTaxIdNumber()) : StringUtils.EMPTY_STRING, isSuccess ? getStaffOrNonStaff(userDetailData.getIsNedbankEmployee()) : StringUtils.EMPTY_STRING, userDetailData.getSecOfficerCd());
        } else {
            mHomePresenter.trackMyAccountScreen(StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        }
    }

    private String getClientIdType(String passportNo, String idOrTaxIdNo) {
        if (StringUtils.isNotEmpty(passportNo) && StringUtils.isNotEmpty(idOrTaxIdNo)) {
            return TrackingParam.VAL_SA_ID_OR_PASSPORT;
        } else if (StringUtils.isNotEmpty(passportNo)) {
            return TrackingParam.VAL_PASSPORT;
        } else if (StringUtils.isNotEmpty(idOrTaxIdNo)) {
            return TrackingParam.VAL_SA_ID;
        } else {
            return StringUtils.EMPTY_STRING;
        }
    }

    private String getStaffOrNonStaff(boolean isNedbankEmployee) {
        return isNedbankEmployee ? TrackingParam.VAL_STAFF : TrackingParam.VAL_NON_STAFF;
    }

    @Override
    public String getFbToken() {
        return mFbToken;
    }

    @Override
    public boolean isHmsApiPreferred() {
        return XmsApiCheck.isHmsApiPreferred(this);
    }

    @Override
    public Map<String, Object> convertStringToHashmap(String jsonString) {
        Map<String, Object> outputMap = new HashMap<>();
        try {
            JSONObject jsonObject = new JSONObject(jsonString);
            Iterator<String> keysItr = jsonObject.keys();
            if (keysItr != null) {
                while (keysItr.hasNext()) {
                    String key = keysItr.next();
                    outputMap.put(key, jsonObject.get(key));
                }
            }

        } catch (Exception e) {
            NBLogger.e(TAG, e.getMessage());
        }
        return outputMap;
    }

    @Override
    public String convertHashmapToString(Map<String, Object> hashMap) {
        JSONObject jsonObject = new JSONObject(hashMap);
        return jsonObject.toString();
    }

    @Override
    public void setBranchCodeList(List<BranchCodeViewModel> branchCodeViewModelList) {
        this.branchCodeViewModelList = branchCodeViewModelList;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void showErrorMsg(ErrorMsgEvent errorMsgEvent) {
        showError(errorMsgEvent.getTitle(),
                errorMsgEvent.getMessage(),
                errorMsgEvent.getActionButton(),
                () -> {
                });
    }
}