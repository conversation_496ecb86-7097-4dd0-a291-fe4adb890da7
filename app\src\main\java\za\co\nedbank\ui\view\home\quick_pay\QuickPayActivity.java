package za.co.nedbank.ui.view.home.quick_pay;

import android.os.Bundle;

import org.greenrobot.eventbus.EventBus;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.databinding.ActivityQuickPayBinding;
import za.co.nedbank.ui.di.AppDI;

public class QuickPayActivity extends NBBaseActivity implements QuickPayView {

    @Inject
    QuickPayPresenter mQuickPayPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        super.onCreate(savedInstanceState);
        ActivityQuickPayBinding binding = ActivityQuickPayBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        mQuickPayPresenter.bind(this);

        initToolbar(binding.toolbar, true, getResources().getString(R.string.quick_pay));
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
    }
}