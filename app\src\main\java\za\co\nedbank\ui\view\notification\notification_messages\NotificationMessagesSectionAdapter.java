package za.co.nedbank.ui.view.notification.notification_messages;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.text.Html;
import android.view.HapticFeedbackConstants;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import za.co.nedbank.R;
import za.co.nedbank.core.base.adapter.BaseSectionedAdapter;
import za.co.nedbank.core.base.adapter.Section;
import za.co.nedbank.core.base.adapter.SectionAdapterItem;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.databinding.ItemNotificationMessagesBinding;
import za.co.nedbank.databinding.ItemNotificationSectionBinding;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;


public class NotificationMessagesSectionAdapter extends BaseSectionedAdapter<FBNotificationsViewModel> implements MultipleSelectionHandler.OnSelectionChangeListener {

    private final Context context;
    private OnMessageItemClickListener mOnMessageItemClickListener;
    private MultipleSelectionHandler mMultipleSelectionHandler;
    private MultipleSelectionHandler.OnSelectionChangeListener mOnSelectionChangedListener;

    private List<SectionAdapterItem<FBNotificationsViewModel>> mItemsToBeDeleted = new ArrayList<>();
    private List<SectionAdapterItem<FBNotificationsViewModel>> mLastItemSnapShot = new ArrayList<>();
    private boolean mHasLongPressed;

    public NotificationMessagesSectionAdapter(final Context context) {
        super(context);
        setUseLoadingMore(true);
        this.context = context;
        mMultipleSelectionHandler = new MultipleSelectionHandler(this);

    }

    @Override
    protected List<Section<FBNotificationsViewModel>> getSections(List<FBNotificationsViewModel> items) {
        List<Section<FBNotificationsViewModel>> sections = new ArrayList<>();
        sections.add(new Section<FBNotificationsViewModel>(context.getString(R.string.notification_section_label_today)) {
            @Override
            public boolean fitSectionCriteria(FBNotificationsViewModel model) {
                return NotificationUtils.isToday(model.getNotificationDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            }
        });

        sections.add(new Section<FBNotificationsViewModel>(context.getString(R.string.notification_section_label_yesterday)) {
            @Override
            public boolean fitSectionCriteria(FBNotificationsViewModel model) {
                return NotificationUtils.isYesterday(model.getNotificationDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            }
        });

        sections.add(new Section<FBNotificationsViewModel>(context.getString(R.string.notification_section_label_older)) {
            @Override
            public boolean fitSectionCriteria(FBNotificationsViewModel model) {
                return NotificationUtils.isOlderThanYesterday(model.getNotificationDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            }
        });
        return sections;
    }

    @Override
    protected RecyclerView.ViewHolder onCreateContentViewHolder(ViewGroup parent, int viewType) {
        ItemNotificationMessagesBinding binding = ItemNotificationMessagesBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    protected RecyclerView.ViewHolder onCreateSectionViewHolder(ViewGroup parent, int viewType) {
        ItemNotificationSectionBinding binding = ItemNotificationSectionBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new MessagesSectionViewHolder(binding);
    }

    @Override
    protected void onBindContentViewHolder(RecyclerView.ViewHolder holder, SectionAdapterItem<FBNotificationsViewModel> model, int position) {
        ((ViewHolder) holder).bindData(model.getContent());
    }

    @Override
    protected void onBindSectionViewHolder(RecyclerView.ViewHolder holder, SectionAdapterItem<FBNotificationsViewModel> model, int position) {
        ((MessagesSectionViewHolder) holder).setup(model.getSection());
    }


    void setOnMessageItemClickListener(OnMessageItemClickListener onMessageItemClickListener) {
        this.mOnMessageItemClickListener = onMessageItemClickListener;
    }


    public void addSectionedData(List<FBNotificationsViewModel> newData) {
        super.addSectionedData(newData);
        //  setCurrentPage(getCurrentPage() + 1);
        if (isAllSelected()) mMultipleSelectionHandler.selectAll(true, itemPositions());
    }

    @SuppressLint("UseSparseArrays")
    void removeItemAt(FBNotificationsViewModel viewModel) {

        int position = findItemPosition(viewModel);
        if (position >= 0) {
            mItemsToBeDeleted.clear();
            mItemsToBeDeleted.add(items.get(position));

            mLastItemSnapShot.clear();
            mLastItemSnapShot.addAll(items);

            items.remove(position);
            notifyItemRemoved(position);

            unsectionedItems.remove(viewModel);
        }
        removeObsoleteSections();
    }

    private int findItemPosition(FBNotificationsViewModel viewModel) {
        for (int i = 0; i < items.size(); i++) {

            FBNotificationsViewModel contentViewModel = items.get(i).getContent();
            if (contentViewModel != null && viewModel != null
                    && contentViewModel.getNotificationId() != null && viewModel.getNotificationId() != null
                    && contentViewModel.getNotificationId().intValue() == viewModel.getNotificationId().intValue()) {
                return i;
            }
        }
        return -1;
    }


    @Override
    public void onSelectionStarted() {
        if (mOnSelectionChangedListener != null) {
            mOnSelectionChangedListener.onSelectionStarted();
        }
        notifyDataSetChanged();
    }


    @Override
    public void onSelectionChanged(List<Integer> selectedItems) {
        mOnSelectionChangedListener.onSelectionChanged(selectedItems);
    }

    void setOnSelectionChangedListener(MultipleSelectionHandler.OnSelectionChangeListener
                                               onSelectionChangedListener) {
        mOnSelectionChangedListener = onSelectionChangedListener;
    }

    void clearSelections() {
        mMultipleSelectionHandler.clearSelections();
        notifyDataSetChanged();
    }

    void deleteSelectedItems() {
        List<Integer> selectedPositions = mMultipleSelectionHandler.getSelectedPositions();

        mItemsToBeDeleted.clear();

        for (Integer pos :
                selectedPositions) {
            mItemsToBeDeleted.add(items.get(pos));
        }
        mLastItemSnapShot.clear();
        mLastItemSnapShot.addAll(items);

        for (SectionAdapterItem<FBNotificationsViewModel> sectionAdapterItem : mItemsToBeDeleted) {

            int deletePos = items.indexOf(sectionAdapterItem);
            items.remove(sectionAdapterItem);
            notifyItemRemoved(deletePos);

            //assuming that sectionAdapterItem.getContent() never be null as only viewModel items can be selected
            unsectionedItems.remove(sectionAdapterItem.getContent());
        }
        clearSelections();
        removeObsoleteSections();
    }

    private void removeObsoleteSections() {


        List<SectionAdapterItem<FBNotificationsViewModel>> obsoleteSections = new ArrayList<>();
        SectionAdapterItem<FBNotificationsViewModel> prevItem = null;
        int size = items.size();
        //filter obsolete sections
        for (int i = 0; i < size; i++) {

            SectionAdapterItem<FBNotificationsViewModel> item = items.get(i);

            //if consecutive items are sections then delete the first one
            if (prevItem != null && prevItem.isSection() && item.isSection()) {

                obsoleteSections.add(prevItem);
            }
            prevItem = item;

            //if last item is section
            if (i == size - 1 && item.isSection()) {
                obsoleteSections.add(item);
            }
        }

        //remove the obsolete sections
        for (SectionAdapterItem<FBNotificationsViewModel> obsoleteSection :
                obsoleteSections) {

            int index = items.indexOf(obsoleteSection);
            items.remove(index);
            notifyItemRemoved(index);
        }

    }

    List<FBNotificationsViewModel> getSelectedItems() {
        List<FBNotificationsViewModel> selectedItems = new ArrayList<>();
        List<Integer> selectedPositions = mMultipleSelectionHandler.getSelectedPositions();

        if (selectedPositions!= null && selectedPositions.size() > 0) {
            for (Integer pos : selectedPositions) {
                selectedItems.add(items.get(pos).getContent());
            }
        }
        return selectedItems;
    }

    /*Assuming addItemAtTop will be called whenever we received a push notification while we are on messages screen */
    void addItemAtTop(FBNotificationsViewModel fbNotificationsViewModel) {

        if (items.size() > 0
                && items.get(0).getSection().getName().equalsIgnoreCase(context.getString(R.string.notification_section_label_today))) {

            items.add(1, new SectionAdapterItem<>(fbNotificationsViewModel));
            mMultipleSelectionHandler.shiftItemsIfSelectable(1);
            notifyItemInserted(1);
        } else {
            SectionAdapterItem<FBNotificationsViewModel> todaySection = new SectionAdapterItem<>(new Section<FBNotificationsViewModel>(context.getString(R.string.notification_section_label_today)) {
                @Override
                public boolean fitSectionCriteria(FBNotificationsViewModel model) {
                    return NotificationUtils.isToday(model.getNotificationDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
                }
            });
            items.add(0, todaySection);
            items.add(1, new SectionAdapterItem<>(fbNotificationsViewModel));
            mMultipleSelectionHandler.shiftItemsIfSelectable(2);
            notifyItemRangeInserted(0, 2);
        }
    }

    boolean isAllSelected() {
        int count = unsectionedItems.size();
        if (count > 0) {
            return count == mMultipleSelectionHandler.getSelectedPositions().size();
        }
        return false;
    }

    void selectAll(boolean selectAll) {
        mMultipleSelectionHandler.selectAll(selectAll, itemPositions());
        notifyDataSetChanged();
    }

    private List<Integer> itemPositions() {

        List<Integer> itemPositions = new ArrayList<>();
        int size = items.size();
        for (int i = 0; i < size; i++) {
            if (items.get(i).isItem()) {
                itemPositions.add(i);
            }
        }
        return itemPositions;
    }

    public void restoreDeletedItems() {
        items.clear();
        items.addAll(mLastItemSnapShot);
        unsectionedItems.clear();
        unsectionedItems.addAll(extractContentItems(mLastItemSnapShot));
        notifyDataSetChanged();
        mLastItemSnapShot.clear();
    }

    public void resetAdapter() {
        items.clear();
        unsectionedItems.clear();
        mLastItemSnapShot.clear();
        notifyDataSetChanged();
    }

    private List<FBNotificationsViewModel> extractContentItems(List<SectionAdapterItem<FBNotificationsViewModel>> sectionAdapterItems) {
        List<FBNotificationsViewModel> fbNotificationsViewModels = new ArrayList<>();
        for (SectionAdapterItem<FBNotificationsViewModel> adapterItem :
                sectionAdapterItems) {

            if (adapterItem.isItem()) {
                fbNotificationsViewModels.add(adapterItem.getContent());
            }
        }
        return fbNotificationsViewModels;
    }

    List<FBNotificationsViewModel> recentDeletedItems() {

        if (mItemsToBeDeleted != null) {
            List<FBNotificationsViewModel> deletedItems = new ArrayList<>();
            for (SectionAdapterItem<FBNotificationsViewModel> sectionAdapterItem :
                    mItemsToBeDeleted) {
                if (sectionAdapterItem != null) {
                    deletedItems.add(sectionAdapterItem.getContent());
                }
            }
            return deletedItems;
        }
        return null;
    }

    public FBNotificationsViewModel getItemAt(int position) {
        return items.get(position).getContent();
    }

    public int getActualItemCount() {
        return unsectionedItems.size();
    }

    public FBNotificationsViewModel getItemByNotificationId(int notificationId) {

        for (int i = 0; i < items.size(); i++) {

            FBNotificationsViewModel contentViewModel = items.get(i).getContent();
            if (contentViewModel != null
                    && contentViewModel.getNotificationId() == notificationId) {
                return contentViewModel;
            }
        }
        return null;
    }

    public void refreshItem(FBNotificationsViewModel fbNotificationViewModel) {
        int index = findItemPosition(fbNotificationViewModel);
        notifyItemChanged(index);
    }

    private boolean isGoingToExpireSoon(String expiryDate) {
        long secondsRemainingFromNow = NotificationUtils.secondsRemainingFromNow(expiryDate, FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
        return secondsRemainingFromNow <= TimeUnit.HOURS.toSeconds(NotificationConstants.SETTINGS.EXPIRY_ALERT_TIME_IN_HOURS);
    }

    public interface OnMessageItemClickListener {
        void onMessageItemClick(FBNotificationsViewModel fbNotificationsViewModel, int pos);
    }

    public static class MessagesSectionViewHolder extends RecyclerView.ViewHolder {

        TextView sectionName;

        public MessagesSectionViewHolder(final ItemNotificationSectionBinding binding) {
            super(binding.getRoot());
            sectionName = binding.tvSectionName;
        }

        public void setup(final Section section) {
            sectionName.setText(section.getName());
        }
    }

    class ViewHolder extends RecyclerView.ViewHolder implements MultipleSelectionHandler.SelectableViewHolder {

        private ItemNotificationMessagesBinding binding;

        public ViewHolder(ItemNotificationMessagesBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }

        void bindData(FBNotificationsViewModel fbNotificationsViewModel) {
            binding.tvNotificationTitle.setText(Html.fromHtml(fbNotificationsViewModel.getHeading()));
            binding.tvHeading.setText(Html.fromHtml(fbNotificationsViewModel.getSubHeading()));
            if (!fbNotificationsViewModel.isRead()) {
                binding.tvNotificationTitle.setTypeface(Typeface.DEFAULT_BOLD);
            } else {
                binding.tvNotificationTitle.setTypeface(Typeface.DEFAULT);
            }
            if (!StringUtils.isNullOrEmpty(fbNotificationsViewModel.getActiveExpiryDate())) {
                binding.tvExpireTime.setText(context.getString(R.string.expires) + StringUtils.SPACE + FormattingUtil.getFormattedDateSAtoLocal(fbNotificationsViewModel.getActiveExpiryDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS, FormattingUtil.DATE_FORMAT_DD_MMM_YYYY_SPACE_SEPARATOR));
                boolean isGoingToExpireSoon = isGoingToExpireSoon(fbNotificationsViewModel.getActiveExpiryDate());
                int textColor = isGoingToExpireSoon ? R.color.notification_expire_red : R.color.color_bbbbbb;
                binding.tvExpireTime.setTextColor(ContextCompat.getColor(context, textColor));
                if (isGoingToExpireSoon) {
                    int unreadDrawableResource = R.drawable.unread_expire_soon_notification_indicator;
                    binding.ivUnreadIndicator.setImageResource(unreadDrawableResource);
                    binding.ivUnreadIndicator.setVisibility(View.VISIBLE);
                } else if (!fbNotificationsViewModel.isRead()) {
                    int unreadDrawableResource = R.drawable.unread_notification_indicator;
                    binding.ivUnreadIndicator.setImageResource(unreadDrawableResource);
                    binding.ivUnreadIndicator.setVisibility(View.VISIBLE);
                } else {
                    binding.ivUnreadIndicator.setVisibility(View.GONE);
                }
                binding.tvExpireTime.setVisibility(View.VISIBLE);
            } else {
                binding.tvExpireTime.setVisibility(View.INVISIBLE);
            }

            int visibility = mMultipleSelectionHandler.isSelectable() ? View.VISIBLE : View.GONE;
            binding.ivCheckbox.setVisibility(visibility);
            List<FBNotificationsViewModel.RichContent> richContents = fbNotificationsViewModel.getRichContent();
            if (richContents != null && !richContents.isEmpty()) {
               handleRichContentIcon(richContents);
            }else{
                binding.ivRichContentIndicator.setVisibility(View.GONE);
            }

            mMultipleSelectionHandler.bind(this);
            binding.messageContainer.setOnClickListener(v -> onClick());
            binding.messageContainer.setOnLongClickListener(this::onLongClick);
            binding.messageContainer.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    ViewHolder.this.onTouch(event);
                    return false;
                }
            });
        }

        private void handleRichContentIcon(List<FBNotificationsViewModel.RichContent> richContents) {
            if (isRichContentPresent(richContents, NotificationConstants.CONTENT_TYPE.VIDEO)) {
                binding.ivRichContentIndicator.setImageResource(R.drawable.ic_indicator_video);
                binding.ivRichContentIndicator.setVisibility(View.VISIBLE);
            } else if (isRichContentPresent(richContents, NotificationConstants.CONTENT_TYPE.DOCUMENT)) {
                binding.ivRichContentIndicator.setImageResource(R.drawable.ic_indicator_attachment);
                binding.ivRichContentIndicator.setVisibility(View.VISIBLE);
            } else {
                binding.ivRichContentIndicator.setVisibility(View.GONE);
            }
        }

        private boolean isRichContentPresent(List<FBNotificationsViewModel.RichContent> richContents, String contentType) {
            for (FBNotificationsViewModel.RichContent richContent : richContents) {
                if (richContent.getContentType().equalsIgnoreCase(contentType)) {
                    return true;
                }
            }
            return false;
        }

        public void onClick() {
            if (!mMultipleSelectionHandler.tapSelection(this)) {
                if (mOnMessageItemClickListener != null) {
                    int clickedPosition = getLayoutPosition();
                    FBNotificationsViewModel fbNotificationsViewModel = items.get(clickedPosition).getContent();
                    mOnMessageItemClickListener.onMessageItemClick(fbNotificationsViewModel, clickedPosition);
                }
            }
        }

        public boolean onLongClick(View v) {
            mHasLongPressed = true;
            if (!mMultipleSelectionHandler.isSelectable()) {
                //vibrate before selection started
                v.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS);
            }
            return true;
        }

        public boolean onTouch(MotionEvent event) {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                if (mHasLongPressed) {
                    mMultipleSelectionHandler.handleSelection(this);
                    mHasLongPressed = false;
                    return true;
                }
            }
            return false;
        }

        @Override
        public void setSelected(boolean isSelected) {
            int imageResource = isSelected ? R.drawable.checkbox_selected : R.drawable.checkbox;
            binding.ivCheckbox.setImageResource(imageResource);

        }
    }
}

