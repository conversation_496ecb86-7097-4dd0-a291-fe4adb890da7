package za.co.nedbank.ui.view.home.close_account_tab;


import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.TaxCertificateClosedAccountBinding;
import za.co.nedbank.ui.view.model.ClosedAccount;
import za.co.nedbank.uisdk.component.CompatTextView;

public class ClosedAccountAdapter extends RecyclerView.Adapter<ClosedAccountAdapter.DataViewHolder> {
    private final ClosedAccountAdapter.IViewHolderInteraction mIViewHolderInteraction;
    private List<ClosedAccount> accountSummaries;

    ClosedAccountAdapter(ClosedAccountAdapter.IViewHolderInteraction mIViewHolderInteraction, List<ClosedAccount> accountSummaries) {
        this.mIViewHolderInteraction = mIViewHolderInteraction;
        this.accountSummaries = accountSummaries;
    }

    @NonNull
    @Override
    public ClosedAccountAdapter.DataViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        TaxCertificateClosedAccountBinding binding = TaxCertificateClosedAccountBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ClosedAccountAdapter.DataViewHolder(binding, mIViewHolderInteraction);
    }


    @Override
    public void onBindViewHolder(@NonNull ClosedAccountAdapter.DataViewHolder holder, int position) {
        if (StringUtils.isNotEmpty(accountSummaries.get(position).getAccountName())){
            ViewUtils.showViews(holder.invest_type);
            holder.invest_type.setText(accountSummaries.get(position).getAccountName());
        }
        if (StringUtils.isNotEmpty(accountSummaries.get(position).getAccountNumber())){
            ViewUtils.showViews(holder.account);
            holder.account.setText(accountSummaries.get(position).getAccountNumber());
        }
        holder.closedAccountContainer.setOnClickListener(v -> holder.handleParentViewClick());
    }

    @Override
    public int getItemCount() {
        return accountSummaries.size();
    }

    interface IViewHolderInteraction {
        void onAccountSelected(ClosedAccount accountSummary);
    }

    public class DataViewHolder extends RecyclerView.ViewHolder {
        ClosedAccountAdapter.IViewHolderInteraction iViewHolderInteraction;

        CompatTextView invest_type;
        CompatTextView account;
        LinearLayout closedAccountContainer;

        public DataViewHolder(@NonNull TaxCertificateClosedAccountBinding binding, IViewHolderInteraction iViewHolderInteraction) {
            super(binding.getRoot());
            this.iViewHolderInteraction = iViewHolderInteraction;
            this.invest_type = binding.investType;
            this.account = binding.account;
            this.closedAccountContainer = binding.closedAccountContainer;
        }

        void handleParentViewClick() {
            int currentPosition = getAdapterPosition();
            if (iViewHolderInteraction != null) {
                iViewHolderInteraction.onAccountSelected(accountSummaries.get(currentPosition));
            }
        }
    }
}
