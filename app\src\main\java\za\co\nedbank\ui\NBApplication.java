/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui;

import static za.co.nedbank.core.Constants.HMS_API_KEY;
import static za.co.nedbank.core.di.modules.ApplicationModule.NETWORK_FLOW;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.lifecycle.ProcessLifecycleOwner;
import androidx.multidex.MultiDexApplication;
import androidx.startup.AppInitializer;

import com.adobe.marketing.mobile.AdobeCallback;
import com.adobe.marketing.mobile.Analytics;
import com.adobe.marketing.mobile.Assurance;
import com.adobe.marketing.mobile.Edge;
import com.adobe.marketing.mobile.Extension;
import com.adobe.marketing.mobile.Identity;
import com.adobe.marketing.mobile.Messaging;
import com.adobe.marketing.mobile.MobileCore;
import com.adobe.marketing.mobile.Signal;
import com.adobe.marketing.mobile.Target;
import com.adobe.marketing.mobile.UserProfile;
import com.adobe.marketing.mobile.edge.consent.Consent;
import com.appsflyer.AppsFlyerLib;
import com.appsflyer.adobeextension.AppsFlyerAdobeExtension;
import com.entersekt.sdk.Auth;
import com.entersekt.sdk.NameValue;
import com.google.android.gms.maps.MapsInitializer;
import com.google.firebase.FirebaseApp;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.huawei.hms.mlsdk.common.MLApplication;

import net.danlew.android.joda.JodaTimeInitializer;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;
import javax.inject.Named;

import io.branch.referral.Branch;
import io.reactivex.Completable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import nedbank.ncp.android.guard.state.PhoneState;
import za.co.nedbank.R;
import za.co.nedbank.booking.common.navigator.BookingNavigator;
import za.co.nedbank.booking.view.deeplink.BookingDeeplinkRegistry;
import za.co.nedbank.core.AppStateEvents;
import za.co.nedbank.core.AuthManager;
import za.co.nedbank.core.BuildConfig;
import za.co.nedbank.core.CoreNavigator;
import za.co.nedbank.core.app_states.ActivityLifecycleCallbacksAdapter;
import za.co.nedbank.core.app_states.AppState;
import za.co.nedbank.core.concierge.chat.GlobalEventBus;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.networking.client.ResultErrorCodes;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.deeplink.CoreDeeplinkRegistry;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.ConnectNidSDKUsecase;
import za.co.nedbank.core.domain.usecase.DisconnectNidSDKUsecase;
import za.co.nedbank.core.domain.usecase.InitializeNidSDKUsecase;
import za.co.nedbank.core.domain.usecase.ita.GetCardPinStatusUsecase;
import za.co.nedbank.core.domain.usecase.ita.GetITAAuthUseCase;
import za.co.nedbank.core.domain.usecase.notifications.GetHMSTokenUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.networking.INetworkFlow;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.pinpoint.CoroutineCallback;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.documentupload.DocumentUploadNavigator;
import za.co.nedbank.documentupload.view.deeplink.DocumentUploadDeeplinkRegistry;
import za.co.nedbank.enroll_v2.EnrollV2Navigator;
import za.co.nedbank.enroll_v2.view.deeplink.EnrollDeeplinkRegistry;
import za.co.nedbank.kidsbanking.view.common.navigator.KidsBankingNavigator;
import za.co.nedbank.kidsbanking.view.deeplink.KidsBankingDeeplinkRegistry;
import za.co.nedbank.loans.common.navigator.LoansNavigator;
import za.co.nedbank.loans.homeloan.navigator.HomeLoanNavigator;
import za.co.nedbank.loans.homeloan.view.deeplink.LoansDeeplinkRegistry;
import za.co.nedbank.loans.preapprovedaccountsoffers.navigator.PreApprovedOffersNavigator;
import za.co.nedbank.payment.atm.navigator.AtmNavigator;
import za.co.nedbank.payment.buy.navigator.BuyNavigator;
import za.co.nedbank.payment.crossborder.navigator.CrossBorderNavigator;
import za.co.nedbank.payment.deeplink.PaymentDeeplinkRegistry;
import za.co.nedbank.payment.investmentswitching.navigator.InvestmentSwitchingNavigator;
import za.co.nedbank.payment.lottopowerball.navigator.LottoPWBNavigator;
import za.co.nedbank.payment.manageinvestment.navigator.ManageInvestmentNavigator;
import za.co.nedbank.payment.mfcsettlementrequest.navigator.MFCNavigator;
import za.co.nedbank.payment.ngi.navigator.NgiNavigator;
import za.co.nedbank.payment.noticeofwithdrawal.navigator.NowNavigator;
import za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountNavigator;
import za.co.nedbank.payment.pay.navigator.PayNavigator;
import za.co.nedbank.payment.payout.navigator.PayoutNavigator;
import za.co.nedbank.payment.recurringpayment.navigator.RecurringNavigator;
import za.co.nedbank.payment.reinvest.navigator.ReinvestNavigator;
import za.co.nedbank.payment.rtp.navigator.RtpNavigator;
import za.co.nedbank.payment.scan.pay.view.navigator.ScanPayNavigator;
import za.co.nedbank.payment.transfer.navigator.TransferNavigator;
import za.co.nedbank.payment.travelcard.navigator.TopUpNavigator;
import za.co.nedbank.payment.vas.common.navigator.VasNavigator;
import za.co.nedbank.payment.vas.dailylotto.navigator.DailyLottoNavigator;
import za.co.nedbank.payment.vas.electricity.navigator.ElectricityNavigator;
import za.co.nedbank.payment.vas.prepaid.navigator.PrepaidNavigator;
import za.co.nedbank.payment.vas.vouchers.navigator.VouchersNavigator;
import za.co.nedbank.profile.view.deeplink.ProfileDeeplinkRegistry;
import za.co.nedbank.profile.view.navigation.ProfileNavigator;
import za.co.nedbank.services.view.deeplink.ServiceDeeplinkRegistry;
import za.co.nedbank.services.view.navigation.ServicesNavigator;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.martec.AjoInAppMessagingDelegateManager;
import za.co.nedbank.ui.view.deeplink.AppDeeplinkRegistry;
import za.co.nedbank.ui.view.ita.ITAFlowActivity;

public class NBApplication extends MultiDexApplication implements LifecycleObserver {

    private int activityCounter = 0;

    @Inject
    GetITAAuthUseCase mGetITAAuthUseCase;

    @Inject
    @Named("memory")
    ApplicationStorage memoryApplicationStorage;

    @Inject
    ApplicationStorage applicationStorage;

    @Inject
    CoroutineCallback coroutineCallback;

    @Inject
    FeatureSetController mFeatureSetController;

    @Inject
    GetCardPinStatusUsecase mGetCardPinStatusUsecase;

    @Inject
    InitializeNidSDKUsecase initializeNidSDKUsecase;

    @Inject
    ConnectNidSDKUsecase connectNidSDKUsecase;

    @Inject
    DisconnectNidSDKUsecase disconnectNidSDKUsecase;

    @Inject
    GetHMSTokenUseCase mGetHMSTokenUseCase;

    @Inject
    ServiceDeeplinkRegistry serviceDeeplinkRegistry;

    @Inject
    ProfileDeeplinkRegistry profileDeeplinkRegistry;

    @Inject
    EnrollDeeplinkRegistry enrollDeeplinkRegistry;

    @Inject
    AppDeeplinkRegistry appDeeplinkRegistry;
    @Inject
    BookingDeeplinkRegistry bookingDeeplinkRegistry;
    @Inject
    CoreDeeplinkRegistry coreDeeplinkRegistry;
    @Inject
    DocumentUploadDeeplinkRegistry documentUploadDeeplinkRegistry;
    @Inject
    KidsBankingDeeplinkRegistry kidsbankingDeeplinkRegistry;
    @Inject
    LoansDeeplinkRegistry loansDeeplinkRegistry;

    @Inject
    PaymentDeeplinkRegistry paymentDeeplinkRegistry;

    private boolean isAppInBackground = false;
    private boolean isAppStart = true;
    private AjoInAppMessagingDelegateManager ajoInAppMessagingDelegate;

    @Override
    public void onCreate() {
        super.onCreate();
        EventBus.getDefault().register(this);
        AppDI.getApplicationComponent(this).inject(this);
        ProcessLifecycleOwner.get().getLifecycle().addObserver(this);

        setupBranchIO();
        setupRemoteConfig();
        setupNavigation();
        setupMapOnIO();
        AppInitializer.getInstance(this).initializeComponent(JodaTimeInitializer.class);
        listenForForeground();
        if (!DeviceUtils.isBuildType(DeviceUtils.BUILD_TYPE_MOCK)) {
            registerAuthListener();
        }

        setupAdobe();
        setupDeeplinks();
        if (za.co.nedbank.BuildConfig.ENABLE_APPSFLYER) {
            setUpAppsFlyerSdk();
        }
        if(MLApplication.getInstance() != null) {
            MLApplication.getInstance().setApiKey(getApiKey());
        }
    }

    private String getApiKey() {
        try {
            return URLEncoder.encode(HMS_API_KEY, "utf-8");
        } catch (UnsupportedEncodingException e) {
            Log.e("TAG", "encode apikey error");
            return "";
        }
    }

    public AjoInAppMessagingDelegateManager getAjoInAppMessagingDelegate() {
        if (ajoInAppMessagingDelegate == null)
            ajoInAppMessagingDelegate = new AjoInAppMessagingDelegateManager(applicationStorage);
        return ajoInAppMessagingDelegate;
    }

    /*
     * Adobe Martech SDK Initialization
     * */
    private void setupAdobe() {
        MobileCore.setApplication(this);
        // Uncomment below lines only for debug to check logs for Adobe Analytics
        /*if (BuildConfig.DEBUG) {
            MobileCore.setLogLevel(LoggingMode.DEBUG);
        }*/
        try {
            List<Class<? extends Extension>> extensions = Arrays.asList(
                    Analytics.EXTENSION,
                    Consent.EXTENSION,
                    Assurance.EXTENSION,
                    com.adobe.marketing.mobile.edge.identity.Identity.EXTENSION,
                    com.adobe.marketing.mobile.Identity.EXTENSION,
                    Target.EXTENSION,
                    Edge.EXTENSION,
                    UserProfile.EXTENSION,
                    com.adobe.marketing.mobile.Lifecycle.EXTENSION,
                    Signal.EXTENSION,
                    Messaging.EXTENSION
            );
            MobileCore.registerExtensions(extensions, (AdobeCallback) o -> MobileCore.configureWithAppID(za.co.nedbank.BuildConfig.ADOBE_CONFIGURE_ID));
            Identity.getExperienceCloudId(cloudId -> {
                NBLogger.e("ECID: ", cloudId);
                applicationStorage.putString(StorageKeys.EXPERIENCE_CLOUD_ID, cloudId);
            });
        } catch (Exception e) {
            NBLogger.e("Adobe Registration Exception", e.getMessage());
        }
    }

    private void setUpAppsFlyerSdk() {
        AppsFlyerLib.getInstance().init(BuildConfig.APPSFLYER_KEY, null, this);
        AppsFlyerLib.getInstance().setOutOfStore(BuildConfig.APP_STORE_NAME);
        AppsFlyerLib.getInstance().setAppInviteOneLink("5NdS");
        AppsFlyerAdobeExtension.registerExtension();
        AppsFlyerLib.getInstance().start(this);
    }

    private void registerAuthListener() {
        mGetITAAuthUseCase
                .execute()
                .subscribe(auth -> authReceived(auth), throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    private void authReceived(Auth auth) {
        mGetCardPinStatusUsecase.execute()
                .subscribe(cardpinstatus -> {
                    if (Boolean.FALSE.equals(cardpinstatus)) {
                        boolean scanToPayInProgress = memoryApplicationStorage.getBoolean(NotificationConstants.STORAGE_KEYS.SCAN_TO_PAY_IN_PROGRESS, false);
                        // check transactionId with request Id
                        if (!scanToPayInProgress && !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ITA)
                                && !isRPPPayment(auth) && !isCallbackAuth(auth)) {
                            handleITAAuth(auth);
                        }
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));

    }

    private boolean isRPPPayment(Auth auth){
        List<NameValue> authLst = auth.getNameValues();
        boolean isRPPPayment = false;
        if (authLst != null && !authLst.isEmpty()) {
            for (NameValue nameValue : authLst) {
                if (nameValue.getName() != null && nameValue.getName().equalsIgnoreCase(ResultErrorCodes.AUTH_NAME_VALUE_KEY_TRANSACTION_ID)) {
                    isRPPPayment = true;
                    break;
                }
            }
        }
        return isRPPPayment;
    }

    private boolean isCallbackAuth(Auth auth) {
        List<NameValue> authLst = auth.getNameValues();
        boolean isSensitiveTransaction = false;
        if (authLst != null && !authLst.isEmpty()) {
            for (NameValue nameValue : authLst) {
                if (nameValue.getName() != null && nameValue.getName().equalsIgnoreCase(ResultErrorCodes.HIDDEN_CALLBACK_FUNCTION)
                        && nameValue.getValue().equalsIgnoreCase(ResultErrorCodes.HIDDEN_SENSITIVE_TRANSACTION)) {
                    isSensitiveTransaction = true;
                    break;
                }
            }
        }
        return isSensitiveTransaction;
    }

    private void handleITAAuth(Auth auth) {
        AuthManager.getInstance().setAuth(auth);
        Tools tool = new Tools();
        if (isAppInBackground) {
            tool.showNotification(this, auth.getTitle(), auth.getText());
        } else {
            Completable.timer(1, TimeUnit.SECONDS, AndroidSchedulers.mainThread())
                    .subscribe(() -> showITAFlow(auth));
        }
    }

    private void showITAFlow(Auth auth) {
        Tools tool = new Tools();
        if (isAppInBackground) {
            tool.showNotification(this, auth.getTitle(), auth.getText());
        } else if (!memoryApplicationStorage.getBoolean(auth.getId(), false)) {
            Intent targetIntent = new Intent(this, ITAFlowActivity.class);
            targetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(targetIntent);
        }
    }

    @SuppressWarnings("deprecation")
    @Keep
    @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
    void onAppBackgrounded() {
        isAppInBackground = true;
        disconnectEnterSktSDK();
    }

    @SuppressWarnings("deprecation")
    @Keep
    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    void onAppForegrounded() {
        isAppInBackground = false;
        if (!isAppStart) {
            connectEnterSktSDK();
        }
        isAppStart = false;
    }

    void disconnectEnterSktSDK() {
        if (NETWORK_FLOW != INetworkFlow.HARD_CODED_TOKEN) {
            disconnectNidSDKUsecase.execute().subscribe();
        }
    }

    void connectEnterSktSDK() {
        if (NETWORK_FLOW != INetworkFlow.HARD_CODED_TOKEN) {
            connectNidSDKUsecase.execute().subscribe();
        }
    }

    private void setupDeeplinks() {
        appDeeplinkRegistry.registerDeeplink();
        bookingDeeplinkRegistry.registerDeeplink();
        coreDeeplinkRegistry.registerDeeplink();
        documentUploadDeeplinkRegistry.registerDeeplink();
        enrollDeeplinkRegistry.registerDeeplink();
        kidsbankingDeeplinkRegistry.registerDeeplink();
        serviceDeeplinkRegistry.registerDeeplink();
        loansDeeplinkRegistry.registerDeeplink();
        paymentDeeplinkRegistry.registerDeeplink();
        profileDeeplinkRegistry.registerDeeplink();
    }

    private void setupNavigation() {
        NavigationRouter.registerNavigator(new CoreNavigator());
        NavigationRouter.registerNavigator(new ServicesNavigator());
        NavigationRouter.registerNavigator(new ProfileNavigator());
        NavigationRouter.registerNavigator(new AppNavigator());
        NavigationRouter.registerNavigator(new TransferNavigator());
        NavigationRouter.registerNavigator(new PayNavigator());
        NavigationRouter.registerNavigator(new BuyNavigator());
        NavigationRouter.registerNavigator(new LottoPWBNavigator());
        NavigationRouter.registerNavigator(new CrossBorderNavigator());
        NavigationRouter.registerNavigator(new PreApprovedOffersNavigator());
        NavigationRouter.registerNavigator(new ScanPayNavigator());
        NavigationRouter.registerNavigator(new MFCNavigator());
        NavigationRouter.registerNavigator(new NowNavigator());
        NavigationRouter.registerNavigator(new OpenNewInvAccountNavigator());
        NavigationRouter.registerNavigator(new EnrollV2Navigator());
        NavigationRouter.registerNavigator(new TopUpNavigator());
        NavigationRouter.registerNavigator(new HomeLoanNavigator());
        NavigationRouter.registerNavigator(new PayoutNavigator());
        NavigationRouter.registerNavigator(new ReinvestNavigator());
        NavigationRouter.registerNavigator(new VasNavigator());
        NavigationRouter.registerNavigator(new PrepaidNavigator());
        NavigationRouter.registerNavigator(new VouchersNavigator());
        NavigationRouter.registerNavigator(new ElectricityNavigator());
        NavigationRouter.registerNavigator(new DailyLottoNavigator());
        NavigationRouter.registerNavigator(new RecurringNavigator());
        NavigationRouter.registerNavigator(new ManageInvestmentNavigator());
        NavigationRouter.registerNavigator(new NgiNavigator());
        NavigationRouter.registerNavigator(new LoansNavigator());
        NavigationRouter.registerNavigator(new AtmNavigator());
        NavigationRouter.registerNavigator(new BookingNavigator());
        NavigationRouter.registerNavigator(new InvestmentSwitchingNavigator());
        NavigationRouter.registerNavigator(new KidsBankingNavigator());
        NavigationRouter.registerNavigator(new DocumentUploadNavigator());
        NavigationRouter.registerNavigator(new RtpNavigator());
    }

    private void setupBranchIO() {
        if (DeviceUtils.isStoreAndProdBuild()) {
            Branch.disableTestMode();
        } else {
            Branch.enableTestMode();
        }
        Branch.getAutoInstance(this);
    }

    private void setupRemoteConfig() {
        FirebaseApp.initializeApp(this);
        FirebaseRemoteConfig mFirebaseRemoteConfig = FirebaseRemoteConfig.getInstance();
        mFirebaseRemoteConfig.setDefaultsAsync(R.xml.remote_config_defaults);
        mFirebaseRemoteConfig.fetch((long) R.string.cache_expire)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        mFirebaseRemoteConfig.activate();
                    }
                });
    }

    private void listenForForeground() {
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacksAdapter() {
            @Override
            public void onActivityStarted(@NonNull Activity activity) {
                if (activityCounter == 0) {
                    GlobalEventBus.getBus().post(new AppStateEvents(AppState.Foreground));
                }
                activityCounter++;
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                activityCounter--;
                if (activityCounter == 0) {
                    GlobalEventBus.getBus().post(new AppStateEvents(AppState.Background));
                }
            }
        });
    }

    @SuppressLint("CheckResult")
    private void setupMapOnIO() {
        Completable.fromAction(() ->
                        MapsInitializer.initialize(getApplicationContext(), MapsInitializer.Renderer.LATEST, renderer -> {
                        }))
                .subscribeOn(Schedulers.newThread())
                .subscribe(() -> {
                    NBLogger.i("MAP","Map init success");
                }, e -> {
                });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void shouldShowAjoInApp(AjoInAppEvent event) {
        if (ajoInAppMessagingDelegate != null)
            ajoInAppMessagingDelegate.shouldShowInAppMessage(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void shouldGetCallState(PhoneState state) {
        if (state == PhoneState.IN_CALL.INSTANCE) {
            APIInformation.getInstance().setUserInCall(true);
            APIInformation.getInstance().setHasUserCutTheCall(false);
        } else if (state == PhoneState.IDLE.INSTANCE) {
            if (APIInformation.getInstance().isUserInCall()) {
                APIInformation.getInstance().setUserInCall(false);
                APIInformation.getInstance().setHasUserCutTheCall(true);
            }
        }
    }
}
