package za.co.nedbank.ui.domain.model.pop;

import java.util.List;

public class ShareProofOfPaymentRequestData {
    private List<SharePopNotificationTypeData> sharePopNotificationTypeData;
    private String transactionKind;
    private String transactionDate;
    private boolean isFromRecentPayments;
    private boolean isSopFromPayDone;
    private boolean isSchedulePayment;

    public List<SharePopNotificationTypeData> getSharePopNotificationTypeData() {
        return sharePopNotificationTypeData;
    }

    public void setSharePopNotificationTypeData(List<SharePopNotificationTypeData> sharePopNotificationTypeData) {
        this.sharePopNotificationTypeData = sharePopNotificationTypeData;
    }

    public String getTransactionKind() {
        return transactionKind;
    }

    public void setTransactionKind(String transactionKind) {
        this.transactionKind = transactionKind;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public boolean isFromRecentPayments() {
        return isFromRecentPayments;
    }

    public void setFromRecentPayments(boolean fromRecentPayments) {
        isFromRecentPayments = fromRecentPayments;
    }

    public boolean isSopFromPayDone() {
        return isSopFromPayDone;
    }

    public void setSopFromPayDone(boolean sopFromPayDone) {
        isSopFromPayDone = sopFromPayDone;
    }

    public boolean isSchedulePayment() {
        return isSchedulePayment;
    }

    public void setSchedulePayment(boolean schedulePayment) {
        isSchedulePayment = schedulePayment;
    }
}
