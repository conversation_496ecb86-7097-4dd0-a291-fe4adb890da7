package za.co.nedbank.ui.view.notification.notification_preferences.all_accounts_preferences;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.ArrayList;
import java.util.IllegalFormatConversionException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.dialog.IDialog;
import za.co.nedbank.core.base.dialog.UserConsentDialog;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.sharedui.listener.IListItemClickListener;
import za.co.nedbank.core.sharedui.ui.SimpleDividerItemDecoration;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.AccountPreference;
import za.co.nedbank.databinding.ActivityPreferenceAllAccountsBinding;
import za.co.nedbank.ui.di.AppDI;

public class AllAccountsPreferenceActivity extends NBBaseActivity implements AllAccountsPreferenceView, IListItemClickListener<Integer> {

    private static final String TAG = "Crash";
    @Inject
    AllAccountsPreferencePresenter mAllAccountsPreferencePresenter;

    private List<AccountPreference> mAllAccountsList = new ArrayList<>();

    private AllAccountsPreferenceRecyclerViewAdapter mAllAccountsPreferenceRecyclerViewAdapter;

    private UserConsentDialog mUserConsentDialog;
    private ActivityPreferenceAllAccountsBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityPreferenceAllAccountsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        mAllAccountsPreferencePresenter.bind(this);
        receiveBundle();
        initToolbar(binding.toolbar, true, this.getString(R.string.account_notifications_title));
        setupUI();
    }

    private void setupUI() {
        binding.switchApplyToAll.setChecked(false);
        binding.viewAllAccountsPrefRecyclerview.setItemAnimator(null);
        binding.viewAllAccountsPrefRecyclerview.setHasFixedSize(true);
        binding.viewAllAccountsPrefRecyclerview.setNestedScrollingEnabled(false);
        binding.viewAllAccountsPrefRecyclerview.addItemDecoration(new SimpleDividerItemDecoration(this));
        binding.viewAllAccountsPrefRecyclerview.setLayoutManager(new LinearLayoutManager(this));
        mAllAccountsPreferenceRecyclerViewAdapter = new AllAccountsPreferenceRecyclerViewAdapter(mAllAccountsList, this);
        binding.viewAllAccountsPrefRecyclerview.setAdapter(mAllAccountsPreferenceRecyclerViewAdapter);

        if (mAllAccountsList != null && !mAllAccountsList.isEmpty()) {
            if (isSameSettings()) {
                binding.switchApplyToAll.setChecked(true);
                AccountPreference accountPreference = mAllAccountsList.get(0);
                if (accountPreference.isAllowPushForTrans()) {
                    binding.switchPushNotifications.setChecked(true);
                    binding.pushPreferenceContainer.setVisibility(View.VISIBLE);
                    binding.textHowMuchSpend.setText(FormattingUtil.convertToFormattedCurrency(accountPreference.getSpendLimitApp()));
                    binding.textLowAccountBalance.setText(FormattingUtil.convertToFormattedCurrency(accountPreference.getBalanceLimitApp()));
                }
                if (accountPreference.isAllowSMSForTrans()) {
                    binding.switchSmsNotifications.setChecked(true);
                    binding.smsPreferenceContainer.setVisibility(View.VISIBLE);
                    binding.textHowMuchSpendSms.setText(FormattingUtil.convertToFormattedCurrency(accountPreference.getSpendLimitSMS()));
                    binding.textLowAccountBalanceSms.setText(FormattingUtil.convertToFormattedCurrency(accountPreference.getBalanceLimitSMS()));
                }

                enableSaveButton(false);

            }
        }
        binding.saveChangesButton.setOnClickListener(v -> onClickSaveChanges());
        binding.switchPushNotifications.setOnCheckedChangeListener((buttonView, isChecked) -> onPushCheckedChanged(isChecked));
        binding.switchSmsNotifications.setOnCheckedChangeListener((buttonView, isChecked) -> onSmsCheckedChanged(isChecked));
        binding.switchApplyToAll.setOnCheckedChangeListener((buttonView, isChecked) -> onAllAccountsCheckedChanged(isChecked));

        binding.textHowMuchSpend.setOnFocusChangeListener((v, hasFocus) -> onPushSpendTextFocusChange(hasFocus));
        binding.textLowAccountBalance.setOnFocusChangeListener((v, hasFocus) -> onPushBalanceTextFocusChange(hasFocus));
        binding.textHowMuchSpendSms.setOnFocusChangeListener((v, hasFocus) -> onSMSSpendTextFocusChange(hasFocus));
        binding.textLowAccountBalanceSms.setOnFocusChangeListener((v, hasFocus) -> onSMSBalanceTextFocusChange(hasFocus));
    }

    @Override
    protected void onDestroy() {
        mAllAccountsPreferencePresenter.unbind();
        super.onDestroy();
    }


    private void receiveBundle() {
        if (getIntent() != null) {
            mAllAccountsList = getIntent().getParcelableArrayListExtra(NotificationConstants.EXTRA.ACCOUNT_PREFERENCE_LIST);
        }
    }


    public void onPushSpendTextFocusChange(boolean hasFocus) {
        if (hasFocus) {
            enableSaveButton(true);
            try {
                announceForAccessibility(String.format(NotificationConstants.FORMAT_SSS, getString(R.string.your_current_spend_limit), binding.textHowMuchSpend.getValue(), getString(R.string.will_notify_of_spend_overuse)));
            } catch (IllegalFormatConversionException e) {
                NBLogger.d(TAG, e.getMessage());
            }
        }

    }

    public void onPushBalanceTextFocusChange(boolean hasFocus) {
        if (hasFocus) {
            enableSaveButton(true);
            try {
                announceForAccessibility(String.format(NotificationConstants.FORMAT_SSS, getString(R.string.current_balance_limit), binding.textLowAccountBalance.getValue(), getString(R.string.will_notify_of_below_balance)));
            } catch (IllegalFormatConversionException e) {
                NBLogger.d(TAG, e.getMessage());
            }
        }
    }

    public void onSMSSpendTextFocusChange(boolean hasFocus) {
        if (hasFocus) {
            enableSaveButton(true);
            announceForAccessibility(String.format(NotificationConstants.FORMAT_SSS,getString(R.string.your_current_spend_limit) , binding.textHowMuchSpendSms.getValue() , getString(R.string.will_notify_of_spend_overuse)));
        }

    }

    public void onSMSBalanceTextFocusChange(boolean hasFocus) {
        if (hasFocus) {
            enableSaveButton(true);
            announceForAccessibility(String.format(NotificationConstants.FORMAT_SSS,getString(R.string.current_balance_limit) , binding.textLowAccountBalanceSms.getValue() ,getString(R.string.will_notify_of_below_balance)));
        }

    }

    public void onClickSaveChanges() {
        ShowConsentDialog();
    }


    public void ShowConsentDialog() {
        mUserConsentDialog = UserConsentDialog.getInstance(getString(R.string.get_notifications_on_all_aacounts),
                getString(R.string.if_you_continue_all_your_account_limits_will_be_replaced), getString(R.string.continue_uppercase), getString(R.string.cancel));
        mUserConsentDialog.setCancelable(false);
        mUserConsentDialog.setiDialog(new IDialog() {
            @Override
            public void onPositiveButtonClick() {
                mUserConsentDialog.dismiss();

                boolean useAppBalanceLimitPrevValue = false;
                boolean useAppSpendLimitPrevValue = false;
                boolean useSMSSpendLimitPrevValue = false;
                boolean useSMSBalanceLimitPrevValue = false;

                AccountPreference accountPreference = new AccountPreference();
                accountPreference.setAllowPushForTrans(binding.switchPushNotifications.isChecked());
                if (binding.switchPushNotifications.isChecked()) {
                    if (!TextUtils.isEmpty(binding.textLowAccountBalance.getValue())) {
                        accountPreference.setBalanceLimitApp(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.textLowAccountBalance.getValue())));
                    } else {
                        useAppBalanceLimitPrevValue = true;
                    }
                    if (!TextUtils.isEmpty(binding.textHowMuchSpend.getValue())) {
                        accountPreference.setSpendLimitApp(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.textHowMuchSpend.getValue())));
                    } else {
                        useAppSpendLimitPrevValue = true;
                    }
                }

                accountPreference.setAllowSMSForTrans(binding.switchSmsNotifications.isChecked());
                if (binding.switchSmsNotifications.isChecked()) {
                    if (!TextUtils.isEmpty(binding.textHowMuchSpendSms.getValue())) {
                        accountPreference.setSpendLimitSMS(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.textHowMuchSpendSms.getValue())));
                    } else {
                        useSMSSpendLimitPrevValue = true;
                    }

                    if (!TextUtils.isEmpty(binding.textLowAccountBalanceSms.getValue())) {
                        accountPreference.setBalanceLimitSMS(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.textLowAccountBalanceSms.getValue())));
                    } else {
                        useSMSBalanceLimitPrevValue = true;
                    }
                }
                mAllAccountsPreferencePresenter.updatePreferences(mAllAccountsList, accountPreference, useAppSpendLimitPrevValue, useAppBalanceLimitPrevValue, useSMSSpendLimitPrevValue, useSMSBalanceLimitPrevValue);
            }

            @Override
            public void onNegativeButtonClick() {
                mUserConsentDialog.dismiss();
            }
        });
        if (!mUserConsentDialog.isVisible()) {
            mUserConsentDialog.show(getSupportFragmentManager(), UserConsentDialog.TAG);
        }
    }


    private boolean isSameSettings() {
        AccountPreference accountPreference = mAllAccountsList.get(0);
        for (int i = 1; i < mAllAccountsList.size(); i++) {
            if (!accountPreference.toString().equals(mAllAccountsList.get(i).toString())) {
                return false;
            }
        }
        return true;
    }
    private void announceForAccessibility(String accesiblitycontent) {
        Observable.timer(20, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result-> AccessibilityUtils.announceForAccessibility(this, accesiblitycontent));

    }

    public void onPushCheckedChanged(boolean isChecked) {

        if (isChecked) {
            binding.pushPreferenceContainer.setVisibility(View.VISIBLE);
            binding.textHowMuchSpend.setText(getResources().getString(R.string.default_spend_limit));
            binding.textLowAccountBalance.setText(getResources().getString(R.string.default_balance_limit));
            announceForAccessibility(getString(R.string.you_are_choosing_to_switch_push));
        } else {
            binding.pushPreferenceContainer.setVisibility(View.GONE);
            ViewUtils.hideSoftKeyboard(this, binding.viewAllAccountsPrefRecyclerview);
            announceForAccessibility(getString(R.string.you_are_choosing_to_switch_off_push));

        }
        enableSaveButton(true);
    }

    public void onSmsCheckedChanged(boolean isChecked) {

        if (isChecked) {
            binding.smsPreferenceContainer.setVisibility(View.VISIBLE);
            binding.textHowMuchSpendSms.setText(getResources().getString(R.string.default_spend_limit));
            binding.textLowAccountBalanceSms.setText(getResources().getString(R.string.default_balance_limit));
            announceForAccessibility(getString(R.string.you_are_choosing_to_switch_on_sms));
        } else {
            binding.smsPreferenceContainer.setVisibility(View.GONE);
            ViewUtils.hideSoftKeyboard(this, binding.viewAllAccountsPrefRecyclerview);
            announceForAccessibility(getString(R.string.you_are_choosing_to_switch_off_sms));
        }
        enableSaveButton(true);
    }

    public void onAllAccountsCheckedChanged(boolean isChecked) {

        if (isChecked) {
            binding.preferenceContainer.setVisibility(View.VISIBLE);
            binding.viewAllAccountsPrefRecyclerview.setVisibility(View.GONE);
            binding.dividerAccountList.setVisibility(View.GONE);
            binding.dividerList.setVisibility(View.GONE);
            enableSaveButton(false);
        } else {
            binding.preferenceContainer.setVisibility(View.GONE);
            binding.viewAllAccountsPrefRecyclerview.setVisibility(View.VISIBLE);
            binding.dividerAccountList.setVisibility(View.VISIBLE);
            binding.dividerList.setVisibility(View.VISIBLE);
            ViewUtils.hideSoftKeyboard(this, binding.viewAllAccountsPrefRecyclerview);
        }

    }

    @Override
    public void showProgress(boolean isVisible) {
        binding.progressBar.setVisibility(isVisible ? View.VISIBLE : View.GONE);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(menuItem);
    }

    @Override
    public void onBackPressed() {
        mAllAccountsPreferencePresenter.trackActionAllAccountPreferenceBack();
        super.onBackPressed();

    }

    @Override
    public void onItemClick(Integer item) {
        mAllAccountsPreferencePresenter.navigateToAccountPreference(mAllAccountsList.get(item));
    }

    @Override
    public void showErrorForUpdatePreferences(boolean isApiFailure, boolean isPushPrefOn, boolean isSMSPrefOn) {
        showError(getString(R.string.something_went_wrong), getString(R.string.try_again_later), getString(R.string.snackbar_action_undo), () -> mAllAccountsPreferencePresenter.onUndoClick());
        mAllAccountsPreferencePresenter.trackFailure(isApiFailure, getString(R.string.something_went_wrong), isPushPrefOn, isSMSPrefOn);
    }

    @Override
    public void enableSaveButton(boolean isEnable) {
        binding.saveChangesButton.setEnabled(isEnable);
        binding.saveChangesButton.setClickable(isEnable);
    }

    @Override
    public void updateUI() {
        setupUI();
    }

}
