package za.co.nedbank.ui.domain.mapper.pop;

import javax.inject.Inject;

import za.co.nedbank.core.data.mapper.MetaDataEntityToDataMapper;
import za.co.nedbank.ui.data.entity.pop.ShareProofOfPaymentResponseEntity;
import za.co.nedbank.ui.domain.model.pop.SharePOPResponseData;

public class SharePOPResponseEntityToDataMapper {
    private final MetaDataEntityToDataMapper mMetaDataEntityToDataMapper;

    @Inject
    public SharePOPResponseEntityToDataMapper(MetaDataEntityToDataMapper metaDataEntityToDataMapper) {
        this.mMetaDataEntityToDataMapper = metaDataEntityToDataMapper;
    }

    public SharePOPResponseData mapSharePOPResponseEntityToDataList(ShareProofOfPaymentResponseEntity shareProofOfPaymentResponseEntity) {
        SharePOPResponseData sharePOPResponseData = new SharePOPResponseData();
        if (shareProofOfPaymentResponseEntity != null) {
            sharePOPResponseData.setMetadata(mMetaDataEntityToDataMapper.mapMetaData(shareProofOfPaymentResponseEntity.getMetadata()));
        }
        return sharePOPResponseData;
    }

}
