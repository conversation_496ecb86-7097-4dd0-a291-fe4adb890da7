package za.co.nedbank.ui.notifications;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.adobe.marketing.mobile.Messaging;
import com.google.gson.Gson;

import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;

public class PushNotificationItemResolver implements NotificationItemResolver {

    private final NotificationManager mNotificationManager;

    public PushNotificationItemResolver(NotificationManager notificationManager) {
        this.mNotificationManager = notificationManager;
    }

    @Override
    public PushNotificationItem resolve(Context context, NotificationData data) {

        PushNotificationItem item = new PushNotificationItem();
        item.setNotificationData(data);
        item.setChannel(createNotificationChannel(data));
        item.setPendingIntent(createNavigationPendingIntent(context, data, NotificationConstants.ACTIONS.DEFAULT_NOTIFICATION_NAVIGATION));
        item.setDeletePendingIntent(createNavigationPendingIntent(context, data, NotificationConstants.ACTIONS.DISMISS));
        return item;
    }

    @Override
    public PushNotificationItem resolveAJO(Context context, AjoPushPayloadDataModel data) {
        PushNotificationItem item = new PushNotificationItem();
        item.setAjoNotificationData(data);
        item.setChannel(createAJONotificationChannel(data));
        item.setPendingIntent(createAJONavigationPendingIntent(context, data, NotificationConstants.ACTIONS.DEFAULT_NOTIFICATION_NAVIGATION));
        item.setDeletePendingIntent(createAJONavigationPendingIntent(context, data, NotificationConstants.ACTIONS.DISMISS));
        return item;
    }

    private PendingIntent createNavigationPendingIntent(Context context, NotificationData data, String action) {
        Intent intent = new Intent(context, NotificationNavigationActivity.class);
        intent.putExtra(NotificationConstants.EXTRA.NOTIFICATION_ID, (int) data.getNotificationId());
        intent.putExtra(NotificationConstants.EXTRA.NOTIFICATION_DATA, data);
        intent.setAction(action);
        if ((Build.VERSION.SDK_INT < Build.VERSION_CODES.M))
            return PendingIntent.getActivity(context, data.getNotificationId(), intent, PendingIntent.FLAG_UPDATE_CURRENT);
        return PendingIntent.getActivity(context, data.getNotificationId(), intent, PendingIntent.FLAG_IMMUTABLE);
    }

    private PendingIntent createAJONavigationPendingIntent(Context context, AjoPushPayloadDataModel data, String action) {
        Intent intent = new Intent(context, NotificationNavigationActivity.class);
        intent.putExtra(NotificationConstants.EXTRA.NOTIFICATION_ID, data.getMessageId().hashCode());
        intent.putExtra(NotificationConstants.EXTRA.AJO_NOTIFICATION_DATA, new Gson().toJson(data));
        Messaging.addPushTrackingDetails(intent, data.getMessageId(), data.getData());
        intent.setAction(action);
        if ((Build.VERSION.SDK_INT < Build.VERSION_CODES.M))
            return PendingIntent.getActivity(context, data.getMessageId().hashCode(), intent, PendingIntent.FLAG_UPDATE_CURRENT);
        return PendingIntent.getActivity(context, data.getMessageId().hashCode(), intent, PendingIntent.FLAG_IMMUTABLE);
    }

    private NotificationChannel createNotificationChannel(NotificationData data) {
        NotificationChannel channel = null;
        if (isNotificationManager(data.getChannelId())) {
            int importance = NotificationManager.IMPORTANCE_HIGH;
            channel = new NotificationChannel(data.getChannelId(), data.getChannelName(), importance);
            channel.setShowBadge(true);
            mNotificationManager.createNotificationChannel(channel);

        }
        return channel;
    }

    private NotificationChannel createAJONotificationChannel(AjoPushPayloadDataModel data) {
        NotificationChannel channel = null;
        String channelId = String.valueOf(data.getChannelId().hashCode());
        if (isNotificationManager(channelId)) {
            int importance = NotificationManager.IMPORTANCE_HIGH;
            channel = new NotificationChannel(channelId, data.getChannelId(), importance);
            channel.setShowBadge(true);
            mNotificationManager.createNotificationChannel(channel);
        }
        return channel;
    }

    private boolean isNotificationManager(String channelId) {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && mNotificationManager != null
                && mNotificationManager.getNotificationChannel(channelId) == null;
    }
}
