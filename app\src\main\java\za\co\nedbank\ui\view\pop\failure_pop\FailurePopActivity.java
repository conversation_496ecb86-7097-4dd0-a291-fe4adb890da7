package za.co.nedbank.ui.view.pop.failure_pop;

import android.os.Bundle;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityFailurePopBinding;
import za.co.nedbank.ui.di.AppDI;

public class FailurePopActivity extends NBBaseActivity implements FailurePopView {
    @Inject
    FailurePopPresenter mPresenter;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityFailurePopBinding binding = ActivityFailurePopBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        initToolbar(binding.toolbar, true, getResources().getString(R.string.transaction_notification));
        mPresenter.bind(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.unbind();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }
}
