<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:minHeight="?attr/actionBarSize"
        app:layout_collapseMode="pin"
        app:popupTheme="@style/AppTheme.PopupOverlay"
        app:theme="@style/ToolBarStyle" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolbar">

        <LinearLayout
            android:id="@+id/llMainView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:importantForAccessibility="no"
            android:orientation="vertical"
            android:paddingBottom="@dimen/dimen_20dp">

            <za.co.nedbank.uisdk.component.CompatTextViewEnhanced
                android:id="@+id/tvDebitDescription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_20dp"
                android:layout_marginTop="@dimen/dimen_40dp"
                android:layout_marginEnd="@dimen/dimen_20dp"
                android:focusable="true"
                android:fontFamily="@string/sans_serif_light"
                android:lineSpacingExtra="@dimen/dimen_4dp"
                android:text="@string/vvap_edit_banking_title"
                android:textColor="@color/color_ff333333"
                android:textSize="@dimen/dimen_22sp"
                android:textStyle="normal" />

            <za.co.nedbank.uisdk.component.CompatTextViewEnhanced
                android:id="@+id/tvAnnualMonthlyTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_20dp"
                android:layout_marginTop="@dimen/dimen_20dp"
                android:layout_marginEnd="@dimen/dimen_20dp"
                android:fontFamily="@string/roboto_regular"
                android:text="@string/insurance_monthly_premium"
                android:textColor="@color/black_333333"
                android:textSize="@dimen/dimen_16sp"
                android:visibility="gone" />

            <za.co.nedbank.uisdk.component.CompatTextViewEnhanced
                android:id="@+id/tvAnnualMonthlyPremium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_20dp"
                android:layout_marginEnd="@dimen/dimen_20dp"
                android:fontFamily="@string/roboto_medium"
                android:lineSpacingExtra="@dimen/dimen_5dp"
                android:textColor="@color/black_333333"
                android:textSize="@dimen/dimen_22sp"
                android:visibility="gone" />

            <za.co.nedbank.uisdk.component.CompatTextViewEnhanced
                android:id="@+id/tvDiscount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_20dp"
                android:layout_marginTop="@dimen/dimen_5dp"
                android:layout_marginEnd="@dimen/dimen_20dp"
                android:fontFamily="@string/roboto_regular"
                android:text="@string/pl_edit_banking_discount"
                android:textColor="@color/color_bbbbbb"
                android:textSize="@dimen/dimen_14sp"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/rlNedBank"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_20dp"
                android:layout_marginTop="@dimen/dimen_20dp"
                android:layout_marginEnd="@dimen/dimen_20dp">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rbNedBank"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:contentDescription="@string/nedbank_account"
                    android:drawableStart="@drawable/radiobutton_selector"
                    android:drawablePadding="@dimen/dimen_10dp" />

                <za.co.nedbank.uisdk.component.CompatTextViewEnhanced
                    android:id="@+id/tvNedBank"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toEndOf="@id/rbNedBank"
                    android:fontFamily="@string/roboto_regular"
                    android:gravity="center_vertical"
                    android:lineSpacingExtra="@dimen/dimen_5dp"
                    android:text="@string/nedbank_account"
                    android:textColor="@color/color_333333"
                    android:textSize="@dimen/text_16sp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rlOtherBank"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_20dp"
                android:layout_marginTop="@dimen/dimen_20dp"
                android:layout_marginEnd="@dimen/dimen_20dp">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/rbOtherBank"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:contentDescription="@string/other_bank"
                    android:drawableStart="@drawable/radiobutton_selector"
                    android:drawablePadding="@dimen/dimen_10dp" />

                <za.co.nedbank.uisdk.component.CompatTextViewEnhanced
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toEndOf="@id/rbOtherBank"
                    android:fontFamily="@string/roboto_regular"
                    android:gravity="center_vertical"
                    android:lineSpacingExtra="@dimen/dimen_5dp"
                    android:text="@string/other_bank"
                    android:textColor="@color/color_333333"
                    android:textSize="@dimen/text_16sp" />
            </RelativeLayout>

            <!--Funeral Products Information Container-->
            <LinearLayout
                android:id="@+id/llFuneralInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_20dp"
                android:background="@color/color_3078be20"
                android:orientation="horizontal"
                android:padding="@dimen/dimen_20dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:importantForAccessibility="no"
                    app:srcCompat="@drawable/ic_wrapper_icon_info" />

                <za.co.nedbank.uisdk.component.CompatTextViewEnhanced
                    android:id="@+id/tvFuneralInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/dimen_14dp"
                    android:focusable="true"
                    android:fontFamily="@string/font_family_body_3_material"
                    android:importantForAccessibility="yes"
                    android:lineSpacingExtra="@dimen/dimen_5dp"
                    android:paddingStart="@dimen/dimen_1dp"
                    android:paddingEnd="@dimen/dimen_5dp"
                    android:text="@string/nifp_policy_type_no_other_banks"
                    android:textColor="@color/gray_666666"
                    android:textSize="@dimen/text_14sp" />
            </LinearLayout>

            <!--Ned bank View-->
            <RelativeLayout
                android:id="@+id/rl_ned_bank_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:visibility="gone">

                <fragment
                    android:id="@+id/fragmentAccounts"
                    android:name="za.co.nedbank.core.view.accounts.ui.AccountsViewFragment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    app:header="@string/insurance_from_which_account"
                    app:is_from="false" />
            </RelativeLayout>

            <!--Other Bank View-->
            <LinearLayout
                android:id="@+id/llOtherBankContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:importantForAccessibility="no"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:id="@+id/llAddBankInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_20dp"
                    android:background="@color/color_3078be20"
                    android:orientation="horizontal"
                    android:padding="@dimen/dimen_20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:importantForAccessibility="no"
                        app:srcCompat="@drawable/ic_wrapper_icon_info" />

                    <za.co.nedbank.uisdk.component.CompatTextViewEnhanced
                        android:id="@+id/tvAddBankInfo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/dimen_14dp"
                        android:focusable="true"
                        android:fontFamily="@string/font_family_body_3_material"
                        android:importantForAccessibility="yes"
                        android:lineSpacingExtra="@dimen/dimen_5dp"
                        android:paddingStart="@dimen/dimen_1dp"
                        android:paddingEnd="@dimen/dimen_5dp"
                        android:text="@string/we_can_only_debit_bank_account"
                        android:textColor="@color/gray_666666"
                        android:textSize="@dimen/text_14sp" />
                </LinearLayout>

                <za.co.nedbank.uisdk.component.CompatPicker
                    android:id="@+id/bankPicker"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_20dp"
                    android:layout_marginTop="@dimen/dimen_20dp"
                    android:layout_marginEnd="@dimen/dimen_20dp"
                    android:hint="@string/insurance_select_bank_name"
                    app:esEllipsize="end"
                    app:es_drawableRight="@drawable/ic_right_arrow_silver_wrapper"
                    app:es_label_text="@string/bank_name" />

                <za.co.nedbank.uisdk.component.CompatPicker
                    android:id="@+id/branchCodePicker"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dimen_20dp"
                    android:hint="@string/insurance_select_branch_code"
                    android:visibility="gone"
                    app:esEllipsize="end"
                    app:es_drawableRight="@drawable/ic_right_arrow_silver"
                    app:es_label_text="@string/insurance_branch_code" />

                <za.co.nedbank.uisdk.component.CompatPicker
                    android:id="@+id/accountTypePicker"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_20dp"
                    android:layout_marginEnd="@dimen/dimen_20dp"
                    android:hint="@string/insurance_select_account_type"
                    android:visibility="gone"
                    app:esEllipsize="end"
                    app:es_drawableRight="@drawable/ic_right_arrow_silver"
                    app:es_label_text="@string/insurance_account_type" />

                <za.co.nedbank.uisdk.component.CompatEditText
                    android:id="@+id/edtAccountNumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_20dp"
                    android:layout_marginTop="@dimen/dimen_20dp"
                    android:layout_marginEnd="@dimen/dimen_20dp"
                    android:hint="@string/insurance_enter_account_number"
                    android:maxLength="@integer/integer_11"
                    android:visibility="gone"
                    app:esEllipsize="end"
                    app:es_drawableRight="@drawable/ic_right_arrow_silver"
                    app:es_info="@string/pl_edit_banking_your_account"
                    app:es_input_style="number"
                    app:es_label_text="@string/insurance_account_number"
                    app:imeOptions="actionDone" />
            </LinearLayout>

            <androidx.appcompat.widget.AppCompatCheckBox
                android:id="@+id/chkBoxConsent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginStart="@dimen/dimen_20dp"
                android:layout_marginTop="@dimen/dimen_40dp"
                android:layout_marginEnd="@dimen/dimen_20dp"
                android:background="?selectableItemBackground"
                android:button="@null"
                android:checked="false"
                android:drawableStart="@drawable/debit_order_checkbox_selector"
                android:drawablePadding="@dimen/dimen_20dp"
                android:fontFamily="@string/roboto_light"
                android:paddingBottom="@dimen/dimen_10dp"
                android:text="@string/vvap_consent_term_desc"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/dimen_16sp" />

            <za.co.nedbank.uisdk.component.CompatButton
                android:id="@+id/saveBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginStart="@dimen/dimen_20dp"
                android:layout_marginTop="@dimen/dimen_25dp"
                android:layout_marginEnd="@dimen/dimen_20dp"
                android:enabled="false"
                android:text="@string/vvap_finance_edit_save_changes"
                android:visibility="gone"
                app:es_button_type="primary" />
        </LinearLayout>
    </ScrollView>

    <ProgressBar
        android:id="@+id/progressBarView"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:indeterminate="true"
        android:visibility="gone" />
</RelativeLayout>