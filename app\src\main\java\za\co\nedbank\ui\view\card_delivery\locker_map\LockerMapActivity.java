package za.co.nedbank.ui.view.card_delivery.locker_map;

import static com.google.android.material.snackbar.BaseTransientBottomBar.LENGTH_LONG;
import static za.co.nedbank.core.Constants.TWO;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.view.AccessibilityDelegateCompat;
import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.DividerItemDecoration;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.jakewharton.rxbinding2.widget.RxTextView;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.domain.model.location.AddressSuggestion;
import za.co.nedbank.core.domain.model.location.PlaceDetails;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.map.variants.OnXMapReadyCallback;
import za.co.nedbank.core.view.map.variants.XClusterItem;
import za.co.nedbank.core.view.map.variants.XLatLng;
import za.co.nedbank.core.view.map.variants.XMap;
import za.co.nedbank.core.view.map.variants.XMapMarkerMode;
import za.co.nedbank.databinding.ActivityLockerMapBinding;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConstants;
import za.co.nedbank.ui.view.card_delivery.SpaceItemDecoration;

public class LockerMapActivity extends NBBaseActivity implements LockerMapView, LockerAddressSuggestionAdapter.AddressSelectionListener, OnXMapReadyCallback, LockerListAdapter.OnLockerItemClickListener {

    @Inject
    XMap xMap;
    @Inject
    LocationManager locationManager;
    private boolean skipNextTextChange;
    private static final XLatLng DEFAULT_CAM_POS = new XLatLng(-28.4792625, 24.6727135);

    private static final int ZOOM_IN_ON_PIN_TRESHOLD = 12;

    private static final int COUNTRY_ZOOM = 5;

    private static final int MAP_PADDING = 100;

    private final HashMap<String, XClusterItem> clusterItems = new HashMap<>();

    private XClusterItem selectedItem;

    private XLatLng currentSearchLatLong;

    private boolean isManualSearch;

    @Inject
    LockerMapPresenter presenter;
    private LockerAddressSuggestionAdapter addressListAdapter;
    private boolean geolocationEnabled;
    private LockerListAdapter lockerListBottomSheetAdapter;
    private SpaceItemDecoration spaceItemDecoration;
    private BottomSheetBehavior<View> bottomSheetBehavior;
    private View searchIcon;
    private ActivityLockerMapBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityLockerMapBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        presenter.bind(this);
        initToolbar(binding.toolbar, true, getResources().getString(R.string.find_a_locker));
        searchIcon = binding.etPlace.findViewById(za.co.nedbank.uisdk.R.id.searchIcon);
        setUpMap();
        setUpBottomSheet();
        initAddressAdapter();
        initLocationSearch();
        setClearSearchAccessibility();
        setUpBottomSheetAccessibility();
        presenter.sendPageAnalytics();
    }

    private void setUpBottomSheetAccessibility() {
        ViewCompat.setAccessibilityDelegate(binding.layoutBottomSheetLockerList.tvLockerListHeading, new AccessibilityDelegateCompat() {
            @Override
            public void sendAccessibilityEvent(View host, int eventType) {
                if (eventType == AccessibilityEvent.TYPE_VIEW_HOVER_ENTER
                        || eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                    handleLockerListAccessibility();
                }
                super.sendAccessibilityEvent(host, eventType);
            }
        });
    }

    private void setUpBottomSheet() {
        bottomSheetBehavior = BottomSheetBehavior.from(binding.layoutBottomSheetLockerList.lockersBottomSheet);
        bottomSheetBehavior.addBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
            @Override
            public void onStateChanged(@NonNull @NotNull View bottomSheet, int newState) {
                if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                    binding.layoutBottomSheetLockerList.tvLockerListHeading.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED);
                    binding.etPlace.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS);
                } else {
                    binding.etPlace.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
                }
            }

            @Override
            public void onSlide(@NonNull @NotNull View bottomSheet, float slideOffset) {
                //Do nothing
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.initialSetUp();
        presenter.checkLocationEnabled();
        if (getInitialLocation() == null && currentSearchLatLong == null)
            binding.etPlace.getInputField().requestFocus();
    }

    private void setUpMap() {
        binding.searchContainer.post(() -> {
            //placing mapview below search widget
            float adjustedY = binding.searchContainer.getY() + binding.searchContainer.getHeight();
            binding.mapContainer.setY(adjustedY);
            binding.lockerMapNotAvailableInfo.setY(adjustedY);
            binding.lockerLoader.setY(adjustedY - getResources().getDimensionPixelSize(R.dimen.dimen_5dp));
        });

        if (xMap != null) {
            replaceFragment((NBBaseFragment) xMap, R.id.map_container);
        }

    }

    private void initAddressAdapter() {
        addressListAdapter = new LockerAddressSuggestionAdapter(this);
        addressListAdapter.setAddressSelectionListener(this);
        binding.etPlace.setAdapter(addressListAdapter);
    }
    @SuppressLint("CheckResult")
    private void initLocationSearch() {
        RxTextView.textChanges(binding.etPlace.getInputField())
                .map(input -> input.toString().length() > TWO ? input : StringUtils.EMPTY_STRING)
                .skip(1)
                .debounce(500, TimeUnit.MILLISECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(chars -> {
                    setClearSearchAccessibility();
                    presenter.sendEventAnalytics();
                    // first condition is only executed if the EditText was directly changed by the user
                    if (skipNextTextChange) {
                        skipNextTextChange = false;
                    } else {
                        binding.etPlace.clearErrors();
                        if (StringUtils.isNotEmpty(chars)) {
                            presenter.searchQueryChanged(String.valueOf(chars));
                        } else {
                            announceAddressClearedAccessibility();
                            hideAddressSuggestions();
                        }
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }


    @Override
    public void hideAddressSuggestions() {
        binding.etPlace.showRecyclerView(false);
        toggleBottomSheetVisibility(true);
    }

    private void toggleBottomSheetVisibility(boolean tryToshow) {
        if (tryToshow && lockerListBottomSheetAdapter != null
                && lockerListBottomSheetAdapter.getItemCount() > 0) {
            bottomSheetBehavior.setPeekHeight(getResources().getDimensionPixelSize(R.dimen.dimen_135dp), true);
        } else {
            bottomSheetBehavior.setPeekHeight(0, false);
        }
    }

    private void handleLockerListAccessibility() {
        if (AccessibilityUtils.isAccessibilityServiceEnabled(this)) {
            bottomSheetBehavior.setState(BottomSheetBehavior.STATE_EXPANDED);
        }
    }

    @Override
    public void showLoadingInProgess(final boolean inProgress) {
        binding.lockerLoader.setVisibility(inProgress ? View.VISIBLE : View.INVISIBLE);
    }

    @Override
    public void showNearByLockerAPIFailedError() {
        handleLockerApiFailure(R.string.nearest_locker_did_not_load, false);
    }

    @Override
    public String getCardActionName() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_ACTION_NAME);
    }

    private void handleLockerApiFailure(int message, boolean isManualSearch) {
        if (isEficaFlow()) {
            presenter.navigateToLockerLocationError();
        } else {
            showError(getString(R.string.something_went_wrong),
                    getString(message),
                    getString(R.string.retry), LENGTH_LONG,
                    () -> presenter.loadNearbyLockerLocations(currentSearchLatLong, isManualSearch));
        }
    }

    @Override
    public void showManualSearchLockerAPIFailedError() {
        handleLockerApiFailure(R.string.list_of_locker_did_not_load, true);

    }

    @Override
    public void clearPlaceSelection() {
        if (selectedItem != null) {
            xMap.setRenderedSelected(selectedItem, false);
            selectedItem = null;
        }
    }

    @Override
    public void showPlaceSelected(PlaceDetails place) {
        ViewUtils.hideSoftKeyboard(this, binding.etPlace);
        binding.etPlace.requestFocus();
        if (selectedItem != null) {
            xMap.setRenderedSelected(selectedItem, false);
        }
        selectedItem = clusterItems.get(place.id);
        if (selectedItem != null) {
            xMap.setRenderedSelected(selectedItem, true);
        }
    }

    @Override
    public void showPlaceDetails(PlaceDetails placeDetails) {
        presenter.navigateToConfirmation(placeDetails);
    }

    @Override
    public void showResultPlacesOnBottonSheet(List<PlaceDetails> lockers) {
        if (CollectionUtils.isEmpty(lockers)) return;

        if (lockerListBottomSheetAdapter == null) {
            lockerListBottomSheetAdapter = new LockerListAdapter(lockers, getInitialLocation());
            lockerListBottomSheetAdapter.setItemClickListener(this);
            spaceItemDecoration = new SpaceItemDecoration(lockers.size() - 1, R.dimen.dimen_56dp);
            binding.layoutBottomSheetLockerList.rvLockers.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
            binding.layoutBottomSheetLockerList.rvLockers.addItemDecoration(spaceItemDecoration);
            binding.layoutBottomSheetLockerList.rvLockers.setAdapter(lockerListBottomSheetAdapter);
            binding.layoutBottomSheetLockerList.rvLockers.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
        } else {
            lockerListBottomSheetAdapter.updateItems(lockers);
            spaceItemDecoration.updatePosition(lockers.size() - 1);
        }
        toggleBottomSheetVisibility(true);
        handleLockerListAccessibility();
    }


    @Override
    public void showSuggestionFailedError(boolean showError) {
        if (showError)
            showError(getString(R.string.something_went_wrong_dot), getString(R.string.suggestion_failed_error_msg));
        else
            showError(getString(R.string.something_went_wrong_dot), getString(R.string.no_results_found));
    }

    @Override
    public void showAddressSuggestions(List<AddressSuggestion> suggestions) {
        toggleBottomSheetVisibility(false);
        binding.etPlace.showRecyclerView(true);
        addressListAdapter.swapData(suggestions);
    }

    @Override
    public boolean isMapServiceAvailable() {
        return xMap.isMapServiceAvailable(this);
    }

    @Override
    public void setMapNotAvailableInfoVisible(boolean isVisible) {
        binding.lockerMapNotAvailableInfo.setVisibility(isVisible ? View.VISIBLE : View.GONE);
    }

    @Override
    public void initMap() {
        if (xMap != null) {
            xMap.getMapAsync(this, XMapMarkerMode.DSV_LOCKER_MARKER);
        }
    }

    @Override
    public void enableGeolocation(boolean isEnabled) {
        this.geolocationEnabled = isEnabled;
        if (xMap != null && xMap.isExMapInitialized()) {
            xMap.setMyLocationEnabled(geolocationEnabled);
            XLatLng latLng = getInitialLocation();

            if (latLng != null) {
                xMap.moveCamera(latLng, Constants.DEFAULT_CAMERA_ZOOM);
                presenter.loadNearbyLockerLocations(latLng, false);
            } else {
                binding.etPlace.getInputField().requestFocus();
            }
        }
    }

    @Override
    public void setCurrentSearchLatLong(XLatLng latLng) {
        currentSearchLatLong = latLng;
    }

    @Override
    public void setIsManualSearch(boolean isManualSearch) {
        this.isManualSearch = isManualSearch;
    }

    @Override
    public void finishSearchEditing(AddressSuggestion suggestion) {
        skipNextTextChange = true;
        binding.etPlace.setText(String.format("%s%s%s", suggestion.primary, StringUtils.COMA_SEPARATOR, suggestion.secondary), true);
        binding.etPlace.getInputField().setSelection(binding.etPlace.getInputField().getText().length());
        ViewUtils.hideSoftKeyboard(this, binding.etPlace);
    }

    @Override
    public void showPlaceLookupFailedError() {
        showError(getString(R.string.something_went_wrong), getString(za.co.nedbank.profile.R.string.unable_to_find_location_details));
    }

    @Override
    public void resetUI() {
        xMap.clearCluster();
        clusterItems.clear();
        ViewUtils.hideSoftKeyboard(this, binding.etPlace);
        binding.etPlace.requestFocus();
    }

    @Override
    public void navigateToPlace(PlaceDetails place, boolean zoomIn) {
        if (xMap != null && xMap.isExMapInitialized()) {
            if (zoomIn || xMap.getZoom() <= ZOOM_IN_ON_PIN_TRESHOLD) {
                if (place.location != null) {
                    xMap.animateCamera(new XLatLng(place.location.getLatitude(), place.location.getLongitude()), Constants.DISTRICT_LEVEL_ZOOM, true);
                }
            } else {
                if (place.location != null) {
                    xMap.animateCamera(new XLatLng(place.location.getLatitude(), place.location.getLongitude()), 0, false);
                }
            }
        }
    }

    @Override
    public void showResultPlacesOnMap(List<PlaceDetails> placeDetailsList) {
        List<XLatLng> latLngList = new ArrayList<>();
        PlaceDetails placeDetails;
        for (int i = Constants.ZERO; i < placeDetailsList.size(); i++) {
            placeDetails = placeDetailsList.get(i);
            XClusterItem item;
            if (i < 10) {
                item = new XClusterItem(placeDetails, true);
                latLngList.add(new XLatLng(placeDetails.location.getLatitude(), placeDetails.location.getLongitude()));
            } else {
                item = new XClusterItem(placeDetails, false);
            }
            xMap.addClusterItem(item);
            clusterItems.put(placeDetails.id, item);
        }

        xMap.doClustering();
        if (CollectionUtils.isNotEmpty(placeDetailsList)) {
            xMap.addLatLngsAndAnimateCamera(latLngList, MAP_PADDING);
        } else {
            showError(getString(R.string.something_went_wrong), getString(R.string.nothing_found));
        }
    }

    @Override
    public void onAddressSelected(AddressSuggestion selectedAddress) {
        presenter.addressSuggestionClicked(selectedAddress);
    }

    @Override
    public void onExMapReady() {
        xMap.setTiltGesturesEnabled(false);
        setMyLocation();
        moveCamera();
        loadNearByLockers();
    }

    private void setMyLocation() {
        if (geolocationEnabled) {
            xMap.setMyLocationEnabled(true);
        }
    }

    private void moveCamera() {
        if (getInitialLocation() == null) {
            xMap.moveCamera(DEFAULT_CAM_POS, COUNTRY_ZOOM);
        } else {
            xMap.moveCamera(getInitialLocation(), Constants.DEFAULT_CAMERA_ZOOM);
        }
    }

    private void loadNearByLockers() {
        if (xMap == null)
            return;
        if (currentSearchLatLong != null) {
            presenter.loadNearbyLockerLocations(currentSearchLatLong, isManualSearch);
        } else {
            XLatLng latLng = getInitialLocation();
            if (latLng != null && xMap.isExMapInitialized()) {
                xMap.moveCamera(latLng, Constants.DEFAULT_CAMERA_ZOOM);
                presenter.loadNearbyLockerLocations(latLng, isManualSearch);
            }
        }
    }


    @Override
    public void onClusterItemClick(XClusterItem xClusterItem) {
        presenter.placeClicked(xClusterItem.getPlaceDetails());
    }

    @Override
    public void mapCameraChanged() {
        //do nothing
    }

    @Override
    public void mapClicked(XLatLng xLatLng) {
        presenter.mapClicked();
    }

    @Nullable
    private XLatLng getInitialLocation() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            return null;
        }

        android.location.Location lastKnownLocation = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
        return lastKnownLocation != null ? new XLatLng(lastKnownLocation.getLatitude(), lastKnownLocation.getLongitude()) : null;
    }

    @Override
    protected void onDestroy() {
        presenter.unbind();
        super.onDestroy();
    }

    @Override
    public void onLockerItemClick(PlaceDetails placeDetails) {
        presenter.navigateToConfirmation(placeDetails);
    }

    @Override
    public void onBackPressed() {
        if (isBottomSheetExpanded()) {
            bottomSheetBehavior.setState(BottomSheetBehavior.STATE_COLLAPSED);
            binding.layoutBottomSheetLockerList.rvLockers.scrollToPosition(0);
        } else {
            super.onBackPressed();
        }
    }

    private boolean isBottomSheetExpanded() {
        return bottomSheetBehavior.getState() == BottomSheetBehavior.STATE_EXPANDED
                || bottomSheetBehavior.getState() == BottomSheetBehavior.STATE_HALF_EXPANDED;
    }

    @Override
    public boolean onOptionsItemSelected(final MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean isEficaFlow() {
        String flow = getIntent().getStringExtra(NavigationTarget.PARAM_CARD_DELIVERY_FLOW);
        return NavigationTarget.VALUE_CARD_DELIVERY_FLOW_EFICA.equals(flow);
    }

    @Override
    public String getCardDeliverySubFeature() {
        return getIntent().getStringExtra(CardDeliveryConstants.EXTRA_SELECT_CARD_DELIVERY_OPTION_NAME);
    }

    private void announceAddressClearedAccessibility() {
        if (binding.etPlace.getRecyclerView().getVisibility() == View.VISIBLE) {
            AccessibilityUtils.announceForAccessibility(this, getString(R.string.suburb_list_cleared));
        }
    }

    private void setClearSearchAccessibility() {
        if (searchIcon != null) {
            if (StringUtils.isNotEmpty(binding.etPlace.getValue())) {
                searchIcon.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
                searchIcon.setContentDescription(getString(R.string.clear_search));
            } else {
                searchIcon.setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO);
            }
        }
    }


}