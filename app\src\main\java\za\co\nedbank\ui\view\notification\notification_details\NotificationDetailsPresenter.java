package za.co.nedbank.ui.view.notification.notification_details;

import static za.co.nedbank.booking.Constants.ParamKeys.PARAM_APPOINTMENT_REF_NO;
import static za.co.nedbank.core.data.storage.StorageKeys.IS_NEW_TAG_MT;
import static za.co.nedbank.core.feature.FeatureConstants.DynamicToggle.FTR_SMALL_BUSINESS_LENDING;
import static za.co.nedbank.core.feature.FeatureConstants.TRANSACT_LOTTO_PWB;
import static za.co.nedbank.core.navigation.NavigationTarget.KIDS_ACK_FRAUD_AWARENESS_PARAM;
import static za.co.nedbank.core.navigation.NavigationTarget.KIDS_ACK_TERMS_AND_CONDITION_PARAM;
import static za.co.nedbank.core.navigation.NavigationTarget.KIDS_ACK_TERMS_AND_CONDITION_SCREEN;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_BIRTH_DATE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_CLIENT_TYPE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD;
import static za.co.nedbank.core.navigation.NavigationTarget.PARENT_ACK_FRAUD_AWARENESS_SCREEN;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.ForMyBusinessFlowType.BORROW_FLOW;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.KEY_AGREEMENT_STATUS;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.KEY_CASE_STATUS;
import static za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountConstants.INVESTMENTS;
import static za.co.nedbank.payment.opennewinvaccount.view.suggestion.SuggestionPresenter.IS_NOTICE_DEPOSIT;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.ACCIDENTAL_INSURANCE_PRODUCT_ID;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.INSURANCE_PRODUCT_ID;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.InsuranceCoverConstants.MIN_VALUE;
import static za.co.nedbank.services.view.navigation.ServicesNavigationTarget.PARAM_DOCUMENT_REFERENCE;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;

import org.greenrobot.eventbus.EventBus;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.Observable;
import za.co.nedbank.booking.common.navigator.BookingNavigatorTarget;
import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.FicaProductFlow;
import za.co.nedbank.core.ResponseStorageKey;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.convochatbot.view.ChatbotConstants;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.data.storage.StorageUtility;
import za.co.nedbank.core.deeplink.DeeplinkUtils;
import za.co.nedbank.core.deeplink.notification.NotificationAdapter;
import za.co.nedbank.core.domain.CachableValue;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.accounts.AccountDetailData;
import za.co.nedbank.core.domain.model.beneficiary.GetBeneficiaryData;
import za.co.nedbank.core.domain.model.beneficiary.bank.BankBeneficiaryData;
import za.co.nedbank.core.domain.model.kids.KidsAckNoticeRequestDataModel;
import za.co.nedbank.core.domain.model.kids.KidsAckTncRequestDataModel;
import za.co.nedbank.core.domain.model.kids.KidsNoticeDataModel;
import za.co.nedbank.core.domain.model.kids.KidsTncDataModel;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.FBNotificationUserChoiceData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsAnalyticData;
import za.co.nedbank.core.domain.model.notifications.FBResponseData;
import za.co.nedbank.core.domain.model.preapprovedoffers.GetPreApprovedOfferRequestData;
import za.co.nedbank.core.domain.model.profile.UserProfile;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetEmcertIdUseCase;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.accounts.GetAccountDetailUseCase;
import za.co.nedbank.core.domain.usecase.enrol.LoginSecurityUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.domain.usecase.investmentonline.GetFicaStatusUseCase;
import za.co.nedbank.core.domain.usecase.ita.ITADeviceManagementUseCase;
import za.co.nedbank.core.domain.usecase.moa.ProductUseCase;
import za.co.nedbank.core.domain.usecase.notifications.FBNotificationUserChoiceUseCase;
import za.co.nedbank.core.domain.usecase.notifications.FBNotificationsAnalyticsUseCase;
import za.co.nedbank.core.domain.usecase.preapprovedoffers.GetPreApprovedOfferUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetMdmProfileUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetProfileUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.investmentonline.mapper.NoticesResponseDataModelToViewModelMapper;
import za.co.nedbank.core.investmentonline.model.NoticesDataViewModel;
import za.co.nedbank.core.investmentonline.usecase.GetListOfNoticesUsecase;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseData;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseDataToViewModelMapper;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseViewModel;
import za.co.nedbank.core.payment.recent.RecentPaymentUseCase;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.ChatTracking;
import za.co.nedbank.core.tracking.InsuranceClaimTrackingEvent;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.core.view.mapper.AccountDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.moa.ProductRequestViewModelToDataMapper;
import za.co.nedbank.core.view.mapper.moa.ProductResponseDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.preapprovedoffers.PreApprovedOffersDataToViewModelMapper;
import za.co.nedbank.core.view.model.AccountDetailViewModel;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.core.view.model.CreateNoticeViewModel;
import za.co.nedbank.core.view.model.InsuranceViewModel;
import za.co.nedbank.core.view.model.InvestmentSwitchingViewModel;
import za.co.nedbank.core.view.model.ManageInvestmentViewModel;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.booking.BookingEntryType;
import za.co.nedbank.core.view.model.moa.ProductRequestViewModel;
import za.co.nedbank.core.view.preapprovedoffers.PreApprovedOffersUtility;
import za.co.nedbank.core.view.preapprovedoffers.view_preapproved_offers.PreApprovedOffersInnerDetailsViewModel;
import za.co.nedbank.core.view.preapprovedoffers.view_preapproved_offers.PreApprovedOffersViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryDetailsViewModel;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.enroll_v2.view.applications.model.applicationList.ApplicationListResponseViewModel;
import za.co.nedbank.enroll_v2.view.applications.model.applicationList.CaseViewModel;
import za.co.nedbank.enroll_v2.view.applications.usecases.ApplicationListUseCase;
import za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants;
import za.co.nedbank.enroll_v2.view.model.fica.ClientDeviceInfoRequestViewModel;
import za.co.nedbank.loans.common.navigator.LoansNavigatorTarget;
import za.co.nedbank.loans.preapprovedaccountsoffers.Constants;
import za.co.nedbank.loans.preapprovedaccountsoffers.navigator.PreApprovedOffersNavigationTarget;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.common.OfferNavigationModel;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextDataViewModelMapper;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextFedarationDetailsViewModel;
import za.co.nedbank.payment.atm.navigator.AtmNavigatorTarget;
import za.co.nedbank.payment.common.domain.data.mapper.beneficiary.user.UserBeneficiaryMapper;
import za.co.nedbank.payment.common.domain.usecases.GetBankBeneficiaryUseCase;
import za.co.nedbank.payment.crossborder.navigator.CrossBorderNavigationTarget;
import za.co.nedbank.payment.ngi.navigator.NgiNavigatorTarget;
import za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountConstants;
import za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountTarget;
import za.co.nedbank.payment.opennewinvaccount.domain.OpenNewAccUserEntriesViewModel;
import za.co.nedbank.payment.opennewinvaccount.domain.datamodel.ProductDataModel;
import za.co.nedbank.payment.opennewinvaccount.domain.mapper.AllProductResponseDataModelToViewModelMapper;
import za.co.nedbank.payment.opennewinvaccount.domain.usecase.GetAllProductsUseCase;
import za.co.nedbank.payment.opennewinvaccount.view.model.AllProductsResponseViewModel;
import za.co.nedbank.payment.opennewinvaccount.view.model.AllProductsViewModel;
import za.co.nedbank.payment.pay.view.PayMode;
import za.co.nedbank.payment.reinvest.navigator.ReinvestNavigatorTarget;
import za.co.nedbank.payment.vas.common.domain.model.response.tag.TagData;
import za.co.nedbank.payment.vas.common.domain.usecase.GetTagListUseCase;
import za.co.nedbank.payment.vas.common.domain.usecase.GetVasOfferingsUseCase;
import za.co.nedbank.payment.vas.common.utils.AvailableLimitHolder;
import za.co.nedbank.payment.vas.common.utils.VasAccountsHolder;
import za.co.nedbank.payment.vas.common.utils.VasConstants;
import za.co.nedbank.payment.vas.common.view.mapper.request.TagIdentifierRequestViewModelToDataMapper;
import za.co.nedbank.payment.vas.common.view.mapper.response.tag.TagDataToViewModelMapper;
import za.co.nedbank.payment.vas.common.view.mapper.response.tag.TagItemDataToViewModelMapper;
import za.co.nedbank.payment.vas.common.view.model.request.tag.TagIdentifierRequestViewModel;
import za.co.nedbank.payment.vas.common.view.model.response.tag.ProductViewModel;
import za.co.nedbank.payment.vas.common.view.model.response.tag.TagItemViewModel;
import za.co.nedbank.payment.vas.common.view.model.response.tag.TagViewModel;
import za.co.nedbank.payment.vas.dailylotto.utils.LottoConstants;
import za.co.nedbank.payment.vas.electricity.model.ElectricityViewModel;
import za.co.nedbank.payment.vas.electricity.navigator.ElectricityNavigatorTarget;
import za.co.nedbank.payment.vas.electricity.utils.ElectricityConstants;
import za.co.nedbank.payment.vas.electricity.utils.ElectricityUtility;
import za.co.nedbank.payment.vas.prepaid.navigator.PrepaidNavigatorTarget;
import za.co.nedbank.payment.vas.prepaid.utils.PrepaidConstants;
import za.co.nedbank.payment.vas.prepaid.view.model.PrepaidPurchaseViewModel;
import za.co.nedbank.payment.vas.vouchers.model.VoucherPurchaseViewModel;
import za.co.nedbank.payment.vas.vouchers.navigator.VouchersNavigatorTarget;
import za.co.nedbank.payment.vas.vouchers.utils.VoucherConstants;
import za.co.nedbank.profile.view.ProfileConstants;
import za.co.nedbank.profile.view.navigation.ProfileNavigationTarget;
import za.co.nedbank.profile.view.tracking.ProfileTracking;
import za.co.nedbank.services.domain.mapper.CardInfoToDataMapper;
import za.co.nedbank.services.domain.model.DebitOrder;
import za.co.nedbank.services.domain.model.DebitOrderStatus;
import za.co.nedbank.services.domain.model.ScheduledPayment;
import za.co.nedbank.services.domain.model.account.AccountDetails;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.branchcode.BranchCodeDataModel;
import za.co.nedbank.services.domain.model.card.CardDataModel;
import za.co.nedbank.services.domain.model.card.CardInfo;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.usecase.account.GetAccountDetailsUseCase;
import za.co.nedbank.services.domain.usecase.branchcode.BranchCodeUseCase;
import za.co.nedbank.services.domain.usecase.cards.GetCardsUseCase;
import za.co.nedbank.services.domain.usecase.debit.GetDebitOrderDetailsUseCase;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.services.insurance.domain.data.request.dashboard.retrieve_policy.RetrievePolicyRequestDataModel;
import za.co.nedbank.services.insurance.domain.data.request.generic.insured_property.NonNedbankInsuredRequestDataModel;
import za.co.nedbank.services.insurance.domain.data.response.dashboard.claim_product.FuneralDependentResponseData;
import za.co.nedbank.services.insurance.domain.usecase.common.GetAllowedCoverAmountUseCase;
import za.co.nedbank.services.insurance.domain.usecase.common.GetNonNedbankInsuredPropertiesUseCase;
import za.co.nedbank.services.insurance.domain.usecase.my_cover.NIFPMyCoverAllowCoverUseCase;
import za.co.nedbank.services.insurance.domain.usecase.retrieve_policy.GetProductClaimUseCase;
import za.co.nedbank.services.insurance.domain.usecase.retrieve_policy.RetrievePolicyUseCase;
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants;
import za.co.nedbank.services.insurance.view.other.enums.generic.InsuranceProductType;
import za.co.nedbank.services.insurance.view.other.mapper.dashboard.amount.AllowedAmountResponseDataToViewMapper;
import za.co.nedbank.services.insurance.view.other.mapper.dashboard.claim_product.GetProductClaimDataToViewModelMapper;
import za.co.nedbank.services.insurance.view.other.mapper.dashboard.cover_amount.AllowedCoverAmountResponseDataToViewModelMapper;
import za.co.nedbank.services.insurance.view.other.mapper.dashboard.retrieve_policy.RetrievePolicyDataToViewModelMapper;
import za.co.nedbank.services.insurance.view.other.mapper.generic.insured_property.NonNedbankInsuredPropertiesDomainToViewMapper;
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.amount.AllowedAmountResponseViewModel;
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.claim_product.GetProductClaimViewModel;
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.cover_amount.AllowedCoverAmountResponseViewModel;
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.RetrievePolicyResponseViewModel;
import za.co.nedbank.services.insurance.view.other.model.response.generic.insured_property.NonNedbankInsuredPropertiesViewModel;
import za.co.nedbank.services.view.account.branchcode.model.BranchCodeViewModel;
import za.co.nedbank.services.view.cards.mapper.CardDataModelToViewModelMapper;
import za.co.nedbank.services.view.cards.model.CardViewModel;
import za.co.nedbank.services.view.mapper.BranchCodeDataModelToBranchCodeViewModelMapper;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.services.view.tracking.ServicesTrackingValue;

import za.co.nedbank.ui.notifications.utils.NotificationUtils;


public class NotificationDetailsPresenter extends NBBasePresenter<NotificationDetailsView> {

    private static final String TAG = NotificationDetailsPresenter.class.getSimpleName();
    private final NavigationRouter mNavigationRouter;
    private final ApplicationStorage memoryApplicationStorage;
    private final Analytics mAnalytics;
    private final FeatureSetController mFeatureSetController;
    private final StorageUtility storageUtility;
    private final ApplicationStorage mApplicationStorage;
    private final GetUserDetailUseCase mGetUserDetailUseCase;
    private final GetAccountDetailsUseCase mGetAccountDetailsUseCase;
    private final GetAccountDetailUseCase mGetAccountDetailUseCase;
    private final GetOverviewUseCase mGetOverviewUseCase;
    private final GetDebitOrderDetailsUseCase mGetDebitOrderDetailsUseCase;
    private final GetPreApprovedOfferUseCase mGetPreApprovedOffersUseCase;
    private final BranchCodeUseCase mBranchCodeUseCase;
    private final GetFicaStatusUseCase mGetFicaStatusUseCase;
    private final GetCardsUseCase mGetCardsUseCase;
    private final GetProfileUseCase mGetProfileUseCase;
    private final GetEmcertIdUseCase mGetEmcertIdUseCase;
    private final GetBankBeneficiaryUseCase mGetBankBeneficiaryUseCase;
    private final GetVasOfferingsUseCase mGetVasOfferingsUseCase;
    private final RecentPaymentUseCase mRecentPaymentUseCase;
    private final RecentPaymentResponseDataToViewModelMapper mRecentPaymentResponseDataToViewModelMapper;
    private final SwitchContextDataViewModelMapper mSwitchContextDataViewModelMapper;
    private final GetFedarationListUseCase mGetFedarationListUseCase;
    private final GetMdmProfileUseCase mGetMdmProfileUseCase;
    private final GetAllProductsUseCase mGetAllProductsUseCase;
    private final NIFPMyCoverAllowCoverUseCase mNifpMyCoverAllowCoverUseCase;
    private final GetListOfNoticesUsecase getListOfNoticesUsecase;
    private final NoticesResponseDataModelToViewModelMapper noticesResponseDataModelToViewModelMapper;
    private final AllowedAmountResponseDataToViewMapper mNifpMyCoverAmountResponseDataToViewModelMapper;
    private final AllProductResponseDataModelToViewModelMapper mAllProductResponseDataModelToViewModelMapper;
    private final TagItemDataToViewModelMapper mTagItemDataToViewModelMapper;
    private final ProductUseCase mProductUseCase;
    private final ApplicationListUseCase applicationListUseCase;
    private final ProductRequestViewModelToDataMapper mProductRequestViewModelToDataMapper;
    private final ProductResponseDataToViewModelMapper mProductResponseDataToViewModelMapper;
    private final PreApprovedOffersDataToViewModelMapper mPreApprovedOffersDataToViewModelMapper;
    private final BranchCodeDataModelToBranchCodeViewModelMapper mModelToBranchCodeViewModelMapper;
    private final CardDataModelToViewModelMapper mCardDataModelToViewModelMapper;
    private final CardInfoToDataMapper mCardInfoToDataMapper;
    private final UserBeneficiaryMapper mUserBeneficiaryMapper;
    private final RetrievePolicyUseCase mRetrievePolicyUseCase;
    private final GetAllowedCoverAmountUseCase mGetAllowedCoverAmountUseCase;
    private final AllowedCoverAmountResponseDataToViewModelMapper mAllowedCoverAmountResponseDataToViewModelMapper;
    private final RetrievePolicyDataToViewModelMapper mRetrieveResponseDataToViewModelMapper;
    private final NonNedbankInsuredPropertiesDomainToViewMapper mNonNedbankInsuredPropertiesDomainToViewMapper;
    private final GetProductClaimDataToViewModelMapper mGetProductClaimDataToViewModelMapper;
    private final GetTagListUseCase mGetTagListUseCase;
    private final TagIdentifierRequestViewModelToDataMapper mTagIdentifierRequestViewModelToDataMapper;
    private final TagDataToViewModelMapper mTagDataToViewModelMapper;
    private FBNotificationsViewModel mFBNotificationsViewModel;
    private FBNotificationsAnalyticsUseCase mFBNotificationsAnalyticsUseCase;
    private FBNotificationUserChoiceUseCase mFBNotificationUserChoiceUseCase;
    private AccountDetailDataToViewModelMapper mAccountDetailDataToViewModelMapper;
    private boolean hasTransactableAccount;
    private String mClientType;
    private String mFicaStatus;
    private double minWithdrawalAmount;
    private double entryValue;
    private AccountDetails mAccountDetails;
    private UserDetailData mUserDetailData;
    private UserDetailViewModel mUserDetailViewModel;
    private List<BranchCodeViewModel> mBranchCodeViewModelList;
    private UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper;
    private String mAccountId;
    private String mNoticeId;
    private String mAccountNo;
    private String mAccountHolderName;
    private String mFirstWithdrawalDate;
    private String mAccountSubtype;
    private UserProfile mUserProfile;
    private Map<String, CardInfo> mCardInfoMap = new HashMap<>();
    private LoginSecurityUseCase mLoginSecurityUseCase;
    private String mAccountType;
    private CardDataModel mCardDataModel;
    private AccountDetailViewModel mAccountDetailViewModel;
    private String mProductType;
    private ITADeviceManagementUseCase mITADeviceManagementUseCase;
    private List<FBNotificationsViewModel.RichContent> mRichContentList;
    private RecentPaymentResponseViewModel mRecipientPaymentViewModel;
    private RecentPaymentResponseViewModel mOnceOffPaymentsViewModel;
    private List<AccountViewModel> mInsuranceAccountList;
    private GetNonNedbankInsuredPropertiesUseCase mNonNedbankInsuredPropertiesUseCase;
    private List<NonNedbankInsuredPropertiesViewModel> mPropertiesViewModels;
    private GetProductClaimUseCase mGetProductClaimUseCase;
    private List<GetProductClaimViewModel> mProductClaimList;
    private String mRetrievePolicyNumber;
    private String mPolicyIssuedDate;
    private OpenNewAccUserEntriesViewModel userEntriesViewModel = new OpenNewAccUserEntriesViewModel();
    private int mOverViewType;
    private String mNoticeType;
    private String mPledgeStatus;
    private final String mConversationId = "undefined";
    private final NotificationAdapter notificationAdapter;

    private String documentReference;

    @Inject
    public NotificationDetailsPresenter(final NavigationRouter navigationRouter,
                                        final FBNotificationsAnalyticsUseCase fbNotificationsAnalyticsUseCase,
                                        final FBNotificationUserChoiceUseCase fbNotificationUserChoiceUseCase,
                                        final GetUserDetailUseCase getUserDetailUseCase,
                                        final GetAccountDetailsUseCase getAccountDetailsUseCase,
                                        final GetAccountDetailUseCase getAccountDetailUseCase,
                                        final GetOverviewUseCase getOverviewUseCase,
                                        final GetDebitOrderDetailsUseCase getDebitOrderDetailsUseCase,
                                        final GetPreApprovedOfferUseCase getPreApprovedOffersUseCase,
                                        final BranchCodeUseCase branchCodeUseCase,
                                        final GetFicaStatusUseCase getFicaStatusUseCase,
                                        final GetProfileUseCase getProfileUseCase,
                                        final GetEmcertIdUseCase getEmcertIdUseCase,
                                        final GetCardsUseCase getCardsUseCase,
                                        final GetVasOfferingsUseCase getVasOfferingsUseCase,
                                        final GetMdmProfileUseCase getMdmProfileUseCase,
                                        final RecentPaymentUseCase recentPaymentUseCase,
                                        final GetAllProductsUseCase getAllProductsUseCase,
                                        final GetNonNedbankInsuredPropertiesUseCase getNonNedbankInsuredPropertiesUseCase,
                                        final GetProductClaimUseCase getProductClaimUseCase,
                                        final NIFPMyCoverAllowCoverUseCase nifpMyCoverAllowCoverUseCase,
                                        final GetListOfNoticesUsecase getListOfNoticesUsecase,
                                        final ProductUseCase productUseCase,
                                        final ProductRequestViewModelToDataMapper productRequestViewModelToDataMapper,
                                        final ProductResponseDataToViewModelMapper productResponseDataToViewModelMapper,
                                        final NoticesResponseDataModelToViewModelMapper noticesResponseDataModelToViewModelMapper,
                                        final AllowedAmountResponseDataToViewMapper nifpMyCoverAmountResponseDataToViewModelMapper,
                                        final AllProductResponseDataModelToViewModelMapper allProductResponseDataModelToViewModelMapper,
                                        final RecentPaymentResponseDataToViewModelMapper recentPaymentResponseDataToViewModelMapper,
                                        final SwitchContextDataViewModelMapper switchContextDataViewModelMapper,
                                        final GetFedarationListUseCase getFedarationListUseCase,
                                        final TagItemDataToViewModelMapper tagItemDataToViewModelMapper,
                                        final GetBankBeneficiaryUseCase getBankBeneficiaryUseCase,
                                        final GetAllowedCoverAmountUseCase getAllowedCoverAmountUseCase,
                                        final NonNedbankInsuredPropertiesDomainToViewMapper nonNedbankInsuredPropertiesDomainToViewMapper,
                                        final AllowedCoverAmountResponseDataToViewModelMapper allowedCoverAmountResponseDataToViewModelMapper,
                                        final AccountDetailDataToViewModelMapper accountDetailDataToViewModelMapper,
                                        final PreApprovedOffersDataToViewModelMapper preApprovedOffersDataToViewModelMapper,
                                        final UserDetailDataToViewModelMapper userDetailDataToViewModelMapper,
                                        final BranchCodeDataModelToBranchCodeViewModelMapper modelToBranchCodeViewModelMapper,
                                        final CardDataModelToViewModelMapper cardDataModelToViewModelMapper,
                                        final UserBeneficiaryMapper userBeneficiaryMapper,
                                        final CardInfoToDataMapper cardInfoToDataMapper,
                                        final GetProductClaimDataToViewModelMapper getProductClaimDataToViewModelMapper,
                                        final FeatureSetController featureSetController,
                                        final ApplicationStorage applicationStorage,
                                        final @Named("memory") ApplicationStorage memoryApplicationStorage,
                                        final Analytics analytics,
                                        final LoginSecurityUseCase loginSecurityUseCase,
                                        final ITADeviceManagementUseCase itaDeviceManagementUseCase,
                                        final RetrievePolicyUseCase retrievePolicyUseCase,
                                        final RetrievePolicyDataToViewModelMapper retrieveResponseDataToViewModelMapper,
                                        final GetTagListUseCase getTagListUseCase,
                                        final TagIdentifierRequestViewModelToDataMapper tagIdentifierRequestViewModelToDataMapper,
                                        final TagDataToViewModelMapper tagDataToViewModelMapper,
                                        final ApplicationListUseCase applicationListUseCase,
                                        final StorageUtility storageUtility,
                                        final NotificationAdapter notificationAdapter) {
        this.mNavigationRouter = navigationRouter;
        this.mFBNotificationsAnalyticsUseCase = fbNotificationsAnalyticsUseCase;
        this.mFBNotificationUserChoiceUseCase = fbNotificationUserChoiceUseCase;
        this.mFeatureSetController = featureSetController;
        this.mApplicationStorage = applicationStorage;
        this.memoryApplicationStorage = memoryApplicationStorage;
        this.mGetUserDetailUseCase = getUserDetailUseCase;
        this.mGetAccountDetailsUseCase = getAccountDetailsUseCase;
        this.mGetAccountDetailUseCase = getAccountDetailUseCase;
        this.mGetOverviewUseCase = getOverviewUseCase;
        this.mGetDebitOrderDetailsUseCase = getDebitOrderDetailsUseCase;
        this.mGetPreApprovedOffersUseCase = getPreApprovedOffersUseCase;
        this.mBranchCodeUseCase = branchCodeUseCase;
        this.mGetFicaStatusUseCase = getFicaStatusUseCase;
        this.mGetCardsUseCase = getCardsUseCase;
        this.mGetProfileUseCase = getProfileUseCase;
        this.mGetEmcertIdUseCase = getEmcertIdUseCase;
        this.mGetBankBeneficiaryUseCase = getBankBeneficiaryUseCase;
        this.mGetFedarationListUseCase = getFedarationListUseCase;
        this.mGetMdmProfileUseCase = getMdmProfileUseCase;
        this.mRecentPaymentUseCase = recentPaymentUseCase;
        this.mGetAllowedCoverAmountUseCase = getAllowedCoverAmountUseCase;
        this.mNonNedbankInsuredPropertiesUseCase = getNonNedbankInsuredPropertiesUseCase;
        this.mGetProductClaimUseCase = getProductClaimUseCase;
        this.mGetAllProductsUseCase = getAllProductsUseCase;
        this.mNifpMyCoverAllowCoverUseCase = nifpMyCoverAllowCoverUseCase;
        this.getListOfNoticesUsecase = getListOfNoticesUsecase;
        this.mProductUseCase = productUseCase;
        this.mProductRequestViewModelToDataMapper = productRequestViewModelToDataMapper;
        this.mProductResponseDataToViewModelMapper = productResponseDataToViewModelMapper;
        this.noticesResponseDataModelToViewModelMapper = noticesResponseDataModelToViewModelMapper;
        this.mNifpMyCoverAmountResponseDataToViewModelMapper = nifpMyCoverAmountResponseDataToViewModelMapper;
        this.mAllProductResponseDataModelToViewModelMapper = allProductResponseDataModelToViewModelMapper;
        this.mGetProductClaimDataToViewModelMapper = getProductClaimDataToViewModelMapper;
        this.mNonNedbankInsuredPropertiesDomainToViewMapper = nonNedbankInsuredPropertiesDomainToViewMapper;
        this.mAllowedCoverAmountResponseDataToViewModelMapper = allowedCoverAmountResponseDataToViewModelMapper;
        this.mRecentPaymentResponseDataToViewModelMapper = recentPaymentResponseDataToViewModelMapper;
        this.mSwitchContextDataViewModelMapper = switchContextDataViewModelMapper;
        this.mAccountDetailDataToViewModelMapper = accountDetailDataToViewModelMapper;
        this.mAnalytics = analytics;
        this.mPreApprovedOffersDataToViewModelMapper = preApprovedOffersDataToViewModelMapper;
        this.mUserDetailDataToViewModelMapper = userDetailDataToViewModelMapper;
        this.mModelToBranchCodeViewModelMapper = modelToBranchCodeViewModelMapper;
        this.mCardDataModelToViewModelMapper = cardDataModelToViewModelMapper;
        this.mCardInfoToDataMapper = cardInfoToDataMapper;
        this.mUserBeneficiaryMapper = userBeneficiaryMapper;
        this.mLoginSecurityUseCase = loginSecurityUseCase;
        this.mGetVasOfferingsUseCase = getVasOfferingsUseCase;
        this.mGetTagListUseCase = getTagListUseCase;
        this.mTagDataToViewModelMapper = tagDataToViewModelMapper;
        this.mTagItemDataToViewModelMapper = tagItemDataToViewModelMapper;
        this.mITADeviceManagementUseCase = itaDeviceManagementUseCase;
        this.mRetrievePolicyUseCase = retrievePolicyUseCase;
        this.mRetrieveResponseDataToViewModelMapper = retrieveResponseDataToViewModelMapper;
        this.mTagIdentifierRequestViewModelToDataMapper = tagIdentifierRequestViewModelToDataMapper;
        this.applicationListUseCase = applicationListUseCase;
        this.storageUtility = storageUtility;
        this.notificationAdapter = notificationAdapter;
    }

    void handleCrossClick() {
        if (view != null) {
            view.close();
        }
    }

    void sendAnalyticsToServer() {
        if (mFBNotificationsViewModel != null) {
            if (mFBNotificationsViewModel.getMustTrackFirstRead() != null && !mFBNotificationsViewModel.getMustTrackFirstRead()
                    || (mFBNotificationsViewModel.getMustTrackAllReads() != null && !mFBNotificationsViewModel.getMustTrackAllReads()
                    && NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_READ.equals(mFBNotificationsViewModel.getStatus())))
                return;
            mFBNotificationsAnalyticsUseCase.execute(createAnalyticsRequest())
                    .compose(bindToLifecycle())
                    .subscribe(analyticsResponseData -> {
                        handleAnalyticsUserChoiceResponse(analyticsResponseData, null, false);

                    }, throwable -> {
                        NBLogger.e(TAG, throwable.getMessage());
                    });
        } else {
            handleNavigationErrorFlow();
        }
    }


    private FBNotificationsAnalyticData createAnalyticsRequest() {
        FBNotificationsAnalyticData analyticsRequest = new FBNotificationsAnalyticData();
        FBNotificationsAnalyticData.Analytic analytic = new FBNotificationsAnalyticData.Analytic();
        analytic.setDeviceId(getDeviceID());
        analytic.setAnalyticValue(NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_READ);
        analytic.setDeviceDate(getDate());
        analyticsRequest.setAnalytic(analytic);

        analyticsRequest.setNotificationId(mFBNotificationsViewModel.getNotificationId());
        return analyticsRequest;
    }

    String getDeviceID() {
        return mApplicationStorage.getString(NotificationConstants.STORAGE_KEYS.UUID, StringUtils.EMPTY_STRING);
    }

    void handleOnClick(FBNotificationsViewModel.ResponseOption selectedResponse, String message, String title) {
        sendAnalyticsEvents(selectedResponse);
        sendUserResponseToServer(selectedResponse);
        String action = selectedResponse.getAction();
        if (DeeplinkUtils.canMoveToDeeplinkFramework(action)) {
            prepareDataAndSendToAdapter(selectedResponse, action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_INVESTMENT_ACCOUNT)) {
            handleInvestmentAccountFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.NON_TP_JOIN_FAMILY_BANKING)) {
            handleNonTpJoinFamilyBankingFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.ENBICHAT)) {
            handleEnbiFlowChat(message, title);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SHARE_MONEY_APP)) {
            handleShareMoneyAppFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.RESPOND_FAMILY_BANKING)) {
            handleFamilyBankingFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SMART_MONEY_DASHBOARD)) {
            handleSmartMoneyDashboardFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.CREDIT_SCORE_DASHBOARD)) {
            handleCreditScoreFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BIOMETRIC_SETTINGS)) {
            handleOpenBiometricSettingsFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.JOIN_FAMILY_BANKING)) {
            handleJoinFamilyBanking();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PERSONAL_LOAN)) {
            handleApplyPersonalLoanFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OFFERS_FOR_YOU)) {
            handleOffersForYouClick();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.STATEMENT_DELIVERY_PREFERENCE)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.ACCOUNT_DOCUMENTS_STATEMENTS)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.REINVESTMENT)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }

        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.INVESTMENT_SWITCHING)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }

        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.PAYOUT)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_SWITCHING)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.EDIT_NOTICES_OF_WITHDRAWAL)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.NOTICE_ID)) {
                    mNoticeId = parameter.getValue();
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_DETAILS)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.DEBIT_ORDER_ID)) {
                    handleDebitOrderDetailsFlow(parameter.getValue());
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_LIST)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    handleAccountDetailsFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.VIEW_NOTICES_OF_WITHDRAWAL)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    handleAccountDetailsFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.GB_REDEEMPTION)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    handleAccountDetailsFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.PRE_APPROVED_CREDIT_CARD_OFFERS)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.PRE_APPROVED_OFFER_ID)) {
                    handlePreApprovedOffers(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.PRE_APPROVED_OVERDRAFT_OFFERS)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.PRE_APPROVED_OFFER_ID)) {
                    handlePreApprovedOffers(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.PRE_APPROVED_OFFERS_HL_READVANCE)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.PRE_APPROVED_OFFER_ID)) {
                    handlePreApprovedOffers(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.RECEIVED_SSL)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (NotificationConstants.ADDITIONAL_PARAM.POA_REFERENCE.equalsIgnoreCase(parameter.getName())) {
                    documentReference = parameter.getValue();
                } else if (NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER.equalsIgnoreCase(parameter.getName()) || NotificationConstants.ADDITIONAL_PARAM.POA_ACCOUNT.equalsIgnoreCase(parameter.getName())) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }

        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_FOREX)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }

        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.ACTIVATE_OVERSEAS_TRAVEL_CARD)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.CARD_NUMBER)) {
                    getPlasticIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }

        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SAVINGS_POCKET_SET_UP)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    handleSavingsPocketSetUpFlow(parameter.getValue());
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.STATEMENT_DOWNLOAD)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.LATEST)) {
            openCovid19();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_LOTTO)) {
            String birthDateParam = storageUtility.getDOB();
            if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS)) {
                handleNavigationVasToggleOffFlow(true, birthDateParam);
            } else if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_LOTTERY_SHOW_GAME)) {
                getSelectLottoGameFlowFromToggleFlag(birthDateParam);
            } else {
                handleNavigationErrorFlow();
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_FUNERAL_POLICY)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_HOME_OWNERS_COVER)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_32DAY_NOTICE_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_JUST_INVEST_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MONEY_TRADER)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_EASY_ACCESS_DEPOSIT_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_EFIXED_DEPOSIT_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PRIME_SELECT_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PLATINUM_INVEST_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PLATINUM_FIXED_DEPOSIT_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SELL_FOREX)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SUBMIT_NOTICE_WITHDRAWAL)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                    getAccountIdandInitiateFlow(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SUBMIT_CLAIM_HOC)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.PRODUCT_CODE)) {
                    loadRetrievePolicy(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SUBMIT_CLAIM_FUNERAL)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.PRODUCT_CODE)) {
                    loadRetrievePolicy(parameter.getValue(), action);
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.PAY_TO_BANK_APPROVED_ACCOUNT)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.BENEFICIARY_ID)) {
                    getBankBeneficiaryListandInitiateFlow(parameter.getValue(), null);
                } else if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.BENEFICIARY_NAME)) {
                    getBankBeneficiaryListandInitiateFlow(null, parameter.getValue());
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.MAINTAIN_SCHEDULED_PAYMENT)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.TRANSACTION_ID)) {
                    handleMaintainScheduledPaymentFlow(parameter.getValue());
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_KIDS_ACCOUNT)) {
            handleOpenKidsAccountFlow();
        } else if (action.equalsIgnoreCase((NotificationConstants.NAVIGATION_TARGET.UPDATE_NED_ID_DETAILS))) {
            handleUpdateNedIdDetails();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_PPE)) {
            String birthDateParam = storageUtility.getDOB();
            handleBuyPPEFlow(birthDateParam);
            resetLimitsAndAccounts();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_PPW)) {
            memoryApplicationStorage.putBoolean(StorageKeys.IS_PPW_FLOW, true);
            String birthDateParam = storageUtility.getDOB();
            getVasOfferings(birthDateParam, VasConstants.VasOfferingsCodes.ELECTRICITY);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_VOUCHER)) {
            String birthDateParam = storageUtility.getDOB();
            handleBuyVoucherFlow(birthDateParam);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_PREPAIDS)) {
            String birthDateParam = storageUtility.getDOB();
            handleBuyPrepaids(birthDateParam);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_URL_RESPONSE)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.REDIRECT_URL)) {
                    navigateToRedirectUrlScreen(parameter.getValue());
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.VIEW_STATEMENTS)) {
            if (selectedResponse.getAdditionalParameters() != null && !selectedResponse.getAdditionalParameters().isEmpty()) {
                for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                    if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                        getAccountIdandInitiateFlow(parameter.getValue(), action);
                    } else {
                        handleViewStatementsFlow();
                    }
                }
            } else {
                handleViewStatementsFlow();
            }

        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.MANAGE_INVESTMENT_ACCOUNT)) {
            if (selectedResponse.getAdditionalParameters() != null && !selectedResponse.getAdditionalParameters().isEmpty()) {
                for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                    if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_NUMBER)) {
                        getAccountIdandInitiateFlow(parameter.getValue(), action);
                    } else {
                        handleNavigationErrorFlow();
                    }
                }
            } else {
                handleNavigationErrorFlow();
            }

        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.NOTIFICATION_PREFS)) {
            handleNotificationPrefsFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.ITA_SETTINGS)) {
            handleITASettingsFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.NON_TP_PRODUCTS)) {
            handleNonTPProductsFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.RETENTION_JOURNEY)) {
            handleRetentionFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.ATM_BRANCH_LOCATOR)) {
            handleAtmBranchLocatorFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.PROFILE_DETAILS)) {
            handleProfileDetailsFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.RECENT_PAYMENTS)) {
            handleRecentPaymentsFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.GET_CASH)) {
            handleGetCashFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.MONEY_TRACKER_DASHBOARD)) {
            mApplicationStorage.putBoolean(IS_NEW_TAG_MT, false);
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MONEY_TRACKER_FLOW_NAVIGATION_ACTIVITY));
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.CREDIT_HEALTH)) {
            mApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME, NotificationConstants.NAVIGATION_TARGET.CREDIT_HEALTH);
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, action));
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.VIEW_GREENBACKS)) {
            mApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME, NotificationConstants.NAVIGATION_TARGET.VIEW_GREENBACKS);
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, action));
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.VIEW_APPOINTMENT)) {
            handleAppointmentFlow(selectedResponse);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SBS_COMPLETE_APPLICATION)) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (NotificationConstants.ADDITIONAL_PARAM.REFERENCE_NUMBER.equalsIgnoreCase(parameter.getName())) {
                    mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.SECOND_THIRD_DIRECTOR_APP_INFO).withParam(za.co.nedbank.enroll_v2.Constants.REFERENCE_NUMBER, parameter.getValue()));
                } else {
                    handleNavigationErrorFlow();
                }
            }

        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SETUP_APPOINTMENT)) {
            handleSetupAppointmentFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SEND_MONEY)) {
            handleSendMoneyFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SMALL_BUSINESS_LOAN)) {
            handleBusinessLoanFlow(selectedResponse);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.KIDS_BANKING_UPDATED_TNC)) {
            handleKidsUpdatedTncFlow(selectedResponse.getAdditionalParameters());
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.KIDS_BANKING_FRAUD_AWARENESS)) {
            handleKidsFraudAwarenessFlow(selectedResponse.getAdditionalParameters());
        } else if (NotificationUtils.canMoveToNavigationHandler(action)) {
            navigateToNavigationHandler(action, selectedResponse);
        } else {
            NavigationTarget target = resolveNavigationTarget(selectedResponse);
            if (target != null) {
                mNavigationRouter.navigateTo(target.withIntentFlagClearTopSingleTop(true));
            } else {
                handleNavigationErrorFlow();
            }
        }
    }

    private void prepareDataAndSendToAdapter(FBNotificationsViewModel.ResponseOption selectedResponse, String action) {
        HashMap<String, String> paramData = null;
        if (selectedResponse.getAdditionalParameters() != null) {
            paramData = new HashMap<>();
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                paramData.put(parameter.getName(), parameter.getValue());
            }
        }
        notificationAdapter.fetchDataFromNotification(action, paramData, null);
    }


    private void navigateToNavigationHandler(String action, FBNotificationsViewModel.ResponseOption selectedResponse) {
        HashMap param = new HashMap<String, String>();
        for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
            param.put(parameter.getName(), parameter.getValue());
        }
        NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.TARGET_GLOBAL_NAVIGATION_HANDLER)
                .withParam(NotificationConstants.Navigation.TARGET, action)
                .withParam(NotificationConstants.Navigation.PARAM, param)
                .withParam(NotificationConstants.Navigation.FROM, NotificationConstants.AjoConstants.PUSH_NOTIFICATION);
        mNavigationRouter.navigateWithResult(targetScreen).subscribe(
                navigationResult -> {
                    boolean actionError = NotificationConstants.AjoConstants.ERROR
                            .equalsIgnoreCase(navigationResult.getStringParam(NotificationConstants.AjoConstants.ACTION));
                    if (navigationResult.isOk() && actionError) handleNavigationErrorFlow();
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    private void handleEnbiFlowChat(String message, String title) {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_FORCE_UPDATE)) {
            handleUpdateApp(message, title);
        } else if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_CONVO_CHATBOT)) {
            handleEnbiFlow();
        } else
            handleNormalChatFlow();
    }

    public void handleUpdateApp(String message, String title) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.APP_SOFT_UPDATE_AVAILABLE);
        navigationTarget
                .withParam(NavigationTarget.IS_DEVICE_ROOTED_PARAM, false)
                .withParam(NavigationTarget.IS_FROM_APP_SHORTCUT, false)
                .withParam(NavigationTarget.IS_SECOND_LOGIN, true)
                .withParam(NavigationTarget.UPDATE_APP_DESC, message)
                .withParam(NavigationTarget.UPDATE_APP_TITLE, title);
        mNavigationRouter.navigateTo(navigationTarget);

        mAnalytics.sendEvent(ChatTracking.AdobeEventName.EVENT_NAME_UPDATE_APP,
                StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }


    private void handleEnbiFlow() {
        if (isChatbotIntoJourneyCompleted())
            navigateToChatBotActivity(mConversationId);
        else
            navigateToChatBotIntroductionActivity(mConversationId);

    }


    private void handleNormalChatFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.APP_CHAT)) {
            navigateToChatActivity();
        } else {
            navigateToChatErrorActivity();
        }
    }

    public void navigateToChatErrorActivity() {
        mNavigationRouter.navigateTo(
                NavigationTarget.to(NavigationTarget.ERROR_SCREEN_ACTIVITY));
    }

    void navigateToChatBotActivity(String conversationId) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.CONVO_CHAT_SCREEN).withIntentSingleTop(true);
        navigationTarget.withParam(NavigationTarget.CONVERSATION_ID, conversationId);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void navigateToChatBotIntroductionActivity(String conversationId) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.CHATBOT_INTRODUCTION_SCREEN).withIntentSingleTop(true);
        navigationTarget.withParam(NavigationTarget.CONVERSATION_ID, conversationId);
        mNavigationRouter.navigateTo(navigationTarget);

    }

    public boolean isChatbotIntoJourneyCompleted() {
        return mApplicationStorage.getBoolean(ChatbotConstants.StorageKeys.CHATBOT_INTRO_DISPLAYED, false);
    }

    private void handleSendMoneyFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SEND_CASH_LANDING));
    }

    public void handleSetupAppointmentFlow() {
        mGetProfileUseCase.execute(false)
                .compose(bindToLifecycle())
                .doFinally(() -> {
                    showLoader(view != null, false);
                })
                .subscribe(userProfile -> {
                    if (userProfile != null) {
                        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NEW_APPOINTMENT)
                                .withParam(NavigationTarget.PARAM_SEC_OFFICER_CD, userProfile.getSecOfficerCd())
                                .withParam(NavigationTarget.PARAM_BOOKING_ENTRY_TYPE, BookingEntryType.GET_IN_TOUCH));
                    } else {
                        handleNavigationErrorFlow();
                    }
                }, error -> {
                    if (view != null) {
                        handleNavigationErrorFlow();
                    }
                });

    }

    public void handleKidsUpdatedTncFlow(List<FBNotificationsViewModel.AdditionalParameter> additionalParameters) {
        KidsAckTncRequestDataModel reqEntity = new KidsAckTncRequestDataModel();
        List<KidsTncDataModel> notices = new ArrayList<>();
        for (FBNotificationsViewModel.AdditionalParameter parameter : additionalParameters) {
            if (NotificationConstants.ADDITIONAL_PARAM.MINOR_ENT_NUM.equalsIgnoreCase(parameter.getName())) {
                reqEntity.setMinorEnterpriseNumber(parameter.getValue());
            } else {
                KidsTncDataModel kidsTncAckDataModel = new KidsTncDataModel();
                kidsTncAckDataModel.setNoticeType(parameter.getValue());
                kidsTncAckDataModel.setNoticeName(parameter.getName());
                notices.add(kidsTncAckDataModel);
            }
        }
        reqEntity.setNotices(notices);
        mNavigationRouter.navigateTo(NavigationTarget.to(KIDS_ACK_TERMS_AND_CONDITION_SCREEN).withParam(KIDS_ACK_TERMS_AND_CONDITION_PARAM, reqEntity));
    }

    public void handleKidsFraudAwarenessFlow(List<FBNotificationsViewModel.AdditionalParameter> additionalParameters) {
        KidsAckNoticeRequestDataModel reqEntity = new KidsAckNoticeRequestDataModel();
        List<KidsNoticeDataModel> notices = new ArrayList<>();
        for (FBNotificationsViewModel.AdditionalParameter parameter : additionalParameters) {
            if (NotificationConstants.ADDITIONAL_PARAM.MINOR_ENT_NUM.equalsIgnoreCase(parameter.getName())) {
                reqEntity.setMinorEnterpriseNumber(parameter.getValue());
            } else {
                KidsNoticeDataModel kidsTncAckDataModel = new KidsNoticeDataModel();
                kidsTncAckDataModel.setNoticeVersion(parameter.getValue());
                kidsTncAckDataModel.setNoticeType(parameter.getName());
                notices.add(kidsTncAckDataModel);
            }
        }
        reqEntity.setNotices(notices);
        mNavigationRouter.navigateTo(NavigationTarget.to(PARENT_ACK_FRAUD_AWARENESS_SCREEN).withParam(KIDS_ACK_FRAUD_AWARENESS_PARAM, reqEntity));
    }

    private void handleOffersForYouClick() {
        NavigationTarget navigationTarget = NavigationTarget.to(LoansNavigatorTarget.LOANS_BORROW_SCREEN);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    private void handleAppointmentFlow(FBNotificationsViewModel.ResponseOption selectedResponse) {
        String refNo = null;
        String buName = null;
        for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
            if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.REF_NO)) {
                refNo = parameter.getValue();
            }
            if (parameter.getName().equalsIgnoreCase(NotificationConstants.ADDITIONAL_PARAM.BU_NAME)) {
                buName = parameter.getValue();
            }
        }
        if (StringUtils.isNotEmpty(refNo) && StringUtils.isNotEmpty(buName)) {
            mNavigationRouter.navigateTo(NavigationTarget.to(BookingNavigatorTarget.APPOINTMENT_DETAILS)
                    .withParam(PARAM_APPOINTMENT_REF_NO, refNo)
                    .withParam(NavigationTarget.PARAM_BUSINESS_UNIT_TYPE, buName));
        } else {
            handleNavigationErrorFlow();
        }
    }

    private void getSelectLottoGameFlowFromToggleFlag(String birthDateParam) {
        boolean isVasDailyLottoEnabled = !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_LOTTERY);
        if (isVasDailyLottoEnabled) {
            getVasOfferings(birthDateParam, VasConstants.VasOfferingsCodes.NEW_LOTTERY_GAMES);
        } else {
            handleBuyLottoFlow(birthDateParam);
        }
    }

    private void handleViewStatementsFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_MOBSTAT_STATEMENTS)) {
            HashMap<String, Object> cdata = new HashMap<>();
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, TrackingEvent.ANALYTICS.VAL_GET_STATEMENTS);
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_DOCUMENTTYPE, TrackingEvent.ANALYTICS.VAL_STATEMENTS);
            mAnalytics.sendEventActionWithMap(TrackingEvent.SD_STATEMENTS, cdata);

            mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.DOWNLOAD_STATEMENT_ACTIVITY).withParam(ServicesNavigationTarget.PARAM_NOTIFICATION_ACCOUNT_NUMBER, mAccountId));
        } else {
            handleNavigationErrorFlow();
        }
    }

    void onClickProductLayout(InsuranceProductType insuranceProduct, List<AccountViewModel> accountList, int productId, double allowedCoverAmount, double existingCoverAmount) {
        NavigationTarget navigationTarget = null;
        if (insuranceProduct == InsuranceProductType.MY_COVER_LIFE) {
            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_NEW_EDUCATION_ACTIVITY);
        } else {
            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_EDUCATION_SCREEN);
        }
        navigationTarget.withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, insuranceProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, productId)
                .withParam(InsuranceConstants.ParamKeys.PARAM_ALLOWED_COVER_AMOUNT, allowedCoverAmount)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EXISTING_COVER_AMOUNT, existingCoverAmount)
                .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, accountList)
                .withParam(InsuranceConstants.BundleKeys.NON_NEDBANK_INSURANCE_PROPERTIES, mPropertiesViewModels);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void onClickMyCoverLayout(InsuranceProductType insuranceProduct, List<AccountViewModel> accountList,
                              int productId, double allowedCoverAmount, double existingCoverAmount) {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_EDUCATION_SCREEN)
                .withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, insuranceProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, productId)
                .withParam(InsuranceConstants.ParamKeys.PARAM_ALLOWED_COVER_AMOUNT, allowedCoverAmount)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EXISTING_COVER_AMOUNT, existingCoverAmount)
                .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, accountList)
                .withParam(InsuranceConstants.BundleKeys.NON_NEDBANK_INSURANCE_PROPERTIES, mPropertiesViewModels);
        mNavigationRouter.navigateTo(navigationTarget);
    }


    void handleGetCashFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(AtmNavigatorTarget.ATM_TUTORIAL_SCREEN));
    }

    private void handleRecentPaymentsFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_RECENT_PAYMENT)) {
            getRecentPayments();
        } else {
            handleNavigationErrorFlow();
        }

    }

    public void getRecentPayments() {
        mRecentPaymentUseCase.execute(za.co.nedbank.core.payment.recent.Constants.ONE, za.co.nedbank.core.payment.recent.Constants.SAVED)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.showProgressVisible(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.showProgressVisible(false);
                })
                .subscribe(this::processRecentPaymentResponse
                        , throwable -> handleNavigationErrorFlow());

    }

    public void getOnceOffPayments() {
        mRecentPaymentUseCase.execute(za.co.nedbank.core.payment.recent.Constants.ONE, za.co.nedbank.core.payment.recent.Constants.ONCE_OFF)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.showProgressVisible(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.showProgressVisible(false);
                })
                .subscribe(onceOffPaymentResponseData -> {
                            if (onceOffPaymentResponseData != null)
                                mOnceOffPaymentsViewModel = mRecentPaymentResponseDataToViewModelMapper.mapData(onceOffPaymentResponseData);
                            handleViewMorePaymentClick();
                        }
                        , throwable -> handleViewMorePaymentClick());

    }

    private void processRecentPaymentResponse(RecentPaymentResponseData recentPaymentResponseData) {
        if (recentPaymentResponseData != null) {
            mRecipientPaymentViewModel = mRecentPaymentResponseDataToViewModelMapper.mapData(recentPaymentResponseData);
            if (mRecipientPaymentViewModel != null && mRecipientPaymentViewModel.getData() != null && !mRecipientPaymentViewModel.getData().isEmpty()) {
                getOnceOffPayments();
            } else {
                handleNavigationErrorFlow();
            }
        } else {
            handleNavigationErrorFlow();
        }
    }

    public void handleViewMorePaymentClick() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RECENT_PAYMENT_HISTORY);

        if (mRecipientPaymentViewModel != null && CollectionUtils.isNotEmpty(mRecipientPaymentViewModel.getData()))
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.RECIPIENT_PAYMENT_DATA, mRecipientPaymentViewModel.getData());
        if (mOnceOffPaymentsViewModel != null && mOnceOffPaymentsViewModel.getMetaDataViewModel() != null)
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.RECIPIENT_DATA_PAGE_LIMIT, mOnceOffPaymentsViewModel.getMetaDataViewModel().getPageLimit());
        if (mOnceOffPaymentsViewModel != null && CollectionUtils.isNotEmpty(mOnceOffPaymentsViewModel.getData())) {
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.ONCEOFF_PAYMENT_DATA, mOnceOffPaymentsViewModel.getData());
        }
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.CURRENT_PAGE, 0);
        if (mOnceOffPaymentsViewModel != null && mOnceOffPaymentsViewModel.getMetaDataViewModel() != null)
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.ONCEOFF_DATA_PAGE_LIMIT, mOnceOffPaymentsViewModel.getMetaDataViewModel().getPageLimit());

        mNavigationRouter.navigateTo(navigationTarget);

    }

    private void handleProfileDetailsFlow() {
        if (isMDMDisable()) {
            getProfileDetails();
        } else {
            getMdmProfileDetails();
        }
    }

    public boolean isMDMDisable() {
        return mFeatureSetController.isFeatureDisabled(FeatureConstants.MDMPROFILEDETAILS);
    }

    void getMdmProfileDetails() {
        mGetMdmProfileUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.showProgressVisible(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.showProgressVisible(false);
                })
                .subscribe(profile -> {
                    if (view != null) {
                        String rsaIdOrPassport;
                        if (!StringUtils.isNullOrEmpty(profile.getResident()) && profile.getResident().equalsIgnoreCase(FormattingUtil.SOUTH_AFRICA_CODE)) {
                            rsaIdOrPassport = profile.getRsaId();
                        } else {
                            rsaIdOrPassport = profile.getPassportNumber();
                        }
                        getFederationList(profile, rsaIdOrPassport);
                    }
                }, this::handleError);

    }

    void getProfileDetails() {
        mGetProfileUseCase.execute(false)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null)
                        view.showProgressVisible(true);
                })
                .doOnTerminate(() -> {
                    if (view != null)
                        view.showProgressVisible(false);
                })
                .subscribe(profile -> {
                    if (view != null) {
                        String rsaIdOrPassport;
                        if (!StringUtils.isNullOrEmpty(profile.getResident()) && profile.getResident().equalsIgnoreCase(FormattingUtil.SOUTH_AFRICA_CODE)) {
                            rsaIdOrPassport = profile.getRsaId();
                        } else {
                            rsaIdOrPassport = profile.getPassportNumber();
                        }
                        getFederationList(profile, rsaIdOrPassport);
                    }
                }, this::handleError);

    }

    private void getFederationList(UserProfile profile, String rsaIdOrPassport) {
        mGetFedarationListUseCase.execute().subscribe(fedarationList -> {
            ArrayList<SwitchContextFedarationDetailsViewModel> fedarationViewModels =
                    (ArrayList<SwitchContextFedarationDetailsViewModel>) mSwitchContextDataViewModelMapper
                            .mapFedarationDetailResponse(fedarationList);
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NEW_PROFILE_DETAILS)
                    .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.USER_PROFILE, profile)
                    .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_DEFAULT_PROFILE, fedarationViewModels.get(0).isDefaultFederation())
                    .withParam(ProfileNavigationTarget.PARAM_PROFILE_MODEL, fedarationViewModels.get(0))
                    .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_NON_TP_USER, isNonTpUser())
                    .withParam(za.co.nedbank.core.Constants.RSA_ID_OR_PASSPORT, rsaIdOrPassport)
                    .withAllData(Boolean.TRUE));

        }, this::handleError);

    }


    private void handleError(Throwable throwable) {
        handleNavigationErrorFlow();
    }

    public void handleAtmBranchLocatorFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ATM_AND_BRANCHES_PROFILE).withParam(NavigationTarget.PARAM_IS_ATM_VISIBLE, true));
    }

    public void handleFamilyBankingFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_INVITE_AND_ACCEPT_FAMILY_BANKING));
    }

    public void handleSmartMoneyDashboardFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NFW_DASHBOARD_ACTIVITY));
    }

    public void handleCreditScoreFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CREDIT_HEALTH_FLOW));
    }

    private void handleJoinFamilyBanking() {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.FAMILY_BANKING_STATUS_LOADING_SCREEN);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    private void handleApplyPersonalLoanFlow() {
        NavigationTarget navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.PERSONAL_LOAN_WISHLIST_ACTIVITY);
        navigationTarget.withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.IS_NON_NEDBANK_ID_FLOW, false);
        navigationTarget.withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.IS_PRE_LOGIN, false);
        navigationTarget.withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.REDIRECT_FROM_SCREEN_NAME, StringUtils.EMPTY_STRING);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    private void navigateToRedirectUrlScreen(String redirectUrl) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_REDIRECT_URL);
        navigationTarget = navigationTarget.withParam(NotificationConstants.EXTRA.URL, redirectUrl);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    private void handleNonTPProductsFlow() {
        getUserInfoForSavingPocket(view.getClientDeviceInfoRequestViewModel());
    }

    void getUserInfoForSavingPocket(ClientDeviceInfoRequestViewModel
                                            clientDeviceInfoRequestViewModel) {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .doOnTerminate(() -> {
                    showLoader(view != null, false);
                })
                .subscribe(data -> navigateToProductList(),
                        throwable -> {
                            if (view != null) {
                                handleNavigationErrorFlow();
                            }
                        });
    }

    void navigateToProductList() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_MOA_IN_APP_ENABLED)) {
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MOA_PRODUCT_LIST));
        }
    }

    private void handleNotificationPrefsFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.NOTIFICATIONS_PREFERENCES)) {
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES));
        } else {
            handleNavigationErrorFlow();
        }
    }

    private void handleITASettingsFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ITA)) {
            mITADeviceManagementUseCase.execute()
                    .compose(bindToLifecycle()).subscribe(o -> {
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        } else {
            handleNavigationErrorFlow();
        }
    }


    void handleBuyLottoFlow(String birthDateParam) {
        if (!mFeatureSetController.isFeatureDisabled(TRANSACT_LOTTO_PWB)) {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.VAS_FEATURE_UNAVAILABLE);
            navigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDateParam);
            navigationTarget.withParam(LottoConstants.EXTRAS.FLOW_TYPE, LottoConstants.LottoFlowType.NOTIFICATION);
            navigationTarget.withParam(LottoConstants.EXTRAS.GAME_TYPE, LottoConstants.ConstantKey.DIRECT_GAME);
            mNavigationRouter.navigateTo(navigationTarget);
        }
    }

    private void handleOpenBiometricSettingsFlow() {
        mLoginSecurityUseCase.execute(Boolean.FALSE)
                .compose(bindToLifecycle()).subscribe(o -> {
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void handleMaintainScheduledPaymentFlow(String transactionId) {
        mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.SCHEDULED_PAYMENTS_DETAILS)
                .withParam(ServicesNavigationTarget.PARAM_PAYMENT_ID, Integer.parseInt(transactionId))
                .withParam(ServicesNavigationTarget.PARAM_PAYMENT_TYPE, ScheduledPayment.Type.PAYMENT));
    }

    public void handleBuyPPEFlow(String birthDateParam) {
        if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS)) {
            handleNavigationVasToggleOffFlow(true, birthDateParam);
        } else if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_ELECTRICITY)) {
            handleNavigationVasToggleOffFlow(false, birthDateParam);
        } else {
            getVasOfferings(birthDateParam, VasConstants.VasOfferingsCodes.ELECTRICITY);
        }
    }

    private void handleBuyVoucherFlow(String birthDateParam) {
        if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS)) {
            handleNavigationVasToggleOffFlow(true, birthDateParam);
        } else if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_VOUCHER)) {
            handleNavigationVasToggleOffFlow(false, birthDateParam);
        } else {
            getVasOfferings(birthDateParam, VasConstants.VasOfferingsCodes.VOUCHER);
        }
    }

    public void handleBuyPrepaids(String birthDateParam) {
        if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS)) {
            handleNavigationVasToggleOffFlow(true, birthDateParam);
        } else if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_PREPAID)) {
            handleNavigationVasToggleOffFlow(false, birthDateParam);
        } else {
            getVasOfferings(birthDateParam, VasConstants.VasOfferingsCodes.PREPAID);
        }
    }


    public void getVasOfferings(String birthDateParam, String vasProductType) {
        mGetVasOfferingsUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .map(apiDataResponse -> mTagItemDataToViewModelMapper.mapTagItemDataListToViewModelList(apiDataResponse.getData()))
                .subscribe(vasOfferingsList -> {
                    if (null != view) {
                        if (CollectionUtils.isNotEmpty(vasOfferingsList)) {
                            handleVasOfferingsSuccessResponse(vasOfferingsList, birthDateParam, vasProductType);
                        } else {
                            view.showProgressVisible(false);
                            handleNavigationErrorFlow();
                        }
                    }
                }, throwable -> {
                    if (null != view) {
                        view.showProgressVisible(false);
                        handleNavigationErrorFlow();
                    }
                });
    }

    private void handleVasOfferingsSuccessResponse(List<TagItemViewModel> vasOfferingsList, String birthDateParam, String vasProductType) {
        TagItemViewModel tagItemViewModel = getVoucherViewModel(vasOfferingsList, vasProductType);
        if (tagItemViewModel != null) {
            handleRespectedVasFlow(tagItemViewModel.getId(), birthDateParam, vasProductType);
            if (!VasConstants.VasOfferingsCodes.ELECTRICITY.equalsIgnoreCase(vasProductType)) {
                view.showProgressVisible(false);
            }
        } else {
            handleNavigationErrorFlow();
            view.showProgressVisible(false);
        }
    }

    public void getCategoriesList(Integer tagId) {
        Observable<TagData> observableCategories;
        observableCategories = mGetTagListUseCase.execute(mTagIdentifierRequestViewModelToDataMapper.mapTagIdentifierRequestViewModelToData(new TagIdentifierRequestViewModel(tagId)));

        observableCategories.compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .map(mTagDataToViewModelMapper::mapTagDataToViewModel)
                .subscribe(categoriesViewModel -> {
                    if (null != view) {
                        if (categoriesViewModel == null || CollectionUtils.isEmpty(categoriesViewModel.getTagRelationships())) {
                            handleNavigationErrorFlow();
                            view.showProgressVisible(false);
                            return;
                        }
                        if (memoryApplicationStorage.getBoolean(StorageKeys.IS_PPW_FLOW, false))
                            handlePPWNavigation(categoriesViewModel);
                        else
                            handlePELNavigation(categoriesViewModel.getTagRelationships());
                        view.showProgressVisible(false);

                    }
                }, throwable -> {
                    NBLogger.e(TAG, throwable.getMessage());
                    handleNavigationErrorFlow();
                    view.showProgressVisible(false);
                });
    }

    public void handlePELNavigation(List<TagViewModel> categories) {
        TagViewModel pelTagViewModel = getPELTagViewModel(categories);

        if (pelTagViewModel == null || pelTagViewModel.getTag() == null) {
            handleNavigationErrorFlow();
            return;
        }

        ElectricityViewModel mElectricityViewModel = new ElectricityViewModel();
        TagIdentifierRequestViewModel tagIdentifierRequestViewModel = new TagIdentifierRequestViewModel();
        tagIdentifierRequestViewModel.addIdToRequestList(pelTagViewModel.getTag().getId());
        mElectricityViewModel.setTagIdentifierRequestViewModel(tagIdentifierRequestViewModel);
        if (CollectionUtils.isNotEmpty(pelTagViewModel.getTag().getProducts())) {
            mElectricityViewModel.setProductList(pelTagViewModel.getTag().getProducts());
            ProductViewModel productViewModel = getPELProductViewModel(pelTagViewModel.getTag().getProducts());
            if (productViewModel != null) {
                mElectricityViewModel.setElectricityFlow(true);
                mElectricityViewModel.setFbe(false);
                mElectricityViewModel.setFbw(false);
                mElectricityViewModel.setProductViewModel(productViewModel);

                mNavigationRouter.navigateTo(NavigationTarget.to(ElectricityNavigatorTarget.ELECTRICITY_METER_DETAIL)
                        .withParam(ElectricityConstants.EXTRAS.ELECTRICITY_VIEW_MODEL, mElectricityViewModel));
            } else {
                handleNavigationErrorFlow();
            }
        } else {
            handleNavigationErrorFlow();
        }
    }

    void resetLimitsAndAccounts() {
        AvailableLimitHolder.resetVasLimits();
        VasAccountsHolder.resetVasAccountsList();
    }

    private void handleRespectedVasFlow(Integer id, String birthDateParam, String vasProductType) {
        if (VasConstants.VasOfferingsCodes.VOUCHER.equalsIgnoreCase(vasProductType)) {
            handleVouchersItemClick(id, birthDateParam);
        } else if (VasConstants.VasOfferingsCodes.PREPAID.equalsIgnoreCase(vasProductType)) {
            handleBuyPrepaidItemClick(id);
        } else if (VasConstants.VasOfferingsCodes.NEW_LOTTERY_GAMES.equalsIgnoreCase(vasProductType)) {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.VAS_FEATURE_UNAVAILABLE);
            navigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDateParam);
            navigationTarget.withParam(LottoConstants.EXTRAS.TAG_ID, id);
            navigationTarget.withParam(LottoConstants.EXTRAS.FLOW_TYPE, LottoConstants.LottoFlowType.NOTIFICATION);
            navigationTarget.withParam(LottoConstants.EXTRAS.GAME_TYPE, LottoConstants.ConstantKey.T_C_GAME);
            mNavigationRouter.navigateTo(navigationTarget);
        } else if (VasConstants.VasOfferingsCodes.ELECTRICITY.equalsIgnoreCase(vasProductType)) {
            getCategoriesList(id);
        }
    }

    private void handlePPWNavigation(TagViewModel categories) {
        memoryApplicationStorage.putBoolean(StorageKeys.IS_PPW_FLOW, false);

        TagViewModel tagViewModel = getWaterTagViewModel(categories.getTagRelationships());
        ElectricityViewModel electricityViewModel = new ElectricityViewModel();
        if (tagViewModel == null || tagViewModel.getTag() == null) {
            handleNavigationErrorFlow();
            return;
        }

        electricityViewModel.setTagIdentifierRequestViewModel(new TagIdentifierRequestViewModel(tagViewModel.getTag().getId()));
        if (CollectionUtils.isNotEmpty(tagViewModel.getTag().getProducts())) {
            ProductViewModel ppwProductModel = getPPWProductViewModel(tagViewModel.getTag().getProducts());
            if (ppwProductModel != null) {
                electricityViewModel.setElectricityFlow(false);
                electricityViewModel.setFbe(false);
                electricityViewModel.setProductViewModel(ppwProductModel);
                electricityViewModel.setFbw(false);
                electricityViewModel.setProductList(tagViewModel.getTag().getProducts());

                mNavigationRouter.navigateTo(NavigationTarget.to(ElectricityNavigatorTarget.ELECTRICITY_METER_DETAIL)
                        .withParam(ElectricityConstants.EXTRAS.ELECTRICITY_VIEW_MODEL, electricityViewModel));
            } else {
                handleNavigationErrorFlow();
            }
        } else {
            handleNavigationErrorFlow();
        }
    }

    private ProductViewModel getPPWProductViewModel(List<ProductViewModel> productList) {
        for (ProductViewModel productViewModel : productList) {
            if (ElectricityConstants.ProductCode.WATER_FLOW.equalsIgnoreCase(productViewModel.getVendorProductCode())) {
                return productViewModel;
            }
        }
        return null;
    }

    private TagViewModel getWaterTagViewModel(List<TagViewModel> tagViewModelList) {
        for (TagViewModel tagViewModel : tagViewModelList) {
            if (ElectricityConstants.ProductCode.WATER_FLOW.equalsIgnoreCase(tagViewModel.getTag().getCode())) {
                return tagViewModel;
            }
        }
        return null;
    }

    private TagItemViewModel getVoucherViewModel(List<TagItemViewModel> vasOfferingsList, String vasProductType) {
        for (TagItemViewModel tagItemViewModel : vasOfferingsList) {
            if (vasProductType.equalsIgnoreCase(tagItemViewModel.getCode())) {
                return tagItemViewModel;
            }
        }
        return null;

    }

    private TagViewModel getPELTagViewModel(List<TagViewModel> categoryList) {
        for (TagViewModel tagViewModel : categoryList) {
            if (tagViewModel.getTag() != null && ElectricityUtility.isElectrictyFlow(tagViewModel.getTag().getCode())) {
                return tagViewModel;
            }
        }
        return null;

    }

    private ProductViewModel getPELProductViewModel(List<ProductViewModel> productList) {
        for (ProductViewModel productViewModel : productList) {
            if (productViewModel.getVendorProductCode() != null && ElectricityUtility.isPrepaidElectricity(productViewModel.getVendorProductCode())) {
                return productViewModel;
            }
        }
        return null;

    }

    public void handleBuyPrepaidItemClick(Integer id) {
        NavigationTarget navigationTarget = NavigationTarget.to(PrepaidNavigatorTarget.PREPAID_DASHBOARD);
        PrepaidPurchaseViewModel prepaidPurchaseViewModel = new PrepaidPurchaseViewModel();
        prepaidPurchaseViewModel.setTagIdentifierRequestViewModel(new TagIdentifierRequestViewModel(id));
        navigationTarget.withParam(PrepaidConstants.EXTRAS.PURCHASE_PREPAID_MODEL, prepaidPurchaseViewModel);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    public void handleVouchersItemClick(Integer id, String birthDateParam) {
        VoucherPurchaseViewModel voucherPurchaseViewModel = new VoucherPurchaseViewModel();
        voucherPurchaseViewModel.setTagIdentifierRequestViewModel(new TagIdentifierRequestViewModel());
        voucherPurchaseViewModel.getTagIdentifierRequestViewModel().addIdToRequestList(id);
        NavigationTarget navigationTarget = null;
        if (!mApplicationStorage.getBoolean(VoucherConstants.StorageKeys.VOUCHER_INTRO_DISPLAYED, false)) {
            navigationTarget = NavigationTarget.to(VouchersNavigatorTarget.VOUCHER_INTRODUCTION);
        } else {
            navigationTarget = NavigationTarget.to(VouchersNavigatorTarget.VOUCHER_DASHBOARD);
        }
        navigationTarget.withParam(VoucherConstants.VOUCHER_PURCHASE_VIEW_MODEL, voucherPurchaseViewModel);
        navigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDateParam);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    private void handleUpdateNedIdDetails() {
        mLoginSecurityUseCase.execute(Boolean.TRUE)
                .compose(bindToLifecycle()).subscribe(o -> {
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    boolean isNonTpUser() {
        String clientType = mApplicationStorage.getString(StorageKeys.CLIENT_TYPE, ClientType.TP.toString());
        return ClientType.getEnum(clientType) == ClientType.NON_TP;
    }

    public void getBankBeneficiaryListandInitiateFlow(String beneficiaryId, String beneficiaryName) {
        showLoader(view != null, true);
        mGetBankBeneficiaryUseCase.execute(getBeneficiaryData())
                .compose(bindToLifecycle())
                .subscribe(bankBeneficiaryList -> {
                            if (view != null) {
                                view.showProgressVisible(false);
                                BankBeneficiaryData bankBeneficiaryData = getSelectedBeneficiaryData(bankBeneficiaryList, beneficiaryId, beneficiaryName);
                                if (bankBeneficiaryData != null) {
                                    navigateToPayWithSelectedAccount(mUserBeneficiaryMapper.mapUserBeneficiaryDetailsToViewModel(
                                            mUserBeneficiaryMapper.mapBankBeneficiaryDetailsToUserBeneficiaryDetails(bankBeneficiaryData)));
                                } else {
                                    handleNavigationErrorFlow();
                                }
                            }
                        },
                        throwable -> {
                            if (view != null) {
                                view.showProgressVisible(false);
                                handleNavigationErrorFlow();
                            }
                        });
    }

    private void navigateToPayWithSelectedAccount(UserBeneficiaryDetailsViewModel userBeneficiaryDetailsToViewModel) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.PAY).withParam(CrossBorderNavigationTarget.PARAM_SET_PAYMENT_TYPE, PayMode.ACCOUNT);
        navigationTarget.withParam(NotificationConstants.EXTRA.BANK_APPROVED_ACCOUNT, userBeneficiaryDetailsToViewModel);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    private BankBeneficiaryData getSelectedBeneficiaryData(Map<String, List<BankBeneficiaryData>> bankBeneficiaryList, String beneficiaryId, String beneficiaryName) {
        Iterator it = bankBeneficiaryList.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry pair = (Map.Entry) it.next();
            ArrayList<BankBeneficiaryData> list = (ArrayList<BankBeneficiaryData>) pair.getValue();
            if (beneficiaryId != null) {
                BankBeneficiaryData bankBeneficiaryData = getBankBeneficiaryData(beneficiaryId, list);
                if (bankBeneficiaryData != null) return bankBeneficiaryData;
            } else if (beneficiaryName != null) {
                BankBeneficiaryData bankBeneficiaryData = getBeneficiaryDataByName(beneficiaryName, list);
                if (bankBeneficiaryData != null) return bankBeneficiaryData;
            }
        }
        return null;
    }

    @Nullable
    private BankBeneficiaryData getBeneficiaryDataByName(String beneficiaryName, ArrayList<BankBeneficiaryData> list) {
        for (BankBeneficiaryData bankBeneficiaryData : list) {
            if (bankBeneficiaryData.getBankDefinedBeneficiaryName().replaceAll("\\s", "").equalsIgnoreCase(beneficiaryName.replaceAll("\\s", ""))) {
                return bankBeneficiaryData;
            }
        }
        return null;
    }

    @Nullable
    private BankBeneficiaryData getBankBeneficiaryData(String beneficiaryId, ArrayList<BankBeneficiaryData> list) {
        for (BankBeneficiaryData bankBeneficiaryData : list) {
            if (bankBeneficiaryData.getBankDefinedBeneficiaryId().equalsIgnoreCase(beneficiaryId)) {
                return bankBeneficiaryData;
            }
        }
        return null;
    }

    private GetBeneficiaryData getBeneficiaryData() {
        GetBeneficiaryData data = new GetBeneficiaryData();
        data.setBeneficiaryType(BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF);
        data.setClearCacheData(false);
        data.setSearchListViewType(za.co.nedbank.core.payment.recent.Constants.ISearchListViewType.VIEW_TYPE_PAY);
        data.setInputSearchParam("");
        return data;
    }

    void navigateToInvSwitchingEduPage(AccountDetailViewModel accountDetailViewModel) {
        if (view != null) {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_INVONLINE_INVESTMENT_SWITCHING);
            navigationTarget.withParam(NavigationTarget.PARAM_SWITCHING_INVESTMENT_VIEW_MODEL, getUpdatedSwitchInvViewModel());
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_MANAGE_ITEM_ACCOUNT_ID, mAccountId);
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_IO_INV_SWITCH_PENDING_NOTICE_NOW_DETAILS, getNowDetails(accountDetailViewModel));
            navigationTarget.withParam(NavigationTarget.PARAM_ONIA_BIRTH_DATE, mUserDetailData.getBirthDate());
            navigationTarget.withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, mUserDetailData.getCisNumber());
            navigationTarget.withParam(NavigationTarget.PARAM_ONIA_CLIENT_TYPE, mUserDetailData.getClientType());
            navigationTarget.withParam(NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD, mUserDetailData.getSecOfficerCd());
            mNavigationRouter.navigateTo(navigationTarget);
        }
    }

    public InvestmentSwitchingViewModel getUpdatedSwitchInvViewModel() {
        InvestmentSwitchingViewModel investmentSwitchingViewModel = new InvestmentSwitchingViewModel();
        investmentSwitchingViewModel.setInvestmentAccType(mAccountDetails.getInvestmentAccName());
        investmentSwitchingViewModel.setAccountNumber(mAccountDetails.getAccountNumber());
        investmentSwitchingViewModel.setCurrentBalance(mAccountDetails.getBalance().doubleValue());
        investmentSwitchingViewModel.setAvailableBalance(mAccountDetails.getAvailableBalance().doubleValue());
        investmentSwitchingViewModel.setNoticeProduct(mNoticeType);
        return investmentSwitchingViewModel;
    }

    public void openCovid19() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.COVID_19));
    }

    private void handleOpenKidsAccountFlow() {
        callProductAcquisitionForMinor();
    }


    void callProductAcquisitionForMinor() {

        Map<String, Object> cdata = new HashMap<>();
        cdata.put(EnrollV2TrackingEvent.ANALYTICS.KEY_PRODUCTS, EnrollV2TrackingEvent.ANALYTICS.VAL_BANK_NEDBANK_4ME);
        memoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, EnrollV2TrackingEvent.ANALYTICS.VAL_BANK_NEDBANK_4ME);
        mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.ANALYTICS.KEY_PRODUCT_SELECTED, cdata);

        memoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT, za.co.nedbank.enroll_v2.Constants.MINOR_PRODUCT_NAME);
        memoryApplicationStorage.putBoolean(StorageKeys.IS_PROFESSIONAL_PRODUCT, false);
        ProductRequestViewModel productRequestViewModel = new ProductRequestViewModel();
        productRequestViewModel.setMonthlySalary(0);
        productRequestViewModel.setDateofbirth(new SimpleDateFormat(za.co.nedbank.enroll_v2.Constants.DATE_FORMAT, Locale.getDefault()).format(new Date()));
        productRequestViewModel.setProductType(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.getAccountTypeCode());

        mProductUseCase.execute(mProductRequestViewModelToDataMapper.mapData(productRequestViewModel))
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(null != view, true);
                })
                .doOnTerminate(() -> {
                    showLoader(null != view, false);
                })
                .map(mProductResponseDataToViewModelMapper::mapData)
                .subscribe(productResponseViewModel -> {
                    if (productResponseViewModel.getMetadata().isSuccess()
                            && productResponseViewModel.getProductDataResponseDataViewModel() != null
                            && productResponseViewModel.getProductDataResponseDataViewModel().getProductDataViewModels() != null
                            && !productResponseViewModel.getProductDataResponseDataViewModel().getProductDataViewModels().isEmpty()) {
                        memoryApplicationStorage.putString(StorageKeys.FICA_SESSION_ID,
                                productResponseViewModel.getProductDataResponseDataViewModel().getSessionId());
                        memoryApplicationStorage.putInteger(StorageKeys.FICA_PRODUCT_FLOW,
                                FicaProductFlow.MINOR);
                        mNavigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.MOA_PRODUCT_DETAIL)
                                .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SELECTED_PRODUCT, productResponseViewModel
                                        .getProductDataResponseDataViewModel()
                                        .getProductDataViewModels()
                                        .get(0)));
                    } else {
                        handleException(productResponseViewModel.getMetadata().getThrowable());
                    }
                }, this::handleException);
    }

    private void handleException(Throwable throwable) {
        handleNavigationErrorFlow();
    }

    private void getPlasticIdandInitiateFlow(String cardNumber, String action) {
        mGetCardsUseCase.execute().compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .doOnTerminate(() -> {
                    showLoader(view != null, false);
                })
                .subscribe(cardDataModels -> {
                    if (cardDataModels != null && view != null) {
                        createCardInfoMap(cardDataModels);
                        mCardDataModel = getSelectedCardDataModel(cardDataModels, cardNumber);
                        mAccountNo = mCardDataModel.getAccountNumber();
                        List<CardViewModel> cardViewModelList = mCardDataModelToViewModelMapper.mapCards(cardDataModels);
                        CardViewModel cardViewModel = getSelectedCardViewModel(cardViewModelList, cardNumber);
                        switch (action) {
                            case NotificationConstants.NAVIGATION_TARGET.ACTIVATE_OVERSEAS_TRAVEL_CARD:
                                navigateToOTAScreen(cardViewModel);
                                break;
                            case NotificationConstants.NAVIGATION_TARGET.STATEMENT_DOWNLOAD:
                                navigateToDownloadStatement(za.co.nedbank.core.Constants.StatementType.CREDIT_CARD_STATEMENT);
                                break;
                            default:
                                handleNavigationErrorFlow();
                        }
                    } else {
                        handleNavigationErrorFlow();
                    }
                }, error -> {
                    if (view != null) {
                        handleNavigationErrorFlow();
                    }
                });
    }

    private void createCardInfoMap(List<CardDataModel> cardDataModels) {
        if (cardDataModels != null && !cardDataModels.isEmpty()) {
            for (CardDataModel cardDataModel : cardDataModels) {
                if (cardDataModel.getActionListDataModelList() != null &&
                        !cardDataModel.getActionListDataModelList().isEmpty()) {
                    CardInfo cardInfo = mCardInfoToDataMapper.map(cardDataModel.getActionListDataModelList());
                    mCardInfoMap.put(cardDataModel.getCardNumber(), cardInfo);
                }
            }
        }
    }

    private void showLoader(boolean isViewNotNull, boolean visibility) {
        if (isViewNotNull) {
            view.showProgressVisible(visibility);
        }
    }

    private void handleShareMoneyAppFlow() {
        showLoader(view != null, true);
        mGetProfileUseCase
                .execute(false)
                .compose(bindToLifecycle())
                .doFinally(() -> {
                    showLoader(view != null, false);
                })
                .subscribe(
                        userProfile -> {
                            if (userProfile != null) {
                                mUserProfile = userProfile;
                                getEmcertId();
                            } else {
                                handleNavigationErrorFlow();
                            }
                        },
                        error -> {
                            if (view != null) {
                                handleNavigationErrorFlow();
                            }
                        }
                );


    }


    private void getEmcertId() {
        mGetEmcertIdUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(emcertId -> {
                    if (view != null && emcertId != null) {
                        String userName = String.valueOf(TextUtils.concat(mUserProfile.getFirstName(), StringUtils.SPACE, mUserProfile.getLastName()));
                        navigateToShareMoneyApp(userName, emcertId);
                    }
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }


    private void navigateToShareMoneyApp(String userName, String emcertId) {
        mAnalytics.sendEvent(ProfileTracking.CLICK_SHARE_THE_APP, ProfileTracking.CLICK_SHARE_MONEY_APP, StringUtils.EMPTY_STRING);
        mNavigationRouter.navigateTo(NavigationTarget.to(ProfileNavigationTarget.SHARE_THE_MONEY_APP)
                .withParam(NavigationTarget.PARAM_NAME, userName)
                .withParam(ProfileConstants.EMCERT_ID, emcertId));
    }

    void sendAnalyticsEvents(FBNotificationsViewModel.ResponseOption selectedResponse) {
        if (selectedResponse != null) {
            String eventName = StringUtils.EMPTY_STRING;
            HashMap<String, Object> cdata = new HashMap<>();
            AdobeContextData adobeContextData = new AdobeContextData(cdata);
            if (NotificationConstants.LABEL_CONTINUE.equalsIgnoreCase(selectedResponse.getLabel())) {
                eventName = NotificationConstants.TRACKING_PARAMS.NOTIFICATION_DETAILS_PROCEED;
                adobeContextData.setClickThroughs();
            } else if (NotificationConstants.LABEL_NO_THANKS.equalsIgnoreCase(selectedResponse.getLabel())) {
                eventName = NotificationConstants.TRACKING_PARAMS.NOTIFICATION_DETAILS_NO_THANKS;
                adobeContextData.setDismisses();
            }
            mAnalytics.sendEventActionWithMap(eventName, cdata);
        }
    }

    private void getAccountIdandInitiateFlow(String accountNo, String action) {
        this.mAccountNo = accountNo;
        mGetOverviewUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .doOnTerminate(() -> {
                    showLoader(view != null, false);
                })
                .subscribe(overviewCachableValue -> {
                    Overview overviewValue = overviewCachableValue.get().clone();
                    setAccountDetails(overviewValue, accountNo);
                    switch (action) {
                        case NotificationConstants.NAVIGATION_TARGET.STATEMENT_DELIVERY_PREFERENCE:
                            handleStatementPreferenceFlow();
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.ACCOUNT_DOCUMENTS_STATEMENTS:
                            handleSalarySwitchShareAccDetailsFlow(action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.REINVESTMENT:
                            handleReinvestmentFlow(action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.INVESTMENT_SWITCHING:
                            handleReinvestmentFlow(action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.PAYOUT:
                            handleAccountDetailsandNextFlow(action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.RECEIVED_SSL:
                            handleSalarySwitchShareAccDetailsFlow(action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_SWITCHING:
                            handleDebitOrderSwitchingFlow();
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.BUY_FOREX:
                            handleBuyForexFlow();
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.SELL_FOREX:
                            handleSellForexFlow();
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.APPLY_FUNERAL_POLICY:
                            handleApplyMyLifeOrFuneralFlow(overviewValue, InsuranceConstants.INSURANCE_PRODUCT_ID);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.APPLY_PERSONAL_ACCIDENT_COVER:
                            handleApplyInsureFlow(overviewValue, action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.APPLY_HOME_OWNERS_COVER:
                            handleApplyInsureFlow(overviewValue, action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER:
                            handleApplyInsureFlow(overviewValue, action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.STATEMENT_DOWNLOAD:
                            handleAccountDetailsandNextFlow(action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.SUBMIT_NOTICE_WITHDRAWAL:
                            handleAccountDetailsandNextFlow(action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.EDIT_NOTICES_OF_WITHDRAWAL:
                            handleAccountDetailsandNextFlow(action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.MANAGE_INVESTMENT_ACCOUNT:
                            handleAccountDetailsandNextFlow(action);
                            break;
                        case NotificationConstants.NAVIGATION_TARGET.VIEW_STATEMENTS:
                            handleViewStatementsFlow();
                            break;
                        default:
                            handleNavigationErrorFlow();
                    }

                }, throwable -> {
                    NBLogger.e(TAG, throwable.getMessage());
                    handleNavigationErrorFlow();
                });
    }

    public void getNoticesList() {
        if (view != null) {
            getListOfNoticesUsecase.execute(mAccountId)
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> view.showProgressVisible(true))
                    .doOnTerminate(() -> view.showProgressVisible(false))
                    .subscribe(noticesResponseDataModel -> createNoticeModelandNavigate(view.filterNoticesDataViewModel(noticesResponseDataModelToViewModelMapper.mapDataModelToViewModel(noticesResponseDataModel), mNoticeId)), throwable -> {
                        if (view != null) {
                            handleNavigationErrorFlow();
                        }
                    });
        }
    }


    private void createNoticeModelandNavigate(NoticesDataViewModel noticesDataViewModel) {
        CreateNoticeViewModel createNoticeViewModel = getNowDetails(mAccountDetailViewModel);
        if (noticesDataViewModel != null) {

            if (noticesDataViewModel.getCapitalDisposalAccountViewModel() != null) {
                createNoticeViewModel.setCapitalDisposalAccountNumber(noticesDataViewModel.getCapitalDisposalAccountViewModel().getAccountNumber());
                createNoticeViewModel.setCapitalDisposalAccountType(noticesDataViewModel.getCapitalDisposalAccountViewModel().getAccountType());
                createNoticeViewModel.setCapitalDisposalAccountName(noticesDataViewModel.getCapitalDisposalAccountViewModel().getAccountName());
            }
            createNoticeViewModel.setAmountTobePaid(String.valueOf(noticesDataViewModel.getNoticeAmount()));
            createNoticeViewModel.setSortCode(Long.parseLong(noticesDataViewModel.getSortCode()));
            createNoticeViewModel.setWithdrawalDate(String.valueOf(noticesDataViewModel.getNoticeDate()));
            createNoticeViewModel.setBeneficiaryIndicator(noticesDataViewModel.getBeneficiaryIndicator());
            createNoticeViewModel.setNoticeSubmitDate(noticesDataViewModel.getSubmissionDate());
            createNoticeViewModel.setMinWithdrawalAmount(minWithdrawalAmount);
            createNoticeViewModel.setBalance(String.valueOf(mAccountDetailViewModel.getAvailableBalance()));
            createNoticeViewModel.setFirstWithdrawalDate(mFirstWithdrawalDate);
            if (createNoticeViewModel.getBeneficiaryIndicator() != null && createNoticeViewModel.getBeneficiaryIndicator().equalsIgnoreCase(za.co.nedbank.services.Constants.NOW_PLACE_NOTICE_YES)) {
                createNoticeViewModel.setPayoutSelection(za.co.nedbank.core.Constants.INVONLINE_PAYOUT_RECIPIENT_SELECTED);
            } else {
                createNoticeViewModel.setPayoutSelection(za.co.nedbank.core.Constants.INVONLINE_PAYOUT_ACCOUNT_SELECTED);
            }
            createNoticeViewModel.setNoticeID(noticesDataViewModel.getNoticeID());
        }
        navigateToEditNotice(createNoticeViewModel);
    }

    void getProductClaimList() {
        mGetProductClaimUseCase.execute(getRSAId())
                .compose(bindToLifecycle()).doOnSubscribe(disposable -> view.showProgressVisible(true))
                .doOnTerminate(() -> view.showProgressVisible(false)).
                subscribe(productClaimData -> setProductClaimResponseData(productClaimData), throwable -> handleNavigationErrorFlow());
    }

    private void setProductClaimResponseData(FuneralDependentResponseData productClaimData) {
        mProductClaimList = mGetProductClaimDataToViewModelMapper.map(productClaimData);
        onSubmitNIFPClaimClick();
    }

    void onSubmitNIFPClaimClick() {
        InsuranceProductType insuranceProduct = InsuranceProductType.FUNERAL_CLAIM;
        mAnalytics.sendEvent(InsuranceClaimTrackingEvent.EVENT_SUBMIT_CLAIM_NIFP_JOURNEY, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_OLD_EDUCATION_SCREEN)
                .withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, insuranceProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, InsuranceConstants.INSURANCE_PRODUCT_ID)
                .withParam(InsuranceConstants.ParamKeys.PARAM_DEPENDANT_DETAILS, mProductClaimList)
                .withParam(InsuranceConstants.INSURANCE_POLICY_NUMBER, mRetrievePolicyNumber)
                .withParam(InsuranceConstants.POLICY_ISSUED_DATE, mPolicyIssuedDate);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    private void handleApplyInsureFlow(Overview overview, String action) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                getInsuranceAccountList(mInsuranceAccountList, accountsOverview, action, overview);
            }

        } else {
            handleNavigationErrorFlow();
        }

    }


    void getAllowedCoverAmount() {
        String partyId = getRSAId();
        mGetAllowedCoverAmountUseCase.execute(ACCIDENTAL_INSURANCE_PRODUCT_ID, partyId)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .doOnTerminate(() -> {
                    showLoader(view != null, false);
                })
                .subscribe(allowedCoverAmountResponseData -> {
                    AllowedCoverAmountResponseViewModel allowedCoverAmountResponseViewModel = mAllowedCoverAmountResponseDataToViewModelMapper
                            .map(allowedCoverAmountResponseData);
                    onClickProductLayout(InsuranceProductType.PERSONAL_ACCIDENTAL, mInsuranceAccountList, ACCIDENTAL_INSURANCE_PRODUCT_ID, allowedCoverAmountResponseViewModel.getAllowedcoveramount(), allowedCoverAmountResponseViewModel.getExistingcoveramount());
                }, throwable -> {
                    if (view != null) {
                        handleNavigationErrorFlow();
                    }
                });
    }


    private String getRSAId() {
        UserDetailViewModel userData = (UserDetailViewModel) memoryApplicationStorage
                .getObject(ResponseStorageKey.USERDETAILDATA);
        if (userData != null) {
            return userData.getIdOrTaxIdNumber();
        }
        return null;
    }

    void getInsuranceAccountList(List<AccountViewModel> mInsuranceAccountList, AccountsOverview accountsOverview, String action, Overview overview) {
        if ((accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                && accountsOverview.insuranceAccountList != null) {
            mInsuranceAccountList = accountsOverview.insuranceAccountList;
        }
        if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PERSONAL_ACCIDENT_COVER)) {
            getAllowedCoverAmount();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_HOME_OWNERS_COVER) || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER)) {
            List<Long> homeLoanAccounts = new ArrayList<>();
            for (AccountsOverview accountsOverview1 : overview.accountsOverviews) {
                addAccountsOverviewTypeLoan(homeLoanAccounts, accountsOverview1);
            }
            InsuranceViewModel viewModel = new InsuranceViewModel();
            viewModel.setAge(FormattingUtil.getAgeDifference(getClientDob()));
            viewModel.setInsuranceAccountList(mInsuranceAccountList);
            viewModel.setHomeLoan(CollectionUtils.isNotEmpty(homeLoanAccounts));
            viewModel.setHomeLoanAccounts(homeLoanAccounts);
            if (CollectionUtils.isNotEmpty(viewModel.getHomeLoanAccounts()) && !mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_HOC)) {
                getNonNedbankInsuredPropertiesUseCase(viewModel, action);
            } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER)) {
                onClickMyCoverLayout(InsuranceProductType.PERSONAL_LINES_BUILDING, mInsuranceAccountList, InsuranceConstants.MY_COVER_INSURANCE_BUILDING_PRODUCT_ID, 0.0, 0.0);
                view.showProgressVisible(false);
            } else {
                view.showProgressVisible(false);
                handleNavigationErrorFlow();
            }
        }

    }

    void addAccountsOverviewTypeLoan(List<Long> homeLoanAccounts, AccountsOverview accountsOverview) {
        if ((accountsOverview.overviewType == OverviewType.LOANS)
                && accountsOverview.accountSummaries != null) {
            for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
                if (!StringUtils.isNullOrEmpty(accountSummary.getAccountCode()) &&
                        accountSummary.getAccountCode().equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.HL.name())) {
                    homeLoanAccounts.add(Long.parseLong(accountSummary.getNumber()));
                }
            }
        }
    }


    private void getNonNedbankInsuredPropertiesUseCase(InsuranceViewModel viewModel, String action) {
        NonNedbankInsuredRequestDataModel requestDomain = new NonNedbankInsuredRequestDataModel();
        List<Long> homeLoanAccounts = viewModel.getHomeLoanAccounts();
        mInsuranceAccountList = viewModel.getInsuranceAccountList();
        if (CollectionUtils.isNotEmpty(homeLoanAccounts)) {
            requestDomain.setInsurer(StringUtils.EMPTY_STRING);
            requestDomain.setHomeLoanAccountNumbers(homeLoanAccounts.toArray(new Long[homeLoanAccounts.size()]));
        }
        mNonNedbankInsuredPropertiesUseCase.execute(requestDomain)
                .compose(bindToLifecycle()).subscribe(nonNedbankInsuredPropertiesDomain -> {
                    viewModel.setHomeLoan(CollectionUtils.isNotEmpty(nonNedbankInsuredPropertiesDomain));
                    List<NonNedbankInsuredPropertiesViewModel> nonNedbankInsuredPropertiesViewModels =
                            mNonNedbankInsuredPropertiesDomainToViewMapper.map(nonNedbankInsuredPropertiesDomain);
                    mPropertiesViewModels = nonNedbankInsuredPropertiesViewModels;
                    navigateToHOCMyCover(action);
                    view.showProgressVisible(false);
                }, error -> navigateToHOCMyCover(action));
    }

    private void navigateToHOCMyCover(String action) {
        if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER)) {
            onClickMyCoverLayout(InsuranceProductType.PERSONAL_LINES_BUILDING, mInsuranceAccountList, InsuranceConstants.MY_COVER_INSURANCE_BUILDING_PRODUCT_ID, 0.0, 0.0);
        } else {
            onClickProductLayout(InsuranceProductType.HOME_OWNER_COVER, mInsuranceAccountList, InsuranceConstants.HOC_INSURANCE_PRODUCT_ID, 0.0, 0.0);
        }
    }

    private String getClientDob() {
        UserDetailViewModel userData = (UserDetailViewModel) memoryApplicationStorage
                .getObject(ResponseStorageKey.USERDETAILDATA);
        if (userData != null) {
            return userData.getBirthDate();
        }
        return null;
    }


    void loadRetrievePolicy(String productCode, String action) {
        mRetrievePolicyUseCase.execute(new RetrievePolicyRequestDataModel(InsuranceConstants.CONSTANT_YES, InsuranceConstants.CONSTANT_YES))
                .compose(bindToLifecycle()).doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                }).doOnTerminate(() -> {
                    showLoader(view != null, false);
                }).subscribe(retrievePolicyDetailsData -> {
                    if (view != null) {
                        za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel itemViewModel = filterInsurancePolicy(mRetrieveResponseDataToViewModelMapper.transform(retrievePolicyDetailsData), productCode);
                        mRetrievePolicyNumber = itemViewModel.getAccountNumber();
                        mPolicyIssuedDate = itemViewModel.getPolicyStartDate();
                        if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SUBMIT_CLAIM_HOC)) {
                            navigateToSubmitClaimHOC();
                        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SUBMIT_CLAIM_FUNERAL)) {
                            getProductClaimList();
                        } else {
                            handleNavigationErrorFlow();
                        }

                    }
                }, throwable -> {
                    if (view != null) {
                        handleNavigationErrorFlow();
                    }
                });
    }

    private za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel filterInsurancePolicy(RetrievePolicyResponseViewModel policyDetailsViewModels, String productCode) {
        za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel accountViewModel = new za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel();
        for (za.co.nedbank.services.insurance.view.other.model.response.dashboard.retrieve_policy.AccountViewModel detailsViewModel : policyDetailsViewModels.getAccounts()) {
            if (detailsViewModel != null && detailsViewModel.getAccountNumber().equalsIgnoreCase(productCode)) {
                accountViewModel = detailsViewModel;
                break;
            }
        }
        return accountViewModel;
    }

    private String getRSAIdNumber() {
        UserDetailViewModel userData
                = (UserDetailViewModel) memoryApplicationStorage
                .getObject(ResponseStorageKey.USERDETAILDATA);
        if (userData != null) {
            return InsuranceConstants.RSA_ID_PREFIX + userData.getIdOrTaxIdNumber();
        }
        return null;
    }

    private void navigateToSubmitClaimHOC() {
        InsuranceProductType instanceProduct = InsuranceProductType.HOME_OWNER_COVER_CLAIM;
        int productId = InsuranceConstants.HOC_INSURANCE_PRODUCT_ID;
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_OLD_EDUCATION_SCREEN)
                .withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, instanceProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, productId)
                .withParam(InsuranceConstants.INSURANCE_POLICY_NUMBER, mRetrievePolicyNumber)
                .withParam(InsuranceConstants.POLICY_ISSUED_DATE, mPolicyIssuedDate);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void onNowInitiate() {
        if (view != null) {
            view.showProgressVisible(true);
            mGetFicaStatusUseCase
                    .execute()
                    .compose(bindToLifecycle())
                    .subscribe(
                            isFicaStatusDataModel -> {
                                view.showProgressVisible(false);
                                if (isFicaStatusDataModel.getIsFica()) {
                                    NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.INVONLINE_NOW_ACTIVITY);
                                    navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_NOW_DETAILS_DATA, getNowDetails(mAccountDetailViewModel));
                                    navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_FIRST_WITHDRAWAL_DATE, mFirstWithdrawalDate);
                                    navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_MANAGE_INVEST_ACCOUNT_IS_NOTICE, mProductType);
                                    navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_IS_FROM_SUMBMIT_NOTICE, true);
                                    if (mAccountDetails != null) {
                                        navigationTarget.withParam(NavigationTarget.KEY_ITEM_ACCOUNT_ID, mAccountDetails.getId());
                                    }
                                    mNavigationRouter.navigateTo(navigationTarget);
                                } else {
                                    handleNavigationErrorFlow();
                                }
                            },
                            throwable -> {
                                view.showProgressVisible(false);
                                handleNavigationErrorFlow();
                            });
        }
    }

    void getUserDetails(String target) {
        if (view != null) {
            mGetUserDetailUseCase
                    .execute(false)
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        showLoader(view != null, true);
                    })
                    .doOnTerminate(() -> {
                        showLoader(view != null, false);
                    })
                    .subscribe(userDetailsData -> setUserData(userDetailsData, target),
                            throwable -> {
                                if (view != null)
                                    handleNavigationErrorFlow();
                            });
        }
    }

    private void setUserData(UserDetailData userDetailData, String target) {
        userEntriesViewModel.setBirthDate(userDetailData.getBirthDate());
        userEntriesViewModel.setSecOfficerCd(userDetailData.getSecOfficerCd());
        userEntriesViewModel.setCisNumber(userDetailData.getCisNumber());
        userEntriesViewModel.setClientType(userDetailData.getClientType());
        userEntriesViewModel.setInvestmentProductType(StringUtils.EMPTY_STRING);
        getAllProducts(target);
    }

    void getAllProducts(String target) {
        if (view != null) {
            mGetAllProductsUseCase
                    .execute(getProductDataModel())
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        showLoader(view != null, true);
                    })
                    .doOnTerminate(() -> {
                        showLoader(view != null, false);
                    })
                    .subscribe(allProductResponseDataModel -> {
                                String productID;
                                switch (target) {
                                    case NotificationConstants.NAVIGATION_TARGET.APPLY_32DAY_NOTICE_ACCOUNT:
                                        productID = NotificationConstants.InsuranceProductCodes.DAY32NOTICE.getValue();
                                        break;
                                    case NotificationConstants.NAVIGATION_TARGET.APPLY_JUST_INVEST_ACCOUNT:
                                        productID = NotificationConstants.InsuranceProductCodes.JUSTINVEST.getValue();
                                        break;
                                    case NotificationConstants.NAVIGATION_TARGET.APPLY_MONEY_TRADER:
                                        productID = NotificationConstants.InsuranceProductCodes.MONEYTRADER.getValue();
                                        break;
                                    case NotificationConstants.NAVIGATION_TARGET.APPLY_EASY_ACCESS_DEPOSIT_ACCOUNT:
                                        productID = NotificationConstants.InsuranceProductCodes.EASYACCESSDEPOSIT.getValue();
                                        break;
                                    case NotificationConstants.NAVIGATION_TARGET.APPLY_EFIXED_DEPOSIT_ACCOUNT:
                                        productID = NotificationConstants.InsuranceProductCodes.EFIXEDDEPOSIT.getValue();
                                        break;
                                    case NotificationConstants.NAVIGATION_TARGET.APPLY_PLATINUM_INVEST_ACCOUNT:
                                        productID = NotificationConstants.InsuranceProductCodes.PLATINUMINVEST.getValue();
                                        break;
                                    case NotificationConstants.NAVIGATION_TARGET.APPLY_PLATINUM_FIXED_DEPOSIT_ACCOUNT:
                                        productID = NotificationConstants.InsuranceProductCodes.PLATINUMFIXEDDEPOSIT.getValue();
                                        break;
                                    case NotificationConstants.NAVIGATION_TARGET.APPLY_PRIME_SELECT_ACCOUNT:
                                        productID = NotificationConstants.InsuranceProductCodes.PRIMESELECT.getValue();
                                        break;
                                    default:
                                        productID = null;
                                        break;
                                }
                                AllProductsViewModel productsViewModel = filterInvestData(mAllProductResponseDataModelToViewModelMapper.mapAllProductsDataModelToViewModel(allProductResponseDataModel), productID);
                                if (productsViewModel != null) {
                                    navigateToMoreInfo(productsViewModel.getIsNoticeDeposit(), productsViewModel, userEntriesViewModel);
                                } else {
                                    handleNavigationErrorFlow();
                                }
                            }
                            ,
                            error -> handleNavigationErrorFlow()
                    );
        }
    }

    private AllProductsViewModel filterInvestData(AllProductsResponseViewModel allProductsResponseViewModel, String option) {
        for (AllProductsViewModel allProductsViewModel : allProductsResponseViewModel.getAllProductsViewModels()) {
            if (Long.toString(allProductsViewModel.getProductId()).equals(option)) {
                return allProductsViewModel;
            }

        }
        return null;
    }

    private ProductDataModel getProductDataModel() {
        ProductDataModel productDataModel = new ProductDataModel();
        productDataModel.setType(INVESTMENTS);
        int clientAge = AppUtility.calculateAge(userEntriesViewModel.getBirthDate());
        productDataModel.setClientAge(clientAge);
        if (clientAge == 0) {
            productDataModel.setClientType(userEntriesViewModel.getClientType());
        } else {
            productDataModel.setClientType(userEntriesViewModel.getSecOfficerCd());
        }
        return productDataModel;
    }

    void navigateToMoreInfo(String isNotice, AllProductsViewModel allProductsViewModel, OpenNewAccUserEntriesViewModel userEntriesViewModel) {
        if (view != null) {
            NavigationTarget navigationTarget = NavigationTarget.to(OpenNewInvAccountTarget.TARGET_SUGGESION_MOREINFO);
            navigationTarget.withParam(IS_NOTICE_DEPOSIT, isNotice);
            navigationTarget.withParam(OpenNewInvAccountConstants.KEY_IS_FILTER_ENABLED_SUGGESION, false);
            navigationTarget.withParam(OpenNewInvAccountConstants.KEY_PRODUCT_MODEL, allProductsViewModel);
            navigationTarget.withParam(OpenNewInvAccountConstants.KEY_USERENTRIES_MODEL, userEntriesViewModel);
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_PROJECTED_AMOUNT, userEntriesViewModel.getProjectedAmount());
            navigationTarget.withParam(NgiNavigatorTarget.PARAM_INVESTMENT_SWITCHING_FLOW, za.co.nedbank.core.Constants.ZERO);
            navigationTarget.withParam(NavigationTarget.PARAM_SWITCHING_INVESTMENT_VIEW_MODEL, new InvestmentSwitchingViewModel());
            mNavigationRouter.navigateTo(navigationTarget);
        }
    }

    public void navigateToEditNotice(CreateNoticeViewModel createNoticeViewModel) {
        if (view != null) {
            NavigationTarget target = NavigationTarget.to(NavigationTarget.INVONLINE_NOW_ACTIVITY);
            target.withParam(NavigationTarget.KEY_IS_FROM_NOTICE_OF_WITHDRAWAL, false);
            target.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_NOW_DETAILS_DATA, createNoticeViewModel);
            target.withParam(NavigationTarget.KEY_INVESTMENT_ONLINE_IS_DELETE_NOTICE_ENABLED, true);
            target.withParam(NavigationTarget.KEY_IS_FROM_UPDATE_NOTICE_OF_WITHDRAWAL, true);
            target.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_FIRST_WITHDRAWAL_DATE, createNoticeViewModel.getFirstWithdrawalDate());
            target.withParam(NavigationTarget.KEY_ITEM_ACCOUNT_ID, mAccountId);
            target.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_MANAGE_INVEST_ACCOUNT_IS_NOTICE, "Y");
            mNavigationRouter.navigateTo(target);
        }
    }

    private void navigateToDownloadStatement(String statementType) {
        if ((isFeatureAvailableForDownloadTransactionFormat() && (za.co.nedbank.core.Constants.ACCOUNT_TYPES.SA.getAccountTypeCode().equals(mAccountDetailViewModel.getAccountType()) || za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.getAccountTypeCode().equals(mAccountDetailViewModel.getAccountType())) && za.co.nedbank.core.Constants.StatementType.BANK_STATEMENT.equals(statementType)) || (isFeatureAvailableForCreditCardMultipleFormat() && za.co.nedbank.core.Constants.StatementType.CREDIT_CARD_STATEMENT.equals(statementType))) {
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_STATEMENTS);
            navigationTarget.withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_NUMBER, mAccountNo)
                    .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_ID, mAccountId)
                    .withParam(ServicesNavigationTarget.DOWNLOAD_STATEMENT_TYPE, statementType)
                    .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_TYPE, mAccountType)
                    .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_NAME, mAccountHolderName)
                    .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_BALANCE, mAccountDetailViewModel.getAvailableBalance());
            mNavigationRouter.navigateTo(navigationTarget);

        } else {
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.DOWNLOAD_STATEMENTS);
            navigationTarget.withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_NUMBER, mAccountNo)
                    .withParam(ServicesNavigationTarget.DOWNLOAD_STATEMENT_TYPE, statementType)
                    .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_TYPE, mAccountType);
            mNavigationRouter.navigateTo(navigationTarget);
        }
    }

    private void handleStatementDownloadFlow() {
        if (isFeatureAvailableForCASADownloadStatements()) {
            navigateToDownloadStatement(za.co.nedbank.core.Constants.StatementType.BANK_STATEMENT);
        } else if (isFeatureAvailableForInvestmentDownloadStatements()) {
            navigateToDownloadStatement(za.co.nedbank.core.Constants.StatementType.INVESTMENT_BANK_STATEMENT);
        } else if (isFeatureAvailableForCreditCardStatement()) {
            getPlasticIdandInitiateFlow(mAccountNo, NotificationConstants.NAVIGATION_TARGET.STATEMENT_DOWNLOAD);
        }
    }


    private boolean isFeatureAvailableForCASADownloadStatements() {
        return (isFeatureAvailableForDownloadStatements()
                && mAccountDetailViewModel.getAccountType() != null
                && (mAccountSubtype.equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.SA.getAccountTypeCode())
                || mAccountSubtype.equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.getAccountTypeCode())));
    }

    private boolean isFeatureAvailableForInvestmentDownloadStatements() {
        return isFeatureAvailableForInvestDownloadStatements()
                && mAccountSubtype != null
                && (mAccountSubtype.equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.DS.getAccountTypeCode())
                || mAccountSubtype.equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.TD.getAccountTypeCode())
                || mAccountSubtype.equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.UT.getAccountTypeCode()));
    }

    private boolean isFeatureAvailableForCreditCardStatement() {
        return isFeatureAvailableForCreditCardDownloadStatements()
                && (mAccountSubtype.equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CC.getAccountTypeCode()));
    }

    boolean isFeatureAvailableForDownloadStatements() {
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.DOWNLOAD_STATEMENTS);
    }

    boolean isFeatureAvailableForInvestDownloadStatements() {
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.INVESTMENT_DOWNLOAD_STATEMENTS);
    }

    public boolean isFeatureAvailableForDownloadTransactionFormat() {
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DOWNLOAD_TRANSACTION_FORMAT);
    }

    public boolean isFeatureAvailableForCreditCardMultipleFormat() {
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DOWNLOAD_CREDIT_CARD_MULTIPLE_FORMAT);
    }

    boolean isFeatureAvailableForCreditCardDownloadStatements() {
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DOWNLOAD_CREDIT_CARD_STATEMENT);
    }

    private void handleApplyMyLifeOrFuneralFlow(Overview overview, int productId) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.insuranceAccountList != null) {
                    mInsuranceAccountList = accountsOverview.insuranceAccountList;
                    getNIFPMyCoverAllowedCoverAmount(productId);
                }
            }
        }
    }


    private void handleDebitOrderSwitchingFlow() {
        if (!StringUtils.isNullOrEmpty(mAccountId)) {
            NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.DEBIT_ORDER_BANK_DETAILS)
                    .withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_ID, mAccountId)
                    .withParam(za.co.nedbank.core.Constants.NAV_TO_DASHBOARD, true);
            mNavigationRouter.navigateTo(navigationTarget);
        } else {
            handleNavigationErrorFlow();
        }

    }

    private void handleBuyForexFlow() {
        if (!StringUtils.isNullOrEmpty(mAccountId)) {
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.Travel_CardPocket_Trips_screen)
                    .withParam(za.co.nedbank.core.Constants.TRAVEL_CARD_ACCOUNT_NUMBER, mAccountNo)
                    .withParam(za.co.nedbank.core.Constants.TRAVEL_CARD_ACCOUNT_ID, mAccountId)
                    .withParam(za.co.nedbank.core.Constants.TRAVEL_CARD_ACCOUNT_HOLDER_NAME, mAccountHolderName));
        } else {
            handleNavigationErrorFlow();
        }

    }

    private void handleSellForexFlow() {
        if (!StringUtils.isNullOrEmpty(mAccountId)) {
            mNavigationRouter.navigateTo((NavigationTarget.to(NavigationTarget.WORKING_HOURS))
                    .withParam(NavigationTarget.PARAM_TRAVEL_SELL_FLOW, true)
                    .withParam(NavigationTarget.PARAM_TRAVEL_ACCOUNT, mAccountNo)
                    .withParam(NavigationTarget.PARAM_TRAVEL_ACCOUNT_ID, mAccountId)
                    .withParam(za.co.nedbank.core.Constants.FROM_SINGLE_POCKET, false)
                    .withParam(NavigationTarget.PARAM_TRAVEL_ACCOUNT_HOLDER_NAME, mAccountHolderName));
        } else {
            handleNavigationErrorFlow();
        }


    }

    void getNIFPMyCoverAllowedCoverAmount(int productId) {
        String partyId = getRSAId();
        List<String> arrayOfRsaId = new ArrayList<>();
        if (StringUtils.isNotEmpty(partyId)) {
            arrayOfRsaId.add(partyId);
        }
        mNifpMyCoverAllowCoverUseCase.execute(productId, arrayOfRsaId)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> view.showProgressVisible(true))
                .doOnTerminate(() -> view.showProgressVisible(false))
                .subscribe(allowedCoverAmountResponseData -> setNIFPMyCoverAmount(mNifpMyCoverAmountResponseDataToViewModelMapper
                                .map(allowedCoverAmountResponseData), productId),
                        throwable -> {
                            if (view != null) {
                                handleNavigationErrorFlow();
                            }
                        });
    }

    private void setNIFPMyCoverAmount(List<AllowedAmountResponseViewModel> allowedCoverList, int productId) {
        if (CollectionUtils.isNotEmpty(allowedCoverList)) {
            AllowedCoverAmountResponseViewModel allowedCover = allowedCoverList.get(za.co.nedbank.core.Constants.ZERO).getAllowedCoverAmountResponseViewModel();
            switch (productId) {
                case INSURANCE_PRODUCT_ID:
                    setAllowedFuneralCoverAmount(allowedCover);
                    break;
                case InsuranceConstants.MY_COVER_LIFE_PRODUCT_ID:
                    setMyCoverAllowedCoverAmount(allowedCover);
                    break;
                default:
                    // There is no default use case
                    break;
            }
        } else {
            handleNavigationErrorFlow();
        }


    }

    private void setAllowedFuneralCoverAmount(AllowedCoverAmountResponseViewModel allowedCoverAmountResponseViewModel) {
        if (allowedCoverAmountResponseViewModel.getAllowedcoveramount() > MIN_VALUE) {
            double allowedMaxCoverAmount = allowedCoverAmountResponseViewModel.getAllowedcoveramount();
            double existingCoverAmount = allowedCoverAmountResponseViewModel.getExistingcoveramount();
            onClickProductLayout(InsuranceProductType.FUNERAL_OWN_COVER,
                    mInsuranceAccountList, InsuranceConstants.INSURANCE_PRODUCT_ID, allowedMaxCoverAmount, existingCoverAmount);
        } else {
            handleNavigationErrorFlow();

        }
    }

    public void setMyCoverAllowedCoverAmount(AllowedCoverAmountResponseViewModel allowedCoverAmountResponseViewModel) {
        if (allowedCoverAmountResponseViewModel.getAllowedcoveramount() > InsuranceConstants.InsuranceCoverConstants.MY_COVER_MIN_VALUE) {
            double allowedMaxCoverAmount = allowedCoverAmountResponseViewModel.getAllowedcoveramount();
            double existingCoverAmount = allowedCoverAmountResponseViewModel.getExistingcoveramount();
            onClickProductLayout(InsuranceProductType.MY_COVER_LIFE,
                    mInsuranceAccountList, InsuranceConstants.MY_COVER_LIFE_PRODUCT_ID, allowedMaxCoverAmount, existingCoverAmount);
        } else {
            handleNavigationErrorFlow();
        }
    }

    private CardViewModel getSelectedCardViewModel(List<CardViewModel> cardViewModelList, String cardNumber) {
        CardViewModel cardViewModel = new CardViewModel();
        for (CardViewModel viewModel : cardViewModelList) {
            if (viewModel.getCardNumber().equalsIgnoreCase(cardNumber)) {
                cardViewModel = viewModel;
                break;
            }
        }
        return cardViewModel;
    }

    private CardDataModel getSelectedCardDataModel(List<CardDataModel> cardDataModelList, String cardNumber) {
        CardDataModel cardDataModel = new CardDataModel();
        for (CardDataModel dataModel : cardDataModelList) {
            if (dataModel.getCardNumber().equalsIgnoreCase(cardNumber)) {
                cardDataModel = dataModel;
                break;
            }
        }
        return cardDataModel;
    }


    private void navigateToOTAScreen(CardViewModel cardViewModel) {
        mAnalytics.sendEvent(ServicesTrackingValue.VIEW_OTN_SCREEN, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.OTN_ACTIVITY)
                .withParam(ServicesNavigationTarget.PARAM_CURRENT_CARD, cardViewModel));
    }

    private void handleSavingsPocketSetUpFlow(String accountNumber) {
        mGetFicaStatusUseCase
                .execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                }).subscribe(ficaStatusDataModel -> {
                    if (ficaStatusDataModel.getIsFica()) {
                        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.ONLINE_SAVINGS_EDUCATIONAL_SCREEN)
                                .withParam(za.co.nedbank.services.Constants.SAVINGS_POCKETS_TP_ACCOUNT, accountNumber);
                        mNavigationRouter.navigateTo(navigationTarget);
                    } else {
                        handleNavigationErrorFlow();
                    }
                }, throwable -> {
                    if (view != null) {
                        handleNavigationErrorFlow();
                    }
                });


    }

    private void setAccountDetails(Overview overview, String accountNo) {
        for (AccountsOverview accountsOverview : overview.accountsOverviews) {
            for (AccountSummary summary : accountsOverview.accountSummaries) {
                if (summary.getNumber() != null && summary.getNumber().equalsIgnoreCase(accountNo)) {
                    mAccountId = summary.getId();
                    mAccountHolderName = summary.getAccountHolderName();
                    mAccountType = summary.getAccountType().name();
                    mFirstWithdrawalDate = summary.getFirstWithdrawalDate();
                    mProductType = summary.getProductType();
                    mOverViewType = summary.getAccountType().getValue();
                    mNoticeType = summary.getNoticeProduct();
                    mPledgeStatus = summary.isPledgeAccount();
                    break;
                }
            }
        }
    }


    private void handleSalarySwitchShareAccDetailsFlow(String target) {

        Observable<UserDetailData> userDetailDataObservable = mGetUserDetailUseCase.execute(false);
        Observable<AccountDetailData> accountDetailDataObservable = mGetAccountDetailUseCase.execute(mAccountId);
        Observable<List<BranchCodeDataModel>> listObservable = mBranchCodeUseCase.execute();

        if (null != userDetailDataObservable && null != accountDetailDataObservable && listObservable != null) {
            Observable.zip(userDetailDataObservable, accountDetailDataObservable, listObservable,
                            (userDetailData, accountDetailData, branchCodeDataModelList) -> {
                                if (null != userDetailData && null != accountDetailData) {
                                    this.mBranchCodeViewModelList = mModelToBranchCodeViewModelMapper.mapBranchCodeViewModel(branchCodeDataModelList);
                                    this.mUserDetailViewModel = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData);
                                    return mAccountDetailDataToViewModelMapper.mapAccountDetailDataToViewModel(accountDetailData);
                                }
                                return null;
                            }).compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        showLoader(view != null, true);
                    })
                    .doOnTerminate(() -> {
                        showLoader(view != null, false);
                    }).subscribe(accountDetailViewModel -> handleSalarySwitchShareAccDetailsResponse(accountDetailViewModel, target),
                            error -> {
                                if (view != null)
                                    handleNavigationErrorFlow();
                            });
        }

    }

    private void handleSalarySwitchShareAccDetailsResponse(AccountDetailViewModel accountDetailViewModel, String target) {
        String name = StringUtils.EMPTY_STRING;

        if (mUserDetailViewModel != null) {
            if (!StringUtils.isNullOrEmpty(mUserDetailViewModel.getFirstName())) {
                name = mUserDetailViewModel.getFirstName();
            }

            if (!StringUtils.isNullOrEmpty(mUserDetailViewModel.getSurname())) {
                name = name + StringUtils.SPACE + mUserDetailViewModel.getSurname();
            }
        }
        accountDetailViewModel.setAccountHolderName(name);
        handleNavigation(accountDetailViewModel, target);
    }

    private void handleNavigation(AccountDetailViewModel accountDetailViewModel, String target) {
        if (null != view && null != accountDetailViewModel) {
            if (target.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.RECEIVED_SSL)) {
                navigateToSalarySwitch(accountDetailViewModel);
            } else if (target.equals(NotificationConstants.NAVIGATION_TARGET.ACCOUNT_DOCUMENTS_STATEMENTS)) {
                handleStatementsAndDocumentsClick(mAccountId, accountDetailViewModel.getAccountType(), mOverViewType, false, accountDetailViewModel.getAccountName(), accountDetailViewModel.getAccountNumber(), accountDetailViewModel.getAvailableBalance());
            }

        } else {
            handleNavigationErrorFlow();
        }
    }

    public String getAccountHolderName(AccountDetailViewModel accountDetailViewModel) {
        if (accountDetailViewModel != null && accountDetailViewModel.getAccountType() != null) {
            if (accountDetailViewModel.getAccountType().equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.HL.getAccountTypeCode()))
                return accountDetailViewModel.getAccountHolderName();
            if (accountDetailViewModel.getAccountType().equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.getAccountTypeCode()) || accountDetailViewModel.getAccountType().equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.SA.getAccountTypeCode()) || accountDetailViewModel.getAccountType().equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.PL.getAccountTypeCode())) {
                return String.format("%s %s", mUserDetailViewModel.getFirstName(), mUserDetailViewModel.getSurname());
            }

        }
        return StringUtils.EMPTY_STRING;

    }


    private void navigateToSalarySwitch(AccountDetailViewModel accountDetailViewModel) {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.SELECT_SALARY_SWITCH_ACCOUNT_REQUEST);
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_NUMBER, accountDetailViewModel.getAccountNumber());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_NAME, accountDetailViewModel.getAccountHolderName());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_TYPE, accountDetailViewModel.getAccountType());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_BRANCH_CODE, getBranchCodeValue(accountDetailViewModel.getAccountType()));
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_EMAIL, mUserDetailViewModel.getEmailAddress());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ID_OR_TAX_ID_NUMBER, mUserDetailViewModel.getIdOrTaxIdNumberString());
        navigationTarget.withParam(za.co.nedbank.services.Constants.ITEM_ACCOUNT_ID, mAccountId);
        navigationTarget.withParam(za.co.nedbank.core.Constants.NAV_TO_DASHBOARD, true);
        navigationTarget.withParam(PARAM_DOCUMENT_REFERENCE, documentReference);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    private String getBranchCodeValue(String accountType) {
        if (mBranchCodeViewModelList != null && !mBranchCodeViewModelList.isEmpty()) {
            for (int i = 0; i < mBranchCodeViewModelList.size(); i++) {
                if (accountType.contains(mBranchCodeViewModelList.get(i).getAccountType())) {
                    return mBranchCodeViewModelList.get(i).getBranchCode();
                }
            }
        }
        return StringUtils.EMPTY_STRING;
    }

    private void handlePreApprovedOffers(String offerId, String action) {

        if (StringUtils.isNullOrEmpty(offerId)) {
            handleNavigationErrorFlow();
            return;
        }

        mGetPreApprovedOffersUseCase.execute(new GetPreApprovedOfferRequestData(za.co.nedbank.core.Constants.PreApprovedOffersProductCode.ALL.getProductCode(), null))
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .doFinally(() -> {
                    showLoader(view != null, false);
                })
                .subscribe(preApprovedOffersData -> {
                            if (view != null) {
                                PreApprovedOffersViewModel preApprovedOffersViewModel = mPreApprovedOffersDataToViewModelMapper.transform(preApprovedOffersData);
                                if (preApprovedOffersViewModel != null && preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel() != null) {
                                    String errorMessageIfAny = PreApprovedOffersUtility.extractErrorFromMetaData(preApprovedOffersViewModel.getPreApprovedOffersMetadataViewModel());
                                    if (StringUtils.isNullOrEmpty(errorMessageIfAny)) {
                                        List<PreApprovedOffersInnerDetailsViewModel> offers = preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList();
                                        PreApprovedOffersInnerDetailsViewModel offer = findOfferById(offerId, offers);
                                        handleNavigationToOffers(offer, action);
                                    } else {
                                        handleNavigationErrorFlow();
                                    }
                                }
                            }
                        },
                        throwable -> {
                            if (view != null) {
                                handleNavigationErrorFlow();
                            }
                        });
    }

    private void handleNavigationToOffers(PreApprovedOffersInnerDetailsViewModel offer, String action) {

        if (offer == null) {
            handleNavigationErrorFlow();
            return;
        }

        long offerId = offer.getId();
        long offerTypeId = 0;
        String offerType = StringUtils.EMPTY_STRING;
        if (offer.getPreApprovedOffersOfferTypeViewModel() != null) {
            offerTypeId = offer.getPreApprovedOffersOfferTypeViewModel().getId();
            offerType = offer.getPreApprovedOffersOfferTypeViewModel().getCode();
        }
        String notificationName = offer.getShortMessage();
        OfferNavigationModel offerNavigationModel = new OfferNavigationModel();
        offerNavigationModel.setOfferId(offerId);
        offerNavigationModel.setOfferTypeId(offerTypeId);
        offerNavigationModel.setOfferType(offerType);
        offerNavigationModel.setNotificationName(notificationName);
        NavigationTarget navigationTarget = null;

        switch (action) {
            case NotificationConstants.NAVIGATION_TARGET.PRE_APPROVED_CREDIT_CARD_OFFERS:
                navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.CARD_OFFER_SELECTION);
                offerNavigationModel.setLimitIncrease(true);
                break;
            case NotificationConstants.NAVIGATION_TARGET.PRE_APPROVED_OVERDRAFT_OFFERS:
                navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.PRE_APPROVED_OFFERS_DISLCAIMERS);
                navigationTarget.withParam(Constants.BundleKeys.IS_FROM_PL, false);
                break;
            case NotificationConstants.NAVIGATION_TARGET.PRE_APPROVED_OFFERS_HL_READVANCE:
                navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.PRE_APPROVED_CAMPAIGN);
                navigationTarget.withParam(Constants.BundleKeys.IS_FROM_PL, false);
                break;
            default:
                break;
            //todo in future for personal loan and consolidation loan offer, navigate to PreApprovedOffersNavigationTarget.PRE_APPROVED_CAMPAIGN
        }
        if (navigationTarget != null) {
            navigationTarget.withParam(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.NOTIFICATION_DETAILS_SCREEN);
            navigationTarget.withParam(Constants.BundleKeys.OFFER_MODEL, offerNavigationModel);
            mNavigationRouter.navigateTo(navigationTarget);
        } else {
            handleNavigationErrorFlow();
        }

    }


    private PreApprovedOffersInnerDetailsViewModel findOfferById(String offerId, List<PreApprovedOffersInnerDetailsViewModel> offers) {

        if (offers != null) {
            for (PreApprovedOffersInnerDetailsViewModel offer :
                    offers) {

                if (offerId.equalsIgnoreCase(Long.toString(offer.getId()))) {
                    return offer;
                }
            }
        }

        return null;
    }

    private void handleDebitOrderDetailsFlow(String debitOrderId) {
        mGetDebitOrderDetailsUseCase.execute(debitOrderId).compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .doOnTerminate(() -> {
                    showLoader(view != null, false);
                })
                .subscribe(debitOrder -> {
                    if (debitOrder != null && view != null) {
                        navigateToDebitOrderDetails(debitOrder);
                    } else {
                        handleNavigationErrorFlow();
                    }
                }, error -> {
                    if (view != null) {
                        handleNavigationErrorFlow();
                    }
                });
    }

    private void navigateToDebitOrderDetails(DebitOrder debitOrder) {
        if (debitOrder.getStatus() == DebitOrderStatus.EXISTING || debitOrder.getStatus() == DebitOrderStatus.REVERSE ||
                debitOrder.getStatus() == DebitOrderStatus.STOPPED) {
            mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.DEBIT_ORDER_DETAILS)
                    .withParam(ServicesNavigationTarget.PARAM_DEBIT_ORDER_DETAILS, debitOrder)
                    .withParam(ServicesNavigationTarget.PARAM_DEBIT_ORDER_STOPPED, debitOrder.isStopped())
                    .withParam(za.co.nedbank.core.Constants.NAV_TO_DASHBOARD, true));
        } else {
            mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.MANDATE_DEBIT_ORDER)
                    .withParam(ServicesNavigationTarget.PARAM_DEBIT_ORDER_DETAILS, debitOrder)
                    .withParam(za.co.nedbank.core.Constants.NAV_TO_DASHBOARD, true));
        }
    }

    private void handleAccountDetailsFlow(String accountNo, String target) {
        Observable<CachableValue<Overview>> overviewObservable = mGetOverviewUseCase.execute();
        Observable<UserDetailData> userDetailsObservable = mGetUserDetailUseCase.execute(false);
        if (null != overviewObservable && null != userDetailsObservable) {
            Observable.zip(overviewObservable, userDetailsObservable,
                            (overviewCachableValue, userDetailData) ->
                                    calculateAccountSummary(overviewCachableValue, userDetailData, accountNo))
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        showLoader(view != null, true);
                    })
                    .doOnTerminate(() -> {
                        showLoader(view != null, false);
                    }).subscribe(accountSummary -> showAccountDetails(accountSummary, target),
                            error -> {
                                if (view != null)
                                    handleNavigationErrorFlow();
                            });
        }
    }

    private AccountSummary calculateAccountSummary(CachableValue<Overview> overviewCachableValue, UserDetailData userDetailData, String accountNo) throws CloneNotSupportedException {
        if (null != overviewCachableValue && null != userDetailData) {
            Overview overviewValue = overviewCachableValue.get().clone();
            hasTransactableAccount = checkForAnyTransactableAccount(overviewValue);
            mClientType = userDetailData.getClientType();
            mFicaStatus = userDetailData.getFicaStatus();
            return getAccountSummary(overviewValue, accountNo);
        }
        return null;
    }

    private void showAccountDetails(AccountSummary accountSummary, String target) {
        if (null != view && null != accountSummary) {
            navigateToAccountDetails(accountSummary, target);
        } else {
            handleNavigationErrorFlow();
        }
    }

    private void handleAccountDetailsandNextFlow(String action) {
        Observable<AccountDetails> accountDetailsObservable = mGetAccountDetailsUseCase.execute(mAccountId);
        Observable<AccountDetailData> accountDetailDataObservable = mGetAccountDetailUseCase.execute(mAccountId);

        if (null != accountDetailsObservable && null != accountDetailDataObservable) {
            Observable.zip(accountDetailsObservable, accountDetailDataObservable,
                            (accountDetails, accountDetailData) -> {
                                if (null != accountDetails && null != accountDetailData) {
                                    this.mAccountDetails = accountDetails;
                                    return mAccountDetailDataToViewModelMapper.mapAccountDetailDataToViewModel(accountDetailData);
                                }
                                return null;
                            }).compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        showLoader(view != null, true);
                    })
                    .doOnTerminate(() -> {
                        showLoader(view != null, false);
                    }).subscribe(accountDetailViewModel -> {
                                handleResponse(action, accountDetailViewModel);

                            },
                            error -> {
                                if (view != null)
                                    handleNavigationErrorFlow();
                            });
        }

    }

    private void handleResponse(String action, AccountDetailViewModel accountDetailViewModel) {
        if (null != view && null != accountDetailViewModel) {
            mAccountDetailViewModel = accountDetailViewModel;
            mAccountSubtype = mAccountDetailViewModel.getAccountType();
            switch (action) {
                case NotificationConstants.NAVIGATION_TARGET.PAYOUT:
                    navigateToPayoutDetails();
                    break;
                case NotificationConstants.NAVIGATION_TARGET.SUBMIT_NOTICE_WITHDRAWAL:
                    onNowInitiate();
                    break;
                case NotificationConstants.NAVIGATION_TARGET.EDIT_NOTICES_OF_WITHDRAWAL:
                    getNoticesList();
                    break;
                case NotificationConstants.NAVIGATION_TARGET.STATEMENT_DOWNLOAD:
                    handleStatementDownloadFlow();
                    break;
                case NotificationConstants.NAVIGATION_TARGET.MANAGE_INVESTMENT_ACCOUNT:
                    handleManageInvestmentAccountFlow();
                    break;
                default:
                    handleNavigationErrorFlow();
            }

        } else {
            handleNavigationErrorFlow();
        }
    }

    private void handleManageInvestmentAccountFlow() {
        if (view != null) {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_INVONLINE_MANAGE_INVESTMENTS);
            ManageInvestmentViewModel manageInvestmentViewModel = new ManageInvestmentViewModel();
            manageInvestmentViewModel.setInvestmentAccount(mAccountDetailViewModel.getInvestmentProductName());
            manageInvestmentViewModel.setAccountNumber(mAccountDetailViewModel.getAccountNumber());

            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_MANAGE_INV_VIEW_MODEL, manageInvestmentViewModel);
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_MANAGE_ITEM_ACCOUNT_ID, mAccountId);
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_MANAGE_INVEST_ACCOUNT_IS_NOTICE, mNoticeType);
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_PRODUCT_TYPE, mAccountDetailViewModel.getInvestmentProductType());
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INV_ONLINE_ACCOUNT_PLEDGE_STATUS, mPledgeStatus);
            mNavigationRouter.navigateTo(navigationTarget);
        }
    }


    private CreateNoticeViewModel getNowDetails(AccountDetailViewModel accountDetailViewModel) {
        CreateNoticeViewModel nowViewModel = new CreateNoticeViewModel();
        if (accountDetailViewModel != null) {
            nowViewModel.setInvestmentAccountName(accountDetailViewModel.getInvestmentProductName());
            nowViewModel.setInvestmentNumber(mAccountDetails.getAccountNumber());
            nowViewModel.setCapitalDisposalAccountNumber(StringUtils.EMPTY_STRING);
            nowViewModel.setCapitalDisposalAccountType(StringUtils.EMPTY_STRING);
            nowViewModel.setAccountSwipePosition(Integer.parseInt(StringUtils.ZERO));
            nowViewModel.setBalance(String.valueOf(mAccountDetails.getAvailableBalance()));
            nowViewModel.setIsRecipient(StringUtils.ZERO);
            nowViewModel.setMinWithdrawalAmount(minWithdrawalAmount);
            nowViewModel.setEntryValue(entryValue);
            nowViewModel.setProjectedAmount(accountDetailViewModel.getProjectedAmount());
            nowViewModel.setPayoutSelection(za.co.nedbank.core.Constants.INVONLINE_PAYOUT_ACCOUNT_SELECTED);
        }
        return nowViewModel;
    }

    private void navigateToPayoutDetails() {
        CreateNoticeViewModel createNoticeViewModel = getNowDetails(mAccountDetailViewModel);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_INVONLINE_PAYOUT_DETAIL)
                .withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_PAYOUT_DETAILS_DATA, createNoticeViewModel)
                .withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_WITHDRAWAL_DATE, mAccountDetailViewModel.getExpiryDate()));
    }

    private void handleReinvestmentFlow(String action) {
        Observable<UserDetailData> userDetailDataObservable = mGetUserDetailUseCase.execute(false);
        Observable<AccountDetailData> accountDetailDataObservable = mGetAccountDetailUseCase.execute(mAccountId);
        Observable<AccountDetails> accountDetailsObservable = mGetAccountDetailsUseCase.execute(mAccountId);
        if (null != userDetailDataObservable && null != accountDetailDataObservable) {
            Observable.zip(userDetailDataObservable, accountDetailDataObservable, accountDetailsObservable,
                            (userDetailData, accountDetailData, accountDetails) -> {
                                if (null != userDetailData && null != accountDetailData && accountDetails != null) {
                                    this.mUserDetailData = userDetailData;
                                    this.mAccountDetails = accountDetails;
                                    return mAccountDetailDataToViewModelMapper.mapAccountDetailDataToViewModel(accountDetailData);
                                }
                                return null;
                            }).compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        showLoader(view != null, true);
                    })
                    .doOnTerminate(() -> {
                        showLoader(view != null, false);
                    }).subscribe(accountDetailViewModel -> navigateToReinvestInvSwitch(accountDetailViewModel, action),
                            error -> {
                                if (view != null)
                                    handleNavigationErrorFlow();
                            });
        }

    }

    private void navigateToReinvestInvSwitch(AccountDetailViewModel accountDetailViewModel, String action) {
        if (null != view && null != accountDetailViewModel) {
            if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.REINVESTMENT)) {
                navigateToReinvest(accountDetailViewModel);
            } else {
                navigateToInvSwitchingEduPage(accountDetailViewModel);
            }

        } else {
            handleNavigationErrorFlow();
        }
    }

    private void navigateToReinvest(AccountDetailViewModel accountDetailViewModel) {
        if (view != null) {
            CreateNoticeViewModel createNoticeViewModel = getNowDetails(accountDetailViewModel);
            createNoticeViewModel.setWithdrawalDate(accountDetailViewModel.getExpiryDate());
            NavigationTarget navigationTarget = NavigationTarget.to(ReinvestNavigatorTarget.TARGET_REINVEST_REINVEST_OPTIONS);
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_PROJECTED_AMOUNT, String.valueOf(createNoticeViewModel.getProjectedAmount()));
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_PRODUCT_TYPE, accountDetailViewModel.getInvestmentProductType());
            navigationTarget.withParam(NavigationTarget.PARAM_ONIA_CLIENT_TYPE, mUserDetailData.getClientType());
            navigationTarget.withParam(NavigationTarget.PARAM_ONIA_BIRTH_DATE, mUserDetailData.getBirthDate());
            navigationTarget.withParam(NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD, mUserDetailData.getSecOfficerCd());
            navigationTarget.withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, mUserDetailData.getCisNumber());
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_ADDITIONAL_DEPO_ALLOWED, accountDetailViewModel.getAddCapitalAllowed());
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_INVESTOR_NUMBER, createNoticeViewModel.getInvestmentNumber());
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_NOW_DETAILS_DATA, createNoticeViewModel);
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_REINVEST_ACCRUED_INTEREST, String.valueOf(accountDetailViewModel.getAccruedInterest()));
            navigationTarget.withParam(za.co.nedbank.core.Constants.KEY_REINVEST_CURRENT_BALANCE, String.valueOf(accountDetailViewModel.getCurrentBalance()));
            mNavigationRouter.navigateTo(navigationTarget);
        }
    }

    private AccountSummary getAccountSummary(Overview overview, String accountNum) {
        AccountSummary accountSummary = null;
        if (null != overview) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                for (AccountSummary summary : accountsOverview.accountSummaries) {
                    if (summary.getNumber() != null && summary.getNumber().equalsIgnoreCase(accountNum)) {
                        accountSummary = summary;
                    }
                }
            }
        }
        return accountSummary;

    }

    private void navigateToAccountDetails(AccountSummary accountSummary, String target) {
        if (target != null && target.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.GB_REDEEMPTION)) {
            mApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET, NotificationConstants.NAVIGATION_TARGET.GB_REDEEMPTION);
        }
        mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, canTransact())
                .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, mClientType)
                .withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate())
                .withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice())
                .withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit())
                .withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct())
                .withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit())
                .withParam(ServicesNavigationTarget.INVONLINE_PRODUCT_TYPE, accountSummary.getProductType())
                .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, mFicaStatus)
                .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                .withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, target)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber())
        );
    }

    private boolean canTransact() {
        return hasTransactableAccount;
    }


    private boolean checkForAnyTransactableAccount(Overview overview) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.CREDIT_CARDS ||
                        accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.accountSummaries != null && (isNonDormatAcc(accountsOverview))) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isNonDormatAcc(AccountsOverview accountsOverview) {
        for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
            if (!accountSummary.isDormantAccount()) {
                return true;
            }
        }
        return false;
    }


    private NavigationTarget resolveNavigationTarget(FBNotificationsViewModel.ResponseOption responseOption) {
        String target = responseOption.getAction();

        if (TextUtils.isEmpty(target)) return null;
        switch (target) {
            case NotificationConstants.NAVIGATION_TARGET.GREEN_BACK_ENROLLMENT:
                if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.REWARDS_ENROLMENT)) {
                    return NavigationTarget.to(ProfileNavigationTarget.REWARDS_LANDING);
                }
                return null;
            case NotificationConstants.NAVIGATION_TARGET.DISMISS:
                return NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_MESSAGES);
            case NotificationConstants.NAVIGATION_TARGET.BUY_FLOW:
                NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.VAS);
                for (FBNotificationsViewModel.AdditionalParameter parameter : responseOption.getAdditionalParameters()) {
                    if (parameter.getName().equalsIgnoreCase(NavigationTarget.PARAM_BIRTH_DATE)) {
                        //quick fix,will be removed post MVP
                        String birthDateParam = parameter.getValue() + NotificationConstants.BIRTH_DATE_POSTFIX;
                        navigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDateParam);
                        break;
                    }
                }
                return navigationTarget;
            case NotificationConstants.NAVIGATION_TARGET.TRANSACT:
            case NotificationConstants.NAVIGATION_TARGET.PRE_APPROVED_OFFERS:
                return NavigationTarget.to(PreApprovedOffersNavigationTarget.PRE_APPROVED_OFFERS_NOTIFICATIONS)
                        .withParam(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.NOTIFICATION_DETAILS_SCREEN);
            case NotificationConstants.NAVIGATION_TARGET.REPORT_FRAUD:
                return obtainNavigationTargetForReportFraud();
            case NotificationConstants.NAVIGATION_TARGET.REDEEM_GB_FOREX:
                return NavigationTarget.to(ServicesNavigationTarget.GREENBACKS_FOREX_LANDING);
            case NotificationConstants.NAVIGATION_TARGET.REDEEM_GB_CHARGES_AND_FEES:
                return NavigationTarget.to(ServicesNavigationTarget.CHARGES_AND_FEES_LANDING);
            case NotificationConstants.NAVIGATION_TARGET.REDEEM_GB_DONATION:
                return NavigationTarget.to(ServicesNavigationTarget.DONATIONS_LANDING);
            case NotificationConstants.NAVIGATION_TARGET.PAY_TO_MOBILE:
                return NavigationTarget.to(NavigationTarget.PAY).withParam(CrossBorderNavigationTarget.PARAM_SET_PAYMENT_TYPE, PayMode.MOBILE);
            case NotificationConstants.NAVIGATION_TARGET.APPLY_FINANCIAL_PLANNER:
                return NavigationTarget.to(NavigationTarget.FINANCIAL_PLANNER_FORM).withParam(za.co.nedbank.enroll_v2.Constants.NfpParam.IS_PRE_LOGIN, false);
            case NotificationConstants.NAVIGATION_TARGET.FEEDBACK:
                return NavigationTarget.to(NavigationTarget.CONTACT_US_ITEM).withParam(NavigationTarget.PARAM_CONTACT_US_FEATURE, za.co.nedbank.services.Constants.CONTACT_US.FEEDBACK);
            case NotificationConstants.NAVIGATION_TARGET.ADD_RECIPIENT:
                return NavigationTarget.to(NavigationTarget.ADD_RECIPIENT);
            case NotificationConstants.NAVIGATION_TARGET.PROFILE_LIMITS:
                return NavigationTarget.to(NavigationTarget.PROFILE_LIMITS);
            case NotificationConstants.NAVIGATION_TARGET.PAY_TO_FOREIGN:
                return NavigationTarget.to(NavigationTarget.PAY).withParam(CrossBorderNavigationTarget.PARAM_SET_PAYMENT_TYPE, PayMode.FOREIGN);
            case NotificationConstants.NAVIGATION_TARGET.SETUP_MONEY_TACKER:
                return NavigationTarget.to(ServicesNavigationTarget.MONEY_TRACKER_DASHBOARD_PURPOSE);
            case NotificationConstants.NAVIGATION_TARGET.VIEW_INWARD_PAYMENTS:
                return NavigationTarget.to(NavigationTarget.INTERNATIONAL_PAYMENT_HISTORY);
            case NotificationConstants.NAVIGATION_TARGET.COMMUNICATION_PREFERENCES:
                return NavigationTarget.to(ProfileNavigationTarget.APP_COMMUNICATION);
            case NotificationConstants.NAVIGATION_TARGET.PERSONALISE_APP:
                return NavigationTarget.to(ProfileNavigationTarget.APP_PERSONALISATION);
            case NotificationConstants.NAVIGATION_TARGET.SCAN2PAY_DEFAULT_CARD:
                return NavigationTarget.to(ProfileNavigationTarget.SCANPAY_SET_DEFAULT_CARD);
            case NotificationConstants.NAVIGATION_TARGET.PAY_ME_REQUEST:
                return NavigationTarget.to(NavigationTarget.PAY_ME);
            case NotificationConstants.NAVIGATION_TARGET.INSURANCE_OPTIONS:
                return NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN);
            default:
                return null;
        }
    }

    private NavigationTarget obtainNavigationTargetForReportFraud() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.REPORT_FRAUD)) {
            return NavigationTarget.to(NavigationTarget.MORE_REPORT_FRAUD);
        }
        return null;
    }

    private void handleNonTpJoinFamilyBankingFlow() {
        memoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, StringUtils.EMPTY_STRING);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MOA_BASIC_INFORMATION));
    }

    private void handleInvestmentAccountFlow() {
        mGetUserDetailUseCase.execute(false).compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .doOnTerminate(() -> showLoader(view != null, false))
                .subscribe(userDetail -> {
                    if (userDetail != null && view != null) {
                        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NGI_GET_STARTED)
                                .withParam(PARAM_ONIA_CLIENT_TYPE, userDetail.getClientType())
                                .withParam(PARAM_ONIA_BIRTH_DATE, userDetail.getBirthDate())
                                .withParam(PARAM_ONIA_SEC_OFFICER_CD, userDetail.getSecOfficerCd())
                                .withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, userDetail.getCisNumber()));
                    } else {
                        handleNavigationErrorFlow();
                    }
                }, error -> {
                    if (view != null) {
                        handleNavigationErrorFlow();
                    }
                });
    }

    private void handleStatementPreferenceFlow() {
        mGetAccountDetailsUseCase.execute(mAccountId).compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    showLoader(view != null, true);
                })
                .doOnTerminate(() -> showLoader(view != null, false))
                .subscribe(accountDetails -> {
                    if (accountDetails != null && view != null) {
                        mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.STATEMENT_DELIVERY)
                                .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_NUMBER, accountDetails.getAccountNumber())
                                .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_NAME, accountDetails.getName())
                                .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_ID, accountDetails.getId())
                                .withParam(ServicesNavigationTarget.BUNDLE_ACCOUNT_TYPE, accountDetails.getAccountType()));
                    } else {
                        handleNavigationErrorFlow();
                    }
                }, error -> {
                    if (view != null) {
                        handleNavigationErrorFlow();
                    }
                });
    }

    public void handleStatementsAndDocumentsClick(String accountId, String accountType, int overviewType, boolean isSavingsPocketAccount, String accountName, String accountNumber, double availBalance) {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.STATEMENTS_AND_DOCUMENTS)
                .withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_ID, accountId)
                .withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_TYPE, accountType)
                .withParam(za.co.nedbank.services.Constants.BUNDLE_OVERVIEW_TYPE, overviewType)
                .withParam(za.co.nedbank.services.Constants.BUNDLE_IS_SAVINGS_POCKET_ACCOUNT, isSavingsPocketAccount)
                .withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_NAME, accountName)
                .withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_NUMBER, accountNumber)
                .withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_BALANCE, availBalance);
        mNavigationRouter.navigateTo(navigationTarget);
    }


    void handleChatNotification() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.APP_CHAT)) {
            navigateToChatActivity();
        } else {
            handleNavigationErrorFlow();
        }
    }

    private void navigateToChatActivity() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withIntentFlagClearTopSingleTop(true));
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CHAT_SCREEN));
    }

    private void handleNavigationErrorFlow() {
        NavigationTarget target = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_CENTER);
        target.withParam(NotificationConstants.EXTRA.SHOULD_SHOW_ERROR, true);
        mNavigationRouter.navigateTo(target.withIntentFlagClearTop(true));
    }

    private void handleNavigationVasToggleOffFlow(boolean isVasOff, String birthDateParam) {
        NavigationTarget target = NavigationTarget.to(NavigationTarget.VAS_TOGGLE_OFF);
        target.withParam(VasConstants.EXTRAS.VAS_TOGGLE_OFF, isVasOff);
        target.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDateParam);
        mNavigationRouter.navigateTo(target);
    }

    private FBNotificationUserChoiceData createUserResponseRequest(FBNotificationsViewModel.ResponseOption selectedResponse) {
        FBNotificationUserChoiceData fbNotificationUserChoiceData = new FBNotificationUserChoiceData();
        FBNotificationUserChoiceData.ChannelInfo channelInfo = new FBNotificationUserChoiceData.ChannelInfo();
        FBNotificationUserChoiceData.Response response = new FBNotificationUserChoiceData.Response();
        response.setResponseValue(selectedResponse.getValue());
        response.setDeviceId(getDeviceID());
        response.setDeviceDate(getDate());
        response.setResponsePriority(mFBNotificationsViewModel.getResponsePriority());
        fbNotificationUserChoiceData.setChannelInfo(channelInfo);
        fbNotificationUserChoiceData.setResponse(response);
        fbNotificationUserChoiceData.setNotificationId(mFBNotificationsViewModel.getNotificationId());
        return fbNotificationUserChoiceData;

    }

    private String getDate() {
        SimpleDateFormat dateFormatForParsing = new SimpleDateFormat(FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
        dateFormatForParsing.setTimeZone(TimeZone.getTimeZone(FormattingUtil.SOUTH_AFRICA_TIMZONE_ID));
        return dateFormatForParsing.format(new Date());
    }

    private void sendUserResponseToServer(FBNotificationsViewModel.ResponseOption selectedResponse) {
        mFBNotificationUserChoiceUseCase.execute(createUserResponseRequest(selectedResponse))
                .compose(bindToLifecycle())
                .subscribe(analyticsResponseData -> handleAnalyticsUserChoiceResponse(analyticsResponseData, selectedResponse, true), throwable -> NBLogger.e(TAG, throwable.getMessage()));

    }

    private void handleAnalyticsUserChoiceResponse(FBResponseData analyticsResponseData, FBNotificationsViewModel.ResponseOption selectedResponse, boolean isUserChoice) {
        int ONLY_SUCCESS_ELEMENT = 1;
        if (analyticsResponseData != null && analyticsResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = analyticsResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && !resultDetailList.isEmpty()) {
                    if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        handleSuccess(selectedResponse, isUserChoice);
                        break;
                    } else {
                        handleFaliure(resultDetailList);
                    }
                }

            }
        }
    }

    private void handleFaliure(ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                NBLogger.e(TAG, resultDetailViewModel.getReason());
            }
        }
    }

    private void handleSuccess(FBNotificationsViewModel.ResponseOption selectedResponse, boolean isUserChoice) {
        changeReadCounter();
        if (isUserChoice) {
            NBLogger.e(TAG, "User Choice api success, Selected response :" + selectedResponse.getLabel());
        } else {
            NBLogger.e(TAG, "analytics api success, notification id :" + mFBNotificationsViewModel.getNotificationId());
        }
    }


    void setNotificationData(FBNotificationsViewModel fbNotificationsViewModel) {
        this.mFBNotificationsViewModel = fbNotificationsViewModel;
    }

    void clearNotificationData() {
        memoryApplicationStorage.clearValue(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL);
    }

    void sendBackArrowAnalytics() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setBackActions();
        mAnalytics.sendEventActionWithMap(NotificationConstants.TRACKING_PARAMS.NOTIFICATION_DETAILS_BACK, cdata);
    }

    public void changeReadCounter() {
        if (mFBNotificationsViewModel != null && !mFBNotificationsViewModel.isRead()) {
            int unreadCount = memoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, 0);
            int unreadTotalCount = mApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, 0);
            memoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, unreadCount - 1);
            mApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, unreadTotalCount - 1);
            mFBNotificationsViewModel.setIsRead(true);
            EventBus.getDefault().post(new MarkReadNotificationEvent(mFBNotificationsViewModel));
        }
    }

    public void clearNotificationFromTray() {
        view.clearNotificationFromTray(mFBNotificationsViewModel);
    }

    void populateRichContentUrls(List<FBNotificationsViewModel.RichContent> richContents) {
        if (richContents != null && !richContents.isEmpty()) {
            mRichContentList = richContents;
            addRichContentUrl();
            FBNotificationsViewModel.RichContent videoContent = getVideoContent(richContents);
            List<FBNotificationsViewModel.RichContent> bannerRichContent = filterRichContent(richContents, NotificationConstants.DISPLAY_TYPE_BANNER);
            List<FBNotificationsViewModel.RichContent> footerRichContent = filterRichContent(richContents, NotificationConstants.DISPLAY_TYPE_FOOTER);
            List<FBNotificationsViewModel.RichContent> embRichContent = filterRichContent(richContents, NotificationConstants.DISPLAY_TYPE_EMBEDDED);

            if (!view.isLandscape()) {
                view.showImageLoading(true);
                if (bannerRichContent.isEmpty() && footerRichContent.isEmpty() && embRichContent.isEmpty()) {
                    view.showImageLoadingFailure();
                }
                showBannerFooterRichContentIfAny(bannerRichContent, footerRichContent);
                showEmbeddedContentIfAny(embRichContent);
            } else {
                showVideoInFullScreen(videoContent);
            }

        } else {
            view.showImageLoadingFailure();
        }
    }

    private void showBannerFooterRichContentIfAny(List<FBNotificationsViewModel.RichContent> bannerRichContent, List<FBNotificationsViewModel.RichContent> footerRichContent) {
        if (!bannerRichContent.isEmpty()) {
            showBannerFooterRichContent(bannerRichContent, NotificationConstants.DISPLAY_TYPE_BANNER);
        }
        if (!footerRichContent.isEmpty()) {
            showBannerFooterRichContent(footerRichContent, NotificationConstants.DISPLAY_TYPE_FOOTER);
        }
    }

    private void showVideoInFullScreen(FBNotificationsViewModel.RichContent videoContent) {
        if (!StringUtils.isNullOrEmpty(videoContent.getUrl())) {
            view.setRichContentVideo(videoContent.getUrl(), videoContent.getDisplayType());
        }
    }

    private void showEmbeddedContentIfAny(List<FBNotificationsViewModel.RichContent> embRichContent) {
        if (!embRichContent.isEmpty()) {
            view.showEmbeddedContent(embRichContent);
        } else {
            view.displayNotificationDetails();
        }
    }

    private FBNotificationsViewModel.RichContent getVideoContent(List<FBNotificationsViewModel.RichContent> richContents) {
        FBNotificationsViewModel.RichContent content = new FBNotificationsViewModel.RichContent();
        for (FBNotificationsViewModel.RichContent richContent : richContents) {
            if (richContent.getContentType().equalsIgnoreCase(NotificationConstants.CONTENT_TYPE.VIDEO)) {
                return richContent;
            }
        }
        return content;
    }


    private void showBannerFooterRichContent(List<FBNotificationsViewModel.RichContent> richContent, String displayType) {
        if (richContent.size() > 1) {
            view.setCorousalData(richContent, displayType);
        } else {
            view.showRichContent(richContent.get(0), displayType);
        }
    }

    private List<FBNotificationsViewModel.RichContent> filterRichContent(List<FBNotificationsViewModel.RichContent> richContents, String displayType) {
        List<FBNotificationsViewModel.RichContent> list = new ArrayList<>();
        for (FBNotificationsViewModel.RichContent richContent : richContents) {
            if (richContent.getDisplayType().equals(displayType)) {
                if (richContent.getContentType().equalsIgnoreCase(NotificationConstants.CONTENT_TYPE.VIDEO)) {
                    List<FBNotificationsViewModel.RichContent> videoList = new ArrayList<>();
                    videoList.add(richContent);
                    return videoList;
                }
                list.add(richContent);
            }
        }

        return list;
    }

    private void addRichContentUrl() {
        for (FBNotificationsViewModel.RichContent richContent : mRichContentList) {
            String basepath = view.getCMSBasePath(richContent.getSourceCms());
            if (basepath != null) {
                richContent.setUrl(basepath.concat(richContent.getSourcePath()));
            }
        }
    }


    private void handleRetentionFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_RETENTION)) {
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.RETENTION_TASK_SELECTION_NOTIFICATION_JOURNEY));
        } else {
            handleNavigationErrorFlow();
        }
    }


    void trackPageLoadOnAdobe() {
        HashMap<String, Object> cData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.setImpressions();
        mAnalytics.sendEventStateWithMap(TrackingEvent.ANALYTICS.PAGE_NAME_NOTIFICATION_DETAILS, cData);
    }

    public boolean isNotifDetailsShowed() {
        boolean isDetailsAlreadyshowed = memoryApplicationStorage.getBoolean(za.co.nedbank.core.Constants.IS_NOTIF_DETAILS_SHOWED, false);
        memoryApplicationStorage.clearValue(za.co.nedbank.core.Constants.IS_NOTIF_DETAILS_SHOWED);
        return isDetailsAlreadyshowed;
    }

    public FBNotificationsViewModel.ResponseOption getSelectedResponse() {
        return (FBNotificationsViewModel.ResponseOption) memoryApplicationStorage.getObject(NotificationConstants.STORAGE_KEYS.SELECTED_RESPONSE);
    }

    protected void fetchLoanApplications(String caseId, String partyId) {
        applicationListUseCase.execute(partyId)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgressVisible(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgressVisible(false);
                    }
                })
                .subscribe(applicationListResponseViewModel -> {
                    if (applicationListResponseViewModel != null
                            && CollectionUtils.isNotEmpty(applicationListResponseViewModel.getCases())) {
                        checkForPendingCases(caseId, applicationListResponseViewModel);
                    } else {
                        handleNavigationErrorFlow();
                    }
                }, this::handleException);
    }

    private void checkForPendingCases(String caseId, ApplicationListResponseViewModel applicationListResponseViewModel) {
        for (CaseViewModel caseViewModel : applicationListResponseViewModel.getCases()) {
            if (caseViewModel.getCaseID().equalsIgnoreCase(caseId)) {
                if (caseViewModel.getResumableWorkItems() != null && !caseViewModel.getResumableWorkItems().isEmpty()) {
                    navigateToWebView(caseViewModel.getResumableWorkItems().get(0).getWorkItemID());
                } else {
//                                    show popup
                    String agreementStatus = StringUtils.EMPTY_STRING;
                    if (caseViewModel.getArrangements() != null && CollectionUtils.isNotEmpty(caseViewModel.getArrangements())) {
                        agreementStatus = caseViewModel.getArrangements().get(0).getAgreementStatus();
                    }
                    navigateToLoanMessageScreen(caseViewModel.getCaseStatus(), agreementStatus);
                }
                return;
            }
        }
        navigateToLoanMessageScreen(StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    private void navigateToWebView(String workItem) {
        mNavigationRouter.navigateTo(
                NavigationTarget.to(NavigationTarget.NTF_WEBVIEW_ACTIVITY)
                        .withParam(za.co.nedbank.enroll_v2.Constants.TOKEN, APIInformation.getInstance().getToken())
                        .withParam(za.co.nedbank.enroll_v2.Constants.CR_FLOW, String.valueOf(BORROW_FLOW))
                        .withParam(ApplyFlowConstants.WORK_ITEM_ID, workItem)
        );
    }

    private boolean isFeatureAvailableForBusinessLoan() {
        return !mFeatureSetController.isFeatureDisabled(FTR_SMALL_BUSINESS_LENDING);
    }

    private void navigateToLoanMessageScreen(String caseStatus, String agreementStatus) {
        mNavigationRouter.navigateTo(
                NavigationTarget.to(EnrollV2NavigatorTarget.LENDING_PRODUCT_UNABLE_TO_PROCESS_ACTIVITY)
                        .withParam(KEY_CASE_STATUS, caseStatus)
                        .withParam(KEY_AGREEMENT_STATUS, agreementStatus));
    }

    private void handleBusinessLoanFlow(FBNotificationsViewModel.ResponseOption selectedResponse) {
        if (isFeatureAvailableForBusinessLoan()) {
            for (FBNotificationsViewModel.AdditionalParameter parameter : selectedResponse.getAdditionalParameters()) {
                if (NotificationConstants.ADDITIONAL_PARAM.WORK_ITEM_ID.equalsIgnoreCase(parameter.getName()) || NotificationConstants.ADDITIONAL_PARAM.CASE_ID.equalsIgnoreCase(parameter.getName())) {
                    if (NotificationConstants.ADDITIONAL_PARAM.CASE_ID.equalsIgnoreCase(parameter.getName())) {
                        UserDetailViewModel userDetailViewModel = (UserDetailViewModel) memoryApplicationStorage.getObject(ResponseStorageKey.USERDETAILDATA);
                        fetchLoanApplications(parameter.getValue(), userDetailViewModel.getCisNumber());
                    }
                } else {
                    handleNavigationErrorFlow();
                }
            }
        } else {
            handleNavigationErrorFlow();
        }
    }
}
