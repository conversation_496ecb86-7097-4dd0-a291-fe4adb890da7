/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.splashscreen;


import android.content.Context;

import java.util.List;
import java.util.Map;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.nid_sdk.main.interaction.TransaktConfig;

interface SplashScreenView extends NBBaseView {

    void showEntersektError();

    boolean isComingFromRootedFlow();

    boolean isShortcutScanPay();

    void setResult(Map<String, Object> params);

    void prepareAnimation();

    TransaktConfig getTransaktConfig();

    void setHMSConfigValues(String hmsSenderId,String token);

    void checkAppsFlyerSMSDeeplink();

    void initializePinPointSDK();

    boolean isAutoLogout();

    List<String> getInstalledAppsPackageName();

    Context getActivityContext();
}
