/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.money_request;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.ui.data.mapper.money_requests.MoneyRequestsActionResponseEntityToDataMapper;
import za.co.nedbank.ui.domain.mapper.PaymentActionDataModelToEntityMapper;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsActionDataModel;
import za.co.nedbank.ui.domain.model.money_request.PaymentActionDataModel;
import za.co.nedbank.ui.domain.repository.IMoneyRequestsActionRepository;

/**
 * Created by swapnil.gawande on 2/25/2018.
 */

public class MoneyRequestsActionUseCase extends UseCase<PaymentActionDataModel, MoneyRequestsActionDataModel> {
    private final IMoneyRequestsActionRepository moneyRequestsActionRepository;
    private final MoneyRequestsActionResponseEntityToDataMapper moneyRequestsActionResponseEntityToDataMapper;
    private final PaymentActionDataModelToEntityMapper paymentActionDataModelToEntityMapper;

    @Inject
    public MoneyRequestsActionUseCase(UseCaseComposer composer, IMoneyRequestsActionRepository moneyRequestsActionRepository,
                                      MoneyRequestsActionResponseEntityToDataMapper moneyRequestsActionResponseEntityToDataMapper, PaymentActionDataModelToEntityMapper paymentActionDataModelToEntityMapper) {
        super(composer);
        this.moneyRequestsActionRepository = moneyRequestsActionRepository;
        this.moneyRequestsActionResponseEntityToDataMapper = moneyRequestsActionResponseEntityToDataMapper;
        this.paymentActionDataModelToEntityMapper = paymentActionDataModelToEntityMapper;
    }

    @Override
    protected Observable<MoneyRequestsActionDataModel> createUseCaseObservable(PaymentActionDataModel paymentActionDataModel) {
        return moneyRequestsActionRepository.paymentRequestsAction(paymentActionDataModelToEntityMapper.mapPaymentActionViewToData(paymentActionDataModel))
                .map(moneyRequestsActionResponseEntityToDataMapper::mapMoneyRequestsMainResponseEntityToDataModel);
    }
}
