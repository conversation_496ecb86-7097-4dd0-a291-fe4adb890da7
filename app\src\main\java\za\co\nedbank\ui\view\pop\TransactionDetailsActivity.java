package za.co.nedbank.ui.view.pop;

import static za.co.nedbank.core.navigation.NavigationTarget.TRANSACTION_HISTORY_DETAIL;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.google.android.material.snackbar.Snackbar;

import java.text.DecimalFormat;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.dialog.IDialog;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseDetailsViewModel;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;
import za.co.nedbank.databinding.ActivityTransactionDetailsPopBinding;
import za.co.nedbank.payment.common.view.PaymentPayzarFlowInfoDialog;
import za.co.nedbank.payment.common.view.tracking.PaymentsTrackingEvent;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.component.CompatButton;
import za.co.nedbank.uisdk.widget.NBSnackbar;

public class TransactionDetailsActivity extends NBBaseActivity implements TransactionDetailsView {

    @Inject
    TransactionDetailsPresenter mPresenter;
    private TransactionHistoryViewModel transactionHistoryViewModel;
    private boolean isFromRecentPaymentFlow = false;
    private boolean isLandingOnOverView = false;
    private boolean isOnceoffFromTransaction = false;
    private boolean isOnceoffFromOverviewTransaction ;
    private static final int DECIMAL_LENGTH = 2;
    private ActivityTransactionDetailsPopBinding binding;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityTransactionDetailsPopBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        mPresenter.bind(this);
        initToolbar(binding.toolbar, true, true);

        if (getIntent() != null) {
            try {
                transactionHistoryViewModel = getIntent().getParcelableExtra(Constants.BUNDLE_KEYS.TRANSACTION_HISTORY_VIEW_MODEL);
                isFromRecentPaymentFlow = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_RECENT_PAYMENT_FLOW, false);
                isLandingOnOverView = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_LANDING_ON_OVERVIEW, false);
                isOnceoffFromTransaction = getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_ONCE_OFF_FROM_TRANSACTIONS, false);
                isOnceoffFromOverviewTransaction=getIntent().getBooleanExtra(Constants.BUNDLE_KEYS.IS_OVERVIEW_TRANSACTION_ONCEOFF_FLOW, false);
            } catch (Exception e) {
                NBLogger.e("Exception:","TransactionDetailActivity",e);
            }
        }
        mPresenter.handleButtonVisibility();
        binding.payAgainBtn.setOnClickListener(v -> handlePayAgainBtnClick());
        binding.sharePopBtn.setOnClickListener(v -> handleSharePOPBtnClick());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        memoryApplicationStorage.clearValue(za.co.nedbank.core.Constants.PAYMENT_START_FROM);
        mPresenter.unbind();
    }

    @Override
    protected void onResume() {
        super.onResume();

        if (transactionHistoryViewModel != null) {
            setUpViewAsPerIntentData();
            if (isFromRecentPaymentFlow) {
                setUpViewForRecentPaymentData();
            }
        }
    }

    public void handleSharePOPBtnClick() {
        mPresenter.navigateToSharePOPScreen(transactionHistoryViewModel, isFromRecentPaymentFlow,isLandingOnOverView, isBeyond90Days());
    }

    public void handlePayAgainBtnClick() {
        if (isOnceoffFromTransaction) {
            mPresenter.checkLocForOnceOff();
        } else {
            handlePayAgainClick();
        }
    }


    @Override
    public void openPopForInterNationalPayment(String bankName) {
        mPresenter.trackCmaActionOnOpen(bankName);
        final PaymentPayzarFlowInfoDialog paymentPayzarFlowInfoDialog = PaymentPayzarFlowInfoDialog.getInstance(new IDialog() {
            @Override
            public void onPositiveButtonClick() {
                mPresenter.fetchProfileForInternationalPaymentTnxFlow(TrackingEvent.ANALYTICS.CMA_POP_UP);
                mPresenter.trackCmaActionOnContinueClick();
            }

            @Override
            public void onNegativeButtonClick() {
                // Handle cancel event if required
                mPresenter.trackCmaActionOnCancelClick();
            }
        });
        if (!paymentPayzarFlowInfoDialog.isVisible()) {
            paymentPayzarFlowInfoDialog.showAllowingStateLoss(getSupportFragmentManager(), PaymentPayzarFlowInfoDialog.TAG);
        }
    }

    @Override
    public void checkRecentPaymentTransaction() {
        if (isFromRecentPaymentFlow) {
            mPresenter.getRecentTransactionDetails(transactionHistoryViewModel.getTransactionID());
        } else {
            mPresenter.navigateToPaymentAmount(transactionHistoryViewModel, isOnceoffFromTransaction);
        }
    }

    @Override
    public void handlePayAgainClick() {
        if (transactionHistoryViewModel == null) return;
        memoryApplicationStorage.putString(za.co.nedbank.core.Constants.PAYMENT_START_FROM, TRANSACTION_HISTORY_DETAIL);
        if (StringUtils.isNotEmpty(transactionHistoryViewModel.getSortCode())) {
            mPresenter.checkForCmaFlow(transactionHistoryViewModel.getSortCode(), transactionHistoryViewModel.getBankName());
        } else if (StringUtils.isNotEmpty(transactionHistoryViewModel.getBranchCode())) {
            mPresenter.checkForCmaFlow(transactionHistoryViewModel.getBranchCode(), transactionHistoryViewModel.getBankName());
        }else {
            if (isFromRecentPaymentFlow) {
                mPresenter.getRecentTransactionDetails(transactionHistoryViewModel.getTransactionID());
            } else {
                mPresenter.navigateToPaymentAmount(transactionHistoryViewModel, isOnceoffFromTransaction);
            }
        }
    }



    @Override
    public boolean isOnceOffFromOverViewTransaction() {
        return isOnceoffFromOverviewTransaction;
    }

    @Override
    public String getAccountType(TransactionHistoryViewModel transactionHistoryViewModel) {
        if (transactionHistoryViewModel != null) {
            if (PaymentsTrackingEvent.CONSTANT_CC.equalsIgnoreCase(transactionHistoryViewModel.getAccountType())) {
                return PaymentsTrackingEvent.VAL_CREDIT_CARD;
            } else if(BeneficiaryConstants.BENEFICIARY_TYPE_PREPAID.equalsIgnoreCase(transactionHistoryViewModel.getBeneficiaryType())) {
                return PaymentsTrackingEvent.VAL_IMALI;
            } else {
                return PaymentsTrackingEvent.VAL_BANK_ACCOUNT;
            }
        }
        return StringUtils.EMPTY_STRING;
    }

    @Override
    public String getDataToAccountName() {
        String toAccountName = StringUtils.EMPTY_STRING;
        if (transactionHistoryViewModel == null) return toAccountName;

        if (transactionHistoryViewModel.isGovernmentPayment() && !TextUtils.isEmpty(transactionHistoryViewModel.getBeneficiaryName())) {
            toAccountName = transactionHistoryViewModel.getBeneficiaryName();
        } else {
            String bfType = transactionHistoryViewModel.getBeneficiaryType();
            if (bfType == null) {
                bfType = transactionHistoryViewModel.getAccountType();
            }
            if (BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF.equalsIgnoreCase(bfType)) {
                toAccountName = BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF;
                if (StringUtils.isNotEmpty(transactionHistoryViewModel.getBankName()))
                    toAccountName = transactionHistoryViewModel.getBankName() + StringUtils.HYPHEN + BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF;
            } else if (BeneficiaryConstants.BENEFICIARY_TYPE_EXTERNAL.equalsIgnoreCase(bfType) && StringUtils.isNotEmpty(transactionHistoryViewModel.getBankName())) {
                toAccountName = transactionHistoryViewModel.getBankName();
            } else {
                toAccountName = fetchMappedToAccountName(bfType);
            }
        }
        return toAccountName;
    }

    private String fetchMappedToAccountName(String bfType) {
        String toAccountName = StringUtils.EMPTY_STRING;
        if (transactionHistoryViewModel != null && StringUtils.isNotEmpty(transactionHistoryViewModel.getAccountType())) {
            String toAccountType = transactionHistoryViewModel.getAccountType();
            za.co.nedbank.core.Constants.ACCOUNT_TYPES accountTypeSelected;
            try {
                accountTypeSelected = za.co.nedbank.core.Constants.ACCOUNT_TYPES.valueOf(toAccountType);
            } catch (IllegalArgumentException illegalArgumentException) {
                accountTypeSelected = za.co.nedbank.core.Constants.ACCOUNT_TYPES.U0;
            }
            toAccountName = getString(accountTypeSelected.getAccountTypeStringResId());

            if (BeneficiaryConstants.BENEFICIARY_TYPE_INTERNAL.equalsIgnoreCase(bfType)) {
                toAccountName = PaymentsTrackingEvent.VAL_NEDBANK + StringUtils.SPACE + toAccountName;
            }
        }
        return toAccountName;
    }

    @Override
    public String fetchRecipientType() {
        if (transactionHistoryViewModel == null) return PaymentsTrackingEvent.VAL_EXISTING;

        String bfType = transactionHistoryViewModel.getBeneficiaryType();
        if (bfType == null) {
            bfType = transactionHistoryViewModel.getAccountType();
        }
        if (BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF.equalsIgnoreCase(bfType)) {
            return PaymentsTrackingEvent.VAL_BANK_APPROVED;
        } else
            return PaymentsTrackingEvent.VAL_EXISTING;
    }

    private void setUpViewAsPerIntentData() {
        if (!TextUtils.isEmpty(transactionHistoryViewModel.getBfName())) {
            binding.layoutTransactionsDetailCommonPop.transactionRecipientText.setText(transactionHistoryViewModel.getBfName());
        } else {
            binding.layoutTransactionsDetailCommonPop.transactionRecipientText.setText("-");
        }

        if (!TextUtils.isEmpty(transactionHistoryViewModel.getMyReference())) {
            binding.layoutTransactionsDetailCommonPop.transactionYourReferenceText.setText(transactionHistoryViewModel.getMyReference());
        } else {
            binding.layoutTransactionsDetailCommonPop.transactionYourReferenceText.setText("-");
        }

        if (!TextUtils.isEmpty(transactionHistoryViewModel.getStartDate())) {
            binding.layoutTransactionsDetailCommonPop.transactionDateText.setText(String.valueOf(FormattingUtil.getFormattedDate(transactionHistoryViewModel.getStartDate(), Constants.DATE_FORMAT.SERVER_DATE_FORMAT, Constants.DATE_FORMAT.DISPLAY_DD_MMMM_YYYY)));
        } else {
            binding.layoutTransactionsDetailCommonPop.transactionDateText.setText("-");
        }
        handleTransactionAmount();
        if (!TextUtils.isEmpty(transactionHistoryViewModel.getTransactionType())) {
            binding.layoutTransactionsDetailCommonPop.transactionTypeText.setText(transactionHistoryViewModel.getTransactionType());

            if (!Constants.PAYMENT.equalsIgnoreCase(transactionHistoryViewModel.getTransactionType())) {
                binding.sharePopBtn.setVisibility(View.INVISIBLE);
                binding.payAgainBtn.setVisibility(View.GONE);
            }
        } else {
            binding.layoutTransactionsDetailCommonPop.transactionTypeText.setText("-");
        }

        if (!TextUtils.isEmpty(transactionHistoryViewModel.getBankName())) {
            binding.layoutTransactionsDetailCommonPop.transactionBankNameText.setText(transactionHistoryViewModel.getBankName());
        } else {
            ViewUtils.hideViews(binding.layoutTransactionsDetailCommonPop.transactionBankName);
        }
        if (shouldShowShapId()) {
            setShapID();
        } else {
            setAccountNumber();
        }
        if (transactionHistoryViewModel.isRapidPayment()) {
            binding.sharePopBtn.setEnabled(true);
            binding.payAgainBtn.setEnabled(false);
        }
    }

    private boolean shouldShowShapId() {
        return transactionHistoryViewModel.isRapidPayment() && !StringUtils.isNullOrEmpty(transactionHistoryViewModel.getShapID());
    }

    private void setShapID() {
        binding.layoutTransactionsDetailCommonPop.transactionAccountNumberLabel.setText(getResources().getString(R.string.shap_id));
        binding.layoutTransactionsDetailCommonPop.transactionAccountNumberText.setText(transactionHistoryViewModel.getShapID());
    }

    private void handleTransactionAmount() {
        DecimalFormat format = new DecimalFormat("0.00");
        String amountText = format.format(transactionHistoryViewModel.getAmount());
        amountText = FormattingUtil.convertFromSouthAfricaCurrency(amountText);
        binding.layoutTransactionsDetailCommonPop.transactionAmountText.setText(new StringBuilder().append("-").append(FormattingUtil.convertAmountToSACurrency(amountText, "R", DECIMAL_LENGTH)).toString());
    }

    private void setUpViewForRecentPaymentData() {
        if (transactionHistoryViewModel.getBeneficiaryID() == 0) {
            ViewUtils.hideViews(binding.layoutTransactionsDetailCommonPop.recipientView);
        }

        binding.layoutTransactionsDetailCommonPop.transactionYourReferenceLabel.setText(R.string.their_reference);
        if (!TextUtils.isEmpty(transactionHistoryViewModel.getBeneficiaryDescription())) {
            binding.layoutTransactionsDetailCommonPop.transactionYourReferenceText.setText(transactionHistoryViewModel.getBeneficiaryDescription());
        } else {
            binding.layoutTransactionsDetailCommonPop.transactionYourReferenceText.setText("-");
        }
        binding.layoutTransactionsDetailCommonPop.transactionAmountText.setText(FormattingUtil.convertToSouthAfricaFormattedCurrency(transactionHistoryViewModel.getAmount()));
        if (transactionHistoryViewModel.getAccountType() != null && transactionHistoryViewModel.getAccountType().equals(Constants.ACCOUNT_TYPES.CC.getAccountTypeCode())) {
            binding.layoutTransactionsDetailCommonPop.transactionAccountNumberLabel.setText(R.string.credit_card_number);
        }

        transactionHistoryViewModel.setFromRecentPayment(true);
        transactionHistoryViewModel.setFromOverViewOnceOff(isLandingOnOverView);
        mPresenter.checkFeaturePayAgainRecentTransaction();

        if(transactionHistoryViewModel.isGovernmentPayment()){
            handleButtonVisibility(true,false,false);
        }

    }

    @Override
    public void handleButtonVisibility(boolean isDisable, boolean isTransactionBeyond90, boolean isSharePOPBeyond90Disable) {
        if (isTransactionBeyond90) {
            if (isSharePOPBeyond90Disable) {
                ViewUtils.hideViews(binding.payAgainBtn, binding.sharePopBtn);
            } else {
                ViewUtils.hideViews(binding.payAgainBtn);
            }
        } else if (isDisable) {
            binding.payAgainBtn.setVisibility(View.GONE);
            binding.sharePopBtn.setButtonType(CompatButton.ButtonType.PRIMARY);
        } else {
            binding.payAgainBtn.setVisibility(View.VISIBLE);
            binding.payAgainBtn.setButtonType(CompatButton.ButtonType.PRIMARY);
            binding.sharePopBtn.setButtonType(CompatButton.ButtonType.SECONDARY);
        }
        setInstantPay();
    }


    @Override
    public boolean isBeyond90Days(){
        if (transactionHistoryViewModel != null) {
            return FormattingUtil.isDateAfter90Days(transactionHistoryViewModel.getStartDate());
        }
        return false;
    }

    @Override
    public void setTransactionDetailData(RecentPaymentResponseDetailsViewModel recentPaymentResponseDetailData) {
        if (transactionHistoryViewModel != null && recentPaymentResponseDetailData != null && recentPaymentResponseDetailData.getData() != null) {
            if(recentPaymentResponseDetailData.getData().getBeneficiaryID() != 0) {
                if (recentPaymentResponseDetailData.getData().getBfType() == null) {
                    transactionHistoryViewModel.setBeneficiaryID(0);
                } else {
                    transactionHistoryViewModel.setBeneficiaryType(recentPaymentResponseDetailData.getData().getBfType());
                }
            }
            transactionHistoryViewModel.setmInstantPayment(recentPaymentResponseDetailData.getData().ismInstantPayment());
            transactionHistoryViewModel.setNotificationDetailsViewModels(mPresenter.buildNotificationModel(recentPaymentResponseDetailData.getData().getNotificationDetailsViewModels()));
        }

        mPresenter.navigateToPaymentAmount(transactionHistoryViewModel, isOnceoffFromTransaction);
    }

    @Override
    public void handleProgressBar(boolean isVisible) {
        binding.payAgainBtn.setLoadingVisible(isVisible);
        setEnabledActivityTouch(!isVisible);
    }

    @Override
    public void errorSnackBar() {
        showError(getString(R.string.error), getString(R.string.something_went_wrong),
                getString(R.string.snackbar_action_ok), Snackbar.LENGTH_INDEFINITE, () -> NBSnackbar.instance().dismissSnackBar());
    }


    private void setAccountNumber()
    {
        if (!TextUtils.isEmpty(transactionHistoryViewModel.getAccountType()) && Constants.ACCOUNT_TYPES.BDF.getAccountTypeCode().equalsIgnoreCase(transactionHistoryViewModel.getAccountType())) {
            binding.layoutTransactionsDetailCommonPop.transactionAccountNumber.setVisibility(View.GONE);
        } else if (!TextUtils.isEmpty(transactionHistoryViewModel.getBeneficiaryAccount())) {
            binding.layoutTransactionsDetailCommonPop.transactionAccountNumberText.setText(transactionHistoryViewModel.getBeneficiaryAccount());
        } else {
            binding.layoutTransactionsDetailCommonPop.transactionAccountNumberText.setText("-");
        }
    }

    private void setInstantPay()
    {
        if (transactionHistoryViewModel != null) {
            if (transactionHistoryViewModel.ismInstantPayment() && getString(R.string.sok).equalsIgnoreCase(transactionHistoryViewModel.getStatus())) {
                binding.sharePopBtn.setEnabled(false);
            } else if (transactionHistoryViewModel.ismInstantPayment() && getString(R.string.eok).equalsIgnoreCase(transactionHistoryViewModel.getStatus())) {
                binding.sharePopBtn.setEnabled(true);
            }
        }
    }
}