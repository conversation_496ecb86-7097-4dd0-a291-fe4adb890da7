package za.co.nedbank.ui.view.home.add_recipient;

import static za.co.nedbank.core.constants.BeneficiaryConstants.BENEFICIARY_TYPE_RPP;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.sharedui.control.CustomLinearLayout;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewModel;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.ShapIdValidator;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.view.recipient.ShapIDViewDataModel;
import za.co.nedbank.uisdk.component.CompatEditText;
import za.co.nedbank.uisdk.component.CompatTextView;

public class ShapIDAdapter extends NBFlexibleItemCountRecyclerviewAdapter {
    private BankAccountAdapter.IActivityAdapterComListener mIActivityAdapterComListener;

    public void setIActivityAdapterComListener(BankAccountAdapter.IActivityAdapterComListener listener) {
        this.mIActivityAdapterComListener = listener;
    }

    public ShapIDAdapter(Context context, NBFlexibleItemCountRecyclerviewModel nbFlexibleItemCountRecyclerviewModel, List<NbRecyclerViewBaseDataModel> nbRecyclerViewBaseDataModelList, IAdapterInteractionListener adapterInteractionListener, ISectionedListItemSelectedListener itemSelectedListener) {
        super(context, nbFlexibleItemCountRecyclerviewModel, nbRecyclerViewBaseDataModelList, adapterInteractionListener, itemSelectedListener);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(Context context, ViewGroup parent) {
        View v = LayoutInflater.from(context).inflate(mNBFlexibleItemCountRecyclerviewModel.getItemView(), parent, false);
        VHShapIdItem vhShapIdItem = new VHShapIdItem(v);

        if (vhShapIdItem.etShapId != null) {
            vhShapIdItem.etShapId.getInputField().setContentDescription(" ");
        }
        return vhShapIdItem;
    }

    private void editableView(VHShapIdItem vhShapIdItem, ShapIDViewDataModel shapIdViewDataModel) {
        if (vhShapIdItem.etShapId != null) {
            vhShapIdItem.etShapId.getInputField().setTransformationMethod(new CompatTextView.NBBlockCopyMethod());
            if (!shapIdViewDataModel.isExistingItem()) {
                vhShapIdItem.etShapId.requestFocus();
                if (shapIdViewDataModel.isSendAccessibilityEvent() && AccessibilityUtils.isAccessibilityServiceEnabled(mContext)) {
                    vhShapIdItem.etShapId.postDelayed(() ->
                            vhShapIdItem.etShapId.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED), 500);
                    shapIdViewDataModel.setSendAccessibilityEvent(false);
                }
            }
        }
    }

    private void unEditableView(VHShapIdItem vhShapIdItem) {
        if (vhShapIdItem.etShapId != null) {
            vhShapIdItem.etShapId.getInputField().setTransformationMethod(null);
            vhShapIdItem.etShapId.setBackgroundColor(ContextCompat.getColor(vhShapIdItem.etShapId.getContext(), android.R.color.transparent));
        }
        if (vhShapIdItem.yourRef != null) {
            vhShapIdItem.yourRef.setBackgroundColor(ContextCompat.getColor(vhShapIdItem.yourRef.getContext(), android.R.color.transparent));
        }
        if (vhShapIdItem.recipientReference != null) {
            vhShapIdItem.recipientReference.setBackgroundColor(ContextCompat.getColor(vhShapIdItem.recipientReference.getContext(), android.R.color.transparent));
        }
    }

    private void setValueToview(VHShapIdItem vhShapIdItem, ShapIDViewDataModel shapIdViewDataModel) {
        if (vhShapIdItem.etShapId != null) {
            vhShapIdItem.etShapId.setText(ShapIdValidator.getProxyNameView(shapIdViewDataModel.getShapid(),shapIdViewDataModel.getProxyDomain()), isEditable());
        }
        if (vhShapIdItem.yourRef != null) {
            vhShapIdItem.yourRef.setText(shapIdViewDataModel.getYourReference(), isEditable());
        }
        if (vhShapIdItem.recipientReference != null) {
            vhShapIdItem.recipientReference.setText(shapIdViewDataModel.getRecipientReference(), isEditable());
        }
    }

    private void setViewAppearance(VHShapIdItem vhShapIdItem) {
        if (isEditable() && mNBFlexibleItemCountRecyclerviewModel.isDeleteButtonShow()) {
            vhShapIdItem.ivRemove.setVisibility(View.VISIBLE);
        } else {
            vhShapIdItem.ivRemove.setVisibility(View.GONE);
        }
        if (vhShapIdItem.etShapId != null) {
            vhShapIdItem.etShapId.setFocusable(isEditable());
            vhShapIdItem.etShapId.setEnabled(isEditable());
        }
        if (vhShapIdItem.yourRef != null) {
            vhShapIdItem.yourRef.setFocusable(isEditable());
            vhShapIdItem.yourRef.setEnabled(isEditable());
        }
        if (vhShapIdItem.recipientReference != null) {
            vhShapIdItem.recipientReference.setFocusable(isEditable());
            vhShapIdItem.recipientReference.setEnabled(isEditable());
        }
    }


    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (position > 0 && (position - 1) < mNbRecyclerViewBaseDataModelList.size() && mNbRecyclerViewBaseDataModelList.get(position - 1) instanceof ShapIDViewDataModel) {
            ShapIDViewDataModel shapIdViewDataModel = (ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(position - 1);
            ShapIDAdapter.VHShapIdItem vhShapIdItem = ((ShapIDAdapter.VHShapIdItem) holder);
            setValueToview(vhShapIdItem, shapIdViewDataModel);
            vhShapIdItem.ivRecipientTypeIcon.setVisibility(isEditable() ? View.GONE : View.VISIBLE);
            if (isEditable()) {
                editableView(vhShapIdItem, shapIdViewDataModel);
            } else {
                unEditableView(vhShapIdItem);

            }
            setViewAppearance(vhShapIdItem);
            vhShapIdItem.llRootView.setViewGroupClickListener(mItemSelectedListener != null ? mViewInterceptListener : null);
            vhShapIdItem.addListenerForShapId(vhShapIdItem.etShapId);
            vhShapIdItem.addListenerForShapIdYourReference(vhShapIdItem.yourRef);
            vhShapIdItem.addListenerForShapIdRecipientReference(vhShapIdItem.recipientReference);
            vhShapIdItem.llRootView.setOnClickListener(v -> vhShapIdItem.handleItemSelected());
            vhShapIdItem.ivRemove.setOnClickListener(v -> vhShapIdItem.onClickOfRemoveImageView());
            if (shapIdViewDataModel.getMatchBackNumber() == 0 && mIActivityAdapterComListener != null) {
                shapIdViewDataModel.setMatchBackNumber(mIActivityAdapterComListener.getMatchBackNumber());
            }
        } else {
            super.onBindViewHolder(holder, position);
        }
    }

    @Override
    protected void addItem() {
        ShapIDViewDataModel shapIDViewDataModel = new ShapIDViewDataModel();
        mNbRecyclerViewBaseDataModelList.add(mNbRecyclerViewBaseDataModelList.size(), shapIDViewDataModel);
        notifyItemInserted(mNbRecyclerViewBaseDataModelList.size());
    }

    class VHShapIdItem extends RecyclerView.ViewHolder {
        CompatEditText etShapId;
        ImageView ivRecipientTypeIcon;
        CustomLinearLayout llRootView;
        ImageView ivRemove;
        CompatEditText yourRef;
        CompatEditText recipientReference;

        public void handleItemSelected() {
            if (mItemSelectedListener != null) {
                mItemSelectedListener.itemSelected(VIEW_TYPE.SHAPID.ordinal(), getBindingAdapterPosition() - 1);
            }
        }

        void onClickOfRemoveImageView() {
            int pos = getBindingAdapterPosition() - 1;
            if (pos >= 0 && pos < mNbRecyclerViewBaseDataModelList.size()) {
                removeItem(getBindingAdapterPosition(), mNbRecyclerViewBaseDataModelList.get(pos).isExistingItem());
            }
        }

        VHShapIdItem(View itemView) {
            super(itemView);
            etShapId = itemView.findViewById(R.id.et_shapid);
            ivRecipientTypeIcon = itemView.findViewById(R.id.iv_recipient_icon);
            llRootView = itemView.findViewById(R.id.ll_root_view);
            ivRemove = itemView.findViewById(R.id.iv_remove);
            yourRef = itemView.findViewById(R.id.et_your_reference);
            recipientReference = itemView.findViewById(R.id.et_recipient_reference);
        }

        void addListenerForShapIdYourReference(CompatEditText compatYourRefEditText) {

            RxTextView.textChanges(compatYourRefEditText.getInputField()).subscribe(chars -> {
                compatYourRefEditText.clearErrors();
                ((ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setYourReference(compatYourRefEditText.getValue());
                if (mINBRecyclerViewListener != null) {

                    mINBRecyclerViewListener.onItemDataChange();
                }
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatYourRefEditText.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatYourRefEditText, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });

        }

        void addListenerForShapId(CompatEditText compatEdtShapIdNumber) {

            RxTextView.textChanges(compatEdtShapIdNumber.getInputField()).subscribe(chars -> {
                compatEdtShapIdNumber.clearErrors();
                ((ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setBeneficiaryType(BENEFICIARY_TYPE_RPP);
                ((ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setShapid(compatEdtShapIdNumber.getValue());
                String customProxy = compatEdtShapIdNumber.getValue();
                if (StringUtils.isNotEmpty(customProxy) && customProxy.contains(StringUtils.AT_THE_RATE_CHAR) && customProxy.split(StringUtils.AT_THE_RATE_CHAR).length > 1) {
                    ((ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setProxyDomain(compatEdtShapIdNumber.getValue().split(StringUtils.AT_THE_RATE_CHAR)[1]);
                }
                if (mINBRecyclerViewListener != null) {

                    mINBRecyclerViewListener.onItemDataChange();
                }
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtShapIdNumber.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtShapIdNumber, Validator.ValidatorType.SHAP_ID_VALIDATOR);
                }
            });

        }


        void addListenerForShapIdRecipientReference(CompatEditText compatEdtShapIdRecipientReference) {

            RxTextView.textChanges(compatEdtShapIdRecipientReference.getInputField()).subscribe(chars -> {
                compatEdtShapIdRecipientReference.clearErrors();
                ((ShapIDViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setRecipientReference(compatEdtShapIdRecipientReference.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtShapIdRecipientReference.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtShapIdRecipientReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });

            RxTextView.editorActions(compatEdtShapIdRecipientReference.getInputField())
                    .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                    .subscribe(chars -> {
                        ViewUtils.hideSoftKeyboard(mContext, compatEdtShapIdRecipientReference);
                        compatEdtShapIdRecipientReference.clearFocus();
                        mIActivityAdapterComListener.validateInput(compatEdtShapIdRecipientReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                    }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        }

    }
}
