package za.co.nedbank.ui.view.home.latest;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.dashboard.HomeWidget;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;

public interface HomeLatestView extends NBBaseView {
    void showProgress(boolean progress);
    void showMediaAndOfferCards(List<AppLayoutViewModel> mediaContentList);
    void showLatestApiError(String message);
    void updateChatIcon(int count);
    void onUnreadChatEvent(UnreadChatEvent unreadChatEvent);
    void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent);

    void setPreferredName(String name);
    void handlePreferredNameError();
    void setCustomerName(String customerUserName);
    void setUserInfo(UserDetailViewModel userDetailViewModel);
    HomeWidget getFromWidgetStatus();
}
