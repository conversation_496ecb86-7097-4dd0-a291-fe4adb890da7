package za.co.nedbank.ui.notifications;

import android.app.Notification;
import android.app.NotificationManager;
import android.content.Context;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;

public class NotificationHelper {

    private final NotificationBuilder notificationBuilder;
    private final NotificationItemResolver notificationItemResolver;
    private final NotificationManager notificationManager;

    @Inject
    public NotificationHelper(NotificationManager notificationManager, NotificationBuilder notificationBuilder, NotificationItemResolver notificationItemResolver) {
        this.notificationBuilder = notificationBuilder;
        this.notificationItemResolver = notificationItemResolver;
        this.notificationManager = notificationManager;
    }

    public void generateNotification(Context context, NotificationData notificationData) {

        PushNotificationItem notificationItem = notificationItemResolver.resolve(context, notificationData);
        Observable.fromCallable(() -> notificationBuilder.build(context, notificationItem))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result -> {
                    Notification notification = result.build();
                    notificationManager.notify(notificationData.getNotificationId(), notification);
                }, throwable -> NBLogger.e("NotificationHelper", throwable.getMessage()));

    }

    public void generateAJONotification(Context context, AjoPushPayloadDataModel payload) {
        PushNotificationItem notificationItem = notificationItemResolver.resolveAJO(context, payload);
        Observable.fromCallable(() -> notificationBuilder.buildAJO(context, notificationItem))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result -> {
                    Notification notification = result.build();
                    notificationManager.notify(payload.getMessageId().hashCode(), notification);
                }, throwable -> NBLogger.e("NotificationHelper", throwable.getMessage()));
    }
}
