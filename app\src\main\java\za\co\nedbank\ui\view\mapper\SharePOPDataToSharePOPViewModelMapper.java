package za.co.nedbank.ui.view.mapper;


import javax.inject.Inject;

import za.co.nedbank.core.domain.mapper.MetaDataDataToViewModelMapper;
import za.co.nedbank.ui.domain.model.pop.SharePOPResponseData;
import za.co.nedbank.ui.view.model.ShareProofOfPaymentResponseViewModel;

public class SharePOPDataToSharePOPViewModelMapper {
    private final MetaDataDataToViewModelMapper mMetaDataDataToViewModelMapper;

    @Inject
    public SharePOPDataToSharePOPViewModelMapper(MetaDataDataToViewModelMapper metaDataDataToViewModelMapper) {
        this.mMetaDataDataToViewModelMapper = metaDataDataToViewModelMapper;
    }

    public ShareProofOfPaymentResponseViewModel mapSharePOPDataToSharePOPViewModel(SharePOPResponseData sharePOPResponseData) {
        ShareProofOfPaymentResponseViewModel shareProofOfPaymentResponseViewModel = new ShareProofOfPaymentResponseViewModel();
        if (sharePOPResponseData != null) {
            shareProofOfPaymentResponseViewModel.setMetadata(mMetaDataDataToViewModelMapper.mapMetaData(sharePOPResponseData.getMetadata()));
        }
        return shareProofOfPaymentResponseViewModel;
    }

}
