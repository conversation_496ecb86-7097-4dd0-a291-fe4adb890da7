package za.co.nedbank.ui.view.card_delivery.locker_map;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.domain.model.location.AddressSuggestion;
import za.co.nedbank.core.domain.model.location.PlaceDetails;
import za.co.nedbank.core.view.map.variants.XLatLng;

public interface LockerMapView extends NBBaseView {

    void hideAddressSuggestions();

    void showSuggestionFailedError(boolean showError);

    void showAddressSuggestions(List<AddressSuggestion> suggestions);

    boolean isMapServiceAvailable();

    void setMapNotAvailableInfoVisible(boolean isVisible);

    void initMap();


    void enableGeolocation(boolean isPermission);

    void setCurrentSearchLatLong(XLatLng latLng);

    void setIsManualSearch(boolean isManualSearch);

    void finishSearchEditing(AddressSuggestion suggestion);

    void showPlaceLookupFailedError();

    void resetUI();

    void navigateToPlace(PlaceDetails placeDetails, boolean zoomIn);

    void showResultPlacesOnMap(List<PlaceDetails> placeDetails);

    void showLoadingInProgess(boolean isVisible);

    void showNearByLockerAPIFailedError();

    void showManualSearchLockerAPIFailedError();

    void clearPlaceSelection();

    void showPlaceSelected(PlaceDetails placeDetails);

    void showPlaceDetails(PlaceDetails placeDetails);

    void showResultPlacesOnBottonSheet(List<PlaceDetails> lockers);

    boolean isEficaFlow();

    String getCardDeliverySubFeature();

    String getCardActionName();
}
