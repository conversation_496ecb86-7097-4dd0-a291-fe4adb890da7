/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.overview;

import static za.co.nedbank.core.Constants.BaseInfoIntentForFicaSDK.FICA;
import static za.co.nedbank.core.Constants.CODE_N;
import static za.co.nedbank.core.Constants.DEFAULT_ACCOUNT_IDENTIFIER;
import static za.co.nedbank.core.Constants.DEFAULT_ACCOUNT_ID_KEY;
import static za.co.nedbank.core.Constants.HTTPS_STRING;
import static za.co.nedbank.core.Constants.HTTP_STRING;
import static za.co.nedbank.core.Constants.JUST_INVEST;
import static za.co.nedbank.core.Constants.MONEY_TRADER;
import static za.co.nedbank.core.Constants.NEDBANK_ELECTRONIC_32_DAY_NOTICE;
import static za.co.nedbank.core.Constants.NOTICE_DESPOSIT_32;
import static za.co.nedbank.core.Constants.ONE;
import static za.co.nedbank.core.Constants.PLATINUM_INVEST;
import static za.co.nedbank.core.Constants.PREFERRED_NAME_KEY;
import static za.co.nedbank.core.Constants.VALUE_ADDED_OFFERS;
import static za.co.nedbank.core.Constants.ZOOM_SDK_VERSION_KEY;
import static za.co.nedbank.core.data.storage.StorageKeys.CLIENT_TYPE;
import static za.co.nedbank.core.data.storage.StorageKeys.IS_PAYSHAP_WIDGET_ENABLED;
import static za.co.nedbank.core.data.storage.StorageKeys.TRAVEL_CARD_MINOR;
import static za.co.nedbank.core.navigation.NavigationTarget.IS_FROM_HOME;
import static za.co.nedbank.core.navigation.NavigationTarget.IS_RETAIL;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_BIRTH_DATE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_CLIENT_TYPE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD;
import static za.co.nedbank.core.tracking.TrackingParam.VAL_MT_CLIENT_TYPE_PREFIX;
import static za.co.nedbank.core.utils.FormattingUtil.AGE_LIMIT;
import static za.co.nedbank.loans.preapprovedaccountsoffers.tracking.PreApprovedOffersTrackingEvent.TAG_DASHBOARD_FOR_YOU_OFFER_CLICK;
import static za.co.nedbank.profile.common.Constants.GREENBACKS_CURRENCY;
import static za.co.nedbank.services.Constants.AvoShopConstants.AVO_HOME_URL_PROD;
import static za.co.nedbank.services.Constants.AvoShopConstants.AVO_HOME_URL_QA;
import static za.co.nedbank.services.Constants.MERCHANT_SERVICES;
import static za.co.nedbank.services.Constants.OVERVIEW_FAMILY_BANKING;
import static za.co.nedbank.services.Constants.OVERVIEW_FCA_TITLE;
import static za.co.nedbank.services.Constants.OVERVIEW_FINANCIAL_BENEFITS;
import static za.co.nedbank.services.Constants.OVERVIEW_INCOMING_PAYMENTS;
import static za.co.nedbank.services.Constants.OVERVIEW_INCOMING_PAYMENTS_SBS;
import static za.co.nedbank.services.discsandfines.tracking.DiscAndFinesTracking.ADDITIONAL_SERVICES;
import static za.co.nedbank.services.discsandfines.tracking.DiscAndFinesTracking.DISC_RENEWAL_AND_FINES;
import static za.co.nedbank.services.discsandfines.tracking.DiscAndFinesTracking.MYACCOUNT_WIDGET_DISC_AND_FINES;
import static za.co.nedbank.services.discsandfines.tracking.DiscAndFinesTracking.WIDGET_DASHBOARD;
import static za.co.nedbank.services.domain.model.overview.OverviewType.LIFESTYLE;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.OfferProductCodes.PLATFORM_CHANNEL_ID;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.OfferProductCodes.PREFERENCE_TYPE;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.core.util.Pair;

import com.google.gson.Gson;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.Notification;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;
import io.reactivex.subjects.ReplaySubject;
import microsoft.aspnet.signalr.client.ConnectionState;
import retrofit2.HttpException;
import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.ResponseStorageKey;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.concierge.chat.GlobalEventBus;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.ServerStateChangedEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.constants.IAccountOptions;
import za.co.nedbank.core.data.accounts.model.LinkableAccountDto;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.data.storage.StorageUtility;
import za.co.nedbank.core.data.storage.UserInformationKeys;
import za.co.nedbank.core.deeplink.WidgetAdapter;
import za.co.nedbank.core.domain.CachableValue;
import za.co.nedbank.core.domain.model.ClientPreferenceDto;
import za.co.nedbank.core.domain.model.Permission;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.FBNotificationCountResponseData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsCountRequestData;
import za.co.nedbank.core.domain.model.preapprovedoffers.GetPreApprovedOfferRequestData;
import za.co.nedbank.core.domain.model.preapprovedoffers.view_preapproved_offers.PreApprovedOffersData;
import za.co.nedbank.core.domain.model.request.DashboardFeatureVisibilityData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.CheckPermissionGrantedUseCase;
import za.co.nedbank.core.domain.usecase.GetAccountsUseCase;
import za.co.nedbank.core.domain.usecase.GetAllClientPreferenceUseCase;
import za.co.nedbank.core.domain.usecase.GetClearCacheApiUseCase;
import za.co.nedbank.core.domain.usecase.GetOddReviewableReasonUseCase;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.RefreshClientAccountsUseCase;
import za.co.nedbank.core.domain.usecase.app_personalisation.GetPersonalisationInfoUseCase;
import za.co.nedbank.core.domain.usecase.eficasdk.FicaSDKUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.CheckIfUserAdminUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.domain.usecase.fatca.FatcaMissingInfoUseCase;
import za.co.nedbank.core.domain.usecase.investmentonline.GetFicaStatusNowUsecase;
import za.co.nedbank.core.domain.usecase.media_content.AppState;
import za.co.nedbank.core.domain.usecase.media_content.AvoMediaContentUseCase;
import za.co.nedbank.core.domain.usecase.notifications.GetFBNotificationsCountExtendedUseCase;
import za.co.nedbank.core.domain.usecase.preapprovedoffers.GetPreApprovedOfferUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetMdmProfileUseCase;
import za.co.nedbank.core.domain.usecase.view_banker.GetBankerDetailsUseCase;
import za.co.nedbank.core.domain.usecase.view_banker.ShowBankerIconUseCase;
import za.co.nedbank.core.enroll.domain.usecase.GetLinkableAccountsUseCase;
import za.co.nedbank.core.enroll.domain.usecase.SetLinkableAccountsUseCase;
import za.co.nedbank.core.enumerations.BackgroundImageTypeEnum;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.errors.HttpStatus;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationResult;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationEvent;
import za.co.nedbank.core.payment.PaymentsUtility;
import za.co.nedbank.core.terms.TermsAndConditionClassification;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.ChatTracking;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.TravelCardAnalyticsEvent;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.tracking.adobe.AdobeDimensions;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.tracking.appsflyer.AddContextData;
import za.co.nedbank.core.tracking.appsflyer.AppsFlyerTags;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.FatcaRestrictionUtil;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.KidsProfileUtilsWrapper;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.ApplicantIdForeignCheckValidator;
import za.co.nedbank.core.view.app_personalisation.mapper.PersonalisationDataModelToViewModelMapper;
import za.co.nedbank.core.view.app_personalisation.model.PersonalisationViewModel;
import za.co.nedbank.core.view.fbnotifications.AccountPreference;
import za.co.nedbank.core.view.mapper.GetOddReviewableResponseDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.preapprovedoffers.PreApprovedOffersDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.view_banker.ShowBankerIconDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.view_banker.ViewBankerResponseDataToViewModelMapper;
import za.co.nedbank.core.view.media_content.DynamicFeatureCardDetailEnum;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.mdm.response.odd.OddReviewableViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.core.view.model.view_banker.BankerDataViewModel;
import za.co.nedbank.core.view.preapprovedoffers.PreApprovedOffersAmountViewModel;
import za.co.nedbank.core.view.preapprovedoffers.PreApprovedOffersUtility;
import za.co.nedbank.core.view.preapprovedoffers.view_preapproved_offers.PreApprovedOffersDetailsViewModel;
import za.co.nedbank.core.view.preapprovedoffers.view_preapproved_offers.PreApprovedOffersInnerDetailsViewModel;
import za.co.nedbank.core.view.preapprovedoffers.view_preapproved_offers.PreApprovedOffersViewModel;
import za.co.nedbank.eficasdk.view.viewmodel.FicaSDKViewModel;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingValue;
import za.co.nedbank.loans.common.navigator.LoansNavigatorTarget;
import za.co.nedbank.loans.preapprovedaccountsoffers.Constants;
import za.co.nedbank.loans.preapprovedaccountsoffers.navigator.PreApprovedOffersNavigationTarget;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.CreditHealthTrackingEvent;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.CreditHealthTrackingValue;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.PreApprovedOffersTrackingEvent;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.PreApprovedOffersTrackingParams;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.common.OfferNavigationModel;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.common.offer_combination.OfferCategory;
import za.co.nedbank.payment.atm.navigator.AtmNavigatorTarget;
import za.co.nedbank.payment.buy.navigator.BuyNavigatorTarget;
import za.co.nedbank.payment.common.navigator.CommonNavigatorTarget;
import za.co.nedbank.payment.common.view.tracking.AtmGetCashTrackingEvent;
import za.co.nedbank.payment.common.view.tracking.PaymentsTracking;
import za.co.nedbank.payment.common.view.tracking.PaymentsTrackingEvent;
import za.co.nedbank.payment.ngi.NGIConstants;
import za.co.nedbank.payment.ngi.tracking.NgiTrackingEvent;
import za.co.nedbank.profile.view.navigation.ProfileNavigationTarget;
import za.co.nedbank.profile.view.profile.fatca_unrestriction.FatcaUnrestrictionOnboardingActivity;
import za.co.nedbank.profile.view.profile.fatca_unrestriction.FatcaUnrestrictionType;
import za.co.nedbank.profile.view.tracking.ProfileTracking;
import za.co.nedbank.services.data.lifestyle.model.LifestyleDashboardResponseDataModel;
import za.co.nedbank.services.domain.mapper.PendingAccountsEntityToDataModelMapper;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.investmentnotices.PendingAccountDataModel;
import za.co.nedbank.services.domain.model.investmentnotices.PendingAccountListDataModel;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.OnlineSavingsAccounts;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.model.overview.ProductCategory;
import za.co.nedbank.services.domain.usecase.investmentnotices.GetPendingAccountsUsecase;
import za.co.nedbank.services.domain.usecase.online_savings.PocketLinkedUseCase;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.services.greenbackapp_v2.view.util.GBConstants;
import za.co.nedbank.services.greenbackapp_v2.view.util.GBUtils;
import za.co.nedbank.services.insurance.domain.data.response.offer.offer_details.InsGetOfferResponseDataModel;
import za.co.nedbank.services.insurance.domain.usecase.offer.InsGetOffersUseCase;
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants;
import za.co.nedbank.services.insurance.view.other.mapper.offer.offer_details.InsGetOfferDataToViewModelMapper;
import za.co.nedbank.services.insurance.view.other.model.response.offer.offer_details.InsGetOfferResponseViewModel;
import za.co.nedbank.services.nfw.SpendTrackingEvent;
import za.co.nedbank.services.view.mapper.PocketLinkedResponseDomainToViewMapper;
import za.co.nedbank.services.view.model.PocketLinkedResponseViewModel;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.services.view.tracking.ServicesTracking;
import za.co.nedbank.services.view.tracking.ServicesTrackingValue;
import za.co.nedbank.ui.domain.model.AvoWalletDetailsModel;
import za.co.nedbank.ui.domain.usecase.GetAvoWalletDetailsUseCase;
import za.co.nedbank.ui.domain.usecase.overview.GetDashboardOrderUseCase;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;
import za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorActivity;
import za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorType;
import za.co.nedbank.ui.view.refica.FicaErrorActivity;
import za.co.nedbank.ui.view.refica.FicaSuccessActivity;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class OverviewPresenter extends NBBasePresenter<OverviewView> {
    private static final String TAG = OverviewPresenter.class.getSimpleName();
    private final PocketLinkedResponseDomainToViewMapper mPocketLinkedResponseDomainToViewMapper;
    private final PocketLinkedUseCase mPocketLinkedUseCase;
    private final GetAvoWalletDetailsUseCase mWalletDetailsUseCase;
    private final AFAnalyticsTracker mAfAnalyticsTracker;
    private final GetAllClientPreferenceUseCase getAllClientPreferenceUseCase;
    private final GetDashboardOrderUseCase getDashboardOrderUseCase;
    private final GetPendingAccountsUsecase getPendingAccountsUsecase;
    private final GetAccountsUseCase mGetAccountsUseCase;
    private final AvoMediaContentUseCase mMediaContentUseCase;
    private final GetOverviewUseCase getOverviewUseCase;
    private final GetFicaStatusNowUsecase getFicaStatusUseCase;
    private final ApplicationStorage mMemoryApplicationStorage;
    private final PendingAccountsEntityToDataModelMapper pendingAccountsEntitiyToDataModelMapper;
    private final GetPreApprovedOfferUseCase mGetPreApprovedOffersUseCase;
    private final PreApprovedOffersDataToViewModelMapper mPreApprovedOffersDataToViewModelMapper;
    private final NavigationRouter navigationRouter;
    private final ErrorHandler errorHandler;
    private final Analytics analytics;
    private final CheckPermissionGrantedUseCase checkPermissionGrantedUseCase;
    private final FeatureSetController featureSetController;
    private final StorageUtility storageUtility;
    private final GetUserDetailUseCase getUserDetailUseCase;
    private final UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper;
    private final ApplicationStorage mApplicationStorage;
    private final GetFBNotificationsCountExtendedUseCase fbNotificationsCountExtendedUseCase;
    private final GetLinkableAccountsUseCase getLinkableAccountsUseCase;
    private final SetLinkableAccountsUseCase setLinkableAccountsUseCase;
    private final ApplicantIdForeignCheckValidator mApplicantIdForeignCheckValidator;
    private final GetBankerDetailsUseCase mGetBankerDetailsUseCase;
    private final ViewBankerResponseDataToViewModelMapper mViewBankerResponseDataToViewModelMapper;
    private final GetFedarationListUseCase getFedarationListUseCase;
    private final CheckIfUserAdminUseCase checkIfUserAdminUseCase;
    private final FicaSDKUseCase mFicaSDKUseCase;
    private NavigationResult mNavigationResult;
    private BackgroundImageTypeEnum mSelectedImageType = BackgroundImageTypeEnum.UNKNOWN;
    private final GetPersonalisationInfoUseCase getPersonalisationInfoUseCase;
    private final PersonalisationDataModelToViewModelMapper personalisationDataModelToViewModelMapper;
    private final InsGetOffersUseCase mInsGetOffersUseCase;
    private final InsGetOfferDataToViewModelMapper mInsGetOfferDataToViewModelMapper;
    private int loanOfferDisplayedIndex = -1;
    private int cardOfferDisplayedIndex = -1;
    public static final String FEATURE = "feature";
    public static final String DATA = "data";
    private String preApprovedOfferType;
    private String cardOfferType;
    private PreApprovedOffersViewModel preApprovedOffersViewModel;
    private int preApprovedOffersCount;
    private double loanAmountOffered;
    private double cardLimitOffered;
    private int unitTrustAccountCount;
    private int pocketsCount;
    private int investmentAccountCount;
    private int newCategoryCount;
    private int termDepositsCount;
    private int noticeDepositsCount;
    private int unitTrustCount;
    private int retirementAnnuityCount;
    private int taxFreeInvestmentsCount;
    private int taxFreeUnitTrustCount;
    private int livingAnnuityCount;
    private int pensionPreservationCount;
    private int providentPreservationCount;
    private int endowmentPlanCount;
    private int mGeneralCount;
    private int mTransactionCount;
    private int mOthersCount;

    private String clientType;
    private String birthDate;
    private String secOfficerID;
    private String cisNumber;
    private String idOrTaxIdNoStr;
    private boolean mIsNPWEnabledAccount;
    private boolean mIsDCAREnabledAccount;
    private boolean mIsBusinessUser;

    private boolean mIsMerchantUser;

    private OnlineSavingsAccounts mOnlineSavingsAccounts;
    private PreApprovedOffersInnerDetailsViewModel mLoanOffer;
    private PreApprovedOffersInnerDetailsViewModel mCreditCardOffer;
    private boolean isViewUpdatedWithPositiveCount;
    private PendingAccountListDataModel pendingAccountListDataModel;
    private AccountsOverview invetmentAccountsOverview;
    RefreshClientAccountsUseCase refreshUserUseCase;
    private boolean mLandOnFinancialWellnessCarousel = false;
    private boolean mIsFinancialWellnessAvailable = false;
    private static final int FINANCIAL_WELLNESS_POSITION = 0;
    private final KidsProfileUtilsWrapper kidsProfileUtilsWrapper;
    private final GetMdmProfileUseCase getMdmProfileUseCase;
    private final GetOddReviewableReasonUseCase getOddReviewableReasonUseCase;
    private final GetOddReviewableResponseDataToViewModelMapper oddMapper;
    public static final String SCREEN_TYPE = "SCREEN_TYPE";
    public static final int PAY_ME = 1;
    public static final int QUICK_PAY = 2;
    public static final int GET_CASH = 3;
    private final FatcaMissingInfoUseCase fatcaMissingInfoUseCase;
    private final WidgetAdapter widgetAdapter;

    private int mTotalUnreadNotificationCount;
    private final GetClearCacheApiUseCase refreshCleanCacheUserUseCase;
    private int preApprovedOfferPosition = -1;
    private String defaultAccountId;
    @Inject
    OverviewPresenter(final GetAllClientPreferenceUseCase getAllClientPreferenceUseCase,
                      final GetDashboardOrderUseCase getDashboardOrderUseCase,
                      final GetPendingAccountsUsecase getPendingAccountsUsecase, GetAccountsUseCase mGetAccountsUseCase, final GetOverviewUseCase getOverviewUseCase,
                      final GetFicaStatusNowUsecase getFicaStatusUseCase, PendingAccountsEntityToDataModelMapper pendingAccountsEntitiyToDataModelMapper, GetPreApprovedOfferUseCase getPreApprovedOffersUseCase,
                      PreApprovedOffersDataToViewModelMapper preApprovedOffersDataToViewModelMapper,
                      final NavigationRouter navigationRouter,
                      final ErrorHandler errorHandler,
                      final Analytics analytics,
                      final StorageUtility storageUtility,
                      final GetPersonalisationInfoUseCase getPersonalisationInfoUseCase,
                      final PersonalisationDataModelToViewModelMapper personalisationDataModelToViewModelMapper,
                      final CheckPermissionGrantedUseCase checkPermissionGrantedUseCase,
                      final FeatureSetController featureSetController,
                      final GetUserDetailUseCase getUserDetailUseCase,
                      final UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper,
                      final ApplicationStorage applicationStorage,
                      @Named("memory") final ApplicationStorage memoryApplicationStorage,
                      final GetFBNotificationsCountExtendedUseCase fbNotificationsCountExtendedUseCase,
                      final GetLinkableAccountsUseCase getLinkableAccountsUseCase,
                      final SetLinkableAccountsUseCase setLinkableAccountsUseCase,
                      final PocketLinkedResponseDomainToViewMapper pocketLinkedResponseDomainToViewMapper,
                      final PocketLinkedUseCase pocketLinkedUseCase,
                      final ShowBankerIconUseCase mShowBankerIconUseCase,
                      final ShowBankerIconDataToViewModelMapper showBankerIconDataToViewModelMapper,
                      final GetBankerDetailsUseCase getBankerDetailsUseCase,
                      final ViewBankerResponseDataToViewModelMapper viewBankerResponseDataToViewModelMapper,
                      final GetFedarationListUseCase getFedarationListUseCase,
                      final CheckIfUserAdminUseCase checkIfUserAdminUseCase,
                      final ApplicantIdForeignCheckValidator applicantIdValidator,
                      final GetAvoWalletDetailsUseCase walletDetailsUseCase,
                      final AFAnalyticsTracker afAnalyticsTracker, AvoMediaContentUseCase mediaContentUseCase,
                      RefreshClientAccountsUseCase refreshUserUseCase,
                      final FicaSDKUseCase mFicaSDKUseCase,
                      final KidsProfileUtilsWrapper kidsProfileUtilsWrapper,
                      final GetMdmProfileUseCase getMdmProfileUseCase,
                      final GetOddReviewableReasonUseCase getOddReviewableReasonUseCase,
                      final GetOddReviewableResponseDataToViewModelMapper oddMapper,
                      final FatcaMissingInfoUseCase fatcaMissingInfoUseCase,
                      final GetClearCacheApiUseCase refreshCleanCacheUserUseCase,
                      final InsGetOffersUseCase insuranceGetOffersUseCase,
                      final InsGetOfferDataToViewModelMapper insGetOfferDataToViewModelMapper,
                      final WidgetAdapter widgetAdapter) {
        this.getAllClientPreferenceUseCase = getAllClientPreferenceUseCase;
        this.getDashboardOrderUseCase = getDashboardOrderUseCase;
        this.getPendingAccountsUsecase = getPendingAccountsUsecase;
        this.mGetAccountsUseCase = mGetAccountsUseCase;
        this.getOverviewUseCase = getOverviewUseCase;
        this.getFicaStatusUseCase = getFicaStatusUseCase;
        this.pendingAccountsEntitiyToDataModelMapper = pendingAccountsEntitiyToDataModelMapper;
        this.mGetPreApprovedOffersUseCase = getPreApprovedOffersUseCase;
        this.mPreApprovedOffersDataToViewModelMapper = preApprovedOffersDataToViewModelMapper;
        this.mUserDetailDataToViewModelMapper = mUserDetailDataToViewModelMapper;
        this.navigationRouter = navigationRouter;
        this.errorHandler = errorHandler;
        this.analytics = analytics;
        this.storageUtility = storageUtility;
        this.featureSetController = featureSetController;
        this.getUserDetailUseCase = getUserDetailUseCase;
        this.getPersonalisationInfoUseCase = getPersonalisationInfoUseCase;
        this.personalisationDataModelToViewModelMapper = personalisationDataModelToViewModelMapper;
        this.checkPermissionGrantedUseCase = checkPermissionGrantedUseCase;
        this.mApplicationStorage = applicationStorage;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.fbNotificationsCountExtendedUseCase = fbNotificationsCountExtendedUseCase;
        this.getLinkableAccountsUseCase = getLinkableAccountsUseCase;
        this.setLinkableAccountsUseCase = setLinkableAccountsUseCase;
        this.mPocketLinkedResponseDomainToViewMapper = pocketLinkedResponseDomainToViewMapper;
        this.mPocketLinkedUseCase = pocketLinkedUseCase;
        this.mGetBankerDetailsUseCase = getBankerDetailsUseCase;
        this.mViewBankerResponseDataToViewModelMapper = viewBankerResponseDataToViewModelMapper;
        this.getFedarationListUseCase = getFedarationListUseCase;
        this.checkIfUserAdminUseCase = checkIfUserAdminUseCase;
        this.mApplicantIdForeignCheckValidator = applicantIdValidator;
        this.mWalletDetailsUseCase = walletDetailsUseCase;
        this.mAfAnalyticsTracker = afAnalyticsTracker;
        this.mMediaContentUseCase = mediaContentUseCase;
        this.refreshUserUseCase = refreshUserUseCase;
        this.mFicaSDKUseCase = mFicaSDKUseCase;
        this.kidsProfileUtilsWrapper = kidsProfileUtilsWrapper;
        this.getMdmProfileUseCase = getMdmProfileUseCase;
        this.oddMapper = oddMapper;
        this.getOddReviewableReasonUseCase = getOddReviewableReasonUseCase;
        this.fatcaMissingInfoUseCase = fatcaMissingInfoUseCase;
        this.refreshCleanCacheUserUseCase = refreshCleanCacheUserUseCase;
        this.mInsGetOffersUseCase = insuranceGetOffersUseCase;
        this.mInsGetOfferDataToViewModelMapper = insGetOfferDataToViewModelMapper;
        this.widgetAdapter = widgetAdapter;
    }

    @Override
    protected void onBind() {
        super.onBind();
        if (null != mNavigationResult && mNavigationResult.isOk()) {
            if (view != null) {
                view.setResult(mNavigationResult.getParams());
                mNavigationResult = null;
            }
        }
        GlobalEventBus.getBus().register(this);
        mMemoryApplicationStorage.clearValue(za.co.nedbank.core.Constants.CARD_RELATIONSHIP);
        if (mApplicationStorage.getInteger(StorageKeys.LIFESTYLE_TOTAL_UNREAD_MESSAGE_COUNT, za.co.nedbank.core.Constants.ZERO) > za.co.nedbank.core.Constants.ZERO) {
            GlobalEventBus.getBus().post(new LifestyleUnreadChatIconEvent(true));
        } else {
            GlobalEventBus.getBus().post(new LifestyleUnreadChatIconEvent(false));
        }

        checkIfUserAdminUseCase.execute().subscribe(isAdminUser -> {
            mApplicationStorage.putBoolean(StorageKeys.IS_ADMIN_USER, isAdminUser);
        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    @Override
    protected void onUnbind() {
        super.onUnbind();
        GlobalEventBus.getBus().unregister(this);
    }

    void loadBackgroundImage() {
        checkPermissionGrantedUseCase
                .execute(Permission.READ_EXTERNAL_STORAGE)
                .compose(bindToLifecycle()).flatMap(result -> getPersonalisationInfoUseCase.execute(result))
                .subscribe(appPersonalisationDataModel -> {
                    if (appPersonalisationDataModel != null && view != null) {
                        PersonalisationViewModel personalisationViewModel = personalisationDataModelToViewModelMapper.mapPersonalisationDataToViewModel(appPersonalisationDataModel);
                        view.loadOverview(personalisationViewModel.getSelectedImageType());

                        if (mSelectedImageType != personalisationViewModel.getSelectedImageType()) {
                            mSelectedImageType = personalisationViewModel.getSelectedImageType();
                            if (personalisationViewModel.getSelectedImageType() == BackgroundImageTypeEnum.CUSTOM) {
                                view.setBackgroundImage(personalisationViewModel.getCustomImagePath());
                            } else {
                                view.setBackgroundImage(personalisationViewModel.getSelectedImageType().bgDrawableId);
                            }
                            view.shouldShowOverlay(mSelectedImageType == BackgroundImageTypeEnum.CUSTOM);
                        }
                    }
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    void loadUserName() {
        getAllClientPreferenceUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(
                        preferenceDtoList -> {
                            String preferredName = StringUtils.EMPTY_STRING;
                             defaultAccountId = StringUtils.EMPTY_STRING;

                            if (CollectionUtils.isNotEmpty(preferenceDtoList)) {
                                for (ClientPreferenceDto preference : preferenceDtoList) {
                                    if (PREFERRED_NAME_KEY.equals(preference.key)) {
                                        preferredName = preference.value;
                                    } else if (DEFAULT_ACCOUNT_IDENTIFIER.equals(preference.key)) {
                                        defaultAccountId = preference.value;
                                    }
                                }
                            }
                            mMemoryApplicationStorage.putString(DEFAULT_ACCOUNT_ID_KEY, defaultAccountId);
                            if (view != null) {
                                view.showPreferredName(preferredName);
                                view.loadBackgroundImage();
                            }
                        },
                        error -> NBLogger.e("DEBUG", "Error Getting Name", error)
                );
    }

    void loadCustomerNAme() {
        getFedarationListUseCase.execute().subscribe(fedarationList -> {
            if (view != null && fedarationList != null && fedarationList.size() > 0
                    && !StringUtils.isNullOrEmpty(fedarationList.get(0).getCustName())) {
                mApplicationStorage.putString(za.co.nedbank.core.Constants.KEY_USER_CLIENT_NAME, fedarationList.get(0).getPrincipalCustomerName());
                view.showCustomerName(StringUtils.removeTitles(fedarationList.get(0).getCustName()), fedarationList.get(0).isDefaultFederation());
            }
        }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

    }

    void saveCustomerPreferredName(String preferredName) {
        getFedarationListUseCase.execute().subscribe(fedarationList -> {
            if (view != null && fedarationList != null && !fedarationList.isEmpty()
                    && !StringUtils.isNullOrEmpty(fedarationList.get(0).getCustName()) && fedarationList.get(0).isDefaultFederation()) {
                mApplicationStorage.putString(za.co.nedbank.core.Constants.KEY_USER_CLIENT_NAME, preferredName.trim());
            }
        }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    void loadOverview() {
        if (view != null) {
            view.showOverviewLoading(true);
        }
        //resetting bell icon animation flag
        isViewUpdatedWithPositiveCount = false;
        Observable<CachableValue<Overview>> overviewObservable = getOverviewUseCase.execute();
        storeFBDataInPreferences(null, null);

        mCreditCardOffer = null;
        mLoanOffer = null;

        if (view != null && !view.isFeatureDisabled(FeatureConstants.PRE_APPROVED_ACCOUNTS)) {
            /*Notification unread count required for Notification MVP*/
            preApprovedOffersCount = 0;
            loanAmountOffered = 0;
            cardLimitOffered = 0;
            GetPreApprovedOfferRequestData requestData = new GetPreApprovedOfferRequestData(za.co.nedbank.core.Constants.PreApprovedOffersProductCode.ALL.getProductCode(), null);
            requestData.setPreferenceType(za.co.nedbank.core.Constants.PreApprovedOffersPrferenceType.DASHBOARD.getPrefrenceType());
            Observable<PreApprovedOffersData> preApprovedOffersDataObservable = mGetPreApprovedOffersUseCase.execute(requestData);

            ReplaySubject<Boolean> isPreApprovedDataAvailable = ReplaySubject.create();
            isPreApprovedDataAvailable.onNext(false);
            ReplaySubject<Boolean> isOverviewDataAvailable = ReplaySubject.create();
            isOverviewDataAvailable.onNext(false);
            ReplaySubject<Overview> overviewReplaySubject = ReplaySubject.create();

            Observable<Boolean> overViewAndOfferCountObservable =
                    Observable.merge(
                                    overviewObservable
                                            .doOnError(throwable -> {
                                                if (view != null) {
                                                    view.showOverviewLoading(false);
                                                    view.showOverviewError(errorHandler.getErrorMessage(throwable).getMessage());
                                                }
                                            }),
                                    preApprovedOffersDataObservable
                                            .observeOn(AndroidSchedulers.mainThread())
                                            .doOnSubscribe(disposable -> {
                                                if (view != null) {
                                                    view.receiveCountForBorrowWidget(0);
                                                }
                                            })
                                            .doOnNext(preApprovedOffersData -> {
                                                preApprovedOffersViewModel = mPreApprovedOffersDataToViewModelMapper.transform(preApprovedOffersData);
                                                if (preApprovedOffersViewModel != null && preApprovedOffersViewModel.getPreApprovedOffersMetadataViewModel() != null) {
                                                    String errorMessageIfAny = PreApprovedOffersUtility.extractErrorFromMetaData(preApprovedOffersViewModel.getPreApprovedOffersMetadataViewModel());
                                                    if (StringUtils.isNullOrEmpty(errorMessageIfAny)) {
                                                        if (preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel() != null
                                                                && CollectionUtils.isNotEmpty(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList())) {
                                                            calculateMaximumLoanAmountOfferedUnread(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList());
                                                            if (loanAmountOffered <= 0) {
                                                                extractPreApprovedOfferForPersistentDisplay(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList(),
                                                                        za.co.nedbank.core.Constants.PreApprovedOffersTypes.PERSONAL_LOAN_OFFER, za.co.nedbank.core.Constants.PreApprovedOffersTypes.CONSOLIDATED_LOAN_OFFER);
                                                            }
                                                            if (cardLimitOffered <= 0) {
                                                                extractPreApprovedOfferForPersistentDisplay(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList(),
                                                                        za.co.nedbank.core.Constants.PreApprovedOffersTypes.NEW_CREDIT_CARD_OFFER, za.co.nedbank.core.Constants.PreApprovedOffersTypes.CARD_LIMIT_INCREASE_OFFER);
                                                            }
                                                        } else {
                                                            if (view != null) {
                                                                view.handleErrorPreApprovedOffers();
                                                            }
                                                        }
                                                    } else {
                                                        if (view != null) {
                                                            view.handleErrorPreApprovedOffers();
                                                        }
                                                    }
                                                }
                                            })
                                            .doFinally(new Action() {
                                                @Override
                                                public void run() throws Exception {
                                                    preApprovedOffersCount=mMemoryApplicationStorage.getInteger(StorageKeys.PRE_APPROVED_OFFERS_COUNT, 0);
                                                    if (view != null) {
                                                        updateViewWithCount(preApprovedOffersCount);
                                                        view.receiveCountForBorrowWidget(preApprovedOffersCount);
                                                    }
                                                }
                                            })
                                            .map(preApprovedOffersData -> preApprovedOffersViewModel)
                                            .doOnError(throwable -> {
                                                if (view != null) {
                                                    view.handleErrorPreApprovedOffers();
                                                }
                                            })
                                            .onErrorReturnItem(new PreApprovedOffersViewModel(new PreApprovedOffersDetailsViewModel())))
                            .doFinally(new Action() {
                                @Override
                                public void run() throws Exception {
                                    isPreApprovedDataAvailable.onComplete();
                                    isOverviewDataAvailable.onComplete();
                                }
                            }).compose(bindToLifecycle())
                            .doOnEach(new Consumer<Object>() {
                                @Override
                                public void accept(Object o) throws Exception {
                                    NBLogger.e(TAG, o + "");
                                    if (o instanceof Notification && ((Notification) o).isOnNext()) {
                                        if (((Notification) o).getValue() instanceof CachableValue) {
                                            isOverviewDataAvailable.onNext(true);
                                            try {
                                                CachableValue<Overview> overview = (CachableValue<Overview>) ((Notification) o).getValue();
                                                Overview overviewValue = overview.get().clone();
                                                overviewReplaySubject.onNext(overview.get().clone());
                                                NBLogger.e(TAG, "overviewreplaysubject " + isPreApprovedDataAvailable.getValue());
                                                if (isPreApprovedDataAvailable.getValue()) {
                                                    addOfferToOverview(overviewValue);
                                                    getPendingAccounts(overviewValue);
                                                } else {
                                                    if (overviewValue.accountsOverviews != null && overviewValue.accountsOverviews.size() > 0) {
                                                        handleOverviewData(overviewValue);
                                                    }
                                                }
                                            } catch (Exception e) {
                                                if (view != null) {
                                                    view.showOverviewLoading(false);
                                                    view.showOverviewError(errorHandler.getErrorMessage(e).getMessage());
                                                }
                                            }
                                        } else if (((Notification) o).getValue() instanceof PreApprovedOffersViewModel) {
                                            isPreApprovedDataAvailable.onNext(true);
                                            NBLogger.e(TAG, "isPreApprovedDataAvailable " + isOverviewDataAvailable.getValue() + " " +
                                                    overviewReplaySubject.hasValue());
                                            if (isOverviewDataAvailable.getValue() && overviewReplaySubject.hasValue()) {
                                                Overview overviewValue = overviewReplaySubject.getValue();
                                                addOfferToOverview(overviewValue);
                                                getPendingAccounts(overviewValue);
                                            }
                                        }
                                    }
                                }
                            })
                            .map(new Function<Object, Boolean>() {
                                @Override
                                public Boolean apply(Object o) throws Exception {
                                    return o != null;
                                }
                            });
            overViewAndOfferCountObservable.subscribe(isResponseReceivedFromApis -> {
                //response already sent to view above
                NBLogger.e(TAG, "view.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS) success");
            }, throwable -> {
                //errors already passed to view above
                NBLogger.e(TAG, throwable.getMessage());
            });
        } else {
            overviewObservable
                    .compose(bindToLifecycle())
                    .subscribe(
                            overview -> {
                                getPendingAccounts(overview.get());
                            },
                            error -> {
                                if (view != null) {
                                    view.showOverviewLoading(false);
                                    view.showOverviewError(errorHandler.getErrorMessage(error).getMessage());
                                }
                            }
                    );
        }

        validatePushNotification();
    }

    private void validatePushNotification() {
        if (isNotificationToggleOn()) {
            if (!isNotificationPresent()) {
                getPushNotificationCount();
            } else {
                if (!view.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_TRANSACTION_NOTIFICATIONS)) {
                    mTotalUnreadNotificationCount = mApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, 0);
                }
                updateViewWithCount(mTotalUnreadNotificationCount);
            }
        }
    }

    private boolean isNotificationToggleOn() {
        return view != null && (!view.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS) || !view.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_TRANSACTION_NOTIFICATIONS));
    }


    public boolean isNotificationPresent() {
        mTotalUnreadNotificationCount = mApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, 0);
        return mTotalUnreadNotificationCount > 0;
    }

    public AccountSummary getPreApprovedOfferAccountSummary(long offerId,
                                                            long offerTypeId,
                                                            String offerType,
                                                            OverviewType overviewType,
                                                            String preApprovedOfferType,
                                                            double amountOffered,
                                                            boolean isDebiCheckEnabled,
                                                            boolean isCCAPOEnabled,
                                                            String headerText,
                                                            String ctaText,
                                                            @OfferCategory int... offerCategory) {
        AccountSummary accountSummary = new AccountSummary();
        //set offerId, offerTypeId offerType and to account summary object
        accountSummary.setId(String.valueOf(offerId));
        accountSummary.setNumber(String.valueOf(offerTypeId));
        accountSummary.setPreApprovedOfferType(offerType);
        accountSummary.setDebicheckMandateEnabled(isDebiCheckEnabled);
        accountSummary.setCCAPOEnabled(isCCAPOEnabled);
        accountSummary.setPreApprovedOfferAccount(true);
        accountSummary.setAccountType(overviewType);
        if ((headerText==null || headerText.isEmpty()) && view !=null) {
            accountSummary.setName(view.providePreapprovedPersonalOfferText(preApprovedOfferType));
        } else {
            accountSummary.setName(headerText);
        }
        if(!TextUtils.isEmpty(ctaText)){
            accountSummary.setCtaText(ctaText);
        }
        accountSummary.setSummaryValue(BigDecimal.valueOf(amountOffered));
        if (offerCategory != null && offerCategory.length != 0)
            accountSummary.setOfferCategory(offerCategory[0]);
        return accountSummary;
    }

    public AccountsOverview getPreApprovedOfferAccountOverView(long offerId,
                                                               long offerTypeId,
                                                               String offerType,
                                                               OverviewType overviewType,
                                                               String preApprovedOfferType,
                                                               double amountOffered,
                                                               boolean isDebiCheckEnabled,
                                                               boolean isCCAPOEnabled,
                                                               String headerText,
                                                               String ctaText,
                                                               @OfferCategory int... offerCategory) {
        AccountsOverview loanAccountsOverview = new AccountsOverview();
        loanAccountsOverview.overviewType = overviewType;
        loanAccountsOverview.accountSummaries.add(getPreApprovedOfferAccountSummary(offerId, offerTypeId, offerType, overviewType,preApprovedOfferType, amountOffered, isDebiCheckEnabled, isCCAPOEnabled,headerText,ctaText,offerCategory));
        return loanAccountsOverview;
    }


    private void getPushNotificationCount() {

        Observable<FBNotificationCountResponseData> notificationObservable = fbNotificationsCountExtendedUseCase.execute(createFBNotificationCountRequest());
        notificationObservable
                .compose(bindToLifecycle())
                .subscribe(notificationCountResponseData -> {
                            handleNotificationCountResponse(notificationCountResponseData);
                        },
                        throwable -> {
                            /*background api no need to handle*/
                            NBLogger.e(TAG, throwable.getMessage());
                        });
    }

    private void handleNotificationCountResponse(FBNotificationCountResponseData fbNotificationCountResponseData) {
        int ONLY_SUCCESS_ELEMENT = 1;
        if (fbNotificationCountResponseData != null && fbNotificationCountResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = fbNotificationCountResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && resultDetailList.size() > 0) {
                    if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        // success case
                        if (view != null) {
                            if (!view.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS)) {
                                mGeneralCount = fbNotificationCountResponseData.getGeneralCount();
                                saveNotificationCount(mGeneralCount);
                            }
                            if (!view.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_TRANSACTION_NOTIFICATIONS)) {
                                mTransactionCount = fbNotificationCountResponseData.getTransactionCount();
                                saveTransactionNotificationCount(mTransactionCount);
                            }
                            mTotalUnreadNotificationCount = mGeneralCount + mTransactionCount;
                            mApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, mTotalUnreadNotificationCount);
                            updateViewWithCount(mTotalUnreadNotificationCount);
                        }
                        break;
                    } else {
                        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
                            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                                NBLogger.e(TAG, resultDetailViewModel.getReason());
                            }
                        }
                    }
                }

            }

        }

    }

    private void updateViewWithCount(int notificationCount) {
        if (!isViewUpdatedWithPositiveCount) {
            view.receivePreApprovedOffersCount(notificationCount);
            isViewUpdatedWithPositiveCount = notificationCount > 0;
        }
    }


    private FBNotificationsCountRequestData createFBNotificationCountRequest() {
        FBNotificationsCountRequestData fbNotificationsCountRequestData = new FBNotificationsCountRequestData();
        fbNotificationsCountRequestData.setStatus(NotificationConstants.NOTIFICATION_STATUS_TYPES.NOTIFICATION_UNREAD);
        return fbNotificationsCountRequestData;
    }

    private void saveNotificationCount(int notificationCount) {
        mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, notificationCount);
    }

    private void saveTransactionNotificationCount(int notificationCount) {
        mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, notificationCount);
    }

    private AccountsOverview getAccountOverView() {
        List<AccountSummary> accountSummary = new ArrayList<>();
        accountSummary.add(getAccountSummary());
        AccountsOverview accountsOverview = new AccountsOverview();
        accountsOverview.accountSummaries = accountSummary;
        accountsOverview.overviewType = OverviewType.REWARDS;
        accountsOverview.summaryValue1 = new BigDecimal(0);
        accountsOverview.summaryValue2 = new BigDecimal(0);
        return accountsOverview;
    }

    public AccountsOverview getInvestmentAccountOverView(boolean hasPendingAccounts, List<AccountSummary> accountSummaries) {
        List<AccountSummary> accountSummary;
        if (hasPendingAccounts) {
            accountSummary = accountSummaries;
        } else {
            accountSummary = new ArrayList<>();
        }
        if (!featureSetController.isFeatureDisabled(FeatureConstants.INVESTMENT_ACCOUNT_TAX_CERTIFICATE_FEATURE))
            accountSummary.add(getInvestmentAccountSummary(za.co.nedbank.services.
                    Constants.INVESTMENT_TAX_CERTIFICATE, false, StringUtils.EMPTY_STRING));
        accountSummary.add(getInvestmentAccountSummary(StringUtils.EMPTY_STRING, false, StringUtils.EMPTY_STRING));
        AccountsOverview accountsOverview = new AccountsOverview();
        accountsOverview.accountSummaries = accountSummary;
        accountsOverview.overviewType = OverviewType.INVESTMENTS;
        accountsOverview.summaryValue1 = new BigDecimal(0);
        accountsOverview.summaryValue2 = new BigDecimal(0);
        return accountsOverview;
    }

    private AccountsOverview getInsuranceOverview() {
        List<AccountSummary> accountSummary = new ArrayList<>();
        AccountsOverview accountsOverview = new AccountsOverview();
        accountSummary.add(getInsuranceAccountSummary());
        if (isInsuranceToggleAvailable()) {
            accountSummary.add(getInsuranceSummary());
        }
        accountsOverview.accountSummaries = accountSummary;
        accountsOverview.overviewType = OverviewType.INSURANCE;
        accountsOverview.summaryValue1 = new BigDecimal(0);
        accountsOverview.summaryValue2 = new BigDecimal(0);
        return accountsOverview;
    }

    private AccountSummary getAccountSummary() {
        AccountSummary accountSummary = new AccountSummary("1", "", OverviewType.REWARDS, za.co.nedbank.services.Constants.OVERVIEW_REWARDS_GREENBACKS, new BigDecimal(0), StringUtils.CURRENCY_SYMBOL);
        accountSummary.setRewardsProgram(FormattingUtil.GREENBACKS_CURRENCY);
        return accountSummary;
    }

    private AccountSummary getInsuranceAccountSummary() {
        AccountSummary accountSummary = new AccountSummary();
        accountSummary.setName(InsuranceConstants.OVERVIEW_INSURANCE_ACCOUNT);
        accountSummary.setAccountHolderName(StringUtils.EMPTY_STRING);
        accountSummary.setAccountType(OverviewType.INSURANCE);
        accountSummary.setCurrency(StringUtils.EMPTY_STRING);
        accountSummary.setSummaryValue(new BigDecimal(0));
        accountSummary.setNumber(StringUtils.HYPHEN);
        accountSummary.setAccountShown(true);
        return accountSummary;
    }

    private AccountSummary getInsuranceSummary() {
        AccountSummary accountSummary = new AccountSummary("1", "", OverviewType.INSURANCE, InsuranceConstants.OVERVIEW_INSURANCE_ACCOUNT, new BigDecimal(0), StringUtils.CURRENCY_SYMBOL);
        return accountSummary;
    }

    private void getInternationalBankingAndTravelOverview(Overview overview) {
        boolean hasForeignContainer = false;
        for (AccountsOverview accountsOverview : overview.accountsOverviews) {
            if (accountsOverview.overviewType == OverviewType.FOREIGN_CURRENCY_ACCOUNT) {
                hasForeignContainer = true;
                if (isFCAToggleOn() && !isBusinessUser()) {
                    accountsOverview.accountSummaries.add(0,getFCASummary());
                }
                accountsOverview.accountSummaries.add(0,getInternationalBankingSummary());
            }
        }

        boolean isKidsBankingProfile = kidsProfileUtilsWrapper.isUserUnderAged(); // for kids banking need to remove - FOREIGN_CURRENCY_ACCOUNT carousel page

        if (!isKidsBankingProfile && !hasForeignContainer) {
            List<AccountSummary> accountSummary = new ArrayList<>();
            AccountsOverview accountsOverview = new AccountsOverview();
            accountSummary.add(getInternationalBankingSummary());
            if (isFCAToggleOn() && !isBusinessUser()) {
                accountSummary.add(getFCASummary());
            }
            accountsOverview.accountSummaries = accountSummary;
            accountsOverview.overviewType = OverviewType.FOREIGN_CURRENCY_ACCOUNT;
            accountsOverview.summaryValue1 = new BigDecimal(0);
            accountsOverview.summaryValue2 = new BigDecimal(0);
            overview.accountsOverviews.add(accountsOverview);
        }
    }

    private AccountSummary getFCASummary() {
        AccountSummary accountSummary = new AccountSummary("1", StringUtils.HYPHEN, OverviewType.FOREIGN_CURRENCY_ACCOUNT, OVERVIEW_FCA_TITLE, new BigDecimal(0), StringUtils.CURRENCY_SYMBOL);
        accountSummary.setFCAAccount(true);
        return accountSummary;
    }

    private AccountSummary getInternationalBankingSummary() {
        AccountSummary accountSummary = new AccountSummary("1", StringUtils.HYPHEN, OverviewType.FOREIGN_CURRENCY_ACCOUNT, isBusinessUser() ? OVERVIEW_INCOMING_PAYMENTS_SBS : OVERVIEW_INCOMING_PAYMENTS, new BigDecimal(0), StringUtils.CURRENCY_SYMBOL);
        accountSummary.setInternationalBankingAccount(true);
        return accountSummary;
    }

    private AccountSummary getInvestmentAccountSummary(String invName, boolean isPendingAccount, String pendingAccountName) {
        AccountSummary accountSummary;
        if (isPendingAccount) {
            accountSummary = new AccountSummary(String.valueOf(ONE), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS_PENDING_ACCOUNTS, pendingAccountName, new BigDecimal(0), invName);
            accountSummary.setOpenNewInvAccount(true);
        } else if (invName.equals(za.co.nedbank.services.Constants.INVESTMENT_TAX_CERTIFICATE))
            accountSummary = new AccountSummary(String.valueOf(ONE), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS, za.co.nedbank.services.Constants.INVESTMENT_TAX_CERTIFICATE, new BigDecimal(0), StringUtils.CURRENCY_SYMBOL);
        else {
            accountSummary = new AccountSummary(String.valueOf(ONE), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS, za.co.nedbank.services.Constants.OVERVIEW_INVESTMENT_ACCOUNT, new BigDecimal(0), StringUtils.CURRENCY_SYMBOL);
        }
        accountSummary.setIspendingAccount(isPendingAccount);
        accountSummary.setIsProductCategory(false);
        return accountSummary;
    }

    private AccountSummary getInvestmentAccountSummary(String productName, double amount, List<AccountSummary> accountSummaryList) {
        int i = za.co.nedbank.core.Constants.ZERO;
        AccountSummary accountSummary;
        ProductCategory productCategory = new ProductCategory(accountSummaryList, productName, amount);
        accountSummary = new AccountSummary(String.valueOf(i), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS, productName, BigDecimal.valueOf(amount), StringUtils.CURRENCY_SYMBOL, productCategory);
        accountSummary.setIspendingAccount(false);
        accountSummary.setOpenNewInvAccount(false);
        accountSummary.setIsProductCategory(true);
        return accountSummary;
    }

    private AccountSummary getSavingsPocketAccountSummary(String accountNumber) {
        AccountSummary summary = new AccountSummary(za.co.nedbank.services.Constants.SAVINGS_POCKET_PRODUCT_TYPE, accountNumber, OverviewType.EVERYDAY_BANKING, view.getNewFreeFeature(), new BigDecimal(0), view.getMyPocketString());
        summary.setSavingsPocketCounter(0);
        return summary;
    }

    private AccountSummary getSavingsPocketWithAmountSummary(String accountNumber, BigDecimal amount, int pocketCounter, int tpCounter) {
        AccountSummary summary = new AccountSummary(za.co.nedbank.services.Constants.SAVINGS_POCKET_PRODUCT_TYPE,
                accountNumber, OverviewType.EVERYDAY_BANKING, String.format(Locale.getDefault(),
                view.getSavingsGoalPocket(), pocketCounter, tpCounter * za.co.nedbank.services.Constants.MAX_SAVINGS_POCKET),
                amount, view.getMyPocketString());
        summary.setSavingsPocketCounter(pocketCounter);
        return summary;
    }

    void invalidateAccountApiCache() {
        getOverviewUseCase.invalidateCache();
    }

    void getDashboardOrder() {
        if (view != null) {
            DashboardFeatureVisibilityData dashboardFeatureVisibilityData = new DashboardFeatureVisibilityData();
            dashboardFeatureVisibilityData.setRecipientFeatureEnabled(true);
            dashboardFeatureVisibilityData.setFeatureAvailableForPayMoneyRequest(true);
            getDashboardOrderUseCase.execute(dashboardFeatureVisibilityData)
                    .compose(bindToLifecycle())
                    .subscribe(dashboardOrder -> {
                        if (view != null)
                            view.setDashboardOrder(dashboardOrder);
                    }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
        }
    }

    void accountTypeClicked(final AccountSummary accountSummary, String featureName) {
        if (view != null) {
            mMemoryApplicationStorage.putObject(za.co.nedbank.core.Constants.CARD_RELATIONSHIP, accountSummary.getCardRelationShip());
            mMemoryApplicationStorage.putObject(za.co.nedbank.core.Constants.IDENTIFICATION_NUMBER, accountSummary.getIdentificationNumber());
            if (accountSummary.getAccountType() == OverviewType.REWARDS) {

                if (TextUtils.isEmpty(accountSummary.getNumber())) {
                    navigateToRewardsLandingScreen(accountSummary);
                } else {
                    NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS);
                    if (accountSummary.getName() != null && accountSummary.getName().equalsIgnoreCase(za.co.nedbank.services.Constants.OVERVIEW_REWARDS_GREENBACKS)) {
                        GBUtils.setGreenbackV2Data(accountSummary.getNumber(), featureSetController, isBusinessUser());
                        if (GBUtils.isGreenBackV2Allowed()) {
                            boolean isJuristic = isBusinessUser() && !featureSetController.isFeatureDisabled(FeatureConstants.FTR_GREENBACKS_JURISTIC);
                            boolean isDeepLinkFlow = mMemoryApplicationStorage.getBoolean(za.co.nedbank.core.Constants.IS_GB_REDEEM_DEEP_LINK_FLOW, false);
                            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.GREENBACKS_APP_DASHBOARD)
                                    .withParam(GBConstants.BUNDLE_IS_JURISTIC, isJuristic)
                                    .withParam(GBConstants.BUNDLE_IS_DEEPLINK, isDeepLinkFlow)
                                    .withParam(GBConstants.BUNDLE_DEEPLINK_FEATURE_NAME, featureName);
                        }
                    } else {
                        boolean isMRV2Allowed = GBUtils.isMembershipRewardsV2Allowed(accountSummary.getName(), accountSummary.getNumber(), featureSetController, isBusinessUser());
                        if (isMRV2Allowed && mApplicationStorage.getBoolean(GBConstants.IS_FIRST_TIME_MEMBERSHIP_REWARDS_USER, true)) {
                            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.MEMBERSHIP_DASHBOARD_PREP_ACTIVITY);
                        } else if (isMRV2Allowed) {
                            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.MEMBERSHIP_DASHBOARD_ACTIVITY);
                        }
                    }

                    navigationRouter.navigateTo(navigationTarget
                            .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                            .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber())
                            .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                            .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_ACCOUNT_TYPE, accountSummary.getAccountCode())
                            .withParam(ServicesNavigationTarget.PARAM_REWARDS_TYPE, accountSummary.getName())
                            .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                            .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                            .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, view.canTransact())
                            .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, view.getFicaStatus())
                            .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, clientType)
                            .withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate())
                            .withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice())
                            .withParam(ServicesNavigationTarget.INVONLINE_DELETE_NOTICE, accountSummary.getDeleteNotice())
                            .withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct())
                            .withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment())
                            .withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment())
                            .withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment())
                            .withParam(ServicesNavigationTarget.INVONLINE_ADD_RECURRING_PAYMENT, accountSummary.getAddRecurringPayment())
                            .withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit())
                            .withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit())
                            .withParam(ServicesNavigationTarget.INVONLINE_VIEW_PAYOUT, accountSummary.getViewPayout())
                            .withParam(ServicesNavigationTarget.INVONLINE_DELETE_PAYOUT, accountSummary.getDeletePayout())
                            .withParam(ServicesNavigationTarget.INVONLINE_VIEW_REINVEST, accountSummary.getViewReinvest())
                            .withParam(ServicesNavigationTarget.INVONLINE_DELETE_REINVEST, accountSummary.getDeleteReinvest())
                            .withParam(ServicesNavigationTarget.INVONLINE_ACCOUNT_PLEDGE_STATUS, accountSummary.isPledgeAccount())
                            .withParam(ServicesNavigationTarget.INVONLINE_DRAWN_DOWN_ACCOUNT_STAUS, accountSummary.getStatus())
                            .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                            .withParam(ServicesNavigationTarget.PARAM_UNIT_TRUST_ACCOUNT_COUNT, unitTrustAccountCount)
                            .withParam(ServicesNavigationTarget.PARAM_IS_SA_RESIDENT, view.isSAResident())
                            .withParam(ServicesNavigationTarget.PARAM_MY_POCKET_COUNT, pocketsCount)
                            .withParam(ServicesNavigationTarget.PARAM_GB_INVESTMENT_ACCOUNT_COUNT, investmentAccountCount)
                            .withParam(ServicesNavigationTarget.INVONLINE_PRODUCT_TYPE, accountSummary.getProductType())
                            .withParam(ServicesNavigationTarget.PARAM_ONIA_CLIENT_TYPE, clientType)
                            .withParam(ServicesNavigationTarget.PARAM_ONIA_BIRTH_DATE, birthDate)
                            .withParam(ServicesNavigationTarget.PARAM_ONIA_SEC_OFFICER_CD, secOfficerID)
                            .withParam(ServicesNavigationTarget.PARAM_ONIA_CIS_NUMBER, cisNumber)

                    );
                }
            } else if (accountSummary.getAccountType() == OverviewType.LOANS && accountSummary.isPreApprovedOfferAccount()) {
                trackActionWithContextDataNA(AppTracking.DASHBOARD_VIEW_OFFER);
                NavigationTarget navigationTarget = prepareNavigationTarget(Long.parseLong(accountSummary.getNumber()),
                        Long.parseLong(accountSummary.getId()), accountSummary.getPreApprovedOfferType(),
                        accountSummary.getOfferCategory(), accountSummary.isDebicheckMandateEnabled(), accountSummary.isCCAPOEnabled(), accountSummary.getSource(), accountSummary.getUniqueIdInSourceSystem(), accountSummary.getDeeplink(), accountSummary.getDeeplinkIdentifier());
                navigationRouter.navigateTo(navigationTarget);
            } else if (accountSummary.getAccountType() == OverviewType.CREDIT_CARDS && accountSummary.isPreApprovedOfferAccount()) {
                trackActionWithContextDataNA(AppTracking.DASHBOARD_VIEW_OFFER_CC);
                NavigationTarget navigationTarget = prepareNavigationTarget(Long.parseLong(accountSummary.getNumber()),
                        Long.parseLong(accountSummary.getId()), accountSummary.getPreApprovedOfferType(),
                        accountSummary.getOfferCategory(), false, accountSummary.isCCAPOEnabled(), accountSummary.getSource(), accountSummary.getUniqueIdInSourceSystem(), accountSummary.getDeeplink(), accountSummary.getDeeplinkIdentifier());
                navigationRouter.navigateTo(navigationTarget);
            } else if (accountSummary.getAccountType() == OverviewType.EVERYDAY_BANKING
                    && za.co.nedbank.services.Constants.SAVINGS_POCKET_PRODUCT_TYPE.equals(accountSummary.getId())) {
                handleOnlineSavingsPocketsClick(accountSummary);
            } else if (accountSummary.getAccountType() == OverviewType.EVERYDAY_BANKING && accountSummary.isPreApprovedOfferAccount()) {
                trackPreApprovedOfferOnEverydayBanking(accountSummary);
                navigateInCaseOfPreApprovedScreen(accountSummary);

            } else if (accountSummary.isTravelCardAccount()) {
                if (PaymentsUtility.isMinorOrAdultBeneficiaryPresent(mMemoryApplicationStorage.getString(za.co.nedbank.core.Constants.CARD_RELATIONSHIP, null))) {
                    trackActionWithContextDataNA(TravelCardAnalyticsEvent.FC_TRAVEL_CC_ACCOUNT_ARROW_ACTION);
                } else {
                    trackActionWithContextDataNA(TravelCardAnalyticsEvent.FC_TRAVEL_MC_ACCOUNT_ARROW_ACTION);
                }
                NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.TRAVEL_CARD_DETAILS)
                        .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                        .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                        .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                        .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                        .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, view.canTransact())
                        .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, view.getFicaStatus())
                        .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true);
                navigationRouter.navigateWithResult(navigationTarget).subscribe(navigationResult -> mNavigationResult = navigationResult, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            } else if (accountSummary.getAccountType() == OverviewType.INVESTMENTS && !accountSummary.isIspendingAccount()) {
                if (accountSummary.getName().equals(za.co.nedbank.services.Constants.INVESTMENT_TAX_CERTIFICATE)) {
                    navigateToInvestmentTaxCertificates();
                } else if (TextUtils.isEmpty(accountSummary.getNumber()) && !accountSummary.isIsProductCategory()) {
                    navigateToInvestment();
                } else if (accountSummary.isIsProductCategory()) {
                    navigateToAccountTypeInvestmentActivity(accountSummary);
                } else {
                    navigateToAccountDetails(accountSummary);
                }
            } else if (accountSummary.getAccountType() == OverviewType.INSURANCE) {
                if (StringUtils.isNullOrEmpty(accountSummary.getNumber())) {
                    navigateToInsuranceDashboard(InsuranceConstants.FlowType.QUOTE);
                } else {
                    navigateToInsuranceDashboard(InsuranceConstants.FlowType.POLICY);
                }
            } else if (accountSummary.getAccountType() == OverviewType.FOREIGN_CURRENCY_ACCOUNT
                    && accountSummary.isInternationalBankingAccount()) {
                navigateToInternationalPayment();
            } else if (accountSummary.getAccountType() == OverviewType.FOREIGN_CURRENCY_ACCOUNT
                    && accountSummary.isFCAAccount()) {
                navigateToFCAOpen();
            } else if (accountSummary.getAccountType() == OverviewType.FINANCIAL_WELLNESS) {
                if (za.co.nedbank.services.Constants.CREDIT_HEALTH.equals(accountSummary.getName())) {
                    openCreditHealthScreen();
                } else if (za.co.nedbank.services.Constants.MONEY_TRACKER.equals(accountSummary.getName())) {
                    openMoneyTrackerScreen();
                } else if (za.co.nedbank.services.Constants.NFW.equals(accountSummary.getName())) {
                    openNFWScreen();
                }
            } else if (accountSummary != null && accountSummary.getAccountType() == LIFESTYLE) {
                if (view.isAccessibilityEnabled()) {
                    navigateToFamilyBanking();
                }
            } else if (accountSummary != null && accountSummary.getAccountType() == OverviewType.MERCHANT_SERVICES) {
                smaIntroduction();
            } else {
                navigateToAccountDetails(accountSummary);
            }
            if (accountSummary != null && accountSummary.isDormantAccount()) {
                analytics.sendEvent(TrackingEvent.DORMANT_ACCOUNT_SECTION, TrackingParam.DORMANT_ACCOUNT_USAGE_DETAIL, StringUtils.EMPTY_STRING);
            }

            if (accountSummary.getAccountType() != OverviewType.INSURANCE
                    && (!(accountSummary.getAccountType() == OverviewType.LOANS && accountSummary.isPreApprovedOfferAccount()))
                    && (!(accountSummary.getAccountType() == OverviewType.EVERYDAY_BANKING && za.co.nedbank.services.Constants.SAVINGS_POCKET_PRODUCT_TYPE.equals(accountSummary.getId())))) {
                trackActionAccountProductNGroup(accountSummary.getAccountType(), accountSummary.getName(), accountSummary.getAccountCode(), AppTracking.MY_ACCOUNTS_SELECT_ACCOUNT, false);
            }
        }
    }

    private void navigateInCaseOfPreApprovedScreen(AccountSummary accountSummary) {
        NavigationTarget navigationTarget = prepareNavigationTarget(Long.parseLong(accountSummary.getNumber()),
                Long.parseLong(accountSummary.getId()), accountSummary.getPreApprovedOfferType(),
                accountSummary.getOfferCategory(), false, accountSummary.isCCAPOEnabled(), accountSummary.getSource(), accountSummary.getUniqueIdInSourceSystem(), accountSummary.getDeeplink(), accountSummary.getDeeplinkIdentifier());
        navigationRouter.navigateTo(navigationTarget);
    }

    public Pair<String, String> getProductAndCategoryName(String offerType) {
        String productName = StringUtils.EMPTY_STRING;
        String productAccount = StringUtils.EMPTY_STRING;
        if (!StringUtils.isNullOrEmpty(offerType)) {
            switch (offerType) {
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.CARD_LIMIT_INCREASE_OFFER -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_CREDIT_LIMIT_INCREASE;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_CREDIT_LIMIT_INCREASE;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.GOLDEN_GOOSE -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_UNILATERAL_OFFER;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_GOLDEN_GOOSE_LIMIT_INCREASE;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.CONSOLIDATED_LOAN_OFFER -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_CONSOLIDATED_LOAN;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_CONSOLIDATED_LOAN;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.PRE_NCA_OVERDRAFT -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_PRE_NCA_OFFER;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_PRE_NCA;
                }
                case za.co.nedbank.core.Constants.EverdayBankingOffers.SAVVY_BUNDLE -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_SAVVY_BUNDLE;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_SAVVY_BUNDLE;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.PERSONAL_LOAN_OFFER -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_PERSONAL_LOAN;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_PERSONAL_LOAN;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.NEW_CREDIT_CARD_OFFER -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_CREDIT_CARD;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_CREDIT_CARD;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.OVER_DRAFT_OFFER -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_OVERDRAFT;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_OVERDRAFT;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.OVERDRAFT_LIMIT_INCREASE -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_OVERDRAFT_LIMIT_INCREASE;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_OVERDRAFT_LIMIT_INCREASE;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.CREDIT_CARD_BALANCE_TRANSFER -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_CCBT;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_CCBT;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.CREDIT_CARD_SAA -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_SAACC;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_SAACC;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.CREDIT_CARD_AMEX -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_CREDIT_CARD_AMEX;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_CREDIT_CARD_AMEX;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.INSURANCE_OFFER -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_INSURANCE;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_INSURANCE;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.INVESTMENT_OFFER -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_INVESTMENT;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_INVESTMENT;
                }
                case za.co.nedbank.core.Constants.PreApprovedOffersTypes.HOME_LOAN_OFFER -> {
                    productName = PreApprovedOffersTrackingParams.KEY_CATEGORY_PRODUCT_HOME_LOAN_OFFER;
                    productAccount = PreApprovedOffersTrackingParams.KEY_FEATURE_OFFERS_HOME_LOAN;
                }
                default -> {
                    productName = String.format(Locale.getDefault(), PreApprovedOffersTrackingParams.KEY_GENERIC_GHOST_OFFER, offerType);
                    productAccount = String.format(Locale.getDefault(), PreApprovedOffersTrackingParams.KEY_FEATURE_GHOST_OFFER, offerType);
                }
            }
        }
        return new Pair<>(productName, productAccount);
    }

    private void trackPreApprovedOfferOnEverydayBanking(AccountSummary accountSummary) {
        HashMap<String, Object> cdata = new HashMap();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(PreApprovedOffersTrackingEvent.VAL_PRODUCT_GROUP_SALES_ONBOARDING);
        adobeContextData.setFeature(PreApprovedOffersTrackingEvent.VAL_PRODUCT_GROUP_OFFER_ONBOARDING);
        if (VALUE_ADDED_OFFERS.equalsIgnoreCase(accountSummary.getSource())) {
            adobeContextData.setProductCategory(PreApprovedOffersTrackingParams.KEY_PRODUCT_CATEGORY_VALUE_ADDS);
            adobeContextData.setContext1(accountSummary.getShortMessage());
            adobeContextData.setCategoryAndProduct(PreApprovedOffersTrackingParams.KEY_CATEGORY_VALUE_ADDS);
            analytics.sendEventActionWithMap(PreApprovedOffersTrackingEvent.TAG_DASHBOARD_VALUE_ADDS_OFFER_CLICK, cdata);
        } else {
            adobeContextData.setProductCategory(PreApprovedOffersTrackingParams.VAL_PRODUCT_CATEGORY_OFFER);
            Pair<String, String> categoryProductAndProductAccount = getProductAndCategoryName(accountSummary.getPreApprovedOfferType());
            adobeContextData.setProductAccount(categoryProductAndProductAccount.second);
            adobeContextData.setCategoryAndProduct(categoryProductAndProductAccount.first);
            analytics.sendEventActionWithMap(TAG_DASHBOARD_FOR_YOU_OFFER_CLICK, cdata);
        }
    }

    private OfferNavigationModel prepareOfferNavigationModelObject(Long offerTypeId,
                                                                   Long offerId,
                                                                   String offerType,
                                                                   long offerCategory,
                                                                   boolean isDebiCheckMandateEnabled,
                                                                   boolean isCCAPOEnabled,
                                                                   String source,
                                                                   String uniqueIdSourceSystem,
                                                                   String deeplink,
                                                                   String deeplinkIdentifier) {
        OfferNavigationModel offerNavigationModel = new OfferNavigationModel();
        offerNavigationModel.setOfferTypeId(offerTypeId);
        offerNavigationModel.setOfferId(offerId);
        offerNavigationModel.setOfferType(offerType);
        offerNavigationModel.setFlowEntryDashboardCarousel(true);
        offerNavigationModel.setOfferCategory(offerCategory);
        offerNavigationModel.setDebiCheckMandateEnable(isDebiCheckMandateEnabled);
        offerNavigationModel.setCCAPOEnabled(isCCAPOEnabled);
        offerNavigationModel.setSource(source);
        offerNavigationModel.setUniqueIdInSourceSystem(uniqueIdSourceSystem);
        offerNavigationModel.setDeeplink(deeplink);
        offerNavigationModel.setDeeplinkIdentifier(deeplinkIdentifier);
        return offerNavigationModel;
    }

    private NavigationTarget prepareNavigationTarget(Long offerTypeId,
                                                     Long offerId,
                                                     String offerType,
                                                     long offerCategory,
                                                     boolean isDebiCheckMandateEnabled,
                                                     boolean isCCAPOEnable, String source,
                                                     String uniqueIdSourceSystem,
                                                     String deeplink,
                                                     String deeplinkIdentifier) {
        NavigationTarget navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.LOAN_LANDING_ACTIVITY);
        navigationTarget.withParam(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.DASHBOARD_SCREEN);
        navigationTarget.withParam(Constants.BundleKeys.OFFER_MODEL,
                prepareOfferNavigationModelObject(offerTypeId, offerId, offerType, offerCategory, isDebiCheckMandateEnabled, isCCAPOEnable, source, uniqueIdSourceSystem, deeplink, deeplinkIdentifier));
        navigationTarget.withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.FLOW_ENTRY_DASHBOARD_CAROUSEL, true);
        return navigationTarget;
    }

    private void navigateToInvestmentTaxCertificates() {
        trackActionWithContextDataNA(TrackingEvent.CLICK_TAX_CERT_DASH_TAXCERTINV);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TAX_CERTIFICATE_ACCOUNTS);
        if (invetmentAccountsOverview != null && invetmentAccountsOverview.accountSummaries != null && invetmentAccountsOverview.accountSummaries.size() > 0)
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.TAX_CERTIFICATES_ACCOUNTS, invetmentAccountsOverview);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void handleOnlineSavingsPocketsClick(AccountSummary accountSummary) {
        if (accountSummary != null) {
            if (accountSummary.getSavingsPocketCounter() > 0) {
                navigateToPocketListingScreen(accountSummary.getNumber());
            } else {
                navigateToEducationalScreen(accountSummary.getNumber());
            }
        }
    }

    void trackProductGroupSelected() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setFeature(TrackingEvent.ANALYTICS.VAL_PRODUCT_ON_BOARDING);
        adobeContextData.setFeatureCategory(TrackingEvent.ANALYTICS.VAL_SALES_AND_ONBOARDING);
        adobeContextData.setCategoryAndProduct(String.format(ServicesTracking.VAL_SAVINGS_POCKET, ""));
        adobeContextData.setEntryPoint(TrackingParam.VAL_APPLY_DASHBOARD);
        adobeContextData.setProductCategory(ServicesTracking.VAL_SAVING_POCKET);
        analytics.sendEventActionWithMap(ServicesTracking.KEY_PRODUCT_GROUP_SELECTED, cdata);
    }

    private void navigateToEducationalScreen(String transactionalAccountNo) {

        getFicaStatusUseCase
                .execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                }).subscribe(ficaStatusDataModel -> {
                    if (ficaStatusDataModel.getIsFica()) {
                        trackProductGroupSelected();
                        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.ONLINE_SAVINGS_EDUCATIONAL_SCREEN)
                                .withParam(za.co.nedbank.services.Constants.SAVINGS_POCKETS_TP_ACCOUNT, transactionalAccountNo);
                        navigationRouter.navigateTo(navigationTarget);
                    } else {
                        showClientTypeErrorActivity(za.co.nedbank.core.Constants.INVONLINE_ONIA_ERROR_FICA_ERROR_TYPE);
                    }
                }, throwable -> {
                    if (view != null) {
                        view.showError(errorHandler.getErrorMessage(throwable).getMessage());
                    }
                });
    }

    private void openEducationalScreen(String transactionalAccountNo) {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.ONLINE_SAVINGS_EDUCATIONAL_SCREEN)
                .withParam(za.co.nedbank.services.Constants.SAVINGS_POCKETS_TP_ACCOUNT, transactionalAccountNo)
                .withParam(za.co.nedbank.services.Constants.CURRENT_TP_ACCOUNTS, mOnlineSavingsAccounts);
        navigationRouter.navigateTo(navigationTarget);
    }

    void getPocketLinkedAccounts(List<AccountSummary> pocketAccountSummary) {
        int i = 0;
        if (pocketAccountSummary != null && !pocketAccountSummary.isEmpty()) {
            String[] itemAccountIds = new String[pocketAccountSummary.size()];
            for (AccountSummary accountSummary : pocketAccountSummary) {
                itemAccountIds[i++] = accountSummary.getId();
            }
            mPocketLinkedUseCase.execute(itemAccountIds)
                    .compose(bindToLifecycle()).doOnSubscribe(disposable -> {
                    }).subscribe(linkedPockets -> {
                        String transactionalAccountNumber = filterLinkedDataAndReturnTPAccount(
                                mPocketLinkedResponseDomainToViewMapper.mapPockets(linkedPockets));
                        openEducationalScreen(transactionalAccountNumber);

                    }, throwable -> {
                        if (view != null) {
                            view.showError(errorHandler.getErrorMessage(throwable).getMessage());
                        }
                    });
        }
    }

    private String filterLinkedDataAndReturnTPAccount(List<PocketLinkedResponseViewModel> linkedResponseViewModels) {
        String transactionalAccountNumber = null;
        Map<String, Integer> mPocketRelationshipMap = new LinkedHashMap<>();
        List<AccountSummary> tpAccountSummaries = mOnlineSavingsAccounts.getCurrentAccounts();
        if (linkedResponseViewModels != null && !linkedResponseViewModels.isEmpty()) {
            for (AccountSummary accountSummary : tpAccountSummaries) {
                mPocketRelationshipMap.put(accountSummary.getNumber(), 0);
                for (PocketLinkedResponseViewModel responseViewModel : linkedResponseViewModels) {
                    if (accountSummary.getNumber().equals(responseViewModel.getPrimaryAccount()
                            .replaceAll(StringUtils.REMOVE_LEADING_ZEROS, StringUtils.EMPTY_STRING))) {
                        Integer count = mPocketRelationshipMap.get(accountSummary.getNumber());
                        mPocketRelationshipMap.put(accountSummary.getNumber(), ++count);
                    }
                }
            }
        }
        for (Map.Entry<String, Integer> entry : mPocketRelationshipMap.entrySet()) {
            if (entry.getValue() < za.co.nedbank.services.Constants.MAX_SAVINGS_POCKET) {
                transactionalAccountNumber = entry.getKey();
                break;
            }
        }
        return transactionalAccountNumber;
    }

    private void navigateToPocketListingScreen(String transactionalAccountNo) {
        trackActionWithContextDataNA(ServicesTrackingValue.CLICK_ANDROID_POCKET_LIST);
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.SAVINGS_POCKET_LIST_SCREEN)
                .withParam(za.co.nedbank.services.Constants.SAVINGS_POCKETS_TP_ACCOUNT, transactionalAccountNo)
                .withParam(za.co.nedbank.services.Constants.IS_NPW_ENABLED_ACCOUNT, mIsNPWEnabledAccount)
                .withParam(za.co.nedbank.services.Constants.IS_DCAR_ENABLED_ACCOUNT, mIsDCAREnabledAccount)
                .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, clientType);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void navigateToAccountTypeInvestmentActivity(AccountSummary accountSummary) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_ACCOUNT_TYPE_LIST)
                .withParam(ServicesNavigationTarget.INVONLINE_ACCOUNT_LIST, accountSummary.getProductCategory().getAccountSummaries())
                .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, view.canTransact())
                .withParam(ServicesNavigationTarget.INVONLINE_DEFALUT_ACCOUNT_ID, defaultAccountId)
                .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, clientType)
                .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, view.getFicaStatus())
                .withParam(ServicesNavigationTarget.INVONLINE_ACCOUNT_TYPE, accountSummary.getName())
                .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                .withParam(ServicesNavigationTarget.PARAM_ONIA_BIRTH_DATE, birthDate)
                .withParam(ServicesNavigationTarget.PARAM_ONIA_SEC_OFFICER_CD, secOfficerID);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void navigateToAccountDetails(AccountSummary accountSummary) {
        navigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_ACCOUNT_TYPE, accountSummary.getAccountCode())
                .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, view.canTransact())
                .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, clientType)
                .withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate())
                .withParam(ServicesNavigationTarget.INVONLINE_FIRST_CONVERSION_DATE, accountSummary.getFirstAvailableConversionDate())
                .withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice())
                .withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit())
                .withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct())
                .withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_ADD_RECURRING_PAYMENT, accountSummary.getAddRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit())
                .withParam(ServicesNavigationTarget.INVONLINE_VIEW_PAYOUT, accountSummary.getViewPayout())
                .withParam(ServicesNavigationTarget.INVONLINE_DELETE_PAYOUT, accountSummary.getDeletePayout())
                .withParam(ServicesNavigationTarget.INVONLINE_VIEW_REINVEST, accountSummary.getViewReinvest())
                .withParam(ServicesNavigationTarget.INVONLINE_DELETE_REINVEST, accountSummary.getDeleteReinvest())
                .withParam(ServicesNavigationTarget.INVONLINE_ACCOUNT_PLEDGE_STATUS, accountSummary.isPledgeAccount())
                .withParam(ServicesNavigationTarget.INVONLINE_DRAWN_DOWN_ACCOUNT_STAUS, accountSummary.getStatus())
                .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, view.getFicaStatus())
                .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                .withParam(ServicesNavigationTarget.PARAM_IS_SA_RESIDENT, view.isSAResident())
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber())
                .withParam(ServicesNavigationTarget.INVONLINE_PRODUCT_TYPE, accountSummary.getProductType())
                .withParam(ServicesNavigationTarget.PARAM_ONIA_CLIENT_TYPE, clientType)
                .withParam(ServicesNavigationTarget.PARAM_ONIA_BIRTH_DATE, birthDate)
                .withParam(ServicesNavigationTarget.PARAM_ONIA_SEC_OFFICER_CD, secOfficerID)
                .withParam(ServicesNavigationTarget.PARAM_ONIA_CIS_NUMBER, cisNumber)
        );
    }

    void onJoinButtonClick(AccountSummary accountSummary, OverviewType overviewType) {
        if (overviewType.getValue() == OverviewType.INVESTMENTS.getValue() && !accountSummary.isIspendingAccount()) {
            navigateToInvestmentOptions();
        } else if (accountSummary.getAccountType() == OverviewType.EVERYDAY_BANKING &&
                za.co.nedbank.services.Constants.SAVINGS_POCKET_PRODUCT_TYPE.equals(accountSummary.getId())) {
            trackActionWithContextDataNA(ServicesTrackingValue.CLICK_ANDROID_SETUP_POCKET);
            handleOnlineSavingsPocketsClick(accountSummary);

        } else if (overviewType.getValue() == OverviewType.INSURANCE.getValue()) {
            navigateToInsuranceDashboard(InsuranceConstants.FlowType.QUOTE);
        } else if (accountSummary.getAccountType() == OverviewType.FOREIGN_CURRENCY_ACCOUNT
                && accountSummary.isInternationalBankingAccount()) {
            navigateToInternationalPayment();
        } else if (accountSummary.getAccountType() == OverviewType.FOREIGN_CURRENCY_ACCOUNT
                && accountSummary.isFCAAccount()) {
            navigateToFCAOpen();
        } else if (accountSummary.getAccountType() == OverviewType.LIFESTYLE
                && accountSummary.isFamilyBankingAccount()) {
            navigateToFamilyBanking();
        } else {
            navigateToRewardsLandingScreen(accountSummary);
        }
    }

    public void navigateToInvestment() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setCategoryAndProduct(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_INVESTMENT);
        adobeContextData.setEntryPoint(TrackingParam.VAL_APPLY_DASHBOARD);
        adobeContextData.setProductCategory(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_GROUP_INVESTMENT);
        adobeContextData.setFeature(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_ON_BOARDING);
        adobeContextData.setFeatureCategory(EnrollV2TrackingValue.ANALYTICS.VAL_SALES_AND_ON_BOARDING);
        analytics.sendEventActionWithMap(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_SELECTED, cdata);
        if (isClientTypeValidForInvestments()) {
            getFicaStatus();
        } else {
            showClientTypeErrorActivity(za.co.nedbank.core.Constants.INVONLINE_ONIA_ERROR_INVALID_CLIENT_TYPE);
        }
    }

    public void navigateToAvoLifestyleApp(MediaCardViewModel mMediaCardViewModel) {
        trackAppsFlyerEvent();
        getAvoWalletDetails(mMediaCardViewModel);
    }

    public boolean isBusinessUser() {
        return mIsBusinessUser;
    }

    public boolean isMerchantUser() {
        return mIsMerchantUser;
    }

    private void getAvoWalletDetails(MediaCardViewModel mMediaCardViewModel) {
        if (view != null) {
            view.showOverviewLoading(true);
        }
        mWalletDetailsUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(
                        result -> openAvoAppInBrowser(result, mMediaCardViewModel),
                        throwable -> {
                            String apiErrorCode = null;
                            if (throwable instanceof HttpException) {
                                apiErrorCode = String.valueOf(((HttpException) throwable).code());
                            }
                            Map<String, Object> cdata = new HashMap<>();
                            AdobeContextData adobeContextData = new AdobeContextData(cdata);
                            adobeContextData.setInternalCompaign(mMediaCardViewModel.getName());
                            trackFailure(true, AppTracking.MY_ACCOUNTS_BANNER_FAILURE, ApiAliasConstants.AVO_WLD, errorHandler.getErrorMessage(throwable).getMessage(), apiErrorCode, cdata);
                            if (view != null) {
                                view.showError(errorHandler.getErrorMessage(throwable).getMessage());
                                view.showOverviewLoading(false);
                            }
                        }
                );
    }

    public void trackFailure(boolean isApiFailure, String tagName, String apiName, String message, String apiErrorCode, Map<String, Object> cdata) {
        analytics.trackFailure(isApiFailure, tagName, apiName, message, apiErrorCode, cdata);
    }

    public void loadMediaContent() {
        mMediaContentUseCase.execute(AppState.POST_LOGIN)
                .compose(bindToLifecycle())
                .subscribe(appLayoutViewModel -> {
                    if (view != null) {
                        if (appLayoutViewModel != null && appLayoutViewModel.getMediaCard() != null)
                            view.updateDynamicAvoBanner(appLayoutViewModel.getMediaCard());
                        else
                            view.updateLocalAvoBanner();
                    }
                }, throwable -> {
                    if (view != null) {
                        view.updateLocalAvoBanner();
                    }
                });
    }

    private void openAvoAppInBrowser(AvoWalletDetailsModel result, MediaCardViewModel mMediaCardViewModel) {
        logOpenAVOAnalytics(result, mMediaCardViewModel);
        if (mMediaCardViewModel != null && mMediaCardViewModel.getExpandedCTALink() != null
                && !mMediaCardViewModel.getExpandedCTALink().isEmpty()) {
            if (mMediaCardViewModel.getExpandedCTALink().startsWith(HTTPS_STRING)
                    || mMediaCardViewModel.getExpandedCTALink().startsWith(HTTP_STRING)) {
                if (view != null) {
                    view.openAvoInWebBrowser(mMediaCardViewModel.getExpandedCTALink());
                }
            } else {
                openAVODeepLinkScreen(mMediaCardViewModel);
            }
        } else {
            if (DeviceUtils.isStoreAndProdBuild()) {
                view.openAvoInWebBrowser(AVO_HOME_URL_PROD);
            } else {
                view.openAvoInWebBrowser(AVO_HOME_URL_QA);
            }
        }
    }

    private void logOpenAVOAnalytics(AvoWalletDetailsModel result, MediaCardViewModel mMediaCardViewModel) {
        Map<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setContext10(result.getAvoId());
        adobeContextData.setEntryPoint(TrackingParam.VAL_APP_MY_ACCOUNTS);
        if (mMediaCardViewModel != null) {
            adobeContextData.setInternalCompaign(mMediaCardViewModel.getName());
        }
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_BANNER_SUCCESS, cdata);
    }

    private void openAVODeepLinkScreen(MediaCardViewModel mMediaCardViewModel){
        String deeplink = mMediaCardViewModel.getExpandedCTALink();
        if(deeplink != null && DynamicFeatureCardDetailEnum.getEnum(deeplink) != DynamicFeatureCardDetailEnum.LOGIN){
            view.openAVODeepLinkScreen(deeplink);
        } else {
            navigateToNavigationHandler(deeplink);
        }
    }

    private void navigateToNavigationHandler(String action) {
        NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.TARGET_GLOBAL_NAVIGATION_HANDLER)
                .withParam(NotificationConstants.Navigation.TARGET, action)
                .withParam(NotificationConstants.Navigation.FROM, NotificationConstants.AjoConstants.PUSH_NOTIFICATION);
        navigationRouter.navigateWithResult(targetScreen).subscribe(
                navigationResult -> {
                    boolean actionError = NotificationConstants.AjoConstants.ERROR
                            .equalsIgnoreCase(navigationResult.getStringParam(NotificationConstants.AjoConstants.ACTION));
                    if (navigationResult.isOk() && actionError) moveToErrorScreen(action);
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    void moveToErrorScreen(String deeplink) {
        Map<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setInternalCompaign(deeplink);
        trackFailure(false, AppTracking.MY_ACCOUNTS_BANNER_FAILURE, null, TrackingEvent.ANALYTICS.SCREEN_OPTION_IS_CURRENTLY_NOT_AVAILABLE, null, cdata);
        navigationRouter.navigateTo(
                NavigationTarget.to(NavigationTarget.AVO_FAILURE_SCREEN)
                        .withParam(NavigationTarget.PARAM_FAILURE_TITLE, view.getErrorScreenTitle())
                        .withParam(NavigationTarget.PARAM_FAILURE_DESCRIPTION, view.getErrorScreenDescription())
                        .withParam(NavigationTarget.PARAM_FAILURE_BUTTON_TEXT, view.getErrorScreenButtonTitle()));
    }

    void trackAppsFlyerEvent() {
        mAfAnalyticsTracker.sendEvent(AppTracking.AF_AVO_REDIRECT, null);
    }

    private void navigateToInternationalPayment() {
        mMemoryApplicationStorage.clearValue(StorageKeys.FOREX_IS_PAY_FRM_RECIPIENT);
        mMemoryApplicationStorage.clearValue(StorageKeys.FOREX_GO_TO_HOME);
        String subEvent = isBusinessUser() ? PaymentsTrackingEvent.SUB_FEATURE_PRODUCT_SBS : PaymentsTrackingEvent.SUB_FEATURE_PRODUCT_IND;
        Map<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setFeatureCategory(TrackingParam.VAL_MONETARY_TRANSACTIONS);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, PaymentsTrackingEvent.INTERNATIONAL_PAYMENT);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_SUBFEATURE, subEvent);
        analytics.sendEventActionWithMap(PaymentsTrackingEvent.ITT_FOREXCONTAINER_IPENTRY, cdata);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.INTERNATIONAL_PAYMENT_OPTIONS));
    }

    private void navigateToFCAOpen() {
        mMemoryApplicationStorage.clearValue(StorageKeys.FOREX_IS_PAY_FRM_RECIPIENT);
        mMemoryApplicationStorage.clearValue(StorageKeys.FOREX_GO_TO_HOME);
        Map<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, PaymentsTrackingEvent.FCA_ANALYTICS_TAGS);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_SUBFEATURE, PaymentsTrackingEvent.SUB_FEATURE_PRODUCT_IND);
        analytics.sendEventActionWithMap(PaymentsTrackingEvent.FCA_FOREX_CONTAINER_ENTRY, cdata);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FCA_ACCOUNTS_LIST_SCREEN));
    }

    protected void navigateToFamilyBanking() {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.FAMILY_BANKING_STATUS_LOADING_SCREEN);
        navigationRouter.navigateTo(navigationTarget);
    }

    void trackAppsFlyerEventFBView() {
        mAfAnalyticsTracker.sendEvent(AppTracking.AF_FAMILY_BANKING_VIEW, null);
    }

    private void navigateToInsuranceDashboard(String flowType) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setEntryPoint(TrackingParam.VAL_APPLY_DASHBOARD);
        adobeContextData.setCategoryAndProduct(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_INSURANCE);
        adobeContextData.setProductCategory(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_GROUP_INSURANCE);
        adobeContextData.setFeature(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_ON_BOARDING);
        adobeContextData.setFeatureCategory(EnrollV2TrackingValue.ANALYTICS.VAL_SALES_AND_ON_BOARDING);
        analytics.sendEventActionWithMap(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_SELECTED, cdata);

        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN)
                .withParam(InsuranceConstants.ParamKeys.IS_FLOW_TYPE_MY_POLICY, flowType);
        navigationRouter.navigateTo(navigationTarget);
    }

    public boolean isClientTypeValid() {
        return view != null && clientType != null && (!clientType.equals(za.co.nedbank.core.Constants.CLIENT_TYPE_51) && !clientType.equals(za.co.nedbank.core.Constants.CLIENT_TYPE_52));
    }

    public boolean isClientTypeValidForInvestments() {
        return view != null && clientType != null && (!clientType.equals(za.co.nedbank.core.Constants.CLIENT_TYPE_51));
    }

    void getFicaStatus() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NGI_GET_STARTED).withParam(PARAM_ONIA_CLIENT_TYPE, clientType)
                .withParam(PARAM_ONIA_BIRTH_DATE, birthDate).withParam(PARAM_ONIA_SEC_OFFICER_CD, secOfficerID).withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, cisNumber));
    }

    void getPendingAccounts(Overview overviewValue) {
        getPendingAccountsUsecase
                .execute(za.co.nedbank.core.Constants.API_PARAM_OPEN_ACCOUNT, za.co.nedbank.core.Constants.API_PARAM_PENDING)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                })
                .doOnTerminate(() -> {
                })
                .subscribe(getPendingAccountDataListEntity -> {
                    pendingAccountListDataModel = pendingAccountsEntitiyToDataModelMapper.
                            getPendingAccountsDataModels(getPendingAccountDataListEntity);
                    handleOverviewData(overviewValue);
                }, throwable -> {
                    if (view != null) {
                        handleOverviewData(overviewValue);
                    }
                });
    }

    void showClientTypeErrorActivity(int errorType) {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_ONIA_INVESTMENT_ERROR)
                .withParam(NavigationTarget.KEY_ONIA_ERROR_TYPE, errorType));
    }

    private void navigateToRewardsLandingScreen(AccountSummary accountSummary) {
        boolean isEnrolmentInProgress = mApplicationStorage.getBoolean(StorageKeys.IS_GB_REWARDS_ENROLMENT_IN_PROGRESS, false);
        if (isEnrolmentInProgress) {
            NBLogger.d(TAG, "Is GB rewards enrolment in progress: " + isEnrolmentInProgress);
            return;
        }
        long timestamp = System.currentTimeMillis() / 1000;
        trackGreenbackRewards(timestamp);
        if (accountSummary != null && accountSummary.getAccountType() == OverviewType.REWARDS) {
            boolean isJuristic = isBusinessUser() && !featureSetController.isFeatureDisabled(FeatureConstants.FTR_GREENBACKS_JURISTIC);

            NavigationTarget navigationTarget = NavigationTarget.to(ProfileNavigationTarget.REWARDS_LANDING)
                    .withParam(za.co.nedbank.profile.common.Constants.Extras.ID_NUMBER, accountSummary.getId());
            if (isJuristic && !featureSetController.isFeatureDisabled(FeatureConstants.FTR_GREENBACKS_V2)) {
                navigationTarget = NavigationTarget.to(ProfileNavigationTarget.REWARD_ENROLL_LANDING_ACTIVITY)
                        .withParam(za.co.nedbank.profile.common.Constants.Extras.ID_NUMBER, accountSummary.getId());
            }
            navigationRouter.navigateTo(navigationTarget);
        }
    }

    void trackGreenbackRewards(long gbStartTime) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setCategoryAndProduct(ProfileTracking.VAL_REWARDS_GREENBACKS);
        adobeContextData.setProductAccount(ProfileTracking.VAL_PRODUCT_GREENBACKS);
        adobeContextData.setInitiations();
        adobeContextData.setEntryPoint(TrackingParam.VAL_APPLY_DASHBOARD);
        mMemoryApplicationStorage.putLong(StorageKeys.PRODUCT_ON_BOARDING_TIME, gbStartTime);
        analytics.sendEventActionWithMap(ProfileTracking.KEY_PRODUCT_SELECTED, cdata);

        Map<String, Object> gBData = new HashMap<>();
        AddContextData addContextData = new AddContextData(gBData);
        addContextData.setProduct(ProfileTracking.VAL_PRODUCT_GREENBACKS);
        addContextData.setOnBoardingProcess(AppsFlyerTags.VAL_REAL_TIME);
        addContextData.setProductGroup(ProfileTracking.VAL_PRODUCT_GROUP_REWARDS);
        mAfAnalyticsTracker.sendEvent(AppsFlyerTags.AF_EXISTING_CLIENT_INITIATION, gBData);
    }

    void handleClickNotificationCount() {
        trackActionMyAccountProductGroup(AppTracking.MY_ACCOUNTS_NOTIFICATIONS);
        boolean isNotificationDisabled = featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS);
        String target = isNotificationDisabled ? PreApprovedOffersNavigationTarget.PRE_APPROVED_OFFERS_NOTIFICATIONS : NavigationTarget.TARGET_NOTIFICATION_CENTER;
        NavigationTarget navigationTarget = NavigationTarget.to(target);
        navigationTarget.withParam(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.DASHBOARD_SCREEN);
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_JOURNEY_FLAG, za.co.nedbank.core.Constants.FLOW_CONSTANTS.POST_LOGIN_DASHBORD_NOTIFICATION_FLOW);
        navigationRouter.navigateTo(navigationTarget);

    }

    void getUserInfo() {
        getUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(this::getUserInfoData,
                        throwable -> {
                            Error error = errorHandler.getErrorMessage(throwable);
                            if (view != null) {
                                if (error.getCode() == HttpStatus.NO_CONTENT) {
                                    view.showEmptyView(error.getMessage());
                                } else {
                                    view.showError(error.getMessage());
                                }
                            }
                        });
    }

    @SuppressLint("CheckResult")
    void getInsuranceOffers() {
        mInsGetOffersUseCase.execute(PLATFORM_CHANNEL_ID,PREFERENCE_TYPE)
                .compose(bindToLifecycle())
                .subscribe(this::getInsuranceOffersResponse,
                        throwable -> mMemoryApplicationStorage.putObject(StorageKeys.INS_GET_OFFERS_RESPONSE,new InsGetOfferResponseDataModel()));
    }

    private void getInsuranceOffersResponse(InsGetOfferResponseDataModel insGetOfferResponseDataModel) {
        InsGetOfferResponseViewModel insuranceOffersResponse = mInsGetOfferDataToViewModelMapper.transform(insGetOfferResponseDataModel);
        if (insuranceOffersResponse != null && insuranceOffersResponse.getInsGetOfferDetailsViewModel() != null) {
            view.showInsuranceOffersCount(insuranceOffersResponse.getInsGetOfferDetailsViewModel().getOffersList());
        }
    }

    public void checkFatcaRestrictions(ApplicationStorage mMemoryApplicationStorage) {
        if (FatcaRestrictionUtil.isFatcaRestrictionsApplicable(mMemoryApplicationStorage, featureSetController)) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FATCA_RESTRICTION_MESSAGE_SCREEN));
        }
    }

    public void getUserInfoData(final UserDetailData userDetailData) {
        if (userDetailData != null && view != null) {
            mMemoryApplicationStorage.putObject(ResponseStorageKey.USERDETAILDATA,
                    mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData));
            mMemoryApplicationStorage.putString(StorageKeys.CIS_NUMBER, userDetailData.getCisNumber());
            mApplicationStorage.putString(StorageKeys.CIS_NUMBER, userDetailData.getCisNumber());
            mIsBusinessUser = userDetailData.getClientType() != null && Integer.parseInt(userDetailData.getClientType())
                    > za.co.nedbank.services.Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;
            mMemoryApplicationStorage.putBoolean(StorageKeys.IS_BUSINESS_USER, mIsBusinessUser);
            mIsNPWEnabledAccount = featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ONLINE_SAVINGS_NPW_POCKET_BUTTON);
            mIsDCAREnabledAccount = userDetailData.getSecOfficerCd() != null
                    && featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ONLINESAVINGS_PBADD_POCKET_BUTTON_PROFESSIONAL)
                    && (userDetailData.getSecOfficerCd().startsWith(za.co.nedbank.services.Constants.SEC_OFFICER_CD_STARTS_76)
                    || userDetailData.getSecOfficerCd().startsWith(za.co.nedbank.services.Constants.SEC_OFFICER_CD_STARTS_82));
            clientType = userDetailData.getClientType();
            secOfficerID = userDetailData.getSecOfficerCd();
            cisNumber = userDetailData.getCisNumber();
            mIsMerchantUser = userDetailData.isMerchantUser();
            birthDate = userDetailData.getBirthDate();
            idOrTaxIdNoStr = userDetailData.getIdOrTaxIdNumberString();
            view.setUserInfo(mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData));
            boolean showGetCashIcon = !mIsBusinessUser && getClientType() == ClientType.TP;
            view.showGetCashIcon(showGetCashIcon);
            view.showShopIcon(mIsBusinessUser || getClientType() == ClientType.TP);
        }
    }

    private boolean checkForAnyTransactableAccount(Overview overview) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.CREDIT_CARDS ||
                        accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.accountSummaries != null) {
                    for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
                        if (!accountSummary.isDormantAccount()) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private void addSavingPocketsForCurrentAccount(Overview overview) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING) && accountsOverview.accountSummaries != null) {
                    setOnlineSavingsAccounts(accountsOverview.onlineSavingsAccounts);
                    if (accountsOverview.onlineSavingsAccounts != null && accountsOverview.onlineSavingsAccounts.getCurrentAccounts() != null
                            && !accountsOverview.onlineSavingsAccounts.getCurrentAccounts().isEmpty()) {
                        if (accountsOverview.onlineSavingsAccounts.getShownPocketCounter() > 0) {
                            AccountSummary myPocket = getSavingsPocketWithAmountSummary(accountsOverview.onlineSavingsAccounts.getCurrentAccounts().get(0).getNumber(),
                                    BigDecimal.valueOf(accountsOverview.onlineSavingsAccounts.getPocketAmount()), accountsOverview.onlineSavingsAccounts.getPocketsCounter(), accountsOverview.onlineSavingsAccounts.getTpAccountCounter());
                            if (preApprovedOfferPosition != -1) {
                                accountsOverview.accountSummaries.add(preApprovedOfferPosition, myPocket);
                            } else {
                                accountsOverview.accountSummaries.add(myPocket);
                            }
                            removePocketAccounts(accountsOverview);
                        } else {
                            if (!mIsNPWEnabledAccount && !mIsDCAREnabledAccount && !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ONLINE_SAVINGS_POCKET_BUTTON) &&
                                    accountsOverview.onlineSavingsAccounts.getPocketsCounter() < (za.co.nedbank.services.Constants.MAX_SAVINGS_POCKET * accountsOverview.onlineSavingsAccounts.getTpAccountCounter())) {
                                AccountSummary myPocket = getSavingsPocketAccountSummary(accountsOverview.onlineSavingsAccounts.getCurrentAccounts().get(0).getNumber());
                                if (preApprovedOfferPosition != -1) {
                                    accountsOverview.accountSummaries.add(preApprovedOfferPosition, myPocket);
                                } else {
                                    accountsOverview.accountSummaries.add(myPocket);
                                }
                                removePocketAccounts(accountsOverview);
                            }
                        }
                    }
                }
            }
        }
    }

    public void setOnlineSavingsAccounts(OnlineSavingsAccounts onlineSavingsAccounts) {
        mOnlineSavingsAccounts = onlineSavingsAccounts;
    }

    private void removePocketAccounts(AccountsOverview accountsOverview) {
        if (accountsOverview != null) {
            List<AccountSummary> accountSummaries = accountsOverview.accountSummaries;
            if (accountSummaries != null) {
                Iterator<AccountSummary> iterator = accountSummaries.iterator();
                while (iterator.hasNext()) {
                    AccountSummary accountSummary = iterator.next();
                    if (za.co.nedbank.services.Constants.SAVINGS_POCKET_PRODUCT_TYPE.equals(accountSummary.getProductType())
                            || (StringUtils.SPACE.equals(accountSummary.getProductType()) && accountSummary.getAccountCode()
                            .equalsIgnoreCase(za.co.nedbank.core.Constants.ACCOUNT_TYPES.SA.name()))) {
                        iterator.remove();
                    }
                }
            }
        }
    }

    private void addInsurance(Overview overview) {
        if (view != null && overview != null && overview.accountsOverviews != null) {
            overview.accountsOverviews.add(getInsuranceOverview());
        }
    }


    private void calculateMaximumLoanAmountOfferedUnread(List<PreApprovedOffersInnerDetailsViewModel> preApprovedOffersInnerDetailsViewModelList) {
        double maximumLoanAmount = 0.0;
        double maximumConsolidatedLoanAmount = 0.0;
        double maximumCardLimit = 0.0;

        int consolidatedLoanOfferIndex = -1;
        if (CollectionUtils.isNotEmpty(preApprovedOffersInnerDetailsViewModelList)) {
            PreApprovedOffersInnerDetailsViewModel preApprovedOffersInnerDetailsViewModel;
            for (int count = 0; count < preApprovedOffersInnerDetailsViewModelList.size(); count++) {
                preApprovedOffersInnerDetailsViewModel = preApprovedOffersInnerDetailsViewModelList.get(count);
                if (StringUtils.isNullOrEmpty(preApprovedOffersInnerDetailsViewModel.getStatus()) && preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersAmountViewModel() != null) {
                        double loanAmount = preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersAmountViewModel().getAmount();
                        if (!StringUtils.isNullOrEmpty(preApprovedOffersInnerDetailsViewModel.getMessage())) {
                            preApprovedOffersInnerDetailsViewModel.setMessage(removeAllHTMLFormatCodes(preApprovedOffersInnerDetailsViewModel.getMessage()));
                        }
                        if (preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel() != null) {
                            if (loanAmount > 0) {
                                if (!TextUtils.isEmpty(preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel().getCode())) {
                                    switch (preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel().getCode()) {
                                        case za.co.nedbank.core.Constants.PreApprovedOffersTypes.PERSONAL_LOAN_OFFER:
                                            if (maximumLoanAmount < loanAmount) {
                                                loanOfferDisplayedIndex = count;
                                                preApprovedOfferType = za.co.nedbank.core.Constants.PreApprovedOffersTypes.PERSONAL_LOAN_OFFER;
                                                maximumLoanAmount = loanAmount;
                                            }
                                            break;
                                        case za.co.nedbank.core.Constants.PreApprovedOffersTypes.CONSOLIDATED_LOAN_OFFER:
                                            if (maximumConsolidatedLoanAmount < loanAmount) {
                                                consolidatedLoanOfferIndex = count;
                                                preApprovedOfferType = za.co.nedbank.core.Constants.PreApprovedOffersTypes.CONSOLIDATED_LOAN_OFFER;
                                                maximumConsolidatedLoanAmount = loanAmount;
                                            }
                                            break;
                                        case za.co.nedbank.core.Constants.PreApprovedOffersTypes.NEW_CREDIT_CARD_OFFER:
                                        case za.co.nedbank.core.Constants.PreApprovedOffersTypes.CARD_LIMIT_INCREASE_OFFER:
                                            if (maximumCardLimit < loanAmount) {
                                                cardOfferDisplayedIndex = count;
                                                cardOfferType = preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel().getCode();
                                                maximumCardLimit = loanAmount;
                                            }
                                            break;
                                    }
                                }
                            }
                        }
                }
            }
        }
        if (maximumConsolidatedLoanAmount > 0) {
            loanAmountOffered = maximumConsolidatedLoanAmount;
            loanOfferDisplayedIndex = consolidatedLoanOfferIndex;
            preApprovedOfferType = za.co.nedbank.core.Constants.PreApprovedOffersTypes.CONSOLIDATED_LOAN_OFFER;
        } else if (maximumLoanAmount > 0) {
            loanAmountOffered = maximumLoanAmount;
        }
        if (maximumCardLimit > 0) {
            cardLimitOffered = maximumCardLimit;
        }
    }

    private String removeAllHTMLFormatCodes(String message) {
        if (!StringUtils.isNullOrEmpty(message)) {
            message = message.replaceAll(Constants.NON_BREAKING_SPACE_HTML_STRING, StringUtils.SPACE);
            message = message.replaceAll(Constants.START_AMOUNT_HTML_STRING, StringUtils.SPACE);
            message = message.replaceAll(Constants.END_AMOUNT_HTML_STRING, StringUtils.SPACE);
        }
        return message;
    }

    @SuppressLint("WrongConstant")
    private void addOfferToOverview(Overview overviewValue) {
        preApprovedOfferPosition = -1;
        if (CollectionUtils.isNotEmpty(overviewValue.accountsOverviews) && hasPreApprovedOffersDetailsViewModel()) {
            boolean isLoanAccountExisting = false;
            boolean isCardOfferExisting = false;
            boolean isEveryDayBankingOfferExisting = false;

            for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
                if (accountsOverview.overviewType.getValue() == OverviewType.LOANS.getValue()) {
                    isLoanAccountExisting = true;
                    if (loanAmountOffered > 0 && hasPreApprovedGhostOffers(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel())) {
                        accountsOverview.accountSummaries.add(getPreApprovedOfferAccountSummary(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getId(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getPreApprovedOffersOfferTypeViewModel().getId(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getPreApprovedOffersOfferTypeViewModel().getCode(),
                                OverviewType.LOANS, preApprovedOfferType, loanAmountOffered,
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).isDebiCheckMandateEnable(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).isCCAPOEnabled(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getMainScreenHeader(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getMainScreenCta(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getOfferCategoryViewModel().getId()));
                    } else if (mLoanOffer != null && mLoanOffer.getPreApprovedOffersOfferTypeViewModel() != null && mLoanOffer.getPreApprovedOffersAmountViewModel() != null) {
                        accountsOverview.accountSummaries.add(getPreApprovedOfferAccountSummary(mLoanOffer.getId(), mLoanOffer.getPreApprovedOffersOfferTypeViewModel().getId(), mLoanOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                                OverviewType.LOANS, mLoanOffer.getPreApprovedOffersOfferTypeViewModel().getCode(), mLoanOffer.getPreApprovedOffersAmountViewModel().getAmount(),
                                mLoanOffer.isDebiCheckMandateEnable(), mLoanOffer.isCCAPOEnabled(),mLoanOffer.getMainScreenHeader(),
                                mLoanOffer.getMainScreenCta(), mLoanOffer.getOfferCategoryViewModel().getId()));
                    }
                }
                if (accountsOverview.overviewType.getValue() == OverviewType.CREDIT_CARDS.getValue()) {
                    isCardOfferExisting = true;
                    if (cardLimitOffered > 0 && hasPreApprovedGhostOffers(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel())) {
                        accountsOverview.accountSummaries.add(getPreApprovedOfferAccountSummary(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getId(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getPreApprovedOffersOfferTypeViewModel().getId(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getPreApprovedOffersOfferTypeViewModel().getCode(),
                                OverviewType.CREDIT_CARDS, cardOfferType, cardLimitOffered, preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).isDebiCheckMandateEnable(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).isCCAPOEnabled(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getMainScreenHeader(),
                                preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getMainScreenCta()));
                    } else if (mCreditCardOffer != null && mCreditCardOffer.getPreApprovedOffersOfferTypeViewModel() != null && mCreditCardOffer.getPreApprovedOffersAmountViewModel() != null) {
                        accountsOverview.accountSummaries.add(getPreApprovedOfferAccountSummary(mCreditCardOffer.getId(), mCreditCardOffer.getPreApprovedOffersOfferTypeViewModel().getId(), mCreditCardOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                                OverviewType.CREDIT_CARDS, mCreditCardOffer.getPreApprovedOffersOfferTypeViewModel().getCode(), mCreditCardOffer.getPreApprovedOffersAmountViewModel().getAmount(),
                                mCreditCardOffer.isDebiCheckMandateEnable(), mCreditCardOffer.isCCAPOEnabled(),mCreditCardOffer.getMainScreenHeader(),mCreditCardOffer.getMainScreenCta(), mCreditCardOffer.getOfferCategoryViewModel().getId()));
                    }
                }

                if (accountsOverview.overviewType.getValue() == OverviewType.EVERYDAY_BANKING.getValue()) {
                    isEveryDayBankingOfferExisting = true;
                    addPreApprovedOffersInAccountOverviews(accountsOverview, preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList(), preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getValueOffersViewModelList());
                }
            }
            if (!isLoanAccountExisting) {
                if (loanAmountOffered > 0) {
                    overviewValue.accountsOverviews.add(getPreApprovedOfferAccountOverView(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getId(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getPreApprovedOffersOfferTypeViewModel().getId(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getPreApprovedOffersOfferTypeViewModel().getCode(),
                            OverviewType.LOANS, preApprovedOfferType, loanAmountOffered,
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).isDebiCheckMandateEnable(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).isCCAPOEnabled(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getMainScreenHeader(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getMainScreenCta(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(loanOfferDisplayedIndex).getOfferCategoryViewModel().getId()));
                } else if (mLoanOffer != null && mLoanOffer.getPreApprovedOffersOfferTypeViewModel() != null && mLoanOffer.getPreApprovedOffersAmountViewModel() != null) {
                    overviewValue.accountsOverviews.add(getPreApprovedOfferAccountOverView(mLoanOffer.getId(), mLoanOffer.getPreApprovedOffersOfferTypeViewModel().getId(), mLoanOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                            OverviewType.LOANS, mLoanOffer.getPreApprovedOffersOfferTypeViewModel().getCode(), mLoanOffer.getPreApprovedOffersAmountViewModel().getAmount(),
                            mLoanOffer.isDebiCheckMandateEnable(), mLoanOffer.isCCAPOEnabled(),mLoanOffer.getMainScreenHeader(),mLoanOffer.getMainScreenCta(), mLoanOffer.getOfferCategoryViewModel().getId()));
                }
            }
            if (!isCardOfferExisting) {
                if (cardLimitOffered > 0) {
                    overviewValue.accountsOverviews.add(getPreApprovedOfferAccountOverView(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getId(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getPreApprovedOffersOfferTypeViewModel().getId(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getPreApprovedOffersOfferTypeViewModel().getCode(),
                            OverviewType.CREDIT_CARDS, cardOfferType, cardLimitOffered,
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).isDebiCheckMandateEnable(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).isCCAPOEnabled(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getMainScreenHeader(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(cardOfferDisplayedIndex).getMainScreenCta()));
                } else if (mCreditCardOffer != null && mCreditCardOffer.getPreApprovedOffersOfferTypeViewModel() != null && mCreditCardOffer.getPreApprovedOffersAmountViewModel() != null) {
                    overviewValue.accountsOverviews.add(getPreApprovedOfferAccountOverView(mCreditCardOffer.getId(), mCreditCardOffer.getPreApprovedOffersOfferTypeViewModel().getId(), mCreditCardOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                            OverviewType.CREDIT_CARDS, mCreditCardOffer.getPreApprovedOffersOfferTypeViewModel().getCode(), mCreditCardOffer.getPreApprovedOffersAmountViewModel().getAmount(),
                            mCreditCardOffer.isDebiCheckMandateEnable(), mCreditCardOffer.isCCAPOEnabled(),mCreditCardOffer.getMainScreenHeader(),
                            mCreditCardOffer.getMainScreenCta(), mCreditCardOffer.getOfferCategoryViewModel().getId()));
                }
            }
            if (!isEveryDayBankingOfferExisting) {
                PreApprovedOffersAmountViewModel model = preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(0).getPreApprovedOffersAmountViewModel();
                double amountOffered = model != null ? model.getAmount() : 0;
                overviewValue.accountsOverviews.add(getPreApprovedOfferAccountOverView(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(0).getId(),
                        preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(0).getPreApprovedOffersOfferTypeViewModel().getId(),
                        preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(0).getPreApprovedOffersOfferTypeViewModel().getCode(),
                        OverviewType.EVERYDAY_BANKING, preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(0).getPreApprovedOffersOfferTypeViewModel().getCode(),
                        amountOffered,
                        preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(0).isDebiCheckMandateEnable(),
                        preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(0).isCCAPOEnabled(),
                        preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(0).getMainScreenHeader(),
                        preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(0).getMainScreenCta()));
            }
        }
    }

    private boolean hasPreApprovedOffersDetailsViewModel() {
        return preApprovedOffersViewModel != null
                && preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel() != null;
    }

    private boolean hasPreApprovedGhostOffers(PreApprovedOffersDetailsViewModel preApprovedOffersDetailsViewModel) {
        return CollectionUtils.isNotEmpty(preApprovedOffersDetailsViewModel.getPreApprovedOffersInnerDetailsViewModelList());
    }

    @SuppressLint("WrongConstant")
    public void addPreApprovedOffersInAccountOverviews(AccountsOverview accountsOverview, List<PreApprovedOffersInnerDetailsViewModel> listOffers, List<PreApprovedOffersInnerDetailsViewModel> listValueAddOffers) {
        setValueForPreApprovedOfferPosition(accountsOverview);
        if (!featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_VALUE_ADDS_DASH_OFFERS)) {
            setNewLogicToShowForYouAndValueOffers(accountsOverview, listOffers, listValueAddOffers);
        } else {
            setOldLogicToShowForYouOffers(accountsOverview, listOffers);
        }
    }

    private void setOldLogicToShowForYouOffers(AccountsOverview accountsOverview, List<PreApprovedOffersInnerDetailsViewModel> listOffers) {
        if (listOffers.size() > 2) {
            addGhostOffer(accountsOverview, listOffers.get(0));
            addInvestInsureOffer(listOffers, accountsOverview);
        } else if (!listOffers.isEmpty()) {
            showBothInvestInsureOffers(accountsOverview, listOffers);
        }
    }

    private void addGhostOffer(AccountsOverview accountsOverview, PreApprovedOffersInnerDetailsViewModel preApprovedOffers) {
        double amount = getAmountForPreApprovedOffer(preApprovedOffers);
        int cateId = getCategoryIdForPreApprovedOffer(preApprovedOffers);
        if (preApprovedOffers.getPreApprovedOffersOfferTypeViewModel().getId() != za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INSURE.getOfferTypeId()) {
            AccountSummary accountSummary = getPreApprovedOfferAccountSummary(preApprovedOffers.getId(),
                    preApprovedOffers.getPreApprovedOffersOfferTypeViewModel().getId(),
                    preApprovedOffers.getPreApprovedOffersOfferTypeViewModel().getCode(),
                    OverviewType.EVERYDAY_BANKING,
                    preApprovedOffers.getPreApprovedOffersOfferTypeViewModel().getCode(),
                    amount,
                    preApprovedOffers.isDebiCheckMandateEnable(),
                    preApprovedOffers.isCCAPOEnabled(),
                    preApprovedOffers.getMainScreenHeader(),
                    preApprovedOffers.getMainScreenCta(),
                    cateId);
            accountSummary.setDeeplink(preApprovedOffers.getDeeplink());
            accountSummary.setDeeplinkIdentifier(preApprovedOffers.getDeeplinkIdentifier());
            accountSummary.setSource(preApprovedOffers.getSource());
            accountSummary.setUniqueIdInSourceSystem(preApprovedOffers.getUniqueIdInSourceSystem());
            accountsOverview.accountSummaries.add(accountSummary);
        }
    }

    private void showBothInvestInsureOffers(AccountsOverview accountsOverview, List<PreApprovedOffersInnerDetailsViewModel> listOffers) {
        if (!listOffers.isEmpty()) {
            for (PreApprovedOffersInnerDetailsViewModel preApprovedOffers : listOffers) {
                double amount = getAmountForPreApprovedOffer(preApprovedOffers);
                int cateId = getCategoryIdForPreApprovedOffer(preApprovedOffers);
                AccountSummary accountSummary = getPreApprovedOfferAccountSummary(preApprovedOffers.getId(),
                        preApprovedOffers.getPreApprovedOffersOfferTypeViewModel().getId(),
                        preApprovedOffers.getPreApprovedOffersOfferTypeViewModel().getCode(),
                        OverviewType.EVERYDAY_BANKING,
                        preApprovedOffers.getPreApprovedOffersOfferTypeViewModel().getCode(),
                        amount,
                        preApprovedOffers.isDebiCheckMandateEnable(),
                        preApprovedOffers.isCCAPOEnabled(),
                        preApprovedOffers.getMainScreenHeader(),
                        preApprovedOffers.getMainScreenCta(),
                        cateId);
                accountSummary.setShortMessage(preApprovedOffers.getShortTextMessage());
                accountSummary.setSource(preApprovedOffers.getSource());
                accountSummary.setUniqueIdInSourceSystem(preApprovedOffers.getUniqueIdInSourceSystem());
                accountsOverview.accountSummaries.add(accountSummary);
            }
        }
    }

    private void setNewLogicToShowForYouAndValueOffers(AccountsOverview accountsOverview, List<PreApprovedOffersInnerDetailsViewModel> listOffers, List<PreApprovedOffersInnerDetailsViewModel> listValueAddOffers) {
        boolean isValueOffersListNotEmpty = CollectionUtils.isNotEmpty(listValueAddOffers);
        if (listOffers.size() > 2) {
            logicOnBasicOffersListSizeGreaterThan2(isValueOffersListNotEmpty, accountsOverview, listOffers, listValueAddOffers);
        } else if (listOffers.size() == 2) {
            logicOnBasicOffersListSize2(isValueOffersListNotEmpty, accountsOverview, listOffers, listValueAddOffers);
        } else {
            logicOnBasicOffersListSizeLessThan2(isValueOffersListNotEmpty, accountsOverview, listOffers, listValueAddOffers);
        }
    }

    private void logicOnBasicOffersListSizeGreaterThan2(boolean isValueOffersListNotEmpty, AccountsOverview accountsOverview, List<PreApprovedOffersInnerDetailsViewModel> listOffers, List<PreApprovedOffersInnerDetailsViewModel> listValueAddOffers) {
        PreApprovedOffersInnerDetailsViewModel preApprovedOffers = listOffers.get(0);
        addGhostOffer(accountsOverview, preApprovedOffers);
        if (isValueOffersListNotEmpty) {
            addValueOffer(accountsOverview, listValueAddOffers.get(0));
        } else {
            addInvestInsureOffer(listOffers, accountsOverview);
        }
    }

    private void logicOnBasicOffersListSizeLessThan2(boolean isValueOffersListNotEmpty, AccountsOverview accountsOverview, List<PreApprovedOffersInnerDetailsViewModel> listOffers, List<PreApprovedOffersInnerDetailsViewModel> listValueAddOffers) {
        if (isValueOffersListNotEmpty && listValueAddOffers.size() >= 2) {
            addValueOffer(accountsOverview, listValueAddOffers.get(0));
            addValueOffer(accountsOverview, listValueAddOffers.get(1));
        } else if (isValueOffersListNotEmpty) {
            addValueOffer(accountsOverview, listValueAddOffers.get(0));
            if (!listOffers.isEmpty()) {
                showBothInvestInsureOffers(accountsOverview, listOffers);
            }
        } else {
            showBothInvestInsureOffers(accountsOverview, listOffers);
        }
    }

    private void logicOnBasicOffersListSize2(boolean isValueOffersListNotEmpty, AccountsOverview accountsOverview, List<PreApprovedOffersInnerDetailsViewModel> listOffers, List<PreApprovedOffersInnerDetailsViewModel> listValueAddOffers) {
        if (isValueOffersListNotEmpty && listValueAddOffers.size() >= 2) {
            addValueOffer(accountsOverview, listValueAddOffers.get(0));
            addValueOffer(accountsOverview, listValueAddOffers.get(1));
        } else if (isValueOffersListNotEmpty) {
            addValueOffer(accountsOverview, listValueAddOffers.get(0));
            addInvestInsureOffer(listOffers, accountsOverview);
        } else {
            showBothInvestInsureOffers(accountsOverview, listOffers);
        }
    }

    private void addValueOffer(AccountsOverview accountsOverview, PreApprovedOffersInnerDetailsViewModel valueOffer) {
        double amount = getAmountForPreApprovedOffer(valueOffer);
        int cateId = getCategoryIdForPreApprovedOffer(valueOffer);
        AccountSummary accountSummary = getPreApprovedOfferAccountSummary(valueOffer.getId(),
                valueOffer.getPreApprovedOffersOfferTypeViewModel().getId(),
                valueOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                OverviewType.EVERYDAY_BANKING,
                valueOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                amount,
                valueOffer.isDebiCheckMandateEnable(),
                valueOffer.isCCAPOEnabled(),
                valueOffer.getMainScreenHeader(),
                valueOffer.getMainScreenCta(),
                cateId);
        accountSummary.setShortMessage(valueOffer.getMainScreenSubHeader());
        accountSummary.setSource(valueOffer.getSource());
        accountSummary.setUniqueIdInSourceSystem(valueOffer.getUniqueIdInSourceSystem());
        accountSummary.setDeeplink(valueOffer.getDeeplink());
        accountSummary.setDeeplinkIdentifier(valueOffer.getDeeplinkIdentifier());
        accountsOverview.accountSummaries.add(accountSummary);
    }

    private int getCategoryIdForPreApprovedOffer(PreApprovedOffersInnerDetailsViewModel preApprovedOffers) {
        return preApprovedOffers.getOfferCategoryViewModel() == null ? za.co.nedbank.core.Constants.ZERO : preApprovedOffers.getOfferCategoryViewModel().getId();
    }

    private double getAmountForPreApprovedOffer(PreApprovedOffersInnerDetailsViewModel preApprovedOffers) {
        return preApprovedOffers.getPreApprovedOffersAmountViewModel() == null ? za.co.nedbank.core.Constants.ZERO : preApprovedOffers.getPreApprovedOffersAmountViewModel().getAmount();
    }

    private void setValueForPreApprovedOfferPosition(AccountsOverview accountsOverview) {
        if (preApprovedOfferPosition == -1) {
            preApprovedOfferPosition = accountsOverview.accountSummaries.size();
        }
    }

    void addInvestInsureOffer(List<PreApprovedOffersInnerDetailsViewModel> listOffers, AccountsOverview accountsOverview) {
        PreApprovedOffersInnerDetailsViewModel investInsureOffer = getRequiredPreApprovedOffer(listOffers, za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INVEST.getOfferTypeId());
        String investInsureRotationLogicValue = featureSetController.getDynamicFeatureValue(Constants.INVEST_INSURE_ROTATION_CONFIG);
        if (!TextUtils.isEmpty(investInsureRotationLogicValue)) {
            int offerRotateAfter = 7;
            try {
                offerRotateAfter = Integer.parseInt(investInsureRotationLogicValue.trim());
            } catch (NumberFormatException exception) {
                NBLogger.e("Exception", exception.getMessage());
            }
            if (offerRotateAfter == 0) {// -1 means no rotation if 0 rotation based on login
                investInsureOffer = rotationLogicOnLoginBasis(listOffers);
            } else if (offerRotateAfter > 0) {
                investInsureOffer = rotationLogicOnDaysBasis(listOffers, offerRotateAfter);
            }
        }
        addInvestInsureOfferInList(investInsureOffer, accountsOverview);
    }

    @SuppressLint("WrongConstant")
    private void addInvestInsureOfferInList(PreApprovedOffersInnerDetailsViewModel investInsureOffer, AccountsOverview accountsOverview) {
        if (investInsureOffer != null) {
            double amount = getAmountForPreApprovedOffer(investInsureOffer);
            int cateId = getCategoryIdForPreApprovedOffer(investInsureOffer);
            mApplicationStorage.putInteger(za.co.nedbank.core.Constants.LAST_ADAM_OFFER_VISIBLE_ON_DASHBOARD, investInsureOffer.getPreApprovedOffersOfferTypeViewModel().getId());
            AccountSummary accountSummary = getPreApprovedOfferAccountSummary(investInsureOffer.getId(),
                    investInsureOffer.getPreApprovedOffersOfferTypeViewModel().getId(),
                    investInsureOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                    OverviewType.EVERYDAY_BANKING,
                    investInsureOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                    amount,
                    investInsureOffer.isDebiCheckMandateEnable(),
                    investInsureOffer.isCCAPOEnabled(),
                    investInsureOffer.getMainScreenHeader(),
                    investInsureOffer.getMainScreenCta(),
                    cateId);
            accountSummary.setShortMessage(investInsureOffer.getShortTextMessage());
            accountSummary.setSource(investInsureOffer.getSource());
            accountSummary.setUniqueIdInSourceSystem(investInsureOffer.getUniqueIdInSourceSystem());
            accountsOverview.accountSummaries.add(accountSummary);
        }
    }

    private PreApprovedOffersInnerDetailsViewModel rotationLogicOnDaysBasis(List<PreApprovedOffersInnerDetailsViewModel> listOffers, int offerRotateAfter) {
        long day = mApplicationStorage.getLong(za.co.nedbank.core.Constants.LAST_ADAM_OFFER_VISIBLE_ON_DASHBOARD_DAY, -1);
        int lastAdamOfferId = mApplicationStorage.getInteger(za.co.nedbank.core.Constants.LAST_ADAM_OFFER_VISIBLE_ON_DASHBOARD, -1);
        long currentDay = Calendar.getInstance().getTimeInMillis();
        PreApprovedOffersInnerDetailsViewModel investInsureOffer = getRequiredPreApprovedOffer(listOffers, za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INVEST.getOfferTypeId());
        if (day == -1 || lastAdamOfferId == -1) {
            mApplicationStorage.putLong(za.co.nedbank.core.Constants.LAST_ADAM_OFFER_VISIBLE_ON_DASHBOARD_DAY, currentDay);
        } else if (getCurrentDateAndPreviousDateDiff(currentDay, day) >= offerRotateAfter) {
            if (lastAdamOfferId == za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INSURE.getOfferTypeId()) {
                investInsureOffer = getRequiredPreApprovedOffer(listOffers, za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INVEST.getOfferTypeId());
            } else {
                investInsureOffer = getRequiredPreApprovedOffer(listOffers, za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INSURE.getOfferTypeId());
            }
            mApplicationStorage.putLong(za.co.nedbank.core.Constants.LAST_ADAM_OFFER_VISIBLE_ON_DASHBOARD_DAY, currentDay);
        } else {
            investInsureOffer = getRequiredPreApprovedOffer(listOffers, lastAdamOfferId);
        }
        return investInsureOffer;
    }

    private PreApprovedOffersInnerDetailsViewModel rotationLogicOnLoginBasis(List<PreApprovedOffersInnerDetailsViewModel> listOffers) {
        int lastAdamOfferId = mApplicationStorage.getInteger(za.co.nedbank.core.Constants.LAST_ADAM_OFFER_VISIBLE_ON_DASHBOARD, -1);
        PreApprovedOffersInnerDetailsViewModel investInsureOffer;
        if (view.isDashboardAdamOfferVisibleFirstTimeAfterLogin()) {
            if (lastAdamOfferId == za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INVEST.getOfferTypeId()) {
                investInsureOffer = getRequiredPreApprovedOffer(listOffers, za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INSURE.getOfferTypeId());
            } else {
                investInsureOffer = getRequiredPreApprovedOffer(listOffers, za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INVEST.getOfferTypeId());
            }
        } else {
            if (lastAdamOfferId == -1) {
                lastAdamOfferId = za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.INVEST.getOfferTypeId();
            }
            investInsureOffer = getRequiredPreApprovedOffer(listOffers, lastAdamOfferId);
        }
        view.setDashboardAdamOfferVisibleFirstTimeAfterLogin(false);
        return investInsureOffer;
    }

    private long getCurrentDateAndPreviousDateDiff(long currentDay, long oldDay) {
        long diffInMillis = Math.abs(currentDay - oldDay);
        return TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);
    }

    private PreApprovedOffersInnerDetailsViewModel getRequiredPreApprovedOffer(List<PreApprovedOffersInnerDetailsViewModel> listOffers, int requiredOfferType) {
        for (PreApprovedOffersInnerDetailsViewModel offer : listOffers) {
            if (offer.getPreApprovedOffersOfferTypeViewModel().getId() == requiredOfferType) {
                return offer;
            }
        }
        return null;
    }

    void navigateToChatActivity() {
        trackActionMyAccountProductGroup(AppTracking.MY_ACCOUNTS_CHAT);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CHAT_SCREEN));
    }
    public void handleUpdateApp(String message, String title) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.APP_SOFT_UPDATE_AVAILABLE);
        navigationTarget
                .withParam(NavigationTarget.IS_DEVICE_ROOTED_PARAM, false)
                .withParam(NavigationTarget.IS_FROM_APP_SHORTCUT, false)
                .withParam(NavigationTarget.IS_SECOND_LOGIN, true)
                .withParam(NavigationTarget.UPDATE_APP_DESC, message)
                .withParam(NavigationTarget.UPDATE_APP_TITLE, title);
        navigationRouter.navigateTo(navigationTarget);

        analytics.sendEvent(ChatTracking.AdobeEventName.EVENT_NAME_UPDATE_APP,
                StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    public void navigateToChatErrorActivity() {
        navigationRouter.navigateTo(
                NavigationTarget.to(NavigationTarget.ERROR_SCREEN_ACTIVITY));
    }

    void navigateToChatBotActivity(String conversationId) {

        trackActionMyAccountProductGroup(AppTracking.MY_ACCOUNTS_CHAT);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.CONVO_CHAT_SCREEN).withIntentSingleTop(true);
        navigationTarget.withParam(NavigationTarget.CONVERSATION_ID, conversationId);
        navigationRouter.navigateTo(navigationTarget);
    }

    void navigateToChatBotIntroductionActivity(String conversationId) {

        trackActionMyAccountProductGroup(AppTracking.MY_ACCOUNTS_CHAT);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.CHATBOT_INTRODUCTION_SCREEN).withIntentSingleTop(true);
        navigationTarget.withParam(NavigationTarget.CONVERSATION_ID, conversationId);
        navigationRouter.navigateTo(navigationTarget);

    }

    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    public void onUnreadChatEvent(UnreadChatEvent unreadChatEvent) {
        if (view != null) {
            view.onUnreadChatEvent(unreadChatEvent);
        }
    }

    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    public void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent) {
        if (view != null) {
            view.onUnreadLifestyleChatEvent(unreadChatEvent);
        }
    }

    public void showProductCount(boolean showCategoryDashboard, AccountSummary accountSummary) {
        if (showCategoryDashboard) {
            getProductsCount(accountSummary);
        }
    }

    public void showAddProductCategories(boolean showCategoryDashboard, AccountsOverview accountsOverview) {
        if (showCategoryDashboard) {
            addProductCategories(accountsOverview);
        }
    }


    private void handleOverviewData(Overview overviewValue) {
        if (view != null) {

            handleRewardOverview(overviewValue);
            travelCardSetup(overviewValue);
            // Carousal page - Foreign currency and International banking
            getBusinessOverview(overviewValue);
            greenbackAccountSetup(overviewValue);
            getMerchantOverView(overviewValue);
            for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
                if (accountsOverview.onlineSavingsAccounts == null) {
                    continue;
                }
                pocketsCount += accountsOverview.onlineSavingsAccounts.getPocketsCounter();
            }


            setupOverviewCategory(overviewValue);
            checkToggleEnables(overviewValue);
            setupNotificationPreferences(overviewValue);

            view.showOverviewLoading(false);
            view.showOverview(overviewValue);
            view.hasTransactableAccount(checkForAnyTransactableAccount(overviewValue));
        }
    }

    private void getBusinessOverview(Overview overviewValue) {
        if (isBusinessUser() ? !featureSetController.isFeatureDisabled(FeatureConstants.FTR_FOREXSBS)
                : !featureSetController.isFeatureDisabled(FeatureConstants.INTERNATIONAL_PAYMENTS_TOGGLE)
                && !mMemoryApplicationStorage.getBoolean(TRAVEL_CARD_MINOR, false)) {
            getInternationalBankingAndTravelOverview(overviewValue);
        }
    }

    public void getMerchantOverView(Overview overviewValue) {
        if (view != null && overviewValue != null && overviewValue.accountsOverviews != null && isMerchantUser() && !view.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_MERCHANT_SERVICES)) {
            overviewValue.accountsOverviews.add(getMerchantOverviewData());
        }

    }

    private AccountsOverview getMerchantOverviewData() {
        List<AccountSummary> accountSummary = new ArrayList<>();
        AccountsOverview accountsOverview = new AccountsOverview();
        accountSummary.add(getMerchantAccountSummary());
        accountsOverview.accountSummaries = accountSummary;
        accountsOverview.overviewType = OverviewType.MERCHANT_SERVICES;
        accountsOverview.summaryValue1 = new BigDecimal(0);
        accountsOverview.summaryValue2 = new BigDecimal(0);
        return accountsOverview;
    }

    private AccountSummary getMerchantAccountSummary() {
        AccountSummary accountSummary = new AccountSummary();
        accountSummary.setName(MERCHANT_SERVICES);
        accountSummary.setAccountHolderName(StringUtils.EMPTY_STRING);
        accountSummary.setAccountType(OverviewType.MERCHANT_SERVICES);
        accountSummary.setCurrency(StringUtils.EMPTY_STRING);
        accountSummary.setSummaryValue(new BigDecimal(0));
        accountSummary.setNumber(StringUtils.HYPHEN);
        accountSummary.setAccountShown(false);
        return accountSummary;
    }


    private void handleRewardOverview(Overview overviewValue) {
        boolean isKidsBankingProfile = kidsProfileUtilsWrapper.isUserUnderAged(); // for kids banking need to remove - REWARDS carousel page
        if (isKidsBankingProfile) { //
            return;
        }
        if (view.isFeatureDisabled(FeatureConstants.REWARDS)) {
            List<AccountsOverview> overviewsWithoutRewards = new ArrayList<>();
            for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
                if (accountsOverview.overviewType != OverviewType.REWARDS) {
                    overviewsWithoutRewards.add(accountsOverview);
                }
            }
            overviewValue.accountsOverviews = overviewsWithoutRewards;
        }
    }

    private void travelCardSetup(Overview overviewValue) {
        if (view.isFeatureDisabled(FeatureConstants.TRAVEL_CARD)) {
            List<AccountsOverview> overviewsWithoutTravelCard = filterTravelCard(overviewValue);
            overviewValue.accountsOverviews = overviewsWithoutTravelCard;
        }
    }

    private List<AccountsOverview> filterTravelCard(Overview overviewValue) {
        List<AccountsOverview> overviewsWithoutTravelCard = new ArrayList<>();
        for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
            if (accountsOverview.overviewType == OverviewType.FOREIGN_CURRENCY_ACCOUNT) {
                Iterator<AccountSummary> accountSummaryIterator = accountsOverview.accountSummaries.iterator();
                while (accountSummaryIterator.hasNext()) {
                    AccountSummary summery = accountSummaryIterator.next();
                    if (summeryHasTravelCardAccount(summery)) {
                        accountSummaryIterator.remove();
                    }
                }
                if (isAccountSummeryEmpty(accountsOverview)) {
                    continue;
                }
            }
            overviewsWithoutTravelCard.add(accountsOverview);
        }
        return overviewsWithoutTravelCard;
    }

    private boolean summeryHasTravelCardAccount(AccountSummary summery) {
        return summery.isTravelCardAccount();
    }

    private boolean isAccountSummeryEmpty(AccountsOverview accountsOverview) {
        return accountsOverview.accountSummaries.isEmpty();
    }

    private boolean validateGreenback() {
        String bDate = view.getBirthDate();
        boolean isNotMinor = bDate == null || (AppUtility.calculateAge(bDate) >= za.co.nedbank.services.Constants.GREENBACKS_ENROLMENT_AGE_LIMIT);
        boolean isGreenbacksRewardAccountHidden = mApplicationStorage.getBoolean(StorageKeys.IS_GB_REWARD_ACCOUNT_HIDDEN, false);
        return !view.isFeatureDisabled(FeatureConstants.REWARDS) && !view.isFeatureDisabled(FeatureConstants.REWARDS_ENROLMENT) && !isGreenbacksRewardAccountHidden && isNotMinor;
    }

    private void greenbackAccountSetup(Overview overviewValue) {
        //Perform auto linking for Greenback rewards account
        performAutoLinkingForGreenBackAccount(overviewValue);
        if (validateGreenback()) {
            filterGreenbackAccounts(overviewValue);
        }
    }

    private void filterGreenbackAccounts(Overview overviewValue) {
        boolean hasGreenbacksAccount = false;
        boolean hasRewardsContainer = false;
        for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
            if (isOverViewRewardType(accountsOverview)) {
                hasRewardsContainer = true;
                for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
                    if (testGreenbackCurrency(accountSummary)) {
                        hasGreenbacksAccount = true;
                        mApplicationStorage.putBoolean(StorageKeys.IS_GB_REWARDS_ENROLMENT_IN_PROGRESS, false);
                        break;
                    }
                }
                if (!hasGreenbacksAccount) {
                    accountsOverview.accountSummaries.add(getAccountSummary());
                }
                break;
            }
        }
        boolean isKidsBankingProfile = kidsProfileUtilsWrapper.isUserUnderAged(); // for kids banking need to remove - REWARDS carousel page
        if (!isKidsBankingProfile && !hasRewardsContainer) {
            overviewValue.accountsOverviews.add(getAccountOverView());
        }
    }

    private boolean isOverViewRewardType(AccountsOverview accountsOverview) {
        return accountsOverview.overviewType == OverviewType.REWARDS;
    }

    private boolean testGreenbackCurrency(AccountSummary accountSummary) {
        return FormattingUtil.GREENBACKS_CURRENCY.equalsIgnoreCase(accountSummary.getRewardsProgram());
    }

    private void performAutoLinkingForGreenBackAccount(Overview overviewValue) {
        for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
            if (accountsOverview.overviewType == OverviewType.REWARDS) {
                for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
                    if (FormattingUtil.GREENBACKS_CURRENCY.equalsIgnoreCase(accountSummary.getRewardsProgram())) {
                        //Check if the account is not linked already
                        if (!accountSummary.isProfileAccount()) {
                            performAccountLinkingCheck(accountSummary);
                            break;
                        }
                    }
                }
                break;
            }
        }
    }

    private void setupOverviewCategory(Overview overviewValue) {
        unitTrustAccountCount = 0;
        investmentAccountCount = 0;
        boolean hasInvestmentContainer = false;
        boolean showCategoryDashboard = !featureSetController.isFeatureDisabled(FeatureConstants.INVESTMENT_ONLINE_TP_DASHBOARD_CATEGORY);
        for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
            if (accountsOverview.overviewType == OverviewType.INVESTMENTS) {
                hasInvestmentContainer = true;
                filterInstitutionNameAndAccount(accountsOverview, showCategoryDashboard);

                showAddProductCategories(showCategoryDashboard, accountsOverview);
                //Adds pending account, a "Open Account" and a "Invetment Account Tax Certificates" container - InvestmentOnline
                addPendingInvestmentAccounts(accountsOverview.accountSummaries);

                try {
                    invetmentAccountsOverview = accountsOverview.clone();
                } catch (CloneNotSupportedException e) {
                    NBLogger.e("Exception:", "OverviewPresenter", e);
                }
                if (!featureSetController.isFeatureDisabled(FeatureConstants.INVESTMENT_ACCOUNT_TAX_CERTIFICATE_FEATURE))
                    accountsOverview.accountSummaries.add(getInvestmentAccountSummary(za.co.nedbank.services.
                            Constants.INVESTMENT_TAX_CERTIFICATE, false, StringUtils.EMPTY_STRING));
                accountsOverview.accountSummaries.add(getInvestmentAccountSummary(za.co.nedbank.services.
                        Constants.OVERVIEW_INVESTMENT_ACCOUNT, false, StringUtils.EMPTY_STRING));
            }
        }

        boolean isKidsBankingProfile = kidsProfileUtilsWrapper.isUserUnderAged(); // for kids banking need to remove - INVESTMENTS carousel page
        if (!isKidsBankingProfile) {
            //Only executes when the hasInvestmentContainer is false and will be adding a "Open Account" container - InvestmentOnline
            showInvestmentContainer(overviewValue, hasInvestmentContainer);
        }

    }

    private void filterInstitutionNameAndAccount(AccountsOverview accountsOverview, boolean showCategoryDashboard) {
        for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
            if (getInstitutionNameAndActValidation(accountSummary)) {
                unitTrustAccountCount++;
            } else if (isValidInvestmentAccountForGbRedeeem(accountSummary)) {
                investmentAccountCount++;
            }
            showProductCount(showCategoryDashboard, accountSummary);
        }
    }

    private boolean getInstitutionNameAndActValidation(AccountSummary accountSummary) {
        return !StringUtils.isNullOrEmpty(accountSummary.getInstitutionName())
                && accountSummary.getInstitutionName().equals(za.co.nedbank.services.Constants.UNIT_TRUST_TYPE) && accountSummary.getNumber().length() == za.co.nedbank.core.Constants.GREENBACKS_VALID_UNIT_TRUST_ACCOUNT_NUMBER_LENGTH;
    }

    private void showInvestmentContainer(Overview overviewValue, boolean hasInvestmentContainer) {
        if (!featureSetController.isFeatureDisabled(FeatureConstants.INVESTMENT_ONLINE_OPEN_NEW_ACCOUNT)
                && !hasInvestmentContainer) {
            List<AccountSummary> accountsOverviews = new ArrayList<>();
            addPendingInvestmentAccounts(accountsOverviews);
            if (accountsOverviews.size() > za.co.nedbank.core.Constants.ZERO) {
                overviewValue.accountsOverviews.add(getInvestmentAccountOverView(true, accountsOverviews));
            } else {
                overviewValue.accountsOverviews.add(getInvestmentAccountOverView(false, accountsOverviews));
            }

        }
    }

    private void checkToggleEnables(Overview overviewValue) {
        //Adding a new row for OnlineSavings Pocket...
        if (!featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ONLINE_SAVINGS)
                && !mIsBusinessUser) {
            addSavingPocketsForCurrentAccount(overviewValue);
        }
        // Adding a new carousal page - Insurance
        boolean isKidsBankingProfile = kidsProfileUtilsWrapper.isUserUnderAged(); // for kids banking need to remove - Insurance carousel page
        if (!isKidsBankingProfile && !featureSetController.isFeatureDisabled(FeatureConstants.INSURANCE_DASHBOARD)) {
            addInsurance(overviewValue);
        }

        // Adding a new carousal page - Financial Wellness
        if (!featureSetController.isFeatureDisabled(FeatureConstants.FTR_FINANCIAL_WELLNESS)) {
            addFinancialWellness(overviewValue);
        }

        if (!isBusinessUser() && !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_FAMILY_BANKING)) {
            addFamilyBankingAndLifestyleOverview(overviewValue);
        }
    }

    private void setupNotificationPreferences(Overview overviewValue) {
        if (!view.isFeatureDisabled(FeatureConstants.DynamicToggle.NOTIFICATIONS_PREFERENCES)) {
            List<AccountPreference> accountPreferences = new ArrayList<>();
            for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
                for (AccountSummary summary : accountsOverview.accountSummaries) {
                    if (checkNotificationAccountPreference(summary)) {
                        AccountPreference preference = new AccountPreference();
                        preference.setAccountNumber(summary.getNumber());
                        accountPreferences.add(preference);
                    }

                }
            }
            Gson gson = new Gson();
            String jsonPreferences = gson.toJson(accountPreferences);
            mMemoryApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.PREF_CASA_LIST, jsonPreferences);
        }
    }

    private boolean checkNotificationAccountPreference(AccountSummary summary) {
        return (summary.getAccountCode() != null) && ((summary.getAccountCode().equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.name()) || summary.getAccountCode().equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.SA.name())) && !summary.isAlternateAccount());
    }

    public void getProductsCount(AccountSummary accountSummary) {
        String category = getCategoryType(accountSummary);
        if (category != null) {
            switch (category) {
                case ServicesNavigationTarget.InvestmentConstant.TERM_DEPOSITS: {
                    termDepositsCount++;
                    break;
                }
                case ServicesNavigationTarget.InvestmentConstant.NOTICE_PRODUCT: {
                    noticeDepositsCount++;
                    break;
                }
                case ServicesNavigationTarget.InvestmentConstant.UNIT_TRUST: {
                    unitTrustCount++;
                    break;
                }
                case ServicesNavigationTarget.InvestmentConstant.RETIREMENT_ANNUITY: {
                    retirementAnnuityCount++;
                    break;
                }
                case ServicesNavigationTarget.InvestmentConstant.TAX_FREE_INVESTMENTS: {
                    taxFreeInvestmentsCount++;
                    break;
                }
                case ServicesNavigationTarget.InvestmentConstant.TAX_FREE_UNIT_TRUST: {
                    taxFreeUnitTrustCount++;
                    break;
                }

                case ServicesNavigationTarget.InvestmentConstant.LIVING_ANNUITY: {
                    livingAnnuityCount++;
                    break;
                }

                case ServicesNavigationTarget.InvestmentConstant.PENSION_PRESERVATION: {
                    pensionPreservationCount++;
                    break;
                }

                case ServicesNavigationTarget.InvestmentConstant.PROVIDENT_PRESERVATION: {
                    providentPreservationCount++;
                    break;
                }

                case ServicesNavigationTarget.InvestmentConstant.ENDOWMENT_PLAN: {
                    endowmentPlanCount++;
                    break;
                }

                case ServicesNavigationTarget.InvestmentConstant.DEFAULT: {
                    mOthersCount++;
                    break;
                }

                default:
                    break;

            }
        }
    }

    public void checkCountAndUpdate(AccountsOverview accountsOverview, String categoryName, int catCount) {
        if (catCount > 0) {
            double amount = 0;
            List<AccountSummary> accountSummaryList = new ArrayList<>();
            for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
                if (!Objects.equals(getCategoryType(accountSummary), categoryName))
                    continue;
                amount += accountSummary.getMarketValue();
                accountSummaryList.add(accountSummary);
            }
            accountsOverview.accountSummaries.add(getInvestmentAccountSummary(categoryName, amount, accountSummaryList));
            newCategoryCount++;
        }
    }

    private void addProductCategories(AccountsOverview accountsOverview) {

        LinkedHashMap<String, Integer> catNameCountMap = new LinkedHashMap<>();
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.TERM_DEPOSITS, termDepositsCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.NOTICE_PRODUCT, noticeDepositsCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.UNIT_TRUST, unitTrustCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.RETIREMENT_ANNUITY, retirementAnnuityCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.TAX_FREE_INVESTMENTS, taxFreeInvestmentsCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.TAX_FREE_UNIT_TRUST, taxFreeUnitTrustCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.LIVING_ANNUITY, livingAnnuityCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.PENSION_PRESERVATION, pensionPreservationCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.PROVIDENT_PRESERVATION, providentPreservationCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.ENDOWMENT_PLAN, endowmentPlanCount);
        catNameCountMap.put(ServicesNavigationTarget.InvestmentConstant.DEFAULT, mOthersCount);


        for (Map.Entry<String, Integer> e : catNameCountMap.entrySet())
            checkCountAndUpdate(accountsOverview, e.getKey(), e.getValue());

        accountsOverview.accountSummaries.subList(0, accountsOverview.accountSummaries.size() - newCategoryCount).clear();
        newCategoryCount = 0;
    }

    private boolean isValidInvestmentAccountForGbRedeeem(AccountSummary accountSummary) {
        String productType = accountSummary.getProductType();
        return JUST_INVEST.equals(productType) || PLATINUM_INVEST.equals(productType) || MONEY_TRADER.equals(productType) || NOTICE_DESPOSIT_32.equals(productType) || NEDBANK_ELECTRONIC_32_DAY_NOTICE.equals(productType);
    }

    private String getFixedNoticeTFI(AccountSummary accountSummary) {
        if (accountSummary.getTfsIndicator() != null && accountSummary.getTfsIndicator().equals(ServicesNavigationTarget.InvestmentConstant.CODE_Y)) {
            return ServicesNavigationTarget.InvestmentConstant.TAX_FREE_INVESTMENTS;
        } else if (!StringUtils.isNullOrEmpty(accountSummary.getTfsIndicator())) {
            boolean isFixed = false;
            if (accountSummary.getNoticeProduct().equals(ServicesNavigationTarget.InvestmentConstant.CODE_N) && accountSummary.getTfsIndicator().equalsIgnoreCase(CODE_N)) {
                isFixed = true;
            }

            return isFixed ? ServicesNavigationTarget.InvestmentConstant.TERM_DEPOSITS : ServicesNavigationTarget.InvestmentConstant.NOTICE_PRODUCT;
        } else {
            return ServicesNavigationTarget.InvestmentConstant.DEFAULT;
        }
    }

    //ngi
    private String getNGIProduct(AccountSummary accountSummary) {
        String category = null;
        switch (accountSummary.getInvestmentProductId()) {
            case NGIConstants.NGI_CONSTANT_UT: {
                accountSummary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_UT);
                category = ServicesNavigationTarget.InvestmentConstant.UNIT_TRUST;
                break;
            }
            case NGIConstants.NGI_CONSTANT_RA: {
                accountSummary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_RA);
                category = ServicesNavigationTarget.InvestmentConstant.RETIREMENT_ANNUITY;
                break;
            }
            case NGIConstants.NGI_CONSTANT_TFI: {
                accountSummary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_TFI);
                category = ServicesNavigationTarget.InvestmentConstant.TAX_FREE_UNIT_TRUST;
                break;
            }

            case NGIConstants.NGI_CONSTANT_LA: {
                accountSummary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_LA);
                category = ServicesNavigationTarget.InvestmentConstant.LIVING_ANNUITY;
                break;
            }

            case NGIConstants.NGI_CONSTANT_PEN: {
                accountSummary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_PEN);
                category = ServicesNavigationTarget.InvestmentConstant.PENSION_PRESERVATION;
                break;
            }

            case NGIConstants.NGI_CONSTANT_PROV: {
                accountSummary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_PROV);
                category = ServicesNavigationTarget.InvestmentConstant.PROVIDENT_PRESERVATION;
                break;
            }

            case NGIConstants.NGI_CONSTANT_END: {
                accountSummary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_END);
                category = ServicesNavigationTarget.InvestmentConstant.ENDOWMENT_PLAN;
                break;
            }
            default:
                accountSummary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_DEFAULT);
                category = ServicesNavigationTarget.InvestmentConstant.DEFAULT;
                break;
        }
        return category;
    }

    private String getCategoryType(AccountSummary accountSummary) {
        String category = null;
        if (accountSummary.getAccountCode() != null) {
            if (accountSummary.getAccountCode().equals(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_DS)) {
                category = getFixedNoticeTFI(accountSummary);
            } else if (accountSummary.getAccountCode().equals(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV) && accountSummary.getInvestmentProductId() != null) {
                category = getNGIProduct(accountSummary);
            } else {
                category = ServicesNavigationTarget.InvestmentConstant.DEFAULT;
            }
        }
        return category;
    }

    private void addPendingInvestmentAccounts(List<AccountSummary> accountSummaries) {
        if (pendingAccountListDataModel != null) {
            for (PendingAccountDataModel pendingAccountDataModel : pendingAccountListDataModel.getPendingAccountDataModels()) {
                accountSummaries.add(getInvestmentAccountSummary(String.format("%s%s%s", pendingAccountDataModel.getInvestorNumber(),
                        StringUtils.HYPHEN, pendingAccountDataModel.getInvestmentNumber()), true, pendingAccountDataModel.getInvestmentAccountName()));
            }
        }
    }

    void performAccountLinkingCheck(AccountSummary accountsOverviews) {
        getLinkableAccountsUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(linkableAccountDtos -> {
                    if (linkableAccountDtos != null) {
                        for (LinkableAccountDto linkableAccountDto : linkableAccountDtos) {
                            if (linkableAccountDto.acccountNumber.equals(accountsOverviews.getNumber())) {
                                handleAutomaticLinking(linkableAccountDto);
                                break;
                            }
                        }
                    }
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    private void handleAutomaticLinking(LinkableAccountDto linkableAccountDto) {
        if (view == null) {
            return;
        }
        setLinkableAccountsUseCase.execute(view.getLinkableAccountList(linkableAccountDto))
                .compose(bindToLifecycle())
                .subscribe(result -> {
                    //If result is TRUE or FALSE no need to do anything.
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    public ClientType getClientType() {
        String clientTypeAtm = mApplicationStorage.getString(CLIENT_TYPE, ClientType.TP.getValue());
        return ClientType.getEnum(clientTypeAtm);
    }

    public void getTotalBankingUnreadMessages() {
        int totalUnreadMessageCount = mApplicationStorage.getInteger(StorageKeys.BANKING_TOTAL_UNREAD_MESSAGE_COUNT, za.co.nedbank.core.Constants.ZERO);
        view.setChatIcon(totalUnreadMessageCount);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNotificationReceived(NotificationEvent notificationEvent) {
        view.receivePreApprovedOffersCount(preApprovedOffersCount + notificationEvent.getUnreadCount());
    }

    @SuppressLint("CheckResult")
    private void extractPreApprovedOfferForPersistentDisplay(List<PreApprovedOffersInnerDetailsViewModel> preApprovedOffersInnerDetailsViewModelList, String... offerTypeArgs) {
        Observable<PreApprovedOffersInnerDetailsViewModel> preApprovedOfferInnerViewModelObservable = obtainObservableForPreApprovedOfferForPersistentDisplay(preApprovedOffersInnerDetailsViewModelList, offerTypeArgs);
        preApprovedOfferInnerViewModelObservable
                .compose(bindToLifecycle())
                .subscribe(preApprovedOffersInnerDetailsViewModel -> {
                    if (preApprovedOffersInnerDetailsViewModel != null) {
                        if (offerTypeArgs != null && offerTypeArgs.length > 0) {
                            for (String offerType : offerTypeArgs) {
                                switch (offerType) {
                                    case za.co.nedbank.core.Constants.PreApprovedOffersTypes.PERSONAL_LOAN_OFFER:
                                    case za.co.nedbank.core.Constants.PreApprovedOffersTypes.CONSOLIDATED_LOAN_OFFER:
                                        mLoanOffer = preApprovedOffersInnerDetailsViewModel;
                                        break;
                                    case za.co.nedbank.core.Constants.PreApprovedOffersTypes.NEW_CREDIT_CARD_OFFER:
                                    case za.co.nedbank.core.Constants.PreApprovedOffersTypes.CARD_LIMIT_INCREASE_OFFER:
                                        mCreditCardOffer = preApprovedOffersInnerDetailsViewModel;
                                        break;
                                }
                            }
                        }

                    }
                }, throwable -> {
                    NBLogger.e(TAG, throwable.getMessage());
                });
    }

    @SuppressLint("CheckResult")
    private Observable obtainObservableForPreApprovedOfferForPersistentDisplay(List<PreApprovedOffersInnerDetailsViewModel> preApprovedOffersInnerDetailsViewModelList, String... offerTypeArgs) {
        Observable<PreApprovedOffersInnerDetailsViewModel> preApprovedOfferInnerViewModelObservable = Observable.fromIterable(preApprovedOffersInnerDetailsViewModelList)
                .filter(preApprovedOffersInnerDetailsViewModel -> {
                    boolean isOfferTypeMatch = false;
                    if (preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel() != null && !StringUtils.isNullOrEmpty(preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel().getCode())
                            && offerTypeArgs != null && offerTypeArgs.length > 0) {
                        for (String offerType : offerTypeArgs) {
                            if (preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel().getCode().equalsIgnoreCase(offerType)) {
                                isOfferTypeMatch = true;
                            }
                        }
                    }
                    return isOfferTypeMatch;
                }).takeLast(1);
        return preApprovedOfferInnerViewModelObservable;
    }

    protected void startPaymentTime(long timestamp) {
        mMemoryApplicationStorage.putLong(StorageKeys.PAYMENT_FLOW_START_TIME, timestamp);
    }


    public void navigateToRespectiveScreen(Bundle bundle) {
        if (bundle.getInt(SCREEN_TYPE) == QUICK_PAY) {
            openQuickPay("Quick Pay");
        } else if (bundle.getInt(SCREEN_TYPE) == PAY_ME) {
            openPayMe();
        } else {
            openGetCashWidget();
        }
    }

    public void checkFica(Bundle bundle) {
        if (isFicaEnabled()) {
            callClientsApi(bundle);
        } else {
            navigateToRespectiveScreen(bundle);
        }
    }

    public void openPayMe() {
        trackActionForPayMe();
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.PAY_ME));
    }

    void openGetCashWidget() {
        trackWidgetAction(AtmGetCashTrackingEvent.GET_CASH_WIDGET, TrackingParam.VAL_WIDGET_DASHBOARD, TrackingParam.VAL_GET_CASH, TrackingParam.VAL_TRANSACTIONAL_INSTRUCTION, TrackingParam.VAL_WIDGET_GET_CASH);

        NavigationTarget navigationTarget = NavigationTarget.to(AtmNavigatorTarget.ATM_TUTORIAL_SCREEN);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void callClientsApi(Bundle feature) {
        getUserDetailUseCase.execute(false).compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    UserDetailViewModel userDetailVM = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail);
                    boolean isFicaVerified = userDetailVM.isFicaVerified();
                    boolean hasIdOrTaxIdNumber = !StringUtils.isNullOrEmpty(userDetailVM.getIdOrTaxIdNumber());
                    boolean isRetailUser = (!StringUtils.isNullOrEmpty(userDetailVM.getClientType()) && Integer.parseInt(userDetailVM.getClientType()) <= 30);
                    boolean oddRestricted = userDetailVM.getOddRestricted();

                    if (!isFicaVerified) {
                        checkFicaConditions(feature, hasIdOrTaxIdNumber, isRetailUser);
                    } else if (oddRestricted && isOddRestrictionEnabled()) {
                        if (!userDetailVM.isOddRequired && !isRetailUser) {
                            navigateToOddError(OddRestrictionErrorType.JURISTIC_ODD_VERIFYING);
                        } else {
                            checkODDFLow(userDetailVM, isRetailUser);
                        }
                    } else if (FatcaRestrictionUtil.isNewFatcaRestrictionApplicable(mMemoryApplicationStorage, featureSetController, userDetail)) {
                        checkFatcaUnrestriction(isRetailUser);
                    } else {
                        navigateToRespectiveScreen(feature);
                    }

                }, this::handleException);

    }

    public void checkFatcaUnrestriction(boolean isRetailUser) {
        if (!isRetailUser) {
            navigateToFatcaUnRestrictions(FatcaUnrestrictionType.JURISTIC, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        } else {
            fatcaMissingInfoUseCase.execute().compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                    })
                    .doOnTerminate(() -> {
                    })
                    .subscribe(data -> {
                                if (data.getData() != null) {
                                    if (data.getData().getRequiredData() != null && !data.getData().getRequiredData().isEmpty() && data.getData().getRequiredDocuments() != null && !data.getData().getRequiredDocuments().isEmpty()) {
                                        navigateToFatcaUnRestrictions(FatcaUnrestrictionType.MISSING_INFO_FORM, data.getData().getDocumentLocation(), data.getData().getFormStr());
                                    } else if (data.getData().getRequiredData() != null && !data.getData().getRequiredData().isEmpty()) {
                                        navigateToFatcaUnRestrictions(FatcaUnrestrictionType.MISSING_INFO, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
                                    } else if (data.getData().getRequiredDocuments() != null && !data.getData().getRequiredDocuments().isEmpty()) {
                                        navigateToFatcaUnRestrictions(FatcaUnrestrictionType.MISSING_FORM, data.getData().getDocumentLocation(), data.getData().getFormStr());
                                    } else {
                                        callRefreshApi();
                                    }
                                }
                            }
                            , e -> view.showError("Apologies! Looks like something's wrong on our side."));
        }
    }

    private void callRefreshApi() {
        refreshCleanCacheUserUseCase.execute(true).compose(bindToLifecycle()).subscribe(res -> getUserDetailUseCase.execute(true).compose(bindToLifecycle())
                .subscribe(userDetail -> navigateToFatcaUnRestrictions(FatcaUnrestrictionType.VERIFYING, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING), this::handleException));

    }

    private void navigateToFatcaUnRestrictions(@FatcaUnrestrictionType int type, String url, String forms) {
        navigationRouter.navigateWithResult(NavigationTarget.to(ProfileNavigationTarget.FATCA_UNRESTRICTION_ONBOARDING_SCREEN)
                .withParam(SCREEN_TYPE, type)
                .withParam("forms", forms)
                .withParam("url", url)
        ).subscribe(
                navigationResult -> {
                    if (navigationResult != null && navigationResult.isOk() &&
                            navigationResult.getParams().containsKey(FatcaUnrestrictionOnboardingActivity.ACTION) && navigationResult.getStringParam(FatcaUnrestrictionOnboardingActivity.ACTION).equals("1")) {
                        openBranchScreen();
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));

    }

    boolean isFCAToggleOn() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.FTR_INTERNATIONALFCA);
    }

    boolean isOddRestrictionEnabled() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DIGITAL_ENABLE_ODD_RESTRICTION);
    }

    private void checkODDFLow(UserDetailViewModel userDetailVM, boolean isRetailUser) {
        if (isRetailUser) {
            if (userDetailVM.getOddRequired()) {
                callOddRulesApi();
            } else {
                navigateToOddError(OddRestrictionErrorType.ODD_VERIFYING);
            }
        } else {
            navigateToOddError(OddRestrictionErrorType.JURISTIC);
        }
    }

    public void navigateToOddError(OddRestrictionErrorType type) {
        navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.ODD_RESTRICTION_ERROR)
                .withParam(za.co.nedbank.core.Constants.TYPE, type)
                .withAllData(Boolean.TRUE)).subscribe(
                navigationResult -> {
                    if (navigationResult != null && navigationResult.isOk() &&
                            navigationResult.getParams().containsKey(OddRestrictionErrorActivity.ACTION_KEY) && navigationResult.getStringParam(OddRestrictionErrorActivity.ACTION_KEY).equals("1")) {
                        openBranchScreen();
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void callOddRulesApi() {
        getOddReviewableReasonUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showWidgetProgress(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showWidgetProgress(false);
                    }
                })
                .subscribe(data -> {
                            OddReviewableViewModel oddReviewableData = oddMapper.mapData(data).getViewModel();
                            if (data.getOddReviewableClientDataResponse().isSelfServiceIsReviewable()) {
                                getMdmProfileDetails(oddReviewableData);
                            } else {
                                boolean is1015 = data.getOddReviewableClientDataResponse().getUnReviewableReason().equals("1015");
                                navigateToOddError(is1015 ? OddRestrictionErrorType.REASON_1015 : OddRestrictionErrorType.REASON_OTHER);
                            }
                        }
                        , this::handleException);

    }


    void getMdmProfileDetails(OddReviewableViewModel oddReviewableData) {
        getMdmProfileUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showWidgetProgress(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showWidgetProgress(false);
                    }
                })
                .subscribe(profile -> {
                    if (profile != null) {
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CONFIRM_ODD_DETAILS)
                                .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.USER_PROFILE, profile)
                                .withParam(za.co.nedbank.core.Constants.RSA_ID_OR_PASSPORT, profile.getRsaId())
                                .withParam(za.co.nedbank.core.Constants.IS_FROM_ODD_RESTRICTION, true)
                                .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.ODD_RULES_DATA, oddReviewableData)
                                .withAllData(Boolean.TRUE));
                    }

                }, this::handleException);

    }


    private void checkFicaConditions(Bundle feature, boolean hasIdOrTaxIdNumber, boolean isRetailUser) {
        if (hasIdOrTaxIdNumber && isRetailUser) {
            navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_VERIFY_ME)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk()) {
                            callFica(feature);
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        } else {
            navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_ERROR)
                    .withParam(IS_FROM_HOME, true)
                    .withParam(IS_RETAIL, isRetailUser)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk() &&
                                navigationResult.getParams().containsKey(FicaErrorActivity.ACTION) && navigationResult.getStringParam(FicaErrorActivity.ACTION).equals("1")) {
                            openBranchScreen();
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
    }

    private void callFica(Bundle feature) {
        mFicaSDKUseCase.execute(FICA).compose(bindToLifecycle())
                .subscribe(ficaSDKViewModel ->
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(DATA, ficaSDKViewModel)
                                .withParam(FEATURE, feature.getInt(SCREEN_TYPE)).withIntentFlagClearTopSingleTop(true)), this::handleException);
    }

    public void handleFicaResponse(int feature, FicaSDKViewModel ficaSDKViewModel) {
        if (ficaSDKViewModel.isFicaed()) {
            navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_SUCCESS)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk() &&
                                navigationResult.getParams().containsKey(FicaSuccessActivity.ACTION) && navigationResult.getStringParam(FicaSuccessActivity.ACTION).equals("1")) {
                            Bundle b = new Bundle();
                            b.putInt(SCREEN_TYPE, feature);
                            callClientsApi(b);
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        } else {
            navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_ERROR)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk() &&
                                navigationResult.getParams().containsKey(FicaErrorActivity.ACTION) && navigationResult.getStringParam(FicaErrorActivity.ACTION).equals("1")) {
                            openBranchScreen();
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
    }

    private void handleException(Throwable throwable) {
        Error error = errorHandler.getErrorMessage(throwable);
        if (view != null) {
            view.showError(error.getMessage());
        }
        NBLogger.e(TAG, Log.getStackTraceString(throwable));
    }

    public void openBranchScreen() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ATM_AND_BRANCHES_PROFILE).
                withParam(NavigationTarget.PARAM_IS_ATM_VISIBLE, true));
    }

    public void openQuickPay(String feature) {
        long timestamp = System.currentTimeMillis() / 1000;
        startPaymentTime(timestamp);

        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_MONETARY_TRANSACTIONS);
        adobeContextData.setFeature(feature);
        adobeContextData.setWidgetName(TrackingParam.VAL_WIDGET_QUICK_PAY);
        adobeContextData.setInitiations();
        adobeContextData.setStep1();
        adobeContextData.setStepName(PaymentsTrackingEvent.VAL_STEP_NAME_QUICK_PAY_INITIATION);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_QUICK_PAY, cdata);

        mAfAnalyticsTracker.sendEvent(AppsFlyerTags.AF_PAYMENT_INITIATION, null);

        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.QUICK_PAY));
    }


    public void openCovid19() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        adobeContextData.setWidgetName(TrackingParam.VAL_WIDGET_LATEST);
        adobeContextData.setInitiations();
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_WIDGET_LATEST, cdata);

        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.COVID_19));
    }

    public void openReportFraud() {
        HashMap<String, Object> cData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.resetDimensions();
        adobeContextData.setFeatureCategory(TrackingParam.VAL_CX_SERVICES);
        adobeContextData.setWidgetName(TrackingParam.VAL_WIDGET_REPORT_FRAUD);
        adobeContextData.setInitiations();
        cData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, TrackingParam.VAL_REPORT_FRAUD);
        cData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_WIDGET_DASHBOARD);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_REPORT_FRAUD, cData);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CONTACT_US_ITEM)
                .withParam(NavigationTarget.PARAM_CONTACT_US_FEATURE, za.co.nedbank.services.Constants.CONTACT_US.REPORT_FRAUD));
    }

    public void openBuyPrepaid() {
        if (view != null) {
            view.sendAnalytics(PaymentsTracking.BUY_PREPAID_WIDGET_ACTION, TrackingParam.VAL_WIDGET_DASHBOARD);
        }
        navigationRouter.navigateTo(NavigationTarget.to(BuyNavigatorTarget.BUY));
    }

    public void openBuyElectricity() {
        if (view != null) {
            view.sendAnalytics(PaymentsTracking.BUY_ELECTRICITY_WIDGET_ACTION, TrackingParam.VAL_WIDGET_DASHBOARD);
        }
        NavigationTarget navigationTarget = NavigationTarget.to(BuyNavigatorTarget.ELECTRICITY_TNC_LANDING);
        navigationTarget = navigationTarget.withParam(CommonNavigatorTarget.EXTRAS.TERMS_AND_CONDITION_TYPE, TermsAndConditionClassification.PREPAID_ELECTRICITY_TN_C);
        navigationTarget.withParam(BuyNavigatorTarget.ELECTRICITY_IS_FBE_FLOW, false);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void openYourBanker() {
        if (view != null) {
            view.sendAnalytics(PaymentsTracking.YOUR_BANKER_WIDGET_ACTION, TrackingParam.VAL_WIDGET_DASHBOARD);
        }
        getBankerDetails(secOfficerID);
    }

    void openBorrowScreen() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        adobeContextData.setWidgetName(TrackingParam.VAL_WIDGET_OFFERS_FOR_YOU);
        adobeContextData.setInitiations();
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_WIDGET_OFFERS, cdata);

        NavigationTarget navigationTarget = NavigationTarget.to(LoansNavigatorTarget.LOANS_BORROW_SCREEN);
        navigationRouter.navigateTo(navigationTarget);
    }

    void openCreditHealthScreen() {
        mMemoryApplicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);

        trackViewMyCreditScore();
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.CREDIT_HEALTH_FLOW);
        navigationRouter.navigateTo(navigationTarget);
    }

    void openHomeLoanToolkit() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        adobeContextData.setWidgetName(TrackingParam.VAL_WIDGET_HOME_LOANS);
        adobeContextData.setInitiations();
        analytics.sendEventActionWithMap(AppTracking.HOME_LOAN_WIDGET_CLICKED, cdata);

        NavigationTarget navigationTarget = NavigationTarget.to(LoansNavigatorTarget.HOME_LOAN_TOOLKIT).withParam(NavigationTarget.PARAM_IS_FROM_WIDGET, true);
        navigationRouter.navigateTo(navigationTarget);
    }


    void navigateToStatementsAndDocuments() {
        HashMap<String, Object> cData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.setWidgetName(TrackingParam.VAL_WIDGET_STATEMENTS);
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_DOCUMENT_REQUEST);
        adobeContextData.resetEntryInteraction();
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_WIDGET_STATEMENTS, cData);

        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SELECT_STATEMENT_OR_DOCUMENT_OPTION));
    }


    void getBankerDetails(String secOfficerCd) {
        if (view != null) {
            view.showOverviewLoading(true);
        }
        mGetBankerDetailsUseCase.execute(secOfficerCd)
                .compose(bindToLifecycle())
                .subscribe(
                        viewBankerDetailsDataModel -> {
                            if (viewBankerDetailsDataModel != null && view != null) {
                                view.showOverviewLoading(false);
                                view.showBankerDetails(mViewBankerResponseDataToViewModelMapper.mapData(viewBankerDetailsDataModel));
                            }
                        },
                        error -> {
                            if (view != null) {
                                view.showOverviewLoading(false);
                            }
                        }
                );
    }

    void navigateToViewBanker(BankerDataViewModel bankerDataViewModel, String division) {
        if (bankerDataViewModel != null) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.VIEW_BANKER).
                    withParam(NavigationTarget.PARAM_BANKER_RETRY, 0).
                    withParam(NavigationTarget.PARAM_BANKER_DIVISION, division).
                    withParam(NavigationTarget.PARAM_BANKER_IMAGE, bankerDataViewModel.getBankerPicture()).
                    withParam(NavigationTarget.PARAM_BANKER_FIRSTNAME, bankerDataViewModel.getFirstName()).
                    withParam(NavigationTarget.PARAM_BANKER_LASTNAME, bankerDataViewModel.getLastName())
                    .withParam(NavigationTarget.PARAM_BANKER_EMAIL, bankerDataViewModel.getEmailAddress()).
                    withParam(NavigationTarget.PARAM_BANKER_PHONE, StringUtils.isNullOrEmpty(bankerDataViewModel.getCellPhoneNumber()) ? bankerDataViewModel.getWorkNumber() : bankerDataViewModel.getCellPhoneNumber())
            );
        }
    }

    void trackActionMyAccountProductGroup(String trackAction) {
        if (view != null) {
            String productGroup = view.getOverviewProductGroup();

            if (!TextUtils.isEmpty(productGroup)) {

                HashMap<String, Object> cdata = new HashMap<>();
                AdobeContextData adobeContextData = new AdobeContextData(cdata);
                adobeContextData.resetDimensions();
                if (trackAction != null && trackAction.equals(AppTracking.MY_ACCOUNTS_NOTIFICATIONS)) {
                    cdata.put(TrackingEvent.ANALYTICS.KEY_PRODUCTS, productGroup + ";;;");
                    adobeContextData.setFeature(TrackingParam.VAL_NOTIFICATIONS);
                    adobeContextData.setFeatureCategory(TrackingParam.VAL_CX_SERVICES);
                    adobeContextData.setEntryPoint(TrackingParam.VAL_APP_NOTIFICATIONS_MY_ACCOUNTS);
                } else if (trackAction != null && trackAction.equals(AppTracking.MY_ACCOUNTS_CHAT)) {
                    cdata.put(TrackingEvent.ANALYTICS.KEY_PRODUCTS, productGroup + ";;;");
                    cdata.put(AdobeDimensions.KEY_NEDBANK_CHAT_CONTEXT, productGroup);
                    cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_USER_JOURNEY, TrackingParam.VAL_INITIATE_CHAT);
                } else {
                    cdata.put(TrackingEvent.ANALYTICS.KEY_PRODUCTS, productGroup + ";;;");
                }
                analytics.sendEventActionWithMap(trackAction, cdata);
            }
        }
    }

    void trackActionAccountProductNGroup(OverviewType overviewType, String accountName, String accountCode, String trackAction, boolean isPocketAccount) {
        if (view != null) {
            String productGroup = view.getOverviewProductGroup();
            String product = view.getOverviewAccountType(overviewType, accountName, accountCode, isPocketAccount);

            if (!TextUtils.isEmpty(productGroup) && !TextUtils.isEmpty(product)) {
                String val = productGroup + ";" + product + ";;";

                mMemoryApplicationStorage.putString(StorageKeys.DASHBOARD_SELECTED_PRODUCT_AND_CATEGORY, val);

                HashMap<String, Object> cdata = new HashMap<>();
                AdobeContextData adobeContextData = new AdobeContextData(cdata);
                adobeContextData.resetDimensions();
                adobeContextData.setProductCategory(productGroup);
                adobeContextData.setProductAccount(product);
                adobeContextData.setCategoryAndProduct(val);
                adobeContextData.setProductCode(accountCode);
                analytics.sendEventActionWithMap(trackAction, cdata);
            }
        }
    }

    int fetchAccountType(String accountCode) {
        for (za.co.nedbank.core.Constants.ACCOUNT_TYPES account : za.co.nedbank.core.Constants.ACCOUNT_TYPES.values()) {
            if (account.getAccountTypeCode().equalsIgnoreCase(accountCode)) {
                return account.getAccountTypeStringResId();
            }
        }
        return -1;
    }

    boolean isInsuranceToggleAvailable() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.INSURANCE);
    }


    public void checkAndShowFeatureDeepLinkScreen(List<AccountsOverview> accountsOverviews) {
        String featureName = mApplicationStorage.getString(za.co.nedbank.core.Constants.DEEP_LINK_DASHBOARD_SELECTION, null);

        if (featureName != null) {
            int position = -1;
            int dummyValue = -1;

            for (AccountsOverview overview : accountsOverviews) {
                dummyValue++;
                if (overview.overviewType.getAccountTypeId() == OverviewType.REWARDS.getAccountTypeId()) {
                    position = dummyValue;
                    break;
                }
            }
            if (view != null && position != -1) {
                view.moveToDashboardPosition(position);
            }
            // clearing the value
            if (featureName.equals(DynamicFeatureCardDetailEnum.JOIN_GREENBACKS.getName()) && position != -1) {
                AccountSummary greenbackAccountSummary = getGreenBackAccount(accountsOverviews.get(position));
                if (greenbackAccountSummary != null) {
                    accountTypeClicked(greenbackAccountSummary, featureName);
                }
                mApplicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_DASHBOARD_SELECTION);
            } else if (featureName.equals(DynamicFeatureCardDetailEnum.GREENBACKS.getName()) && position != -1) {
                if (isGreenbacksAvailable(accountsOverviews, position)) {
                    mMemoryApplicationStorage.putBoolean(za.co.nedbank.core.Constants.IS_GB_REDEEM_DEEP_LINK_FLOW, true);
                    accountTypeClicked(accountsOverviews.get(position).accountSummaries.get(0), featureName);
                } else {
                    HashMap<String, Object> cdata = new HashMap<>();
                    AdobeContextData adobeContextData = new AdobeContextData(cdata);
                    adobeContextData.setEntryPoint(TrackingParam.VAL_DEEP_LINK);
                    analytics.sendEventStateWithMap(PaymentsTrackingEvent.KEY_GREENBACK, cdata);
                }
                mApplicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_DASHBOARD_SELECTION);
            } else if (featureName.equals(DynamicFeatureCardDetailEnum.REDEEM_GREENBACKS.getName()) && position != -1) {
                AccountSummary greenbackAccountSummary = getGreenBackAccount(accountsOverviews.get(position));
                if (greenbackAccountSummary != null) {
                    mMemoryApplicationStorage.putBoolean(za.co.nedbank.core.Constants.IS_GB_REDEEM_DEEP_LINK_FLOW, true);
                    accountTypeClicked(greenbackAccountSummary, featureName);
                }
                mApplicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_DASHBOARD_SELECTION);
            }
        }
    }

    private AccountSummary getGreenBackAccount(AccountsOverview accountsOverview) {
        for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
            if (accountSummary.getName().equalsIgnoreCase(za.co.nedbank.services.Constants.OVERVIEW_REWARDS_GREENBACKS)) {
                return accountSummary;
            }
        }
        return null;
    }

    private boolean isGreenbacksAvailable(List<AccountsOverview> accountsOverviews, int position) {
        return accountsOverviews.get(position) != null
                && accountsOverviews.get(position).accountSummaries != null
                && !accountsOverviews.get(position).accountSummaries.isEmpty()
                && accountsOverviews.get(position).accountSummaries.get(0) != null
                && !StringUtils.isNullOrEmpty(accountsOverviews.get(position).accountSummaries.get(0).getNumber())
                && !StringUtils.isNullOrEmpty(accountsOverviews.get(position).accountSummaries.get(0).getRewardsProgram())
                && accountsOverviews.get(position).accountSummaries.get(0).getRewardsProgram().equals(GREENBACKS_CURRENCY);
    }

    public void trackActionForPayMe() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        adobeContextData.setFeature(TrackingParam.VAL_PAY_ME_REQUEST);
        adobeContextData.setWidgetName(TrackingParam.VAL_WIDGET_PAY_ME);
        adobeContextData.setInitiations();
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_PAY_ME, cdata);
    }

    public void trackActionWithEntryPoint(String actionName, String entryPoint) {
        HashMap cdata = new HashMap<String, Object>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        if (entryPoint != null)
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, entryPoint);
        if (actionName != null)
            analytics.sendEventActionWithMap(actionName, cdata);
    }

    public void setZoomSDKVersion(String version) {
        mApplicationStorage.putString(ZOOM_SDK_VERSION_KEY, version);
    }

    public void trackWidgetAction(String actionName, String entryPoint, String feature, String featureCategory, String widgetName) {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.resetDimensions();
        adobeContextData.setEntryPoint(entryPoint);
        adobeContextData.setFeature(feature);
        adobeContextData.setFeatureCategory(featureCategory);
        adobeContextData.setInitiations();
        adobeContextData.setWidgetName(widgetName);
        analytics.sendEventActionWithMap(actionName, contextData);
    }

    public void trackActionWithContextDataNA(String actionName) {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.resetDimensions();
        analytics.sendEventActionWithMap(actionName, contextData);
    }

    public void setmLandOnFinancialWellnessCarousel(boolean mLandOnFinancialWellnessCarousel) {
        this.mLandOnFinancialWellnessCarousel = mLandOnFinancialWellnessCarousel;
    }

    /* Based on the value of FTR_DISPLAY_CH_FIRST, determine if we need to land on the
     * Financial Wellness carousel or not
     * If true and if credit health is available then land on Financial Wellness carousel for the first time.
     * After that, land on Everyday Banking */
    private void addFinancialWellness(Overview overview) {
        if (view != null && overview != null && overview.accountsOverviews != null) {
            int financialWellnessDisplayedOnLadingPageCount = mApplicationStorage.getInteger(StorageKeys.FINANCIAL_WELLNESS_DISPLAYED_ON_LANDING_COUNT, 0);

            if (financialWellnessDisplayedOnLadingPageCount <= Constants.MAX_COUNT_FOR_FINANCIAL_WELLNESS_CAROUSAL_AT_FIRST_POSITION
                    && !featureSetController.isFeatureDisabled(FeatureConstants.FTR_DISPLAY_CH_FIRST) &&
                    isCreditHealthAvailable()) {
                mLandOnFinancialWellnessCarousel = true;
            }

            AccountsOverview financialWellnessOverview = getFinancialWellnessOverview();
            if (financialWellnessOverview != null
                    && financialWellnessOverview.accountSummaries != null
                    && !financialWellnessOverview.accountSummaries.isEmpty()) {
                overview.accountsOverviews.add(FINANCIAL_WELLNESS_POSITION, financialWellnessOverview);
                mIsFinancialWellnessAvailable = true;
            }
        }
    }

    private AccountsOverview getFinancialWellnessOverview() {
        List<AccountSummary> accountSummaryList = new ArrayList<>();
        AccountsOverview financialWellnessOverview = null;

        if (isNFWAvailable()) {
            addNFWAccountSummary(accountSummaryList);
        }
        // MoneyTracker
        addMoneyTrackerAccountSummary(accountSummaryList);

        //Credit Health
        addCreditHealthAccountSummary(accountSummaryList);

        if (!accountSummaryList.isEmpty()) {
            financialWellnessOverview = new AccountsOverview();
            financialWellnessOverview.accountSummaries = accountSummaryList;
            financialWellnessOverview.overviewType = OverviewType.FINANCIAL_WELLNESS;
        }

        return financialWellnessOverview;
    }

    void addCreditHealthAccountSummary(List<AccountSummary> accountSummaryList) {
        if (isCreditHealthAvailable() && isCreditHealthAvailableForCarousal()
                && accountSummaryList != null) {
            AccountSummary accountSummary = new AccountSummary();
            accountSummary.setName(za.co.nedbank.services.Constants.CREDIT_HEALTH);
            accountSummary.setAccountType(OverviewType.FINANCIAL_WELLNESS);
            accountSummary.setSummaryValue(new BigDecimal(0));
            accountSummary.setNumber(StringUtils.HYPHEN);
            accountSummary.setAccountShown(true);
            accountSummaryList.add(accountSummary);
        }
    }

    boolean isCreditHealthAvailableForCarousal() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.FTR_CREDIT_HEALTH_CAROUSAL);
    }

    public boolean isCreditHealthAvailable() {
        return (!featureSetController.isFeatureDisabled(FeatureConstants.FTR_CREDIT_HEALTH)
                && !mIsBusinessUser
                && StringUtils.isValidRSAId(storageUtility.getIdOrTaxNumberString())
                && (StringUtils.isNullOrEmpty(birthDate) || getAgeByBirthDate() >= Constants.CREDIT_HEALTH_MIN_AGE)
        );
    }

    void updateFinancialWellnessDisplayedOnLandingCount() {
        int count = mApplicationStorage.getInteger(StorageKeys.FINANCIAL_WELLNESS_DISPLAYED_ON_LANDING_COUNT, 0);
        if (count <= Constants.MAX_COUNT_FOR_FINANCIAL_WELLNESS_CAROUSAL_AT_FIRST_POSITION) {
            mApplicationStorage.putInteger(StorageKeys.FINANCIAL_WELLNESS_DISPLAYED_ON_LANDING_COUNT, ++count);
        }
    }

    public void setOverviewTitleFor(OverviewType overviewType) {
        if (view != null) {
            view.setOverviewTitleFor(overviewType);
        }
    }

    public void trackViewMyCreditScore() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(CreditHealthTrackingValue.FEATURE_CATEGORY);
        adobeContextData.setFeature(CreditHealthTrackingValue.FEATURE_CREDIT_SCORE);
        analytics.sendEventActionWithMap(CreditHealthTrackingEvent.DASHBOARD_SELECT_CREDIT_SCORE, adobeContextData.getCdata());
    }

    public void trackFinancialWellnessPageLoad() {
        if (!mApplicationStorage.getBoolean(StorageKeys.IS_FINANCIAL_WELLNESS_PAGE_LOAD_TRACKED, false)) {
            analytics.sendState(CreditHealthTrackingValue.PAGE_LOAD_FINANCIAL_WELLNESS);
            mApplicationStorage.putBoolean(StorageKeys.IS_FINANCIAL_WELLNESS_PAGE_LOAD_TRACKED, true);
        }
    }

    public void refreshAccountsAndLoadOverview() {
        refreshUserUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(response -> loadOverview(), error -> loadOverview());
    }

    public boolean isLandOnFinancialWellnessCarousel() {
        return mLandOnFinancialWellnessCarousel;
    }

    public void setLandOnFinancialWellnessCarousel(boolean isLandOnFinancialWellnessCarousel) {
        mLandOnFinancialWellnessCarousel = isLandOnFinancialWellnessCarousel;
    }

    public boolean isFinancialWellnessAvailable() {
        return mIsFinancialWellnessAvailable;
    }

    public int getOverviewPosition(List<AccountsOverview> accountsOverviews, OverviewType overviewType) {
        if (overviewType != null && accountsOverviews != null && !accountsOverviews.isEmpty()) {
            for (AccountsOverview overview :
                    accountsOverviews) {
                if (overview.overviewType == overviewType) {
                    return accountsOverviews.indexOf(overview);
                }
            }
        }
        return 0;
    }

    public AccountSummary getCurrentAccount(int everydayBankingPosition, List<AccountsOverview> accountsOverviews) {
        for (int i = 0; i < accountsOverviews.get(everydayBankingPosition).accountSummaries.size(); i++) {
            if (za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.getAccountTypeCode()
                    .equals(accountsOverviews.get(everydayBankingPosition).accountSummaries.get(i).getAccountCode())) {
                return accountsOverviews.get(everydayBankingPosition).accountSummaries.get(i);
            }
        }
        return null;
    }

    public AccountSummary getSavingsAccount(int everydayBankingPosition, List<AccountsOverview> accountsOverviews) {
        for (int i = 0; i < accountsOverviews.get(everydayBankingPosition).accountSummaries.size(); i++) {
            if (za.co.nedbank.core.Constants.ACCOUNT_TYPES.SA.getAccountTypeCode()
                    .equals(accountsOverviews.get(everydayBankingPosition).accountSummaries.get(i).getAccountCode())) {
                return accountsOverviews.get(everydayBankingPosition).accountSummaries.get(i);
            }
        }
        return null;
    }

    void addNFWAccountSummary(List<AccountSummary> accountSummaryList) {
        if (accountSummaryList != null) {
            AccountSummary accountSummary = new AccountSummary();
            accountSummary.setName(za.co.nedbank.services.Constants.NFW);
            accountSummary.setAccountType(OverviewType.FINANCIAL_WELLNESS);
            accountSummary.setSummaryValue(new BigDecimal(0));
            accountSummary.setNumber(StringUtils.HYPHEN);
            accountSummary.setAccountShown(true);
            accountSummaryList.add(accountSummary);
        }
    }

    boolean isNFWAvailable() {
        return !(isBusinessUser() || featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_SMART_MONEY));
    }

    void addMoneyTrackerAccountSummary(List<AccountSummary> accountSummaryList) {
        if (isMoneyTrackerAvailable() && accountSummaryList != null) {
            AccountSummary accountSummary = new AccountSummary();
            accountSummary.setName(za.co.nedbank.services.Constants.MONEY_TRACKER);
            accountSummary.setAccountType(OverviewType.FINANCIAL_WELLNESS);
            accountSummary.setSummaryValue(new BigDecimal(0));
            accountSummary.setNumber(StringUtils.HYPHEN);
            accountSummary.setAccountShown(true);
            accountSummaryList.add(accountSummary);
        }
    }

    boolean isMoneyTrackerAvailable() {
        return (!featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_MONEY_TRACKER));
    }

    void openNFWScreen() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeature(SpendTrackingEvent.VAL_FEATURE_MY_SMART_MONEY);
        adobeContextData.setFeatureCategory(SpendTrackingEvent.VAL_FEATURE_CATEGORY_PERSONAL_FINANCIAL_WELLNESS);
        analytics.sendEventActionWithMap(SpendTrackingEvent.TAG_MSM_DASHBOARD_CLICK, cdata);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.NFW_DASHBOARD_ACTIVITY);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void addFamilyBankingAndLifestyleOverview(Overview overview) {
        boolean isValidRSAId = mApplicantIdForeignCheckValidator.validateInput(idOrTaxIdNoStr).isOk();
        if (isValidRSAId) {
            overview.accountsOverviews.add(getFamilyBankingOverviewAccounts(false, null));
            storeFBDataInPreferences(null, null, false);
        }
    }

    public AccountsOverview getFamilyBankingOverviewAccounts(boolean groupCreated, LifestyleDashboardResponseDataModel data) {
        AccountsOverview accountsOverview = new AccountsOverview();
        accountsOverview.summaryValue1 = BigDecimal.valueOf(0.0);
        accountsOverview.summaryValue2 = BigDecimal.valueOf(0.0);
        accountsOverview.overviewType = LIFESTYLE;
        String label = groupCreated ? OVERVIEW_FAMILY_BANKING : OVERVIEW_FINANCIAL_BENEFITS;
        AccountSummary accountSummary = new AccountSummary("1", "", OverviewType.LIFESTYLE, label, new BigDecimal(0), StringUtils.CURRENCY_SYMBOL);
        if (groupCreated) {
            accountSummary.setId(data.getGroupId());
            accountSummary.setFbGroupName(data.getGroupName());
            accountSummary.setIsFbGroupCreated(1);
        } else {
            accountSummary.setIsFbGroupCreated(0);
        }
        if (data != null) {
            accountSummary.setFbInvitePending(data.isInvitePending());
            accountSummary.setFbAccountLinked(data.isAccountLinked());
        }
        accountSummary.setFamilyBankingAccount(true);
        accountsOverview.accountSummaries.add(accountSummary);
        return accountsOverview;
    }

    public void storeFBDataInPreferences(String groupId, String groupName) {
        mApplicationStorage.putString(UserInformationKeys.FAMILY_BANKING_GROUP_ID, groupId);
        mApplicationStorage.putString(UserInformationKeys.FAMILY_BANKING_GROUP_NAME, groupName);
    }

    public void storeFBDataInPreferences(String groupId, String groupName, boolean accountLinked) {
        storeFBDataInPreferences(groupId, groupName);
        mApplicationStorage.putBoolean(UserInformationKeys.FAMILY_BANKING_ACCOUNT_SELECTED, accountLinked);
    }

    void openMoneyTrackerScreen() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeature(TrackingEvent.ANALYTICS.VAL_FEATURE_MONEY_TRACKER);
        adobeContextData.setFeatureCategory(TrackingEvent.ANALYTICS.VAL_FEATURE_CATEGORY_PERSONAL_FINANCIAL_WELLNESS);
        if (view != null) {
            adobeContextData.setContext1(String.format("%s%s",
                    VAL_MT_CLIENT_TYPE_PREFIX, isBusinessUser() ? view.getUserTypeString(true) :
                            view.getUserTypeString(false)));
        }
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_INTERACTION, TrackingEvent.ANALYTICS.VAL_ONE);
        analytics.sendEventActionWithMap(TrackingEvent.FINANCIAL_WELLNESS_MONEY_TRACKER, cdata);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.MONEY_TRACKER_FLOW_NAVIGATION_ACTIVITY);
        navigationRouter.navigateTo(navigationTarget);
    }

    public int getAgeByBirthDate() {
        return StringUtils.isNotEmpty(birthDate) ? FormattingUtil.getAgeDifference(birthDate) : za.co.nedbank.core.Constants.ZERO;
    }

    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    public void onServerStateChangedEvent(ServerStateChangedEvent stateChangedEvent) {
        boolean isChatDisconnected = stateChangedEvent.getNewState() == ConnectionState.Disconnected;
        view.onServerStateChanged(!isChatDisconnected);
    }

    public void fetchAccountsForShapIdWidget() {
        mGetAccountsUseCase.execute(IAccountOptions.PAYMENT_ACCOUNTS)
                .compose(bindToLifecycle())
                .subscribe(payAccountsList -> {
                    for (AccountViewModel accountViewModel : payAccountsList) {
                        if (accountViewModel.getAccountRuleViewModel() != null) {
                            boolean isShowRppFeature = accountViewModel.getAccountRuleViewModel().ismRegisterSharpId();
                            if (isShowRppFeature) {
                                mMemoryApplicationStorage.putBoolean(IS_PAYSHAP_WIDGET_ENABLED, isShowRppFeature);
                                if (view != null) {
                                    view.showShapIDIcon();
                                }
                                break;
                            }
                        }
                    }

                }, t -> NBLogger.d("account api", t.getLocalizedMessage()));

    }

    boolean isFicaEnabled() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DIGITAL_ENABLE_REFICA);
    }

    boolean isFicaEnabledForPayME() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DIGITAL_ENABLE_REFICA_NEW_ENTRY);
    }

    public void trackActionForApplyWidget() {
        HashMap<String, Object> cData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.setWidgetName(TrackingParam.VAL_WIDGET_APPLY);
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        adobeContextData.resetEntryInteraction();
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_WIDGET_APPLY, cData);
    }

    public void trackActionForShopWidget() {
        HashMap<String, Object> cData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.setFeature(TrackingParam.VAL_AVO_SHOP);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_WIDGET_SHOP, cData);
    }

    public void trackActionPayRequestWidget() {
        HashMap<String, Object> cData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.setWidgetName(TrackingParam.VAL_WIDGET_PAY_REQUEST);
        adobeContextData.setEntryPoint(TrackingParam.VAL_WIDGET_DASHBOARD);
        adobeContextData.setFeature(TrackingParam.VAL_REQUEST_TO_PAY);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_MONETARY_TRANSACTIONS);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_WIDGET_REQUEST_TO_PAY, cData);
    }

    public void sendActionForMerchantWidget() {
        HashMap<String, Object> cData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.setFeature(TrackingParam.VAL_MERCHANT_SERVICING);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_MANAGE_MERCHANT_SERVICING);
        analytics.sendEventActionWithMap(AppTracking.SMA_DASHBOARD_CLICK, cData);
    }

    public void insureWidgetClicked() {
        trackWidgetAction(AppTracking.MY_ACCOUNTS_WIDGET_INSURE, TrackingParam.VAL_WIDGET_DASHBOARD, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING, TrackingParam.VAL_WIDGET_INSURE);
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN)
                .withParam(InsuranceConstants.ParamKeys.IS_FLOW_TYPE_MY_POLICY, InsuranceConstants.FlowType.QUOTE);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void smaIntroduction() {
        sendActionForMerchantWidget();
        if (!mApplicationStorage.getBoolean(StorageKeys.SMA_INTRODUCTION, false)) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SMA_MERCHANT_INTRODUCTION));
        } else {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SMA_MERCHANT_LANDING));
        }
    }

    public void moveToAvoDemoSplashScreen() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.AVO_DEMO_SPLASH_SCREEN));
    }

    public void shopDashborad() {
        boolean doNotShowAgain = mApplicationStorage.getBoolean(StorageKeys.DATA_USAGE_NEVER_ASK_AGAIN_WIDGET_SHOP, false);

        if (doNotShowAgain || featureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_DATA_USAGE_SCREEN)) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHOP_DASHBOARD));
            return;
        }

        navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.DATA_USAGE_WARNING)
                        .withParam(za.co.nedbank.core.Constants.FROM_SCREEN, za.co.nedbank.core.Constants.FROM_SHOP_WIDGET)
                        .withParam(za.co.nedbank.core.Constants.COMPLETE_SCREEN_LABEL, view.getActivityLabel()))
                .subscribe(navigationResult -> {
                    boolean accepted = navigationResult.getBooleanParam(NavigationTarget.RESULT_DATA_USAGE_WARNING_ACCEPT);
                    if (accepted) {
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHOP_DASHBOARD));
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public boolean isKidsProfile() {
        return kidsProfileUtilsWrapper.isUserUnderAged();
    }

    public void discRenewalWidgetClick() {
        trackDiscRenewalWidgetEvent();
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.VEHICLES_LANDING));
    }

    public void trackDiscRenewalWidgetEvent() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setEntryPoint(WIDGET_DASHBOARD);
        adobeContextData.setFeatureCategory(ADDITIONAL_SERVICES);
        adobeContextData.setFeature(DISC_RENEWAL_AND_FINES);
        analytics.sendEventActionWithMap(MYACCOUNT_WIDGET_DISC_AND_FINES, cdata);
    }


    public boolean isDiscsAndFinesAvailable() {
        return getAgeByBirthDate() >= AGE_LIMIT;
    }

    public void openForYouOfferEnhanceScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.FOR_YOU_OFFERS_ENHANCE)
                .withParam(Constants.BundleKeys.IS_FROM_OFFERS_FOR_YOU_WIDGET_DASHBOARD, true);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void moveToMakePayshapRequest() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MAKE_PAYSHAP_REQUEST));
    }

    public void saveShowHideBalance() {
        boolean isBalanceHide = isBalanceHidden();
        mApplicationStorage.putBoolean(StorageKeys.IS_BALANCE_HIDE, !isBalanceHide);
        view.refreshBalances();
    }

    public boolean isBalanceHidden() {
        return mApplicationStorage.getBoolean(StorageKeys.IS_BALANCE_HIDE, false);
    }

    public void trackEyeIconAnalytics(String eventName) {
        analytics.sendEvent(eventName, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    public boolean showShopWidgetBadge() {
        boolean isFeatureDisabled = featureSetController.isFeatureDisabled(FeatureConstants.FTR_SHOP_NOTIFICATION_BADGE);
        if (isFeatureDisabled) {
            return false;
        }
        String lastLoginTime = mApplicationStorage.getString(StorageKeys.NGI_LOGIN_TIMESTAMP, "");
        String lastBadgeDisplayTime = mApplicationStorage.getString(StorageKeys.LAST_SHOP_NOTIFICATION_DISPLAY_TIME, "");
        boolean isToday = NotificationUtils.isToday(lastBadgeDisplayTime, FormattingUtil.NGI_REQUEST_DATE_FORMAT);
        if (lastLoginTime.equals(lastBadgeDisplayTime)){
            //same login session but screen loaded multi time
            return true;
        }
        return lastBadgeDisplayTime.isEmpty() || !isToday;
    }

    public void navigateToInvestmentOptions () {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setCategoryAndProduct(EnrollV2TrackingValue.ANALYTICS.PRODUCT_GROUP_INVESTMENT);
        adobeContextData.setEntryPoint(TrackingParam.VAL_APPLY_DASHBOARD);
        adobeContextData.setProductCategory(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_GROUP_INVESTMENT);
        adobeContextData.setFeature(EnrollV2TrackingValue.ANALYTICS.VAL_PRODUCT_ON_BOARDING);
        adobeContextData.setFeatureCategory(EnrollV2TrackingValue.ANALYTICS.VAL_SALES_AND_ON_BOARDING);
        analytics.sendEventActionWithMap(NgiTrackingEvent.INV_DASHBOARD_EXPLORE_OPTION_CLICK, cdata);

        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_INVESTMENT_OPTION_LANDING)
                .withParam(PARAM_ONIA_CLIENT_TYPE, clientType)
                .withParam(PARAM_ONIA_BIRTH_DATE, birthDate)
                .withParam(PARAM_ONIA_SEC_OFFICER_CD, secOfficerID)
                .withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, cisNumber));
    }

    void prepareDataAndSendToWidgetAdapter(String action) {
        if (StringUtils.isNotEmpty(action))
            widgetAdapter.fetchData(action, null, null);
    }
}
