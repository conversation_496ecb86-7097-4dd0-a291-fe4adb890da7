package za.co.nedbank.ui.view.ita.ita_authentication;


import android.app.Activity;
import android.os.Bundle;

import androidx.annotation.Nullable;

import org.greenrobot.eventbus.EventBus;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.view.IFragmentToActivityComListener;
import za.co.nedbank.databinding.ActivityItaAuthBinding;
import za.co.nedbank.ui.di.AppDI;

public class ITAAuthenticationActivity extends NBBaseActivity implements ITAAuthenticationView, IFragmentToActivityComListener {


    @Inject
    ITAAuthenticationPresenter mItaAuthenticationPresenter;

    private String fromScreen = Constants.FROM_ITA_FLOW;

    @Override
    protected void onCreate(@Nullable @org.jetbrains.annotations.Nullable Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        mItaAuthenticationPresenter.bind(this);
        ActivityItaAuthBinding binding = ActivityItaAuthBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        if (getIntent() != null
                && getIntent().getExtras() != null
                && !getIntent().getExtras().isEmpty()
                && getIntent().getExtras().containsKey(Constants.FROM_SCREEN)) {
            fromScreen = getIntent().getExtras().getString(Constants.FROM_SCREEN);
        }

        addFragment(ITAAuthFragment.getInstance(fromScreen), R.id.main_container);
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
        mItaAuthenticationPresenter.unbind();
    }

    @Override
    public void returnResult() {
        setResult(Activity.RESULT_OK);
    }

    @Override
    public void onEvent(int event, Bundle bundle) {
        switch (event) {
            case EVENT_CONSTANTS.EVENT_PARTIAL_LOGIN_SUCCESS:
                returnResult();
                close();
                break;
            case EVENT_CONSTANTS.EVENT_PARTIAL_LOGIN_FAILURE:
            case EVENT_CONSTANTS.EVENT_SHOW_FINGERPRINT_DIALOG_CLOSE:
                handleCancelFlow();
                break;
            case EVENT_CONSTANTS.EVENT_TERMS_AND_CONDITION_TO_CAM_PERMISSION_SCREEN:
                ITAAuthFragment fragment = (ITAAuthFragment) getSupportFragmentManager().findFragmentById(R.id.main_container);
                if (fragment != null) {
                    fragment.pinPasswordLogin(fromScreen);
                }
                break;
            case EVENT_CONSTANTS.EVENT_SCAN_FINGER_SUCCESS_RESPONSE:
                ITAAuthFragment fragment1 = (ITAAuthFragment) getSupportFragmentManager().findFragmentById(R.id.main_container);
                if (fragment1 != null) {
                    fragment1.handleFingerPrintDetectSuccess();
                }
                break;
            default:
                close();
                break;
        }

    }

    private void handleCancelFlow() {
        setResult(Activity.RESULT_CANCELED);
        close();
    }
}
