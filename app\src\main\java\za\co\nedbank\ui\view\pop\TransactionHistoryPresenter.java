package za.co.nedbank.ui.view.pop;

import android.annotation.SuppressLint;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.exception.NoContentException;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryData;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryParentData;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryRequestData;
import za.co.nedbank.ui.domain.usecase.pop.GetTransactionHistoryUseCase;
import za.co.nedbank.ui.view.mapper.TransactionHistoryDataToTransactionHistoryViewModelMapper;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class TransactionHistoryPresenter extends NBBasePresenter<TransactionHistoryView> {

    private static final int PAGE_SIZE = 30;
    public int contactCardId;
    private int mPage = 1;
    private final NavigationRouter mNavigationRouter;
    private final GetTransactionHistoryUseCase getTransactionHistoryUseCase;
    private TransactionHistoryDataToTransactionHistoryViewModelMapper transactionHistoryDataToTransactionHistoryViewModelMapper;
    private List<TransactionHistoryViewModel> mTransactionDataList = new ArrayList<>();
    private final FeatureSetController mFeatureSetController;
    private final Analytics mAnalytics;

    final static int DOWNLOAD_STATE_INITIAL = 0;   // Nothing downloaded
    final static int DOWNLOAD_COMPLETE = 1;        // Download Complete for last year
    final static int DOWNLOAD_COMPLETE_LAST_QUARTER = 2;  // Download Complete for last quarter
    final static int DOWNLOAD_COMPLETE_LESS_THAN_LAST_QUARTER = 3; // Downloaded some but not all data for last quarter
    final static int DOWNLOAD_COMPLETE_MORE_THAN_LAST_QUARTER = 4; // Downloaded more data than the last quarter but not complete data

    private int mDownloadState = DOWNLOAD_STATE_INITIAL;

    @Inject
    TransactionHistoryPresenter(final NavigationRouter navigationRouter, GetTransactionHistoryUseCase getTransactionHistoryUseCase, final TransactionHistoryDataToTransactionHistoryViewModelMapper transactionHistoryDataToTransactionHistoryViewModelMapper, FeatureSetController mFeatureSetController, Analytics analytics) {
        this.mNavigationRouter = navigationRouter;
        this.getTransactionHistoryUseCase = getTransactionHistoryUseCase;
        this.transactionHistoryDataToTransactionHistoryViewModelMapper = transactionHistoryDataToTransactionHistoryViewModelMapper;
        this.mFeatureSetController = mFeatureSetController;
        this.mAnalytics = analytics;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

    void initialiseData() {
        mPage = 1;
        mTransactionDataList = new ArrayList<>();
        mDownloadState = DOWNLOAD_STATE_INITIAL;
    }

    @SuppressLint("CheckResult")
    void getTransactionHistory() {
        if (dataDownloadComplete()) {
            return;
        }

        if (view != null) {
            // Show next page loader if there is already downloaded data available else show empty screen loader
            if (mTransactionDataList != null && !mTransactionDataList.isEmpty()) {
                view.showFooterView(TransactionFooterViewType.NEXT_PAGE_LOADING);
            } else {
                view.showListLoading();
            }
        }

        String endDate = getEndDate();

        String startDate = getStartDate();

        TransactionHistoryRequestData transactionHistoryRequestData = new TransactionHistoryRequestData();
        transactionHistoryRequestData.setContactCardId(contactCardId);
        transactionHistoryRequestData.setEndDate(endDate);
        transactionHistoryRequestData.setPage(String.valueOf(mPage));
        transactionHistoryRequestData.setPageSize(String.valueOf(PAGE_SIZE));
        transactionHistoryRequestData.setStartDate(startDate);

        getTransactionHistoryUseCase
                .execute(transactionHistoryRequestData)
                .compose(bindToLifecycle())
                .doOnTerminate(() -> {
                    view.setIsLoading(false);
                    view.showListLoaded();
                })
                .subscribe(
                        this::handleTransactionHistory,
                        this::showError
                );
    }


    private boolean isDownloadCompletedForLastQuarter() {
        return !(mDownloadState == DOWNLOAD_STATE_INITIAL || mDownloadState == DOWNLOAD_COMPLETE_LESS_THAN_LAST_QUARTER);
    }

    void handleTransactionHistory(TransactionHistoryParentData transactionHistoryParentData) {
        if (transactionHistoryParentData != null) {
            MetaDataModel transactionMetadataModel = transactionHistoryParentData.getTransactionMetadataModel();
            List<TransactionHistoryData> transactionHistoryData = transactionHistoryParentData.getTransactionHistoryData();

            if (transactionMetadataModel != null) {
                setDownloadStateAndPageNumber(transactionMetadataModel.getPageLimit(), transactionMetadataModel.getPage());
                showTransactionHistory(transactionHistoryData);
            }

        } else {
            showError(null);
        }
    }

    void setDownloadStateAndPageNumber(int pageLimit, int page) {
        if (pageLimit <= page) {
            if (!isDownloadCompletedForLastQuarter()) {
                setDownloadState(DOWNLOAD_COMPLETE_LAST_QUARTER);
                setPageNumber(1);
            } else {
                setDownloadState(DOWNLOAD_COMPLETE);
            }
        } else {
            if (mDownloadState == DOWNLOAD_STATE_INITIAL) {
                setDownloadState(DOWNLOAD_COMPLETE_LESS_THAN_LAST_QUARTER);
            } else {
                setDownloadState(DOWNLOAD_COMPLETE_MORE_THAN_LAST_QUARTER);
            }
            setPageNumber(mPage + 1);
        }
    }

    void setPageNumber(int pageNumber) {
        mPage = pageNumber;
    }

    void setDownloadState(int downloadState) {
        mDownloadState = downloadState;
    }

    /**
     * If we are in the process of downloading the last quarter data, start date would be 3 months back from current date,
     * else it would be 1 year back from current date.
     *
     * @return formatted date as string.
     */
    String getStartDate() {
        Calendar startCalendar = Calendar.getInstance();
        if (!isDownloadCompletedForLastQuarter()) {
            startCalendar.add(Calendar.DAY_OF_MONTH, -90);
        } else {
            startCalendar.add(Calendar.YEAR, -1);
        }

        return FormattingUtil.getDateInStringFormat(startCalendar.getTime(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD);
    }

    /**
     * If we are in the process of downloading the last quarter data, end date would be current date,
     * else it would be 3 months and one day back from current date.
     *
     * @return formatted date as string.
     */
    String getEndDate() {
        Calendar endCalendar = Calendar.getInstance();
        if (isDownloadCompletedForLastQuarter()) {
            endCalendar.add(Calendar.DAY_OF_MONTH, -91);
        }

        return FormattingUtil.getDateInStringFormat(endCalendar.getTime(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD);
    }

    void showError(Throwable throwable) {
        if (throwable instanceof NoContentException) {
            if (!isDownloadCompletedForLastQuarter()) {
                setDownloadState(DOWNLOAD_COMPLETE_LAST_QUARTER);
            } else {
                setDownloadState(DOWNLOAD_COMPLETE);
            }
            checkIfPreviousDataRequiredTobeFetched();
        } else {
            if (view != null) {
                if (mTransactionDataList == null || mTransactionDataList.isEmpty()) {
                    view.showEmptyListIndicator();
                } else {
                    view.showError(throwable != null ? throwable.getMessage() : "Error");
                    view.hideFooterView();
                }
            }
        }
    }

    @SuppressLint("CheckResult")
    void showTransactionHistory(List<TransactionHistoryData> transactionHistoryList) {

        if (view != null && transactionHistoryList != null && !transactionHistoryList.isEmpty()) {
            showViewMoreOrListEndFooterViewIfApplicable();
            List<TransactionHistoryViewModel> transactionHistoryViewModels = transactionHistoryDataToTransactionHistoryViewModelMapper.
                    mapTransactionHistoryDataToTransactionHistoryViewModel(transactionHistoryList);

            if (contactCardId != 0) {
                List<TransactionHistoryViewModel> transactionHistoryViewModelsTemp = new ArrayList<>();
                for (TransactionHistoryViewModel transactionHistoryViewModel : transactionHistoryViewModels) {
                    if (transactionHistoryViewModel.getContactCardID() == contactCardId) {
                        transactionHistoryViewModelsTemp.add(transactionHistoryViewModel);
                    }
                }
                addDataToTransactionList(transactionHistoryViewModelsTemp);
                updateView();

            } else if (view != null && mDownloadState == DOWNLOAD_COMPLETE &&
                    (mTransactionDataList == null || mTransactionDataList.isEmpty())) {
                view.showEmptyListIndicator();
            }
        } else {
            checkIfPreviousDataRequiredTobeFetched();
        }
    }

    void updateView() {
        if (view != null) {
            if (!mTransactionDataList.isEmpty()) {
                view.showTransactionHistoryViewModel(mTransactionDataList);
            } else if (dataDownloadComplete()) {
                view.showEmptyListIndicator();
            }
        }
    }

    void showViewMoreOrListEndFooterViewIfApplicable() {
        if (dataDownloadComplete()) {
            view.showFooterView(TransactionFooterViewType.NO_MORE_PAYMENTS);
        } else if (mDownloadState == DOWNLOAD_COMPLETE_LAST_QUARTER && isFeatureAvailableForTransactionsBeyond90()) {
            view.showFooterView(TransactionFooterViewType.VIEW_MORE);
        }
    }

    void addDataToTransactionList(List<TransactionHistoryViewModel> transactionHistoryViewModelsTemp) {
        if (transactionHistoryViewModelsTemp != null) {
            mTransactionDataList.addAll(transactionHistoryViewModelsTemp);
        }
    }

    /**
     * After data download call, If no data available,show empty list UI if download process has completed and getTransactionHistory
     * if download process has not completed
     */
    void checkIfPreviousDataRequiredTobeFetched() {
        if (mTransactionDataList.isEmpty()) {
            if (dataDownloadComplete()) {
                if (view != null) {
                    view.showEmptyListIndicator();
                }
            } else if (mDownloadState == DOWNLOAD_COMPLETE_LAST_QUARTER) {
                getTransactionHistory();
            }
        } else {
            showViewMoreOrListEndFooterViewIfApplicable();
        }
    }

    void openTransactionDetails(TransactionHistoryViewModel transactionHistoryViewModel) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TRANSACTION_HISTORY_DETAIL);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.TRANSACTION_HISTORY_VIEW_MODEL, transactionHistoryViewModel);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    public void setContactCardId(int contactCardId) {
        this.contactCardId = contactCardId;
    }

    void onViewMoreClicked() {
        mAnalytics.sendEvent(StringUtils.EMPTY_STRING, AppTracking.CLICK_VIEW_MORE_BEYOND_90DAYS, StringUtils.EMPTY_STRING);
        getTransactionHistory();
    }

    boolean isFeatureAvailableForTransactionsBeyond90() {
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_RECIPIENT_TRANSACTION_HISTORY_BEYOND90);
    }

    boolean dataDownloadComplete() {
        return mDownloadState == DOWNLOAD_COMPLETE || (mDownloadState == DOWNLOAD_COMPLETE_LAST_QUARTER &&
                !isFeatureAvailableForTransactionsBeyond90());
    }
}
