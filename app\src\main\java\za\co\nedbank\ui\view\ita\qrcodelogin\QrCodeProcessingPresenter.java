package za.co.nedbank.ui.view.ita.qrcodelogin;

import android.annotation.SuppressLint;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.model.qrcode.QrCodeRequestData;
import za.co.nedbank.core.domain.model.qrcode.QrCodeResponseData;
import za.co.nedbank.core.domain.usecase.qrcode.QrCodeVerificationUseCase;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;

@SuppressLint("CheckResult")
public class QrCodeProcessingPresenter extends NBBasePresenter<QrCodeProcessingView> {

    private final NavigationRouter navigationRouter;
    private final QrCodeVerificationUseCase mQrCodeVerificationUseCase;

    @Inject
    QrCodeProcessingPresenter(
            final NavigationRouter navigationRouter,
            final QrCodeVerificationUseCase qrCodeVerificationUseCase
    ) {
        this.navigationRouter = navigationRouter;
        this.mQrCodeVerificationUseCase = qrCodeVerificationUseCase;
    }

    void loginWithQrCode() {
        mQrCodeVerificationUseCase.execute(getQrCodeRequestData())
                .compose(bindToLifecycle())
                .subscribe(this::handleQrCodeResponse,
                        throwable -> navigateToResult(Constants.QrLoginResult.ERROR));
    }

    private void handleQrCodeResponse(QrCodeResponseData qrCodeResponseData) {
        navigateToResult(QrCodeResponseData.getResult(qrCodeResponseData.getMetaData().getResult()));
    }

    private QrCodeRequestData getQrCodeRequestData() {
        QrCodeRequestData qrCodeRequestData = new QrCodeRequestData();
        qrCodeRequestData.setAction(view.getIntentType());
        qrCodeRequestData.setJwtToken(view.getToken());
        qrCodeRequestData.setVerificationId(0);
        return qrCodeRequestData;
    }

    public void navigateToResult(int result) {
        view.close();
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.QR_LOGIN_RESULT)
                .withParam(Constants.TYPE, result));
    }
}
