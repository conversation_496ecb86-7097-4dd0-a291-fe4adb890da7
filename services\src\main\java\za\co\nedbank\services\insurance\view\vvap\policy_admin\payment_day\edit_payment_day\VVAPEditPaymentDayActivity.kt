package za.co.nedbank.services.insurance.view.vvap.policy_admin.payment_day.edit_payment_day

import android.content.res.Resources
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.CheckedTextView
import android.widget.LinearLayout
import android.widget.ProgressBar
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import za.co.nedbank.core.base.NBBaseActivity
import za.co.nedbank.core.tracking.InsuranceTrackingEvent.EVENT_MANAGE_POLICY_SAVE_PAYMENT
import za.co.nedbank.core.tracking.InsuranceTrackingEvent.EVENT_MANAGE_POLICY_SUCCESS_PAYMENT
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.utils.FormattingUtil
import za.co.nedbank.core.utils.StringUtils
import za.co.nedbank.core.utils.ViewUtils
import za.co.nedbank.services.R
import za.co.nedbank.services.databinding.ActivityVvapEditPaymentDayBinding
import za.co.nedbank.services.di.ServicesDI
import za.co.nedbank.services.insurance.view.generic.common.other.model.InsuranceCodeDescriptionViewModel
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.ParamKeys.PARAM_IS_FUNERAL_FLOW
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.ParamKeys.PARAM_IS_PL_FLOW
import za.co.nedbank.services.insurance.view.other.helper.InsuranceHelperClass
import za.co.nedbank.services.insurance.view.other.utils.confirmAlert
import za.co.nedbank.uisdk.component.CompatButton
import za.co.nedbank.uisdk.component.CompatPicker
import javax.inject.Inject

class VVAPEditPaymentDayActivity : NBBaseActivity(), VVAPEditPaymentDayView,
    View.OnClickListener {
    private lateinit var mToolbar: Toolbar
    private lateinit var mMainView: LinearLayout
    private lateinit var mConsentView: LinearLayout
    private lateinit var mSaveButton: CompatButton
    private lateinit var mProgressBar: ProgressBar
    private lateinit var mDayPicker: CompatPicker
    private lateinit var mCbkConsent: AppCompatCheckBox
    private lateinit var mNifpCbkConsent: CheckedTextView

    private val binding get() = _binding
    private var _binding: ActivityVvapEditPaymentDayBinding? = null

    @Inject
    lateinit var mPresenter: VVAPEditPaymentDayPresenter

    @Inject
    lateinit var mHelperErrorClass: InsuranceHelperClass

    private var mPaymentDate: String? = null
    private var mSubProduct: String? = null
    private lateinit var mPolicyNumber: String
    private lateinit var mRiskSerialNumber: String
    private var mIsPaymentDayUpdated: Boolean = false
    private var mPaymentDayModel: InsuranceCodeDescriptionViewModel? = null
    private var isPolicyPl: Boolean = false
    private var isPolicyFuneral: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        ServicesDI.getActivityComponent(this).inject(this)
        super.onCreate(savedInstanceState)
        _binding = ActivityVvapEditPaymentDayBinding.inflate(layoutInflater)
        setContentView(binding!!.root)
        mPresenter.bind(this)
        initializeVariables()
    }

    private fun getIntentData() {
        if (intent != null) {
            mPolicyNumber =
                intent.getStringExtra(InsuranceConstants.ParamKeys.PARAM_VVAP_POLICY_NUMBER)!!
            mRiskSerialNumber =
                intent.getStringExtra(InsuranceConstants.ParamKeys.PARAM_VVAP_RISK_SERIAL_NUMBER)!!
            mPaymentDate =
                intent.getStringExtra(InsuranceConstants.ParamKeys.PARAM_EDIT_PAYMENT_DAY_DETAILS)
            mSubProduct =
                intent.getStringExtra(InsuranceConstants.ParamKeys.PARAM_VVAPS_SUB_PRODUCT_NAME)
            isPolicyPl = intent.getBooleanExtra(PARAM_IS_PL_FLOW, false)
            isPolicyFuneral = intent.getBooleanExtra(PARAM_IS_FUNERAL_FLOW, false)
        }
    }

    private fun initializeVariables() {

        //initializing all the variables
        mToolbar = binding!!.toolbar
        mMainView = binding!!.llMainView
        mConsentView = binding!!.consentContainer
        mSaveButton = binding!!.saveBtn
        mCbkConsent = binding!!.chkBoxConsent
        mNifpCbkConsent = binding!!.nifpChkBoxConsent
        mDayPicker = binding!!.dayPicker
        mProgressBar = binding!!.progressBarView

        // Initially disable checkbox - user must change fields first
        mCbkConsent.isEnabled = false
        mNifpCbkConsent.isEnabled = false
        mNifpCbkConsent.setOnClickListener(this)
        mCbkConsent.setOnClickListener(this)
        mSaveButton.setOnClickListener(this)
        mDayPicker.setOnClickListener(this)
        // set toolbar
        initToolbar(mToolbar, true, false)
        mToolbar.title = getString(R.string.vvap_payment_day_edit_tool_bar)

        // set click listeners
        mSaveButton.setOnClickListener(this)

        // get intent data
        getIntentData()

        //set previous day of month
        if (mPaymentDate?.isNotEmpty() == true) {
            val paymentDay = FormattingUtil.getDayOfMonthFromDate(
                mPaymentDate, FormattingUtil.DATE_FORMAT_YYYY_MM_DD
            )
            mDayPicker.setText(FormattingUtil.getDayOfMonthSuffix(paymentDay))

            // get payment day details
            mPresenter.fetchPaymentDayList(paymentDay.toString(), isPolicyFuneral)
        }

        updateUI()
    }

    private fun updateUI() {
        if (isPolicyFuneral) {
            ViewUtils.showViews(mConsentView)
            ViewUtils.hideViews(mCbkConsent)
            addMarginIfFuneral()

        } else {
            ViewUtils.showViews(mCbkConsent)
            ViewUtils.hideViews(mConsentView)
        }
    }

    private fun addMarginIfFuneral() {
        val r: Resources = resources
        val topPx: Int =
            TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 23f, r.displayMetrics).toInt()
        val startEndPx: Int =
            TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 20f, r.displayMetrics).toInt()
        val params: LinearLayout.LayoutParams = LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT
        )
        params.setMargins(startEndPx, topPx, startEndPx, 0)
        mSaveButton.layoutParams = params
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.dayPicker -> {
                mPresenter.navigateToDaySelection()
            }

            R.id.saveBtn -> {
                saveButtonAnalytics()
                if (mSaveButton.isEnabled && mPaymentDayModel != null && mPaymentDayModel!!.description?.isNotEmpty() == true) {
                    confirmationAsPerDate()
                }
            }

            R.id.chkBoxConsent -> {
                validateInputFields()
            }

            R.id.nifpChkBoxConsent -> {
                mNifpCbkConsent.isChecked = !mNifpCbkConsent.isChecked // Toggle the checked state
                validateInputFields()
            }
        }

    }

    private fun saveButtonAnalytics() {
        if (isPolicyPl) {
            mPresenter.sendEventWithProduct(
                EVENT_MANAGE_POLICY_SAVE_PAYMENT, mSubProduct,
                TrackingEvent.ANALYTICS.PL_COMBO_TITLE
            )
        } else {
            mPresenter.sendEventWithProduct(
                EVENT_MANAGE_POLICY_SAVE_PAYMENT,
                mSubProduct,
                TrackingEvent.ANALYTICS.VVAPS_TITLE
            )
        }
    }

    private fun confirmationAsPerDate() {
        if (mPaymentDayModel!!.code!!.toInt() < FormattingUtil.getCurrentDayOfMonth()) {
            confirmationDialog(getString(R.string.vvap_payment_day_next_month_txt))
        } else {
            confirmationDialog(getString(R.string.vvap_payment_day_current_month_txt))
        }
    }

    private fun confirmationDialog(dialogDescription: String) {
        confirmAlert(
            getString(R.string.vvap_payment_day_debit_order_txt),
            dialogDescription,
            getString(R.string.cancel_caps),
            getString(R.string.confirm_caps)
        ) {

            val productType =
                if (isPolicyPl) InsuranceConstants.InsuranceApiType.PERSONAL_LINES else InsuranceConstants.InsuranceApiType.VVAPS
            mPresenter.updatePaymentDayDetails(
                mPaymentDayModel!!.code!!, mPolicyNumber, mRiskSerialNumber, productType
            )
        }
    }


    override fun showProgressBar(isVisible: Boolean) {
        if (isVisible) {
            ViewUtils.showViews(mProgressBar)
            ViewUtils.hideViews(mMainView)
            window.setFlags(
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
            )
        } else {
            window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            ViewUtils.hideViews(mProgressBar)
            ViewUtils.showViews(mMainView)
        }
    }

    override fun setPaymentDay(paymentDay: InsuranceCodeDescriptionViewModel?) {
        this.mPaymentDayModel = paymentDay

        if (isPolicyFuneral) {
            val dayWithSuffix = paymentDay?.description?.toIntOrNull()?.let {
                FormattingUtil.getDayOfMonthSuffix(it)
            } ?: StringUtils.EMPTY_STRING

            mDayPicker.setText(dayWithSuffix)
        } else {
            mDayPicker.setText(paymentDay?.description ?: StringUtils.EMPTY_STRING)
        }

        mIsPaymentDayUpdated = true
        enableCheckboxIfFieldsChanged()
        validateInputFields()
    }

    override fun setNextButtonEnable(isEnabled: Boolean) {
        mSaveButton.isEnabled = isEnabled
    }

    override fun showSubmitError() {
        mPresenter.navigateToPaymentDayDetail(StringUtils.EMPTY_STRING)
    }

    override fun submitSuccess(paymentDay: String) {
        successAnalyticsTag()
        val paymentDate: String = if (paymentDay.toInt() < FormattingUtil.getCurrentDayOfMonth()) {
            FormattingUtil.getFormattedDateWithDateNextMonth(
                FormattingUtil.DATE_FORMAT_YYYY_MM_DD, paymentDay
            )
        } else {
            FormattingUtil.getFormattedDateWithDate(
                FormattingUtil.DATE_FORMAT_YYYY_MM_DD, paymentDay
            )
        }
        mPresenter.navigateToPaymentDayDetail(paymentDate)
    }

    private fun successAnalyticsTag() {
        if (isPolicyPl) {
            mPresenter.sendEventWithProduct(
                EVENT_MANAGE_POLICY_SUCCESS_PAYMENT,
                mSubProduct,
                TrackingEvent.ANALYTICS.PL_COMBO_TITLE
            )
        } else {
            mPresenter.sendEventWithProduct(
                EVENT_MANAGE_POLICY_SUCCESS_PAYMENT,
                mSubProduct,
                TrackingEvent.ANALYTICS.VVAPS_TITLE
            )
        }
    }

    private fun validateInputFields() {
        val isChecked = if (isPolicyFuneral) mNifpCbkConsent.isChecked else mCbkConsent.isChecked
        mPresenter.validateAllFields(
            mDayPicker, isChecked, mIsPaymentDayUpdated
        )
    }


    override fun showAPIError() {
        mHelperErrorClass.showOkayErrorScreen { mPresenter.moveToProductListScreen() }
    }

    /**
     * Enable checkbox only after user has made changes to any field
     */
    private fun enableCheckboxIfFieldsChanged() {
        if (!mCbkConsent.isEnabled && mIsPaymentDayUpdated) {
            mCbkConsent.isEnabled = true
            // Update visual styling when enabled
            mCbkConsent.setTextColor(ContextCompat.getColor(this, R.color.black_333333))
            mCbkConsent.buttonTintList = ContextCompat.getColorStateList(this, R.color.color_bbbbbb)

        }

        if (!mNifpCbkConsent.isEnabled && mIsPaymentDayUpdated && isPolicyFuneral) {
            mNifpCbkConsent.isEnabled = true
            mNifpCbkConsent.invalidate() // Refresh the view to reflect the changes
        }
    }
}