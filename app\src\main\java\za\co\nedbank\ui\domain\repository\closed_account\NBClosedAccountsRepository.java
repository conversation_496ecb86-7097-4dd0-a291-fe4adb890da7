package za.co.nedbank.ui.domain.repository.closed_account;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.ui.data.entity.closed_account.ClosedAccountResponseEntity;
import za.co.nedbank.ui.data.networking.AccountsAPI;

public class NBClosedAccountsRepository implements IClosedAccountsRepository {
    private final NetworkClient mNetworkClient;

    @Inject
    public NBClosedAccountsRepository(NetworkClient networkClient) {
        this.mNetworkClient = networkClient;
    }

    @Override
    public Observable<ClosedAccountResponseEntity> getClosedAccounts() {
        return mNetworkClient.create(AccountsAPI.class)
                .getClosedAccounts();
    }
}
