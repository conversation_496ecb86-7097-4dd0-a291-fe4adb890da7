package za.co.nedbank.ui.view.model;

import java.util.List;

import za.co.nedbank.ui.domain.model.pop.SharePopNotificationTypeData;

public class ShareProofOfPaymentRequestViewModel {
    private List<SharePopNotificationTypeData> sharePopNotificationTypeData;
    private String transactionKind;
    private String transactionDate;
    private boolean isFromRecentPayments;
    private boolean isSopFromPayDone;

    public List<SharePopNotificationTypeData> getSharePopNotificationTypeData() {
        return sharePopNotificationTypeData;
    }

    public void setSharePopNotificationTypeData(List<SharePopNotificationTypeData> sharePopNotificationTypeData) {
        this.sharePopNotificationTypeData = sharePopNotificationTypeData;
    }

    public String getTransactionKind() {
        return transactionKind;
    }

    public void setTransactionKind(String transactionKind) {
        this.transactionKind = transactionKind;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public boolean isFromRecentPayments() {
        return isFromRecentPayments;
    }

    public void setFromRecentPayments(boolean fromRecentPayments) {
        isFromRecentPayments = fromRecentPayments;
    }


    public boolean isSopFromPayDone() {
        return isSopFromPayDone;
    }

    public void setSopFromPayDone(boolean sopFromPayDone) {
        isSopFromPayDone = sopFromPayDone;
    }
}
