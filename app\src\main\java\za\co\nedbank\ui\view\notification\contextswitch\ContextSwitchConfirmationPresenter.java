package za.co.nedbank.ui.view.notification.contextswitch;


import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.Observable;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.CachableValue;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.CheckIfUserAdminUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.ProfileSwitchingWorkflowUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.core.view.mapper.fbnotifications.FBNotificationsInnerDetailsDataToViewModelMapper;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextFedarationDetailsViewModel;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;

public class ContextSwitchConfirmationPresenter extends NBBasePresenter<ContextSwitchConfirmationView> {

    private final ProfileSwitchingWorkflowUseCase mProfileSwitchingWorkflowUseCase;

    private final CheckIfUserAdminUseCase mCheckIfUserAdminUseCase;

    private final GetOverviewUseCase mGetOverviewUseCase;

    private final GetUserDetailUseCase mGetUserDetailUseCase;

    private final ApplicationStorage memoryApplicationStorage;

    private final NavigationRouter mNavigationRouter;

    private final FeatureSetController mFeatureSetController;

    private FBNotificationsViewModel mNotificationDetailsViewModel;

    private FBNotificationsInnerDetailsDataToViewModelMapper mFBNotificationsInnerDetailsDataToViewModelMapper;

    private boolean hasTransactableAccount;
    private String mClientType;
    private String mFicaStatus;

    @Inject
    public ContextSwitchConfirmationPresenter(final NavigationRouter navigationRouter, ProfileSwitchingWorkflowUseCase profileSwitchingWorkflowUseCase,
                                              CheckIfUserAdminUseCase checkIfUserAdminUseCase, @Named("memory") final ApplicationStorage memoryApplicationStorage,
                                              GetOverviewUseCase getOverviewUseCase, GetUserDetailUseCase getUserDetailUseCase, FeatureSetController featureSetController,
                                              FBNotificationsInnerDetailsDataToViewModelMapper fBNotificationsInnerDetailsDataToViewModelMapper) {
        this.mProfileSwitchingWorkflowUseCase = profileSwitchingWorkflowUseCase;
        this.mCheckIfUserAdminUseCase = checkIfUserAdminUseCase;
        this.mGetOverviewUseCase = getOverviewUseCase;
        this.mGetUserDetailUseCase = getUserDetailUseCase;
        this.memoryApplicationStorage = memoryApplicationStorage;
        this.mNavigationRouter = navigationRouter;
        this.mFeatureSetController = featureSetController;
        this.mFBNotificationsInnerDetailsDataToViewModelMapper = fBNotificationsInnerDetailsDataToViewModelMapper;

    }

    public void startContextSwitchingFlow(SwitchContextFedarationDetailsViewModel switchContextFedarationDetailsViewModel) {
        mProfileSwitchingWorkflowUseCase.execute(switchContextFedarationDetailsViewModel)
                .compose(bindToLifecycle())
                .subscribe(dto -> {
                    memoryApplicationStorage.clearValue(Constants.CLIENT_PREFERENCES_RESPONSE);
                    mCheckIfUserAdminUseCase.execute().subscribe(isAdminUser -> {
                        mNotificationDetailsViewModel = (FBNotificationsViewModel) memoryApplicationStorage.getObject(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL);
                        if (NotificationConstants.NOTIFICATION_TYPES.TRANSACTION.equals(mNotificationDetailsViewModel.getNotificationType())) {
                            handleTransactionNotificationFlow(mNotificationDetailsViewModel);
                            clearNotificationData();
                        } else {
                            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_CENTER));
                            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_MESSAGES));
                            NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_DETAILS).withParam(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL, mNotificationDetailsViewModel);
                            mNavigationRouter.navigateTo(targetScreen);
                        }
                    }, throwable->handleError());
                }, throwable->handleError());

    }

    private void handleError() {
        if(view!=null){
            view.close();
        }
    }

    private void handleTransactionNotificationFlow(FBNotificationsViewModel notificationDetailsViewModel) {
        List<FBNotificationsViewModel.ResponseOption> responseOptionList = notificationDetailsViewModel.getResponseOptions();
        if (responseOptionList != null && !responseOptionList.isEmpty() && responseOptionList.get(0).getAction() != null) {
            String selectedAction = responseOptionList.get(0).getAction();
            if (selectedAction.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.REPORT_FRAUD)) {
                handleReportFraudFlow(notificationDetailsViewModel);
            } else if (selectedAction.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_LIST)) {
                handleDebitOrderListFlow(notificationDetailsViewModel);
            }
        }

    }

    private void handleDebitOrderListFlow(FBNotificationsViewModel notificationDetailsViewModel) {
        FBTransactionNotificationsViewModel transactionNotificationsViewModel = getFBTransNotificationViewModel(notificationDetailsViewModel);
        if (transactionNotificationsViewModel.getMetaAccNumber() != null) {
            getUserDetailandHandleNavigation(transactionNotificationsViewModel.getMetaAccNumber());
        } else {
            handleNavigationErrorFlow();
        }
    }

    private void handleNavigationErrorFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_ERROR));
    }

    private void getUserDetailandHandleNavigation(String accountNo) {
        Observable<CachableValue<Overview>> overviewObservable = mGetOverviewUseCase.execute();
        Observable<UserDetailData> userDetailsObservable = mGetUserDetailUseCase.execute(false);
        if (null != overviewObservable && null != userDetailsObservable) {
            Observable.zip(overviewObservable, userDetailsObservable,
                    (overviewCachableValue, userDetailData) -> {
                        if (null != overviewCachableValue && null != userDetailData) {
                            Overview overviewValue = overviewCachableValue.get().clone();
                            hasTransactableAccount = checkForTransactableAccount(overviewValue);
                            mClientType = userDetailData.getClientType();
                            mFicaStatus = userDetailData.getFicaStatus();
                            return getSelectedAccountSummary(overviewValue, accountNo);
                        }
                        return null;
                    }).compose(bindToLifecycle())
                    .subscribe(accountSummary -> {
                                if (null != view && null != accountSummary) {
                                    navigateToAccountDetailsScreen(accountSummary, NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_LIST);
                                } else {
                                    handleNavigationErrorFlow();
                                }
                            },
                            error -> {
                                if (view != null)
                                    handleNavigationErrorFlow();
                            });
        }
    }

    private AccountSummary getSelectedAccountSummary(Overview overview, String accountNo) {
        AccountSummary accountSummary = null;
        for (AccountsOverview accountsOverview : overview.accountsOverviews) {
            for (AccountSummary summary : accountsOverview.accountSummaries) {
                if (summary.getNumber() != null && summary.getNumber().equalsIgnoreCase(accountNo)) {
                    accountSummary = summary;
                }
            }
        }
        return accountSummary;

    }

    private boolean checkForTransactableAccount(Overview overview) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.CREDIT_CARDS ||
                        accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.accountSummaries != null) {
                    return isNotDormantAccount(accountsOverview);
                }
            }
        }
        return false;
    }

    private boolean isNotDormantAccount(AccountsOverview accountsOverview) {
        for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
            if (!accountSummary.isDormantAccount()) {
                return true;
            }
        }
        return false;
    }

    private void navigateToAccountDetailsScreen(AccountSummary accountSummary, String target) {
        mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, canTransact())
                .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, mClientType)
                .withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate())
                .withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice())
                .withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit())
                .withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct())
                .withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit())
                .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, mFicaStatus)
                .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                .withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, target)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber())
        );
    }

    private boolean canTransact() {
        return hasTransactableAccount;
    }

    private void handleReportFraudFlow(FBNotificationsViewModel notificationDetailsViewModel) {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.REPORT_FRAUD)) {
            FBTransactionNotificationsViewModel fbTransactionNotificationsViewModel = getFBTransNotificationViewModel(notificationDetailsViewModel);
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MORE_REPORT_FRAUD)
                    .withParam(za.co.nedbank.core.Constants.PARAM_REPORT_SUSPICIOUS, fbTransactionNotificationsViewModel)
                    .withParam(NavigationTarget.IS_FROM_REPORT, true));
        } else {
            handleNavigationErrorFlow();
        }

    }

    protected FBTransactionNotificationsViewModel getFBTransNotificationViewModel(FBNotificationsViewModel fbNotificationsViewModel) {
        FBNotificationsData fbNotificationsData = mFBNotificationsInnerDetailsDataToViewModelMapper.transformBack(fbNotificationsViewModel);
        return mFBNotificationsInnerDetailsDataToViewModelMapper.transformTransactionViewModel(fbNotificationsData);
    }


    public void clearNotificationData() {
        memoryApplicationStorage.clearValue(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL);

    }
}
