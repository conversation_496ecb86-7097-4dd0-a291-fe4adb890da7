/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.money_request;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.VoidUseCase;
import za.co.nedbank.core.view.mapper.AccountsViewModelMapper;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.ui.domain.repository.money_request.PayMeAccountsRepository;

public class GetRequesterAccountsUseCase extends VoidUseCase<List<AccountViewModel>> {

    private final PayMeAccountsRepository mAccountsRepository;
    private final AccountsViewModelMapper mAccountsViewModelMapper;

    @Inject
    GetRequesterAccountsUseCase(final UseCaseComposer useCaseComposer, final PayMeAccountsRepository accountsRepository, AccountsViewModelMapper accountsViewModelMapper) {
        super(useCaseComposer);
        this.mAccountsRepository = accountsRepository;
        this.mAccountsViewModelMapper = accountsViewModelMapper;
    }

    @Override
    protected Observable<List<AccountViewModel>> createUseCaseObservable() {
        return mAccountsRepository.getAccounts().map(mAccountsViewModelMapper::mapAccountsViewModelList);
    }
}
