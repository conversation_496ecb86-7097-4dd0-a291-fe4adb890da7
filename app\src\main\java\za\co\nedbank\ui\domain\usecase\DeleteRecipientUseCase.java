/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.data.mapper.RecipientResponseEntityToDomainDataMapper;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.model.response.DeleteRecipientResponse;
import za.co.nedbank.core.domain.repository.IRecipientRepository;

/**
 * Created by priyadhingra on 28-07-2017.
 */
public class DeleteRecipientUseCase extends UseCase<Integer, DeleteRecipientResponse> {

    private final String TAG = DeleteRecipientUseCase.class.getSimpleName();
    private final IRecipientRepository mIRecipientRepository;
    private final RecipientResponseEntityToDomainDataMapper mDeleteRecipientResponseEntityToDomainDataMapper;

    @Inject
    protected DeleteRecipientUseCase(final UseCaseComposer useCaseComposer, final IRecipientRepository iRecipientRepository, RecipientResponseEntityToDomainDataMapper deleteRecipientResponseEntityToDomainDataMapper) {
        super(useCaseComposer);
        this.mIRecipientRepository = iRecipientRepository;
        this.mDeleteRecipientResponseEntityToDomainDataMapper = deleteRecipientResponseEntityToDomainDataMapper;
    }

    @Override
    protected Observable<DeleteRecipientResponse> createUseCaseObservable(Integer contactCardId) {
        return mIRecipientRepository.deleteRecipient(contactCardId).map(mDeleteRecipientResponseEntityToDomainDataMapper::mapDeleteRecipientResponseEntityToData);
    }
}
