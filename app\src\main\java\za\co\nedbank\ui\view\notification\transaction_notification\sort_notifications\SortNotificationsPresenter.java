package za.co.nedbank.ui.view.notification.transaction_notification.sort_notifications;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;

public class SortNotificationsPresenter extends NBBasePresenter<SortNotificationsView> {

    private final NavigationRouter mNavigationRouter;

    @Inject
    public SortNotificationsPresenter(NavigationRouter mNavigationRouter) {
        this.mNavigationRouter = mNavigationRouter;
    }


    @Override
    protected void onBind() {
        super.onBind();
    }

    @Override
    protected void onUnbind() {
        super.onUnbind();
    }
}
