package za.co.nedbank.ui.view.card_delivery.delivery_options;

import static za.co.nedbank.core.view.card_delivery.CardDeliveryOptionsEnum.LOCKER;

import android.annotation.SuppressLint;

import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.FicaProductFlow;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.card_delivery.PostCardDeliveryOptionRequestData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.card_delivery.PostCardDeliveryOptionUseCase;
import za.co.nedbank.core.domain.usecase.moa.PostAccountUseCase;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.card_delivery.CardDeliveryOptionsEnum;
import za.co.nedbank.core.view.mapper.moa.PostAccountRequestViewModelToDataMapper;
import za.co.nedbank.core.view.mapper.moa.PostAccountResponseDataToViewModelMapper;
import za.co.nedbank.core.view.model.moa.PostAccountRequestViewModel;
import za.co.nedbank.core.view.model.moa.PostAccountResponseViewModel;
import za.co.nedbank.enroll_v2.Constants;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.domain.models.card_delivery.CardDeliveryOptionsOuterDataModel;
import za.co.nedbank.enroll_v2.domain.usecases.card_delivery.GetCardDeliveryOptionsUseCase;
import za.co.nedbank.enroll_v2.tracking.MOAApiErrorAnalytics;
import za.co.nedbank.enroll_v2.tracking.MOATrackingEvent;
import za.co.nedbank.enroll_v2.view.fica.error.FicaErrorHandler;
import za.co.nedbank.enroll_v2.view.mapper.card_delivery.CardDeliveryOptionsDataToViewModelMapper;
import za.co.nedbank.enroll_v2.view.model.card_delivery.CardDeliveryOptionsOuterViewModel;
import za.co.nedbank.enroll_v2.view.model.card_delivery.CardDeliveryOptionsViewModel;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConstants;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryUtils;

public class CardDeliveryOptionsPresenter extends NBBasePresenter<CardDeliveryOptionsView> {

    private final ApplicationStorage applicationStorage;
    private final NavigationRouter navigationRouter;
    private final GetCardDeliveryOptionsUseCase getCardDeliveryOptionsUseCase;
    private final CardDeliveryOptionsDataToViewModelMapper cardDeliveryOptionsDataToViewModelMapper;
    private final FeatureSetController featureSetController;
    private final Analytics analytics;
    private final ErrorHandler errorHandler;
    private final FicaErrorHandler mFicaErrorHandler;

    protected final PostCardDeliveryOptionUseCase postCardDeliveryOptionUseCase;
    private final PostAccountUseCase mPostAccountUseCase;
    private final PostAccountResponseDataToViewModelMapper mPostAccountResponseDataToViewModelMapper;
    private final MOAApiErrorAnalytics mMOAApiErrorAnalytics;
    private final PostAccountRequestViewModelToDataMapper mPostAccountRequestViewModelToDataMapper;
    private boolean isFicaOnCardDelivery;
    private String accountNum;

    @Inject
    public CardDeliveryOptionsPresenter(NavigationRouter navigationRouter,
                                        @Named("memory") final ApplicationStorage applicationStorage,
                                        GetCardDeliveryOptionsUseCase getCardDeliveryOptionsUseCase,
                                        CardDeliveryOptionsDataToViewModelMapper cardDeliveryOptionsDataToViewModelMapper,
                                        FeatureSetController featureSetController,
                                        Analytics analytics,
                                        ErrorHandler errorHandler,
                                        FicaErrorHandler ficaErrorHandler,
                                        PostCardDeliveryOptionUseCase postCardDeliveryOptionUseCase,
                                        PostAccountUseCase mPostAccountUseCase,
                                        PostAccountResponseDataToViewModelMapper mPostAccountResponseDataToViewModelMapper,
                                        MOAApiErrorAnalytics mMOAApiErrorAnalytics,
                                        PostAccountRequestViewModelToDataMapper mPostAccountRequestViewModelToDataMapper) {
        this.applicationStorage = applicationStorage;
        this.navigationRouter = navigationRouter;
        this.getCardDeliveryOptionsUseCase = getCardDeliveryOptionsUseCase;
        this.cardDeliveryOptionsDataToViewModelMapper = cardDeliveryOptionsDataToViewModelMapper;
        this.featureSetController = featureSetController;
        this.analytics = analytics;
        this.errorHandler = errorHandler;
        this.postCardDeliveryOptionUseCase = postCardDeliveryOptionUseCase;
        this.mPostAccountUseCase = mPostAccountUseCase;
        this.mPostAccountResponseDataToViewModelMapper = mPostAccountResponseDataToViewModelMapper;
        this.mMOAApiErrorAnalytics = mMOAApiErrorAnalytics;
        this.mPostAccountRequestViewModelToDataMapper = mPostAccountRequestViewModelToDataMapper;
        this.mFicaErrorHandler = ficaErrorHandler;
    }

    void loadContent(String flow) {
        if (isMinorFlow()) {
            PostAccountResponseViewModel mPostAccountResponseViewModel = (PostAccountResponseViewModel) applicationStorage.getObject(Constants.ACCOUNT_RESPONSE);
            if (mPostAccountResponseViewModel != null) {
                accountNum = mPostAccountResponseViewModel.getAccountDataModel().getAccountNumber();
                isFicaOnCardDelivery = mPostAccountResponseViewModel.getAccountDataModel().isFicaOnCardDelivery();
            }
            loadDeliveryOptions(flow);
        }else if(isEfica(flow)){
            PostAccountRequestViewModel postAccountRequestViewModel = new PostAccountRequestViewModel();
            postAccountRequestViewModel.setSessionId(applicationStorage.getString(StorageKeys.FICA_SESSION_ID, StringUtils.EMPTY_STRING));

            mPostAccountUseCase.execute(mPostAccountRequestViewModelToDataMapper.mapData(postAccountRequestViewModel)).
                    compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> view.showProgress(true))
                    .map(mPostAccountResponseDataToViewModelMapper::mapData)
                    .subscribe(accountResponseViewModel -> {
                        if (accountResponseViewModel.getMetaDataViewModel().isSuccess()) {
                            accountNum = accountResponseViewModel.getAccountDataModel().getAccountNumber();
                            isFicaOnCardDelivery = accountResponseViewModel.getAccountDataModel().isFicaOnCardDelivery();
                            loadDeliveryOptions(flow);
                        } else {
                            mMOAApiErrorAnalytics.trackMoaApiErrorAnalytics(true, MOATrackingEvent.GET_ACCOUNT_NUMBER,
                                    ApiAliasConstants.USE_ACC,
                                    accountResponseViewModel.getMetaDataViewModel().getErrorMessage(),
                                    accountResponseViewModel.getMetaDataViewModel().getResultCode());
                            handleException(accountResponseViewModel.getMetaDataViewModel().getThrowable());
                        }
                    }, throwable -> {
                        handleException(throwable);
                        mMOAApiErrorAnalytics.trackMoaApiErrorAnalytics(false, MOATrackingEvent.GET_ACCOUNT_NUMBER,
                                ApiAliasConstants.USE_ACC,
                                errorHandler.getErrorMessage(throwable).getMessage(),
                                null);
                    });
        }else{
            loadDeliveryOptions(flow);
        }
    }

    @SuppressLint("CheckResult")
    public void loadDeliveryOptions(String flow) {
        boolean includeNoCardOption = false;
        if (!isCardOrderingFeatureEnabled(flow)) {
            sendErrorAnalytics(false, view.getUserError(), StringUtils.EMPTY_STRING);
            navigaToError(flow);
            return;
        }

        if (isEfica(flow)) {
            includeNoCardOption = true;
        }

        String sessionId = isEfica(flow) ? applicationStorage.getString(StorageKeys.FICA_SESSION_ID, StringUtils.EMPTY_STRING)
                : StringUtils.EMPTY_STRING;
        String[] queryRequest = {sessionId, flow};
        getCardDeliveryOptionsUseCase
                .execute(queryRequest, includeNoCardOption)
                .compose(bindToLifecycle())
                .doOnSubscribe(s -> view.showProgress(true))
                .doFinally(() -> view.showProgress(false))
                .subscribe(cardDeliveryOptionsOuterDataModel -> {
                    if (isCardDeliverOptionApiSuccess(cardDeliveryOptionsOuterDataModel)) {

                        CardDeliveryOptionsOuterViewModel cardOptionViewModel = cardDeliveryOptionsDataToViewModelMapper
                                .map(cardDeliveryOptionsOuterDataModel.getCardDeliveryOptionsData());
                        view.updateOptions(cardOptionViewModel.getCardDeliveryOptionsViewModels());
                    } else {
                        String errorCode = CardDeliveryUtils.extractResponseCode(cardDeliveryOptionsOuterDataModel.getMetaDataModel());
                        String message = CardDeliveryUtils.extractMessage(cardDeliveryOptionsOuterDataModel.getMetaDataModel());
                        sendErrorAnalytics(true, message, errorCode);
                        navigaToError(flow);
                    }
                }, throwable -> {
                    Error error = errorHandler.getErrorMessage(throwable);
                    sendErrorAnalytics(false, error.getMessage(), String.valueOf(error.getCode()));
                    navigaToError(flow);
                });
    }

    private boolean isCardDeliverOptionApiSuccess(CardDeliveryOptionsOuterDataModel cardDeliveryOptionsOuterDataModel) {
        return CardDeliveryUtils.isSuccess(cardDeliveryOptionsOuterDataModel.getMetaDataModel())
                && cardDeliveryOptionsOuterDataModel.getCardDeliveryOptionsData() != null
                && CollectionUtils.isNotEmpty(cardDeliveryOptionsOuterDataModel.getCardDeliveryOptionsData().getCardDeliveryOptionsData());
    }

    private void navigaToError(String flow) {
        view.hideContent();
        NavigationTarget target = NavigationTarget.to(NavigationTarget.CARD_DELIVERY_RESULT)
                .withAllData(true)
                .withParam(NavigationTarget.PARAM_CARD_DELIVERY_FLOW, flow);
        navigationRouter.navigateTo(target);
    }

    public void featureCheckForLocker(List<CardDeliveryOptionsViewModel> options) {
        if (CollectionUtils.isNotEmpty(options)) {
            for (CardDeliveryOptionsViewModel viewModel :
                    options) {
                if (LOCKER.getValue().equals(viewModel.getOptionCode())) {
                    viewModel.setEnabled(viewModel.isEnabled()
                            && isLockerFeatureEnabled(view.getFlow()));
                    break;
                }
            }
        }
    }

    public void navigateToAccountReadyScreen() {
        navigationRouter.navigateTo(
                NavigationTarget.to(EnrollV2NavigatorTarget.MOA_ACCOUNT_READY)
                        .withParam(NavigationTarget.PARAM_ACCOUNT_NUMBER, accountNum)
                        .withParam(NavigationTarget.PARAM_IS_FICA_ON_CARD_DELIVERY, isFicaOnCardDelivery)
        );
    }

    public boolean isLockerFeatureEnabled(String flow) {

        if (isEfica(flow)) {
            return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_LOCKER_ACC_OPEN);
        } else if (isReplaceCard(flow)) {
            return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_LOCKER_REPLACE_CARD);
        }
        return false;
    }


    public boolean isEfica(String flow) {
        return NavigationTarget.VALUE_CARD_DELIVERY_FLOW_EFICA.equals(flow);
    }

    public boolean isReplaceCard(String flow) {
        return NavigationTarget.VALUE_CARD_DELIVERY_FLOW_BLOCK_AND_REPLACE.equals(flow);
    }

    public boolean isCardOrderingFeatureEnabled(String flow) {

        if (isEfica(flow)) {
            return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_CARD_ORDERING_ACC_OPEN);
        } else if (isReplaceCard(flow)) {
            return !featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_CARD_ORDERING_REPLACE_CARD);
        }
        return false;
    }

    public void handleOptionClicked(CardDeliveryOptionsViewModel selectedOption) {
        switch (CardDeliveryOptionsEnum.fromValue(selectedOption.getOptionCode())) {
            case LOCKER:
                sendEventAnalytics(selectedOption.getOptionCode());
                navigateTo(NavigationTarget.LOCKER_MAP, selectedOption);
                break;
            case DELIVERY:
                sendEventAnalytics(selectedOption.getOptionCode());
                navigateTo(NavigationTarget.CARD_DELIVERY_DELIVER_TO_ME_CONFIRMATION, selectedOption);
                break;
            case BRANCH_PICK:
                sendEventAnalytics(selectedOption.getOptionCode());
                navigateTo(NavigationTarget.CARD_DELIVERY_BRANCH_CONFIRMATION, selectedOption);
                break;
            case NO_CARD_REQUIRED:
                handleFicaConfirmation();
                sendEventAnalytics(selectedOption.getOptionCode());
                break;
            case UNKNOWN:
                //unknow delivery method. Do nothing.
        }
    }

    public void navigateTo(String target, CardDeliveryOptionsViewModel selectedOption) {
        navigationRouter.navigateTo(NavigationTarget.to(target)
                .withAllData(true)
                .withParam(CardDeliveryConstants.EXTRA_SELECT_CARD_DELIVERY_OPTION, selectedOption.getOptionCode())
                .withParam(CardDeliveryConstants.EXTRA_SELECT_CARD_DELIVERY_OPTION_NAME, selectedOption.getOptionName()));
    }

    public void checkFeeApplicable(String flow) {
        if (view != null) {
            view.updateFeeInfo(isReplaceCard(flow) && view.isCardReplaceFeeApplicable(),
                    isCardDeliveryFreeProduct(flow, applicationStorage.getString(StorageKeys.MOA_SELECTED_PRODUCT_CODE, StringUtils.EMPTY_STRING)));
        }
    }

    private boolean isCardDeliveryFreeProduct(String flow,String productCode){
        return isEfica(flow) && (CardDeliveryConstants.SAVVY_PLUS_GOLD.equalsIgnoreCase(productCode) || CardDeliveryConstants.NEDBANK_PAY_AS_YOU_USE.equalsIgnoreCase(productCode));
    }

    public void sendPageAnalytics() {
        String pageCategory = StringUtils.EMPTY_STRING;
        String feature = StringUtils.EMPTY_STRING;
        String featureCategory = StringUtils.EMPTY_STRING;

        if (isEfica(view.getFlow())) {
            pageCategory = CardDeliveryAnalytics.VAL_DELIVERY_OPTIONS;
            feature = CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING;
            featureCategory = CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING;
        } else if (isReplaceCard(view.getFlow())) {
            pageCategory = CardDeliveryAnalytics.VAL_CARD_REPLACEMENT;
            feature = view.getCardActionName();
            featureCategory = CardDeliveryAnalytics.VAL_CARD_MAINTENANCE;

        }

        setupAnalytics();
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);
        contextData.setPageCategory(pageCategory);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductCategory(CardDeliveryAnalytics.VAL_CARDS);
        contextData.setProductAccount(getProductName());

        contextData.setFeatureCategory(featureCategory);
        contextData.setFeature(feature);
        analytics.sendEventStateWithMap(CardDeliveryAnalytics.VAL_DELIVERY_OPTIONS, contextData.getCdata());

    }

    private void setupAnalytics() {
        String selectedProductName = StringUtils.EMPTY_STRING;
        if (isEfica(view.getFlow())) {
            selectedProductName = getEficaSelectedProductName();
        } else if (isReplaceCard(view.getFlow())) {
            selectedProductName = view.getCardName();
        }
        applicationStorage.putString(StorageKeys.CARD_DELIVERY_SELECTED_PRODUCT, selectedProductName);
        applicationStorage.putLong(StorageKeys.CARD_DELIVERY_FEATURE_START_TIME, System.currentTimeMillis());
    }

    private void sendEventAnalytics(String option) {

        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductCategory(CardDeliveryAnalytics.VAL_CARDS);
        contextData.setProductAccount(getProductName());

        if (isEfica(view.getFlow())) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
        } else if (isReplaceCard(view.getFlow())) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            contextData.setFeature(view.getCardActionName());
        }

        contextData.setSubFeature(option);
        contextData.setInitiations();

        analytics.sendEventActionWithMap(CardDeliveryAnalytics.EVENT_CM_DELIVERY_METHOD, contextDataMap);
    }

    private void sendErrorAnalytics(boolean isApiFailure, String message, String apiErrorCode) {

        String tagName = StringUtils.EMPTY_STRING;
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductAccount(getProductName());

        if (isEfica(view.getFlow())) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
            tagName = CardDeliveryAnalytics.EVENT_CM_ORDER_CARD_FAILURE;
        } else if (isReplaceCard(view.getFlow())) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            contextData.setFeature(view.getCardActionName());
            tagName = CardDeliveryAnalytics.EVENT_CM_REPLACE_CARD_FAILURE;
        }

        analytics.trackFailure(isApiFailure, tagName,
                CardDeliveryAnalytics.API_CAR_DEL, message, apiErrorCode, contextDataMap);
    }

    private String getProductName() {
        return applicationStorage.getString(StorageKeys.CARD_DELIVERY_SELECTED_PRODUCT, StringUtils.EMPTY_STRING);
    }

    private String getEficaSelectedProductName() {
        return applicationStorage.getString(StorageKeys.FICA_SELECTED_PRODUCT, StringUtils.EMPTY_STRING);
    }

    protected void handleFicaConfirmation() {
        postCardDeliveryOptionUseCase
                .execute(createPostDeliveryOptionRequestEntity())
                .compose(bindToLifecycle())
                .doOnSubscribe(d -> view.showProgress(true))
                .doFinally(() -> view.showProgress(false))
                .subscribe(postCardDeliveryOptionsResponseData -> showEficaCardDeliverConfirmationResult());
    }

    public PostCardDeliveryOptionRequestData createPostDeliveryOptionRequestEntity() {
        PostCardDeliveryOptionRequestData requestData = new PostCardDeliveryOptionRequestData();
        requestData.setBranchCode(StringUtils.EMPTY_STRING);
        requestData.setDeliveryOptionCode(CardDeliveryOptionsEnum.NO_CARD_REQUIRED.getValue());
        String sessionId = applicationStorage.getString(StorageKeys.FICA_SESSION_ID, StringUtils.EMPTY_STRING);
        requestData.setSessionId(sessionId);
        return requestData;
    }

    private void showEficaCardDeliverConfirmationResult() {
        view.onSetBackResult();
    }

    private void handleException(Throwable throwable) {
        toggleProgress(false);
        mFicaErrorHandler.handleError(throwable);
    }

    private void toggleProgress(boolean isLoading) {
        if (view != null) {
            view.showProgress(isLoading);
        }
    }

    private boolean isMinorFlow() {
        return (applicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) ==
                FicaWorkFlow.IN_APP || applicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) ==
                FicaWorkFlow.PRE_LOGIN) &&
                applicationStorage.getInteger(StorageKeys.FICA_PRODUCT_FLOW, FicaProductFlow.DEFAULT) ==
                        FicaProductFlow.MINOR;
    }

}
