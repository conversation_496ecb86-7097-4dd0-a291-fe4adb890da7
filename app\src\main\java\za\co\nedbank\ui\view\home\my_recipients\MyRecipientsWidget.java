/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients;

import android.content.Context;
import android.os.Build;
import android.text.Selection;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import com.google.android.material.snackbar.BaseTransientBottomBar;
import com.jakewharton.rxbinding2.widget.RxTextView;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.domain.model.beneficiary.user.UserBeneficiaryData;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.payment.PaymentsUtility;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.sharedui.listener.IAlphabetsBarItemSelectionListener;
import za.co.nedbank.core.sharedui.listener.IViewPagerChildClickListener;
import za.co.nedbank.core.sharedui.ui.NBCardWidget;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.IFragmentToActivityComListener;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryDataViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryDetailsViewModel;
import za.co.nedbank.databinding.CardWidgetMyRecipientsBinding;
import za.co.nedbank.payment.common.domain.data.mapper.BeneficiaryTypeMapper;
import za.co.nedbank.payment.common.domain.data.mapper.BranchCodeToSortCodeMapper;
import za.co.nedbank.payment.common.domain.data.mapper.beneficiary.user.UserBeneficiaryMapper;
import za.co.nedbank.payment.pay.view.PayMode;
import za.co.nedbank.payment.pay.view.model.PaymentViewModel;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.my_recipients.choose_from_account.ChooseFromAccountDialog;
import za.co.nedbank.ui.view.home.my_recipients.choose_from_account.IChooseFromAccountDialogActivityInterface;
import za.co.nedbank.ui.view.home.my_recipients.choose_recipient.ChooseRecipientDialog;
import za.co.nedbank.ui.view.home.my_recipients.choose_recipient.ChooseRecipientsViewModel;
import za.co.nedbank.ui.view.home.my_recipients.choose_recipient.IChooseRecipientDialogActivityInterface;
import za.co.nedbank.uisdk.component.CompatCurrency;
import za.co.nedbank.uisdk.widget.NBSnackbar;


/**
 * Created by charurani on 17-08-2017.
 */

public class MyRecipientsWidget extends NBCardWidget implements IMyRecipientsView, IAlphabetsBarItemSelectionListener, IRecipientsViewPagerListener, IChooseFromAccountDialogActivityInterface, IViewPagerChildClickListener, IChooseRecipientDialogActivityInterface {

    @Inject
    MyRecipientsPresenter mPresenter;

    @Inject
    UserBeneficiaryMapper mUserBeneficiaryMapper;

    private MyRecipientsPagerAdapter mPagerAdapter;
    private List<UserBeneficiaryDataViewModel> mUserBeneficiaryDataViewModelList;
    private ChooseRecipientsViewModel mChooseRecipientsViewModel;
    private List<AccountViewModel> mPayAccounts;
    private double mLimitToBeCompared;
    private IFragmentToActivityComListener mIFragmentToActivityComListener;
    private String mDefaultAccountIdentifier;
    private int fromAccountSelectedPos = 0;
    private CardWidgetMyRecipientsBinding binding;

    public MyRecipientsWidget(final Context context) {
        this(context, null);
    }

    public MyRecipientsWidget(final Context context, @Nullable final AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MyRecipientsWidget(final Context context, @Nullable final AttributeSet attrs, final int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    void handleAddNewRecipientButtonClick() {
        if (mIFragmentToActivityComListener != null) {
            mIFragmentToActivityComListener.onEvent(za.co.nedbank.core.Constants.EVENT_CONSTANTS.EVENT_ADD_RECIPIENT_CLICKED, null);
        }
    }

    void handleAddRecipient() {
        if (mIFragmentToActivityComListener != null) {
            mIFragmentToActivityComListener.onEvent(za.co.nedbank.core.Constants.EVENT_CONSTANTS.EVENT_ADD_RECIPIENT_CLICKED, null);
        }
    }

    private void initView() {
        binding = CardWidgetMyRecipientsBinding.inflate(LayoutInflater.from(getContext()), this, true);

        if (getContext() instanceof NBBaseActivity) {
            AppDI.getActivityComponent((NBBaseActivity) getContext()).inject(this);
        } else {
            throw new ClassCastException("getContext() value must be a subclass of NBBaseActivity");
        }

        mPagerAdapter = new MyRecipientsPagerAdapter((getContext()), ((NBBaseActivity) getContext()).getSupportFragmentManager(), this, this);

        int pagePadding = getResources().getDimensionPixelSize(R.dimen.my_recipients_view_padding);
        binding.myRecipientsViewPager.setClipToPadding(false);
        binding.myRecipientsViewPager.setPadding(pagePadding, 0, pagePadding, 0);

        int pageMargin = getResources().getDimensionPixelSize(R.dimen.my_recipients_view_margin);
        binding.myRecipientsViewPager.setPageMargin(-pageMargin);

        binding.myRecipientsViewPager.addOnPageChangeListener(mPagerAdapter);
        binding.myRecipientsViewPager.setOffscreenPageLimit(5);
        binding.myRecipientsViewPager.setAdapter(mPagerAdapter);

        binding.sideBar.setListener(this);

        //hide views until data for recipients is received
        ViewUtils.hideViews(binding.sideBar);
        ViewUtils.hideViews(binding.recipientNameTv);
        ViewUtils.hideViews(binding.recipientBankOrMobileTv);
        ViewUtils.hideViews(binding.myRecipientTypesIv);

        binding.myRecipientCardPayButton.setText(getContext().getString(R.string.my_recipients_pay_button_text));
        //disable the pay button until the amount is entered
        setPayButtonEnabled(false);

        if (getContext() instanceof IFragmentToActivityComListener) {
            mIFragmentToActivityComListener = (IFragmentToActivityComListener) getContext();
        }
        fetchAccountDetails();
        addListenerForAmount(binding.myRecipientCardAmountEt);
        binding.myRecipientAddARecipientButton.setOnClickListener(v -> handleAddNewRecipientButtonClick());
        binding.ivAddRecipient.setOnClickListener(v -> handleAddRecipient());
        binding.tvUnableToLoad.setOnClickListener(v -> handleUnableToLoadTextviewClick());
        binding.myRecipientTypesLl.setOnClickListener(v -> onRecipientTypesImageViewClicked());
        binding.llFromAccountType.setOnClickListener(v -> onFromAccountTypeImageViewClicked());
        binding.myRecipientCardPayButton.setOnClickListener(v -> onPayClick());
        binding.myRecipientsErrorStateLl.setOnClickListener(v -> onFailedLoadRecipientLayoutClick());
    }

    private void fetchAccountDetails() {
        binding.tvUnableToLoad.setText(getContext().getString(R.string.loading_ellipse));
        mPresenter.fetchFromAccountDetails();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        mPresenter.bind(this);
        updateUserBeneficiaryData();
    }

    @Override
    public void updateUserBeneficiaryData() {
        ViewUtils.showViews(binding.progressMyRecipients);
        ViewUtils.setInvisibleAction(binding.myRecipientsViewPager);
        mPresenter.getUserBeneficiaryData(true);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mPresenter.unbind();
    }

    @Override
    public void setTitle(String title) {
        //override default implementation required

    }

    @Override
    public void selectedCharacter(int charSelected) {
        //set the page selection based on the character selected on the bar
        binding.myRecipientsViewPager.setCurrentItem(mPagerAdapter.getCurrentItem(charSelected));
    }

    @Override
    public void selectedPage(int pagePosition) {
        //set the character selection based on the page selected on viewpager
        binding.sideBar.setAlphabetSelected(mPagerAdapter.getSectionForPosition(pagePosition));
        if (pagePosition == 0 && mPagerAdapter.getSectionedListIndexSet() != null && mPagerAdapter.getSectionedListIndexSet().size() > 0) {
            Iterator<Integer> characterIterator = mPagerAdapter.getSectionedListIndexSet().iterator();
            if (characterIterator.hasNext()) {
                selectedCharacter(characterIterator.next());
            }
        }
        //update the details on the currentRecipient Card
        UserBeneficiaryDetailsViewModel userBeneficiaryDetailsVM = mUserBeneficiaryDataViewModelList.get(pagePosition).getBfDetails().get(0);
        String accountNumberOrMobileNumber = StringUtils.EMPTY_STRING;

        if (BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF.equalsIgnoreCase(userBeneficiaryDetailsVM.getBeneficiaryType())) {
            accountNumberOrMobileNumber = getContext().getString(R.string.bdf_type_beneficiary_second_line);
        } else {
            accountNumberOrMobileNumber = userBeneficiaryDetailsVM.getAccountNumber();
            String bankName = mUserBeneficiaryDataViewModelList.get(pagePosition).getBfDetails().get(0).getBankName();
            if (!TextUtils.isEmpty(bankName)) {
                accountNumberOrMobileNumber = (accountNumberOrMobileNumber + StringUtils.COMMA + StringUtils.SPACE + bankName);
            }
        }

        updateCurrentRecipientDetails(mUserBeneficiaryDataViewModelList.get(pagePosition).getContactCardName(), accountNumberOrMobileNumber);

        if (mUserBeneficiaryDataViewModelList.get(pagePosition).getBfDetails().size() > 1) {
            ViewUtils.showViews(binding.myRecipientTypesIv);
        } else {
            ViewUtils.hideViews(binding.myRecipientTypesIv);
        }
        if (pagePosition > 0) {
            mPresenter.handleRecipientSelected();
        }
    }

    @Override
    public void onScrolledPage() {
        this.mChooseRecipientsViewModel = null;
    }

    @Override
    public void showChooseRecipientDialog() {
        UserBeneficiaryDataViewModel userBeneficiaryDataViewModel = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem());
        if (userBeneficiaryDataViewModel.getBfDetails().size() > 1) {
            ChooseRecipientsViewModel chooseRecipientsViewModel = new ChooseRecipientsViewModel();
            chooseRecipientsViewModel.setCurrentBeneficiaryName(userBeneficiaryDataViewModel.getContactCardName());
            if (mChooseRecipientsViewModel != null && userBeneficiaryDataViewModel.getContactCardName().equalsIgnoreCase(mChooseRecipientsViewModel.getCurrentBeneficiaryName())) {
                chooseRecipientsViewModel.setCurrentBeneficiarySelection(mChooseRecipientsViewModel.getCurrentBeneficiarySelection());
            } else {
                chooseRecipientsViewModel.setCurrentBeneficiarySelection(0);
            }
            chooseRecipientsViewModel.setBfDetails(userBeneficiaryDataViewModel.getBfDetails());
            DialogFragment dialogFragment = ((DialogFragment) ChooseRecipientDialog.getInstance(this.getContext(), chooseRecipientsViewModel));
            ((ChooseRecipientDialog) dialogFragment).setDialogActivityInterface(this);
            dialogFragment.show(((NBBaseActivity) getContext()).getSupportFragmentManager(), ChooseRecipientDialog.TAG);
        }
    }

    @Override
    public void showFromAccountTypeDialog() {
        if (mPayAccounts != null && mPayAccounts.size() > 0) {
            DialogFragment dialogFragment = ((DialogFragment) ChooseFromAccountDialog.getInstance(this.getContext(), (ArrayList<AccountViewModel>) mPayAccounts, fromAccountSelectedPos));
            ((ChooseFromAccountDialog) dialogFragment).setDialogActivityInterface(this);
            dialogFragment.show(((NBBaseActivity) getContext()).getSupportFragmentManager(), ChooseFromAccountDialog.TAG);
        }
    }

    void handleUnableToLoadTextviewClick() {
        fetchAccountDetails();
    }

    @Override
    public void showError(String error, @IMyRecipientsViewAPIErrorType int apiErrorType) {
         switch (apiErrorType) {
            case IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API:
                ((NBBaseActivity) getContext()).showError(getContext().getString(za.co.nedbank.core.R.string.snackbar_header_default), error,
                        getContext().getString(R.string.snackbar_action_retry), BaseTransientBottomBar.LENGTH_INDEFINITE, this::updateUserBeneficiaryData);
                break;
            case IMyRecipientsViewAPIErrorType.ERROR_IN_LIMITS_API:
                ((NBBaseActivity) getContext()).showError(getContext().getString(za.co.nedbank.core.R.string.snackbar_header_default), error,
                        getContext().getString(R.string.snackbar_dismiss), BaseTransientBottomBar.LENGTH_INDEFINITE, () -> mPresenter.handleDismissSnackBarActionClick(apiErrorType),
                        () -> mPresenter.handleDismissSnackBarActionClick(apiErrorType));
                break;
            case IMyRecipientsViewAPIErrorType.ERROR_IN_ACCOUNTS_API:
            case IMyRecipientsViewAPIErrorType.ERROR_IN_DEFAULT_ACCOUNTS_API:
                ViewUtils.setInvisibleAction(binding.tvFromAccountNumber);
                binding.tvUnableToLoad.setText(getContext().getString(R.string.unable_to_load));
                ViewUtils.showViews(binding.tvUnableToLoad);
                NBSnackbar.instance().action(getContext().getString(R.string.snackbar_action_retry), this::fetchAccountDetails).build(binding.getRoot(), error);
                break;
            case IMyRecipientsViewAPIErrorType.ERROR_IN_INTERNATIONAL_FLOW:
                ((NBBaseActivity) getContext()).showError(getContext().getString(R.string.error), getContext().getString(R.string.something_went_wrong),
                        getContext().getString(R.string.snackbar_action_ok), BaseTransientBottomBar.LENGTH_INDEFINITE, () -> NBSnackbar.instance().dismissSnackBar());

                break;
        }
    }

    @Override
    public void receiveUserBeneficiaryBeans(List<UserBeneficiaryData> userBeneficiaryDataList) {
        ViewUtils.showViews(binding.myRecipientsViewPager);
        if (userBeneficiaryDataList != null && userBeneficiaryDataList.size() > 0) {
            setVisibilityOnEmptyStateView(false);
            mUserBeneficiaryDataViewModelList = new ArrayList<>();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                mUserBeneficiaryDataViewModelList.addAll(userBeneficiaryDataList.stream().
                        map(userBeneficiaryData -> mUserBeneficiaryMapper.mapUserBeneficiaryDataToViewModel(userBeneficiaryData)).collect(Collectors.toList()));
            } else {
                for (UserBeneficiaryData userBeneficiaryData : userBeneficiaryDataList) {
                    mUserBeneficiaryDataViewModelList.add(mUserBeneficiaryMapper.mapUserBeneficiaryDataToViewModel(userBeneficiaryData));
                }
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                mUserBeneficiaryDataViewModelList.sort((current, next) -> {
                    String currentName = current.getContactCardName();
                    String nextName = next.getContactCardName();
                    return currentName.compareToIgnoreCase(nextName);
                });
            } else {
                Collections.sort(mUserBeneficiaryDataViewModelList, (current, next) -> {
                    String currentName = current.getContactCardName();
                    String nextName = next.getContactCardName();
                    return currentName.compareToIgnoreCase(nextName);
                });
            }

            mPagerAdapter.setList(mUserBeneficiaryDataViewModelList);
            mPagerAdapter.notifyDataSetChanged();

            ViewUtils.showViews(binding.sideBar);
            binding.sideBar.setEnabledCharacters(mPagerAdapter.buildEnabledItemsList());

            ViewUtils.showViews(binding.recipientNameTv);
            ViewUtils.showViews(binding.recipientBankOrMobileTv);
            ViewUtils.showViews(binding.myRecipientTypesIv);

            //notify the alphabet bar to highlight the first item according to the list
            selectedPage(0);

            //set pay button enabled
            if (!TextUtils.isEmpty(binding.myRecipientCardAmountEt.getValue())) {
                mPresenter.checkInputsOnScreen(binding.myRecipientCardAmountEt, binding.tvFromAccountNumber.getText().toString());
            }
        } else {
            setVisibilityOnEmptyStateView(true);
        }
    }

    @Override
    public void receiveAccounts(List<AccountViewModel> payAccounts) {
        this.mPayAccounts = payAccounts;
    }

    @Override
    public PaymentsViewModel buildPaymentsViewModel(AccountViewModel fromAccountViewModel) {
        if (fromAccountViewModel == null) {
            return null;
        }
        PaymentsViewModel paymentsViewModel = new PaymentsViewModel();

        PaymentViewModel paymentViewModel = new PaymentViewModel();
        UserBeneficiaryDetailsViewModel userBeneficiaryDetailsViewModel;
        if (mChooseRecipientsViewModel == null) {
            userBeneficiaryDetailsViewModel = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getBfDetails().get(0);
        } else {
            userBeneficiaryDetailsViewModel = this.mChooseRecipientsViewModel.getBfDetails().get(this.mChooseRecipientsViewModel.getCurrentBeneficiarySelection());
        }
        if (userBeneficiaryDetailsViewModel != null) {

            paymentViewModel.setBeneficiaryID(userBeneficiaryDetailsViewModel.getBeneficiaryID());
            paymentViewModel.setSortCode(new BranchCodeToSortCodeMapper()
                    .map(userBeneficiaryDetailsViewModel, userBeneficiaryDetailsViewModel.getBranchCode()));
            paymentViewModel.setBeneficiaryName(userBeneficiaryDetailsViewModel.getBeneficiaryName());
            paymentViewModel.setPayMode(getPayModeByUserBeneficiary(userBeneficiaryDetailsViewModel));
            paymentViewModel.setInstantPayAllowed(userBeneficiaryDetailsViewModel.isInstantPayment());
            paymentViewModel.setRPPPayAllowed(userBeneficiaryDetailsViewModel.isRPPPayment());
            paymentViewModel.setBeneficiaryType(new BeneficiaryTypeMapper()
                    .map(userBeneficiaryDetailsViewModel, userBeneficiaryDetailsViewModel.getBeneficiaryType()));
            paymentViewModel.setPickedRecipient(true);
            String userReference = userBeneficiaryDetailsViewModel.getMyReference() != null && !TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getMyReference())
                    ? userBeneficiaryDetailsViewModel.getMyReference()
                    : getContext().getString(R.string.pay_toolbar_title) + (TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getBeneficiaryName()) ? StringUtils.EMPTY_STRING : StringUtils.SPACE + userBeneficiaryDetailsViewModel.getBeneficiaryName());
            paymentViewModel.setUserReference(userReference);
            String beneficiaryReference = userBeneficiaryDetailsViewModel.getBeneficiaryReference();
            beneficiaryReference = beneficiaryReference != null && !TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getBeneficiaryReference())
                    ? beneficiaryReference : userBeneficiaryDetailsViewModel.getBeneficiaryName();
            paymentViewModel.setBeneficiaryReference(beneficiaryReference);
            paymentViewModel.setInstantPayment(false);
            paymentViewModel.setStartDate(Calendar.getInstance().getTimeInMillis());
            if (!TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getBankName())) {
                paymentViewModel.setBankName(userBeneficiaryDetailsViewModel.getBankName());
            }
            AccountViewModel accountViewModel = new AccountViewModel();
            accountViewModel.setAccountNumber(userBeneficiaryDetailsViewModel.getAccountNumber());
            accountViewModel.setAccountType(userBeneficiaryDetailsViewModel.getAccountType());
            paymentViewModel.setToAccountViewModel(accountViewModel);


            try {
                paymentViewModel.setAmount(Double.parseDouble(FormattingUtil.convertCurrencyToAmount(binding.myRecipientCardAmountEt.getValue())));
            } catch (NumberFormatException exception) {
                paymentViewModel.setAmount(0);
            }

            //set from account based on the calculations
            paymentViewModel.setFromAccountViewModel(fromAccountViewModel);

            if (mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getNotificationDetails().size() > 0) {
                paymentViewModel.setBeneficiaryNotificationList(mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getNotificationDetails());
            }

            //set validations
            if (!TextUtils.isEmpty(binding.myRecipientCardAmountEt.getValue())) {
                if (!mPresenter.isAmountWithinLimits(binding.myRecipientCardAmountEt, mLimitToBeCompared)) {
                    paymentsViewModel.setQuickPayValidationError(true);
                    paymentsViewModel.setQuickPayValidationErrorTypes(Constants.QUICK_PAY_VALIDATION_ERROR.LIMIT_VALIDATION);
                } else if (!mPresenter.isAmountLessThanAccountBalance(binding.myRecipientCardAmountEt, fromAccountViewModel.getAvailableBalance(), fromAccountViewModel.isViewAvailBal())) {
                    paymentsViewModel.setQuickPayValidationError(true);
                    paymentsViewModel.setQuickPayValidationErrorTypes(Constants.QUICK_PAY_VALIDATION_ERROR.BALANCE_VALIDATION);
                } else if (!mPresenter.isAmountValid(binding.myRecipientCardAmountEt, isMobileNumberFlow())) {
                    paymentsViewModel.setQuickPayValidationError(true);
                    paymentsViewModel.setQuickPayValidationErrorTypes(Constants.QUICK_PAY_VALIDATION_ERROR.INVALID_AMOUNT_VALIDATION);
                }
            }
        }

        paymentsViewModel.getPaymentViewModelList().add(paymentViewModel);
        return paymentsViewModel;
    }

    @Override
    public AccountViewModel provideDefaultFromAccount() {
        final AccountViewModel[] accountViewModels = new AccountViewModel[1];
        if (!TextUtils.isEmpty(mDefaultAccountIdentifier)) {
            Observable.just(mPayAccounts)
                    .flatMapIterable(accountViewModels1 -> accountViewModels1)
                    .filter(accountViewModel -> accountViewModel != null
                            && accountViewModel.getItemAccountId() != null
                            && mDefaultAccountIdentifier !=null
                            && accountViewModel.getItemAccountId().equalsIgnoreCase(mDefaultAccountIdentifier)
                            && PaymentsUtility.isValidFromAccount(accountViewModel)
                            && accountViewModel.getAccountRuleViewModel() != null
                            && accountViewModel.getAccountRuleViewModel().isOnceOffPayFrom())
                    .toList()
                    .subscribe(payAccounts -> {
                        if (payAccounts != null && payAccounts.size() > 0) {
                            accountViewModels[0] = payAccounts.get(0);
                        }
                    }, throwable -> {
                        //need not do anything, respective error handled by observables
                    });
        }
        if (accountViewModels[0] == null) {
            Observable.just(mPayAccounts)
                    .flatMapIterable(mPayAccounts -> mPayAccounts)
                    .filter(accountViewModel -> PaymentsUtility.isValidFromAccount(accountViewModel)
                            && accountViewModel.getAccountRuleViewModel() != null && accountViewModel.getAccountRuleViewModel().isOnceOffPayFrom())
                    .toList()
                    .subscribe(payAccounts -> accountViewModels[0] = Collections.max(payAccounts), throwable -> {
                    });
        }
        return accountViewModels[0];
    }

    @Override
    public void handleEnterAccountNumber() {
        if (mUserBeneficiaryDataViewModelList != null && mUserBeneficiaryDataViewModelList.size() > 0) {
            mPresenter.checkInputsOnScreen(binding.myRecipientCardAmountEt, binding.tvFromAccountNumber.getText().toString());
        }
    }

    @Override
    public void setPayButtonEnabled(boolean isEnabled) {
        binding.myRecipientCardPayButton.setEnabled(isEnabled);
    }

    @Override
    public void setActivityTouchEnabled(boolean isEnabled) {
        if (isEnabled) {
            ((NBBaseActivity) getContext()).getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        } else {
            ((NBBaseActivity) getContext()).getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        }
    }

    @Override
    public void showLoadingOnButton(boolean inProgress) {
        binding.myRecipientCardPayButton.setLoadingVisible(inProgress);
    }
    @Override
    public void hideKeyBoard() {
        ViewUtils.hideSoftKeyboard((getContext()), binding.myRecipientCardAmountEt);
    }

    @Override
    public void setLimitToBeCompared(double limit) {
        this.mLimitToBeCompared = limit;
    }

    @Override
    public void setVisibilityOnEmptyStateView(boolean isVisible) {
        if (isVisible) {
            ViewUtils.showViews(binding.myRecipientsEmptyStateLl);
            ViewUtils.hideViews(binding.myRecipientDetailsLl);
        } else {
            ViewUtils.hideViews(binding.myRecipientsEmptyStateLl);
            ViewUtils.showViews(binding.myRecipientDetailsLl);
        }
        ViewUtils.hideViews(binding.progressMyRecipients);
    }

    @Override
    public void showAmountValidationError() {
        // implementation not required

    }

    @Override
    public String provideNoAccountString() {
        return getContext().getString(R.string.empty_pay_accounts_list);
    }

    @Override
    public void dismissSnackBar() {
        NBSnackbar.instance().dismissSnackBar();
    }

    @Override
    public void setVisibilityOnRecipientLoadLayout(boolean isVisible) {
        if (isVisible) {
            ViewUtils.showViews(binding.myRecipientsErrorStateLl);
            ViewUtils.setInvisibleAction(binding.myRecipientDetailsInnerLl);
        } else {
            ViewUtils.hideViews(binding.myRecipientsErrorStateLl);
            ViewUtils.showViews(binding.myRecipientDetailsInnerLl);
        }
        ViewUtils.hideViews(binding.progressMyRecipients);
    }

    @Override
    public void receiveDefaultAccountIdentifierValue(String value) {
        mDefaultAccountIdentifier = value;
        AccountViewModel accountViewModel = provideDefaultFromAccount();
        if (accountViewModel != null) {
            fromAccountSelectedPos = accountViewModel.getPosition();
            binding.tvFromAccountNumber.setText(accountViewModel.getDisplayAccountName());
            ViewUtils.setInvisibleAction(binding.tvUnableToLoad);
        }
    }

    @Override
    public void updateEditData(PaymentsViewModel paymentViewModel) {
        // implementation not required
    }

    @Override
    public AccountViewModel getSelectedFromAccountViewModel() {
        if (mPayAccounts != null && fromAccountSelectedPos < mPayAccounts.size()) {
            return mPayAccounts.get(fromAccountSelectedPos);
        }
        return null;
    }

    @Override
    public boolean isFeatureDisabled(String feature) {
        return false;
    }

    @Override
    public String fetchFromAccountName() {
        return null;
    }

    @Override
    public String fetchToAccountName() {
        return null;
    }

    @Override
    public String getBeneficiaryType() {
        return null;
    }

    @Override
    public void showPopForInterNationalPayment(String bankName) {
        // Empty body
    }

    public void onRecipientTypesImageViewClicked() {
        mPresenter.handleRecipientsTypeClick();
    }

    public void onFromAccountTypeImageViewClicked() {
        mPresenter.handleFromAccountTypeClick();
    }

    public void onPayClick() {
        if(binding.myRecipientCardAmountEt.hasFocus()) {
            binding.myRecipientCardAmountEt.clearFocus();
        }
        if (getSelectedFromAccountViewModel() != null) {
            mPresenter.handlePayButtonClick(getUserBeneficiaryDetailsViewModel().getBeneficiaryType());
        }
    }

    private void addListenerForAmount(CompatCurrency compatEdtAmount) {
        RxTextView.textChanges(compatEdtAmount.getInputField()).subscribe(chars -> {
            if (compatEdtAmount.hasError()) {
                compatEdtAmount.clearErrors();
            }
            mPresenter.handleAmountTextEnter();
        },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    public void onFailedLoadRecipientLayoutClick() {
        mPresenter.handleFailedLoadRecipientLayoutClick();
    }

    @Override
    public void onDialogDismiss(String tag) {
        // implementation not required
    }

    @Override
    public void receiveRecipientData(ChooseRecipientsViewModel chooseRecipientsViewModel) {
        this.mChooseRecipientsViewModel = chooseRecipientsViewModel;
        //update the details on the currentRecipient Card
        String accountOrMobileNumber = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getBfDetails().get(chooseRecipientsViewModel.getCurrentBeneficiarySelection()).getAccountNumber();
        String bankName = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getBfDetails().get(chooseRecipientsViewModel.getCurrentBeneficiarySelection()).getBankName();
        if (!TextUtils.isEmpty(bankName)) {
            accountOrMobileNumber = (accountOrMobileNumber + StringUtils.COMMA + StringUtils.SPACE + bankName);
        }
        updateCurrentRecipientDetails(mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getContactCardName(), accountOrMobileNumber);
    }

    private void updateCurrentRecipientDetails(String recipientName, String accountOrMobileNumber) {
        binding.recipientNameTv.setText(recipientName);
        binding.recipientBankOrMobileTv.setText(accountOrMobileNumber);
    }

    public void receiveUpdatedPaymentBundle(PaymentsViewModel paymentsViewModel) {
        if (paymentsViewModel != null) {
            if (paymentsViewModel.getPaymentViewModelList() != null && paymentsViewModel.getPaymentViewModelList().size() > 0) {
                PaymentViewModel paymentViewModel = paymentsViewModel.getPaymentViewModelList().get(0);
                if (paymentViewModel != null) {
                    Float amount = FormattingUtil.convertFromSouthAfricaFormattedCurrency(String.valueOf(paymentViewModel.getAmount()));
                    if (amount != null) {
                        binding.myRecipientCardAmountEt.getInputField().getText().clear();
                        binding.myRecipientCardAmountEt.setText(BigDecimal.valueOf(amount).stripTrailingZeros().toPlainString());
                    }
                    Selection.setSelection(binding.myRecipientCardAmountEt.getInputField().getText(), binding.myRecipientCardAmountEt.getInputField().getText().length());
                }
            }
        } else {
            binding.myRecipientCardAmountEt.getInputField().getText().clear();
        }
    }

    private UserBeneficiaryDetailsViewModel getUserBeneficiaryDetailsViewModel() {
        UserBeneficiaryDetailsViewModel userBeneficiaryDetailsViewModel;
        if (mChooseRecipientsViewModel == null) {
            userBeneficiaryDetailsViewModel = mUserBeneficiaryDataViewModelList.get(binding.myRecipientsViewPager.getCurrentItem()).getBfDetails().get(0);
        } else {
            userBeneficiaryDetailsViewModel = this.mChooseRecipientsViewModel.getBfDetails().get(this.mChooseRecipientsViewModel.getCurrentBeneficiarySelection());
        }
        return userBeneficiaryDetailsViewModel;
    }

    private PayMode getPayModeByUserBeneficiary(UserBeneficiaryDetailsViewModel userBeneficiaryDetailsViewModel) {
        if (!TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getBeneficiaryType()) && userBeneficiaryDetailsViewModel.getBeneficiaryType().equalsIgnoreCase(BeneficiaryConstants.BENEFICIARY_TYPE_PREPAID)) {
            return PayMode.MOBILE;
        } else if (!TextUtils.isEmpty(userBeneficiaryDetailsViewModel.getAccountType()) && userBeneficiaryDetailsViewModel.getAccountType().equalsIgnoreCase(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CC.getAccountTypeCode())) {
            return PayMode.CREDIT_CARD;
        } else {
            return PayMode.ACCOUNT;
        }
    }

    private boolean isMobileNumberFlow() {
        return getPayModeByUserBeneficiary(getUserBeneficiaryDetailsViewModel()) == PayMode.MOBILE;
    }

    @Override
    public void onItemClick(Integer... params) {
        binding.myRecipientsViewPager.setCurrentItem(params[0]);
    }

    @Override
    public void onItemSelected(int pos) {
        if (mPayAccounts != null && mPayAccounts.size() > pos) {
            fromAccountSelectedPos = pos;
            binding.tvFromAccountNumber.setText(mPayAccounts.get(pos).getDisplayAccountName());
        }
    }
}
