package za.co.nedbank.ui.view.pop;

import android.util.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.EmailValidator;
import za.co.nedbank.core.validation.InternationalNotificationMobileNumberValidator;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.validation.ValidationResult;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.payment.common.view.tracking.PaymentsTrackingEvent;
import za.co.nedbank.ui.domain.model.pop.SharePOPResponseData;
import za.co.nedbank.ui.domain.model.pop.SharePopNotificationTypeData;
import za.co.nedbank.ui.domain.model.pop.ShareProofOfPaymentRequestData;
import za.co.nedbank.ui.domain.usecase.pop.ShareProofOfPaymentUseCase;
import za.co.nedbank.ui.view.mapper.SharePOPDataToSharePOPViewModelMapper;
import za.co.nedbank.ui.view.mapper.ShareProofOfPaymentViewModelToDataMapper;
import za.co.nedbank.ui.view.model.ShareProofOfPaymentRequestViewModel;
import za.co.nedbank.ui.view.model.ShareProofOfPaymentResponseViewModel;
import za.co.nedbank.ui.view.tracking.AppTracking;
import za.co.nedbank.uisdk.component.CompatEditText;

public class NewShareProofOfPaymentPresenter extends NBBasePresenter<NewShareProofOfPaymentView> {

    private final NavigationRouter mNavigationRouter;
    private final Analytics mAnalytics;
    private final ShareProofOfPaymentViewModelToDataMapper shareProofOfPaymentViewModelToDataMapper;
    private final SharePOPDataToSharePOPViewModelMapper sharePOPDataToSharePOPViewModelMapper;
    private final InternationalNotificationMobileNumberValidator mInternationalNotificationMobileNumberValidator;
    private NonEmptyTextValidator mNonEmptyTextValidator;
    private final EmailValidator mEmailValidator;
    private ShareProofOfPaymentUseCase shareProofOfPaymentUseCase;

    @Inject
    NewShareProofOfPaymentPresenter(final NavigationRouter navigationRouter, final NonEmptyTextValidator mNonEmptyTextValidator, final EmailValidator emailValidator, final ShareProofOfPaymentUseCase shareProofOfPaymentUseCase, final Analytics analytics, ShareProofOfPaymentViewModelToDataMapper shareProofOfPaymentViewModelToDataMapper, SharePOPDataToSharePOPViewModelMapper sharePOPDataToSharePOPViewModelMapper,
                                    final InternationalNotificationMobileNumberValidator internationalNotificationMobileNumberValidator) {
        this.mNavigationRouter = navigationRouter;
        this.mNonEmptyTextValidator = mNonEmptyTextValidator;
        mEmailValidator = emailValidator;
        this.shareProofOfPaymentUseCase = shareProofOfPaymentUseCase;
        mAnalytics = analytics;
        this.shareProofOfPaymentViewModelToDataMapper = shareProofOfPaymentViewModelToDataMapper;
        this.sharePOPDataToSharePOPViewModelMapper = sharePOPDataToSharePOPViewModelMapper;
        this.mInternationalNotificationMobileNumberValidator = internationalNotificationMobileNumberValidator;
    }

    void navigateToSharePOPMethodSelectionScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.NEW_SHARE_PROOF_OF_PAYMENT_METHOD);
        mNavigationRouter.navigateWithResult(navigationTarget).subscribe(
                navigationResult -> {
                    if (view != null) {
                        view.setResult(navigationResult.getParams());
                    }
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
        );
    }

    void navigateToSharePOPSuccessScreen(boolean isSopFromPayDone,boolean isFromRecentPaymentFlow, boolean isBeyond90daysTransaction, boolean isLandingOnOverView, String notificationsType, String transactionId) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setNotificationType(notificationsType);
        adobeContextData.setDocumentType(AppTracking.VAL_DOCUMENT_TYPE_SHARE_POP);
        adobeContextData.setTimeFrame(isBeyond90daysTransaction ? AppTracking.VAL_TIME_FRAME_SHARE_POP_BEYOND_90 : AppTracking.VAL_TIME_FRAME_SHARE_POP_WITHIN_90);
        adobeContextData.setApplicationReference(transactionId);
        mAnalytics.sendEventActionWithMap(isBeyond90daysTransaction ? AppTracking.SHARE_POP_BEYOND90DAYS_SUCCESS : AppTracking.SHARE_POP_WITHIN90DAYS_SUCCESS, cdata);

        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.SHARE_PROOF_OF_PAYMENT_API_RESPONSE);
        navigationTarget.withParam(za.co.nedbank.core.Constants.EXTRAS.SCREEN_TYPE, za.co.nedbank.core.Constants.API_SUCCESS);
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_RECENT_PAYMENT_FLOW, isFromRecentPaymentFlow);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_SOP_FROM_PAY_DONE, isSopFromPayDone);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_LANDING_ON_OVERVIEW, isLandingOnOverView);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_FROM_RECIPIENT_HISTORY, view.isFromRecipientHistory());
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void navigateToSharePOPFailureScreen(boolean isSopFromPayDone,boolean isFromRecentPaymentFlow, boolean isLandingOnOverView) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.SHARE_PROOF_OF_PAYMENT_API_RESPONSE);
        navigationTarget.withParam(za.co.nedbank.core.Constants.EXTRAS.SCREEN_TYPE, za.co.nedbank.core.Constants.API_FAILURE);
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_RECENT_PAYMENT_FLOW, isFromRecentPaymentFlow);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_SOP_FROM_PAY_DONE, isSopFromPayDone);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_LANDING_ON_OVERVIEW, isLandingOnOverView);
        navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_FROM_RECIPIENT_HISTORY, view.isFromRecipientHistory());
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void checkInputsOnScreen(ArrayList<CompatEditText> inputs) {
        if (view == null) {
            return;
        }

        boolean allTheInputsValid = true;
        for (int i = 0; i < inputs.size(); i++) {
            boolean isValidInput = false;
            if (StringUtils.isNullOrEmpty(inputs.get(i).getValue())){
                isValidInput = validateInputs(inputs.get(i),mNonEmptyTextValidator);
            } else if (za.co.nedbank.core.Constants.SharePOPMethod.EMAIL.equalsIgnoreCase((String) inputs.get(i).getTag())) {
                isValidInput = validateInputs(inputs.get(i),mEmailValidator);
            } else if (za.co.nedbank.core.Constants.SharePOPMethod.FAX.equalsIgnoreCase((String) inputs.get(i).getTag())) {
                isValidInput = validateInputs(inputs.get(i),mInternationalNotificationMobileNumberValidator);
            } else if (za.co.nedbank.core.Constants.SharePOPMethod.SMS.equalsIgnoreCase((String) inputs.get(i).getTag())) {
                isValidInput = validateInputs(inputs.get(i),mInternationalNotificationMobileNumberValidator);
            }
            if (!isValidInput) {
                allTheInputsValid = false;
            }
        }
        view.setSharedButtonEnabled(allTheInputsValid);
    }

    protected  boolean validateInputs(final CompatEditText input, final Validator<String> validator) {
        final String value = input.getValue();
        boolean valid = true;
        input.clearErrors();
        final ValidationResult validationResult = validator.validateInput(value);

        if (!validationResult.isOk() && !input.hasFocus()) {
            final String errorMessage = validationResult.getErrorsMessage();
            input.showError(errorMessage);
        }

        valid &= validationResult.isOk();
        return valid;
    }

    public void postShareProofOfPayment(boolean isSopFromPayDone,List<SharePopNotificationTypeData> notificationTypeDataArrayList, String transactionId, boolean isFromRecentPayments, String transactionKind, String transactionDate,boolean isSchedulePayment) {
        ShareProofOfPaymentRequestData shareProofOfPaymentRequestData;
        ShareProofOfPaymentRequestViewModel shareProofOfPaymentRequestViewModel = new ShareProofOfPaymentRequestViewModel();
        shareProofOfPaymentRequestViewModel.setSharePopNotificationTypeData(notificationTypeDataArrayList);
        shareProofOfPaymentRequestViewModel.setTransactionDate(transactionDate);
        shareProofOfPaymentRequestViewModel.setTransactionKind(transactionKind);
        shareProofOfPaymentRequestViewModel.setFromRecentPayments(isFromRecentPayments);
        shareProofOfPaymentRequestViewModel.setSopFromPayDone(isSopFromPayDone);

        shareProofOfPaymentRequestData = shareProofOfPaymentViewModelToDataMapper.mapData(shareProofOfPaymentRequestViewModel);
        shareProofOfPaymentRequestData.setSchedulePayment(isSchedulePayment);
        if (view != null) view.showLoading(true);
        shareProofOfPaymentUseCase.execute(shareProofOfPaymentRequestData, transactionId)
                .subscribe(this::handleSuccess, error -> {
                    handleError();
                });
    }

    public void handleSuccess(SharePOPResponseData sharePOPResponseData) {
        if (view != null) {
            view.showLoading(false);
            if (sharePOPResponseData != null) {
                ShareProofOfPaymentResponseViewModel shareProofOfPaymentResponseViewModel = sharePOPDataToSharePOPViewModelMapper.mapSharePOPDataToSharePOPViewModel(sharePOPResponseData);
                if (shareProofOfPaymentResponseViewModel != null && Constants.API_SUCCESS.equalsIgnoreCase(getApiStatus(shareProofOfPaymentResponseViewModel))) {
                    view.sharePOPSuccess();
                } else {
                    view.trackFailure(true, null);
                    view.sharePOPFailure();
                }
            } else {
                view.sharePOPFailure();
            }
        }
    }

    public void trackSharePOPFailure(boolean isApiFailure, String errorMessage, String errorCode) {
        mAnalytics.trackFailure(isApiFailure, AppTracking.SHARE_POP_FAILURE, ApiAliasConstants.PA_TR_NO, errorMessage, errorCode);

    }

    public void trackSinglePayFlowSharePOPFailure(boolean isApiFailure, String errorMessage, String errorCode) {
        mAnalytics.trackFailure(isApiFailure, PaymentsTrackingEvent.SOP_FAILURE_SINGLE_PAY_FLOW, ApiAliasConstants.PA_TR_NO, errorMessage, errorCode);

    }

    public void trackPayAgainFlowSharePOPFailure(boolean isApiFailure, String errorMessage, String errorCode) {
        mAnalytics.trackFailure(isApiFailure, PaymentsTrackingEvent.SOP_FAILURE_PAY_AGAIN_FLOW, ApiAliasConstants.PA_TR_NO, errorMessage, errorCode);

    }
    public void trackQuickPayFlowSharePOPFailure(boolean isApiFailure, String errorMessage, String errorCode) {
        mAnalytics.trackFailure(isApiFailure, PaymentsTrackingEvent.SOP_FAILURE_QUICK_PAY_FLOW, ApiAliasConstants.PA_TR_NO, errorMessage, errorCode);

    }

    public void trackSinglePayFlowSharePOPSuccess() {
        mAnalytics.sendEvent(PaymentsTrackingEvent.SOP_SUCCESS_SINGLE_PAY_FLOW, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    public void trackPayAgainFlowSharePOPSuccess() {
        mAnalytics.sendEvent(PaymentsTrackingEvent.SOP_SUCCESS_PAY_AGAIN_FLOW, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    public void trackQuickPayFlowSharePOPSuccess() {
        mAnalytics.sendEvent(PaymentsTrackingEvent.SOP_SUCCESS_QUICK_PAY_FLOW, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }


    public String getApiStatus(ShareProofOfPaymentResponseViewModel shareProofOfPaymentResponseViewModel) {
        return shareProofOfPaymentResponseViewModel.getMetadata().getResultData().get(0).getResultDetail().get(0).getStatus();
    }

    public void handleError() {
        if (view != null) {
            view.showLoading(false);
            view.sharePOPFailure();
            view.trackFailure(false, null);
        }
    }


}