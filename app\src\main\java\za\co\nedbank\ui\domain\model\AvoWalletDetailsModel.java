package za.co.nedbank.ui.domain.model;

public class AvoWalletDetailsModel {

    private boolean hasAvoWalletAccount;
    private String avoId;
    private String avoLoginUrl;
    private String avoRegisterUrl;

    public boolean isAvoWalletAccount() {
        return hasAvoWalletAccount;
    }

    public void setAvoWalletAccount(boolean hasAvoWalletAccount) {
        this.hasAvoWalletAccount = hasAvoWalletAccount;
    }

    public String getAvoId() {
        return avoId;
    }

    public void setAvoId(String avoId) {
        this.avoId = avoId;
    }

    public String getAvoLoginUrl() {
        return avoLoginUrl;
    }

    public void setAvoLoginUrl(String avoLoginUrl) {
        this.avoLoginUrl = avoLoginUrl;
    }

    public String getAvoRegisterUrl() {
        return avoRegisterUrl;
    }

    public void setAvoRegisterUrl(String avoRegisterUrl) {
        this.avoRegisterUrl = avoRegisterUrl;
    }
}
