/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.payme_tab;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.base.NBBaseView;


public class PayMeTabPresenter extends NBBasePresenter<NBBaseView> {

    @Inject
    PayMeTabPresenter() {
    }

    @Override
    protected void onBind() {
        super.onBind();
    }
}
