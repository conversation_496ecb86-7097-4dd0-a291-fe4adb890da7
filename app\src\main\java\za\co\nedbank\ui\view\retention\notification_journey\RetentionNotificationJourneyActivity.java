package za.co.nedbank.ui.view.retention.notification_journey;

import static com.google.android.material.snackbar.BaseTransientBottomBar.LENGTH_INDEFINITE;

import android.graphics.Typeface;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.text.HtmlCompat;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.databinding.ActivityNextBestPlanOptionsBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.widget.NBSnackbar;

public class RetentionNotificationJourneyActivity extends NBBaseActivity implements View.OnClickListener, RetentionNotificationJourneyView {

    @Inject
    RetentionNotificationJourneyPresenter mPresenter;
    private ActivityNextBestPlanOptionsBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityNextBestPlanOptionsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        mPresenter.bind(this);
        init();
    }

    private void init() {

        initToolbar(binding.toolbar, true, StringUtils.EMPTY_STRING);
        binding.retNbaOptionsLabel.setText(HtmlCompat.fromHtml(getString(R.string.label_retention_task_options), HtmlCompat.FROM_HTML_MODE_LEGACY));
        mPresenter.handleViewVisibility();

        binding.todoRetentionTaskView.retTaskShareAccountView.setOnClickListener(this);
        binding.todoRetentionTaskView.retLoginSecurityView.setOnClickListener(this);
        binding.todoRetentionTaskView.retProfileLimitView.setOnClickListener(this);
        binding.todoRetentionTaskView.retDebitOrderView.setOnClickListener(this);

        binding.compRetentionTaskView.retTaskShareAccountView.setOnClickListener(this);
        binding.compRetentionTaskView.retLoginSecurityView.setOnClickListener(this);
        binding.compRetentionTaskView.retProfileLimitView.setOnClickListener(this);
        binding.compRetentionTaskView.retDebitOrderView.setOnClickListener(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.unbind();
    }

    @Override
    public void onClick(View v) {
        if (v == binding.todoRetentionTaskView.retTaskShareAccountView) {
            mPresenter.handleShareAccountTodoTaskClick();
            mPresenter.trackActivationJourneyOptionsOnAppsFlyer(binding.todoRetentionTaskView.retShareHeadingTV.getText().toString().trim());
        } else if (v == binding.todoRetentionTaskView.retLoginSecurityView) {
            mPresenter.handleLoginSecurityTodoTaskClick();
            mPresenter.trackActivationJourneyOptionsOnAppsFlyer(binding.todoRetentionTaskView.retSecurityHeadingTV.getText().toString().trim());
        } else if (v == binding.todoRetentionTaskView.retProfileLimitView) {
            mPresenter.handleProfileLimitTodoTaskClick();
            mPresenter.trackActivationJourneyOptionsOnAppsFlyer(binding.todoRetentionTaskView.retProfileLimitHeadingTV.getText().toString().trim());
        } else if (v == binding.todoRetentionTaskView.retDebitOrderView) {
            mPresenter.handleDebitOrderSwitchingTodoTaskClick();
            mPresenter.trackActivationJourneyOptionsOnAppsFlyer(binding.todoRetentionTaskView.retDebitOrderHeadingTV.getText().toString().trim());
        } else if (v == binding.compRetentionTaskView.retTaskShareAccountView) {
            mPresenter.handleShareAccountCompTaskClick();
        } else if (v == binding.compRetentionTaskView.retLoginSecurityView) {
            mPresenter.handleLoginSecurityCompTaskClick();
        } else if (v == binding.compRetentionTaskView.retProfileLimitView) {
            mPresenter.handleProfileLimitCompTaskClick();
        }else if (v == binding.compRetentionTaskView.retDebitOrderView) {
            mPresenter.handleDebitOrderSwitchingCompTaskClick();
        }
    }

    @Override
    public void showError() {
        showError(getString(R.string.somethings_wrong), getString(R.string.error_generic),
                getString(R.string.dismiss), LENGTH_INDEFINITE, () -> NBSnackbar.instance().dismissSnackBar());
    }

    @Override
    public boolean onOptionsItemSelected(final MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            mPresenter.sendBackArrowAnalytics();
            super.onOptionsItemSelected(item);
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        mPresenter.sendBackArrowAnalytics();
        super.onBackPressed();
    }

    @Override
    public void setVisibilityCompTask() {
        binding.completedTaskLabelTV.setVisibility(View.VISIBLE);
    }

    @Override
    public void setVisibilityTodoLabel() {
        binding.todoLabelTV.setVisibility(View.VISIBLE);
    }

    @Override
    public void setShareAccountCompDivider() {
        binding.compRetentionTaskView.retShareDivider.setVisibility(View.VISIBLE);
    }

    @Override
    public void setSecurityCompDivider() {
        binding.compRetentionTaskView.retSecurityDivider.setVisibility(View.VISIBLE);
    }

    @Override
    public void setDebitCompDivider() {
        binding.compRetentionTaskView.retDebitOrderDivider.setVisibility(View.VISIBLE);
    }

    @Override
    public void setVisibilityProfileGroupComp() {
        binding.compRetentionTaskView.retProfileGroup.setVisibility(View.VISIBLE);
    }

    @Override
    public void setProfileLimitHeadingComp() {
        binding.compRetentionTaskView.retProfileLimitHeadingTV.setTypeface(Typeface.create(getString(R.string.font_sans_serif_light), Typeface.NORMAL));
    }

    @Override
    public void setShareDividerTodo() {
        binding.compRetentionTaskView.retShareDivider.setVisibility(View.VISIBLE);
    }

    @Override
    public void setSecurityDividerTodo() {
        binding.todoRetentionTaskView.retSecurityDivider.setVisibility(View.VISIBLE);
    }

    @Override
    public void setDebitDividerTodo() {
        binding.todoRetentionTaskView.retDebitOrderDivider.setVisibility(View.VISIBLE);
    }

    @Override
    public void setProfileGroupTodo() {
        binding.todoRetentionTaskView.retProfileGroup.setVisibility(View.VISIBLE);
    }

    @Override
    public void setShareDividerComp() {
        binding.compRetentionTaskView.retShareDivider.setVisibility(View.VISIBLE);
    }

    @Override
    public void setDebitGroupComp() {
        binding.compRetentionTaskView.retDebitOrderGroup.setVisibility(View.VISIBLE);
    }

    @Override
    public void setDebitCompHeading() {
        binding.compRetentionTaskView.retDebitOrderHeadingTV.setTypeface(Typeface.create(getString(R.string.font_sans_serif_light), Typeface.NORMAL));
    }

    @Override
    public void setDebitGroupTodo() {
        binding.todoRetentionTaskView.retDebitOrderGroup.setVisibility(View.VISIBLE);
    }

    @Override
    public void setSecurityGroupComp() {
        binding.compRetentionTaskView.retSecurityGroup.setVisibility(View.VISIBLE);
    }

    @Override
    public void setSecurityHeadingComp() {
        binding.compRetentionTaskView.retSecurityHeadingTV.setTypeface(Typeface.create(getString(R.string.font_sans_serif_light), Typeface.NORMAL));
    }

    @Override
    public void setSecurityGroupTodo() {
        binding.todoRetentionTaskView.retSecurityGroup.setVisibility(View.VISIBLE);
    }

    @Override
    public void setShareGroupComp() {
        binding.compRetentionTaskView.retShareGroup.setVisibility(View.VISIBLE);
    }

    @Override
    public void setShareHeadingComp() {
        binding.compRetentionTaskView.retShareHeadingTV.setTypeface(Typeface.create(getString(R.string.font_sans_serif_light), Typeface.NORMAL));
    }

    @Override
    public void setShareGroupTodo() {
        binding.todoRetentionTaskView.retShareGroup.setVisibility(View.VISIBLE);
    }
}
