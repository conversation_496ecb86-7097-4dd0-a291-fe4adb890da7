package za.co.nedbank.ui.view.card_delivery;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import org.jetbrains.annotations.NotNull;

public class SpaceItemDecoration extends RecyclerView.ItemDecoration {

    private  int position;
    private final int spaceDimenResource;

    public SpaceItemDecoration(int position, int spaceDimenResource) {
        this.position = position;
        this.spaceDimenResource = spaceDimenResource;
    }

    @Override
    public void getItemOffsets(@NonNull @NotNull Rect outRect, @NonNull @NotNull View view, @NonNull @NotNull RecyclerView parent, @NonNull @NotNull RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        int layoutPosition = ((RecyclerView.LayoutParams) view.getLayoutParams()).getViewLayoutPosition();
        //last position
        if (layoutPosition == this.position) {
            outRect.set(0, 0, 0, view.getResources().getDimensionPixelSize(spaceDimenResource));
        }
    }

    public void updatePosition(int position) {
        this.position = position;
    }
}

