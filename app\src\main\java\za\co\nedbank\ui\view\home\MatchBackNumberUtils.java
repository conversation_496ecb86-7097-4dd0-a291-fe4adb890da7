package za.co.nedbank.ui.view.home;

import java.util.ArrayList;
import java.util.List;

import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.view.recipient.RecipientViewModel;

public class MatchBackNumberUtils {
    private static List<Integer> mMatchBackNumberAvoidList;

    private MatchBackNumberUtils() {
    }

    public static int removeMatchBackNumbers(List<NbRecyclerViewBaseDataModel> bankAccountViewDataModelList, List<NbRecyclerViewBaseDataModel> creditCardViewDataModelList, List<NbRecyclerViewBaseDataModel> mobileNumberViewDataModelList, List<NbRecyclerViewBaseDataModel> electricityMeterViewDataModelList, List<NbRecyclerViewBaseDataModel> shapeIdDataModelList, RecipientViewModel recipientViewModel) {
        int uniqueMatchBacks = 0;
        mMatchBackNumberAvoidList = new ArrayList<>();
        uniqueMatchBacks = calculateMatchBack(bankAccountViewDataModelList, creditCardViewDataModelList, mobileNumberViewDataModelList, electricityMeterViewDataModelList, shapeIdDataModelList, uniqueMatchBacks);

        if (recipientViewModel != null) {
            List<NbRecyclerViewBaseDataModel> accounts = recipientViewModel.getBankAccountViewDataModelList();
            if (CollectionUtils.isNotEmpty(accounts)) {
                uniqueMatchBacks = changeAccountCardMeterMatchBack(accounts, uniqueMatchBacks);
            }

            List<NbRecyclerViewBaseDataModel> cards = recipientViewModel.getCreditCardViewDataModelList();
            if (CollectionUtils.isNotEmpty(cards)) {
                uniqueMatchBacks = changeAccountCardMeterMatchBack(cards, uniqueMatchBacks);
            }

            List<NbRecyclerViewBaseDataModel> meters = recipientViewModel.getElectricityMeterViewDataModelList();
            if (CollectionUtils.isNotEmpty(meters)) {
                uniqueMatchBacks = changeAccountCardMeterMatchBack(meters, uniqueMatchBacks);
            }

            List<NbRecyclerViewBaseDataModel> shapeId = recipientViewModel.getShapIdViewDataModelList();
            if (CollectionUtils.isNotEmpty(shapeId)) {
                uniqueMatchBacks = changeAndFilterMatchBack(shapeId, uniqueMatchBacks);
            }

            List<NbRecyclerViewBaseDataModel> mobiles = recipientViewModel.getMobileNumberViewDataModelList();
            if (CollectionUtils.isNotEmpty(mobiles)) {
                uniqueMatchBacks = changeAndFilterMatchBack(mobiles, uniqueMatchBacks);
            }
        }
        return uniqueMatchBacks;
    }

    private static int calculateMatchBack(List<NbRecyclerViewBaseDataModel> bankAccountViewDataModelList, List<NbRecyclerViewBaseDataModel> creditCardViewDataModelList, List<NbRecyclerViewBaseDataModel> mobileNumberViewDataModelList, List<NbRecyclerViewBaseDataModel> electricityMeterViewDataModelList, List<NbRecyclerViewBaseDataModel> shapeIdDataModelList, int uniqueMatchBacks) {
        if (CollectionUtils.isNotEmpty(bankAccountViewDataModelList)) {
            uniqueMatchBacks = changeAccountCardMeterMatchBack(bankAccountViewDataModelList, uniqueMatchBacks);
        }

        if (CollectionUtils.isNotEmpty(creditCardViewDataModelList)) {
            uniqueMatchBacks = changeAccountCardMeterMatchBack(creditCardViewDataModelList, uniqueMatchBacks);
        }

        if (CollectionUtils.isNotEmpty(electricityMeterViewDataModelList)) {
            uniqueMatchBacks = changeAccountCardMeterMatchBack(electricityMeterViewDataModelList, uniqueMatchBacks);
        }

        if (CollectionUtils.isNotEmpty(mobileNumberViewDataModelList)) {
            uniqueMatchBacks = changeAndFilterMatchBack(mobileNumberViewDataModelList, uniqueMatchBacks);
        }
        if (CollectionUtils.isNotEmpty(shapeIdDataModelList)) {
            uniqueMatchBacks = changeAndFilterMatchBack(shapeIdDataModelList, uniqueMatchBacks);
        }

        return uniqueMatchBacks;

    }

    private static int changeAndFilterMatchBack(List<NbRecyclerViewBaseDataModel> mobileNumberViewDataModelList, int uniqueMatchBacks) {
        for (NbRecyclerViewBaseDataModel model : mobileNumberViewDataModelList) {
            uniqueMatchBacks++;
            model.setMatchBackNumber(uniqueMatchBacks);
            mMatchBackNumberAvoidList.add(uniqueMatchBacks);
        }
        return uniqueMatchBacks;
    }

    private static int changeAccountCardMeterMatchBack(List<NbRecyclerViewBaseDataModel> mobileNumberViewDataModelList, int uniqueMatchBacks) {
        for (NbRecyclerViewBaseDataModel model : mobileNumberViewDataModelList) {
            uniqueMatchBacks++;
            model.setMatchBackNumber(uniqueMatchBacks);
        }
        return uniqueMatchBacks;
    }

    public static List<Integer> getMatchBackNumberAvoidList() {
        return mMatchBackNumberAvoidList;
    }
}
