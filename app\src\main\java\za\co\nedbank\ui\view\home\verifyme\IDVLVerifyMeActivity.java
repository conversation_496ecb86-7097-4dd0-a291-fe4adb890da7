package za.co.nedbank.ui.view.home.verifyme;

import static za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_FINGERPRINT_ALTERED;

import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.text.Html;

import androidx.appcompat.app.AlertDialog;
import androidx.core.text.HtmlCompat;

import org.greenrobot.eventbus.EventBus;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.databinding.ActivityIdvlverifyMeBinding;
import za.co.nedbank.idvcore.base.data.model.ProcessType;
import za.co.nedbank.nid_sdk.main.interaction.listeners.EnrolmentListener;
import za.co.nedbank.nid_sdk.main.interaction.model.EnrolmentDto;
import za.co.nedbank.nid_sdk.main.interaction.model.SaIdCaptureDto;
import za.co.nedbank.nid_sdk.main.interaction.registration.EnrolmentFlow;
import za.co.nedbank.nid_sdk.main.views.pin_biometric.PinBiometricFlows;
import za.co.nedbank.ui.di.AppDI;

public class IDVLVerifyMeActivity extends NBBaseActivity implements IDVLVerifyMeView {

    @Inject
    IDVLVerifyMePresenter presenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EventBus.getDefault().post(new AjoInAppEvent(IDVLVerifyMeActivity.class, false));
        ActivityIdvlverifyMeBinding binding = ActivityIdvlverifyMeBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        presenter.bind(this);
        presenter.sendPageAnalytics();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            binding.tvVerifyMeMessage.setText(Html.fromHtml(getString(R.string.idvl_verify_me_alert_message), HtmlCompat.FROM_HTML_MODE_LEGACY));
        } else {
            binding.tvVerifyMeMessage.setText(Html.fromHtml(getString(R.string.idvl_verify_me_alert_message)));
        }
        binding.buttonNotNow.setOnClickListener(v -> onNotNowClick());
        binding.icCrossIdvl.setOnClickListener(v -> onCrossIconClicked());
        binding.buttonVerifyMe.setOnClickListener(v -> onVerifyClick());
    }

    public void onNotNowClick(){
      showConfirmationDialog();
    }

    public void onCrossIconClicked() {
        presenter.trackActionVerifyMeClose();
        navigateToHome();
    }

    private void showConfirmationDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            builder.setTitle(Html.fromHtml(getString(R.string.idvl_dialog_title), HtmlCompat.FROM_HTML_MODE_LEGACY));
        } else {
            builder.setTitle(Html.fromHtml(getString(R.string.idvl_dialog_title)));
        }
        builder.setMessage(getString(R.string.idvl_dialog_description));
        builder.setCancelable(false);
        builder.setPositiveButton(
                getText(R.string.yes), (DialogInterface dialog, int id) -> {
                    dialog.dismiss();
                    navigateToHome();
                    presenter.trackActionVerifyMeNotNow(getString(R.string.yes));
                });
        builder.setNegativeButton(
                getText(R.string.no), (DialogInterface dialog, int id) -> {
                    dialog.dismiss();
                    presenter.trackActionVerifyMeNotNow(getString(R.string.no));
                });
        AlertDialog alertDialog = builder.create();
        alertDialog.show();
    }

    private void navigateToHome() {
        PinBiometricFlows pinBiometricFlow = (PinBiometricFlows) getIntent().getSerializableExtra(za.co.nedbank.core.Constants.BUNDLE_KEYS.PIN_BIOMETRIC_FLOW);
        boolean fromSecondLoginScreen = IntentUtils.getBooleanValue(getIntent(), NavigationTarget.FROM_SECOND_LOGIN_SCREEN, false);
        boolean isFingerPrintAltered = IntentUtils.getBooleanValue(getIntent(), IS_FINGERPRINT_ALTERED, false);
        presenter.navigateToNextScreen(pinBiometricFlow, fromSecondLoginScreen, isFingerPrintAltered);
    }

    public void onVerifyClick(){
        if (isDemoMode()) return;
        presenter.trackActionVerifyMe();
        EnrolmentFlow.getInstance(this).triggerAuthenticatedIdvl(ProcessType.VERIFY, new EnrolmentListener() {
            @Override
            public void onEnrolmentComplete(EnrolmentDto enrolmentDto) {
                navigateToHome();
            }
            @Override
            public void onSidCaptured(SaIdCaptureDto saIdCaptureDto) {
                //Implementation not required
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().post(new AjoInAppEvent(IDVLVerifyMeActivity.class, true));
    }
}