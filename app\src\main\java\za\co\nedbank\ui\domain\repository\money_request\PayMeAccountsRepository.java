/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository.money_request;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.data.mapper.AccountsEntityToDataMapper;
import za.co.nedbank.core.domain.model.AccountDetailsData;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.ui.data.networking.PaymeAccountsAPI;
import za.co.nedbank.ui.domain.repository.IPayMeAccountsRepository;


public class PayMeAccountsRepository implements IPayMeAccountsRepository {

    private final AccountsEntityToDataMapper mAccountsEntityToDataMapper;
    private final NetworkClient mNetworkClient;

    @Inject
    PayMeAccountsRepository(AccountsEntityToDataMapper payAccountsEntityToDataMapper, NetworkClient networkClient) {
        this.mAccountsEntityToDataMapper = payAccountsEntityToDataMapper;
        this.mNetworkClient = networkClient;
    }

    @Override
    public Observable<List<AccountDetailsData>> getAccounts() {

        return mNetworkClient.create(PaymeAccountsAPI.class)
                .getTransferAccounts()
                .map(mAccountsEntityToDataMapper::mapAccountDetailEntityToData);
    }
}
