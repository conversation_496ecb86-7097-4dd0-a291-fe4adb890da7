/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository.pop;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.ui.data.networking.TransactionApi;
import za.co.nedbank.ui.domain.mapper.pop.TransactionProviderEntityToDataMapper;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryParentData;


public class NBTransactionRepository implements TransactionRepository {

    private final NetworkClient mNetworkClient;
    private final TransactionProviderEntityToDataMapper transactionProviderEntityToDataMapper;

    @Inject
    public NBTransactionRepository(TransactionProviderEntityToDataMapper transactionProviderEntityToDataMapper, NetworkClient networkClient) {
        this.transactionProviderEntityToDataMapper = transactionProviderEntityToDataMapper;
        this.mNetworkClient = networkClient;
    }

    @Override
    public Observable<TransactionHistoryParentData> getTransactionHistory(int contactCardId,
                                                                          String pageSize,
                                                                          String page,
                                                                          String startDate,
                                                                          String endDate) {
        return mNetworkClient.create(TransactionApi.class)
                .getTransactionHistory(contactCardId, pageSize, page, startDate, endDate)
                .map(transactionProviderEntityToDataMapper::mapTransactionHistoryParentEntityToData);
    }
}
