package za.co.nedbank.ui.notifications;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.NotificationManager;
import android.app.TaskStackBuilder;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.adobe.marketing.mobile.Messaging;
import com.adobe.marketing.mobile.services.ServiceProvider;
import com.google.gson.Gson;

import java.util.ArrayList;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.core.notification.mapper.NotificationDataToViewModelMapper;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.databinding.ActivityNotificationNavigationBinding;
import za.co.nedbank.enroll_v2.Constants;
import za.co.nedbank.enroll_v2.view.login.LoginActivity;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextDataViewModelMapper;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextFedarationDetailsViewModel;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;
import za.co.nedbank.ui.view.notification.NotificationCenterActivity;
import za.co.nedbank.ui.view.notification.ajo_notification_details.AJONotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.contextswitch.ContextSwitchConfirmationActivity;
import za.co.nedbank.ui.view.notification.notification_details.NotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.notification_messages.NotificationMessagesActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.details.TransactionNotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.inbox.TransactionInboxActivity;

public class NotificationNavigationActivity extends NBBaseActivity {

    private static final String TAG = NotificationNavigationActivity.class.getSimpleName();

    private ActivityNotificationNavigationBinding binding;
    private ArrayList<Intent> mIntents;

    @Inject
    NotificationManager notificationManager;
    @Inject
    NotificationDataToViewModelMapper notificationDataToViewModelMapper;
    @Inject
    GetFedarationListUseCase mGetFedarationListUseCase;
    @Inject
    SwitchContextDataViewModelMapper mSwitchContextDataViewModelMapper;
    @Inject
    APIInformation apiInformation;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getActivityComponent(this).inject(this);
        binding = ActivityNotificationNavigationBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        prepareAnimation();
        handleNotificationNavigation(getIntent());
    }

    private void prepareAnimation() {
        binding.txvWelcom.animate().alpha(Constants.SPLASH.MAX_ALPHA).setDuration(Constants.SPLASH.ALPHA_ANIMATION_DURATION);

        binding.splashLoader.setSpeed(Constants.SPLASH.LOTTIE_SPEED);
        binding.splashLoader.playAnimation();

        int deviceHeight = Resources.getSystem().getDisplayMetrics().heightPixels;
        ValueAnimator valueAnimator = ValueAnimator.ofInt(deviceHeight / 2, deviceHeight / 4);
        valueAnimator.setDuration(Constants.SPLASH.LOTTIE_ANIMATION_DURATION);
        valueAnimator.addUpdateListener(animation -> updateTopMargin((Integer) animation.getAnimatedValue()));
        valueAnimator.start();
    }

    private boolean isContextSwitchNeeded(FBNotificationsViewModel fbNotificationsViewModel) {
        if (fbNotificationsViewModel.getClient() != null && !StringUtils.isNullOrEmpty(fbNotificationsViewModel.getClient().getCisNo()) && !(fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.UBC) || fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.ECERT) || fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.TBC))) {
            return !fbNotificationsViewModel.getClient().getCisNo().equals(memoryApplicationStorage.getString(StorageKeys.CIS_NUMBER, StringUtils.EMPTY_STRING));
        }
        return false;
    }

    private void updateTopMargin(int topMargin) {
        float dimen40Dp = getResources().getDimension(R.dimen.dimen_40dp);
        float dimen10Dp = getResources().getDimension(R.dimen.dimen_10dp);
        RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) binding.txvWelcom.getLayoutParams();
        lp.setMargins(0, (int) (topMargin - dimen40Dp), 0, (int) dimen10Dp);
        binding.txvWelcom.setLayoutParams(lp);
    }

    private void handleNotificationNavigation(Intent intent) {
        try {
            if (intent != null && intent.getExtras() != null) {
                String action = NotificationConstants.ACTIONS.DISMISS;
                int notificationId = 0;
                if (intent.getAction() != null) {
                    action = intent.getAction();
                }
                if (intent.getExtras().containsKey(NotificationConstants.EXTRA.NOTIFICATION_ID)) {
                    notificationId = intent.getExtras().getInt(NotificationConstants.EXTRA.NOTIFICATION_ID);
                }
                notificationManager.cancel(notificationId);
                switch (action) {
                    case NotificationConstants.ACTIONS.DEFAULT_NOTIFICATION_NAVIGATION,
                            NotificationConstants.ACTIONS.VIEW ->
                            processNotificationNavigation(action, intent);
                    case NotificationConstants.ACTIONS.DISMISS -> handleDismissAction(intent);
                    default -> finish();
                }
            }
        } catch (Exception e) {
            NBLogger.e(TAG, Log.getStackTraceString(e));
        }
        finish();
    }

    private void processNotificationNavigation(String action, @NonNull Intent intent) {
        if (intent.getExtras() != null && intent.getExtras().containsKey(NotificationConstants.EXTRA.AJO_NOTIFICATION_DATA)) {
            String intentData = intent.getExtras().getString(NotificationConstants.EXTRA.AJO_NOTIFICATION_DATA);
            AjoPushPayloadDataModel ajoPushPayload = new Gson().fromJson(intentData, AjoPushPayloadDataModel.class);
            handleAJONavigation(ajoPushPayload);
            Messaging.handleNotificationResponse(intent, true, null);
        } else {
            Object notificationObject = getSerializable(intent);
            if (notificationObject instanceof NotificationData notificationData) {
                notificationData.setSelectedAction(action);
                FBNotificationsViewModel notificationViewModel = notificationDataToViewModelMapper.transform(notificationData);
                handleDefaultNavigation(notificationViewModel);
            }
        }
    }

    private void handleAJONavigation(AjoPushPayloadDataModel ajoPushPayload) {
        if (ajoPushPayload.getActionType() == AjoPushPayloadDataModel.ActionType.DEEPLINK) {
            handleAJOPushNavigation(ajoPushPayload);
        } else if (ajoPushPayload.getActionType() == AjoPushPayloadDataModel.ActionType.WEB_URL) {
            openUri(ajoPushPayload.getActionUri());
        } else {
            openApplication();
        }
    }

    private void handleDefaultNavigation(FBNotificationsViewModel notificationsViewModel) {
        mIntents = new ArrayList<>();
        final PackageManager packageManager = getPackageManager();
        final Intent appLaunchIntent = packageManager.getLaunchIntentForPackage(getPackageName());

        if (isLoggedIn()) {
            mIntents.add(appLaunchIntent);
            if (isContextSwitchNeeded(notificationsViewModel)) {
                getContextSwitchModel(Long.parseLong(notificationsViewModel.getClient().getCisNo()));
            } else {
                handleNonContextSwitchHandling(notificationsViewModel);
            }
        } else {
            //app not logged in
            if (notificationsViewModel.getNotificationType().equalsIgnoreCase(NotificationConstants.NOTIFICATION_TYPES.TRANSACTION) || notificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.UBC) || notificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.TBC) || notificationsViewModel.getNotificationType().equalsIgnoreCase(NotificationConstants.NOTIFICATION_TYPES.INFO) || notificationsViewModel.getDistributionType().equalsIgnoreCase(NotificationConstants.DISTRIBUTION_TYPES.ECERT) || Boolean.TRUE.equals(notificationsViewModel.getAllowAnonymous())) {
                Intent targetIntent = new Intent(this, TransactionNotificationDetailsActivity.class);
                targetIntent.putExtra(NavigationTarget.PARAM_TRANS_PUSH_DATA, notificationsViewModel);
                targetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                mIntents.add(targetIntent);
            } else {
                //if login screen is already in stack open it with clear top
                mIntents.add(appLaunchIntent);
                boolean canNavigateToLogin = memoryApplicationStorage.getBoolean(NotificationConstants.STORAGE_KEYS.CAN_NAVIGATE_TO_LOGIN, false);
                if (canNavigateToLogin) {
                    Intent targetIntent = new Intent(this, LoginActivity.class);
                    targetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    mIntents.add(targetIntent);
                }
            }
            startActivities(mIntents.toArray(new Intent[]{}));
        }
        //saving notification data in memory to be retrieved in home screen for further navigation
        memoryApplicationStorage.putObject(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL, notificationsViewModel);
        NBLogger.e("NotificationNavigationActivity", new Gson().toJson(notificationsViewModel));
    }

    private void handleNonContextSwitchHandling(FBNotificationsViewModel notificationsViewModel) {
        //adding notification center screen
        Intent notificationCenterIntent = new Intent(this, NotificationCenterActivity.class);
        notificationCenterIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        notificationCenterIntent.putExtra(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_JOURNEY_FLAG, za.co.nedbank.core.Constants.FLOW_CONSTANTS.IN_APP_NOTIFICATION_FLOW);

        if (notificationsViewModel.getNotificationType().equals(NotificationConstants.NOTIFICATION_TYPES.ITA)) {
            // Nothing to do here as ITA pushed from Application class
        } else if (!notificationsViewModel.getNotificationType().equals(NotificationConstants.NOTIFICATION_TYPES.TRANSACTION)) {
            //adding notification messages screen
            mIntents.add(notificationCenterIntent);
            Intent notificationMessagesIntent = new Intent(this, NotificationMessagesActivity.class);
            notificationMessagesIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            mIntents.add(notificationMessagesIntent);
            //adding notification details screen
            Intent targetIntent = new Intent(this, NotificationDetailsActivity.class);
            targetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            targetIntent.putExtra(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL, notificationsViewModel);
            mIntents.add(targetIntent);
        } else {
            mIntents.add(notificationCenterIntent);
            Intent transactionInboxIntent = new Intent(this, TransactionInboxActivity.class);
            transactionInboxIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            transactionInboxIntent.putExtra(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL, notificationsViewModel);
            mIntents.add(transactionInboxIntent);
        }
        startActivities(mIntents.toArray(new Intent[]{}));
    }

    @SuppressLint("CheckResult")
    private void getContextSwitchModel(long cis) {
        mGetFedarationListUseCase.execute().subscribe(fedarationList -> {
            ArrayList<SwitchContextFedarationDetailsViewModel> fedarationViewModels = (ArrayList<SwitchContextFedarationDetailsViewModel>) mSwitchContextDataViewModelMapper.mapFedarationDetailResponse(fedarationList);

            for (SwitchContextFedarationDetailsViewModel model : fedarationViewModels) {
                if (cis == model.getEnterpriseCustomerNumber()) {
                    Intent confirmationIntent = new Intent(this, ContextSwitchConfirmationActivity.class);
                    confirmationIntent.putExtra(NotificationConstants.EXTRA.CONTEXT_SWITCH_MODEL, model);
                    mIntents.add(confirmationIntent);
                    startActivities(mIntents.toArray(new Intent[]{}));
                }
            }

        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    private void handleAJOPushNavigation(AjoPushPayloadDataModel ajoPushPayload) {
        mIntents = new ArrayList<>();
        final PackageManager packageManager = getPackageManager();
        final Intent appLaunchIntent = packageManager.getLaunchIntentForPackage(getPackageName());
        final String payload = new Gson().toJson(ajoPushPayload);

        if (isLoggedIn()) {
            mIntents.add(appLaunchIntent);
            //adding ajo notification details screen
            Intent targetIntent = new Intent(this, AJONotificationDetailsActivity.class);
            targetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            targetIntent.putExtra(NotificationConstants.EXTRA.AJO_NOTIFICATION_DATA, payload);
            mIntents.add(targetIntent);
        } else {
            //if login screen is already in stack open it with clear top
            boolean canNavigateToLogin = memoryApplicationStorage.getBoolean(NotificationConstants.STORAGE_KEYS.CAN_NAVIGATE_TO_LOGIN, false);
            mIntents.add(appLaunchIntent);
            if (canNavigateToLogin) {
                Intent mTargetIntent = new Intent(this, LoginActivity.class);
                mTargetIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                mIntents.add(mTargetIntent);
            }
        }
        //saving notification data in memory to be retrieved in home screen for further navigation
        memoryApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.AJO_NOTIFICATION_PAYLOAD, payload);
        NBLogger.e("NotificationNavigationActivity", payload);
        startActivities(mIntents.toArray(new Intent[]{}));
    }

    private boolean isLoggedIn() {
        return !apiInformation.isLoggedOut();
    }

    private void handleDismissAction(Intent intent) {
        if (intent.getExtras() != null && intent.getExtras().containsKey(NotificationConstants.EXTRA.AJO_NOTIFICATION_DATA)) {
            Messaging.handleNotificationResponse(intent, false, NotificationConstants.ACTIONS.DISMISS);
        }
    }

    @SuppressWarnings("deprecation")
    private Object getSerializable(Intent intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return intent.getSerializableExtra(NotificationConstants.EXTRA.NOTIFICATION_DATA, NotificationData.class);
        } else {
            return intent.getSerializableExtra(NotificationConstants.EXTRA.NOTIFICATION_DATA);
        }
    }

    private void openUri(final String uri) {
        if (StringUtils.isNullOrEmpty(uri)) return;
        final Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(uri));
        TaskStackBuilder stackBuilder = TaskStackBuilder.create(this);
        stackBuilder.addNextIntentWithParentStack(intent);
        stackBuilder.startActivities();
    }

    private void openApplication() {
        final Activity currentActivity = ServiceProvider.getInstance().getAppContextService().getCurrentActivity();
        final Intent launchIntent;
        if (currentActivity != null) {
            launchIntent = new Intent(currentActivity, currentActivity.getClass());
        } else {
            launchIntent = getPackageManager().getLaunchIntentForPackage(getPackageName());
        }
        if (launchIntent != null) {
            launchIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(launchIntent);
        }
    }
}