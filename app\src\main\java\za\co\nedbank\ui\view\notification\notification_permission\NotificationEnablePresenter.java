package za.co.nedbank.ui.view.notification.notification_permission;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.provider.Settings;

import java.util.HashMap;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;

public class NotificationEnablePresenter extends NBBasePresenter<NotificationEnableView> {

    private final Analytics mAnalytics;
    private final ApplicationStorage mMemoryApplicationStorage;
    @Inject
    NotificationEnablePresenter(Analytics analytics, final @Named("memory") ApplicationStorage memoryApplicationStorage) {
        this.mAnalytics = analytics;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
    }

    @SuppressLint("InlinedApi")
    public void navigateToNotificationSettings(int sdkInt) {
        Intent settingsIntent;
        if (sdkInt >= android.os.Build.VERSION_CODES.O) {
            settingsIntent = new Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            settingsIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            settingsIntent.putExtra(Settings.EXTRA_APP_PACKAGE, view.getPackageName());
        } else {
            Uri uri = Uri.fromParts("package", view.getPackageName(), null);
            settingsIntent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            settingsIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            settingsIntent.setData(uri);
        }
        mMemoryApplicationStorage.putBoolean(Constants.KEY_CAME_FROM_NOTIFICATION_SETTING_SCREEN,true);
        view.startActivity(settingsIntent);
    }

    public void sendPageLoadAnalytics() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setPageCategory(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        adobeContextData.setFeature(TrackingParam.NOTIFICATIONS);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_CX_SERVICES);
        adobeContextData.setSubFeature(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        mAnalytics.sendEventStateWithMap(TrackingParam.ENABLE_PUSH_NOTIFICATIONS, cdata);
    }

    public void trackActionNotificationEnableClicked() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeature(TrackingParam.NOTIFICATIONS);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_CX_SERVICES);
        adobeContextData.setSubFeature(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        adobeContextData.setScreenName(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        mAnalytics.sendEventActionWithMap(TrackingParam.ACTION_PUSH_NOTIFICATIONS_ENABLE, cdata);
    }

    public void trackActionNotificationNotNowClicked() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setScreenName(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        adobeContextData.setFeature(TrackingParam.NOTIFICATIONS);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_CX_SERVICES);
        adobeContextData.setSubFeature(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        adobeContextData.setFeatureCount();
        adobeContextData.setFeatureCategoryCount();
        adobeContextData.setSubFeatureCount();
        mAnalytics.sendEventActionWithMap(TrackingParam.ACTION_PUSH_NOTIFICATIONS_NOT_NOW, cdata);
    }

    public void trackNotificationEnabledAction() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setScreenName(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        adobeContextData.setFeature(TrackingParam.NOTIFICATIONS);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_CX_SERVICES);
        adobeContextData.setSubFeature(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        adobeContextData.setFeatureCount();
        adobeContextData.setFeatureCategoryCount();
        adobeContextData.setSubFeatureCount();
        mAnalytics.sendEventActionWithMap(TrackingParam.PUSH_NOTIFICATIONS_ENABLE_SUCCESS, cdata);
    }

    public void trackNotificationNotEnabledAction(String errorMsg) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setScreenName(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        adobeContextData.setFeature(TrackingParam.NOTIFICATIONS);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_CX_SERVICES);
        adobeContextData.setSubFeature(TrackingParam.ENABLE_PUSH_NOTIFICATIONS);
        adobeContextData.setFeatureCount();
        adobeContextData.setFeatureCategoryCount();
        adobeContextData.setSubFeatureCount();
        adobeContextData.setUserErrorCount();
        mAnalytics.trackFailure(false,TrackingParam.PUSH_NOTIFICATIONS_ENABLE_FAILURE, ApiAliasConstants.PU_NO,errorMsg,null,cdata);
    }
}
