/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.List;

import io.reactivex.annotations.NonNull;
import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.payment.PaymentsUtility;
import za.co.nedbank.core.sharedui.control.CustomLinearLayout;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewModel;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.view.banklist.model.BankBranchViewModel;
import za.co.nedbank.core.view.banklist.model.BankViewModel;
import za.co.nedbank.core.view.recipient.BankAccountViewDataModel;
import za.co.nedbank.payment.pay.domain.data.model.account_type.AccountType;
import za.co.nedbank.ui.view.home.edit_recipient.EditRecipientActivity;
import za.co.nedbank.uisdk.component.CompatEditText;
import za.co.nedbank.uisdk.component.CompatPicker;
import za.co.nedbank.uisdk.component.CompatTextView;
import za.co.nedbank.uisdk.validation.ValidatableInput;

/**
 * Created by priyadhingra on 9/5/2017.
 */

public class BankAccountAdapter extends NBFlexibleItemCountRecyclerviewAdapter {

    private final String mSelectBankString;
    private IActivityAdapterComListener mIActivityAdapterComListener;

    public void setIActivityAdapterComListener(IActivityAdapterComListener iActivityAdapterComListener) {
        mIActivityAdapterComListener = iActivityAdapterComListener;
    }

    public BankAccountAdapter(@NonNull final Context context,
                              @NonNull final NBFlexibleItemCountRecyclerviewModel nbFlexibleItemCountRecyclerviewModel,
                              @NonNull final List<NbRecyclerViewBaseDataModel> nbRecyclerViewBaseDataModelList,
                              final IAdapterInteractionListener adapterInteractionListener,
                              ISectionedListItemSelectedListener itemSelectedListener) {
        super(context, nbFlexibleItemCountRecyclerviewModel, nbRecyclerViewBaseDataModelList, adapterInteractionListener, itemSelectedListener);
        this.mContext = context;
        mSelectBankString = StringUtils.EMPTY_STRING;
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(Context context, ViewGroup parent) {
        View v = LayoutInflater.from(context).inflate(mNBFlexibleItemCountRecyclerviewModel.getItemView(), parent, false);
        VHBankAccountItem vHBankAccountItem = new VHBankAccountItem(v);

        if (vHBankAccountItem.accountNumber != null) {
            vHBankAccountItem.accountNumber.getInputField().setContentDescription(" ");
        }

        return vHBankAccountItem;
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (isBindViewCriteriaMatches(position)) {
            VHBankAccountItem vhBankAccountItem = ((VHBankAccountItem) holder);
            handleBindView(position, vhBankAccountItem);
        } else {
            super.onBindViewHolder(holder, position);
        }
    }

    private boolean isBindViewCriteriaMatches(int position) {
        return position > 0 && (position - 1) < mNbRecyclerViewBaseDataModelList.size()
                && mNbRecyclerViewBaseDataModelList.get(position - 1) instanceof BankAccountViewDataModel;
    }

    private void handleBindView(int position, VHBankAccountItem vhBankAccountItem) {

        BankAccountViewDataModel bankAccountViewDataModel = (BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(position - 1);

        if (vhBankAccountItem.accountNumber != null) {
            vhBankAccountItem.accountNumber.setText(bankAccountViewDataModel.getAccountNumber(), isEditable());
        }

        String bankName = bankAccountViewDataModel.getBankName();
        vhBankAccountItem.bankName.setText(bankName);
        if (StringUtils.isNullOrEmpty(bankName)) {
            vhBankAccountItem.bankName.getPickerTextField().setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_NO);
        } else {
            vhBankAccountItem.bankName.getPickerTextField().setImportantForAccessibility(View.IMPORTANT_FOR_ACCESSIBILITY_YES);
            vhBankAccountItem.bankName.getPickerTextField().setContentDescription(StringUtils.convertStringToTitleCase(bankName));
        }
        if (vhBankAccountItem.recipientRef != null) {
            vhBankAccountItem.recipientRef.setText(bankAccountViewDataModel.getRecipientRef(), isEditable());
        }
        if (vhBankAccountItem.yourRef != null) {
            vhBankAccountItem.yourRef.setText(bankAccountViewDataModel.getYourRef(), isEditable());
        }
        vhBankAccountItem.ivRecipientTypeIcon.setVisibility(isEditable() ? View.GONE : View.VISIBLE);

        if (vhBankAccountItem.accountNumber != null) {
            vhBankAccountItem.accountNumber.setFocusable(isEditable());
            vhBankAccountItem.accountNumber.setEnabled(isEditable());
        }
        vhBankAccountItem.bankName.setFocusable(isEditable());
        if (vhBankAccountItem.recipientRef != null) {
            vhBankAccountItem.recipientRef.setFocusable(isEditable());
            vhBankAccountItem.recipientRef.setEnabled(isEditable());
        }
        if (vhBankAccountItem.yourRef != null) {
            vhBankAccountItem.yourRef.setFocusable(isEditable());
            vhBankAccountItem.yourRef.setEnabled(isEditable());
        }
        vhBankAccountItem.bankName.setEnabled(isEditable());
        vhBankAccountItem.branchCodeSelector.setEnabled(isEditable());
        vhBankAccountItem.accountTypeSelector.setEnabled(isEditable());

        if (isEditable()) {
            handleBindViewEditable(bankAccountViewDataModel, vhBankAccountItem);
        } else {
            handleBindViewNotEditable(vhBankAccountItem);
        }

        if (BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF.equalsIgnoreCase(bankAccountViewDataModel.getBeneficiaryType())) {
            handleBeneficaryTypeBDF(vhBankAccountItem,bankAccountViewDataModel);
        } else {
            handleBeneficaryTypeNotBDF(position,vhBankAccountItem);
        }
        vhBankAccountItem.llRootView.setViewGroupClickListener(mItemSelectedListener != null ? mViewInterceptListener : null);
        vhBankAccountItem.addListenerForRecipientReference(vhBankAccountItem.recipientRef);
        vhBankAccountItem.addListenerForAccountNumber(vhBankAccountItem.accountNumber);
        vhBankAccountItem.addListenerForYourReference(vhBankAccountItem.yourRef);

        vhBankAccountItem.ivRemove.setOnClickListener(v -> vhBankAccountItem.onClickOfRemoveImageView());
        vhBankAccountItem.llRootView.setOnClickListener(v -> vhBankAccountItem.handleItemSelected());
        vhBankAccountItem.branchCodeSelector.setOnClickListener(v -> vhBankAccountItem.handleBranchCodeClick());

        vhBankAccountItem.accountTypeSelector.setOnClickListener(v -> vhBankAccountItem.handleAccountTypeClick());
        vhBankAccountItem.bankName.setOnClickListener(v -> vhBankAccountItem.handleBankNameClick());
        handleMatchBackNumber(vhBankAccountItem, bankAccountViewDataModel);
    }

    private void handleMatchBackNumber(VHBankAccountItem vhBankAccountItem, BankAccountViewDataModel bankAccountViewDataModel) {
        if (bankAccountViewDataModel.getMatchBackNumber() == 0 && mIActivityAdapterComListener!=null) {
            bankAccountViewDataModel.setMatchBackNumber(mIActivityAdapterComListener.getMatchBackNumber());
        } else  {
            checkForMatchBackNumberError(vhBankAccountItem.accountNumber, bankAccountViewDataModel.getMatchBackNumber());
        }
    }

    private void handleBeneficaryTypeNotBDF(int position, VHBankAccountItem vhBankAccountItem) {

        vhBankAccountItem.bankName.setVisibility(View.VISIBLE);
        if (vhBankAccountItem.accountNumber != null) {
            vhBankAccountItem.accountNumber.setVisibility(View.VISIBLE);
        }
        if (vhBankAccountItem.yourRef != null) {
            vhBankAccountItem.yourRef.setVisibility(View.VISIBLE);
        }
        vhBankAccountItem.branchCodeSelector.setVisibility(View.VISIBLE);
        vhBankAccountItem.ivRecipientTypeIcon.setImageResource(R.drawable.ic_bank_recipient_detail);
        checkAndShowBranch(position, vhBankAccountItem);
        checkAndShowAccountType(position, vhBankAccountItem);
    }

    private void handleBeneficaryTypeBDF(VHBankAccountItem vhBankAccountItem,
                                         BankAccountViewDataModel bankAccountViewDataModel) {
        vhBankAccountItem.accountTypeSelector.setVisibility(View.GONE);
        if (vhBankAccountItem.accountNumber != null) {
            vhBankAccountItem.accountNumber.setVisibility(View.GONE);
        }
        vhBankAccountItem.branchCodeSelector.setVisibility(View.GONE);
        vhBankAccountItem.ivRecipientTypeIcon.setImageResource(R.drawable.ic_bank_approved_beneficiary_green);

        if (isEditable()) {
            vhBankAccountItem.ivRemove.setVisibility(View.GONE);
            vhBankAccountItem.bankName.setVisibility(View.GONE);
            if (vhBankAccountItem.yourRef != null) {
                vhBankAccountItem.yourRef.setVisibility(View.VISIBLE);
            }
        } else {
            //On Recipient detail screen (In non editable mode) bank type icon needs to be
            //display against reference, hence reused bank name label to show ref with icon
            //to avoid UI impact on entire screen.
            vhBankAccountItem.bankName.setVisibility(View.VISIBLE);
            if (vhBankAccountItem.yourRef != null) {
                vhBankAccountItem.yourRef.setVisibility(View.GONE);
            }
            vhBankAccountItem.bankName.setText(bankAccountViewDataModel.getYourRef());
        }
    }

    private void handleBindViewNotEditable(VHBankAccountItem vhBankAccountItem) {
        if (vhBankAccountItem.accountNumber != null) {
            vhBankAccountItem.accountNumber.getInputField().setTransformationMethod(null);
            vhBankAccountItem.accountNumber.setBackgroundColor(ContextCompat.getColor(vhBankAccountItem.accountNumber.getContext(), android.R.color.transparent));
        }
        vhBankAccountItem.bankName.setBackgroundColor(ContextCompat.getColor(vhBankAccountItem.bankName.getContext(), android.R.color.transparent));
        vhBankAccountItem.branchCodeSelector.setBackgroundColor(ContextCompat.getColor(vhBankAccountItem.branchCodeSelector.getContext(), android.R.color.transparent));
        vhBankAccountItem.accountTypeSelector.setBackgroundColor(ContextCompat.getColor(vhBankAccountItem.accountTypeSelector.getContext(), android.R.color.transparent));
        if (vhBankAccountItem.recipientRef != null) {
            vhBankAccountItem.recipientRef.setBackgroundColor(ContextCompat.getColor(vhBankAccountItem.recipientRef.getContext(), android.R.color.transparent));
        }
        if (vhBankAccountItem.yourRef != null) {
            vhBankAccountItem.yourRef.setBackgroundColor(ContextCompat.getColor(vhBankAccountItem.yourRef.getContext(), android.R.color.transparent));
        }
        vhBankAccountItem.ivRemove.setVisibility(View.GONE);
    }

    private void handleBindViewEditable(BankAccountViewDataModel bankAccountViewDataModel,
                                        VHBankAccountItem vhBankAccountItem) {
        if (!bankAccountViewDataModel.isExistingItem()) {
            vhBankAccountItem.bankName.requestFocus();
            if (bankAccountViewDataModel.isSendAccessibilityEvent() && AccessibilityUtils.isAccessibilityServiceEnabled(mContext)) {
                vhBankAccountItem.bankName.postDelayed(() ->
                        vhBankAccountItem.bankName.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED), 500);
                bankAccountViewDataModel.setSendAccessibilityEvent(false);
            }
        }
        if (vhBankAccountItem.accountNumber != null) {
            vhBankAccountItem.accountNumber.getInputField().setTransformationMethod(new CompatTextView.NBBlockCopyMethod());
        }
        vhBankAccountItem.ivRemove.setVisibility(View.VISIBLE);
    }

    private void checkAndShowAccountType(int position, @NonNull final VHBankAccountItem vhBankAccountItem) {
        BankAccountViewDataModel bankAccountViewDataModel = ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(position - 1));
        if (isEditable() && PaymentsUtility.isNedBank(bankAccountViewDataModel.getBankCode(), bankAccountViewDataModel.getBankName())) {
            vhBankAccountItem.accountTypeSelector.setVisibility(View.VISIBLE);
            vhBankAccountItem.accountTypeSelector.setText(mContext.getString(Constants.ACCOUNT_TYPES.valueOf(bankAccountViewDataModel.getAccountType()).getAccountTypeStringResId()));
            handleBranchFieldVisibility(!AppUtility.isCreditCardAccount(bankAccountViewDataModel.getAccountType()), vhBankAccountItem, bankAccountViewDataModel);
        } else if (!isEditable() && PaymentsUtility.isKnownAccount(bankAccountViewDataModel.getAccountType())) {
            vhBankAccountItem.accountTypeSelector.setVisibility(View.VISIBLE);
            vhBankAccountItem.accountTypeSelector.setText(mContext.getString(Constants.ACCOUNT_TYPES.valueOf(bankAccountViewDataModel.getAccountType()).getAccountTypeStringResId()));
        } else {
            vhBankAccountItem.accountTypeSelector.setVisibility(View.GONE);
        }

    }

    private void checkAndShowBranch(int position, @NonNull final VHBankAccountItem vhBankAccountItem) {
        BankAccountViewDataModel bankAccountViewDataModel = ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(position - 1));
        // if no bank is selected then hide branch name field
        if (!TextUtils.isEmpty(bankAccountViewDataModel.getBankName()) && !bankAccountViewDataModel.getBankName().contentEquals(mSelectBankString)) {
            handleBranchFieldVisibility(TextUtils.isEmpty(bankAccountViewDataModel.getBankUniversalCode()), vhBankAccountItem, bankAccountViewDataModel);
        } else {
            handleBranchFieldVisibility(false, vhBankAccountItem, bankAccountViewDataModel);
        }
    }

    private void handleBranchFieldVisibility(boolean isVisible, @NonNull final VHBankAccountItem vhBankAccountItem, @NonNull final BankAccountViewDataModel bankAccountViewDataModel) {
        // if add/edit recipient flow then isEditable() is true and branch field should be visible
        // only after user has selected bank hence bank code will be available
        if (isEditable() && !TextUtils.isEmpty(bankAccountViewDataModel.getBankCode()) && isVisible) {
            vhBankAccountItem.branchCodeSelector.setVisibility(!PaymentsUtility.isNedBank(bankAccountViewDataModel.getBankCode(), bankAccountViewDataModel.getBankName()) ? View.VISIBLE : View.GONE);
            if (!TextUtils.isEmpty(bankAccountViewDataModel.getBranchCode())) {
                String branchName = StringUtils.EMPTY_STRING;
                if (!TextUtils.isEmpty(bankAccountViewDataModel.getBranchName())) {
                    branchName = StringUtils.HYPHEN + bankAccountViewDataModel.getBranchName();
                }
                vhBankAccountItem.branchCodeSelector.setText(bankAccountViewDataModel.getBranchCode()
                        + branchName);
            } else {
                vhBankAccountItem.branchCodeSelector.setText(mContext.getString(za.co.nedbank.payment.R.string.select_branch));
            }
        } else if (!isEditable()) {
            // recipient detail flow
            vhBankAccountItem.branchCodeSelector.setVisibility(View.GONE);
        } else {
            vhBankAccountItem.branchCodeSelector.setVisibility(View.GONE);
        }
    }

    @Override
    protected void addItem() {
        BankAccountViewDataModel bankAccountViewDataModel = new BankAccountViewDataModel(mSelectBankString);
        mNbRecyclerViewBaseDataModelList.add(mNbRecyclerViewBaseDataModelList.size(), bankAccountViewDataModel);
        notifyItemInserted(mNbRecyclerViewBaseDataModelList.size());
    }

    void onBankSelected(int selectedItemPos, BankViewModel bankViewModel, String selectedAccountTypeCode, int selectedAccountTypeNameResId) {
        int effectiveSelectedPos = selectedItemPos - 1;
        if (effectiveSelectedPos < mNbRecyclerViewBaseDataModelList.size()) {
            ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBankName(bankViewModel.getBankName());
            ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBankCode(bankViewModel.getBankCode());
            if (PaymentsUtility.isNedBank(bankViewModel.getBankCode(), bankViewModel.getBankName())) {
                ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBeneficiaryType(BeneficiaryConstants.BENEFICIARY_TYPE_INTERNAL);
            } else {
                ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBeneficiaryType(BeneficiaryConstants.BENEFICIARY_TYPE_EXTERNAL);
            }
            ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBankUniversalCode(bankViewModel.getUniversalCode());
            ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setAccountType(selectedAccountTypeCode);
            ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setAccountTypeNameResId(selectedAccountTypeNameResId);
            if (PaymentsUtility.isNedBank(bankViewModel.getBankCode(), bankViewModel.getBankName())) {
                // set default branch code and branch name for nedbank.
                ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBranchName(Constants.NEDBANK_DEFAULT_BRANCH_NAME);
                ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBranchCode(Constants.NEDBANK_DEFAULT_BRANCH_CODE);
            } else {
                // reset branch name when a bank other than nedbank is selected
                ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBranchCode(null);
                ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBranchName(null);
            }
            notifyItemChanged(selectedItemPos);
        }
    }

    void onBranchSelected(int selectedItemPos, BankBranchViewModel bankBranchViewModel) {
        int effectiveSelectedPos = selectedItemPos - 1;
        if (effectiveSelectedPos < mNbRecyclerViewBaseDataModelList.size()) {
            ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBranchName(bankBranchViewModel.getBranchName());
            ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setBranchCode(bankBranchViewModel.getBranchCode());
            notifyItemChanged(selectedItemPos);
        }
    }

    void onAccountTypeSelected(int selectedItemPos, AccountType accountType) {
        int effectiveSelectedPos = selectedItemPos - 1;
        if (effectiveSelectedPos < mNbRecyclerViewBaseDataModelList.size()) {
            ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setAccountTypeNameResId(accountType.getAccountTypeResId());
            ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(effectiveSelectedPos)).setAccountType(accountType.getAccountTypeCode());
            notifyItemChanged(selectedItemPos);
        }
    }

    class VHBankAccountItem extends RecyclerView.ViewHolder {

        CompatPicker bankName;
        CompatEditText accountNumber;
        CompatEditText yourRef;
        CompatEditText recipientRef;
        CompatPicker accountTypeSelector;
        ImageView ivRecipientTypeIcon;
        CompatPicker branchCodeSelector;
        ImageView ivRemove;
        CustomLinearLayout llRootView;

        void handleBankNameClick() {
            if (getBindingAdapterPosition() - 1 < mNbRecyclerViewBaseDataModelList.size()) {
                BankAccountViewDataModel bankAccountViewDataModel = (BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1);
                if (mIActivityAdapterComListener != null && (!(mIActivityAdapterComListener instanceof EditRecipientActivity && bankAccountViewDataModel.isExistingItem()) || !bankAccountViewDataModel.isExistingItem())) {
                    mIActivityAdapterComListener.onBankSelected(getBindingAdapterPosition());
                }
            }

        }

        void handleAccountTypeClick() {
            if (mIActivityAdapterComListener != null) {
                mIActivityAdapterComListener.onAccountTypeSelected(getBindingAdapterPosition());
            }


        }

        void handleBranchCodeClick() {
            if ((getBindingAdapterPosition() - 1) < mNbRecyclerViewBaseDataModelList.size()) {
                BankAccountViewDataModel bankAccountViewDataModel = ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1));

                if (mIActivityAdapterComListener != null) {
                    if (bankAccountViewDataModel.isExistingItem()) {
                        BankViewModel bankViewModel = new BankViewModel();
                        bankViewModel.setBankCode(bankAccountViewDataModel.getBankCode());
                        mIActivityAdapterComListener.onSetExistingBankModel(bankViewModel);
                        mIActivityAdapterComListener.onBranchCodeSelected(getBindingAdapterPosition());
                    } else {
                        mIActivityAdapterComListener.onBranchCodeSelected(getBindingAdapterPosition());
                    }
                }

            }
        }

        public void handleItemSelected() {
            if (mItemSelectedListener != null) {
                mItemSelectedListener.itemSelected(VIEW_TYPE.BANK.ordinal(), getBindingAdapterPosition() - 1);
            }
        }

        void addListenerForRecipientReference(CompatEditText compatEdtRecipientReference) {

            RxTextView.textChanges(compatEdtRecipientReference.getInputField()).subscribe(chars -> {
                if (compatEdtRecipientReference.hasError())
                    compatEdtRecipientReference.clearErrors();
                if (!TextUtils.isEmpty(chars) && !StringUtils.blockCharacterPattern.matcher(chars).matches()) {
                    String replacedString = chars.toString().replace("\n", StringUtils.SPACE);
                    replacedString = replacedString.replace("\r", StringUtils.SPACE);
                    ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setRecipientRef(replacedString);
                } else {
                    ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setRecipientRef(chars.toString());
                }
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtRecipientReference.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtRecipientReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });

            RxTextView.editorActions(compatEdtRecipientReference.getInputField())
                    .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                    .subscribe(chars -> {
                        ViewUtils.hideSoftKeyboard(mContext, compatEdtRecipientReference);
                        compatEdtRecipientReference.clearFocus();
                        mIActivityAdapterComListener.validateInput(compatEdtRecipientReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                    }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        }

        void addListenerForYourReference(CompatEditText compatEdtYourReference) {

            RxTextView.textChanges(compatEdtYourReference.getInputField()).subscribe(chars -> {
                if (compatEdtYourReference.hasError()) compatEdtYourReference.clearErrors();
                if (!TextUtils.isEmpty(chars) && !StringUtils.blockCharacterPattern.matcher(chars).matches()) {
                    String replacedString = chars.toString().replace("\n", StringUtils.SPACE);
                    replacedString = replacedString.replace("\r", StringUtils.SPACE);
                    ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setYourRef(replacedString);
                } else {
                    ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setYourRef(chars.toString());
                }
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtYourReference.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtYourReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });

        }

        void addListenerForAccountNumber(CompatEditText compatEdtAccountNumber) {

            RxTextView.textChanges(compatEdtAccountNumber.getInputField()).subscribe(chars -> {
                if (compatEdtAccountNumber.hasError() && !chars.toString().equals(((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).getAccountNumber())) {
                    compatEdtAccountNumber.clearErrors();
                    removeMatchBackNumberFromErrorList( mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1).getMatchBackNumber());
                }
                ((BankAccountViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setAccountNumber(compatEdtAccountNumber.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
                compatEdtAccountNumber.setTag(getBindingAdapterPosition() - 1);
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
            compatEdtAccountNumber.setTag(getBindingAdapterPosition() - 1);
            compatEdtAccountNumber.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtAccountNumber, Validator.ValidatorType.ACCOUNT_NUMBER_VALIDATOR);
                    if (mNbRecyclerViewBaseDataModelList != null && !mNbRecyclerViewBaseDataModelList.isEmpty() && mNbRecyclerViewBaseDataModelList.size() > (Integer) compatEdtAccountNumber.getTag())
                        checkForMatchBackNumberError(compatEdtAccountNumber, mNbRecyclerViewBaseDataModelList.get((Integer) compatEdtAccountNumber.getTag()).getMatchBackNumber());
                }
            });
        }

        void onClickOfRemoveImageView() {
            int pos = getBindingAdapterPosition() - 1;
            if (pos >= 0 && pos < mNbRecyclerViewBaseDataModelList.size()) {
                removeItem(getBindingAdapterPosition(), mNbRecyclerViewBaseDataModelList.get(pos).isExistingItem());
            }
        }

        public VHBankAccountItem(View itemView) {
            super(itemView);
            bankName = itemView.findViewById(R.id.recipient_select_bank_layout);
            accountNumber = itemView.findViewById(R.id.et_account_number);
            yourRef = itemView.findViewById(R.id.et_your_reference);
            recipientRef = itemView.findViewById(R.id.et_recipient_reference);
            accountTypeSelector = itemView.findViewById(R.id.selector_account_type);
            ivRecipientTypeIcon = itemView.findViewById(R.id.iv_recipient_icon);
            branchCodeSelector = itemView.findViewById(R.id.selector_branch_code);
            ivRemove = itemView.findViewById(R.id.iv_remove);
            llRootView = itemView.findViewById(R.id.ll_root_view);
        }

    }

    public interface IActivityAdapterComListener {

        void onBankSelected(int pos);

        void onAccountTypeSelected(int adapterPosition);

        void onBranchCodeSelected(int adapterPosition);

        void onSetExistingBankModel(BankViewModel bankViewModel);

        void validateInput(final ValidatableInput<String> input, Validator.ValidatorType validatorType);

        int getMatchBackNumber();

    }

}
