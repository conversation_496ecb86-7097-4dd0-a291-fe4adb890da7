/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository.money_request;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.ui.data.datastore.ValidateMobileNumberFactory;
import za.co.nedbank.ui.data.entity.money_request.ValidateNumberEntity;
import za.co.nedbank.ui.domain.repository.IValidateNumberRepository;


public class ValidateNumberRepository implements IValidateNumberRepository {

    private final ValidateMobileNumberFactory validateMobileNumberFactory;

    @Inject
    ValidateNumberRepository(final ValidateMobileNumberFactory validateMobileNumberFactory) {
        this.validateMobileNumberFactory = validateMobileNumberFactory;
    }


    @Override
    public Observable<ValidateNumberEntity> validateMobileNumber(String mobileNumber) {
        return validateMobileNumberFactory.validateMobileNumber(mobileNumber);
    }

}
