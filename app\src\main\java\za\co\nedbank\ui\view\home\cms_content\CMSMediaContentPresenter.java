package za.co.nedbank.ui.view.home.cms_content;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.Permission;
import za.co.nedbank.core.domain.model.warning.DataUsageStatus;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.CheckPermissionUseCase;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;

public class CMSMediaContentPresenter extends NBBasePresenter<CMSMediaContentView> {

    private final NavigationRouter mNavigationRouter;
    private final ApplicationStorage applicationStorage;
    private final ApplicationStorage memoryStorage;
    private DataUsageStatus dataUsageStatus = DataUsageStatus.UNKNOWN;
    private final CheckPermissionUseCase checkPermissionUseCase;

    @Inject
    CMSMediaContentPresenter(final NavigationRouter mNavigationRouter, final ApplicationStorage applicationStorage, final @Named("memory") ApplicationStorage memoryStorage, CheckPermissionUseCase checkPermissionUseCase) {
        this.mNavigationRouter = mNavigationRouter;
        this.applicationStorage = applicationStorage;
        this.memoryStorage = memoryStorage;
        this.checkPermissionUseCase = checkPermissionUseCase;
    }

    @Override
    protected void onBind() {
        super.onBind();
        if (view == null)
            return;

        boolean dataUsageAccepted = checkDataUsageAccepted();
        if (!dataUsageAccepted) {
            return;
        }
    }


    public void openDataUsageWarning() {
        if (view == null) {
            return;
        }
        mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.DATA_USAGE_WARNING)
                .withParam(Constants.FROM_SCREEN, Constants.FROM_LATEST_SCREEN_ACTIVITY))
                .subscribe(navigationResult -> {
                    boolean accepted = navigationResult.getBooleanParam(NavigationTarget.RESULT_DATA_USAGE_WARNING_ACCEPT);
                    dataUsageStatus = accepted ? DataUsageStatus.ACCEPTED : DataUsageStatus.DECLINED;
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }


    public boolean checkDataUsageAccepted() {
        boolean dataWarningAcceptedPermanently = applicationStorage.getBoolean(StorageKeys.DATA_USAGE_NEVER_ASK_AGAIN_LATEST_SCREEN, false);
        boolean isDemoMode = memoryStorage.getBoolean(StorageKeys.IS_DEMO, false);
        if (dataWarningAcceptedPermanently || isDemoMode || (view != null && !view.isFromWidget())) {
            return true;
        }

        switch (dataUsageStatus) {
            case UNKNOWN:
                openDataUsageWarning();
                return false;
            case ACCEPTED:
                return true;
            case DECLINED:
                if (view != null) {
                    view.close();
                    view.finishActivity();
                }
                return false;
        }

        return false;
    }


    void checkPermissions() {
        checkPermissionUseCase.execute(Permission.READ_EXTERNAL_STORAGE)
                .compose(bindToLifecycle())
                .subscribe(
                        permissionsGranted -> {
                            if (view != null) {
                                if(permissionsGranted) {
                                    view.openGallery();
                                }else{
                                    view.permissionDeny();
                                }
                            }

                        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage())
                );
    }

    public void navigateToOverview() {
        NavigationTarget target = NavigationTarget.to(NavigationTarget.HOME);
        mNavigationRouter.navigateTo(target);
    }
}
