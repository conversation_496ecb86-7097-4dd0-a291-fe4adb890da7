/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.di;

import android.app.Application;
import android.app.Service;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.di.modules.ActivityModule;
import za.co.nedbank.core.di.modules.ApplicationModule;
import za.co.nedbank.core.di.modules.FragmentModule;
import za.co.nedbank.ui.di.components.AppActivityComponent;
import za.co.nedbank.ui.di.components.AppApplicationComponent;
import za.co.nedbank.ui.di.components.AppFragmentComponent;
import za.co.nedbank.ui.di.components.AppServiceComponent;
import za.co.nedbank.ui.di.components.DaggerAppApplicationComponent;
import za.co.nedbank.ui.di.modules.AppActivityModule;
import za.co.nedbank.ui.di.modules.AppApplicationModule;
import za.co.nedbank.ui.di.modules.AppFragmentModule;
import za.co.nedbank.ui.di.modules.AppServiceModule;

public class AppDI {

    private static AppApplicationComponent applicationComponent;

    public static AppApplicationComponent getApplicationComponent(NBBaseActivity activity) {
        if (applicationComponent == null) {
            applicationComponent = DaggerAppApplicationComponent.builder()
                    .appApplicationModule(new AppApplicationModule(activity.getApplication()))
                    .applicationModule(new ApplicationModule(activity.getApplication()))
                    .build();
        }
        return applicationComponent;
    }


    public static AppActivityComponent getActivityComponent(NBBaseActivity activity) {
        return getApplicationComponent(activity).plus(new AppActivityModule(activity), new ActivityModule(activity));
    }

    public static AppApplicationComponent getApplicationComponent(NBBaseFragment fragment) {
        if (applicationComponent == null) {
            applicationComponent = DaggerAppApplicationComponent.builder()
                    .appApplicationModule(new AppApplicationModule(fragment.getActivity().getApplication()))
                    .applicationModule(new ApplicationModule(fragment.getActivity().getApplication()))
                    .build();
        }
        return applicationComponent;
    }

    public static AppFragmentComponent getFragmentComponent(NBBaseFragment fragment) {
        return getApplicationComponent(fragment).plus(new AppFragmentModule(fragment), new FragmentModule(fragment));
    }

    public static AppApplicationComponent getApplicationComponent(Application application) {
        if (applicationComponent == null) {
            applicationComponent = DaggerAppApplicationComponent.builder()
                    .appApplicationModule(new AppApplicationModule(application))
                    .applicationModule(new ApplicationModule(application))
                    .build();
        }
        return applicationComponent;
    }

    public static AppServiceComponent getServiceComponent(Service service) {
        return getApplicationComponent(service.getApplication()).plus(new AppServiceModule(service));
    }
}
