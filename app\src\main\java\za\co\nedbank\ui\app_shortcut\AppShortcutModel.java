package za.co.nedbank.ui.app_shortcut;

/**
 * Created by ka<PERSON><PERSON><PERSON><PERSON> on 19/01/18.
 */

public class AppShortcutModel {

    private String id;
    private String shortLabel;
    private String longLabel;
    private int iconResId;
    private String intentAction;

    public String getId() {
        return id;
    }

    public String getShortLabel() {
        return shortLabel;
    }

    public void setShortLabel(String shortLabel) {
        this.shortLabel = shortLabel;
    }

    public String getLongLabel() {
        return longLabel;
    }

    public void setLongLabel(String longLabel) {
        this.longLabel = longLabel;
    }

    public int getIconResId() {
        return iconResId;
    }

    public void setIconResId(int iconResId) {
        this.iconResId = iconResId;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIntentAction() {
        return intentAction;
    }

    public void setIntentAction(String intentAction) {
        this.intentAction = intentAction;
    }
}
