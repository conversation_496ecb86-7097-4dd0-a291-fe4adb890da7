/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.recipient_detail;

import android.util.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

import javax.inject.Inject;

import za.co.nedbank.core.domain.mapper.RecipientResponseDataToViewModelMapper;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.response.RecipientResponseData;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.errors.ErrorProvider;
import za.co.nedbank.core.errors.ErrorType;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.KidsProfileUtilsWrapper;
import za.co.nedbank.core.validation.AccountValidator;
import za.co.nedbank.core.validation.CreditCardNumberValidator;
import za.co.nedbank.core.validation.ElectricityMeterNumberValidator;
import za.co.nedbank.core.validation.EmailValidator;
import za.co.nedbank.core.validation.MobileNumberValidator;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.validation.RecipientNameCharValidator;
import za.co.nedbank.core.validation.ReferenceFieldValidator;
import za.co.nedbank.core.validation.ShapIdValidator;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.payment.common.validation.RecipientNameValidator;
import za.co.nedbank.payment.common.view.tracking.PaymentsTracking;
import za.co.nedbank.ui.domain.usecase.GetShapPayDomainUseCase;
import za.co.nedbank.ui.domain.usecase.GetSingleRecipientUseCase;
import za.co.nedbank.ui.view.home.add_recipient.BaseRecipientPresenter;

/**
 * Created by priyadhingra on 9/12/2017.
 */

public class RecipientDetailPresenter extends BaseRecipientPresenter<RecipientDetailView> {

    private final GetSingleRecipientUseCase mSingleRecipientUseCase;
    private final RecipientResponseDataToViewModelMapper mRecipientResponseDataToViewModelMapper;
    private final Analytics mAnalytics;
    private final ErrorProvider mErrorProvider;
    private final KidsProfileUtilsWrapper kidsProfileUtilsWrapper;

    @Inject
    RecipientDetailPresenter(NavigationRouter navigationRouter, RecipientNameValidator recipientNameValidator, RecipientNameCharValidator recipientNameNewValidator,
                             MobileNumberValidator mMobileNumberValidator, NonEmptyTextValidator nonEmptyTextValidator,
                             ReferenceFieldValidator referenceFieldValidator, CreditCardNumberValidator creditCardNumberValidator,
                             AccountValidator accountValidator, ElectricityMeterNumberValidator electricityMeterNumberValidator,
                             EmailValidator emailValidator, ErrorHandler errorHandler,
                             GetSingleRecipientUseCase getSingleRecipientUseCase, final GetShapPayDomainUseCase getFastPayDomainUseCase,
                             RecipientResponseDataToViewModelMapper recipientResponseDataToViewModelMapper, Analytics analytics,
                             ErrorProvider errorProvider, FeatureSetController featureSetController, ShapIdValidator shapIdValidator, final KidsProfileUtilsWrapper kidsProfileUtilsWrapper) {
        super(navigationRouter, recipientNameValidator, recipientNameNewValidator,mMobileNumberValidator, nonEmptyTextValidator, referenceFieldValidator, creditCardNumberValidator, accountValidator, electricityMeterNumberValidator, emailValidator, errorHandler, featureSetController, shapIdValidator, getFastPayDomainUseCase);
        this.mSingleRecipientUseCase = getSingleRecipientUseCase;
        this.mRecipientResponseDataToViewModelMapper = recipientResponseDataToViewModelMapper;
        this.mAnalytics = analytics;
        this.mErrorProvider = errorProvider;
        this.kidsProfileUtilsWrapper = kidsProfileUtilsWrapper;
    }

    @Override
    protected void onBind() {
        if (null != mNavigationResult && mNavigationResult.isOk() && view != null) {
            view.setResult(mNavigationResult.getParams());
        }
    }

    void trackOnPageLoad(int position) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, TrackingEvent.ANALYTICS.VAL_FEATURE_MANAGE_RECIPIENTS);
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_SUBFEATURE, (position == 0) ? TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_VIEW_RECIPIENT : TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_VIEW_RECIPIENT_PAYMENT_HISTORY);
        mAnalytics.sendEventActionWithMap((position == 0) ? PaymentsTracking.RECIPIENTS_DETAILS : PaymentsTracking.RECIPIENTS_HISTORY, cdata);
    }

    void handleEditClick(RecipientViewModel recipientViewModel) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.EDIT_RECIPIENT);
        navigationTarget.withParam(Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL, recipientViewModel);
        mNavigationRouter.navigateWithResult(navigationTarget).subscribe(
                navigationResult -> mNavigationResult = navigationResult
                , throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
        );
    }

    @Override
    protected void preApiCallTasks() {
        if (view != null) {
            if (view instanceof RecipientDetailView) {
                view.setEnabledActivityTouch(false);
                ((RecipientDetailView) view).showProgressBar();
            }
        }
    }

    private String[] getErrors(MetaDataModel metaDataViewModel) {
        int ONLY_SUCCESS_ELEMENT = 1;
        Set<String> errorList = new HashSet<>();
        try {
            ArrayList<ResultDataModel> resultData = metaDataViewModel.getResultData();
            if (resultData != null && !resultData.isEmpty()) {
                for (ResultDataModel resultDataViewModel : resultData) {
                    ArrayList<ResultDetailModel> resultDetailList = resultDataViewModel.getResultDetail();
                    if (resultDetailList != null && !resultDetailList.isEmpty()) {
                        if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                            // success case
                            break;
                        } else {
                            handleError(errorList, resultDetailList);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            NBLogger.d(TAG, ex.getMessage());
            errorList.add(mErrorHandler.getUnknownError());
        }
        return errorList.toArray(new String[errorList.size()]);
    }

    private void handleError(Set<String> errorList, ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(Constants.API_FAILURE)) {
                errorList.add(resultDetailViewModel.getReason());
            }
        }
    }

    void callGetSingleRecipientUseCase(String contactCardId) {
        mSingleRecipientUseCase.execute(contactCardId)
                .doOnSubscribe(disposable -> preApiCallTasks())
                .compose(bindToLifecycle())
                .subscribe(editRecipientResponseData -> {
                    if (null != editRecipientResponseData) {
                        String[] errors = getErrors(editRecipientResponseData.getMetadata());
                        if (errors.length == 0) {
                            if (editRecipientResponseData.getData() != null) {
                                handleSuccess(editRecipientResponseData);
                            } else {
                                handleError(mErrorProvider.getMessage(ErrorType.GENERIC));
                            }

                        } else {
                            hasErrors(errors);
                        }
                    }
                }, throwable -> {
                    NBLogger.e(TAG, Log.getStackTraceString(throwable));
                    handleError(mErrorHandler.getErrorMessage(throwable).getMessage());
                });
    }

    private void handleSuccess(RecipientResponseData editRecipientResponseData) {
        if (view != null) {
            if (view instanceof RecipientDetailView) {
                ((RecipientDetailView) view).onRecipientDetailFetched(mRecipientResponseDataToViewModelMapper.mapEditRecipientResponseDataToRecipientViewModel(editRecipientResponseData.getData()));
            }
        }
    }

    private void hasErrors(String[] errors) {
        if (view != null) {
            if (view instanceof RecipientDetailView) {
                ((RecipientDetailView) view).showGetSingleRecipientApiError(errors);
            }
        }
    }

    private void handleError(String mErrorProvider) {
        if (view != null && (view instanceof RecipientDetailView)) {
                ((RecipientDetailView) view).showGetSingleRecipientApiError(mErrorProvider);

        }
    }

    void handleBackPressed() {
        if (view == null)
            return;
        ((RecipientDetailView) view).finishOnBack();
    }
    // Checks if the user is a kid's profile
    public boolean isKidsProfile() {
        return kidsProfileUtilsWrapper.isUserUnderAged();
    }
}
