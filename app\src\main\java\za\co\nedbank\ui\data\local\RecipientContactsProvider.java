/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.local;

import android.content.ContentProviderClient;
import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.RemoteException;
import android.provider.ContactsContract;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.payment.buy.domain.model.response.UserContactData;


public class RecipientContactsProvider {
    private final Context mContext;

    @Inject
    RecipientContactsProvider(Context context) {
        this.mContext = context;
    }

    public Observable<List<UserContactData>> getUserContacts() throws RemoteException {
        Set<UserContactData> userContactDataSet = new LinkedHashSet<>();
        List<UserContactData> userContactDataList = new ArrayList<>();
        Uri uri = ContactsContract.CommonDataKinds.Phone.CONTENT_URI;
        String sortOrder = ContactsContract.Contacts.DISPLAY_NAME + Constants.ASC;
        ContentResolver cr = mContext.getContentResolver();
        ContentProviderClient mCProviderClient = cr.acquireContentProviderClient(uri);

        if (null != mCProviderClient) {
            String[] phoneNumberProjection = new String[]{ContactsContract.CommonDataKinds.Phone.NUMBER
                    , ContactsContract.Contacts.DISPLAY_NAME};
            Cursor pCur = mCProviderClient.query(uri, phoneNumberProjection, null, null, sortOrder);
            if (null != pCur) {
                while (pCur.moveToNext()) {
                    String phoneNo = pCur.getString(pCur.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER));
                    String name = pCur.getString(pCur.getColumnIndex(ContactsContract.Contacts.DISPLAY_NAME));
                    UserContactData userContactData = new UserContactData();
                    userContactData.setContactName(name);

                    userContactData.setPhoneNumber(FormattingUtil.getFormattedPhoneNumber(phoneNo));
                    userContactDataSet.add(userContactData);
                }
                pCur.close();
            }
        }
        if (null != mCProviderClient) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                mCProviderClient.close();
            } else {
                mCProviderClient.release();
            }
        }
        userContactDataList.addAll(userContactDataSet);
        return Observable.defer(() -> Observable.just(userContactDataList));
    }

}
