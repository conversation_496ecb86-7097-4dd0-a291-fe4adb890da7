package za.co.nedbank.ui.view.home.tax_certificates_accounts;

import static androidx.fragment.app.FragmentStatePagerAdapter.BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT;

import android.os.Bundle;

import androidx.viewpager.widget.ViewPager;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityTaxCertificatesAccountsBinding;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.active_account_tab.ActiveAccountsFragment;
import za.co.nedbank.ui.view.home.close_account_tab.CloseAccountFragment;

public class TaxCertificatesAccountsActivity extends NBBaseActivity implements TaxCertificatesAccountsView {

    @Inject
    TaxCertificatesAccountsPresenter mPresenter;
    TaxCertificatePagerAdapter taxCertificatePagerAdapter;
    private AccountsOverview invetmentAccountsOverview;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityTaxCertificatesAccountsBinding binding = ActivityTaxCertificatesAccountsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        mPresenter.bind(this);
        initToolbar(binding.toolbar, true, true);
        getBundle();
        setupViewPager(binding.viewpager);
        binding.taxCertificatesTabs.setupWithViewPager(binding.viewpager);
    }

    private void setupViewPager(ViewPager viewPager) {
        taxCertificatePagerAdapter = new TaxCertificatePagerAdapter(getSupportFragmentManager(),BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        taxCertificatePagerAdapter.addFragment(ActiveAccountsFragment.newInstance(invetmentAccountsOverview), getString(za.co.nedbank.payment.R.string.active_account_lbl));
        taxCertificatePagerAdapter.addFragment(CloseAccountFragment.newInstance(), getString(za.co.nedbank.payment.R.string.closed_account_lbl));
        viewPager.setAdapter(taxCertificatePagerAdapter);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.unbind();
    }

    public void getBundle() {
        if (getIntent() != null) {
            invetmentAccountsOverview = (AccountsOverview) getIntent().getParcelableExtra(za.co.nedbank.core.Constants.BUNDLE_KEYS.TAX_CERTIFICATES_ACCOUNTS);
        }
    }

}