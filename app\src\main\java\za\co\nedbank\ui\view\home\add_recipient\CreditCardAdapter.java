/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.accessibility.AccessibilityEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.List;

import io.reactivex.annotations.NonNull;
import za.co.nedbank.R;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.sharedui.control.CustomLinearLayout;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewModel;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.view.recipient.CreditCardViewDataModel;
import za.co.nedbank.uisdk.component.CompatEditText;
import za.co.nedbank.uisdk.component.CompatTextView;

/**
 * Created by piyushgupta on 10/06/2017.
 */

public class CreditCardAdapter extends NBFlexibleItemCountRecyclerviewAdapter {

    private BankAccountAdapter.IActivityAdapterComListener mIActivityAdapterComListener;

    public void setIActivityAdapterComListener(BankAccountAdapter.IActivityAdapterComListener iActivityAdapterComListener) {
        mIActivityAdapterComListener = iActivityAdapterComListener;
    }

    public CreditCardAdapter(@NonNull final Context context,
                             @NonNull final NBFlexibleItemCountRecyclerviewModel nbFlexibleItemCountRecyclerviewModel,
                             @NonNull final List<NbRecyclerViewBaseDataModel> nbRecyclerViewBaseDataModelList,
                             final IAdapterInteractionListener adapterInteractionListener,
                             ISectionedListItemSelectedListener itemSelectedListener) {
        super(context, nbFlexibleItemCountRecyclerviewModel, nbRecyclerViewBaseDataModelList, adapterInteractionListener, itemSelectedListener);
    }

    @Override
    public RecyclerView.ViewHolder getItemViewHolder(Context context, ViewGroup parent) {
        View v = LayoutInflater.from(context).inflate(mNBFlexibleItemCountRecyclerviewModel.getItemView(), parent, false);
        VHCreditCardItem vHCreditCardItem = new VHCreditCardItem(v);

        if (vHCreditCardItem.etCardNumber != null) {
            vHCreditCardItem.etCardNumber.getInputField().setContentDescription(" ");
        }
        return vHCreditCardItem;
    }

    @Override
    protected void addItem() {
        CreditCardViewDataModel creditCardViewDataModel = new CreditCardViewDataModel();
        mNbRecyclerViewBaseDataModelList.add(mNbRecyclerViewBaseDataModelList.size(), creditCardViewDataModel);
        notifyItemInserted(mNbRecyclerViewBaseDataModelList.size());
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (position > 0 && (position - 1) < mNbRecyclerViewBaseDataModelList.size() && mNbRecyclerViewBaseDataModelList.get(position - 1) instanceof CreditCardViewDataModel) {
            CreditCardViewDataModel creditCardViewDataModel = (CreditCardViewDataModel) mNbRecyclerViewBaseDataModelList.get(position - 1);
            VHCreditCardItem vhCreditCardItem = ((VHCreditCardItem) holder);
            if (vhCreditCardItem.etCardNumber != null) {
                vhCreditCardItem.etCardNumber.setText(creditCardViewDataModel.getCardNumber(), isEditable());
            }
            if (vhCreditCardItem.yourRef != null) {
                vhCreditCardItem.yourRef.setText(creditCardViewDataModel.getYourRef(), isEditable());
            }
            if (vhCreditCardItem.recipientReference != null) {
                vhCreditCardItem.recipientReference.setText(creditCardViewDataModel.getRecipientRef(), isEditable());
            }
            vhCreditCardItem.ivRecipientTypeIcon.setVisibility(isEditable() ? View.GONE:View.VISIBLE);
            if (isEditable()) {
                if (vhCreditCardItem.etCardNumber != null) {
                    vhCreditCardItem.etCardNumber.getInputField().setTransformationMethod(new CompatTextView.NBBlockCopyMethod());
                    if (!creditCardViewDataModel.isExistingItem() && (mMatchBackNumberErrorMap == null || mMatchBackNumberErrorMap.isEmpty())) {
                        vhCreditCardItem.etCardNumber.requestFocus();
                        if (creditCardViewDataModel.isSendAccessibilityEvent() && AccessibilityUtils.isAccessibilityServiceEnabled(mContext)) {
                            vhCreditCardItem.etCardNumber.postDelayed(() ->
                                    vhCreditCardItem.etCardNumber.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED), 500);
                            creditCardViewDataModel.setSendAccessibilityEvent(false);
                        }
                    }
                }
            } else {
                if (vhCreditCardItem.etCardNumber != null) {
                    vhCreditCardItem.etCardNumber.getInputField().setTransformationMethod(null);
                    vhCreditCardItem.etCardNumber.setBackgroundColor(ContextCompat.getColor(vhCreditCardItem.etCardNumber.getContext(), android.R.color.transparent));
                }
                if (vhCreditCardItem.yourRef != null) {
                    vhCreditCardItem.yourRef.setBackgroundColor(ContextCompat.getColor(vhCreditCardItem.yourRef.getContext(), android.R.color.transparent));
                }
                if (vhCreditCardItem.recipientReference != null) {
                    vhCreditCardItem.recipientReference.setBackgroundColor(ContextCompat.getColor(vhCreditCardItem.recipientReference.getContext(), android.R.color.transparent));
                }

            }
            if (isEditable() && mNBFlexibleItemCountRecyclerviewModel.isDeleteButtonShow()) {
                vhCreditCardItem.ivRemove.setVisibility(View.VISIBLE);
            } else {
                vhCreditCardItem.ivRemove.setVisibility(View.GONE);
            }
            if (vhCreditCardItem.etCardNumber != null) {
                vhCreditCardItem.etCardNumber.setFocusable(isEditable());
                vhCreditCardItem.etCardNumber.setEnabled(isEditable());
            }
            if (vhCreditCardItem.yourRef != null) {
                vhCreditCardItem.yourRef.setFocusable(isEditable());
                vhCreditCardItem.yourRef.setEnabled(isEditable());
            }
            if(vhCreditCardItem.recipientReference!=null) {
                vhCreditCardItem.recipientReference.setFocusable(isEditable());
                vhCreditCardItem.recipientReference.setEnabled(isEditable());
            }
            vhCreditCardItem.llRootView.setViewGroupClickListener(mItemSelectedListener != null ? mViewInterceptListener : null);
            vhCreditCardItem.addListenerForCreditCardNumber(vhCreditCardItem.etCardNumber);
            vhCreditCardItem.addListenerForYourReference(vhCreditCardItem.yourRef);
            vhCreditCardItem.addListenerForRecipientReference(vhCreditCardItem.recipientReference);
            vhCreditCardItem.ivRemove.setOnClickListener(v -> vhCreditCardItem.onClickOfRemoveImageView());
            vhCreditCardItem.llRootView.setOnClickListener(v -> vhCreditCardItem.handleItemSelected());
            if (creditCardViewDataModel.getMatchBackNumber() == 0 && mIActivityAdapterComListener != null) {
                creditCardViewDataModel.setMatchBackNumber(mIActivityAdapterComListener.getMatchBackNumber());
            } else {
                checkForMatchBackNumberError(vhCreditCardItem.etCardNumber, creditCardViewDataModel.getMatchBackNumber());
            }
        } else {
            super.onBindViewHolder(holder, position);
        }
    }

    class VHCreditCardItem extends RecyclerView.ViewHolder {

        CompatEditText etCardNumber;
        ImageView ivRemove;
        CompatEditText yourRef;
        CompatEditText recipientReference;
        ImageView ivRecipientTypeIcon;
        CustomLinearLayout llRootView;

        public void handleItemSelected() {
            if (mItemSelectedListener != null) {
                mItemSelectedListener.itemSelected(VIEW_TYPE.CREDIT_CARD.ordinal(), getBindingAdapterPosition() - 1);
            }
        }

        void onClickOfRemoveImageView() {
            int pos = getBindingAdapterPosition() - 1;
            if (pos >= 0 && pos < mNbRecyclerViewBaseDataModelList.size()) {
                removeItem(getBindingAdapterPosition(), mNbRecyclerViewBaseDataModelList.get(pos).isExistingItem());
            }
        }

        VHCreditCardItem(View itemView) {
            super(itemView);
            etCardNumber = itemView.findViewById(R.id.et_credit_card_number);
            ivRemove = itemView.findViewById(R.id.iv_remove);
            yourRef = itemView.findViewById(R.id.et_your_reference);
            recipientReference = itemView.findViewById(R.id.et_recipient_reference);
            ivRecipientTypeIcon = itemView.findViewById(R.id.iv_recipient_icon);
            llRootView = itemView.findViewById(R.id.ll_root_view);
        }

        void addListenerForCreditCardNumber(CompatEditText compatEdtCreditCardNumber) {

            RxTextView.textChanges(compatEdtCreditCardNumber.getInputField()).subscribe(chars -> {
                if (compatEdtCreditCardNumber.hasError() && !chars.toString().equals(((CreditCardViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).getCardNumber())) {
                    compatEdtCreditCardNumber.clearErrors();
                    removeMatchBackNumberFromErrorList(mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1).getMatchBackNumber());
                }
                ((CreditCardViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setCardNumber(compatEdtCreditCardNumber.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
                compatEdtCreditCardNumber.setTag(getBindingAdapterPosition() - 1);
            }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtCreditCardNumber.setTag(getBindingAdapterPosition() - 1);
            compatEdtCreditCardNumber.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtCreditCardNumber, Validator.ValidatorType.CARD_NUMBER_VALIDATOR);
                    if (mNbRecyclerViewBaseDataModelList != null && !mNbRecyclerViewBaseDataModelList.isEmpty() && mNbRecyclerViewBaseDataModelList.size() > (Integer) compatEdtCreditCardNumber.getTag())
                        checkForMatchBackNumberError(compatEdtCreditCardNumber, mNbRecyclerViewBaseDataModelList.get((Integer) compatEdtCreditCardNumber.getTag()).getMatchBackNumber());
                }
            });

        }

        void addListenerForYourReference(CompatEditText compatEdtYourReference) {

            RxTextView.textChanges(compatEdtYourReference.getInputField()).subscribe(chars -> {
                compatEdtYourReference.clearErrors();
                ((CreditCardViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setYourRef(compatEdtYourReference.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtYourReference.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtYourReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });

        }

        void addListenerForRecipientReference(CompatEditText compatEdtRecipientReference) {

            RxTextView.textChanges(compatEdtRecipientReference.getInputField()).subscribe(chars -> {
                compatEdtRecipientReference.clearErrors();
                ((CreditCardViewDataModel) mNbRecyclerViewBaseDataModelList.get(getBindingAdapterPosition() - 1)).setRecipientRef(compatEdtRecipientReference.getValue());
                if (mINBRecyclerViewListener != null) {
                    mINBRecyclerViewListener.onItemDataChange();
                }
            },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

            compatEdtRecipientReference.setOnFocusChangeListener((v, hasFocus) -> {
                if (!hasFocus && mIActivityAdapterComListener != null) {
                    mIActivityAdapterComListener.validateInput(compatEdtRecipientReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                }
            });

            RxTextView.editorActions(compatEdtRecipientReference.getInputField())
                    .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                    .subscribe(chars -> {
                        ViewUtils.hideSoftKeyboard(mContext, compatEdtRecipientReference);
                        compatEdtRecipientReference.clearFocus();
                        mIActivityAdapterComListener.validateInput(compatEdtRecipientReference, Validator.ValidatorType.REFERENCE_VALIDATOR);
                    },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        }

    }
}
