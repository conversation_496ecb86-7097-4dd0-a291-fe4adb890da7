package za.co.nedbank.ui.view.card_delivery.deliver_to_me_confirmation;

import android.os.Bundle;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.databinding.ActivityDeliverToMeConfirmationBinding;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConstants;

public class DeliverToMeConfirmationActivity extends NBBaseActivity implements DeliverToMeConfirmationView {

    @Inject
    DeliverToMeConfirmationPresenter presenter;
    private ActivityDeliverToMeConfirmationBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDeliverToMeConfirmationBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        presenter.bind(this);
        presenter.sendPageAnalytics();
        presenter.callConfirmationAPI();
    }

    @Override
    public String getFlow() {
        return getIntent().getStringExtra(NavigationTarget.PARAM_CARD_DELIVERY_FLOW);
    }

    @Override
    public String getCardActionName() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_ACTION_NAME);
    }

    @Override
    public String getCardDeliverySubFeature() {
        return getIntent().getStringExtra(CardDeliveryConstants.EXTRA_SELECT_CARD_DELIVERY_OPTION_NAME);
    }

    @Override
    public void showConfirmButtonLoading(boolean isLoading) {
        if (isLoading) {
            binding.progressBar.show();
        } else {
            binding.progressBar.hide();
        }
    }

    @Override
    protected void onDestroy() {
        presenter.unbind();
        super.onDestroy();
    }

    @Override
    public String getCardPlasticId() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_PLASTIC_ID);
    }
}