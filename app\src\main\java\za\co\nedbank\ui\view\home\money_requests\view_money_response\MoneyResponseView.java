/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.validation.ValidationResult;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.payment.pay.view.model.PaymentViewModel;
import za.co.nedbank.payment.transfer.view.model.response.LimitsViewModel;

/**
 * Created by devrath.rathee on 2/20/2018.
 */

public interface MoneyResponseView extends NBBaseView {

    void showError(String error);

    void setAccounts();

    void setNextButtonEnabled(final boolean isEnabled);

    void showNotificationOptions();

    void setNotificationType(String type);

    void setYourReference(String reference);

    void setRequestedAmount(double amount);

    double getRequestedAmount();

    String amountToPay();

    String getReceiverName();

    String getReceiverMobileNumber();

    long getPaymentRequestId();

    String getReceiverDescription();

    String getReceiverAccountNumber();

    String getReceiverAccountType();

    String getYourReference();

    String getRecipientReference();

    String getNotificationType();

    void setPaymentDetails(PaymentViewModel paymentViewModel);

    PaymentViewModel getPaymentModel();

    AccountViewModel provideSelectedAccount();

    void setAccountContainerList(List<AccountViewModel> accountContainerList);

    void setLimits(LimitsViewModel limitsViewModel);

    void setAccountEnabled(boolean isEnabled);

    void showEmailError(ValidationResult result);

    void showMobileNumberError(ValidationResult result);

    String getNotificationMobileNumber();

    String getNotificationEmailAddress();

    void trackFetchAccountsFailure();

    void trackLimitsFailure();
}
