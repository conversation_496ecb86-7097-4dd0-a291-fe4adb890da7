package za.co.nedbank.ui.app_shortcut.data;

import android.content.Context;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observable;
import za.co.nedbank.R;
import za.co.nedbank.ui.app_shortcut.AppShortCutConstants;
import za.co.nedbank.ui.app_shortcut.AppShortcutModel;
import za.co.nedbank.ui.app_shortcut.domain.DynamicShortcutRepository;

/**
 * Created by ka<PERSON><PERSON><PERSON><PERSON> on 19/01/18.
 */

public class DynamicAppShortcutRepository implements DynamicShortcutRepository {
    private final Context mContext;

    public DynamicAppShortcutRepository(Context context) {
        this.mContext = context;
    }

    @Override
    public Observable<List<AppShortcutModel>> getAppShortcutList() {
        List<AppShortcutModel> appShortcutModels = new ArrayList<>();
        AppShortcutModel scanPayShortcut = new AppShortcutModel();
        scanPayShortcut.setId(AppShortCutConstants.SCAN_PAY_ID);
        scanPayShortcut.setShortLabel(mContext.getResources().getString(R.string.app_shortcut_scan_pay));
        scanPayShortcut.setLongLabel(mContext.getResources().getString(R.string.app_shortcut_scan_pay));
        scanPayShortcut.setIconResId(R.drawable.circle_blank_icon);
        scanPayShortcut.setIntentAction(AppShortCutConstants.ACTION_SCAN_PAY);
        appShortcutModels.add(scanPayShortcut);
        return Observable.just(appShortcutModels);
    }
}
