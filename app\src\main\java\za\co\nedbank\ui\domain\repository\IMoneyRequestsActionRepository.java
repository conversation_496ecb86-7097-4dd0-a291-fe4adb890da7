/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository;

import io.reactivex.Observable;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsActionResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentActionRequestEntity;

/**
 * Created by swapnil.gawande on 2/25/2018.
 */

public interface IMoneyRequestsActionRepository {
    Observable<MoneyRequestsActionResponseEntity> paymentRequestsAction(PaymentActionRequestEntity paymentActionRequestEntity);
}
