package za.co.nedbank.ui.view.sales_latest;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.common.CardType;
import za.co.nedbank.core.common.FeatureCardEnum;
import za.co.nedbank.core.common.ProductType;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.media_content.MediaContentAdapter;
import za.co.nedbank.core.view.media_content.MediaContentClickListener;
import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.databinding.FragmentSalesLatestBinding;
import za.co.nedbank.ui.di.AppDI;

public class SalesLatestFragment extends NBBaseFragment implements SalesLatestView, MediaContentClickListener {


    @Inject
    SalesLatestPresenter mPresenter;

    @Inject
    FeatureSetController mFeatureSetController;
    private List<AppLayoutViewModel> mMediaContentList = new ArrayList<>();
    private MediaContentAdapter mAdapter;
    private FragmentSalesLatestBinding binding;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentSalesLatestBinding.inflate(inflater, container, false);
        setShimmerView();
        setUpRecyclerView();
        mPresenter.bind(this);
        mPresenter.loadMediaContent();
        return binding.getRoot();
    }

    private void setUpRecyclerView() {
        binding.rvAppbarContent.setHasFixedSize(true);
        binding.rvAppbarContent.setLayoutManager(new LinearLayoutManager(getActivity()));
        binding.rvAppbarContent.setItemAnimator(null);
        mAdapter = new MediaContentAdapter(getActivity(), this, mMediaContentList, DeviceUtils.getDeviceWidth(getActivity()), false, false, mFeatureSetController);
        binding.rvAppbarContent.setAdapter(mAdapter);
    }

    private void setShimmerView() {
        ViewUtils.setHeightAsPerAspectRatio(binding.sdLandingShimmerContainer.ivShimmerMediaCardImage, MediaContentAdapter.aspectW, MediaContentAdapter.aspectH, DeviceUtils.getDeviceWidth(getActivity()));
    }

    @Override
    public void showProgress(boolean progress) {
        if (progress && CollectionUtils.isEmpty(mMediaContentList)) {
            binding.sdLandingShimmerContainer.parentShimmer.setVisibility(View.VISIBLE);
        } else {
            binding.sdLandingShimmerContainer.parentShimmer.setVisibility(View.GONE);
        }
    }

    @Override
    public void showMediaAndOfferCards(List<AppLayoutViewModel> mediaContentList) {
        ViewUtils.hideViews(binding.sdLandingShimmerContainer.parentShimmer);
        ViewUtils.showViews(binding.rvAppbarContent);

        Iterator<AppLayoutViewModel> iterator = mediaContentList.iterator();
        while(iterator.hasNext()){
            AppLayoutViewModel model = iterator.next();
            if(model.getCardType() == CardType.NEDBANK_PRODUCTS) {
                iterator.remove();
            }
        }
        mMediaContentList.clear();
        mMediaContentList.addAll(mediaContentList);
        mAdapter.notifyDataSetChanged();
    }


    @Override
    public void onClickMediaContentItem(MediaCardViewModel mediaCardViewModel, int position) {
        mPresenter.onClickContentItem(mediaCardViewModel);
        position++;
        if (mediaCardViewModel != null && StringUtils.isNotEmpty(mediaCardViewModel.getName())) {
            mPresenter.logMediaCardClickEvent(mediaCardViewModel.getName(), position);
        }
    }

    @Override
    public void onClickProductOfferItem(ProductType productType) {
        if (ProductType.BANK == productType) {
            mPresenter.navigateToBankLayout();
        } else if (ProductType.LOAN == productType) {
            mPresenter.navigateToLoanLayout();
        } else if (ProductType.INVESTMENT == productType) {
            mPresenter.navigateToInvestLayout();
        } else if (ProductType.INSURANCE == productType) {
            mPresenter.navigateToInsureLayout();
        } else if (ProductType.FINANCIAL_PLANNER == productType) {
            mPresenter.navigateToFinanceLayout();
        }
    }

    @Override
    public void onClickFeatureCardItem(FeatureCardEnum featureCardEnum) {
        //No implementation for now.
    }

    @Override
    public void onClickFeatureCardItem(MediaCardViewModel featureCardItemViewModel) {
        //No implementation for now.
    }

    public static SalesLatestFragment getInstance() {
        return new SalesLatestFragment();
    }

}
