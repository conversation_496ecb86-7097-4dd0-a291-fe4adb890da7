package za.co.nedbank.ui.view.home.latest.latestWidget;

import java.util.HashMap;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;

public class LatestWidgetPresenter extends NBBasePresenter<LatestWidgetView> {

    private final NavigationRouter mNavigationRouter;
    private final Analytics mAnalytics;

    @Inject
    LatestWidgetPresenter(final NavigationRouter mNavigationRouter, final Analytics analytics) {
        this.mNavigationRouter = mNavigationRouter;
        this.mAnalytics = analytics;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

    public void onChatMenuClicked(String name) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        if (name != null)
            adobeContextData.setChatContext(TrackingEvent.ANALYTICS.CONSTANT_WIDGET + name);
        mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.TAG_WIDGET_LATEST_CHAT, cdata);
    }
}