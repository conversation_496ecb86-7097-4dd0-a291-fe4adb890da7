package za.co.nedbank.ui.view.pay_me;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;

public class PayMePresenter extends NBBasePresenter<PayMeView> {

    private final NavigationRouter mNavigationRouter;

    @Inject
    PayMePresenter(final NavigationRouter mNavigationRouter) {
        this.mNavigationRouter = mNavigationRouter;
    }
}
