package za.co.nedbank.ui.view.pay_me;

import android.os.Bundle;

import org.greenrobot.eventbus.EventBus;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.databinding.ActivityPayMeBinding;
import za.co.nedbank.ui.di.AppDI;

public class PayMeActivity extends NBBaseActivity implements PayMeView {

    @Inject
    PayMePresenter mPayMePresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        super.onCreate(savedInstanceState);
        ActivityPayMeBinding binding = ActivityPayMeBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        mPayMePresenter.bind(this);

        initToolbar(binding.toolbar, true, getResources().getString(R.string.pay_me));
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
    }
}