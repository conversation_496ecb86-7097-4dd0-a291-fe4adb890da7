/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.pop;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryParentData;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryRequestData;
import za.co.nedbank.ui.domain.repository.pop.TransactionRepository;


public class GetTransactionHistoryUseCase extends UseCase<TransactionHistoryRequestData,TransactionHistoryParentData> {

    private TransactionRepository transactionRepository;

    @Inject
    GetTransactionHistoryUseCase(final UseCaseComposer useCaseComposer, final TransactionRepository transactionRepository) {
        super(useCaseComposer);
        this.transactionRepository = transactionRepository;
        setCacheObservable(false);
    }

    @Override
    protected Observable<TransactionHistoryParentData> createUseCaseObservable(TransactionHistoryRequestData param) {
        int contactCardId = param.getContactCardId();
        String pageSize = param.getPageSize();
        String page = param.getPage();
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        return transactionRepository.getTransactionHistory(contactCardId, pageSize, page, startDate, endDate);
    }
}
