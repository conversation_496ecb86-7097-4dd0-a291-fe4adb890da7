/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import androidx.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;


/**
 * Created by swapnil.gawande on 2/25/2018.
 */

@StringDef({PaymentRequestActionType.PAY_NOW, PaymentRequestActionType.PAY_LATER, PaymentRequestActionType.REJECT, PaymentRequestActionType.REMIND})
@Retention(RetentionPolicy.RUNTIME)
public @interface PaymentRequestActionType {
    String PAY_NOW = "AUT";
    String PAY_LATER = "PLT";
    String REJECT = "DEC";
    String REMIND = "REM";
}
