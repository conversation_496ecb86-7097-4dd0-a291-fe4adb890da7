/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data;

import java.util.LinkedHashMap;
import java.util.Map;

import io.reactivex.Observable;
import za.co.nedbank.ui.domain.repository.IFinanceDataRepository;

/**
 * Created by piyushgupta01 on 7/20/2017.
 */

public class NBFinanceDataRepository implements IFinanceDataRepository {
    @Override
    public Observable<Map<String, Float>> getIncome() {
        //TODO API call goes here
        return Observable.defer(() -> Observable.just(getIncomeData()));
    }

    @Override
    public Observable<Map<String, Float>> getExpense() {
        //TODO API call goes here
        return Observable.defer(() -> Observable.just(getExpenseData()));
    }

    private Map<String, Float> getIncomeData() {
        Map<String, Float> map = new LinkedHashMap<>();
        map.put("Feb", 33213f);
        map.put("Mar", 33213f);
        map.put("Apr", 33213f);
        map.put("May", 23213f);
        map.put("Jun", 23213f);
        map.put("Jul", 73213f);
        return map;
    }

    private Map<String, Float> getExpenseData() {
        Map<String, Float> map = new LinkedHashMap<>();
        map.put("Feb", 13213f);
        map.put("Mar", 13213f);
        map.put("Apr", 13213f);
        map.put("May", 3213f);
        map.put("Jun", 13213f);
        map.put("Jul", 53213f);
        return map;
    }
}
