/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository.money_request;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.ui.data.entity.money_request.NotificationsResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentRequestEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentResponseEntity;
import za.co.nedbank.ui.data.networking.MoneyRequestAPI;
import za.co.nedbank.ui.domain.repository.IMoneyRequestRepository;

/**
 * Created by sandip.lawate on 2/22/2018.
 */

public class MoneyRequestRepository implements IMoneyRequestRepository {


    private final NetworkClient mNetworkClient;

    @Inject
    MoneyRequestRepository(NetworkClient networkClient) {
        this.mNetworkClient = networkClient;
    }

    @Override
    public Observable<PaymentResponseEntity> sendPaymentsRequest(PaymentRequestEntity paymentRequestEntity) {
        return mNetworkClient.create(MoneyRequestAPI.class)
                .sendPaymentRequest(paymentRequestEntity);
    }

    @Override
    public Observable<NotificationsResponseEntity> getNotifications() {
        return mNetworkClient.create(MoneyRequestAPI.class)
                .getNotifications();
    }

}
