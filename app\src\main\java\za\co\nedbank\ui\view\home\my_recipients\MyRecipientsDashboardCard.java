/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients;

import android.view.View;

import za.co.nedbank.core.dashboard.DashboardCard;
import za.co.nedbank.core.dashboard.DashboardCardType;

/**
 * Created by ch<PERSON><PERSON> on 08-09-2017.
 */

public class MyRecipientsDashboardCard extends DashboardCard {

    public MyRecipientsDashboardCard(DashboardCardType cardType, View content) {
        super(cardType, content);
    }

    @Override
    public boolean shouldReloadContent() {
        return false;
    }
}
