package za.co.nedbank.ui.data.entity.pop;


import com.squareup.moshi.Json;

public class ShareProofOfPaymentRequestEntity {
    @Json(name = "notificationType")
    private String notificationType;
    @Json(name = "notificationAddress")
    private String notificationAddress;

    public String getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }

    public String getNotificationAddress() {
        return notificationAddress;
    }

    public void setNotificationAddress(String notificationAddress) {
        this.notificationAddress = notificationAddress;
    }
}
