package za.co.nedbank.ui.view.home.active_account_tab;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.databinding.ActiveAccountsFragmentBinding;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.di.AppDI;

public class ActiveAccountsFragment extends NBBaseFragment implements ActiveAccountsView, ActiveAccountAdapter.IViewHolderInteraction {

    @Inject
    ActiveAccountsPresenter mPresenter;
    static AccountsOverview investmentAccountsOverview;

    @Inject
    FeatureSetController mFeatureSetController;
    List<AccountSummary> productCategoriesAccountSummaries = new ArrayList<>();
    private ActiveAccountsFragmentBinding binding;

    public static ActiveAccountsFragment newInstance(AccountsOverview invetmentAccountsOverview) {
        ActiveAccountsFragment mFragment = new ActiveAccountsFragment();
        ActiveAccountsFragment.investmentAccountsOverview = invetmentAccountsOverview;
        return mFragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = ActiveAccountsFragmentBinding.inflate(inflater, container, false);
        initAdapter();
        return binding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();
        mPresenter.onActiveAccountsFragmentVisible();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        mPresenter.unbind();
    }


    public void initAdapter() {
        if (investmentAccountsOverview != null && investmentAccountsOverview.accountSummaries != null
                && !investmentAccountsOverview.accountSummaries.isEmpty()) {
            getProductAccountSummaries();
        }
    }

    private void getProductAccountSummaries() {
        boolean showCategoryDashboard = !mFeatureSetController.isFeatureDisabled(FeatureConstants.INVESTMENT_ONLINE_TP_DASHBOARD_CATEGORY);
        productCategoriesAccountSummaries.clear();
        if (showCategoryDashboard) {
            for (AccountSummary accountSummary : investmentAccountsOverview.accountSummaries) {
                onGetProductCategory(accountSummary);
            }
        } else {
            productCategoriesAccountSummaries = investmentAccountsOverview.accountSummaries;
        }

        Collections.sort(productCategoriesAccountSummaries, (obj1, obj2) ->
                Integer.compare((int) obj2.getAvailableBalance(), (int) obj1.getAvailableBalance()));
        ActiveAccountAdapter activeAccountAdapter = new ActiveAccountAdapter(this, productCategoriesAccountSummaries);
        binding.accountRecycler.setAdapter(activeAccountAdapter);

    }

    public void onGetProductCategory(AccountSummary accountSummary) {
        if (accountSummary.isIsProductCategory() && accountSummary.getProductCategory() != null) {
            for (AccountSummary mAccountSummary : accountSummary.getProductCategory().getAccountSummaries()) {
                if (mAccountSummary.getAccountCode().equals(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_DS)) {
                    productCategoriesAccountSummaries.add(mAccountSummary);
                }
            }
        }
    }

    @Override
    public void onAccountSelected(AccountSummary accountSummary) {
        mPresenter.navigateToDownloadStatement(accountSummary);
    }
}