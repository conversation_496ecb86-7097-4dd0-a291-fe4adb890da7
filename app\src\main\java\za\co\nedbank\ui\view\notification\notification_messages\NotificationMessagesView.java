package za.co.nedbank.ui.view.notification.notification_messages;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;

public interface NotificationMessagesView extends NBBaseView {

    void showProgress(boolean isVisible);

    void showErrorForNotificationMessages(String message);

    void updateNotificationMessages(List<FBNotificationsViewModel> notificationsViewModels);

    void clearDeletedItems();

    void endSelection();

    void onNotificationReceived(FBNotificationsViewModel fbNotificationsViewModel);

    void toggleEmptyView();

    void removeDeletedItem(FBNotificationsViewModel notificationsViewModel);

    void showUndoDeleteOption(int noOfDeletedMessages);

    void restoreDeletedItems();

    String getDeleteErrorMsg();

    void showListLoaded();

    void updateNotificationMessagesPageData(List<FBNotificationsViewModel> items);

    void showFullScreenError();

    void markReadNotification(int notificationId);

    void refreshItem(FBNotificationsViewModel fbNotificationsViewModel);

    boolean isUndoTimerComplete();

    void setDeleteSuccess(boolean isDeleteSuccess);

    boolean isDeleteSuccess();
}
