/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.payment.pay.view.model.PaymentViewModel;

/**
 * Created by devrath.rathee on 2/26/2018.
 */

public interface MoneyResponseSuccessView extends NBBaseView {
    String getReceiverMobile();

    PaymentViewModel getPaymentViewModel();

    void finishScreen();

    void setPaymentSuccessData(PaymentViewModel paymentViewModel, String receiverPhoneNumber);
}
