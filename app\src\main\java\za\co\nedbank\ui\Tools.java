package za.co.nedbank.ui;

import android.annotation.TargetApi;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;

import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import za.co.nedbank.R;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.DeviceUtils;


public class Tools {
    private static final int NOTIFICATION_ID = 0;
    public static final String DEFAULT_CHANNEL_ID = "default_channel";


    /**
     * Creates a system notification
     */
    void showNotification(Context context, String title, String content) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            setupDefaultNotificationChannel(context);
        }
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, DEFAULT_CHANNEL_ID)
                .setSmallIcon(getSmallNotificationIcon())
                .setContentTitle(title)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(content))
                .setContentText(content)
                .setTicker(content)
                .setAutoCancel(true)
                .setDefaults(Notification.DEFAULT_ALL)
                .setContentIntent(getPendingIntent(context, NotificationConstants.ACTIONS.DEFAULT_NOTIFICATION_NAVIGATION));
        NotificationManagerCompat.from(context).notify(NOTIFICATION_ID, builder.build());
    }

    private PendingIntent getPendingIntent(Context context, String action) {
        Intent intent = new Intent(context, ITANavigationService.class);
        intent.setAction(action);
        if ((Build.VERSION.SDK_INT < Build.VERSION_CODES.M))
            return PendingIntent.getService(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        return PendingIntent.getService(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);
    }

    @TargetApi(Build.VERSION_CODES.O)
    private void setupDefaultNotificationChannel(Context context) {
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        String channelName = "Channel Name";
        int importance = NotificationManager.IMPORTANCE_HIGH;
        NotificationChannel notificationChannel = new NotificationChannel(DEFAULT_CHANNEL_ID, channelName, importance);
        notificationChannel.setDescription("Description of the channel");
        notificationChannel.enableLights(true);
        notificationChannel.setLightColor(Color.BLUE);
        notificationChannel.enableVibration(true);
        notificationChannel.setShowBadge(true);
        notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE);
        notificationManager.createNotificationChannel(notificationChannel);
    }

    public void dismissNotification(Context context) {
        NotificationManagerCompat.from(context).cancel(NOTIFICATION_ID);
    }


    public int getSmallNotificationIcon() {
        if ((android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP)) {
            return R.drawable.ic_oreo_notification_wrapper;
        } else {
            return getNotificationIcon();
        }
    }

    private int getNotificationIcon() {
        if (DeviceUtils.isBuildTypeDebug()) {
            return R.mipmap.ic_launcher_debug;
        } else if (DeviceUtils.isBuildTypeQa()) {
            return R.mipmap.ic_launcher_qa;
        } else if (DeviceUtils.isBuildTypeQaRelease()) {
            return R.mipmap.ic_launcher_qa_release;
        } else {
            return R.mipmap.ic_app_launcher;
        }
    }


}
