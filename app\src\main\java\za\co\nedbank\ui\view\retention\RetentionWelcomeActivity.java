package za.co.nedbank.ui.view.retention;

import android.os.Bundle;

import androidx.annotation.Nullable;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityRetentionWelcomeBinding;
import za.co.nedbank.ui.di.AppDI;

public class RetentionWelcomeActivity extends NBBaseActivity implements RetentionWelcomeView {

    @Inject
    RetentionWelcomePresenter welcomePresenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityRetentionWelcomeBinding binding = ActivityRetentionWelcomeBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        binding.retWelReady.setOnClickListener(v -> handleReadyClick());
        binding.retWelSkip.setOnClickListener(v -> handleSkipClick());
    }

    @Override
    protected void onResume() {
        super.onResume();
        welcomePresenter.bind(this);
        welcomePresenter.updateRetentionStatus();
    }

    @Override
    protected void onPause() {
        super.onPause();
        welcomePresenter.unbind();
    }

    public void handleReadyClick(){
        welcomePresenter.handleReadyClick();
    }

    public void handleSkipClick(){
        welcomePresenter.handleSkipClick();
    }

    @Override
    public void onBackPressed() {
        welcomePresenter.handleSkipClick();
    }

}
