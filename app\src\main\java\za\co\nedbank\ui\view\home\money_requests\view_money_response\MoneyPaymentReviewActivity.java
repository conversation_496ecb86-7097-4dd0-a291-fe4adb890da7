/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.animation.ObjectAnimator;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AccelerateDecelerateInterpolator;

import org.greenrobot.eventbus.EventBus;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.enumerations.NotificationType;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.ActivityMoneyPaymentReviewBinding;
import za.co.nedbank.payment.pay.view.model.PaymentViewModel;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.model.PaymentActionViewModel;
import za.co.nedbank.uisdk.widget.NBSnackbar;

public class MoneyPaymentReviewActivity extends NBBaseActivity implements MoneyResponseReviewView {

    @Inject
    MoneyPaymentReviewPresenter moneyPaymentReviewPresenter;
    private ActivityMoneyPaymentReviewBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityMoneyPaymentReviewBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        moneyPaymentReviewPresenter.bind(this);
        moneyPaymentReviewPresenter.setReviewData();
        startAnimation();
        binding.txvHeader.setText(getString(R.string.review_payment));
        initToolbar(binding.reviewToolbar, true, true);
        binding.tvGoToDashboardMoneyPayment.setOnClickListener(v -> onClickCancelPayment());
        binding.reviewLoadingButton.setOnClickListener(v -> pay());
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(za.co.nedbank.payment.R.menu.menu_close, binding.reviewToolbar.getMenu());
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == za.co.nedbank.payment.R.id.menu_item_close) {
            moneyPaymentReviewPresenter.navigateToDashboardScreen();
            return true;
        }
        return super.onOptionsItemSelected(menuItem);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
        moneyPaymentReviewPresenter.unbind();
    }

    public void pay() {
        moneyPaymentReviewPresenter.trackPayNowConfirm();
        moneyPaymentReviewPresenter.makeAccountPaymentCall(null);
    }

    @Override
    public void showError(String error) {
        showError(getString(R.string.error), error);
    }


    @Override
    public void setLoadingEnabled(boolean enabled) {
        binding.reviewLoadingButton.setLoadingVisible(enabled);
    }

    @Override
    public void setEnabledActivityTouch(boolean isEnabled) {
        if (isEnabled) {
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        } else {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE);
        }
    }

    @Override
    public PaymentViewModel getPaymentViewModel() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_PAYMENT_MODEL)) {
            PaymentViewModel paymentViewModel = bundle.getParcelable(NavigationTarget.PARAM_PAYMENT_MODEL);
            paymentViewModel.setSaveBeneficiary(binding.paySaveRecipientToggle.isChecked());
            return paymentViewModel;
        }

        return null;
    }


    @Override
    public long getPaymentRequestId() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_PAYMENT_REQUEST_ID)) {
            return bundle.getLong(NavigationTarget.PARAM_PAYMENT_REQUEST_ID);
        }

        return 0;
    }

    @Override
    public PaymentActionViewModel getPaymentActionRequestModel() {
        PaymentActionViewModel paymentActionViewModel = new PaymentActionViewModel();
        PaymentViewModel paymentViewModel = getPaymentViewModel();
        if (paymentViewModel != null) {
            paymentActionViewModel.setPayerAccountNumber(paymentViewModel.getFromAccountViewModel().getAccountNumber());
            paymentActionViewModel.setPayerAccountType(paymentViewModel.getFromAccountViewModel().getAccountType());
            paymentActionViewModel.setProcessAmount(paymentViewModel.getAmount());
            paymentActionViewModel.setPayerDescription(paymentViewModel.getBeneficiaryReference());
            paymentActionViewModel.setPaymentRequestId(getPaymentRequestId());
            paymentActionViewModel.setPaymentRequestAction(PaymentRequestActionType.PAY_NOW);
        }
        return paymentActionViewModel;
    }


    @Override
    public void setPayButtonEnable(boolean enable) {
        binding.reviewLoadingButton.setEnabled(enable);
    }

    @Override
    public PaymentsViewModel getPaymentsViewModel() {
        PaymentsViewModel paymentsViewModel = new PaymentsViewModel();
        paymentsViewModel.addPaymentToList(getPaymentViewModel());
        paymentsViewModel.setValidate(true);
        return paymentsViewModel;
    }

    @Override
    public void trackDoublePayment() {
        moneyPaymentReviewPresenter.trackDoublePayment(getString(R.string.double_payment_msg));
    }

    @Override
    public void setDoublePaymentLayoutVisibility(boolean visibility) {
        binding.doubleMoneyPaymentReviewValidationLl.setVisibility(visibility ? View.VISIBLE : View.GONE);
    }

    @Override
    public void setPaymentPostCallError(String requestID, String errorMsg) {
        NBSnackbar.instance().action(getString(R.string.retry),
                        () -> moneyPaymentReviewPresenter.makeAccountPaymentCall(requestID))
                .header(getString(za.co.nedbank.payment.R.string.transaction_failed))
                .build(binding.reviewNsv, errorMsg);
    }

    protected void startAnimation() {
        ObjectAnimator animation1 = ObjectAnimator.ofFloat(binding.reviewInfoCardview, Constants.ANIMATION_KEYS.ROTATION_X, Constants.ANIMATION_CONSTANTS.M_ROTATE_START_ANGLE, Constants.ANIMATION_CONSTANTS.M_ROTATE_END_ANGLE);
        animation1.setDuration(Constants.ANIMATION_CONSTANTS.M_ROTATE_ANIMATION_DURATION);
        animation1.setRepeatCount(Constants.ANIMATION_CONSTANTS.M_ROTATE_REPEAT_COUNT);
        animation1.setInterpolator(new AccelerateDecelerateInterpolator());
        animation1.start();
    }

    @Override
    public String getReceiverMobile() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_RECEIVER_MOBILE_NUMBER)) {
            return bundle.getString(NavigationTarget.PARAM_RECEIVER_MOBILE_NUMBER);
        }

        return null;
    }

    @Override
    public void setPaymentDetails(PaymentViewModel paymentViewModel, String mobileNumber) {
        if (mobileNumber != null) {
            binding.recipientMobileNumberTv.setText(FormattingUtil.formatPhoneNumber(mobileNumber));
        }
        if (paymentViewModel != null) {
            binding.yourReferenceTxt.setText(paymentViewModel.getUserReference() == null ?
                    StringUtils.EMPTY_STRING : paymentViewModel.getUserReference());
            binding.recipientReferenceTxt.setText(paymentViewModel.getBeneficiaryReference() == null ?
                    StringUtils.EMPTY_STRING : paymentViewModel.getBeneficiaryReference());
            if (paymentViewModel.getFromAccountViewModel() != null) {
                binding.fromAccountTxt.setText(paymentViewModel.getFromAccountViewModel().getDisplayAccountName() == null ?
                        StringUtils.EMPTY_STRING : paymentViewModel.getFromAccountViewModel().getDisplayAccountName());
            }
            binding.recipientNameTv.setText(paymentViewModel.getBeneficiaryName() == null ?
                    StringUtils.EMPTY_STRING : paymentViewModel.getBeneficiaryName());
            binding.dateTxt.setText(FormattingUtil.convertMillisToDisplaySlashDateString(paymentViewModel.getStartDate()));
            binding.amountTv.setText(FormattingUtil.convertToSouthAfricaFormattedCurrency(paymentViewModel.getAmount()));
            if (paymentViewModel.getNotificationDetailsViewModel() != null && paymentViewModel.getNotificationDetailsViewModel().getNotificationType() != null) {
                if (paymentViewModel.getNotificationDetailsViewModel().getNotificationType().equalsIgnoreCase(NotificationType.EMAIL.getValue())) {
                    binding.notificationTypeTxt.setText(getString(R.string.pay_review_notification_email_label));
                    binding.emailTypeTxt.setText(paymentViewModel.getNotificationDetailsViewModel().getNotificationAddress());
                } else if (paymentViewModel.getNotificationDetailsViewModel().getNotificationType().equalsIgnoreCase(NotificationType.SMS.getValue())) {
                    binding.notificationTypeTxt.setText(getString(R.string.pay_review_notification_sms_label));
                    binding.emailTypeTxt.setText(paymentViewModel.getNotificationDetailsViewModel().getNotificationAddress());
                }
            } else {
                ViewUtils.hideViews(binding.notificationTypeTxt, binding.emailTypeTxt);
            }
        }
    }

    public void onClickCancelPayment() {
        moneyPaymentReviewPresenter.navigateToDashboardScreen();
    }
}
