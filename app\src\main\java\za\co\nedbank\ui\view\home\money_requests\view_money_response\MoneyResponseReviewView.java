/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.payment.pay.view.model.PaymentViewModel;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;
import za.co.nedbank.ui.view.model.PaymentActionViewModel;

/**
 * Created by devrath.rathee on 2/26/2018.
 */

public interface MoneyResponseReviewView extends NBBaseView {

    PaymentViewModel getPaymentViewModel();

    long getPaymentRequestId();

    String getReceiverMobile();

    void setPaymentDetails(PaymentViewModel paymentViewModel, String mobileNumber);

    void showError(String error);

    void setLoadingEnabled(boolean enabled);

    void setEnabledActivityTouch(boolean isEnabled);

    PaymentActionViewModel getPaymentActionRequestModel();

    void setPaymentPostCallError(String requestID, String errorMsg);

    void setDoublePaymentLayoutVisibility(boolean visibility);

    void setPayButtonEnable(boolean enable);

    PaymentsViewModel getPaymentsViewModel();

    void trackDoublePayment();
}
