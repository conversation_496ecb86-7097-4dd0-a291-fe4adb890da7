/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui;

/**
 * Created by ch<PERSON><PERSON> on 04-07-2017.
 */

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_CARDS;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_FINANCES;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_LATEST;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_MORE;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_NON_TP_ACCOUNTS;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_NON_TP_APPLY;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_OVERVIEW;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_RECIPIENTS;
import static za.co.nedbank.ui.MenuOptions.MENU_OPTION_TRANSACT;

@IntDef({MENU_OPTION_OVERVIEW, MENU_OPTION_CARDS, MENU_OPTION_TRANSACT,
        MENU_OPTION_FINANCES, MENU_OPTION_RECIPIENTS, MENU_OPTION_MORE,
        MENU_OPTION_NON_TP_ACCOUNTS, MENU_OPTION_NON_TP_APPLY, MENU_OPTION_LATEST})
@Retention(RetentionPolicy.SOURCE)
public @interface MenuOptions {
    int MENU_OPTION_OVERVIEW = 1;
    int MENU_OPTION_CARDS = 2;
    int MENU_OPTION_TRANSACT = 3;
    int MENU_OPTION_FINANCES = 4;
    int MENU_OPTION_RECIPIENTS = 5;
    int MENU_OPTION_MORE = 6;
    int MENU_OPTION_NON_TP_ACCOUNTS = 7;
    int MENU_OPTION_NON_TP_APPLY = 8;
    int MENU_OPTION_LATEST = 9;
}
