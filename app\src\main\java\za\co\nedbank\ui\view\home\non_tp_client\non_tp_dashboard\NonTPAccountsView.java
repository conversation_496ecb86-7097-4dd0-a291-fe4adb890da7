package za.co.nedbank.ui.view.home.non_tp_client.non_tp_dashboard;

import androidx.annotation.DrawableRes;

import za.co.nedbank.core.enumerations.BackgroundImageTypeEnum;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.ui.view.home.non_tp_client.BaseDashboardAccountsView;

public interface NonTPAccountsView extends BaseDashboardAccountsView {
    void setChatIcon(int totalUnreadMessageCount);

    void showAccountsLoading(boolean loading);

    void setNonTpAccounts(Overview overview, boolean isReloading);

    void showAccountsLoadingError(String msg);

    String getBirthDate();

    void hasTransactableAccount(boolean isTransactable);

    String getOverviewProductGroup(OverviewType overviewType);

    String getOverviewAccountType(OverviewType overviewType, String accountName, String accountCode, boolean isPocketAccount);

    void shouldShowOverlay(boolean showOverlay);

    void setBackgroundImage(String imagePath);

    void setBackgroundImage(@DrawableRes int imageId);

    void loadOverview(BackgroundImageTypeEnum backgroundImageTypeEnum);

    void refreshBalances();

    String getActivityLabel();

    void updateLocalAvoBanner();

    void updateDynamicAvoBanner(MediaCardViewModel mediaCardViewModel);

}
