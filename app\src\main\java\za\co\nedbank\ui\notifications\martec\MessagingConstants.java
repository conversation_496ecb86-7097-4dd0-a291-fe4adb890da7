/*
  Copyright 2021 Adobe. All rights reserved.
  This file is licensed to you under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License. You may obtain a copy
  of the License at http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software distributed under
  the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
  OF ANY KIND, either express or implied. See the License for the specific language
  governing permissions and limitations under the License.
*/

package za.co.nedbank.ui.notifications.martec;

public final class MessagingConstants {

    public static final String LOG_TAG = "Messaging";

    private MessagingConstants() {
    }

    public static final class Push {
        public static class PayloadKeys {
            public static final String TITLE = "adb_title";
            public static final String BODY = "adb_body";
            public static final String SOUND = "adb_sound";
            public static final String BADGE_NUMBER = "adb_n_count";
            public static final String NOTIFICATION_VISIBILITY = "adb_n_visibility";
            public static final String NOTIFICATION_PRIORITY = "adb_n_priority";
            public static final String CHANNEL_ID = "adb_channel_id";
            public static final String ICON = "adb_icon";
            public static final String IMAGE_URL = "adb_image";
            public static final String ACTION_TYPE = "adb_a_type";
            public static final String ACTION_URI = "adb_uri";
            public static final String ACTION_BUTTONS = "adb_act";

            private PayloadKeys() {
            }
        }

        private Push() {
        }
    }

    public static final class NotificationAction {
        public static final String DISMISSED = "Notification Dismissed";
        public static final String OPENED = "Notification Opened";
        public static final String BUTTON_CLICKED = "Notification Button Clicked";
        private NotificationAction() {}
    }

    public static final class Tracking {
        public static final class Keys {
            public static final String ACTION_ID = "actionId";
            public static final String ACTION_URI = "actionUri";
            public static final String MESSAGE_ID = "messageId";

            private Keys() {
            }
        }

        private Tracking() {
        }
    }
}