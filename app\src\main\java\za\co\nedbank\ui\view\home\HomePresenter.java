/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home;

import static za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_FOR_APPLICATION;
import static za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_DEFAULT_CARD_GUIDE;
import static za.co.nedbank.core.Constants.BUNDLE_KEYS.UNBOXING_MOVE_TO_SCREEN;
import static za.co.nedbank.core.Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;
import static za.co.nedbank.core.Constants.BaseInfoIntentForFicaSDK.FICA;
import static za.co.nedbank.core.Constants.DEEP_LINK_FROM_SMS_RETENTION;
import static za.co.nedbank.core.Constants.DEEP_LINK_FROM_SMS_RETENTION_COUNT;
import static za.co.nedbank.core.Constants.FLOW_CONSTANTS.CREDIT_CARD_FLOW;
import static za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW;
import static za.co.nedbank.core.Constants.ZERO;
import static za.co.nedbank.core.data.storage.StorageKeys.CLIENT_TYPE;
import static za.co.nedbank.core.data.storage.StorageKeys.IS_DEMO;
import static za.co.nedbank.core.data.storage.StorageKeys.IS_NEW_TO_BANK;
import static za.co.nedbank.core.data.storage.StorageKeys.TRAVEL_CARD_MINOR;
import static za.co.nedbank.core.navigation.NavigationTarget.IS_FROM_HOME;
import static za.co.nedbank.core.navigation.NavigationTarget.IS_RETAIL;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_EXTRA_FEATURE_NAME;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_RECIPIENT_DETAIL_OBJ;
import static za.co.nedbank.enroll_v2.Constants.BundleKeys.FLOW_JOURNEY_FLAG;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.APPLY_DIRECTOR_TYPE_PARAM;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.DIRECTOR_TYPE_SINGLE;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.FOR_ME_FOR_BUSINESS_FLOW_TYPE_PARAM;
import static za.co.nedbank.enroll_v2.view.fica.apply_flow.utils.ApplyFlowConstants.ForMyBusinessFlowType.MERCHANT_FLOW;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.google.firebase.messaging.FirebaseMessaging;

import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.Observable;
import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.FicaProductFlow;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.ResponseStorageKey;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.convochatbot.chatbotutil.CapiRequest;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotAuthenticatedPayloadDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotSessionMainRequestDataModel;
import za.co.nedbank.core.convochatbot.domain.usecase.ChatbotStartSessionUseCase;
import za.co.nedbank.core.convochatbot.view.ChatbotConstants;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.data.storage.UserInformationKeys;
import za.co.nedbank.core.deeplink.notification.NotificationAdapter;
import za.co.nedbank.core.domain.CachableValue;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsData;
import za.co.nedbank.core.domain.model.notifications.FBResponseData;
import za.co.nedbank.core.domain.model.notifications.FBUpdateTokenRequestData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetClearCacheApiUseCase;
import za.co.nedbank.core.domain.usecase.GetDataForEmUUidClientTypeUseCase;
import za.co.nedbank.core.domain.usecase.GetEmcertIdUseCase;
import za.co.nedbank.core.domain.usecase.GetLoginUseCase;
import za.co.nedbank.core.domain.usecase.GetOddReviewableReasonUseCase;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.GetUuidEmcertUseCase;
import za.co.nedbank.core.domain.usecase.LogoutUserUseCase;
import za.co.nedbank.core.domain.usecase.PutStringValueIntoAppStorageUseCase;
import za.co.nedbank.core.domain.usecase.eficasdk.FicaSDKUseCase;
import za.co.nedbank.core.domain.usecase.enrol.LoginSecurityUseCase;
import za.co.nedbank.core.domain.usecase.enrol.login.GetTrustedDeviceUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.domain.usecase.fatca.FatcaMissingInfoUseCase;
import za.co.nedbank.core.domain.usecase.ita.ITAEnrolmentUsecase;
import za.co.nedbank.core.domain.usecase.notifications.FBRegisterTokenUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetMdmProfileUseCase;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationResult;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.FatcaRestrictionUtil;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.core.view.mapper.GetOddReviewableResponseDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.fbnotifications.FBNotificationsInnerDetailsDataToViewModelMapper;
import za.co.nedbank.core.view.media_content.DynamicFeatureCardDetailEnum;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.mdm.response.odd.OddReviewableViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryCollectiveDataViewModel;
import za.co.nedbank.eficasdk.view.viewmodel.FicaSDKViewModel;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.tracking.EnrollTrackingParam.EnrollV2TrackingParam;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.loans.preapprovedaccountsoffers.Constants;
import za.co.nedbank.loans.preapprovedaccountsoffers.navigator.PreApprovedOffersNavigationTarget;
import za.co.nedbank.nid_sdk.main.interaction.SharedConstant;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextDataViewModelMapper;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextFedarationDetailsViewModel;
import za.co.nedbank.payment.common.view.model.InternationalRecipientViewModel;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.payment.transfer.tracking.TransferTrackingEvent;
import za.co.nedbank.profile.view.navigation.ProfileNavigationTarget;
import za.co.nedbank.profile.view.profile.fatca_unrestriction.FatcaUnrestrictionOnboardingActivity;
import za.co.nedbank.profile.view.profile.fatca_unrestriction.FatcaUnrestrictionType;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.usecase.branchcode.BranchCodeUseCase;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.services.view.account.branchcode.model.BranchCodeViewModel;
import za.co.nedbank.services.view.mapper.BranchCodeDataModelToBranchCodeViewModelMapper;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.app_shortcut.domain.GetDynamicShortcutUseCase;
import za.co.nedbank.core.deeplink.DeeplinkUtils;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;
import za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorActivity;
import za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorType;
import za.co.nedbank.ui.view.refica.FicaErrorActivity;
import za.co.nedbank.ui.view.refica.FicaSuccessActivity;
import za.co.nedbank.ui.view.tracking.AppTracking;

/**
 * Created by kapil.vij  on 29-06-2017.
 *
 * @edited by charu.rani on 29-06-2017.
 */

public class HomePresenter extends NBBasePresenter<HomeView> {
    private static final String TAG = HomePresenter.class.getCanonicalName();
    public static final int BUY = 1;
    public static final int PAY = 2;
    public static final int TRANSFER = 3;
    public static final int RECEIPIENT_TRANSFER = 4;
    public static final String DATA = "FICA_DATA";
    public static final String SCREEN_TYPE = "SCREEN_TYPE";

    private final NavigationRouter navigationRouter;
    private final LogoutUserUseCase logoutUserUseCase;
    private final Analytics analytics;
    private final GetDynamicShortcutUseCase getDynamicShortcutUseCase;
    private final ApplicationStorage inMemoryStorage;
    private final ApplicationStorage applicationStorage;
    private final GetUserDetailUseCase mGetUserDetailUseCase;
    private final GetLoginUseCase mGetLoginUseCase;
    private final GetEmcertIdUseCase mGetEmcertIdUseCase;
    private final UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper;
    private final PutStringValueIntoAppStorageUseCase mPutIntoAppStorageUseCase;
    private final FeatureSetController mFeatureSetController;
    private final FBRegisterTokenUseCase fbUpdateTokenUseCase;
    private NavigationResult mNavigationResult;
    private final LoginSecurityUseCase loginSecurityUseCase;
    private boolean isFromScanToPay;
    private boolean gotoScanPayManagement;
    private final FBNotificationsInnerDetailsDataToViewModelMapper mFBNotificationsInnerDetailsDataToViewModelMapper;
    private boolean hasTransactableAccount;
    private String mClientType;
    private String mFicaStatus;
    private final GetOverviewUseCase mGetOverviewUseCase;
    private final GetUuidEmcertUseCase mGetUuidEmcertUseCase;
    private final ITAEnrolmentUsecase itaEnrolmentUsecase;
    private final GetFedarationListUseCase mGetFedarationListUseCase;
    private final SwitchContextDataViewModelMapper mSwitchContextDataViewModelMapper;
    private final ErrorHandler errorHandler;
    private final GetDataForEmUUidClientTypeUseCase mGetDataForEmUUidClientTypeUseCase;
    private Map<String, Object> deviceRegistrationMap;
    private String mDeviceRegistrationKey;
    private String mFbToken;
    private String mEmcert;
    private boolean isRegistrationApiInProgress;
    private SwitchContextFedarationDetailsViewModel contextFedarationDetailsViewModel;
    private final ChatbotStartSessionUseCase chatbotStartSessionUseCase;
    private final GetTrustedDeviceUseCase getTrustedDeviceUseCase;
    private final FicaSDKUseCase mFicaSDKUseCase;
    private boolean hasCompletedUnboxingFlow = false;
    private final BranchCodeUseCase branchCodeUseCase;
    private final BranchCodeDataModelToBranchCodeViewModelMapper modelToBranchCodeViewModelMapper;
    private final GetMdmProfileUseCase getMdmProfileUseCase;
    private final GetOddReviewableReasonUseCase getOddReviewableReasonUseCase;
    private final GetOddReviewableResponseDataToViewModelMapper oddMapper;
    private final FatcaMissingInfoUseCase fatcaMissingInfoUseCase;
    private final GetClearCacheApiUseCase refreshUserUseCase;
    private final NotificationAdapter notificationAdapter;

    @Inject
    public HomePresenter(final NavigationRouter navigationRouter, final Analytics analytics, final ApplicationStorage applicationStorage,
                         final GetUserDetailUseCase getUserDetailUseCase, final UserDetailDataToViewModelMapper userDetailDataToViewModelMapper,
                         final PutStringValueIntoAppStorageUseCase putIntoAppStorageUseCase, final GetFedarationListUseCase getFedarationListUseCase, SwitchContextDataViewModelMapper switchContextDataViewModelMapper,
                         final LogoutUserUseCase logoutUserUseCase, final GetLoginUseCase getLoginUseCase, final GetEmcertIdUseCase getEmcertIdUseCase, final GetDynamicShortcutUseCase getDynamicShortcutUseCase,
                         final FeatureSetController featureSetController, @Named("memory") ApplicationStorage inMemoryStorage,
                         final FBRegisterTokenUseCase fbUpdateTokenUseCase, final LoginSecurityUseCase loginSecurityUseCase, final ITAEnrolmentUsecase itaEnrolmentUsecase,
                         final FBNotificationsInnerDetailsDataToViewModelMapper fbNotificationsInnerDetailsDataToViewModelMapper, GetOverviewUseCase getOverviewUseCase,
                         final GetUuidEmcertUseCase getUuidEmcertUseCase, final ErrorHandler errorHandler, final GetDataForEmUUidClientTypeUseCase getDataForEmUUidClientTypeUseCase,
                         final ChatbotStartSessionUseCase chatbotStartSessionUseCase,
                         final GetTrustedDeviceUseCase getTrustedDeviceUseCase, final FicaSDKUseCase mFicaSDKUseCase,
                         final BranchCodeUseCase branchCodeUseCase,
                         final BranchCodeDataModelToBranchCodeViewModelMapper modelToBranchCodeViewModelMapper,
                         final GetMdmProfileUseCase getMdmProfileUseCase,
                         final GetOddReviewableReasonUseCase getOddReviewableReasonUseCase,
                         final GetOddReviewableResponseDataToViewModelMapper oddMapper,
                         final FatcaMissingInfoUseCase fatcaMissingInfoUseCase,
                         final GetClearCacheApiUseCase refreshUserUseCase,
                         final NotificationAdapter notificationAdapter) {

        this.navigationRouter = navigationRouter;
        this.analytics = analytics;
        this.applicationStorage = applicationStorage;
        this.mGetUserDetailUseCase = getUserDetailUseCase;
        this.mUserDetailDataToViewModelMapper = userDetailDataToViewModelMapper;
        this.mPutIntoAppStorageUseCase = putIntoAppStorageUseCase;
        this.logoutUserUseCase = logoutUserUseCase;
        this.mGetLoginUseCase = getLoginUseCase;
        this.mGetEmcertIdUseCase = getEmcertIdUseCase;
        this.getDynamicShortcutUseCase = getDynamicShortcutUseCase;
        this.mFeatureSetController = featureSetController;
        this.inMemoryStorage = inMemoryStorage;
        this.fbUpdateTokenUseCase = fbUpdateTokenUseCase;
        this.loginSecurityUseCase = loginSecurityUseCase;
        this.mGetFedarationListUseCase = getFedarationListUseCase;
        this.mSwitchContextDataViewModelMapper = switchContextDataViewModelMapper;
        this.mFBNotificationsInnerDetailsDataToViewModelMapper = fbNotificationsInnerDetailsDataToViewModelMapper;
        this.mGetOverviewUseCase = getOverviewUseCase;
        this.mGetUuidEmcertUseCase = getUuidEmcertUseCase;
        this.itaEnrolmentUsecase = itaEnrolmentUsecase;
        this.errorHandler = errorHandler;
        this.mGetDataForEmUUidClientTypeUseCase = getDataForEmUUidClientTypeUseCase;
        this.chatbotStartSessionUseCase = chatbotStartSessionUseCase;
        this.getTrustedDeviceUseCase = getTrustedDeviceUseCase;
        this.mFicaSDKUseCase = mFicaSDKUseCase;
        this.branchCodeUseCase = branchCodeUseCase;
        this.modelToBranchCodeViewModelMapper = modelToBranchCodeViewModelMapper;
        this.getMdmProfileUseCase = getMdmProfileUseCase;
        this.oddMapper = oddMapper;
        this.getOddReviewableReasonUseCase = getOddReviewableReasonUseCase;
        this.fatcaMissingInfoUseCase = fatcaMissingInfoUseCase;
        this.refreshUserUseCase = refreshUserUseCase;
        this.notificationAdapter = notificationAdapter;
    }

    @Override
    protected void onBind() {
        if (null != mNavigationResult && mNavigationResult.isOk() && view != null) {
            view.setResult(mNavigationResult.getParams());
        }
    }

    void trackMyAccountScreen(String segment, String clientIdType, String staffOrNonStaff, String dcar) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setClientIdType(clientIdType);
        adobeContextData.setStaffOrNonStaff(staffOrNonStaff);
        adobeContextData.setDcar(dcar);

        trackTimeSpentAndSegment(segment, adobeContextData);
        cdata.put(EnrollV2TrackingEvent.ANALYTICS.KEY_NEDBANK_PAGE_CATEGORY, EnrollV2TrackingEvent.ANALYTICS.VAL_DASHBOARD_MY_ACCOUNTS);

        boolean isUUIDCaptured = inMemoryStorage.getBoolean(StorageKeys.IS_UUID_CAPTURED, false);

        boolean isEmcertIdCaptured = inMemoryStorage.getBoolean(StorageKeys.IS_MY_ACCOUNTS_WITH_EMCERT_ID_TAGGED, false);

        if (isUUIDCaptured) {
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_USER_JOURNEY, TrackingParam.VAL_NAV_TO_DASHBOARD);
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_MY_ACCOUNTS);
            sendMyAccountsEventOncePerSession(cdata);
        } else if (isEmcertIdCaptured) {
            isEmcertIdAvailableCase(cdata);
        } else {
            isUuidAvailableCase(cdata);
        }
    }

    private void trackTimeSpentAndSegment(String segment, AdobeContextData adobeContextData) {
        if (StringUtils.isNotEmpty(segment)) {
            boolean isBusinessUser = Integer.parseInt(segment) > za.co.nedbank.services.Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;
            adobeContextData.setSegment(isBusinessUser ? TrackingParam.VAL_PROFILE_SELECTED_BUSINESS : TrackingParam.VAL_PROFILE_SELECTED_PERSONAL);
        }
    }

    private void isUuidAvailableCase(HashMap<String, Object> cdata) {
        mGetUuidEmcertUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(uuidOrEmcert -> {
                    if (!StringUtils.isNullOrEmpty(uuidOrEmcert)) {

                        if (uuidOrEmcert.startsWith("e")) {
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_USER_JOURNEY, TrackingParam.VAL_NAV_TO_DASHBOARD);
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_MY_ACCOUNTS);
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_UUID, uuidOrEmcert);
                            sendMyAccountsEventOncePerSession(cdata);
                            inMemoryStorage.putBoolean(StorageKeys.IS_MY_ACCOUNTS_WITH_EMCERT_ID_TAGGED, true);
                        } else {
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_USER_JOURNEY, TrackingParam.VAL_NAV_TO_DASHBOARD);
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_MY_ACCOUNTS);
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_UUID, uuidOrEmcert);
                            sendMyAccountsEventOncePerSession(cdata);
                            inMemoryStorage.putBoolean(StorageKeys.IS_UUID_CAPTURED, true);
                        }
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    private void isEmcertIdAvailableCase(HashMap<String, Object> cdata) {
        mGetUuidEmcertUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(uuidOrEmcert -> {
                    if (!StringUtils.isNullOrEmpty(uuidOrEmcert)) {

                        if (!uuidOrEmcert.startsWith("e")) {
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_USER_JOURNEY, TrackingParam.VAL_NAV_TO_DASHBOARD);
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_MY_ACCOUNTS);
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_UUID, uuidOrEmcert);
                            sendMyAccountsEventOncePerSession(cdata);
                            inMemoryStorage.putBoolean(StorageKeys.IS_UUID_CAPTURED, true);
                        } else {
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_USER_JOURNEY, TrackingParam.VAL_NAV_TO_DASHBOARD);
                            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_MY_ACCOUNTS);
                            sendMyAccountsEventOncePerSession(cdata);
                        }
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    void sendMyAccountsEventOncePerSession(HashMap<String, Object> cdata) {
        boolean isEventAlreadySent = inMemoryStorage.getBoolean(StorageKeys.IS_MY_ACCOUNTS_EVENT_SENT, false);
        if (!isEventAlreadySent) {
            trackWelcomeContextDataForAdobe(EnrollV2TrackingParam.EFICA.MY_ACCOUNTS, cdata);
            inMemoryStorage.putBoolean(StorageKeys.IS_MY_ACCOUNTS_EVENT_SENT, true);
        }
    }

    void trackWelcomeContextDataForAdobe(String tagName, HashMap<String, Object> finalContextData) {
        mGetDataForEmUUidClientTypeUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(welcomeData -> {
                    if (welcomeData != null) {
                        finalContextData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_APP_TYPE, TrackingParam.VAL_MONEY_APP_ANDROID);
                        finalContextData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_LOGGED_IN_STATE, TrackingEvent.ANALYTICS.VAL_LOGGED_IN);

                        if (!StringUtils.isNullOrEmpty(welcomeData.getEmcertId())) {
                            finalContextData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_EMCERT, welcomeData.getEmcertId());
                        }
                        if (!StringUtils.isNullOrEmpty(welcomeData.getClientType())) {
                            finalContextData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_CLIENT_TYPE, welcomeData.getClientType());
                        }
                        boolean isUUIDCaptured = inMemoryStorage.getBoolean(StorageKeys.IS_UUID_CAPTURED, false);
                        if (!isUUIDCaptured && !StringUtils.isNullOrEmpty(welcomeData.getUuid())) {
                            finalContextData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_UUID, welcomeData.getUuid());
                            inMemoryStorage.putBoolean(StorageKeys.IS_UUID_CAPTURED, true);
                        }
                        if (!StringUtils.isNullOrEmpty(welcomeData.getSessionId())) {
                            finalContextData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_SESSION_ID, welcomeData.getSessionId());
                            finalContextData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_SESSION_COUNT, TrackingEvent.ANALYTICS.VAL_ONE);
                        }
                        analytics.sendEventStateWithMap(tagName, finalContextData);
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    void trackTransact() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_TRANSACT, cdata);
    }

    public void toggleTransactionFabMenu() {
        if (this.view != null) {
            this.view.toggleTransactionMenu();
        }
    }

    void takeMeToPay() {

        if (view == null)
            return;
        this.view.resetMenuWithoutAnimation();
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setEntryPoint(TrackingParam.VAL_APP_PAY_MY_ACCOUNTS);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_MONETARY_TRANSACTIONS);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_PAY_RECEIVE, cdata);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.PAY_LANDING)
                .withParam(NavigationTarget.PARAM_SHOW_ITT, toShowItt()));

    }

    boolean isBusinessUser() {
        int clientType = applicationStorage.getInteger(za.co.nedbank.core.Constants.KEY_USER_CLIENT_TYPE, ZERO);
        return (clientType > BUSINESS_USER_PROFILE_CHECK_LIMIT);
    }

    protected void startPaymentTime(long timestamp) {
        inMemoryStorage.putLong(StorageKeys.TRANSFER_PAYMENT_TIME, timestamp);
    }


    public void checkFica(Bundle bundle) {

        if (isFicaEnabled()) {
            callClientsEnablementsApi(bundle);
        } else {
            navigateToRespectiveScreen(bundle);
        }

    }


    private void callClientsEnablementsApi(Bundle bundle) {
        mGetUserDetailUseCase.execute(false).compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    UserDetailViewModel userDetailVM = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail);
                    boolean isFicaVerified = userDetailVM.isFicaVerified();
                    boolean oddRestricted = userDetailVM.getOddRestricted();
                    boolean hasIdOrTaxIdNumber = !StringUtils.isNullOrEmpty(userDetailVM.getIdOrTaxIdNumber());
                    boolean isRetailUser = (!StringUtils.isNullOrEmpty(userDetailVM.getClientType()) && Integer.parseInt(userDetailVM.getClientType())<=30);
                    if(!isFicaVerified) {
                        checkAllConditions(bundle, hasIdOrTaxIdNumber, isRetailUser);
                    } else if (oddRestricted && isOddRestrictionEnabled()) {
                        if (!userDetailVM.isOddRequired && !isRetailUser) {
                            navigateToOddError(OddRestrictionErrorType.JURISTIC_ODD_VERIFYING);
                        } else {
                            checkODDFLow(userDetailVM, isRetailUser);
                        }
                    } else if (FatcaRestrictionUtil.isNewFatcaRestrictionApplicable(inMemoryStorage, mFeatureSetController, userDetail)) {
                        checkFatcaUnrestriction(bundle, isRetailUser);
                    } else {
                        navigateToRespectiveScreen(bundle);
                    }
                }, this::handleException);
    }

    private void checkFatcaUnrestriction(Bundle bundle, boolean isRetailUser) {
        if(!isRetailUser){
            navigateToFatcaUnRestrictions(FatcaUnrestrictionType.JURISTIC, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        }else{
            fatcaMissingInfoUseCase.execute().compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                    })
                    .doOnTerminate(() -> {
                    })
                    .subscribe(data -> {
                                if(data.getData()!=null){
                                    if(data.getData().getRequiredData()!=null && !data.getData().getRequiredData().isEmpty() && data.getData().getRequiredDocuments() != null && !data.getData().getRequiredDocuments().isEmpty() ){
                                        navigateToFatcaUnRestrictions(FatcaUnrestrictionType.MISSING_INFO_FORM, data.getData().getDocumentLocation(), data.getData().getFormStr());
                                    }else if(data.getData().getRequiredData()!=null && !data.getData().getRequiredData().isEmpty() ){
                                        navigateToFatcaUnRestrictions(FatcaUnrestrictionType.MISSING_INFO, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
                                    }else if(data.getData().getRequiredDocuments() != null && !data.getData().getRequiredDocuments().isEmpty()){
                                        navigateToFatcaUnRestrictions(FatcaUnrestrictionType.MISSING_FORM, data.getData().getDocumentLocation(), data.getData().getFormStr());
                                    }else {
                                        callRefreshApi();
                                    }
                                }
                            }
                            , e -> view.showErrorWithOutTitle("Apologies! Looks like something's wrong on our side."));
        }
    }

    private void callRefreshApi() {
        refreshUserUseCase.execute(true).compose(bindToLifecycle()).subscribe(res -> mGetUserDetailUseCase.execute(true).compose(bindToLifecycle())
                .subscribe(userDetail -> navigateToFatcaUnRestrictions(FatcaUnrestrictionType.VERIFYING, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING), this::handleException));

    }

    private void navigateToFatcaUnRestrictions(@FatcaUnrestrictionType int type, String url, String forms){
        view.toggleTransactionMenu();
        navigationRouter.navigateWithResult(NavigationTarget.to(ProfileNavigationTarget.FATCA_UNRESTRICTION_ONBOARDING_SCREEN)
                .withParam(SCREEN_TYPE, type)
                .withParam("forms", forms)
                .withParam("url", url)
                .withAllData(true)
        ).subscribe(
                navigationResult -> {
                    if (navigationResult != null && navigationResult.isOk() &&
                            navigationResult.getParams().containsKey(FatcaUnrestrictionOnboardingActivity.ACTION) && navigationResult.getStringParam(FatcaUnrestrictionOnboardingActivity.ACTION).equals("1")) {
                        openBranchScreen();
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));

    }

    private void checkAllConditions(Bundle bundle, boolean hasIdOrTaxIdNumber, boolean isRetailUser) {
        if (hasIdOrTaxIdNumber && isRetailUser) {
            navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_VERIFY_ME)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk()) {
                            callFica(bundle);
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        } else {
            navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_ERROR)
                    .withParam(IS_FROM_HOME, true)
                    .withParam(IS_RETAIL, isRetailUser)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk() &&
                                navigationResult.getParams().containsKey(FicaErrorActivity.ACTION) && navigationResult.getStringParam(FicaErrorActivity.ACTION).equals("1")) {
                            openBranchScreen();
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
    }

    private void checkODDFLow(UserDetailViewModel userDetailVM, boolean isRetailUser) {
        if (isRetailUser) {
            if (userDetailVM.getOddRequired()) {
                callOddRulesApi();
            } else {
                navigateToOddError(OddRestrictionErrorType.ODD_VERIFYING);
            }
        } else {
            navigateToOddError(OddRestrictionErrorType.JURISTIC);
        }
    }

    public void navigateToOddError(OddRestrictionErrorType type) {
        navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.ODD_RESTRICTION_ERROR)
                .withParam(za.co.nedbank.core.Constants.TYPE, type)
                .withAllData(Boolean.TRUE)).subscribe(
                navigationResult -> {
                    if (navigationResult != null && navigationResult.isOk() &&
                            navigationResult.getParams().containsKey(OddRestrictionErrorActivity.ACTION_KEY) && navigationResult.getStringParam(OddRestrictionErrorActivity.ACTION_KEY).equals("1")) {
                        openBranchScreen();
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void callOddRulesApi() {
        getOddReviewableReasonUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                })
                .doOnTerminate(() -> {
                })
                .subscribe(data -> {
                            OddReviewableViewModel oddReviewableData = oddMapper.mapData(data).getViewModel();
                            if (data.getOddReviewableClientDataResponse().isSelfServiceIsReviewable()) {
                                getMdmProfileDetails(oddReviewableData);
                            } else {
                                boolean is1015 = data.getOddReviewableClientDataResponse().getUnReviewableReason().equals("1015");
                                navigateToOddError(is1015 ? OddRestrictionErrorType.REASON_1015 : OddRestrictionErrorType.REASON_OTHER);
                            }
                        }
                        , this::handleException);

    }


    void getMdmProfileDetails(OddReviewableViewModel oddReviewableData) {
        getMdmProfileUseCase.execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                })
                .doOnTerminate(() -> {
                })
                .subscribe(profile -> {
                    if (profile != null) {
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CONFIRM_ODD_DETAILS)
                                .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.USER_PROFILE, profile)
                                .withParam(za.co.nedbank.core.Constants.RSA_ID_OR_PASSPORT, profile.getRsaId())
                                .withParam(za.co.nedbank.core.Constants.IS_FROM_ODD_RESTRICTION, true)
                                .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.ODD_RULES_DATA, oddReviewableData)
                                .withAllData(Boolean.TRUE));
                    }
                }, this::handleException);

    }


    private void callFica(Bundle bundle) {
        mFicaSDKUseCase.execute(FICA).compose(bindToLifecycle())
                .subscribe(ficaSDKViewModel ->
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(DATA, ficaSDKViewModel)
                                .withParam(SCREEN_TYPE, bundle).withIntentFlagClearTopSingleTop(true)), this::handleException);
    }

    public void handleFicaResponse(Bundle bundle, FicaSDKViewModel ficaSDKViewModel) {
        if (ficaSDKViewModel.isFicaed()) {
            navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_SUCCESS)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk() &&
                                navigationResult.getParams().containsKey(FicaSuccessActivity.ACTION) && navigationResult.getStringParam(FicaSuccessActivity.ACTION).equals("1")) {
                            callClientsEnablementsApi(bundle);
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        } else {
            navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.FICA_ERROR)).subscribe(
                    navigationResult -> {
                        if (navigationResult != null && navigationResult.isOk() &&
                                navigationResult.getParams().containsKey(FicaErrorActivity.ACTION) && navigationResult.getStringParam(FicaErrorActivity.ACTION).equals("1")) {
                            openBranchScreen();
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
    }

    public void openBranchScreen() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ATM_AND_BRANCHES_PROFILE).
                withParam(NavigationTarget.PARAM_IS_ATM_VISIBLE, true));
    }

    private void navigateToRespectiveScreen(Bundle bundle) {
        if (bundle.getInt(SCREEN_TYPE) == BUY) {
            takeMeToBuy();
        } else if (bundle.getInt(SCREEN_TYPE) == PAY) {
            takeMeToPay();
        } else if (bundle.getInt(SCREEN_TYPE) == TRANSFER) {
            takeMeToTransfer();
        } else {
            handleRecipientItemClick(bundle);
        }
    }

    private void handleException(Throwable throwable) {
        Error error = errorHandler.getErrorMessage(throwable);
        if (view != null) {
            view.setOverviewUserInfo(false, null, error);
        }
        NBLogger.e(TAG, Log.getStackTraceString(throwable));
    }

    public void takeMeToTransfer() {
        if (view == null) return;
        this.view.resetMenuWithoutAnimation();

        long timestamp = System.currentTimeMillis() / 1000;
        startPaymentTime(timestamp);

        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(TrackingEvent.ANALYTICS.VAL_MONETARY_TRANSACTIONS_FEATURE_CATEGORY);
        adobeContextData.setFeature(TrackingParam.VAL_TRANSFER);
        adobeContextData.setEntryPoint(TrackingParam.VAL_APP_TRANSFER_MY_ACCOUNTS);
        adobeContextData.setStepName(TrackingParam.VAL_STEP_NAME_TRANSFER_INITIATION);
        adobeContextData.setInitiations();
        adobeContextData.setStep1();
        analytics.sendEventActionWithMap(TransferTrackingEvent.SCREEN_TRANSFER, cdata);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TRANSFER));

    }

    public void takeMeToBuy() {
        if (view == null) return;
        this.view.resetMenuWithoutAnimation();
        HashMap<String, Object> cdata = new HashMap<>();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_BUY_MY_ACCOUNTS);
        analytics.sendEventActionWithMap(AppTracking.KEY_BUY, cdata);

        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.VAS);
        navigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, view.getBirthDate());
        navigationRouter.navigateTo(navigationTarget);
    }

    public void loadOverviewView() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        addMyAccountEvent(cdata);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_OVERVIEW, cdata);
        if (view != null)
            this.view.showOverviewUI();
    }

    public void addMyAccountEvent(Map<String, Object> cdata) {
        boolean isEventSent = inMemoryStorage.getBoolean(StorageKeys.IS_MY_ACCOUNTS_EVENT_SENT, false);
        if (!isEventSent) {
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_MY_ACCOUNTS);
            inMemoryStorage.putBoolean(StorageKeys.IS_MY_ACCOUNTS_EVENT_SENT, true);
        }
    }

    public void loadCardsView(boolean isFromDeepLink) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_CARDS_MY_ACCOUNTS);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_CARDS, cdata);
        if (view != null)
            this.view.showCardsUI(isFromDeepLink);
    }

    public void loadHomeLatestNonTpSalesUI() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_LATEST, cdata);
        if (view != null) {
            view.showLatestNonTpSalesUI();
        }
    }

    public void loadRecipientView() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_RECIPIENTS_MY_ACCOUNTS);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_RECIPIENTS, cdata);
        if (view != null)
            view.showRecipientUI();
    }

    public void loadMoreOptionsView() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_MORE_MY_ACCOUNTS);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_MORE, cdata);
        if (view != null)
            this.view.showMoreOptionsUI(false);

    }

    public void loadAccountsNonTPOptionsView() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        addMyAccountEvent(cdata);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_ACCOUNTS, cdata);
        if (view != null)
            this.view.showAccountsNonTPUI();
    }

    public void loadApplyNonTPOptionsView() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_APPLY_MY_ACCOUNTS);
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_APPLY, cdata);
        if (view != null)
            this.view.showApplyNonTPUI();
    }

    public void openFeedbackView() {
        navigationRouter.navigateTo(NavigationTarget.to(ProfileNavigationTarget.FEEDBACK));
    }

    public void handleAddRecipient() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FEATURE_RECIPIENT_WORKFLOW))
            navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.ADD_RECIPIENT)).subscribe(
                    navigationResult -> mNavigationResult = navigationResult, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        else
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.COMING_SOON));
    }

    public void handleRecipientItemClick(Bundle bundle) {
        if (bundle != null && bundle.getParcelable(PayNavigatorTarget.EXTRAS.BENEFICIARY_SELECTED) != null) {
            Object selectedBeneficiary = bundle.getParcelable(PayNavigatorTarget.EXTRAS.BENEFICIARY_SELECTED);
            if (selectedBeneficiary instanceof UserBeneficiaryCollectiveDataViewModel) {
                UserBeneficiaryCollectiveDataViewModel selectedUserBeneficiaryViewModel = (UserBeneficiaryCollectiveDataViewModel) selectedBeneficiary;
                NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.RECIPIENT_DETAIL_WITH_HISTORY);
                navigationTarget.withParam(za.co.nedbank.core.payment.recent.Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL, selectedUserBeneficiaryViewModel);
                navigationRouter.navigateWithResult(navigationTarget).subscribe(
                        navigationResult -> {
                            mNavigationResult = navigationResult;
                            if (view != null && null != navigationResult && navigationResult.isOk()) {
                                view.setResult(mNavigationResult.getParams());
                            }
                        }
                );
            }
        }
    }

    public void handleInternationalRecipientDetailClick(Bundle bundle) {
        if (bundle != null && bundle.get(PayNavigatorTarget.EXTRAS.BENEFICIARY_SELECTED) != null) {
            Object selectedBeneficiary = bundle.get(PayNavigatorTarget.EXTRAS.BENEFICIARY_SELECTED);
            if (selectedBeneficiary instanceof InternationalRecipientViewModel) {
                InternationalRecipientViewModel selectedUserBeneficiaryViewModel = (InternationalRecipientViewModel) selectedBeneficiary;
                NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.INTERNATIONAL_RECIPIENT_DETAILS_SCREEN);
                navigationTarget.withParam(PARAM_RECIPIENT_DETAIL_OBJ, selectedUserBeneficiaryViewModel);
                navigationRouter.navigateTo(navigationTarget);
            }
        }
    }

    private void navigateToSecondLogin() {
        if (view != null) {
            view.finishAffinity();
        }
        logoutChatHistory();
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.LOGIN);
        navigationTarget.withClearStack(true);
        navigationRouter.navigateTo(navigationTarget);

    }


    public void handleBackButtonClick() {
        logoutUserUseCase
                .execute()
                .subscribe(aBoolean -> navigateToSecondLogin()
                        , (err) -> {
                            NBLogger.e(getClass().getName(), err.getMessage());

                            navigateToSecondLogin();
                        });
    }

    public void trackSkipFromScanToPay() {
        analytics.sendEvent(AppTracking.SKIP_SCAN_TO_PAY_SECURITY, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    void handleConfirmGuide(boolean isFromScanToPay, boolean gotoScanPayManagement) {
        this.isFromScanToPay = isFromScanToPay;
        this.gotoScanPayManagement = gotoScanPayManagement;
        analytics.sendEvent(AppTracking.LETS_GO_SCAN_TO_PAY_SECURITY, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        navigateToLoginSecurity();
    }

    void navigateToLoginSecurity() {
        if (gotoScanPayManagement) {
            navigationRouter.navigateTo(NavigationTarget.to(ProfileNavigationTarget.SCAN_PAY_MANAGEMENT).
                    withParam(IS_DEFAULT_CARD_GUIDE, true));
        } else {
            loginSecurityUseCase.execute(Boolean.FALSE)
                    .compose(bindToLifecycle()).subscribe(result -> {
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
        disableTutorialGuide();
    }

    void handleSkipGuide() {
        disableTutorialGuide();
    }

    private void disableTutorialGuide() {
        if (view != null) {
            view.hideTutorialGuide();
        }
        if (!isFromScanToPay)
            applicationStorage.putBoolean(StorageKeys.SHOW_APP_PIN_GUIDE, false);
    }

    boolean getStatusTutorialGuide(final boolean fromSecondLoginScreen, boolean isFingerPrintAltered) {
        boolean isShowAppPinGuide = applicationStorage.getBoolean(StorageKeys.SHOW_APP_PIN_GUIDE, true);
        boolean isAppPinEnabled = applicationStorage.getBoolean(StorageKeys.USE_PIN_PREF, false);
        boolean isFingerPrintEnabled = applicationStorage.getBoolean(StorageKeys.IS_FINGER_PRINT_ENABLE_PREF, false);
        if (fromSecondLoginScreen && (isAppPinEnabled || isFingerPrintEnabled || isFingerPrintAltered) && isShowAppPinGuide) {
            applicationStorage.putBoolean(StorageKeys.SHOW_APP_PIN_GUIDE, false);
        }
        return fromSecondLoginScreen && isShowAppPinGuide && !isAppPinEnabled && !isFingerPrintEnabled;
    }

    public void fetchUserDetail() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    UserDetailViewModel userDetailVM = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail);
                    if (view != null) {
                        view.setUserInfo(userDetailVM);
                        // checking if it is being called from feature detail, if yes then show the appropriate screen
                        checkAndShowFeatureDeepLinkScreen();
                        view.setOverviewUserInfo(true, userDetail, null);
                        mFbToken = view.getFbToken();
                    }
                    mDeviceRegistrationKey = userDetailVM.getCisNumber() + StringUtils.UNDERSCORE + userDetail.getProfileNumber();
                    saveMobileIntoStorage(userDetailVM.getCellNumber());
                    if (!isRegistrationApiInProgress && isTokenValid()) {
                        isRegistrationApiInProgress = true;
                        updateTokenToServer();
                    }
                    saveFullNameInToStorage(userDetailVM.getFullNames());
                    saveGenderInToStorage(userDetailVM.getGender());
                    saveForexMinorStatusIntoStorage(FormattingUtil.isAgeMinor(userDetailVM.getBirthDate()));
                    saveIsForeignNationalIntoStorage(userDetailVM.getIdOrTaxIdNumber());

                }, throwable -> {
                    Error error = errorHandler.getErrorMessage(throwable);
                    if (view != null) {
                        view.setOverviewUserInfo(false, null, error);
                    }
                    NBLogger.e(TAG, Log.getStackTraceString(throwable));
                });
    }

    public void checkUserAgeAndLoadInternationalPayment(String featureName) {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    boolean isAgeMinor = StringUtils.isNotEmpty(userDetail.getBirthDate()) && FormattingUtil.isAgeMinor(userDetail.getBirthDate());
                    if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.INTERNATIONAL_PAYMENTS_TOGGLE) && !isAgeMinor && !isBusinessUser()) {
                        navigateToDeepLinkIntermediateScreen(featureName);
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    private void saveIsForeignNationalIntoStorage(String idOrTaxIdNumber) {
        applicationStorage.putBoolean(StorageKeys.IS_FOREIGN_NATIONAL, TextUtils.isEmpty(idOrTaxIdNumber) ||
                idOrTaxIdNumber.equals(za.co.nedbank.core.Constants.FOREIGN_NATIONAL_TAXID));
    }

    void saveGenderInToStorage(String gender) {
        mPutIntoAppStorageUseCase.execute(UserInformationKeys.GENDER, gender)
                .subscribe(result -> {
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    void saveMobileIntoStorage(String mobileNo) {
        mPutIntoAppStorageUseCase.execute(StorageKeys.MOBILE_NUMBER_UNMASKED, mobileNo)
                .subscribe(result -> {
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    void saveForexMinorStatusIntoStorage(boolean isMinor) {
        inMemoryStorage.putBoolean(StorageKeys.TRAVEL_CARD_MINOR, isMinor);
    }

    private void saveFullNameInToStorage(String fullNames) {
        mPutIntoAppStorageUseCase.execute(StorageKeys.USERNAME, fullNames)
                .subscribe(result -> {
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void fetchDynamicShortCuts() {
        getDynamicShortcutUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(appShortcutList -> {
                    if (view != null) {
                        view.publishShortcuts(appShortcutList);
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    void handleNotificationsFromMoreClicked() {
        boolean isNotificationDisabled = mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS);
        String target = isNotificationDisabled ? PreApprovedOffersNavigationTarget.PRE_APPROVED_OFFERS_NOTIFICATIONS : NavigationTarget.TARGET_NOTIFICATION_CENTER;
        NavigationTarget navigationTarget = NavigationTarget.to(target);
        navigationTarget.withParam(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.NOTIFICATIONS_MORE_SCREEN);
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_JOURNEY_FLAG, za.co.nedbank.core.Constants.FLOW_CONSTANTS.POST_LOGIN_MORE_NOTIFICATION_FLOW);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void handleNotificationFlow(String fragmentToBeNavigated) {
        String ajoNotificationData = inMemoryStorage.getString(NotificationConstants.STORAGE_KEYS.AJO_NOTIFICATION_PAYLOAD, null);
        String ajoNavigationLink = inMemoryStorage.getString(NotificationConstants.STORAGE_KEYS.AJO_NOTIFICATION_LINK, null);
        if (!StringUtils.isNullOrEmpty(fragmentToBeNavigated)) {
            navigateTo(fragmentToBeNavigated);
        } else if (!StringUtils.isNullOrEmpty(ajoNotificationData)) {
            NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.TARGET_AJO_NOTIFICATION_DETAILS)
                    .withParam(NotificationConstants.EXTRA.AJO_NOTIFICATION_DATA, ajoNotificationData);
            navigationRouter.navigateTo(targetScreen);
        } else if (!StringUtils.isNullOrEmpty(ajoNavigationLink)) {
            NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.TARGET_GLOBAL_NAVIGATION_HANDLER)
                    .withParam(NotificationConstants.Navigation.TARGET, ajoNavigationLink)
                    .withParam(NotificationConstants.Navigation.FROM, NotificationConstants.AjoConstants.AJO_IN_APP);
            navigationRouter.navigateTo(targetScreen);
        } else {
            Object notificationViewModelObject = inMemoryStorage.getObject(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL);
            if (notificationViewModelObject instanceof FBNotificationsViewModel) {
                FBNotificationsViewModel notificationDetailsViewModel = (FBNotificationsViewModel) notificationViewModelObject;
                if (Boolean.FALSE.equals(notificationDetailsViewModel.getAuth()) && notificationDetailsViewModel.getClient() != null) {
                    if (isContextSwitchNeeded(notificationDetailsViewModel)) {
                        getContextSwitchModel(notificationDetailsViewModel.getClient().getCisNo());

                    } else {
                        handleNonContextSwitchNotifHandling(notificationDetailsViewModel);
                    }
                }
            }
        }
    }

    private void handleNonContextSwitchNotifHandling(FBNotificationsViewModel notificationDetailsViewModel) {
        if (NotificationConstants.NOTIFICATION_TYPES.TRANSACTION.equals(notificationDetailsViewModel.getNotificationType())) {
            handleTransactionNotification(notificationDetailsViewModel);
        } else {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_CENTER));
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_MESSAGES));
            NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_DETAILS).withParam(NotificationConstants.EXTRA.NOTIFICATION_VIEW_MODEL, notificationDetailsViewModel);
            navigationRouter.navigateTo(targetScreen);
        }
    }

    private void navigateToContextSwitchConfirmScreen(SwitchContextFedarationDetailsViewModel model) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_CONTEXT_SWITCH_CONFIRM);
        navigationTarget = navigationTarget.withParam(NotificationConstants.EXTRA.CONTEXT_SWITCH_MODEL, model);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void getContextSwitchModel(String cisNo) {
        fetchUserProfile(Long.parseLong(cisNo));

    }


    private boolean isContextSwitchNeeded(FBNotificationsViewModel fbNotificationsViewModel) {
        if (!StringUtils.isNullOrEmpty(fbNotificationsViewModel.getClient().getCisNo()) && !(fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.UBC) || fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.TBC) || fbNotificationsViewModel.getDistributionType().equals(NotificationConstants.DISTRIBUTION_TYPES.ECERT))) {
            return !fbNotificationsViewModel.getClient().getCisNo().equals(applicationStorage.getString(StorageKeys.CIS_NUMBER, StringUtils.EMPTY_STRING));
        }
        return false;
    }

    private void handleTransactionNotification(FBNotificationsViewModel notificationDetailsViewModel) {
        List<FBNotificationsViewModel.ResponseOption> responseOptions = notificationDetailsViewModel.getResponseOptions();
        if (responseOptions != null && !responseOptions.isEmpty() && responseOptions.get(0).getAction() != null) {
            String action = responseOptions.get(0).getAction();
            if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.REPORT_FRAUD)) {
                navigateToReportFraud(notificationDetailsViewModel);
            } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_LIST)) {
                navigateToDebitOrderList(notificationDetailsViewModel);
            }
        }

    }

    private void navigateToDebitOrderList(FBNotificationsViewModel notificationDetailsViewModel) {
        FBTransactionNotificationsViewModel model = getFBTransactionNotificationViewModel(notificationDetailsViewModel);
        if (model.getMetaAccNumber() != null) {
            handleDebitOrderListFlow(model.getMetaAccNumber());
        } else {
            handleNavigationErrorFlow();
        }
    }

    private void handleNavigationErrorFlow() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_ERROR));
    }

    @SuppressLint("CheckResult")
    private void handleDebitOrderListFlow(String accountNo) {
        Observable<CachableValue<Overview>> overviewObservable = mGetOverviewUseCase.execute();
        Observable<UserDetailData> userDetailsObservable = mGetUserDetailUseCase.execute(false);
        if (null != overviewObservable && null != userDetailsObservable) {
            Observable.zip(overviewObservable, userDetailsObservable,
                            (overviewCachableValue, userDetailData) -> {
                                if (null != overviewCachableValue && null != userDetailData) {
                                    Overview overviewValue = overviewCachableValue.get().clone();
                                    hasTransactableAccount = checkForAnyTransactableAccount(overviewValue);
                                    mClientType = userDetailData.getClientType();
                                    mFicaStatus = userDetailData.getFicaStatus();
                                    return getAccountSummary(overviewValue, accountNo);
                                }
                                return null;
                            }).compose(bindToLifecycle())
                    .subscribe(accountSummary -> {
                                if (null != view && null != accountSummary) {
                                    navigateToAccountDetails(accountSummary, NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_LIST);
                                } else {
                                    handleNavigationErrorFlow();
                                }
                            },
                            error -> {
                                if (view != null)
                                    handleNavigationErrorFlow();
                            });
        }
    }

    private AccountSummary getAccountSummary(Overview overview, String accountNum) {
        AccountSummary accountSummary = null;
        for (AccountsOverview accountsOverview : overview.accountsOverviews) {
            for (AccountSummary summary : accountsOverview.accountSummaries) {
                if (summary.getNumber() != null && summary.getNumber().equalsIgnoreCase(accountNum)) {
                    accountSummary = summary;
                }
            }
        }
        return accountSummary;

    }

    private boolean checkForAnyTransactableAccount(Overview overview) {
        if (isOverViewNotNull(overview)) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                if (isCreditCardAndEverydayBanking(accountsOverview)) {
                    for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
                        if (!accountSummary.isDormantAccount()) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private boolean isOverViewNotNull(Overview overview) {
        return overview != null && overview.accountsOverviews != null;
    }

    private boolean isCreditCardAndEverydayBanking(AccountsOverview accountsOverview) {
        return (accountsOverview.overviewType == OverviewType.CREDIT_CARDS ||
                accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                && accountsOverview.accountSummaries != null;
    }

    private void navigateToAccountDetails(AccountSummary accountSummary, String target) {
        navigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, canTransact())
                .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, mClientType)
                .withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate())
                .withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice())
                .withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit())
                .withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct())
                .withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit())
                .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, mFicaStatus)
                .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                .withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, target)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber())
        );
    }

    private boolean canTransact() {
        return hasTransactableAccount;
    }

    private void navigateToReportFraud(FBNotificationsViewModel notificationDetailsViewModel) {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.REPORT_FRAUD)) {
            FBTransactionNotificationsViewModel fbTransactionNotificationsViewModel = getFBTransactionNotificationViewModel(notificationDetailsViewModel);
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MORE_REPORT_FRAUD)
                    .withParam(za.co.nedbank.core.Constants.PARAM_REPORT_SUSPICIOUS, fbTransactionNotificationsViewModel)
                    .withParam(NavigationTarget.IS_FROM_REPORT, true));
        } else {
            handleNavigationErrorFlow();
        }

    }

    protected FBTransactionNotificationsViewModel getFBTransactionNotificationViewModel(FBNotificationsViewModel fbNotificationsViewModel) {
        FBNotificationsData notificationData = mFBNotificationsInnerDetailsDataToViewModelMapper.transformBack(fbNotificationsViewModel);
        return mFBNotificationsInnerDetailsDataToViewModelMapper.transformTransactionViewModel(notificationData);
    }

    public void navigateTo(String fragmentToBeNavigated) {
        switch (fragmentToBeNavigated) {
            case NotificationConstants.NAVIGATION_TARGET.CARD_FREEZE:
                loadCardsView(false);
                break;
            case NotificationConstants.NAVIGATION_TARGET.ACTIVATE_CARD:
                loadCardsView(false);
                break;
            case NotificationConstants.NAVIGATION_TARGET.TRANSACT:
                loadOverviewView();
                toggleTransactionFabMenu();
                break;
            case NotificationConstants.NAVIGATION_TARGET.VIEW_GREENBACKS:
                loadOverviewView();
                break;
            case NotificationConstants.NAVIGATION_TARGET.VIRTUAL_CARD:
                loadCardsView(true);
                break;
            default:
        }
    }


    public void navigateToAsperChatBot(String fragmentToBeNavigated) {
        switch (fragmentToBeNavigated) {
            case ChatbotConstants.NAVIGATION_TARGET.MY_CARDS:
                loadCardsView(false);
                break;
            case ChatbotConstants.NAVIGATION_TARGET.MORE:
                loadMoreOptionsView();
                break;
            case ChatbotConstants.NAVIGATION_TARGET.APPLY:
                handleApplyItemClick(false);
                break;
            case ChatbotConstants.NAVIGATION_TARGET.RECIPIENT:
                loadRecipientView();
                break;
            case ChatbotConstants.NAVIGATION_TARGET.APPLICATIONS:
                loadApplicationView();
                break;
            default:
                NBLogger.e("default", "handling default case");
                break;

        }
    }

    public void loadApplicationView() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_ENTRY_POINT, TrackingParam.VAL_APP_APPLICATIONS);
        analytics.sendEventActionWithMap(AppTracking.NON_TP_APPLICATIONS, cdata);
        if (view != null)
            view.showApplyNonTPUI();
    }

    public void updateTokenToServer() {
        deviceRegistrationMap = loadDeviceRegistrationMap();
        String uuid = applicationStorage.getString(NotificationConstants.STORAGE_KEYS.UUID, StringUtils.EMPTY_STRING);
        String lastSyncedToken = "";
        if (deviceRegistrationMap.containsKey(mDeviceRegistrationKey)) {
            lastSyncedToken = (String) deviceRegistrationMap.get(mDeviceRegistrationKey);
        }
        if (!TextUtils.isEmpty(uuid) && (StringUtils.isNullOrEmpty(lastSyncedToken)
                || isFbTokenChanged())) {
            sendFirebaseTokenToServer(uuid);
        }
    }

    private boolean isTokenValid() {
        boolean isTokenValid = false;
        String hmsToken = applicationStorage.getString(NotificationConstants.STORAGE_KEYS.HMS_TOKEN, StringUtils.EMPTY_STRING);
        if (!StringUtils.isNullOrEmpty(mFbToken)) {
            isTokenValid = true;
        }
        if (!isTokenValid && view.isHmsApiPreferred() && !StringUtils.isNullOrEmpty(hmsToken)) {
            isTokenValid = true;
        }
        return isTokenValid;
    }

    private boolean isFbTokenChanged() {
        String lastSyncedToken = "";
        String currentToken = mFbToken;
        if (StringUtils.isNullOrEmpty(currentToken) && view.isHmsApiPreferred()) {
            currentToken = applicationStorage.getString(NotificationConstants.STORAGE_KEYS.HMS_TOKEN, StringUtils.EMPTY_STRING);
        }
        if (deviceRegistrationMap.containsKey(mDeviceRegistrationKey)) {
            lastSyncedToken = (String) deviceRegistrationMap.get(mDeviceRegistrationKey);
        }
        return !org.apache.commons.lang3.StringUtils.equals(currentToken, lastSyncedToken);
    }

    private void sendFirebaseTokenToServer(String uniqueKey) {
        mEmcert = applicationStorage.getString(NotificationConstants.STORAGE_KEYS.EMCERT, StringUtils.EMPTY_STRING);
        FBUpdateTokenRequestData fbUpdateTokenRequestData = new FBUpdateTokenRequestData();
        FBUpdateTokenRequestData.Registration registration = new FBUpdateTokenRequestData.Registration();
        if (StringUtils.isNullOrEmpty(mFbToken) && view.isHmsApiPreferred()) {
            String mFbTokenToSend = String.format("%s%s", NotificationConstants.HMS_TOKEN_PREFIX, applicationStorage.getString(NotificationConstants.STORAGE_KEYS.HMS_TOKEN, StringUtils.EMPTY_STRING));
            registration.setFbToken(mFbTokenToSend);
            mFbToken = applicationStorage.getString(NotificationConstants.STORAGE_KEYS.HMS_TOKEN, StringUtils.EMPTY_STRING);
        } else {
            registration.setFbToken(mFbToken);
        }
        registration.setDeviceId(uniqueKey);
        registration.setPlatform(NotificationConstants.PLATFORM_TYPES.PLATFORM_ANDROID);
        registration.seteCert(mEmcert);
        fbUpdateTokenRequestData.setRegistration(registration);
        fbUpdateTokenUseCase.execute(fbUpdateTokenRequestData)
                .compose(bindToLifecycle())
                .subscribe(fbResponseData ->
                        handleResponse(fbResponseData, mFbToken), throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    private void handleResponse(FBResponseData fbResponseData, String fbToken) {
        if (fbResponseData != null && fbResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = fbResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && !resultDetailList.isEmpty()) {
                    if (isSuccessResult(resultData, resultDetailList)) {
                        // success case
                        handleSuccessResult(fbToken);
                        break;
                    } else {
                        handleResultFailureCase(resultDetailList);
                    }
                }
            }
        }
        isRegistrationApiInProgress = false;
    }

    private void handleResultFailureCase(ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null
                    && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                NBLogger.e(HomePresenter.class.getSimpleName(), resultDetailViewModel.getReason());
            }
        }
    }

    private boolean isSuccessResult(List<ResultDataModel> resultData, ArrayList<ResultDetailModel> resultDetailList) {
        int onlySuccessElement = 1;
        return resultData.size() == onlySuccessElement && resultDetailList.size() == onlySuccessElement
                && resultDetailList.get(0).getStatus() != null
                && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS);
    }

    private void handleSuccessResult(String fbToken) {
        deviceRegistrationMap.put(mDeviceRegistrationKey, fbToken);
        applicationStorage.putString(NotificationConstants.STORAGE_KEYS.DEVICE_REGISTRATION_MAP, view.convertHashmapToString(deviceRegistrationMap));
    }

    private Map<String, Object> loadDeviceRegistrationMap() {
        String jsonString = applicationStorage.getString(NotificationConstants.STORAGE_KEYS.DEVICE_REGISTRATION_MAP, (new JSONObject()).toString());
        return view.convertStringToHashmap(jsonString);
    }


    public void setupUUID() {
        String uuid = applicationStorage.getString(NotificationConstants.STORAGE_KEYS.UUID, StringUtils.EMPTY_STRING);
        if (StringUtils.isNullOrEmpty(uuid)) {
            mGetLoginUseCase
                    .execute()
                    .compose(bindToLifecycle())
                    .subscribe(nidUserName -> {
                        if (!StringUtils.isNullOrEmpty(nidUserName)) {
                            if (view != null) {
                                view.setUserId(nidUserName);
                            }
                        } else {
                            getEmcertId();
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));

        }

    }

    public void getEmcertId() {
        mGetEmcertIdUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(emcertId -> {
                    mEmcert = emcertId;
                    if (view != null)
                        view.setUserId(emcertId);
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void handleApplyItemClick(boolean isFromApplicationNewFlow) {
        if (isDemoMode()) {
            if (view != null) {
                view.showDemoModeEnabledDialog();
            }
        } else {
            inMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);

            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FICA_SELECT_INTENT)
                    .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_JOURNEY_FLAG, za.co.nedbank.core.Constants.FLOW_CONSTANTS.POST_LOGIN_MORE_APPLY_BORROW_FLOW)
                    .withParam(FLOW_FOR_APPLICATION, isFromApplicationNewFlow));
        }
    }

    public void handleFicaFlow() {
        boolean clientHasProductAcquisitionIntent = inMemoryStorage.getBoolean(StorageKeys.CLIENT_HAS_PRODUCT_ACQUISITION_INTENT, false);
        if (clientHasProductAcquisitionIntent) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FICA_EXISTING_CLIENT_CONTINUE));
        }
    }

    public void unsubscribeUnenrolledNotification() {
        FirebaseMessaging.getInstance().unsubscribeFromTopic(NotificationConstants.TOPICS.UNENROLLED);
        NBLogger.e("firebase_topic", "unsubscribeUnenrolledNotification");
    }

    public void subscribeEnrolledNotification() {
        FirebaseMessaging.getInstance().subscribeToTopic(NotificationConstants.TOPICS.ENROLLED);
        NBLogger.e("firebase_topic", "unsubscribeEnrolledNotification");
    }

    public void saveUUID() {
        String deviceID = view.getDeviceID();
        applicationStorage.putString(NotificationConstants.STORAGE_KEYS.UUID, String.format("%s%s%s", deviceID, StringUtils.UNDERSCORE, view.getUserID()));

    }

    public ClientType getClientType() {
        //Check for NTF and set ClientType = NTP
        String clientType = applicationStorage.getString(CLIENT_TYPE, ClientType.TP.getValue());
        return ClientType.getEnum(clientType);
    }

    boolean isDemoMode() {
        return inMemoryStorage.getBoolean(IS_DEMO, false);
    }

    void setLoginTimeStamp() {
        SimpleDateFormat loginFormat = new SimpleDateFormat(FormattingUtil.NGI_REQUEST_DATE_FORMAT);
        applicationStorage.putString(StorageKeys.NGI_LOGIN_TIMESTAMP, loginFormat.format(new Date()));
    }

    private boolean toShowItt() {
        return isBusinessUser() ? !mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_FOREXSBS)
                : (!mFeatureSetController.isFeatureDisabled(FeatureConstants.INTERNATIONAL_PAYMENTS_TOGGLE)
                && !inMemoryStorage.getBoolean(TRAVEL_CARD_MINOR, false));
    }

    private void prepareDataAndSendToAdapter(String action) {
        notificationAdapter.fetchDataFromNotification(action, null, null);
        applicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW);
    }

    public void checkAndShowFeatureDeepLinkScreen() {
        String featureName = applicationStorage.getString(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE, null);

        if (featureName == null) return;

        featureName = featureName.toUpperCase();  // Convert once to upper case for comparisons

        if (DeeplinkUtils.canMoveToDeeplinkFramework(featureName)) {
            prepareDataAndSendToAdapter(featureName);
        } else if (isFeatureForDeepLinkScreen(featureName)) {
            if (ClientType.TP == getClientType()) {
                navigateToDeepLinkIntermediateScreen(featureName);
            }
        } else if (isTransactionProductFeature(featureName)) {
            checkforTransactionProducts(featureName);
        } else if (isVirtualCardFeature(featureName)) {
            handleVirtualCardFeature();
        } else if (DynamicFeatureCardDetailEnum.MY_SMART_MONEY.getName().equalsIgnoreCase(featureName)) {
            navigateToSmartMoney();
        }  else if (DynamicFeatureCardDetailEnum.CREDITHEALTH.getName().equalsIgnoreCase(featureName)) {
            navigateToCreditHealth();
        } else if (DynamicFeatureCardDetailEnum.CREDIT_CARD.getName().equalsIgnoreCase(featureName)) {
            navigateToApplyCreditCard();
        } else if (DynamicFeatureCardDetailEnum.SMA_ONBOARDING.getName().equalsIgnoreCase(featureName)) {
            navigateToSMAOnboarding();
        } else if (NotificationUtils.canMoveToNavigationHandler(featureName)) {
            handleNavigationHandler(featureName);
        } else if (!DynamicFeatureCardDetailEnum.INTERNATIONAL_PAYMENTS.getName().equalsIgnoreCase(featureName)) {
            navigateToDeepLinkIntermediateScreen(featureName);
        } else {
            checkUserAgeAndLoadInternationalPayment(featureName);
        }
    }

    private boolean isFeatureForDeepLinkScreen(String featureName) {
        return featureName.equals(DynamicFeatureCardDetailEnum.MYCOVER_LIFE.getName()) ||
                featureName.equals(DynamicFeatureCardDetailEnum.MYCOVER_PL.getName()) ||
                featureName.equals(DynamicFeatureCardDetailEnum.MOABUSINESS.getName()) ||
                featureName.equals(DynamicFeatureCardDetailEnum.NEDBANK4ME.getName()) ||
                featureName.equalsIgnoreCase(DynamicFeatureCardDetailEnum.PAY_MY_BILLS.getName()) ||
                featureName.equalsIgnoreCase(DynamicFeatureCardDetailEnum.INSURANCE_FUNERAL.getName());
    }

    private boolean isTransactionProductFeature(String featureName) {
        return featureName.equals(DynamicFeatureCardDetailEnum.TRANSACTION_PRODUCT.getName()) ||
                featureName.equalsIgnoreCase(DynamicFeatureCardDetailEnum.EXPAND_CAMPAIGN.getName()) ||
                featureName.equalsIgnoreCase(DynamicFeatureCardDetailEnum.OFFER_FOR_YOU.getName());
    }

    private boolean isVirtualCardFeature(String featureName) {
        return featureName.equalsIgnoreCase(DynamicFeatureCardDetailEnum.VIRTUAL_CARDS.getName());
    }

    private void handleVirtualCardFeature() {
        navigateTo(NotificationConstants.NAVIGATION_TARGET.VIRTUAL_CARD);
        applicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW);
    }

    private void handleNavigationHandler(String featureName) {
        applicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        navigateToNavigationHandler(featureName);
    }
    private void navigateToNavigationHandler(String target) {
        NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.TARGET_GLOBAL_NAVIGATION_HANDLER)
                .withParam(NotificationConstants.Navigation.TARGET, target);
        if (target.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_MI_GOALS)
                || target.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_MI_GOALS_PLUS)
                || target.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_MI_GOALS_PREMIUM)) {
            targetScreen.withParam(NotificationConstants.Navigation.FROM, NotificationConstants.AjoConstants.POST_ERROR_MSG);
        } else {
            targetScreen.withParam(NotificationConstants.Navigation.FROM, NotificationConstants.AjoConstants.AJO_IN_APP);
        }
        navigationRouter.navigateTo(targetScreen);
    }

    private void navigateToApplyCreditCard() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.CREDIT_CARD_FACILITY)) {
            NavigationTarget navigationTarget = NavigationTarget.to(EnrollV2NavigatorTarget.BROWSE_CREDIT_CARD_ACTIVITY);
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FROM_APPLY_JOURNEY, CREDIT_CARD_FLOW);
            navigationTarget.withParam(NavigationTarget.PARAM_NTF_SECOND_LOGIN, (applicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) != FicaWorkFlow.IN_APP));
            navigationTarget.withParam(NavigationTarget.IS_DEEPLINK, true);
            navigationTarget.withParam(NavigationTarget.PARAM_NTF, inMemoryStorage.getBoolean(IS_NEW_TO_BANK, false));
            navigationRouter.navigateTo(navigationTarget);
        } else {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.PREAPPROVED_OFFERS_APPLY_ACTIVITY);
            navigationTarget.withParam(FLOW_JOURNEY_FLAG, CREDIT_CARD_FLOW);
            navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.NFPCreditCardParam.IS_PRE_LOGIN, (applicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) != FicaWorkFlow.IN_APP));
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FROM_APPLY_JOURNEY, CREDIT_CARD_FLOW);
            navigationRouter.navigateTo(navigationTarget);
        }
        //add the in app flow
        applicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
        inMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);

        //clear the feature value
        applicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW);
    }

    private void navigateToSMAOnboarding() {
        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.APPLY_INSESSION)
                .withParam(APPLY_DIRECTOR_TYPE_PARAM, DIRECTOR_TYPE_SINGLE)
                .withParam(IS_DEEP_LINK_FLOW, true)
                .withParam(za.co.nedbank.enroll_v2.Constants.IS_PRE_LOGIN_SMA, (applicationStorage.getInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.NEW_CUSTOMER) != FicaWorkFlow.IN_APP))
                .withParam(FOR_ME_FOR_BUSINESS_FLOW_TYPE_PARAM, MERCHANT_FLOW));
        //add the in app flow
        applicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
        inMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);

        //clear the feature value
        applicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW);
    }

    private void checkforTransactionProducts(String featureName) {
        String productID = inMemoryStorage.getString(StorageKeys.DEEP_LINK_PRODUCT_ID, StringUtils.EMPTY_STRING);
        if (productID.equalsIgnoreCase(DynamicFeatureCardDetailEnum.NEDBANK4ME.getName())) {
            inMemoryStorage.putInteger(StorageKeys.FICA_PRODUCT_FLOW, FicaProductFlow.MINOR);
        }
        inMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
        navigateToDeepLinkIntermediateScreen(featureName);
    }

    private void navigateToSmartMoney() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NFW_DASHBOARD_ACTIVITY)
                .withParam(IS_DEEP_LINK_FLOW, true));
        applicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW);
    }
    void navigateToCreditHealth() {
        inMemoryStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);

        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CREDIT_HEALTH_FLOW)
                .withParam(IS_DEEP_LINK_FLOW, true));
        applicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW);
    }


    void navigateToDeepLinkIntermediateScreen(String featureName) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.DEEP_LINK_INTERMEDIATE_SCREEN)
                .withParam(PARAM_EXTRA_FEATURE_NAME, featureName);
        navigationRouter.navigateWithResult(navigationTarget);// clearing the value
        applicationStorage.clearValue(za.co.nedbank.core.Constants.DEEP_LINK_FEATURE_TYPE);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW);
    }

    public void handleITAEnrolmentFlow() {
        boolean isITASetupDone = applicationStorage.getBoolean(ResponseStorageKey.IS_ITA_SETUP_SCREEN_SHOWN, false);
        if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ITA) || !view.isShowITA() || isITASetupDone)
            return;

        boolean isShown = applicationStorage.getBoolean(StorageKeys.IS_ITA_ENROLMENT_SHOWN, false);
        if (!isShown && !view.isITAEnrolled()) {
            applicationStorage.putBoolean(StorageKeys.IS_ITA_ENROLMENT_SHOWN, true);
            inMemoryStorage.putBoolean(StorageKeys.IS_ITA_ENROLMENT_SHOWN, true);
            itaEnrolmentUsecase.execute()
                    .compose(bindToLifecycle())
                    .subscribe(acknowledgeDto -> {
                        //do nothing
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));

        }
    }

    public void handleRetentionFlow() {
        if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_RETENTION_URL) ||
                mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_RETENTION))
            return;

        boolean isSMSAppLink = applicationStorage.getBoolean(DEEP_LINK_FROM_SMS_RETENTION, false);
        if (isSMSAppLink) {
            int smsRetentionCount = applicationStorage.getInteger(DEEP_LINK_FROM_SMS_RETENTION_COUNT, 0);
            if (smsRetentionCount == 0) {
                applicationStorage.putInteger(DEEP_LINK_FROM_SMS_RETENTION_COUNT, 1);
                applicationStorage.putBoolean(DEEP_LINK_FROM_SMS_RETENTION, false);
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.RETENTION_WELCOME_ACTIVITY));
            }
        }
    }

    private void fetchUserProfile(long cis) {
        mGetFedarationListUseCase.execute().subscribe(fedarationList -> {
            ArrayList<SwitchContextFedarationDetailsViewModel> fedarationViewModels =
                    (ArrayList<SwitchContextFedarationDetailsViewModel>) mSwitchContextDataViewModelMapper
                            .mapFedarationDetailResponse(fedarationList);

            for (SwitchContextFedarationDetailsViewModel model : fedarationViewModels) {
                if (cis == model.getEnterpriseCustomerNumber()) {
                    contextFedarationDetailsViewModel = model;
                    navigateToContextSwitchConfirmScreen(contextFedarationDetailsViewModel);
                }
            }

        }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void updateRetentionStatus() {
        applicationStorage.putBoolean(StorageKeys.IS_RET_FIRST_ACTIVATION, false);
    }

    public void clearEdbFicaStorage() {
        inMemoryStorage.clearValue(StorageKeys.HAS_GHOST_OFFERS);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.EDBOffersBundleKeys.IS_EDB_FICA_FLOW);
        inMemoryStorage.clearValue(za.co.nedbank.core.Constants.EDBOffersBundleKeys.OFFER_MODEL_EDB);
    }

    public void logoutChatHistory() {
        ChatbotAuthenticatedPayloadDataModel chatbotPayloadRequestDataModel = new ChatbotAuthenticatedPayloadDataModel();
        String sessionId = inMemoryStorage.getString(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID
                , StringUtils.EMPTY_STRING);


        if (!StringUtils.isNullOrEmpty(sessionId)) {
            ChatbotSessionMainRequestDataModel chatbotSessionMainRequestDataModel = CapiRequest.getRequestModel(true, sessionId,
                    ChatbotConstants.PARAMS.PARAM_LOGOUT_VALUE, chatbotPayloadRequestDataModel, mFeatureSetController, "");


            chatbotStartSessionUseCase.execute(true, chatbotSessionMainRequestDataModel)
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {

                    })
                    .doOnTerminate(() -> {

                    })
                    .subscribe(getFederatedUserDataReponseDataModel -> {
                        if (getFederatedUserDataReponseDataModel.getStatus().isOk()) {
                            inMemoryStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                            inMemoryStorage.clearValue(ResponseStorageKey.CHATBOT_ENGAGED_USER_SENT);
                            if (view != null) {
                                view.close();
                            }
                        } else {
                            inMemoryStorage.clearValue(ResponseStorageKey.CHATBOT_LAST_MESSAGE_SESSION_ID);
                            inMemoryStorage.clearValue(ResponseStorageKey.CHATBOT_ENGAGED_USER_SENT);
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
    }

    public void getUpdatedTrustedDeviceEcert() {
        boolean isEnrolShown = inMemoryStorage.getBoolean(StorageKeys.IS_ITA_ENROLMENT_SHOWN, false);
        if (isEnrolShown) {
            inMemoryStorage.putBoolean(StorageKeys.IS_ITA_ENROLMENT_SHOWN, false);
            getTrustedDeviceEcert();
        }
    }

    public void getTrustedDeviceEcert() {
        getTrustedDeviceUseCase.execute()
                .timeout(SharedConstant.ENTERSEKT_TIMEOUT_INTERVAL, TimeUnit.SECONDS)
                .subscribe(trustedDeviceManagementDto -> {
                    if (trustedDeviceManagementDto != null && trustedDeviceManagementDto.getResult().isOk()) {
                        if (trustedDeviceManagementDto.getTrustedDeviceEcert() != null) {
                            setTrustedDeviceFlag(trustedDeviceManagementDto.getTrustedDeviceEcert());
                        } else {
                            applicationStorage.putBoolean(StorageKeys.IS_TRUSTED_DEVICE, false);
                        }
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    void setTrustedDeviceFlag(String emCert) {
        mGetEmcertIdUseCase.execute()
                .subscribe(emcertId -> applicationStorage.putBoolean(StorageKeys.IS_TRUSTED_DEVICE, emcertId.equalsIgnoreCase(emCert)), throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    void handleNotificationEnableFlow() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_ENABLE));
    }

    public void navigateToGPaySuccessScreen() {
        navigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.G_PAY_ADD_SUCCESS));
    }

    public void navigateToGPayFailureScreen() {
        navigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.G_PAY_ADD_FAILURE));
    }

    boolean isFicaEnabled() {
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DIGITAL_ENABLE_REFICA);
    }

    boolean isOddRestrictionEnabled() {
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_DIGITAL_ENABLE_ODD_RESTRICTION);
    }

    public void checkUnboxingFlow(Bundle extras, AccountSummary currentAccount, AccountSummary savingsAccount,
                                  String accountType, List<BranchCodeViewModel> branchCodeViewModelList) {
        if (extras.containsKey(UNBOXING_MOVE_TO_SCREEN) && !hasCompletedUnboxingFlow) {
            if (extras.getInt(UNBOXING_MOVE_TO_SCREEN, 0) ==
                    za.co.nedbank.core.Constants.UnboxingFlowType.ADD_RECIPIENT) {
                hasCompletedUnboxingFlow = true;
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ADD_RECIPIENT));
            } else if (extras.getInt(UNBOXING_MOVE_TO_SCREEN, 0) ==
                    za.co.nedbank.core.Constants.UnboxingFlowType.SHARE_ACCOUNT_DETAIL) {
                if (currentAccount != null) {
                    hasCompletedUnboxingFlow = true;
                    openShareAccountDetailsActivity(currentAccount, accountType, branchCodeViewModelList);
                } else if (savingsAccount != null) {
                    hasCompletedUnboxingFlow = true;
                    openShareAccountDetailsActivity(savingsAccount, accountType, branchCodeViewModelList);
                }
            } else if (extras.getInt(UNBOXING_MOVE_TO_SCREEN, 0) ==
                    za.co.nedbank.core.Constants.UnboxingFlowType.TRANSACTIONS) {
                if (currentAccount != null) {
                    hasCompletedUnboxingFlow = true;
                    navigateToAccountDetails(currentAccount, NotificationConstants.NOTIFICATION_TYPES.TRANSACTION);
                } else if (savingsAccount != null) {
                    hasCompletedUnboxingFlow = true;
                    navigateToAccountDetails(savingsAccount, NotificationConstants.NOTIFICATION_TYPES.TRANSACTION);
                }
            }
        }
    }

    @SuppressLint("CheckResult")
    void openShareAccountDetailsActivity(AccountSummary accountSummary, String accountType, List<BranchCodeViewModel> branchCodeViewModelList) {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                            UserDetailViewModel userDetailVM = mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail);
                            if (view != null) {
                                NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.SHARE_ACC_INFO);
                                navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_NUMBER, accountSummary.getNumber());
                                navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_NAME, accountSummary.getAccountHolderName());
                                navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_TYPE, accountType);
                                navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_BRANCH_CODE,
                                        getBranchCodeValue(branchCodeViewModelList, accountSummary.getAccountCode()));
                                navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_CIS_NUMBER, userDetailVM.getCisNumber());
                                navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_EMAIL, userDetailVM.getEmailAddress());
                                navigationRouter.navigateTo(navigationTarget);
                            }
                        }, throwable -> NBLogger.e(TAG, Log.getStackTraceString(throwable))
                );


    }


    @SuppressLint("CheckResult")
    void getBranchCodeListIfShareAccount(int unboxingMoveToScreen) {
        if (unboxingMoveToScreen == za.co.nedbank.core.Constants.UnboxingFlowType.SHARE_ACCOUNT_DETAIL) {
            branchCodeUseCase.execute()
                    .compose(bindToLifecycle())
                    .subscribe(branchCodeDataModelList ->
                                    setBranchCode(modelToBranchCodeViewModelMapper
                                            .mapBranchCodeViewModel(branchCodeDataModelList)),
                            throwable -> NBLogger.e(TAG, Log.getStackTraceString(throwable)));
        }
    }

    void handleMemoryClearWhnEndDemo() {
        inMemoryStorage.clearValue(StorageKeys.MDM_CIS_NUMBER);
    }

    void setBranchCode(List<BranchCodeViewModel> branchCodeViewModelList) {
        view.setBranchCodeList(branchCodeViewModelList);
    }

    String getBranchCodeValue(List<BranchCodeViewModel> branchCodeViewModelList, String accountType) {
        if (branchCodeViewModelList != null && branchCodeViewModelList.size() > 0) {
            for (int i = 0; i < branchCodeViewModelList.size(); i++) {
                if (accountType.contains(branchCodeViewModelList.get(i).getAccountType())) {
                    return branchCodeViewModelList.get(i).getBranchCode();
                }
            }
        }
        return StringUtils.EMPTY_STRING;
    }
}