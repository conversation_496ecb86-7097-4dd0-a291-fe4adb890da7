package za.co.nedbank.ui.data.entity.closed_account;

import com.squareup.moshi.Json;

import java.util.List;

import za.co.nedbank.core.data.metadatav3.entity.MetaDataEntity;

public class ClosedAccountResponseEntity {

    @Json(name = "data")
    private List<ClosedAccountEntity> data = null;

    @Json(name = "metadata")
    private MetaDataEntity metaDataEntity;

    public List<ClosedAccountEntity> getData() {
        return data;
    }


    public MetaDataEntity getMetaDataEntity() {
        return metaDataEntity;
    }
}