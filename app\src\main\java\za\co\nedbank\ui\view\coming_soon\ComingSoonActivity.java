/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.coming_soon;

import android.os.Bundle;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityComingSoonBinding;

/**
 * Created by piyushgupta01 on 09-07-2017.
 */

public class ComingSoonActivity extends NBBaseActivity {

    private ActivityComingSoonBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityComingSoonBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        setUpToolBar();
    }

    private void setUpToolBar() {
        initToolbar(binding.toolbar, true, false);
    }

}
