/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.mapper.money_requests;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsDataModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsViewModel;


/**
 * Created by swapnil.gawande on 2/25/2018.
 */

public class MoneyRequestsDataModelToViewModelMapper {
    @Inject
    MoneyRequestsDataModelToViewModelMapper() {
    }

    public List<MoneyRequestsViewModel> mapMoneyRequestsDataModelToViewModel(ArrayList<MoneyRequestsDataModel> moneyRequestsDataModels) {
        List<MoneyRequestsViewModel> moneyRequestsViewModels = new ArrayList<>();
        if (moneyRequestsDataModels != null && moneyRequestsDataModels.size() > 0) {
            for (MoneyRequestsDataModel moneyRequestsDataModel : moneyRequestsDataModels) {
                MoneyRequestsViewModel moneyRequestsViewModel = new MoneyRequestsViewModel();
                moneyRequestsViewModel.setReminder(moneyRequestsDataModel.isReminder());
                moneyRequestsViewModel.setPayLater(moneyRequestsDataModel.isPayLater());
                moneyRequestsViewModel.setPaymentRequestId(moneyRequestsDataModel.getPaymentRequestId());
                moneyRequestsViewModel.setPaid(moneyRequestsDataModel.isPaid());
                moneyRequestsViewModel.setReject(moneyRequestsDataModel.isReject());
                moneyRequestsViewModel.setRequestDate(moneyRequestsDataModel.getRequestDate());
                moneyRequestsViewModel.setPartyName(moneyRequestsDataModel.getPartyName());
                moneyRequestsViewModel.setRequestAmount(moneyRequestsDataModel.getRequestAmount());
                moneyRequestsViewModel.setPartyPhoneNumber(moneyRequestsDataModel.getPartyPhoneNumber());
                moneyRequestsViewModel.setPartyDescription(moneyRequestsDataModel.getPartyDescription());
                moneyRequestsViewModel.setRequestStatus(moneyRequestsDataModel.getRequestStatus());
                moneyRequestsViewModel.setPartyAccountNumber(moneyRequestsDataModel.getPartyAccountNumber());
                moneyRequestsViewModel.setPartyAccountType(moneyRequestsDataModel.getPartyAccountType());
                moneyRequestsViewModel.setExpiryDate(moneyRequestsDataModel.getExpiryDate());
                moneyRequestsViewModel.setCurrentDate(moneyRequestsDataModel.getCurrentDate());
                moneyRequestsViewModel.setProcessDate(moneyRequestsDataModel.getProcessDate());
                moneyRequestsViewModels.add(moneyRequestsViewModel);
            }
        }
        return moneyRequestsViewModels;
    }
}
