/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.content.Context;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import za.co.nedbank.R;
import za.co.nedbank.core.utils.StringUtils;

public class ViewMoneyRequestsPagerAdapter extends FragmentPagerAdapter {
    private final Context mContext;
    private final ViewMoneyRequestPage[] viewMoneyRequestPages;

    ViewMoneyRequestsPagerAdapter(final Context context, final FragmentManager fragmentManager) {
        super(fragmentManager);
        mContext = context;
        viewMoneyRequestPages = getViewMoneyRequestPages();
    }

    @Override
    public Fragment getItem(int position) {
        if (viewMoneyRequestPages[position] == ViewMoneyRequestPage.RECEIVED) {
            return ReceivedMoneyRequestsFragment.newInstance();
        } else {
            return SentMoneyRequestsFragment.newInstance();
        }
    }

    @Override
    public int getCount() {
        return viewMoneyRequestPages != null ? viewMoneyRequestPages.length : 0;
    }

    @Override
    public CharSequence getPageTitle(int position) {
        switch (viewMoneyRequestPages[position]) {
            case RECEIVED:
                return mContext.getString(R.string.view_money_requests_tab_received);
            case SENT:
                return mContext.getString(R.string.view_money_requests_tab_sent);
            default:
                return StringUtils.EMPTY_STRING;
        }
    }

    private ViewMoneyRequestPage[] getViewMoneyRequestPages() {
        return new ViewMoneyRequestPage[]{ViewMoneyRequestPage.RECEIVED, ViewMoneyRequestPage.SENT};
    }

    private enum ViewMoneyRequestPage {
        RECEIVED,
        SENT
    }
}
