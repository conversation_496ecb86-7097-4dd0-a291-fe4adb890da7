/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import java.util.Map;

import za.co.nedbank.payment.pay.view.model.SelectedBankViewModel;

/**
 * Created by priyadhingra on 9/4/2017.
 */

public interface AddRecipientView extends BaseRecipientView {
    void showAddRecipientApiError(String... message);
    void showAddRecipientApiError();
    void showAddRecipientApiError(Map<Integer, String> matchBackNumber, String... message);
    void postAddRecipient();
    void showApproveItApiError(String... message);
    void onRecipientAdded();
    void handleFewRecipientAdditionFailedError(String... message);
    SelectedBankViewModel getSelectedBankViewModel();
    void setMatchBackNumber(int matchBackNumber);
}
