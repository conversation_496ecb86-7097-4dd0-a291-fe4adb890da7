package za.co.nedbank.ui.domain.model.pop;

import java.util.List;

import za.co.nedbank.core.domain.model.metadata.MetaDataModel;

public class TransactionHistoryParentData {
    private List<TransactionHistoryData> transactionHistoryData;
    private MetaDataModel transactionMetadataModel;

    public List<TransactionHistoryData> getTransactionHistoryData() {
        return transactionHistoryData;
    }

    public void setTransactionHistoryData(List<TransactionHistoryData> transactionHistoryData) {
        this.transactionHistoryData = transactionHistoryData;
    }

    public MetaDataModel getTransactionMetadataModel() {
        return transactionMetadataModel;
    }

    public void setTransactionMetadataModel(MetaDataModel transactionMetadataModel) {
        this.transactionMetadataModel = transactionMetadataModel;
    }
}
