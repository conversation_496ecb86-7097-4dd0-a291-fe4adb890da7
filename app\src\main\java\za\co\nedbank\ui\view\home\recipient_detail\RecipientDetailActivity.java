/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.recipient_detail;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import java.util.Map;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.SimpleDividerItemDecoration;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.mapper.RecipientViewModelToEntityMapper;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryCollectiveDataViewModel;
import za.co.nedbank.databinding.ActivityRecipientDetailBinding;
import za.co.nedbank.payment.common.domain.data.mapper.beneficiary.user.UserBeneficiaryMapper;
import za.co.nedbank.payment.common.view.IPayChildScreenTypes;
import za.co.nedbank.payment.common.view.beneficiary.user.model.SelectedUserBeneficiaryViewModel;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.add_recipient.BankAccountAdapter;
import za.co.nedbank.ui.view.home.add_recipient.BaseRecipientActivity;
import za.co.nedbank.ui.view.home.add_recipient.CreditCardAdapter;
import za.co.nedbank.ui.view.home.add_recipient.ElectricityMeterAdapter;
import za.co.nedbank.ui.view.home.add_recipient.EmailAdapter;
import za.co.nedbank.ui.view.home.add_recipient.MobileNumberAdapter;
import za.co.nedbank.ui.view.home.add_recipient.ShapIDAdapter;
import za.co.nedbank.uisdk.widget.NBSnackbar;

/**
 * Created by priyadhingra on 9/12/2017.
 */

public class RecipientDetailActivity extends BaseRecipientActivity implements RecipientDetailView {

    @Inject
    RecipientDetailPresenter mRecipientDetailPresenter;
    @Inject
    RecipientViewModelToEntityMapper mEditRecipientViewModelToEntityMapper;
    @Inject
    UserBeneficiaryMapper mUserBeneficiaryMapper;
    private boolean mIsItemEdited;
    private boolean mSelectRecipientAccount = false;
    private UserBeneficiaryCollectiveDataViewModel mUserBeneficiaryCollectiveDataViewModel;
    private ISectionedListItemSelectedListener itemSelectedListener = new ISectionedListItemSelectedListener() {
        @Override
        public void itemSelected(int sectionPosition, int itemPositionInSection) {
            setupResult(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.values()[sectionPosition], itemPositionInSection);
        }
    };
    private ActivityRecipientDetailBinding binding;

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        binding = ActivityRecipientDetailBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        super.onCreate(savedInstanceState, mRecipientDetailPresenter);
        initToolbar(binding.toolbar, true, false);
        if (getIntent() != null) {
            mUserBeneficiaryCollectiveDataViewModel = getIntent().getParcelableExtra(Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL);
            mSelectRecipientAccount = getIntent().getBooleanExtra(Constants.EXTRAS.SELECT_RECIPIENT_ACCOUNT, false);
            if (mUserBeneficiaryCollectiveDataViewModel != null) {
                mRecipientViewModel = mEditRecipientViewModelToEntityMapper.mapToRecipientViewModel(mUserBeneficiaryCollectiveDataViewModel);
            }
            setUpViewAsPerIntentData();
        }
        binding.tvEdit.setOnClickListener(v -> handleEditClick());
    }

    @Override
    public String getDefaultBankNameText() {
        return StringUtils.EMPTY_STRING;
    }

    void handleEditClick() {
        mRecipientDetailPresenter.handleEditClick(mRecipientViewModel);
    }

    private void setUpViewAsPerIntentData() {
        if (!TextUtils.isEmpty(mRecipientViewModel.getRecipientName())) {
            binding.tvNameInitial.setText(StringUtils.getNameInitials(mRecipientViewModel.getRecipientName()));
            binding.tvLabelRecipientName.setText(mRecipientViewModel.getRecipientName());
        }
        setupRecyclerViews();
        setLayoutParamOfRecyclerView(false);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mRecipientDetailPresenter.bind(this);
        mRecipientDetailPresenter.trackOnPageLoad(0);
        if (mIsItemEdited) {
            mRecipientDetailPresenter.callGetSingleRecipientUseCase(String.valueOf(mRecipientViewModel.getContactCardId()));
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        mRecipientDetailPresenter.unbind();
    }

    @Override
    protected void setUpRecyclerViewAdapter() {
        if (mRecipientViewModel != null) {
            ViewUtils.showViews(rvAccount);
            if (mBankAccountNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getBankAccountViewDataModelList() != null && mRecipientViewModel.getBankAccountViewDataModelList().size() > 0) {
                mBankAccountViewDataModelList = mRecipientViewModel.getBankAccountViewDataModelList();
                mBankAccountAdapter = new BankAccountAdapter(this, mBankAccountNbFlexibleItemCountRecyclerviewModel, mBankAccountViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
                mBankAccountAdapter.setINBRecyclerViewListener(this);
                mBankAccountAdapter.setIActivityAdapterComListener(this);
                mBankAccountAdapter.setEditable(false);
                rvAccount.addItemDecoration(new SimpleDividerItemDecoration(this));
                rvAccount.setAdapter(mBankAccountAdapter);
            } else {
                ViewUtils.hideViews(rvAccount);
            }

            buildMobileNumberAdapter();

            buildElectricityMeterAdapter();

            buildEmailAdapter();

            buildCreditCardAdapter();

            buildShapIDAdapter();
        }
    }

    private void buildMobileNumberAdapter() {
        if (mMobileNumberNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getMobileNumberViewDataModelList() != null && mRecipientViewModel.getMobileNumberViewDataModelList().size() > 0) {
            ViewUtils.showViews(rvMobileNumber);
            mMobileNumberViewDataModelList = mRecipientViewModel.getMobileNumberViewDataModelList();
            mMobileNumberAdapter = new MobileNumberAdapter(this, mMobileNumberNbFlexibleItemCountRecyclerviewModel,
                    mMobileNumberViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
            mMobileNumberAdapter.setINBRecyclerViewListener(this);
            mMobileNumberAdapter.setEditable(false);
            rvMobileNumber.addItemDecoration(new SimpleDividerItemDecoration(this));
            rvMobileNumber.setAdapter(mMobileNumberAdapter);
        } else {
            ViewUtils.hideViews(rvMobileNumber);
        }
    }

    private void buildEmailAdapter() {
        if (mEmailNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getEmailViewDataModelList() != null && mRecipientViewModel.getEmailViewDataModelList().size() > 0) {
            ViewUtils.showViews(rvEmail);
            mEmailViewDataModelList = mRecipientViewModel.getEmailViewDataModelList();
            mEmailAdapter = new EmailAdapter(this, mEmailNbFlexibleItemCountRecyclerviewModel, mEmailViewDataModelList, null, null);
            mEmailAdapter.setEditable(false);
            rvEmail.setAdapter(mEmailAdapter);
        } else {
            ViewUtils.hideViews(rvEmail);
        }
    }

    private void buildElectricityMeterAdapter() {
        if (mElectricityNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getElectricityMeterViewDataModelList() != null && mRecipientViewModel.getElectricityMeterViewDataModelList().size() > 0) {
            ViewUtils.showViews(rvElectricity);
            mElectricityMeterViewDataModelList = mRecipientViewModel.getElectricityMeterViewDataModelList();
            mElectricityMeterAdapter = new ElectricityMeterAdapter(this, mElectricityNbFlexibleItemCountRecyclerviewModel,
                    mElectricityMeterViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
            mElectricityMeterAdapter.setINBRecyclerViewListener(this);
            mElectricityMeterAdapter.setEditable(false);
            rvElectricity.addItemDecoration(new SimpleDividerItemDecoration(this));
            rvElectricity.setAdapter(mElectricityMeterAdapter);
        } else {
            ViewUtils.hideViews(rvElectricity);
        }
    }

    private void buildCreditCardAdapter() {
        if (mCreditCardNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getCreditCardViewDataModelList() != null && mRecipientViewModel.getCreditCardViewDataModelList().size() > 0) {
            ViewUtils.showViews(rvCreditCard);
            mCreditCardViewDataModelList = mRecipientViewModel.getCreditCardViewDataModelList();
            mCreditCardAdapter = new CreditCardAdapter(this, mCreditCardNbFlexibleItemCountRecyclerviewModel,
                    mCreditCardViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
            mCreditCardAdapter.setEditable(false);
            rvCreditCard.addItemDecoration(new SimpleDividerItemDecoration(this));
            rvCreditCard.setAdapter(mCreditCardAdapter);
        } else {
            ViewUtils.hideViews(rvCreditCard);
        }
    }

    private void buildShapIDAdapter() {
        if (mShapIdNbFlexibleItemCountRecyclerviewModel != null && CollectionUtils.isNotEmpty(mRecipientViewModel.getShapIdViewDataModelList())) {
            ViewUtils.showViews(rvCreditCard);
            mShapIDViewDataModelList = mRecipientViewModel.getShapIdViewDataModelList();
            mShapIDAdapter = new ShapIDAdapter(this, mShapIdNbFlexibleItemCountRecyclerviewModel,
                    mShapIDViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
            mShapIDAdapter.setEditable(false);
            rvShapeID.addItemDecoration(new SimpleDividerItemDecoration(this));
            rvShapeID.setAdapter(mShapIDAdapter);
        } else {
            ViewUtils.hideViews(rvShapeID);
        }
    }

    private void setupResult(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE view_type, int itemPositionInSection) {
        Bundle bundle = new Bundle();
        bundle.putInt(Constants.EXTRAS.SCREEN_TYPE, IPayChildScreenTypes.BENEFICIARY_SCREEN);
        SelectedUserBeneficiaryViewModel selectedUserBeneficiaryViewModel = null;
        switch (view_type) {
            case BANK:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getBankBeneficiaryList().get(itemPositionInSection));
                break;
            case CREDIT_CARD:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getCreditCardBeneficiaryList().get(itemPositionInSection));
                break;
            case MOBILE_NUMBER:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getMobileBeneficiaryList().get(itemPositionInSection));
                break;
            case ELECTRICITY_METER:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getElectricityBeneficiaryList().get(itemPositionInSection));
                break;
            case SHAPID:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getRppBeneficiaryList().get(itemPositionInSection));
                break;
            case EMAIL:
                break;
        }
        bundle.putParcelable(PayNavigatorTarget.EXTRAS.BENEFICIARY_SELECTED, selectedUserBeneficiaryViewModel);
        Intent intent = new Intent();
        intent.putExtras(bundle);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    @Override
    public void setResult(Map<String, Object> resultMap) {
        if (resultMap != null && resultMap.size() > 0) {

            Object isItemDeleted = resultMap.get(Constants.EXTRAS.IS_ITEM_DELETED);
            Object errorModel = resultMap.get(Constants.EXTRAS.ERROR_MESSAGE);
            if (errorModel instanceof String[]) {
                String[] errorStrings = (String[]) errorModel;
                if (errorStrings.length > 0) {
                    NBSnackbar.instance().action(getString(R.string.snackbar_dismiss), () -> {
                    }).build(lnrTopView, getString(R.string.few_recipient_failed_error));
                }
            }
            if (isItemDeleted instanceof Boolean) {
                sendBeneficiaryListRefreshIntent();
            } else {
                Object isItemEdited = resultMap.get(Constants.EXTRAS.IS_ITEM_EDITED);
                if (isItemEdited instanceof Boolean) {
                    mIsItemEdited = (boolean) isItemEdited;
                }
            }
        }
    }

    @Override
    public void supportFinishAfterTransition() {
        mRecipientDetailPresenter.handleBackPressed();
    }

    @Override
    public void dataValidationSuccess() {
        //data validation not required
    }

    @Override
    public void onRecipientDetailFetched(RecipientViewModel recipientViewModel) {
        ViewUtils.hideViews(binding.pbRecyclerViewLoading);
        setEnabledActivityTouch(true);
        if (recipientViewModel != null) {
            mRecipientViewModel = recipientViewModel;
            setUpViewAsPerIntentData();
        }
    }

    @Override
    public void showGetSingleRecipientApiError(String... message) {
        setEnabledActivityTouch(true);
        ViewUtils.hideViews(binding.pbRecyclerViewLoading);
        NBSnackbar.instance().action(getString(R.string.snackbar_action_retry), () -> mRecipientDetailPresenter.callGetSingleRecipientUseCase(String.valueOf(mRecipientViewModel.getContactCardId()))).build(lnrTopView, message);
    }

    @Override
    public void showProgressBar() {
        ViewUtils.showViews(binding.pbRecyclerViewLoading);
    }

    @Override
    public void finishOnBack() {
        if (mIsItemEdited) {
            sendBeneficiaryListRefreshIntent();
        } else {
            finish();
        }
    }

    @Override
    public void onBackPressed() {
        mRecipientDetailPresenter.handleBackPressed();
    }

    @Override
    public int getMatchBackNumber() {
        return 0;
    }
}
