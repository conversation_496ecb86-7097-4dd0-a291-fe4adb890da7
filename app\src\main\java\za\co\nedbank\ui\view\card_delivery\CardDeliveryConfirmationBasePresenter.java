package za.co.nedbank.ui.view.card_delivery;

import java.util.HashMap;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.card_delivery.PostCardDeliveryOptionRequestData;
import za.co.nedbank.core.domain.model.card_delivery.PostCardDeliveryOptionsResponseData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.card_delivery.PostCardDeliveryOptionUseCase;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.services.domain.model.metadata.MetaDataModel;
import za.co.nedbank.services.domain.usecase.cards.ReplaceCardUseCase;
import za.co.nedbank.services.view.mapper.ReplaceCardViewModelToDataMapper;
import za.co.nedbank.services.view.model.ReplaceCardViewModel;

public abstract class CardDeliveryConfirmationBasePresenter<V extends CardDeliveryConfirmationBaseView> extends NBBasePresenter<V> {

    protected final PostCardDeliveryOptionUseCase postCardDeliveryOptionUseCase;
    protected final ReplaceCardUseCase replaceCardUseCase;
    protected NavigationRouter navigationRouter;
    protected ReplaceCardViewModelToDataMapper replaceCardViewModelToDataMapper;
    protected ApplicationStorage applicationStorage;
    protected Analytics analytics;
    protected ErrorHandler errorHandler;

    protected CardDeliveryConfirmationBasePresenter(PostCardDeliveryOptionUseCase postCardDeliveryOptionUseCase,
                                                    ReplaceCardUseCase replaceCardUseCase,
                                                    ReplaceCardViewModelToDataMapper replaceCardViewModelToDataMapper,
                                                    NavigationRouter navigationRouter,
                                                    ApplicationStorage applicationStorage,
                                                    Analytics analytics,
                                                    ErrorHandler errorHandler) {
        this.postCardDeliveryOptionUseCase = postCardDeliveryOptionUseCase;
        this.replaceCardUseCase = replaceCardUseCase;
        this.replaceCardViewModelToDataMapper = replaceCardViewModelToDataMapper;
        this.navigationRouter = navigationRouter;
        this.applicationStorage = applicationStorage;
        this.analytics = analytics;
        this.errorHandler = errorHandler;
    }

    protected void handleConfirmation() {

        if (isEficaFlow()) {
            handleFicaConfirmation();
        } else if (isReplaceCardFlow()) {
            handleReplaceCardConfirmation();
        }
    }

    private void handleReplaceCardConfirmation() {
        replaceCardUseCase
                .execute(replaceCardViewModelToDataMapper.map(createReplaceCardRequestEntity()))
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> view.showConfirmButtonLoading(true))
                .doFinally(() -> view.showConfirmButtonLoading(false))
                .subscribe(this::showReplaceCardConfirmationResult,
                        this::showReplaceCardConfirmationError);
    }

    protected abstract ReplaceCardViewModel createReplaceCardRequestEntity();

    //override this method to do extra handling e.g. analytics
    protected void showReplaceCardConfirmationError(Throwable throwable) {
        navigateToResult(false);
    }

    //override this method to do extra handling e.g. analytics
    protected void showReplaceCardConfirmationResult(MetaDataModel metaDataModel) {
        navigateToResult(metaDataModel.getResultDetailModel().isOk());
    }

    protected void handleFicaConfirmation() {
        postCardDeliveryOptionUseCase
                .execute(createPostDeliveryOptionRequestEntity())
                .compose(bindToLifecycle())
                .doOnSubscribe(d -> view.showConfirmButtonLoading(true))
                .doFinally(() -> view.showConfirmButtonLoading(false))
                .subscribe(this::showEficaCardDeliverConfirmationResult,
                        this::showEficaCardDeliverConfirmationError);
    }

    protected abstract PostCardDeliveryOptionRequestData createPostDeliveryOptionRequestEntity();

    //override this method to do extra handling e.g. analytics
    protected void showEficaCardDeliverConfirmationResult(PostCardDeliveryOptionsResponseData responseData) {
        za.co.nedbank.core.domain.model.metadata_v2.MetaDataModel metaData = responseData.getMetaDataModel();
        navigateToResult(CardDeliveryUtils.isSuccess(metaData));
        if (CardDeliveryUtils.isSuccess(metaData)) {
            sendCardDeliverSuccessAnalytics();
        } else {
            sendErrorAnalytics(true, CardDeliveryUtils.extractMessage(metaData), CardDeliveryUtils.extractResponseCode(metaData));
        }
    }

    //override this method to do extra handling e.g. analytics
    protected void showEficaCardDeliverConfirmationError(Throwable throwable) {
        Error error = errorHandler.getErrorMessage(throwable);
        sendErrorAnalytics(false, error.getMessage(), String.valueOf(error.getCode()));
        navigateToResult(false);
    }

    protected void navigateToResult(boolean isSuccess) {
        NavigationTarget target = NavigationTarget.to(NavigationTarget.CARD_DELIVERY_RESULT)
                .withAllData(true)
                .withParam(NavigationTarget.PARAM_IS_CARD_ORDERING_SUCCESS, isSuccess);
        navigationRouter.navigateTo(target);
        view.close();
    }


    protected boolean isEficaFlow() {
        return NavigationTarget.VALUE_CARD_DELIVERY_FLOW_EFICA.equals(view.getFlow());
    }

    protected boolean isReplaceCardFlow() {
        return NavigationTarget.VALUE_CARD_DELIVERY_FLOW_BLOCK_AND_REPLACE.equals(view.getFlow());
    }

    public void retryClose() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CARD_DELIVERY_OPTIONS)
                .withParam(NavigationTarget.PARAM_IS_CARD_ORDERING_SUCCESS, false)
                .withIntentFlagClearTopSingleTop(true));
        view.close();
    }

    public void sendPageAnalytics(String pageName, String pageCategory) {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);
        contextData.setPageCategory(pageCategory);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductAccount(getProductName());

        if (isEficaFlow()) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
        } else if (isReplaceCardFlow()) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            contextData.setFeature(view.getCardActionName());
        }

        contextData.setSubFeature(view.getCardDeliverySubFeature());
        analytics.sendEventActionWithMap(pageName, contextData.getCdata());
    }

    protected String getProductName() {
        return applicationStorage.getString(StorageKeys.CARD_DELIVERY_SELECTED_PRODUCT, StringUtils.EMPTY_STRING);
    }

    private void sendCardDeliverSuccessAnalytics() {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductAccount(getProductName());
        if (isEficaFlow()) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
        } else {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            contextData.setFeature(view.getCardActionName());
        }
        contextData.setFeatureCategoryCount();
        contextData.setFeatureCount();
        contextData.setSubFeature(view.getCardDeliverySubFeature());
        contextData.setSubFeatureCount();
        contextData.setCompletions();
        contextData.setTimeSpentPerFeature(String.valueOf(getTimeSpentPerFeature()));

        analytics.sendEventActionWithMap(CardDeliveryAnalytics.EVENT_CM_ORDER_CARD_SUCCESSFUL, contextData.getCdata());
    }

    private void sendErrorAnalytics(boolean isApiFailure, String message, String apiErrorCode) {
        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);

        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductAccount(getProductName());

        if (isEficaFlow()) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
        } else {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            contextData.setFeature(view.getCardActionName());
        }
        contextData.setFeatureCategoryCount();
        contextData.setFeatureCount();
        contextData.setSubFeature(view.getCardDeliverySubFeature());
        contextData.setSubFeatureCount();

        analytics.trackFailure(isApiFailure, CardDeliveryAnalytics.EVENT_CM_ORDER_CARD_FAILURE, CardDeliveryAnalytics.API_USE_CAR, message, apiErrorCode, contextData.getCdata());

    }

    private long getTimeSpentPerFeature() {
        long startTime = applicationStorage.getLong(StorageKeys.CARD_DELIVERY_FEATURE_START_TIME, 0);
        return startTime != 0 ? (System.currentTimeMillis() - startTime) / 1000 : 0;
    }

}

