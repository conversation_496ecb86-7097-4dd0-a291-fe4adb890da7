package za.co.nedbank.ui.view.pop.success_pop;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;

public class SuccessPopPresenter extends NBBasePresenter<SuccessPopView> {

    private final NavigationRouter mNavigationRouter;

    @Inject
    SuccessPopPresenter(final NavigationRouter navigationRouter) {
        this.mNavigationRouter = navigationRouter;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

    void navigateToHomeScreen(boolean isLandingToOverView, boolean isFromRecipientHistory) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.HOME);
        if (!isLandingToOverView)
            navigationTarget.withParam(Constants.EXTRAS.SCREEN_TYPE, za.co.nedbank.core.Constants.FLOW_CONSTANTS.SHARE_POP);
        if (isFromRecipientHistory)
            navigationTarget.withParam(Constants.BUNDLE_KEYS.IS_FROM_RECIPIENT_HISTORY, true);
        navigationTarget.withIntentFlagClearTop(true);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void navigateToPayLandingScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.PAY_LANDING);
        navigationTarget.withIntentFlagClearTopSingleTop(true);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void navigateToPayDoneScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.PAY_DONE);
        navigationTarget.withIntentFlagClearTopSingleTop(true);
        mNavigationRouter.navigateTo(navigationTarget);
    }
}