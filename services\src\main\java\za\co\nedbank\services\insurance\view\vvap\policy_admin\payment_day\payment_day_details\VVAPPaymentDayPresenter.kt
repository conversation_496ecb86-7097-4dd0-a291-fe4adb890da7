package za.co.nedbank.services.insurance.view.vvap.policy_admin.payment_day.payment_day_details

import android.annotation.SuppressLint
import io.reactivex.Observable
import za.co.nedbank.core.navigation.NavigationRouter
import za.co.nedbank.core.navigation.NavigationTarget
import za.co.nedbank.core.tracking.Analytics
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.tracking.adobe.AdobeContextData
import za.co.nedbank.services.Constants
import za.co.nedbank.services.insurance.domain.data.response.vvap.claim.policy_detail.PolicyDetailResponseDataModel
import za.co.nedbank.services.insurance.domain.usecase.personal_lines.PLPolicyDetailUseCase
import za.co.nedbank.services.insurance.domain.usecase.vvap.claim.VVAPPolicyDetailUseCase
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.MONTHLY_FREQUENCY
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.ParamKeys.PARAM_IS_FUNERAL_FLOW
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.ParamKeys.PARAM_IS_PL_FLOW
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.claim.PolicyDetailRequestViewToDataMapper
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.claim.PolicyDetailResponseDataToViewMapper
import za.co.nedbank.services.insurance.view.other.model.request.vvaps.claim.policy_detail.PolicyDetailRequestViewModel
import za.co.nedbank.services.insurance.view.other.model.response.vvap.claim.policy_detail.PolicyDetailResponseViewModel
import za.co.nedbank.services.insurance.view.vvap.policy_admin.base.VVAPAdminPolicyBasePresenter
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget
import javax.inject.Inject

class VVAPPaymentDayPresenter @Inject constructor(
    private val analytics: Analytics,
    private val mNavigationRouter: NavigationRouter,
    private val policyDetailUseCasePL: PLPolicyDetailUseCase,
    private val policyDetailUseCaseVVAP: VVAPPolicyDetailUseCase,
    private val requestViewToDataMapper: PolicyDetailRequestViewToDataMapper,
    private val responseDataToViewMapper: PolicyDetailResponseDataToViewMapper
) : VVAPAdminPolicyBasePresenter<VVAPPaymentDayView>(
    policyDetailUseCaseVVAP,
    requestViewToDataMapper,
    responseDataToViewMapper
) {
    private var mRiskSerialNumber: String? = null

    @SuppressLint("CheckResult")
    fun getPaymentDayDetails(
        policyNumber: String,
        isGapFlow: Boolean,
        isTlcFlow: Boolean,
        isDentScratchFlow: Boolean,
        isTyreRimFlow: Boolean,
        isEssentialFlow: Boolean,
        isComprehensiveFlow: Boolean,
    ) {
        showViewProgressBar(true)
        getRiskSerialNumber(
            policyNumber,
            when {
                isGapFlow -> InsuranceConstants.ONE_STRING
                isTlcFlow -> InsuranceConstants.TWO_STRING
                isDentScratchFlow -> InsuranceConstants.THREE_STRING
                isTyreRimFlow -> InsuranceConstants.FOUR_STRING
                isEssentialFlow -> InsuranceConstants.FIVE_STRING
                isComprehensiveFlow -> InsuranceConstants.SIX_STRING
                else -> InsuranceConstants.ZERO_STRING
            },
            callback = { riskSerialNumber ->
                paymentFrequencyAPI(policyNumber, riskSerialNumber, true)

            },
            errorCallback = {
                showAPIError()
            })
    }

    @SuppressLint("CheckResult")
    fun paymentFrequencyAPI(policyNumber: String, riskSerialNumber: String, isVVAPFlow: Boolean) {
        this.mRiskSerialNumber = riskSerialNumber
        val requestModel = requestViewToDataMapper.map(
            PolicyDetailRequestViewModel(
                InsuranceConstants.PolicyAdminConstants.PAYMENT_FREQUENCY,
                policyNumber,
                riskSerialNumber
            )
        )

        val referenceObservable: Observable<PolicyDetailResponseDataModel> =
            if (isVVAPFlow) { //VVAPS USE CASE
                policyDetailUseCaseVVAP.execute(requestModel)
            } else { //PL Use Case
                policyDetailUseCasePL.execute(requestModel)
            }
        referenceObservable.compose(bindToLifecycle())
            .doOnTerminate { showViewProgressBar(false) }
            .subscribe(
                { data: PolicyDetailResponseDataModel? ->
                    data?.let {
                        this.handlePolicyDetailResponse(
                            it
                        )
                    }
                }, {
                    showAPIError()
                })
    }


    fun handlePolicyDetailResponse(data: PolicyDetailResponseDataModel?) {
        if (view == null || data == null || processInvalidResponse(
                responseDataToViewMapper.map(data)
            )
        ) {
            showAPIError()
            return
        }


        val paymentFrequency = responseDataToViewMapper.map(data)
        parseFrequencyAndDate(paymentFrequency)
    }

    fun parseFrequencyAndDate(
        paymentFrequency: PolicyDetailResponseViewModel
    ) {
        val frequencyType = paymentFrequency.policyInquiry.run {
            this?.get(Constants.ZERO)?.policy?.policySection?.policyBilling?.payment?.get(Constants.ZERO)
        }
        val paymentDate =
            paymentFrequency.policyInquiry?.get(Constants.ZERO)?.policy?.policySection?.policyBilling?.payment?.get(
                Constants.ZERO
            )?.paymentDate
        if (frequencyType?.frequencyCode?.value.equals(MONTHLY_FREQUENCY, ignoreCase = true)) {
            view?.setPaymentDate(true, paymentDate)
        } else {
            view?.setPaymentDate(false, paymentDate)
        }
    }


    fun showViewProgressBar(isProgress: Boolean) {
        view?.showProgressBar(isProgress)
    }

    fun showAPIError() {
        view?.showAPIError()
        showViewProgressBar(false)
    }

    fun editPaymentDayDetails(
        policyNumber: String,
        paymentDate: String?,
        subProduct: String?,
        isPersonalLine: Boolean,
        isFuneral: Boolean
    ) {
        mNavigationRouter.navigateTo(
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_EDIT_PAYMENT_DAY_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_VVAP_POLICY_NUMBER, policyNumber)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EDIT_PAYMENT_DAY_DETAILS, paymentDate)
                .withParam(
                    InsuranceConstants.ParamKeys.PARAM_VVAP_RISK_SERIAL_NUMBER,
                    mRiskSerialNumber
                )
                .withParam(
                    InsuranceConstants.ParamKeys.PARAM_VVAPS_SUB_PRODUCT_NAME, subProduct
                )
                .withParam(
                    PARAM_IS_PL_FLOW, isPersonalLine
                )
                .withParam(PARAM_IS_FUNERAL_FLOW,isFuneral)
        )
    }

    fun moveToProductListScreen() {
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_DETAILS_CONTAINER)
        navigationTarget.withIntentFlagClearTopSingleTop(true)
        mNavigationRouter.navigateTo(navigationTarget)
        if (view != null) {
            view!!.close()
        }
    }

    fun sendEventWithProduct(eventName: String?, subProduct: String?,productType: String?) {
        val cdata = HashMap<String, Any>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY,
            productType
        )
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
        adobeContextData.setProductAccount(productType)
        adobeContextData.setSubProduct(subProduct)
        analytics.sendEventActionWithMap(eventName, cdata)
    }

}