package za.co.nedbank.ui.view.home.investments;

import static com.google.android.material.snackbar.BaseTransientBottomBar.LENGTH_INDEFINITE;
import static za.co.nedbank.core.data.storage.StorageKeys.CLIENT_TYPE;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.concierge.chat.view.NBChatBaseActivity;
import za.co.nedbank.core.databinding.ActivityAccTypeInvestmentBinding;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.widget.NBSnackbar;

public class AccountTypeInvestmentActivity extends NBChatBaseActivity implements AccountTypeListView, AccountTypeRowInterface {

    @Inject
    AccountTypeListPresenter presenter;

    @Inject
    FeatureSetController featureController;

    @Inject
    ApplicationStorage mApplicationStorageClientType;

    AccountTypeListAdapter accountTypeListAdapter;
    private ActivityAccTypeInvestmentBinding binding;
    private List<AccountSummary> summaryList=new ArrayList<>();
    private boolean isFromDeLinkInv=false;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityAccTypeInvestmentBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        if (!getAccountSummeryList().isEmpty()){
            initToolbar(binding.toolbar, true, getAccountName());
        }
        presenter.bind(this);
        initStatusAdapter();

    }

    private void initStatusAdapter() {
        RecyclerView.LayoutManager layoutManager = new LinearLayoutManager(this);
        binding.rvSavingsPockets.setLayoutManager(layoutManager);
        accountTypeListAdapter = new AccountTypeListAdapter(this);
        accountTypeListAdapter.setSelectedRowInterface(this);
        binding.rvSavingsPockets.setAdapter(accountTypeListAdapter);
        summaryList=getAccountSummeryList();
        if (!summaryList.isEmpty()){
            summaryList.sort(Comparator.nullsLast(Comparator.comparing(AccountSummary::getGoalName, Comparator.nullsLast(Comparator.naturalOrder()))));
            accountTypeListAdapter.swapData(summaryList);
        }
    }

    @Override
    protected void handleChatMenuClicked(String conversationId) {
        super.handleChatMenuClicked("Account Type Investment");
    }

    private String getAccountName(){
        return getProductName()+ StringUtils.SPACE + StringUtils.OPEN_BRACE + getAccountSummeryList().size() + StringUtils.CLOSE_BRACE;
    }

    @Override
    public void showProgressBar(boolean progress){
        if (progress){
            binding.progressBarAcctype.setVisibility(View.VISIBLE);
        }else {
            binding.progressBarAcctype.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public List<AccountSummary> getAccountSummeryList() {
        return Objects.requireNonNull(getIntent().getExtras())
                .getParcelableArrayList(ServicesNavigationTarget.INVONLINE_ACCOUNT_LIST);
    }

    @Override
    public boolean canTransact() {
        return getIntent() != null && getIntent()
                .getBooleanExtra(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, false);
    }

    @Override
    public String getFicaStatus() {
        return getIntent() != null && getIntent().getExtras() != null
                && getIntent().getStringExtra(ServicesNavigationTarget.PARAM_FICA_STATUS) != null
                ? getIntent().getStringExtra(ServicesNavigationTarget.PARAM_FICA_STATUS) : StringUtils.EMPTY_STRING;
    }

    @Override
    public String getProductName() {
        return getIntent() != null && getIntent().getExtras() != null
                && getIntent().getStringExtra(ServicesNavigationTarget.INVONLINE_ACCOUNT_TYPE) != null
                ? getIntent().getStringExtra(ServicesNavigationTarget.INVONLINE_ACCOUNT_TYPE) : StringUtils.EMPTY_STRING;
    }

    @Override
    public String getDefaultAccountId() {
        return getIntent() != null && getIntent().getExtras() != null
                && getIntent().getStringExtra(ServicesNavigationTarget.INVONLINE_DEFALUT_ACCOUNT_ID) != null
                ? getIntent().getStringExtra(ServicesNavigationTarget.INVONLINE_DEFALUT_ACCOUNT_ID) : StringUtils.EMPTY_STRING;
    }

    @Override
    public void onAccountClick(AccountSummary accountSummary, int position) {
        presenter.onAccountSelected(accountSummary, getUserType());
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {

        getMenuInflater().inflate(za.co.nedbank.services.R.menu.chat_menu, binding.toolbar.getMenu());
        setChatMenuIcon(menu.findItem(za.co.nedbank.services.R.id.menu_item_chat));
        if (binding.toolbar != null && binding.toolbar.getMenu() != null) {
            binding.toolbar.getMenu().findItem(za.co.nedbank.services.R.id.menu_item_chat).setVisible(true);
        }
        return super.onCreateOptionsMenu(menu);
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        presenter.unbind();
    }

    @Override
    public ClientType getUserType() {
        String clientType = mApplicationStorageClientType.getString(CLIENT_TYPE, ClientType.TP.getValue());
        return ClientType.getEnum(clientType);
    }

    @Override
    public String getClientType() {
       return getIntent() != null && getIntent().getExtras() != null
               && getIntent().getStringExtra(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE) != null
                ? getIntent().getStringExtra(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE) : StringUtils.ZERO;
    }

    @Override
    public boolean isSAResident() {
        return getIntent() != null
                && getIntent().getBooleanExtra(ServicesNavigationTarget.PARAM_IS_SA_RESIDENT, false);
    }

    @Override
    public String getBirthDate() {
        return getIntent() == null || getIntent().getExtras() == null
                || getIntent().getStringExtra(ServicesNavigationTarget.PARAM_ONIA_BIRTH_DATE) == null
                ? StringUtils.EMPTY_STRING
                : getIntent().getStringExtra(ServicesNavigationTarget.PARAM_ONIA_BIRTH_DATE);
    }

    @Override
    public String getCisNumber() {
        return getIntent() == null || getIntent().getExtras() == null
                || getIntent().getStringExtra(ServicesNavigationTarget.PARAM_ONIA_CIS_NUMBER) == null ?
                StringUtils.EMPTY_STRING : getIntent().getStringExtra(ServicesNavigationTarget.PARAM_ONIA_CIS_NUMBER);
    }

    @Override
    public String getSecOfficerCd() {
        return getIntent() == null || getIntent().getExtras() == null
                || getIntent().getStringExtra(ServicesNavigationTarget.PARAM_ONIA_SEC_OFFICER_CD) == null
                ? StringUtils.EMPTY_STRING : getIntent().getStringExtra(ServicesNavigationTarget.PARAM_ONIA_SEC_OFFICER_CD);
    }

    @Override
    public void showError(String message) {
        showError(getString(za.co.nedbank.R.string.error), message);
    }

    @Override
    public void updateInvestAccountList(int id) {
        isFromDeLinkInv=true;
        for (int i=0;i<summaryList.size();i++){
            if (id==summaryList.get(i).getGoalId()){
                summaryList.get(i).setGoalName(null);
                summaryList.get(i).setGoalId(0);

                showError(StringUtils.SPACE, getString(R.string.unlink_target_success_mesg), getString(R.string.all_caps_ok), LENGTH_INDEFINITE, () -> NBSnackbar.instance().dismissSnackBar());
                break;
            }
        }
        summaryList.sort(Comparator.nullsLast(Comparator.comparing(AccountSummary::getGoalName, Comparator.nullsLast(Comparator.naturalOrder()))));
        accountTypeListAdapter.swapData(summaryList);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (!isFromDeLinkInv){
                close();
            }
            else {
                presenter.navigateToHome();
            }

        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            if (!isFromDeLinkInv){
                close();
            }
            else {
                presenter.navigateToHome();
            }
        }
        return true;
    }
}
