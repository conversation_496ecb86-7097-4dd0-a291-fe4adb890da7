package za.co.nedbank.ui.view.retention;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.view.account.branchcode.model.BranchCodeViewModel;

public interface MultipleAccountsShareView extends NBBaseView {

    void showLoadingProgress(final boolean show);

    void setAccounts(List<AccountSummary> accounts);

    void setUserInfo(UserDetailViewModel userDetailViewModel);

    String getAccountHolderName(AccountSummary accounts);

    void setBranchCodeList(List<BranchCodeViewModel> branchCodeViewModelList);

    void setActivityResult();

    void finishScreen();

    String getOverviewProductGroup(OverviewType overviewType);

    boolean isDebitOrderRetentionFlow();
}
