/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.money_request;

import java.util.ArrayList;
import java.util.Map;
import java.util.TreeMap;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.payment.buy.domain.model.response.UserContactData;
import za.co.nedbank.ui.data.datastore.GetUserContactsStoreFactory;

public class GetContactsUseCase extends UseCase<String, Map<String, ArrayList<UserContactData>>> {

    private final GetUserContactsStoreFactory mGetUserContactsStoreFactory;

    @Inject
    GetContactsUseCase(final UseCaseComposer useCaseComposer, final GetUserContactsStoreFactory getUserContactsStoreFactory) {
        super(useCaseComposer);
        this.mGetUserContactsStoreFactory = getUserContactsStoreFactory;
    }

    @Override
    protected Observable<Map<String, ArrayList<UserContactData>>> createUseCaseObservable(String param) {
        return Observable.defer(() -> {
            Map<String, ArrayList<UserContactData>> sectionedMap = new TreeMap<>();
            return mGetUserContactsStoreFactory.getData(param).map(userContactDatas -> {
                if (userContactDatas != null && !userContactDatas.isEmpty()) {
                    for (UserContactData userContactData : userContactDatas) {
                        String sectionHeader = String.valueOf(userContactData.getContactName().charAt(0)).toUpperCase();
                        ArrayList<UserContactData> contactDataArrayList = sectionedMap.get(sectionHeader);
                        if (contactDataArrayList == null) {
                            contactDataArrayList = new ArrayList<>();
                        }
                        contactDataArrayList.add(userContactData);
                        sectionedMap.put(sectionHeader, contactDataArrayList);
                    }
                }
                return sectionedMap;
            });
        });
    }

}
