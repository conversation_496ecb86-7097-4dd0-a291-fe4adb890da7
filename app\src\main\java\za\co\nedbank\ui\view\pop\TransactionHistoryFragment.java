package za.co.nedbank.ui.view.pop;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.snackbar.Snackbar;

import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;
import za.co.nedbank.core.view.recipient.BankAccountViewDataModel;
import za.co.nedbank.core.view.recipient.CreditCardViewDataModel;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.databinding.FragmentTransactionHistoryBinding;
import za.co.nedbank.ui.di.AppDI;

public class TransactionHistoryFragment extends NBBaseFragment implements TransactionHistoryView, TransactionHistoryRowInterface, FooterViewInterface {

    @Inject
    TransactionHistoryPresenter transactionHistoryPresenter;
    private TransactionHistoryAdapter adapter;
    private boolean mIsListRendered;
    private RecipientViewModel mRecipientViewModel;
    private boolean mIsLoading;
    private FragmentTransactionHistoryBinding binding;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentTransactionHistoryBinding.inflate(inflater, container, false);
        transactionHistoryPresenter.bind(this);
        return binding.getRoot();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
        setUserVisibleHint(false);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mRecipientViewModel = getArguments() != null ? getArguments().getParcelable(Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL) : null;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        transactionHistoryPresenter.unbind();
    }

    private void initAdapter() {
        adapter = new TransactionHistoryAdapter(getContext());
        adapter.setRowInterface(this, this);
        binding.historyRecycler.setAdapter(adapter);
        LinearLayoutManager layoutManager = (LinearLayoutManager) binding.historyRecycler.getLayoutManager();
        binding.historyRecycler.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                if (mIsLoading || adapter.isViewMoreVisible() || transactionHistoryPresenter.dataDownloadComplete())
                    return;
                int visibleItemCount;
                if (layoutManager != null) {
                    visibleItemCount = layoutManager.getChildCount();

                    int totalItemCount = layoutManager.getItemCount();
                    int pastVisibleItems = layoutManager.findFirstVisibleItemPosition();
                    if (pastVisibleItems + visibleItemCount >= totalItemCount) {
                        //End of list
                        mIsLoading = true;
                        transactionHistoryPresenter.getTransactionHistory();
                    }
                }
            }
        });
        if (mRecipientViewModel != null)
            transactionHistoryPresenter.setContactCardId(mRecipientViewModel.getContactCardId());
        transactionHistoryPresenter.initialiseData();
        transactionHistoryPresenter.getTransactionHistory();

    }

    @Override
    public boolean isLoading() {
        return mIsLoading;
    }

    public Fragment getInstance() {
        return null;
    }

    @Override
    public void showListLoading() {
        binding.listLoadingView.showListLoading();
    }

    @Override
    public void showListReloading() {
        binding.listLoadingView.showListReloading();
    }

    @Override
    public void showListLoaded() {
        if (binding.listLoadingView != null)
            binding.listLoadingView.clearLoading();
    }

    @Override
    public void showEmptyListIndicator() {
        if (binding.emptyListIndicator != null)
            binding.emptyListIndicator.setVisibility(View.VISIBLE);
    }

    @Override
    public void setIsLoading(boolean isLoading) {
        this.mIsLoading = isLoading;
    }


    @Override
    public void showFooterView(TransactionFooterViewType footerViewType) {
        adapter.showFooterView(footerViewType);
    }

    @Override
    public void showTransactionHistoryViewModel(List<TransactionHistoryViewModel> transactionHistoryViewModels) {
        int initialItemCount = adapter.getItemCount();
        binding.emptyListIndicator.setVisibility(View.GONE);
        mIsListRendered = transactionHistoryViewModels != null && transactionHistoryViewModels.size() > 0;
        adapter.swapSectionedData(transactionHistoryViewModels);
        if (initialItemCount == 0) {
            binding.historyRecycler.scheduleLayoutAnimation();
        }
    }

    @Override
    public void showError(String message) {
        (getNBActivity()).showError(getString(za.co.nedbank.enroll_v2.R.string.failed_to_retrieve_info), message, getString(za.co.nedbank.enroll_v2.R.string.snackbar_action_retry),
                Snackbar.LENGTH_INDEFINITE, () -> transactionHistoryPresenter.getTransactionHistory());
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (binding != null) {
            if (isVisibleToUser && !mIsListRendered) {
                initAdapter();
            } else if (binding.historyRecycler != null) {
                binding.historyRecycler.smoothScrollToPosition(0);
            }
        }
    }

    @Override
    public void transactionHistoryClicked(TransactionHistoryViewModel transactionHistoryViewModel) {
        if (mRecipientViewModel != null && mRecipientViewModel.getBankAccountViewDataModelList() != null
                && !mRecipientViewModel.getBankAccountViewDataModelList().isEmpty()
                && mRecipientViewModel.getBankAccountViewDataModelList().get(0) != null){

            BankAccountViewDataModel bankAccountViewDataModel = getBankDetails(transactionHistoryViewModel);
            transactionHistoryViewModel.setBankName(bankAccountViewDataModel.getBankName());
            transactionHistoryViewModel.setBeneficiaryType(bankAccountViewDataModel.getBeneficiaryType());
            transactionHistoryViewModel.setAccountType(bankAccountViewDataModel.getAccountType());
            transactionHistoryViewModel.setYourRef(bankAccountViewDataModel.getYourRef());
            transactionHistoryViewModel.setRecipientRef(bankAccountViewDataModel.getRecipientRef());
            transactionHistoryViewModel.setBeneficiaryIDRequired(bankAccountViewDataModel.isBeneficiaryIDRequired());
            transactionHistoryViewModel.setBeneficiaryName(bankAccountViewDataModel.getmBeneficiaryName());
            transactionHistoryViewModel.setBeneficiaryReference(bankAccountViewDataModel.getmBeneficiaryReference());
            transactionHistoryViewModel.setmContactCardId(bankAccountViewDataModel.getmContactCardId());
            transactionHistoryViewModel.setmIsValid(bankAccountViewDataModel.ismIsValid());
            transactionHistoryViewModel.setBranchCode(bankAccountViewDataModel.getBranchCode());
            transactionHistoryViewModel.setFromRecipientHistory(true);
        } else if (mRecipientViewModel != null && mRecipientViewModel.getCreditCardViewDataModelList() != null
                && !mRecipientViewModel.getCreditCardViewDataModelList().isEmpty()
                && mRecipientViewModel.getCreditCardViewDataModelList().get(0) != null){
            CreditCardViewDataModel bankAccountViewDataModel = getBankAccountCreditCard(transactionHistoryViewModel);
            transactionHistoryViewModel.setYourRef(bankAccountViewDataModel.getYourRef());
            transactionHistoryViewModel.setRecipientRef(bankAccountViewDataModel.getRecipientRef());
            transactionHistoryViewModel.setAccountType(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CC.getAccountTypeCode());
            transactionHistoryViewModel.setFromRecipientHistory(true);
        }

        transactionHistoryPresenter.openTransactionDetails(transactionHistoryViewModel);
    }


    private BankAccountViewDataModel getBankDetails(TransactionHistoryViewModel transactionHistoryViewModel) {
        for (int i = 0; i < mRecipientViewModel.getBankAccountViewDataModelList().size(); i++) {
            BankAccountViewDataModel bankAccountViewDataModel = (BankAccountViewDataModel) mRecipientViewModel.getBankAccountViewDataModelList().get(i);
            if (bankAccountViewDataModel.getAccountNumber() != null && bankAccountViewDataModel.getAccountNumber().equals(transactionHistoryViewModel.getBeneficiaryAccount())) {
                return bankAccountViewDataModel;
            }
        }
        return (BankAccountViewDataModel) mRecipientViewModel.getBankAccountViewDataModelList().get(0);
    }

    private CreditCardViewDataModel getBankAccountCreditCard(TransactionHistoryViewModel transactionHistoryViewModel) {
        for (int i = 0; i < mRecipientViewModel.getCreditCardViewDataModelList().size(); i++) {
            CreditCardViewDataModel creditCardDataModel = (CreditCardViewDataModel) mRecipientViewModel.getCreditCardViewDataModelList().get(i);
            if (creditCardDataModel.getCardNumber() != null && creditCardDataModel.getCardNumber().equals(transactionHistoryViewModel.getBeneficiaryAccount())) {
                return creditCardDataModel;
            }
        }
        return (CreditCardViewDataModel) mRecipientViewModel.getCreditCardViewDataModelList().get(0);
    }


    @Override
    public void footerViewClicked() {
        if (adapter.isViewMoreVisible()) {
            transactionHistoryPresenter.onViewMoreClicked();
        }
    }

    @Override
    public boolean isViewMoreVisible() {
        return adapter.isViewMoreVisible();
    }

    @Override
    public void hideFooterView() {
        adapter.showFooterView(null);
    }


}