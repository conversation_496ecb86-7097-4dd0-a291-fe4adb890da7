/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.inject.Inject;

import io.reactivex.annotations.NonNull;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.model.Permission;
import za.co.nedbank.core.domain.usecase.CheckPermissionGrantedUseCase;
import za.co.nedbank.core.domain.usecase.CheckPermissionRationaleUseCase;
import za.co.nedbank.core.domain.usecase.CheckPermissionUseCase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.KidsProfileUtilsWrapper;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.payment.buy.domain.model.response.UserContactData;
import za.co.nedbank.payment.common.model.user_contact.UserContactViewModel;
import za.co.nedbank.ui.domain.usecase.money_request.GetContactsUseCase;
import za.co.nedbank.ui.view.tracking.AppTracking;


class RecipientContactsPresenter extends NBBasePresenter<UserContactsView> {

    private final GetContactsUseCase mGetUserContactsUseCase;
    private final CheckPermissionUseCase mCheckPermissionUseCase;
    private final CheckPermissionGrantedUseCase mCheckPermissionGrantedUseCase;
    private final CheckPermissionRationaleUseCase mCheckPermissionRationaleUseCase;
    private final ErrorHandler mErrorHandler;
    private boolean mPermissionDenied;
    private boolean mPermissionProvided;
    private final Analytics mAnalytics;
    private final KidsProfileUtilsWrapper kidsProfileUtilsWrapper;

    @Inject
    RecipientContactsPresenter(@NonNull final GetContactsUseCase getUserContactsUseCase,
                               @NonNull final CheckPermissionGrantedUseCase checkPermissionGrantedUseCase,
                               @NonNull final CheckPermissionRationaleUseCase checkPermissionRationaleUseCase,
                               @NonNull final CheckPermissionUseCase checkPermissionUseCase,
                               @NonNull final ErrorHandler errorHandler,
                               final Analytics analytics,
                               final KidsProfileUtilsWrapper kidsProfileUtilsWrapper) {
        this.mGetUserContactsUseCase = getUserContactsUseCase;
        this.mCheckPermissionGrantedUseCase = checkPermissionGrantedUseCase;
        this.mCheckPermissionRationaleUseCase = checkPermissionRationaleUseCase;
        this.mCheckPermissionUseCase = checkPermissionUseCase;
        this.mErrorHandler = errorHandler;
        this.mAnalytics = analytics;
        this.kidsProfileUtilsWrapper = kidsProfileUtilsWrapper;
    }

    void checkContactReadPermissionGranted() {
        mCheckPermissionGrantedUseCase
                .execute(Permission.READ_CONTACTS)
                .compose(bindToLifecycle())
                .subscribe(result -> {
                    if (null != view) {
                        if (result) {
                            mPermissionProvided = true;
                            view.showPermissionProvidedView();
                        } else {
                            checkShouldShowPermissionRationale();
                            view.showPermissionNotProvided();
                        }
                    }
                }, error -> view.showError(mErrorHandler.getErrorMessage(error).getMessage()));
    }

    void checkShouldShowPermissionRationale() {
        mCheckPermissionRationaleUseCase.execute(Permission.READ_CONTACTS)
                .subscribe(result -> {
                    if (null != view && result) {
                            mPermissionDenied = true;
                            view.showPermissionDeniedView();
                    }
                }, error -> view.showError(mErrorHandler.getErrorMessage(error).getMessage()));
    }

    void requestReadContactsPermission() {
        mCheckPermissionUseCase
                .execute(Permission.READ_CONTACTS)
                .subscribe(result -> {
                            if (null != view) {
                                if (result) {
                                    mPermissionProvided = true;
                                    view.showPermissionProvidedView();
                                } else {
                                    mPermissionDenied = true;
                                    view.showPermissionDeniedView();
                                }
                                trackAccessContacts(result);
                            }
                        },
                        error -> {
                            if (null != view) {
                                view.showError(mErrorHandler.getErrorMessage(error).getMessage());
                            }
                        });
    }

    void trackAccessContacts(boolean mPermissionProvided) {
        mAnalytics.sendEvent(mPermissionProvided ? AppTracking.TAG_PAY_ME_ALLOW_CONTACT_ACCESS : AppTracking.TAG_PAY_ME_RESTRICT_CONTACT_ACCESS, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    void getUserContacts(String searchQuery) {
        if (view != null) {
            view.setVisibilityOnProgressBar(true);
        }
        if (!mPermissionProvided) {
            return;
        }
        mGetUserContactsUseCase.execute(searchQuery)
                .compose(bindToLifecycle())
                .map(map -> {
                    Map<String, List<UserContactViewModel>> viewModelMap = new TreeMap<>();
                    Iterator it = map.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry pair = (Map.Entry) it.next();

                        List<UserContactViewModel> userContactViewModelList = new ArrayList<>();
                        ArrayList<UserContactData> userContactDataList = (ArrayList<UserContactData>) pair.getValue();
                        for (UserContactData userContactData : userContactDataList) {
                            userContactViewModelList.add(new UserContactViewModel(userContactData.getContactName(), userContactData.getPhoneNumber()));
                        }
                        viewModelMap.put((String) pair.getKey(), userContactViewModelList);
                    }
                    return viewModelMap;
                }).subscribe(
                userContactDataMap -> {
                    if (null != view) {
                        view.setVisibilityOnProgressBar(false);
                        view.showContacts(userContactDataMap);
                    }
                },
                error -> {
                    if (null != view) {
                        view.setVisibilityOnProgressBar(false);
                        view.showError(mErrorHandler.getErrorMessage(error).getMessage());
                    }
                    mAnalytics.trackFailure(false, AppTracking.TAG_PAY_ME_FAILURE, za.co.nedbank.core.ApiAliasConstants.PM_CN_FCH, mErrorHandler.getErrorMessage(error).getMessage(), null);
                }
        );
    }

    void onContactSelected(UserContactViewModel selectedContact) {
        if (null != selectedContact && null != view) {
            view.setActivityResult(selectedContact);
        }
    }

    void handleAllowButtonClicked() {
        if (mPermissionDenied) {
            if (null != view) {
                view.openAppSettings();
            }
        } else {
            requestReadContactsPermission();
        }
    }

    void handleSearchField(String searchText) {
        if (null != view) {
            view.handleSearchInput(searchText);
        }
    }

    // Checks if the user is a kid's profile
    public boolean isKidsProfile() {
        return kidsProfileUtilsWrapper.isUserUnderAged();
    }
}
