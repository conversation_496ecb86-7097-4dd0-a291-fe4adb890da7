package za.co.nedbank.ui.view.notification.notification_details;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;

import com.facebook.shimmer.ShimmerFrameLayout;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;

import java.util.ArrayList;
import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;

public class RichNotificationAdapter extends PagerAdapter {

    private List<FBNotificationsViewModel.RichContent> richContents;

    ShimmerFrameLayout shimmerLayout;

    public RichNotificationAdapter() {
        richContents = new ArrayList<>();
    }

    @Override
    public int getCount() {
        return richContents.size();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    public void seRichContent(List<FBNotificationsViewModel.RichContent> richContents) {
        this.richContents.clear();
        this.richContents.addAll(richContents);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        View viewItem = LayoutInflater.from(container.getContext()).inflate(R.layout.rich_content_pager_item, container, false);
        shimmerLayout = viewItem.findViewById(R.id.shimmer_layout);
        loadImage(viewItem.findViewById(R.id.iv_rich_content_image), richContents.get(position).getUrl());
        container.addView(viewItem);
        return viewItem;

    }

    private void loadImage(ImageView imageVIew, String imageUrl) {
        if (StringUtils.isNullOrEmpty(imageUrl)) {
            shimmerLayout.hideShimmer();
            shimmerLayout.stopShimmer();
        } else {
            ViewUtils.showViews(shimmerLayout);
            Picasso.get()
                    .load(imageUrl)
                    .noPlaceholder()
                    .priority(Picasso.Priority.HIGH)
                    .into(imageVIew, new Callback() {
                        @Override
                        public void onSuccess() {
                            shimmerLayout.stopShimmer();
                            ViewUtils.hideViews(shimmerLayout);
                        }

                        @Override
                        public void onError(Exception e) {
                            shimmerLayout.stopShimmer();
                        }
                    });
        }
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView((View) object);
    }

}
