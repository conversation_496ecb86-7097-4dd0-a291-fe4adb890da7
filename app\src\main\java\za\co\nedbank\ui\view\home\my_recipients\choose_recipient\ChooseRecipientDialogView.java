/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients.choose_recipient;

import za.co.nedbank.core.base.NBBaseView;

/**
 * Created by charurani on 23-08-2017.
 */

interface ChooseRecipientDialogView extends NBBaseView {
    void dismissDialogWithData(ChooseRecipientsViewModel chooseRecipientsViewModel);
    ChooseRecipientsViewModel buildRecipientSelectedModel(int position);
}
