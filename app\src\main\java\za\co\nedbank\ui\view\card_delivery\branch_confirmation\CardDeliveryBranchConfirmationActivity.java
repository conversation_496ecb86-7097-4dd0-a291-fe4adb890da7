package za.co.nedbank.ui.view.card_delivery.branch_confirmation;

import android.os.Bundle;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.enroll_v2.databinding.ActivityCardDeliveryBranchConfirmationBinding;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConstants;

public class CardDeliveryBranchConfirmationActivity extends NBBaseActivity implements CardDeliveryBranchConfirmationView {
    @Inject
    CardDeliveryBranchConfirmationPresenter presenter;
    private ActivityCardDeliveryBranchConfirmationBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCardDeliveryBranchConfirmationBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        initToolbar(binding.toolbar, true, false);
        presenter.bind(this);
        presenter.loadDefaultBranch();
        binding.layoutCdbc.btnConfirm.setOnClickListener(v -> presenter.confirmClick());
        binding.layoutCdbc.btnChooseDifferentBranch.setOnClickListener(v -> onClickChooseBranch());
    }

    void onClickChooseBranch() {
        presenter.sendChangeBranchAnalytics();
        presenter.chooseDifferentBranch();
    }

    public void updateBranchNameAddress(String branchName, String branchAddress) {
        ViewUtils.showViews(binding.layoutCdbc.container);
        binding.layoutCdbc.branchName.setText(branchName);
        binding.layoutCdbc.branchAddress.setText(branchAddress);
    }

    @Override
    public String getCardActionName() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_ACTION_NAME);
    }

    @Override
    public String getFlow() {
        return getIntent().getStringExtra(NavigationTarget.PARAM_CARD_DELIVERY_FLOW);
    }

    @Override
    public String getCardDeliverySubFeature() {
        return getIntent().getStringExtra(CardDeliveryConstants.EXTRA_SELECT_CARD_DELIVERY_OPTION_NAME);
    }

    @Override
    public void showLoading(boolean isLoading) {
        if (isLoading) {
            binding.progressBar.show();
        } else {
            binding.progressBar.hide();
        }
        setEnabledActivityTouch(!isLoading);
    }

    @Override
    public String getCardPlasticId() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_PLASTIC_ID);
    }

    @Override
    public String getDefaultBranchId() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_BRANCH_ID);
    }

    @Override
    public void showConfirmButtonLoading(boolean isLoading) {
        binding.layoutCdbc.btnConfirm.setLoadingVisible(isLoading);
    }

    @Override
    protected void onDestroy() {
        presenter.unbind();
        super.onDestroy();
    }
}