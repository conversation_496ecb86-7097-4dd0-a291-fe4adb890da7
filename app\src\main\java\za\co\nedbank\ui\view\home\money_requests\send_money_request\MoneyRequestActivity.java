/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.Map;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.databinding.ActivitySendMoneyBinding;
import za.co.nedbank.payment.common.model.user_contact.UserContactViewModel;
import za.co.nedbank.payment.common.view.IPayChildScreenTypes;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.component.CompatEditText;
import za.co.nedbank.uisdk.validation.ValidatableInput;


public class MoneyRequestActivity extends NBBaseActivity implements MoneyRequestView {

    @Inject
    MoneyRequestPresenter moneyRequestPresenter;
    private ActivitySendMoneyBinding binding;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySendMoneyBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        initToolbar(binding.toolbar, true, false);
        AppDI.getActivityComponent(this).inject(this);
        setupForKeyboardDismiss(binding.recipientNameEt, this);
        setNextButtonEnabled(false);

        addListenerToRecipientName(binding.recipientNameEt);
        addListenerToRecipientPhone(binding.recipientPhoneEt);
        binding.btnNext.setOnClickListener(v -> onNextButtonClick());
        binding.recipientIv.setOnClickListener(v -> moneyRequestPresenter.handleRecipientIconClick());

    }

    void validateInput(final ValidatableInput<String> input, Validator.ValidatorType validatorType) {
        if (input != null && !moneyRequestPresenter.validateInput(input, validatorType)) {
            setNextButtonEnabled(false);
        } else {
            setNextButtonEnabled(true);
        }
    }

    @Override
    public void handleNextClick() {
        moneyRequestPresenter.validateMobileNumber(moneyRequestPresenter.getMobileNumberWithoutContryCode(binding.recipientPhoneEt.getValue()));
    }

    @Override
    protected void onResume() {
        super.onResume();
        moneyRequestPresenter.bind(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        moneyRequestPresenter.unbind();
    }

    @Override
    public void setResult(Map<String, Object> params) {
        readResult(params);
    }

    public void onNextButtonClick() {
        showLoadingOnButton(true);
        moneyRequestPresenter.handleNextClick();
    }

    public void addListenerToRecipientName(CompatEditText compatEdtRecipientName){

        RxTextView.textChanges(compatEdtRecipientName.getInputField()).subscribe(chars -> {
            if (compatEdtRecipientName.hasError()) compatEdtRecipientName.clearErrors();
            compatEdtRecipientName.clearErrors();
            moneyRequestPresenter.handleRecipientNameTextChanged();
        },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        compatEdtRecipientName.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                validateInput(compatEdtRecipientName, Validator.ValidatorType.RECIPIENT_NAME_VALIDATOR);
                compatEdtRecipientName.clearFocus();
            }
        });

    }

    public void addListenerToRecipientPhone(CompatEditText compatEdtRecipientPhone){

        RxTextView.textChanges(compatEdtRecipientPhone.getInputField()).subscribe(chars -> {
            if (compatEdtRecipientPhone.hasError()) compatEdtRecipientPhone.clearErrors();
            compatEdtRecipientPhone.clearErrors();
            moneyRequestPresenter.handleMobileNumberTextChanged();
        },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        compatEdtRecipientPhone.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                validateInput(compatEdtRecipientPhone, Validator.ValidatorType.RECIPIENT_CONTACT_VALIDATOR);
                if (TextUtils.isEmpty(binding.recipientPhoneEt.getValue())) {
                    setCountryCodeVisibility(false);
                }
                binding.recipientPhoneEt.clearFocus();
            } else {
                setCountryCodeVisibility(true);
            }
        });

        RxTextView.editorActions(compatEdtRecipientPhone.getInputField())
                .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                .subscribe(chars -> {
                    ViewUtils.hideSoftKeyboard(MoneyRequestActivity.this, compatEdtRecipientPhone);
                    compatEdtRecipientPhone.clearFocus();
                    validateInput(compatEdtRecipientPhone, Validator.ValidatorType.RECIPIENT_CONTACT_VALIDATOR);
                },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

    }

    @Override
    public void setNextButtonEnabled(boolean enabled) {
        binding.btnNext.setEnabled(enabled);
    }

    @Override
    public void handleEnterRecipientName() {
        passScreenInputsToPresenter();
    }

    @Override
    public void handleEnterMobileNumber() {
        passScreenInputsToPresenter();
    }

    @Override
    public void showErrorMessage(String message) {
        showError(getString(R.string.error), message);
    }

    @Override
    public void navigateToRequestDetailOnSuccess() {
        moneyRequestPresenter.navigateToRequestDetailScreen();
    }

    @Override
    public String getRecipientName() {
        return binding.recipientNameEt.getValue();
    }

    @Override
    public String getRecipientMobileNumber() {
        return StringUtils.ZERO + binding.recipientPhoneEt.getValue();
    }

    @Override
    public String getRecipientNumberWithoutZero(String recipientContactNumber) {
        if (recipientContactNumber != null) {
            if (recipientContactNumber.startsWith(StringUtils.ZERO)) {
                recipientContactNumber = recipientContactNumber.substring(1);
                return recipientContactNumber;
            } else if (recipientContactNumber.startsWith(getString(R.string.sa_country_code))) {
                recipientContactNumber = recipientContactNumber.substring(3);
                if (recipientContactNumber.startsWith(StringUtils.ZERO)) {
                    recipientContactNumber = recipientContactNumber.substring(1);
                }
                return recipientContactNumber;
            } else if (recipientContactNumber.startsWith(getString(R.string.sa_country_code_with_plus_prefix))) {
                recipientContactNumber = recipientContactNumber.substring(5);
            } else if (recipientContactNumber.startsWith(getString(R.string.sa_country_code_with_zero_prefix))) {
                recipientContactNumber = recipientContactNumber.substring(4);
            }
        }
        return recipientContactNumber;
    }

    private void passScreenInputsToPresenter() {
        moneyRequestPresenter.checkInputsOnScreen(binding.recipientNameEt, binding.recipientPhoneEt);
    }

    private void readResult(Map<String, Object> resultMap) {
        if (null != resultMap && resultMap.size() > 0) {
            int screenType = Constants.NO_ITEM_SELECTED;
            Object screenTypeObject = resultMap.get(Constants.EXTRAS.SCREEN_TYPE);
            if (screenTypeObject instanceof Integer) {
                screenType = (Integer) screenTypeObject;
            }
            Object objectReceived;
            switch (screenType) {
                case IPayChildScreenTypes.USER_CONTACT_SCREEN:
                    objectReceived = resultMap.get(PayNavigatorTarget.EXTRAS.CONTACT_SELECTED);
                    if (objectReceived instanceof UserContactViewModel) {
                        UserContactViewModel userContactViewModel = (UserContactViewModel) objectReceived;
                        binding.recipientNameEt.setText(userContactViewModel.getContactName());
                        binding.recipientPhoneEt.setText(getRecipientNumberWithoutZero(userContactViewModel.getPhoneNumber()));
                        setCountryCodeVisibility(!TextUtils.isEmpty(userContactViewModel.getPhoneNumber()));
                    }
                    break;
                default:
            }
        }
    }

    @Override
    public void showLoadingOnButton(boolean inProgress) {
        binding.btnNext.setLoadingVisible(inProgress);
    }

    @Override
    public void setCountryCodeVisibility(boolean shouldVisible) {
        if (shouldVisible) {
            ViewUtils.showViews(binding.tvCountryCode, binding.mobileNumberLabelTv);
            binding.recipientPhoneEt.getInputField().setHint(StringUtils.EMPTY_STRING);
            setMarginToMobileFieldContainer();
        } else {
            ViewUtils.hideViews(binding.tvCountryCode);
            ViewUtils.setInvisibleAction(binding.mobileNumberLabelTv);
            binding.recipientPhoneEt.getInputField().setHint(getString(R.string.sa_cellphone_number));
            setMarginToMobileFieldContainer();
        }
    }

    private void setMarginToMobileFieldContainer() {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.setMargins(0, 6, 0, 0);
        binding.mobileNumberContainer.setLayoutParams(params);
    }

    public void setupForKeyboardDismiss(View view, final Activity activity) {
        if (!(view instanceof EditText)) {
            view.setOnTouchListener((View v, MotionEvent event) -> {
                ViewUtils.hideSoftKeyboard(activity, v);
                return false;
            });
        }

        if (view instanceof ViewGroup) {
            for (int i = 0; i < ((ViewGroup) view).getChildCount(); i++) {
                View innerView = ((ViewGroup) view).getChildAt(i);
                setupForKeyboardDismiss(innerView, activity);
            }
        }
    }
}