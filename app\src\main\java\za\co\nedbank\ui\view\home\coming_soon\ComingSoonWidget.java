/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.coming_soon;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.sharedui.ui.NBCardWidget;
import za.co.nedbank.ui.di.AppDI;


/**
 * Created by piyushgupta01 on 09-07-2017.
 */

public class ComingSoonWidget extends NBCardWidget {

    public ComingSoonWidget(final Context context) {
        this(context, null);
    }

    public ComingSoonWidget(final Context context, @Nullable final AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ComingSoonWidget(final Context context, @Nullable final AttributeSet attrs, final int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        inflate(getContext(), R.layout.widget_quick_pay_coming_soon, this);
        if (getContext() instanceof NBBaseActivity) {
            AppDI.getActivityComponent((NBBaseActivity) getContext()).inject(this);
        } else {
            throw new ClassCastException("getContext() value must be a subclass of NBBaseActivity");
        }
    }

    @Override
    public void setTitle(String title) {
        // Not required to set title
    }

}
