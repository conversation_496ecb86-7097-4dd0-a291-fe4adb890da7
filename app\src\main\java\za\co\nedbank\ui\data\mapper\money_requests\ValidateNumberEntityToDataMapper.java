/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.mapper.money_requests;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.data.metadata.MetaDataEntity;
import za.co.nedbank.core.data.metadata.ResultDataEntity;
import za.co.nedbank.core.data.metadata.ResultDetailEntity;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.ui.data.entity.money_request.ValidateNumberEntity;


public class ValidateNumberEntityToDataMapper {

    @Inject
    ValidateNumberEntityToDataMapper() {
    }

    public MetaDataModel mapValidateNumberEntityToDataModel(ValidateNumberEntity validateNumberEntity) {
        MetaDataEntity metaDataEntity = validateNumberEntity.getMetaDataEntity();
        return mapValidateNumberEntityToResultDataModel(metaDataEntity);
    }


    private MetaDataModel mapValidateNumberEntityToResultDataModel(MetaDataEntity metaDataEntity) {
        MetaDataModel metaDataModel = new MetaDataModel();
        List<ResultDataEntity> resultDataEntityList = metaDataEntity.getResultData();
        metaDataModel.setResultData(mapResultDataEntityListToDataModelList(resultDataEntityList));
        return metaDataModel;
    }

    private ArrayList<ResultDataModel> mapResultDataEntityListToDataModelList(List<ResultDataEntity> resultDataEntities) {
        ArrayList<ResultDataModel> resultDataModels = new ArrayList<>();
        if (resultDataEntities != null && resultDataEntities.size() > 0) {
            for (ResultDataEntity resultDataEntity : resultDataEntities) {
                ResultDataModel resultDataModel = new ResultDataModel();
                resultDataModel.setTransactionID(resultDataEntity.getTransactionID());
                resultDataModel.setExecEngineRef(resultDataEntity.getExecEngineRef());
                List<ResultDetailEntity> validateNumberDataEntityList = resultDataEntity.getResultDetail();
                resultDataModel.setResultDetail(mapResultDetailEntityToDataModelList(validateNumberDataEntityList));
                resultDataModels.add(resultDataModel);
            }
        }
        return resultDataModels;
    }

    private ArrayList<ResultDetailModel> mapResultDetailEntityToDataModelList(List<ResultDetailEntity> resultDetailEntities) {
        ArrayList<ResultDetailModel> resultDetailDataModelList = new ArrayList<>();
        for (ResultDetailEntity resultDetailEntity : resultDetailEntities) {
            ResultDetailModel resultDetailModel = new ResultDetailModel();
            resultDetailModel.setOperationReference(resultDetailEntity.getOperationReference());
            resultDetailModel.setReason(resultDetailEntity.getReason());
            resultDetailModel.setResult(resultDetailEntity.getResult());
            resultDetailModel.setStatus(resultDetailEntity.getStatus());
            resultDetailDataModelList.add(resultDetailModel);
        }
        return resultDetailDataModelList;
    }
}
