/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import androidx.annotation.NonNull;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;

/**
 * Created by devrath.rathee on 2/26/2018.
 */

public class MoneyPaymentSuccessPresenter extends NBBasePresenter<MoneyResponseSuccessView> {

    private final NavigationRouter mNavigationRouter;

    @Inject
    MoneyPaymentSuccessPresenter(@NonNull NavigationRouter navigationRouter) {
        this.mNavigationRouter = navigationRouter;
    }

    void setPaymentSuccessDetails() {
        view.setPaymentSuccessData(view.getPaymentViewModel(), view.getReceiverMobile());
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

    void navigateToViewMoneyRequestScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.VIEW_MONEY_REQUESTS);
        navigationTarget.withIntentFlagClearTopSingleTop(true);
        mNavigationRouter.navigateTo(navigationTarget);
        view.finishScreen();
    }
}
