/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.domain.model.response.CoreFastPayDomainResponseData;
import za.co.nedbank.payment.pay.view.model.SelectedBankViewModel;

/**
 * Created by priyadhingra on 9/14/2017.
 */

public interface BaseRecipientView extends NBBaseView {
    void setResult(Map<String, Object> params);
    String getDefaultBankNameText();
    void setEnabledActivityTouch(boolean isEnabled);
    void setLoadingButtonEnabled(boolean enabled);
    void showLoadingOnButton(boolean inProgress);
    void dataValidationSuccess();
    String getSelectedAccountTypeCode();
    SelectedBankViewModel getSelectedBankViewModel();
    void hideAddShapIdSection();
    HashMap<String,String> setFastPayDomainNameMap(List<CoreFastPayDomainResponseData> fastPayDomainList);
    HashMap<String,String> fetchFastPayDomainNameMap();
    List<CoreFastPayDomainResponseData> fetchFastPayDomainNameList();

}
