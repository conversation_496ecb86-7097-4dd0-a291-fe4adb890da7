package za.co.nedbank.ui.view.enbichatbot;

import static za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW;

import android.Manifest;
import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.speech.RecognitionListener;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.webkit.URLUtil;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieDrawable;
import com.huawei.hms.mlsdk.asr.MLAsrConstants;
import com.huawei.hms.mlsdk.asr.MLAsrListener;
import com.huawei.hms.mlsdk.asr.MLAsrRecognizer;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import javax.inject.Inject;

import za.co.nedbank.core.R;
import za.co.nedbank.core.base.dialog.IDialog;
import za.co.nedbank.core.base.dialog.UserConsentDialog;
import za.co.nedbank.core.concierge.chat.ChatConstants;
import za.co.nedbank.core.concierge.chat.ChatService;
import za.co.nedbank.core.concierge.chat.view.NBChatBaseActivity;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotListContentPayloadButtonDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotMessageContentsResponseDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotQuickRepliesResponseDataModel;
import za.co.nedbank.core.convochatbot.domain.datamodel.ChatbotSessionMainResponseDataModel;
import za.co.nedbank.core.convochatbot.view.ChatbotErrorCodes;
import za.co.nedbank.core.convochatbot.view.ChatbotEventListener;
import za.co.nedbank.core.convochatbot.view.ChatbotUpdatedRecyclerViewAdapter;
import za.co.nedbank.core.convochatbot.view.ChatbotView;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.databinding.ActivityChatbotBinding;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.enroll_v2.Constants;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.widget.NBSnackbar;

public class ChatbotActivity extends NBChatBaseActivity implements TextWatcher, ChatbotView,
        ServiceConnection, RadioGroup.OnCheckedChangeListener, ChatbotEventListener {

    @Inject
    ChatbotPresenter chatbotPresenter;

    private ChatService mBoundService;

    @Inject
    APIInformation apiInformation;

    @Inject
    FeatureSetController mfeaturesetcontroller;

    private ChatbotUpdatedRecyclerViewAdapter mChatRecyclerViewAdapter;
    LinearLayoutManager layoutManager;

    private static final String TAG = ChatbotActivity.class.getSimpleName();

    private List<ChatbotSessionMainResponseDataModel> mChatMessages = new ArrayList<>();
    private boolean cameFromLiveAgent = false;

    public static final Integer RECORD_AUDIO_REQUEST_CODE = 1;
    private SpeechRecognizer speechRecognizer;
    private MLAsrRecognizer speechRecognizerHuawei;
    private Intent speechRecognizerIntent;
    private Intent speechRecognizerIntentForHuawei;
    private boolean isMicrophoneActive = false;

    private boolean isFromDeepLink = false;
    private ActivityChatbotBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityChatbotBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        initToolbar(binding.toolbar, true, true, false, false);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setHomeAsUpIndicator(ViewUtils.changeBackArrowColor(this, R.color.black_333333));
        }
        chatbotPresenter.bind(this);
        chatbotPresenter.setConversationId(getScreenName());
        chatbotPresenter.decideThecall();

        binding.edittextChatbox.setImeOptions(EditorInfo.IME_ACTION_DONE);
        binding.edittextChatbox.setRawInputType(InputType.TYPE_CLASS_TEXT);

        binding.edittextChatbox.addTextChangedListener(this);
        binding.chatbotFeedback.feedbackStarsRadioGroup.setOnCheckedChangeListener(this);
        init();
        mChatRecyclerViewAdapter = new ChatbotUpdatedRecyclerViewAdapter(this, mChatMessages, this);
        binding.recyclerViewMessageList.setAdapter(mChatRecyclerViewAdapter);


        binding.llWeAreConnecting.setVisibility(View.GONE);
        binding.llBothDown.setVisibility(View.GONE);

        chatScrollListener();
        voiceToTextFeature();

        isFromDeepLink = getIntent().getBooleanExtra(IS_DEEP_LINK_FLOW, false);

        if (isFromDeepLink){
            chatbotPresenter.trackAnalyticsOnPageLoad();
        }
        binding.micButton.setOnClickListener(v -> micButtonClick());
        binding.buttonChatboxSend.setOnClickListener(v -> sendMessage());
        binding.includeBothDownLinearLayout.tvMobileNumber.setOnClickListener(v -> dailMobileNumber());
        binding.buttonScroll.setOnClickListener(v -> scrollButtonClick());
        binding.edittextChatbox.setOnFocusChangeListener((v, hasFocus) -> onEditTextFocusChange(hasFocus));
    }

    public void voiceToTextFeature() {
        if (!mfeaturesetcontroller.isFeatureDisabled(FeatureConstants.FTR_CONVERSATIONAL_VOICE_TO_TEXT)) {
            binding.micButton.setVisibility(View.VISIBLE);
            audioPermission();
            if (isHuaweiDevice()) {
                performVoiceToTextActionForHuawei();
            } else {
                performVoiceTotextAction();
            }
        } else {
            binding.micButton.setVisibility(View.GONE);
        }
    }

    public void audioPermission() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            checkPermission();
        }
    }

    public boolean isHuaweiDevice() {
        try {
            String device = Build.MANUFACTURER;
            return device != null && device.equalsIgnoreCase(Constants.DEVICE_MANUFACTURER.HUAWEI);
        } catch (Exception e) {
            NBLogger.e("isHuaweiDevice", e.getMessage());
        }
        return false;
    }

    void micButtonClick() {
        if (!isMicrophoneActive) {
            isMicrophoneActive = true;
            binding.micButton.setImageResource(R.drawable.microphoneactive);
            binding.edittextChatbox.setHint(getString(R.string.speak_now_and_tap_stop));
            binding.edittextChatbox.setContentDescription(getString(R.string.speak_now_and_tap_stop));
            if (isHuaweiDevice()) {
                speechRecognizerHuawei.startRecognizing(speechRecognizerIntentForHuawei);
            } else {
                speechRecognizer.startListening(speechRecognizerIntent);
            }
            chatbotPresenter.handleChatbotVoiceToTextAnalytics();
        } else {
            isMicrophoneActive = false;
            if (speechRecognizer != null)
                speechRecognizer.stopListening();
            binding.micButton.setImageResource(R.drawable.microphone);
            showMicButton();
            binding.edittextChatbox.setHint(ChatConstants.HINT_MESSAGE);
            binding.edittextChatbox.setContentDescription(ChatConstants.HINT_MESSAGE);
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (speechRecognizer != null) {
            speechRecognizer.destroy();
        }
        if (speechRecognizerHuawei != null) {
            speechRecognizerHuawei.destroy();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == RECORD_AUDIO_REQUEST_CODE && grantResults.length > 0) {
            if (grantResults[0] == PackageManager.PERMISSION_GRANTED)
                Toast.makeText(this, "Permission Granted", Toast.LENGTH_SHORT).show();
            else
                ViewUtils.hideViews(binding.micButton);

        }
    }

    private void checkPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.RECORD_AUDIO}, RECORD_AUDIO_REQUEST_CODE);
        }
    }

    private String getScreenName() {
        return getIntent() != null && getIntent().getExtras() != null && getIntent().getExtras().containsKey(NavigationTarget.CONVERSATION_ID) ?
                getIntent().getStringExtra(NavigationTarget.CONVERSATION_ID) : "";
    }

    private void chatScrollListener() {
        binding.recyclerViewMessageList.addOnScrollListener(new RecyclerView.OnScrollListener() {


            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                int totalItemCount = binding.recyclerViewMessageList.getAdapter().getItemCount();
                if (totalItemCount <= 0) return;
                int lastVisibleItemIndex = layoutManager.findLastVisibleItemPosition();
                if (lastVisibleItemIndex < totalItemCount - 1) {
                    showFloatingButton();
                } else {
                    hideFloatingButton();
                }

            }
        });
    }

    protected void onEditTextFocusChange(boolean hasFocus) {
        if (hasFocus) {
            binding.layoutChatbox.setEnabled(true);
            binding.layoutChatbox.setBackgroundColor(ContextCompat.getColor(this, R.color.nbtoolbarchart_axisXLabelColor));
            binding.edittextChatbox.setHint(StringUtils.EMPTY_STRING);
            binding.edittextChatbox.setContentDescription(StringUtils.EMPTY_STRING);
        } else {
            binding.layoutChatbox.setEnabled(false);
            binding.layoutChatbox.setBackgroundColor(ContextCompat.getColor(this, R.color.slight_gray));
            if (binding.edittextChatbox.getText().toString().isEmpty()) {
                binding.edittextChatbox.setHint(ChatConstants.HINT_MESSAGE);
                binding.edittextChatbox.setContentDescription(ChatConstants.HINT_MESSAGE);
            }
        }
    }

    private void init() {
        enableDisableChatView(true);
        layoutManager = new LinearLayoutManager(this,
                LinearLayoutManager.VERTICAL, false) {
            @Override
            public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
                LinearSmoothScroller smoothScroller = new LinearSmoothScroller(ChatbotActivity.this) {

                    private static final float SPEED = 100f;// Change this value (default=25f)

                    @Override
                    protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
                        return SPEED / displayMetrics.densityDpi;
                    }

                };
                smoothScroller.setTargetPosition(position);
                startSmoothScroll(smoothScroller);
            }
        };
        layoutManager.setStackFromEnd(true);
        layoutManager.setItemPrefetchEnabled(false);
        binding.recyclerViewMessageList.setLayoutManager(layoutManager);
        binding.recyclerViewMessageList.setHasFixedSize(true);
        handleAgentTyping(true);

    }

    private void hideFloatingButton() {
        binding.buttonScroll.animate().alpha(0f).setDuration(200);
    }

    private void showFloatingButton() {
        binding.buttonScroll.animate().alpha(1f).setDuration(200);
    }

    private void enableDisableChatView(boolean state) {
        ViewUtils.enableDisableViews(state, binding.edittextChatbox, binding.buttonChatboxSend, binding.layoutChatbox);
        if (binding.edittextChatbox.isEnabled()) {
            binding.edittextChatbox.clearFocus();
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
            ViewUtils.showViews(binding.layoutChatbox);
            binding.layoutChatbox.setBackgroundColor(ContextCompat.getColor(this, R.color.slight_gray));
            if (binding.edittextChatbox.getText().toString().isEmpty()) {
                binding.edittextChatbox.setHint(ChatConstants.HINT_MESSAGE);
                binding.edittextChatbox.setContentDescription(ChatConstants.HINT_MESSAGE);
            }
        } else {
            ViewUtils.disableAndClearFocusEditableField(binding.edittextChatbox);
            getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
            ViewUtils.hideViews(binding.layoutChatbox);
        }
    }

    /**
     * @param s
     * @param start
     * @param count
     * @param after This is a compulsory method of Text Change Listener
     */
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        Log.v(TAG, "beforeTextChanged");


    }

    /**
     * @param s
     * @param start
     * @param before
     * @param count  This is a compulsory method of Text Change Listener
     */
    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        Log.v(TAG, "onTextChanged");
        onChatbotEditTextTextChange();

    }

    /**
     * @param s This is a compulsory method of Text Change Listener
     */
    @Override
    public void afterTextChanged(Editable s) {
        Log.v(TAG, "afterTextChanged");
    }

    @Override
    public void handleAgentTyping(boolean isTyping) {
        if (isTyping) {
            ViewUtils.showViews(binding.typingLayout, binding.animationView);
            binding.animationView.setImageAssetsFolder("assets/");
            binding.animationView.setAnimation("agent_typing.json");
            binding.animationView.setRepeatCount(LottieDrawable.INFINITE);
            binding.animationView.playAnimation();
        } else {
            ViewUtils.hideViews(binding.typingLayout, binding.animationView);
        }
    }

    @Override
    public void onCustomerMessage(ChatbotSessionMainResponseDataModel chatMessage) {
        mChatMessages.add(chatMessage);
        setChatMessages(chatMessage);
    }


    @Override
    public void setChatMessages(ChatbotSessionMainResponseDataModel chatMessage) {
        handleAgentTyping(false);

        //set Visibility
        binding.llWeAreConnecting.setVisibility(View.GONE);
        binding.llBothDown.setVisibility(View.GONE);
        binding.recyclerViewMessageList.setVisibility(View.VISIBLE);
        binding.layoutChatbox.setVisibility(View.VISIBLE);

        mChatMessages.add(chatMessage);
        binding.recyclerViewMessageList.scrollToPosition(mChatRecyclerViewAdapter.getItemCount() + 2);
        NBLogger.e("TAG", mChatRecyclerViewAdapter.getNumberOfSections() + "");
        NBLogger.e("TAG ItemsInSection", mChatRecyclerViewAdapter.getNumberOfItemsInSection(mChatRecyclerViewAdapter.getNumberOfSections() - 1) + "");


        mChatRecyclerViewAdapter.notifyAllSectionsDataSetChanged();

        if (mChatRecyclerViewAdapter.getNumberOfItemsInSection(mChatRecyclerViewAdapter.getNumberOfSections() - 1) > 1) {
            showFloatingButton();
        }

    }

    @Override
    public void closeKeyboard() {

        ViewUtils.hideSoftKeyboard(this, binding.edittextChatbox);
    }

    @Override
    public void showProgressBar(boolean show) {
        if (show)
            ViewUtils.showViews(binding.progressbarChatHistory);
        else
            ViewUtils.hideViews(binding.progressbarChatHistory);

    }

    @Override
    public void checkLiveAgentIsUp() {
        if (!mfeaturesetcontroller.isFeatureDisabled(FeatureConstants.APP_CHAT)) {
            if (mBoundService != null && mBoundService.isChatServerConnected()) {
                mBoundService.decideHistoryOrSession();
                chatbotPresenter.callHistory();
            } else {
                cameFromLiveAgent = true;
                binding.llWeAreConnecting.setVisibility(View.GONE);
                binding.llBothDown.setVisibility(View.VISIBLE);
                binding.layoutChatbox.setVisibility(View.INVISIBLE);
                binding.recyclerViewMessageList.setVisibility(View.INVISIBLE);
                binding.micButton.setVisibility(View.GONE);
            }
        } else {
            (NonTpLiveAgentErrorDialog.getInstance())
                    .show(this.getSupportFragmentManager(), NonTpLiveAgentErrorDialog.TAG);
        }
    }


    @Override
    public void showIndicator(boolean toShow) {
        NBLogger.e(TAG, "showIndicator");

    }

    @Override
    public void showExitDialog() {
        final UserConsentDialog userConsentDialog = UserConsentDialog.getInstance(getString(R.string.end_chatbot),
                getString(R.string.end_chatbot_message),
                getString(R.string.end_chat), getString(R.string.continue_chatbot));
        userConsentDialog.setCancelable(false);
        userConsentDialog.setiDialog(new IDialog() {
            @Override
            public void onPositiveButtonClick() {
                userConsentDialog.dismiss();
                chatbotPresenter.handleCloseChatMenuClicked();
                if (isFromDeepLink) {
                    chatbotPresenter.sendAnalyticsOnLeaveChat();
                }
            }

            @Override
            public void onNegativeButtonClick() {
                userConsentDialog.dismiss();
            }
        });

        userConsentDialog.show(getSupportFragmentManager(), UserConsentDialog.TAG);

    }

    @Override
    public <T> void showChatError(String errorCode, int apiRetryCounter, String apiAlias, String apiName, T modelName) {
        handleAgentTyping(false);
        if (apiRetryCounter == 0) {
            binding.llWeAreConnecting.setVisibility(View.VISIBLE);
            binding.llBothDown.setVisibility(View.GONE);
            binding.layoutChatbox.setVisibility(View.GONE);
            binding.recyclerViewMessageList.setVisibility(View.GONE);
            binding.micButton.setVisibility(View.GONE);
        } else if (apiRetryCounter == 1) {
            binding.llWeAreConnecting.setVisibility(View.GONE);

            if (errorCode.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_500)) {

                defaultError();

            } else if (errorCode.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_0)) {

                showError(getString(R.string.You_are_not_connected), getString(R.string.check_your_connection),
                        getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());

                binding.llBothDown.setVisibility(View.GONE);
                binding.layoutChatbox.setVisibility(View.VISIBLE);
                binding.recyclerViewMessageList.setVisibility(View.VISIBLE);
            } else if (errorCode.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_201)) {

                showError(getString(R.string.server_error), getString(R.string.something_got_in_our_way),
                        getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());
                binding.layoutChatbox.setVisibility(View.VISIBLE);
                binding.llBothDown.setVisibility(View.GONE);
                binding.recyclerViewMessageList.setVisibility(View.VISIBLE);
            } else if (errorCode.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_204)) {

                showError(getString(R.string.operation_succeeded_however_there_are_contents), getString(R.string.just_a_moment_enbi_is_looking_for_an_agent),
                        getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());

                defaultError();

            } else if (errorCode.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_400)) {
                showError(getString(R.string.invalid_request), getString(R.string.something_got_in_our_way_please_try_another_question),
                        getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());

                defaultError();

            } else if (errorCode.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_401)) {

                showError(getString(R.string.Operation_succeeded_access_denied), getString(R.string.access_denied_please_call_nedbank),
                        getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());

                defaultError();

            } else if (errorCode.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_403)) {

                showError(getString(R.string.invalid_secret), getString(R.string.something_is_not_right_please_try_again_later)
                        ,
                        getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());

                defaultError();

            } else if (errorCode.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_404)) {

                showError(getString(R.string.page_not_found), getString(R.string.enbi_couldnot_find_what_you_are_looking_for),
                        getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());
                defaultError();
            } else {
                showChatError1(errorCode);
            }
        }

        int splashDisplayLength = 2000;
        new Handler(Looper.getMainLooper()).postDelayed(() ->
                chatbotPresenter.retryApi(apiAlias, apiName, modelName), splashDisplayLength);

    }

    private void showChatError1(String chaterror) {
        if (chaterror.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_450)) {

            showError(getString(R.string.operation_secceeded_otp_is_required), getString(R.string.enter_the_one_time_password_as_soon_as_you_get_it),
                    getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());
            defaultError();

        } else if (chaterror.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_451)) {

            showError(getString(R.string.operation_succeded_invalid_otp_required_please_try_again), getString(R.string.incorrect_otp_please_reenter_the_one_time_password),
                    getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());
            showError(getString(R.string.operation_succeded_invalid_otp_required_please_try_again), getString(R.string.incorrect_otp_please_reenter_the_one_time_password),
                    getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());

            defaultError();

        } else if (chaterror.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_452)) {

            showError(getString(R.string.operation_succeeded_expired_otp), getString(R.string.expired_otp_your_one_time_password_timed_out),
                    getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());

            defaultError();

        } else if (chaterror.equalsIgnoreCase(ChatbotErrorCodes.ERROR_CODE_453)) {

            showError(getString(R.string.operation_succeeded_too_many_otp_failures), getString(R.string.you_re_reached_the_otp_limit),
                    getString(R.string.snackbar_action_ok), NBSnackbar.FOREVER, () -> NBSnackbar.instance().dismissSnackBar());

            defaultError();

        } else {
            defaultError();
        }
    }

    private void defaultError() {
        binding.layoutChatbox.setVisibility(View.GONE);
        binding.llBothDown.setVisibility(View.VISIBLE);
        binding.recyclerViewMessageList.setVisibility(View.GONE);
        binding.micButton.setVisibility(View.GONE);
    }

    @Override
    public void finishActivity() {
        finish();

    }

    @Override
    public void handleHyperlink(String payload) {
        if (URLUtil.isValidUrl(payload)) {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse(payload));
            startActivity(intent);
        } else {
            NBLogger.e(TAG, "Not Valid Url");
        }
    }

    public void showMicButton() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED) {
            ViewUtils.showViews(binding.micButton);
        }
    }

    protected void onChatbotEditTextTextChange() {
        if (binding.edittextChatbox.getText().toString().isEmpty()) {
            ViewUtils.hideViews(binding.buttonChatboxSend);
            showMicButton();
        } else {
            ViewUtils.showViews(binding.buttonChatboxSend);
            ViewUtils.hideViews(binding.micButton);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.chat_menu_close, menu);
        return true;
    }

    void sendMessage() {
        if (speechRecognizer != null)
            speechRecognizer.stopListening();
        binding.micButton.setImageResource(R.drawable.microphone);
        showMicButton();
        chatbotPresenter.postEngagedUserAnalytics();
        chatbotPresenter.sendMessage(binding.edittextChatbox.getText().toString().trim());
        binding.edittextChatbox.setText(StringUtils.EMPTY_STRING);
        binding.edittextChatbox.clearFocus();
    }

    void dailMobileNumber() {
        Intent intent = new Intent(Intent.ACTION_DIAL);
        intent.setData(Uri.parse("tel:0800 555 111"));
        startActivity(intent);
    }

    void scrollButtonClick() {

        int totalItemCount = binding.recyclerViewMessageList.getAdapter().getItemCount();
        if (totalItemCount <= 0) return;


        layoutManager.smoothScrollToPosition(binding.recyclerViewMessageList, null, mChatRecyclerViewAdapter.getLastItemPosition());

        hideFloatingButton();

    }

    /**
     * mandatory method
     *
     * @param name
     * @param service
     */
    @Override
    public void onServiceConnected(ComponentName name, IBinder service) {
        NBLogger.e("service", "service connected");
        mBoundService = ((ChatService.ChatServiceBinder) service).getService();
        mBoundService.checkServerIsUpForEnbi();
    }

    /**
     * mandatory method
     *
     * @param name
     */
    @Override
    public void onServiceDisconnected(ComponentName name) {
        NBLogger.e("service", "service disconnected");
    }

    @Override
    public void onCheckedChanged(RadioGroup radioGroup, int value) {
        int selectedId = radioGroup.getCheckedRadioButtonId();
        if (selectedId == binding.chatbotFeedback.radioBtnOne.getId()) {
            Toast.makeText(this, "One", Toast.LENGTH_SHORT).show();
        } else if (selectedId == binding.chatbotFeedback.radioBtnTwo.getId()) {
            Toast.makeText(this, "Two", Toast.LENGTH_SHORT).show();
        } else if (selectedId == binding.chatbotFeedback.radioBtnThree.getId()) {
            Toast.makeText(this, "Three", Toast.LENGTH_SHORT).show();
        } else if (selectedId == binding.chatbotFeedback.radioBtnFour.getId()) {
            Toast.makeText(this, "Four", Toast.LENGTH_SHORT).show();
        } else if (selectedId == binding.chatbotFeedback.radioBtnFive.getId()) {
            Toast.makeText(this, "Five", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(this, "One", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void startBrowser(String url) {
        IntentUtils.openDefaultBrowser(this, url);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.menu_item_chat_close) {
            chatbotPresenter.handleEndChatClick();
            return super.onOptionsItemSelected(item);
        } else if (item.getItemId() == android.R.id.home) {
            if (cameFromLiveAgent) {
                binding.llWeAreConnecting.setVisibility(View.GONE);
                binding.llBothDown.setVisibility(View.GONE);
                binding.layoutChatbox.setVisibility(View.VISIBLE);
                binding.recyclerViewMessageList.setVisibility(View.VISIBLE);
                cameFromLiveAgent = false;
                return true;

            } else {
                chatbotPresenter.backActionAnalytics();
                if (isFromDeepLink) {
                    chatbotPresenter.sendAnalyticsOnLeaveChat();
                }
                return super.onOptionsItemSelected(item);
            }

        }
        return super.onOptionsItemSelected(item);

    }


    @Override
    public void onButtonClicked(ChatbotMessageContentsResponseDataModel item) {
        chatbotPresenter.postEngagedUserAnalytics();
        String payloadWithoutSpace = item.getPayload().getPayload().replaceAll("\\s","");
        item.getPayload().setPayload(payloadWithoutSpace);
        chatbotPresenter.handleButtonClick(item);
    }

    @Override
    public void onQuickOneClicked(ChatbotQuickRepliesResponseDataModel quickreply) {
        chatbotPresenter.postEngagedUserAnalytics();
        chatbotPresenter.postQuickReply(quickreply);

    }

    @Override
    public void onSelectedListButtonDeepLinkClicked(ChatbotListContentPayloadButtonDataModel debitOrderItem) {
        chatbotPresenter.postEngagedUserAnalytics();
        chatbotPresenter.onListButtonDeepLinkClicked(debitOrderItem);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        chatbotPresenter.backActionAnalytics();
        if (isFromDeepLink) {
            chatbotPresenter.sendAnalyticsOnLeaveChat();
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        bindChatService();
    }

    @Override
    public void onStop() {
        super.onStop();
        unbindChatService();
    }

    private void bindChatService() {
        bindService(new Intent(this, ChatService.class), this, BIND_AUTO_CREATE);
    }

    private void unbindChatService() {
        unbindService(this);
    }

    private void performVoiceTotextAction() {
        speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this);
        speechRecognizerIntent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
        speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);
        speechRecognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault());
        speechRecognizer.setRecognitionListener(new RecognitionListener() {
            @Override
            public void onReadyForSpeech(Bundle bundle) {
                // Called when the endpointer is ready for the user to start speaking.
            }

            @Override
            public void onBeginningOfSpeech() {
                binding.edittextChatbox.setText("");
                binding.edittextChatbox.setHint(getString(R.string.listening_and_tap_stop));
                binding.edittextChatbox.setContentDescription(getString(R.string.listening_and_tap_stop));
                handleVoiceToTextTyping(true);
            }

            @Override
            public void onRmsChanged(float v) {
                // Used for receiving notifications from the SpeechRecognizer when the recognition related events occur.
            }

            @Override
            public void onBufferReceived(byte[] bytes) {
                // The purpose of this function is to allow giving feedback to the user regarding the captured audio.
            }

            @Override
            public void onEndOfSpeech() {
                // Called after the user stops speaking.
            }

            @Override
            public void onError(int error) {
                if (error == SpeechRecognizer.ERROR_NO_MATCH) {
                    endOfSpeech();
                }
            }

            @Override
            public void onResults(Bundle bundle) {
                ArrayList<String> data = bundle.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
                binding.edittextChatbox.setText(data.get(0));
                ViewUtils.hideViews(binding.micButton);
                endOfSpeech();
            }

            @Override
            public void onPartialResults(Bundle bundle) {
                // Called when partial recognition results are available.
            }

            @Override
            public void onEvent(int i, Bundle bundle) {
                // Reserved for adding future events.
            }
        });

    }


    private void endOfSpeech() {
        handleVoiceToTextTyping(false);
        binding.edittextChatbox.setHint(ChatConstants.HINT_MESSAGE);
        binding.edittextChatbox.setContentDescription(ChatConstants.HINT_MESSAGE);
    }

    protected class SpeechRecognitionListener implements MLAsrListener {
        @Override
        public void onStartListening() {
            binding.edittextChatbox.setText("");
            binding.edittextChatbox.setHint(getString(R.string.listening_and_tap_stop));
            binding.edittextChatbox.setContentDescription(getString(R.string.listening_and_tap_stop));
            handleVoiceToTextTyping(true);
        }

        @Override
        public void onStartingOfSpeech() {
            // Calls on start of speech
        }

        @Override
        public void onVoiceDataReceived(byte[] data, float energy, Bundle bundle) {
            // Return the original PCM stream and audio power to the user. This API is not running in the main thread, and the return result is processed in the sub-thread.
        }

        @Override
        public void onRecognizingResults(Bundle partialResults) {
            // Return partial results
        }

        @Override
        public void onResults(Bundle results) {
            ViewUtils.hideViews(binding.micButton);
            handleVoiceToTextTyping(false);
            String data = results.getString(MLAsrRecognizer.RESULTS_RECOGNIZED);
            binding.edittextChatbox.setText(data);
            binding.edittextChatbox.setHint(ChatConstants.HINT_MESSAGE);
            binding.edittextChatbox.setContentDescription(ChatConstants.HINT_MESSAGE);
        }

        @Override
        public void onError(int error, String errorMessage) {
            // Method will be called when any error occurs
        }

        @Override
        public void onState(int state, Bundle params) {
            // Return state
        }
    }

    public void performVoiceToTextActionForHuawei() {
        speechRecognizerHuawei = MLAsrRecognizer.createAsrRecognizer(this);
        speechRecognizerHuawei.setAsrListener(new SpeechRecognitionListener());
        speechRecognizerIntentForHuawei = new Intent(MLAsrConstants.ACTION_HMS_ASR_SPEECH);
        speechRecognizerIntentForHuawei
                .putExtra(MLAsrConstants.LANGUAGE, "en-US")
                .putExtra(MLAsrConstants.FEATURE, MLAsrConstants.FEATURE_WORDFLUX);
    }

    public void handleVoiceToTextTyping(boolean isTyping) {
        if (isTyping) {
            ViewUtils.showViews(binding.typingLayoutVoice, binding.animationViewVoice);
            binding.animationViewVoice.setImageAssetsFolder("assets/");
            binding.animationViewVoice.setAnimation("agent_typing.json");
            binding.animationViewVoice.setRepeatCount(LottieDrawable.INFINITE);
            binding.animationViewVoice.playAnimation();
        } else {
            ViewUtils.hideViews(binding.typingLayoutVoice, binding.animationViewVoice);
        }
    }
}
