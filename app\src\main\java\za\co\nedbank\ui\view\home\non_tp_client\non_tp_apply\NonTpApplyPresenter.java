package za.co.nedbank.ui.view.home.non_tp_client.non_tp_apply;

import static za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.ENABLE_JAVA_SCRIPT;
import static za.co.nedbank.enroll_v2.Constants.BundleKeys.CMS_URL;

import android.annotation.SuppressLint;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.ResponseStorageKey;
import za.co.nedbank.core.common.CardType;
import za.co.nedbank.core.data.networking.client.ResultErrorCodes;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.data.storage.StorageUtility;
import za.co.nedbank.core.domain.GetNonTpUserDetailUseCase;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetPreferredNameUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.domain.usecase.investmentonline.GetFicaStatusUseCase;
import za.co.nedbank.core.domain.usecase.media_content.AvoMediaContentUseCase;
import za.co.nedbank.core.domain.usecase.notifications.GetFBNotificationsCountExtendedUseCase;
import za.co.nedbank.core.domain.usecase.preapprovedoffers.GetPreApprovedOfferUseCase;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.errors.HttpStatus;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.tracking.appsflyer.AddContextData;
import za.co.nedbank.core.tracking.appsflyer.AppsFlyerTags;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.preapprovedoffers.PreApprovedOffersDataToViewModelMapper;
import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;
import za.co.nedbank.core.view.model.media_content.ProductOfferViewModel;
import za.co.nedbank.core.view.model.media_content.ProductStatus;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.domain.usecases.fica.AcquisitionUseCase;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.enroll_v2.view.fica.error.FicaErrorHandler;
import za.co.nedbank.enroll_v2.view.mapper.fica.AcquisitionUseCaseRequestViewModelToDataMapper;
import za.co.nedbank.enroll_v2.view.mapper.fica.AcquisitionUseCaseResponseDataToViewModelMapper;
import za.co.nedbank.enroll_v2.view.model.fica.AcquisitionUseCaseRequestViewModel;
import za.co.nedbank.services.domain.mapper.PendingAccountsEntityToDataModelMapper;
import za.co.nedbank.services.domain.usecase.investmentnotices.GetPendingAccountsUsecase;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.ui.domain.usecase.GetAvoWalletDetailsUseCase;
import za.co.nedbank.ui.view.home.non_tp_client.BaseDashboardAccountsPresenter;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class NonTpApplyPresenter extends BaseDashboardAccountsPresenter<NonTpApplyView> {

    @Inject
    public NonTpApplyPresenter(GetPreferredNameUseCase getPreferredNameUseCase,
                               GetOverviewUseCase getOverviewUseCase,
                               NavigationRouter navigationRouter, ErrorHandler errorHandler, Analytics analytics,
                               FeatureSetController featureSetController, ApplicationStorage applicationStorage,
                               @Named("memory") ApplicationStorage memoryApplicationStorage, GetFBNotificationsCountExtendedUseCase getFBNotificationsCountExtendedUseCase,
                               GetNonTpUserDetailUseCase getNonTpUserDetailUseCase, UserDetailDataToViewModelMapper userDetailDataToViewModelMapper,
                               GetFicaStatusUseCase getFicaStatusUseCase,
                               final FicaErrorHandler ficaErrorHandler, AcquisitionUseCase acquisitionUseCase,
                               final AcquisitionUseCaseResponseDataToViewModelMapper acquisitionUseCaseResponseDataToViewModelMapper,
                               final AcquisitionUseCaseRequestViewModelToDataMapper acquisitionUseCaseRequestViewModelToDataMapper,
                               final GetFedarationListUseCase getFedarationListUseCase,
                               final GetAvoWalletDetailsUseCase walletDetailsUseCase,
                               final AFAnalyticsTracker afAnalyticsTracker, final AvoMediaContentUseCase mediaContentUseCase,
                               GetPreApprovedOfferUseCase getPreApprovedOffersUseCase,
                               final GetPendingAccountsUsecase getPendingAccountsUsecase,
                               final PendingAccountsEntityToDataModelMapper pendingAccountsEntitiyToDataModelMapper,
                               PreApprovedOffersDataToViewModelMapper preApprovedOffersDataToViewModelMapper,
                               final StorageUtility storageUtility) {
        super(getPreferredNameUseCase, getOverviewUseCase, navigationRouter, errorHandler, analytics, storageUtility, featureSetController,
                applicationStorage, memoryApplicationStorage, getFBNotificationsCountExtendedUseCase, getNonTpUserDetailUseCase,
                userDetailDataToViewModelMapper, getFicaStatusUseCase, ficaErrorHandler, acquisitionUseCase,
                acquisitionUseCaseResponseDataToViewModelMapper, acquisitionUseCaseRequestViewModelToDataMapper,
                getFedarationListUseCase, walletDetailsUseCase, afAnalyticsTracker,pendingAccountsEntitiyToDataModelMapper,getPendingAccountsUsecase, mediaContentUseCase,
                getPreApprovedOffersUseCase,preApprovedOffersDataToViewModelMapper);

    }

    @Override
    public boolean canTransact() {
        return false;
    }

    @Override
    public void onChatIconCount(int chatIconCount) {
        if (view != null) {
            ((NonTpApplyView) view).setChatIcon(chatIconCount);
        }
    }

    void onClickBankLayout() {
        mMemoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, StringUtils.EMPTY_STRING);
        mMemoryApplicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);
        analytics.sendEvent(StringUtils.EMPTY_STRING, AppTracking.EN_3_NON_TP_BANK_CARD, StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BANK_INTENT).withParam(za.co.nedbank.enroll_v2.Constants.NFPCreditCardParam.IS_PRE_LOGIN, false));
    }

    public void onLoanClick() {
        analytics.sendEvent(EnrollV2TrackingEvent.EN_3_NON_TP_DB_PRODUCT_CARD_BORROW, TrackingParam.CLICK_ACTION,StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BORROW_INTENT));
    }

    public void onFinancialPlannerClick() {
        analytics.sendEvent(EnrollV2TrackingEvent.EN_3_NON_TP_DB_PRODUCT_CARD_FP, TrackingParam.CLICK_ACTION,StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FINANCIAL_PLANNER_HOME));
    }

    @SuppressLint("CheckResult")
    public void setSessionId() {

        AcquisitionUseCaseRequestViewModel acquisitionUseCaseRequestViewModel = new AcquisitionUseCaseRequestViewModel(cisNumber);

        acquisitionUseCase.execute(acquisitionUseCaseRequestViewModelToDataMapper.mapData(acquisitionUseCaseRequestViewModel))
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (null != view) {
                        ((NonTpApplyView) view).showProgressBar(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (null != view) {
                        ((NonTpApplyView) view).showProgressBar(false);
                    }
                })
                .map(acquisitionUseCaseResponseDataToViewModelMapper::mapData)
                .subscribe(acquisitionUseCaseResponseViewModel -> {
                    if (acquisitionUseCaseResponseViewModel != null
                            && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel()
                            && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData()
                            && acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().size() > 0
                            && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0)
                            && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail()
                            && acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail().size() > 0
                            && (acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail().get(0)
                            .getResult().equalsIgnoreCase(ResultErrorCodes.VALID_R_RESULT) ||
                            acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail().get(0)
                                    .getResult().equalsIgnoreCase(ResultErrorCodes.DATA_ITEM_ALREADY_SET))
                            && acquisitionUseCaseResponseViewModel.getDataEntity() != null
                            && acquisitionUseCaseResponseViewModel.getDataEntity().getSessionId() != null) {

                        mMemoryApplicationStorage.putString(StorageKeys.FICA_SESSION_ID, acquisitionUseCaseResponseViewModel.getDataEntity().getSessionId());
                        boolean isStudent = false;
                        if (idOrTaxIdNo != null) {
                            isStudent = isStudent(idOrTaxIdNo.substring(za.co.nedbank.core.Constants.ZERO, za.co.nedbank.core.Constants.DIGIT_COUNT_USER_AGE));
                            mMemoryApplicationStorage.putString(StorageKeys.FICA_ID_NUMBER, idOrTaxIdNo);
                        }
                        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BANK_INTENT)
                                .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.IS_STUDENT, isStudent));
                    } else {
                        if (acquisitionUseCaseResponseViewModel != null
                                && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel()
                                && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData()
                                && acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().size() > 0
                                && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0)
                                && null != acquisitionUseCaseResponseViewModel.getMetaDataViewModel().getResultData().get(0).getResultDetail()) {
                            Throwable throwable = new Throwable(acquisitionUseCaseResponseViewModel.getMetaDataViewModel()
                                    .getResultData().get(0).getResultDetail().get(0)
                                    .getResult());
                            handleException(throwable);
                        }
                    }

                }, this::handleException);
    }

    private boolean isStudent(String number) {
        if (number.isEmpty()) {
            return false;
        }
        Date date = FormattingUtil.getDate(number);
        if (null != date) {
            return AppUtility.isDateInStudentAgeRange(date);
        }
        return false;
    }

    public void loadNotificationCount() {
        int notificationCount = mMemoryApplicationStorage.getInteger(StorageKeys.TOTAL_NOTIFICATION_COUNT, 0);
        if (notificationCount <= 0) {
            notificationCount = mMemoryApplicationStorage.getInteger(StorageKeys.PRE_APPROVED_OFFERS_COUNT, 0);
        }
        if (view != null) {
            view.receiveNotificationCount(notificationCount);
        }
    }

    private void handleException(Throwable throwable) {
        ficaErrorHandler.handleError(throwable);
    }

    public void navigateToMediaDetail(String link) {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CMS_MEDIA_CONTENT).withParam(ENABLE_JAVA_SCRIPT,true).withParam(CMS_URL, link));
    }

    void getUserInfo() {
        getNonTpUserDetailUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(this::getUserInfo,
                        throwable -> {
                            Error error = errorHandler.getErrorMessage(throwable);
                            if (view != null) {
                                if (error.getCode() == HttpStatus.NO_CONTENT) {
                                    view.showError(error.getMessage());
                                }
                            }
                        });
    }


    private void getUserInfo(final UserDetailData userDetailData) {
        if (userDetailData != null && view != null) {
            view.setUserInfo(mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData), Boolean.TRUE);
            mMemoryApplicationStorage.putObject(ResponseStorageKey.USERDETAILDATA,
                    mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData));
            if (StringUtils.isNullOrEmpty(mMemoryApplicationStorage.getString(
                    StorageKeys.CIS_NUMBER, StringUtils.EMPTY_STRING))) {
                mMemoryApplicationStorage.putString(StorageKeys.CIS_NUMBER,
                        userDetailData.getCisNumber());
            }
        }
    }

    void loadProductCard(boolean isBankCardDisable) {

        List<AppLayoutViewModel> mList = new ArrayList<>();
        mList.add(getProductCard(isBankCardDisable));
        if (view != null) {
            ((NonTpApplyView) view).showMediaAndOfferCards(mList);
        }
    }

    private AppLayoutViewModel getProductCard(boolean isBankCardDisable){
        AppLayoutViewModel appLayoutProductCard = new AppLayoutViewModel();
        appLayoutProductCard.setCardType(CardType.PRODUCT_OFFER);
        ProductOfferViewModel productViewModel = new ProductOfferViewModel();

        ProductStatus mInvisibleState = new ProductStatus();
        mInvisibleState.setVisible(false);

        ProductStatus mDisableState = new ProductStatus();
        mDisableState.setDisable(true);


        ProductStatus mActiveState = new ProductStatus();
        productViewModel.setBank( isBankCardDisable? mDisableState : mActiveState);
        productViewModel.setLoan(mActiveState);

        productViewModel.setFinancial(mActiveState);
        productViewModel.setForex(mInvisibleState);
        productViewModel.setInsurance(mInvisibleState);
        productViewModel.setInvestment(mInvisibleState);
        productViewModel.setSma(mInvisibleState);

        appLayoutProductCard.setProductOffer(productViewModel);

        return appLayoutProductCard;
    }





    void navigateToProductCardDetail(boolean isBankCardDisable) {
        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.PRODUCT_CARD_DETAIL).withParam(za.co.nedbank.enroll_v2.Constants.NFPCreditCardParam.IS_PRE_LOGIN, false).withParam(Constants.NON_TP_APPLY,true)
        .withParam(Constants.PRODUCT_CARD_STATUS,getProductCard(isBankCardDisable)));
    }

    void navigateToBankLayout() {
        mMemoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, StringUtils.EMPTY_STRING);
        analytics.sendEvent(StringUtils.EMPTY_STRING, EnrollV2TrackingEvent.EN_3_NON_TP_DB_PRODUCT_CARD_BANK, StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.BANK_INTENT).withParam(za.co.nedbank.enroll_v2.Constants.NFPCreditCardParam.IS_PRE_LOGIN, false));
    }

    public void logMediaCardClickEvent(String mediaCard, int position) {
        trackCompaignInitiationOnAppsflyer(mediaCard);
        HashMap cData = new HashMap();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.resetDimensions();
        adobeContextData.setFeatureCategory(TrackingParam.VAL_MARKETING_COMMUNICATION);
        adobeContextData.setFeature(TrackingParam.VAL_COMPAIGN);
        adobeContextData.setSubFeature(TrackingParam.VAL_MEDIA_CARDS);
        adobeContextData.setInternalCompaign(mediaCard);
        adobeContextData.setImpressions();
        adobeContextData.setSequence(String.format(TrackingEvent.ANALYTICS.VAL_MEDIA_TILE_SEQUENCE, position));
        analytics.sendEventActionWithMap(EnrollV2TrackingEvent.KEY_LATEST_MEDIA_CARD, cData);
    }

    public void trackCompaignInitiationOnAppsflyer(String compaignName){
        HashMap<String, Object> cdata = new HashMap<>();
        AddContextData addContextData = new AddContextData(cdata);
        addContextData.setCompaignName(compaignName);

        mAfAnalyticsTracker.sendEvent(AppsFlyerTags.AF_COMPAIGN_INITIATION, cdata);
    }


}
