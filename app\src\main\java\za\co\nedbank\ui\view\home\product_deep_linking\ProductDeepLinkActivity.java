package za.co.nedbank.ui.view.home.product_deep_linking;

import static za.co.nedbank.core.Constants.PNG;

import android.os.Bundle;
import android.text.Html;

import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.concierge.chat.view.NBChatBaseActivity;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.core.utils.IntentUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.ActivityProductDeepLinkBinding;
import za.co.nedbank.ui.di.AppDI;

public class ProductDeepLinkActivity extends NBChatBaseActivity implements ProductDeepLinkView {
    @Inject
    ProductDeepLinkPresenter mPresenter;
    private String mShortDescription;
    private boolean mIsFromTransactionalFlow;
    private ActivityProductDeepLinkBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProductDeepLinkBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        mPresenter.bind(this);
        readIntent();
        handleUI();
        mPresenter.handleAnalytics(mIsFromTransactionalFlow);
        binding.nextButton.setOnClickListener(v -> onNextClicked());
        binding.ivCross.setOnClickListener(v -> onCrossClick());
        binding.viewMoreText.setOnClickListener(v -> onViewMoreClick());
        binding.tAndCTxt.setOnClickListener(v -> onTermAndConditionClick());
        binding.ivChatIcon.setOnClickListener(v -> onChatClick());
    }

    private void readIntent() {
        mShortDescription = mPresenter.getShortDescription();
        mIsFromTransactionalFlow = IntentUtils.getBooleanValue(getIntent(), Constants.BUNDLE_KEYS.FROM_TRANSACTIONAL_FLOW, true);
    }

    private void handleUI() {
        if (mIsFromTransactionalFlow) {
            mPresenter.showContent(mShortDescription);
            ViewUtils.hideViews(binding.expandCampaignLayout);
            ViewUtils.showViews(binding.transactionalLayout);
        } else {
            ViewUtils.hideViews(binding.transactionalLayout);
            ViewUtils.showViews(binding.expandCampaignLayout);
            loadImage();
        }
    }

    private void loadImage() {
        showProgressBar(true);
        String deviceDensity = DeviceUtils.getDeviceDensity(getResources()) + PNG;
        Picasso.get()
                .load(Constants.EXPAND_CAMPAIGN_BANNER_IMAGE_URL + deviceDensity)
                .noPlaceholder()
                .priority(Picasso.Priority.HIGH)
                .into(binding.expandBannerImg, new Callback() {
                    @Override
                    public void onSuccess() {
                        showProgressBar(false);
                    }

                    @Override
                    public void onError(Exception e) {
                        showProgressBar(false);
                    }
                });
    }

    @Override
    public void showContent(String description) {
        binding.webContent.loadDataWithBaseURL(StringUtils.EMPTY_STRING, Html.fromHtml(description).toString(), za.co.nedbank.enroll_v2.Constants.MIME_TYPE, za.co.nedbank.enroll_v2.Constants.UTF_8, StringUtils.EMPTY_STRING);
    }

    @Override
    public void showProgressBar(boolean isShow) {
        if (isShow) {
            ViewUtils.showViews(binding.loadingView);
        } else {
            ViewUtils.hideViews(binding.loadingView);
        }
    }

    public void onNextClicked()
    {
         mPresenter.navigateToNextScreen(mIsFromTransactionalFlow);
    }

    public void onCrossClick() {
        mPresenter.onCrossClick(mIsFromTransactionalFlow);
    }

    public void onViewMoreClick() {
        mPresenter.handleViewMoreClick(mIsFromTransactionalFlow);
    }

    public void onTermAndConditionClick() {
        mPresenter.handleViewMoreClick(mIsFromTransactionalFlow);
    }
    public void onChatClick() {
        String conversationId = TrackingEvent.ANALYTICS.VAL_QUIT_MODAL;
        handleChatMenuClicked(conversationId);
    }
}