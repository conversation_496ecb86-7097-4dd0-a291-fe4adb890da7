/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import androidx.annotation.NonNull;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class ViewMoneyRequestsPresenter extends NBBasePresenter<ViewMoneyRequestsView> {

    private final NavigationRouter mNavigationRouter;
    private final Analytics mAnalytics;

    @Inject
    ViewMoneyRequestsPresenter(NavigationRouter navigationRouter, @NonNull Analytics analytics) {
        this.mNavigationRouter = navigationRouter;
        this.mAnalytics = analytics;
    }

    void navigateToDashboardScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.HOME);
        navigationTarget.withIntentFlagClearTopSingleTop(true);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    void trackMoneyRequest(int position) {
        mAnalytics.sendEvent(StringUtils.EMPTY_STRING, (position == 0) ? AppTracking.TAG_PAY_ME_REQUEST_RECEIVED :AppTracking.TAG_PAY_ME_REQUEST_SENT , StringUtils.EMPTY_STRING);
    }
}
