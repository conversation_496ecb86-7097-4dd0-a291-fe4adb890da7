/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.retention.feedback;


import android.os.Bundle;

import androidx.annotation.Nullable;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityRetentionFeedbackSuccessBinding;
import za.co.nedbank.ui.di.AppDI;

public class RetentionFeedbackSuccessActivity extends NBBaseActivity implements RetentionFeedbackSuccessView {

    @Inject
    RetentionFeedbackSuccessPresenter presenter;

    @Override
    protected void onCreate(@Nullable final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityRetentionFeedbackSuccessBinding binding = ActivityRetentionFeedbackSuccessBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        binding.successDoneButton.setOnClickListener(v -> presenter.doneBtnClicked());
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.bind(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        presenter.unbind();
    }

    @Override
    public void onBackPressed() {
        // Do nothing to prevent device back button
    }

}
