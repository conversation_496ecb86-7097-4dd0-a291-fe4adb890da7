package za.co.nedbank.ui.view.mapper;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.validation.ShapIdValidator;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;
import za.co.nedbank.core.view.model.TransactionsNotificationDetailsViewModel;
import za.co.nedbank.payment.common.domain.data.model.NotificationDetailsData;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryData;

public class TransactionHistoryDataToTransactionHistoryViewModelMapper {
    @Inject
    public TransactionHistoryDataToTransactionHistoryViewModelMapper() {
        // injected class constructor
    }

    public List<TransactionHistoryViewModel> mapTransactionHistoryDataToTransactionHistoryViewModel(List<TransactionHistoryData> transactionHistoryData) {
        List<TransactionHistoryViewModel> transactionHistoryViewModels = new ArrayList<>();
        for (TransactionHistoryData transactionHistoryDataTemp : transactionHistoryData) {
            if (transactionHistoryDataTemp != null)
                transactionHistoryViewModels.add(mapTransactionHistoryDataToView(transactionHistoryDataTemp));
        }
        return transactionHistoryViewModels;
    }

    private TransactionHistoryViewModel mapTransactionHistoryDataToView(TransactionHistoryData transactionHistoryDataTemp) {
        TransactionHistoryViewModel transactionHistoryViewModel = new TransactionHistoryViewModel();
        transactionHistoryViewModel.setTransactionType(transactionHistoryDataTemp.getTransactionType());
        transactionHistoryViewModel.setContactCardID(transactionHistoryDataTemp.getContactCardID());
        transactionHistoryViewModel.setAmount(transactionHistoryDataTemp.getAmount());
        transactionHistoryViewModel.setBatchID(transactionHistoryDataTemp.getBatchID());
        transactionHistoryViewModel.setmInstantPayment(transactionHistoryDataTemp.isInstantPayment());
        transactionHistoryViewModel.setStatus(transactionHistoryDataTemp.getStatus());
        transactionHistoryViewModel.setBeneficiaryID(transactionHistoryDataTemp.getBeneficiaryID());
        transactionHistoryViewModel.setBfName(transactionHistoryDataTemp.getBfName());
        transactionHistoryViewModel.setCapturedDate(transactionHistoryDataTemp.getCapturedDate());
        transactionHistoryViewModel.setDestinationNumber(transactionHistoryDataTemp.getDestinationNumber());
        transactionHistoryViewModel.setMyReference(transactionHistoryDataTemp.getMyReference());
        transactionHistoryViewModel.setMyDescription(transactionHistoryDataTemp.getMyDescription());
        transactionHistoryViewModel.setNextTransDate(transactionHistoryDataTemp.getNextTransDate());
        transactionHistoryViewModel.setPrepaidStatus(transactionHistoryDataTemp.getPrepaidStatus());
        transactionHistoryViewModel.setProductCode(transactionHistoryDataTemp.getProductCode());
        transactionHistoryViewModel.setBeneficiaryAccount(transactionHistoryDataTemp.getBeneficiaryAccount());
        transactionHistoryViewModel.setPurchaseReferenceNumber(transactionHistoryDataTemp.getPurchaseReferenceNumber());
        transactionHistoryViewModel.setServiceProvider(transactionHistoryDataTemp.getServiceProvider());
        transactionHistoryViewModel.setStartDate(transactionHistoryDataTemp.getStartDate());
        transactionHistoryViewModel.setTransactionID(transactionHistoryDataTemp.getTransactionID());
        transactionHistoryViewModel.setNotificationDetailsViewModels(mapNotification(transactionHistoryDataTemp.getNotificationDetailsData()));
        transactionHistoryViewModel.setRapidPayment(transactionHistoryDataTemp.isRapidPayment());
        if (transactionHistoryDataTemp.isRapidPayment() && transactionHistoryDataTemp.getBeneficiaryType().equalsIgnoreCase(BeneficiaryConstants.BENEFICIARY_TYPE_RPP)) {
            transactionHistoryViewModel.setShapID(parseShapId(transactionHistoryDataTemp.getProxyName(), transactionHistoryDataTemp.getProxyDomain()));
        } else {
            transactionHistoryViewModel.setShapID(null);
        }
        return transactionHistoryViewModel;
    }

    private String parseShapId(String proxyName, String proxyDomain) {
        String formattedProxyName = ShapIdValidator.getProxyNameView(proxyName,null);
        return ShapIdValidator.getProxyNameView(formattedProxyName,proxyDomain);
    }


    private List<TransactionsNotificationDetailsViewModel> mapNotification(List<NotificationDetailsData> notificationDetailsDataList) {
        List<TransactionsNotificationDetailsViewModel> notificationDetailsViewModels = null;
        if (notificationDetailsDataList != null && !notificationDetailsDataList.isEmpty()) {
            notificationDetailsViewModels = new ArrayList<>();
            for (NotificationDetailsData notificationDetailsData : notificationDetailsDataList) {
                TransactionsNotificationDetailsViewModel notificationDetailsViewModel = new TransactionsNotificationDetailsViewModel();
                notificationDetailsViewModel.setNotificationType(notificationDetailsData.getNotificationType());
                notificationDetailsViewModel.setNotificationAddress(notificationDetailsData.getNotificationAddress());
                notificationDetailsViewModels.add(notificationDetailsViewModel);
            }
        }
        return notificationDetailsViewModels;
    }
}
