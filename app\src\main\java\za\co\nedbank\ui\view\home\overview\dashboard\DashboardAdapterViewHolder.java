/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.overview.dashboard;


import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.core.view.ViewCompat;
import androidx.recyclerview.widget.RecyclerView;

import za.co.nedbank.R;
import za.co.nedbank.core.dashboard.DashboardCard;
import za.co.nedbank.core.dashboard.DashboardCardType;

public class DashboardAdapterViewHolder extends RecyclerView.ViewHolder {

    FrameLayout cardContent;


    DashboardAdapterViewHolder(final View itemView) {
        super(itemView);
        cardContent = itemView.findViewById(R.id.card_content_frame);
    }

    void bindCard(final DashboardCard model) {
        cardContent.removeAllViews();

        float elevationPixels = itemView.getContext().getResources().getDimensionPixelSize(R.dimen.dashboard_card_elevation);

        ViewCompat.setElevation(cardContent, elevationPixels);
        cardContent.addView(model.getContent());
        setCardBottomMargin(model.getCardType());
    }

    private void setCardBottomMargin(DashboardCardType cardType) {
        if (cardType.equals(DashboardCardType.MONEY_REQUESTS)) {
            ViewCompat.setElevation(cardContent, 0);
        } else {
            setMargin();
        }
    }

    private void setMargin() {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.setMargins(0, 0, 0, 0);
        cardContent.setLayoutParams(params);
        ViewCompat.setElevation(cardContent, 0);
    }
}
