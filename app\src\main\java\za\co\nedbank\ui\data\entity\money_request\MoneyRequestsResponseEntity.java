/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.entity.money_request;

import com.squareup.moshi.Json;

/**
 * Created by swapnil.gawande on 2/23/2018.
 */

public class MoneyRequestsResponseEntity {

    @Json(name = "initiatorUserId")
    private long initiatorUserId;
    @Json(name = "payMeReference")
    private String payMeReference;
    @Json(name = "isReminder")
    private boolean isReminder;
    @Json(name = "initiatorDescription")
    private String initiatorDescription;
    @<PERSON>son(name = "notificationDescription")
    private String notificationDescription;
    @<PERSON><PERSON>(name = "isPayLater")
    private boolean isPayLater;
    @<PERSON><PERSON>(name = "paymentRequestId")
    private long paymentRequestId;
    @Json(name = "isPaid")
    private boolean isPaid;
    @Json(name = "isReject")
    private boolean isReject;
    @<PERSON><PERSON>(name = "initiatorAcctType")
    private String initiatorAcctType;
    @Json(name = "notificationMethod")
    private String notificationMethod;
    @Json(name = "requestDate")
    private String requestDate;
    @Json(name = "partyName")
    private String partyName;
    @Json(name = "requestAmount")
    private double requestAmount;
    @Json(name = "requestCaptureDate")
    private String requestCaptureDate;
    @Json(name = "partyPhoneNumber")
    private String partyPhoneNumber;
    @Json(name = "partyDescription")
    private String partyDescription;
    @Json(name = "initiatorAcctNumber")
    private String initiatorAcctNumber;
    @Json(name = "requestStatus")
    private String requestStatus;
    @Json(name = "partyAcctType")
    private String partyAccountType;
    @Json(name = "partyAcctNumber")
    private String partyAccountNumber;
    @Json(name = "expiryDate")
    private String expiryDate;
    @Json(name = "currentDate")
    private String currentDate;
    @Json(name = "processDate")
    private String processDate;

    public String getExpiryDate() {
        return expiryDate;
    }

    public String getCurrentDate() {
        return currentDate;
    }

    public String getProcessDate() {
        return processDate;
    }

    public String getPayMeReference() {
        return payMeReference;
    }

    public boolean isReminder() {
        return isReminder;
    }

    public String getInitiatorDescription() {
        return initiatorDescription;
    }

    public String getNotificationDescription() {
        return notificationDescription;
    }

    public boolean isPayLater() {
        return isPayLater;
    }

    public long getPaymentRequestId() {
        return paymentRequestId;
    }

    public boolean isPaid() {
        return isPaid;
    }

    public boolean isReject() {
        return isReject;
    }

    public String getInitiatorAcctType() {
        return initiatorAcctType;
    }

    public String getNotificationMethod() {
        return notificationMethod;
    }

    public String getRequestDate() {
        return requestDate;
    }

    public String getPartyName() {
        return partyName;
    }

    public double getRequestAmount() {
        return requestAmount;
    }

    public String getRequestCaptureDate() {
        return requestCaptureDate;
    }

    public String getPartyPhoneNumber() {
        return partyPhoneNumber;
    }

    public String getPartyDescription() {
        return partyDescription;
    }

    public String getInitiatorAcctNumber() {
        return initiatorAcctNumber;
    }

    public String getRequestStatus() {
        return requestStatus;
    }

    public String getPartyAccountType() {
        return partyAccountType;
    }

    public String getPartyAccountNumber() {
        return partyAccountNumber;
    }
}