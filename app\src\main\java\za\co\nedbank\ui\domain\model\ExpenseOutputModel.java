/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.model;

import java.util.Map;

/**
 * Created by piyushgupta01 on 7/20/2017.
 */

public class ExpenseOutputModel {
    private final Map<String, Float> mData;
    private final Float mValuesSum;

    public ExpenseOutputModel(Map<String, Float> stringFloatMap, float sum) {
        this.mData = stringFloatMap;
        this.mValuesSum = sum;
    }

    public Map<String, Float> getData() {
        return mData;
    }

    public Float getValuesSum() {
        return mValuesSum;
    }

}
