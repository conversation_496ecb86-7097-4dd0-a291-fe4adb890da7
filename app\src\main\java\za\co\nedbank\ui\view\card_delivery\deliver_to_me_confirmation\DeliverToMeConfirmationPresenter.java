package za.co.nedbank.ui.view.card_delivery.deliver_to_me_confirmation;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.card_delivery.PostCardDeliveryOptionRequestData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.card_delivery.PostCardDeliveryOptionUseCase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.card_delivery.CardDeliveryOptionsEnum;
import za.co.nedbank.services.domain.usecase.cards.ReplaceCardUseCase;
import za.co.nedbank.services.view.mapper.ReplaceCardViewModelToDataMapper;
import za.co.nedbank.services.view.model.ReplaceCardViewModel;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConfirmationBasePresenter;

public class DeliverToMeConfirmationPresenter extends CardDeliveryConfirmationBasePresenter<DeliverToMeConfirmationView> {

    @Inject
    public DeliverToMeConfirmationPresenter(PostCardDeliveryOptionUseCase postCardDeliveryOptionUseCase,
                                            ReplaceCardUseCase replaceCardUseCase,
                                            ReplaceCardViewModelToDataMapper replaceCardViewModelToDataMapper,
                                            NavigationRouter navigationRouter,
                                            @Named("memory") ApplicationStorage applicationStorage,
                                            Analytics analytics,
                                            ErrorHandler errorHandler) {
        super(postCardDeliveryOptionUseCase, replaceCardUseCase, replaceCardViewModelToDataMapper,
                navigationRouter, applicationStorage, analytics, errorHandler);
    }

    @Override
    public ReplaceCardViewModel createReplaceCardRequestEntity() {
        return new ReplaceCardViewModel(view.getCardPlasticId(), Constants.COURIER_CODE);
    }

    @Override
    public PostCardDeliveryOptionRequestData createPostDeliveryOptionRequestEntity() {
        PostCardDeliveryOptionRequestData requestData = new PostCardDeliveryOptionRequestData();
        requestData.setBranchCode(Constants.COURIER_CODE);
        requestData.setDeliveryOptionCode(CardDeliveryOptionsEnum.DELIVERY.getValue());
        String sessionId = applicationStorage.getString(StorageKeys.FICA_SESSION_ID, StringUtils.EMPTY_STRING);
        requestData.setSessionId(sessionId);
        return requestData;
    }

    public void callConfirmationAPI() {
        handleConfirmation();
    }

    public void sendPageAnalytics() {
        sendPageAnalytics(CardDeliveryAnalytics.VAL_ADDRESS_DETAILS_CONFIRMED, CardDeliveryAnalytics.VAL_DELIVERY_VIA_COURIER);
    }
}
