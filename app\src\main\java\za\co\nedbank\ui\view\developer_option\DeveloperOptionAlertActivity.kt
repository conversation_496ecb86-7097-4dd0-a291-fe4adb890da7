package za.co.nedbank.ui.view.developer_option

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.View
import za.co.nedbank.R
import za.co.nedbank.core.base.NBBaseActivity
import za.co.nedbank.core.base.NBBaseView
import za.co.nedbank.core.utils.DeviceUtils
import za.co.nedbank.ui.di.AppDI
import javax.inject.Inject

class DeveloperOptionAlertActivity : NBBaseActivity(), NBBaseView {


    val OPEN_DEVELOPER_SETTINGS = 11009;
    @Inject
    lateinit var presenter: DeveloperOptionAlertPresenter

    override fun onCreate(savedInstanceState: Bundle?) {
        AppDI.getActivityComponent(this).inject(this)
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_developer_option_alert)
        DeviceUtils.develop_option_uncheck  = false
        presenter.bind(this)
        findViewById<View>(R.id.btn_not_now).setOnClickListener {
            presenter.closeActivity()
        }
        findViewById<View>(R.id.btn_setting).setOnClickListener {
            startActivityForResult(Intent(Settings.ACTION_APPLICATION_DEVELOPMENT_SETTINGS), OPEN_DEVELOPER_SETTINGS)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if(requestCode == OPEN_DEVELOPER_SETTINGS){
            if(DeviceUtils.isDeveloperOptionsEnabled(this)){
                if(!DeviceUtils.isAlwaysFinishActivitiesOptionEnabledInDeveloperOption(this)){
                    setResult(RESULT_OK)
                    presenter.closeActivity()
                }
            }else{
                setResult(RESULT_OK)
                presenter.closeActivity()
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.unbind()
    }
}