package za.co.nedbank.ui.view.card_delivery.locker_confirmation;

import za.co.nedbank.ui.view.card_delivery.CardDeliveryConfirmationBaseView;

public interface CardDeliveryLockerConfirmationView extends CardDeliveryConfirmationBaseView {

    String getLockerBranchCode();

    void updateLockerDetailUI();

    String getCardPlasticId();

    void showRetryScreen();

    String getLockerName();

    void updateRetryCountAccessibility();
}
