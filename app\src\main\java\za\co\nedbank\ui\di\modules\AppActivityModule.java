/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.di.modules;

import android.app.Activity;
import android.content.Context;
import android.location.LocationManager;

import dagger.Module;
import dagger.Provides;
import za.co.nedbank.core.data.mapper.AccountsEntityToDataMapper;
import za.co.nedbank.core.di.scopes.ActivityScope;
import za.co.nedbank.core.domain.repository.accounts.AccountsRepository;
import za.co.nedbank.core.domain.repository.accounts.IAccountsRepository;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.payment.common.data.cache.beneficiary.BankBeneficiaryCache;
import za.co.nedbank.payment.common.data.cache.beneficiary.BankBeneficiaryCacheImpl;
import za.co.nedbank.payment.common.data.cache.beneficiary.InternationalRecipientListCache;
import za.co.nedbank.payment.common.data.cache.beneficiary.InternationalRecipientListImpl;
import za.co.nedbank.payment.common.data.cache.beneficiary.UserBeneficiaryCache;
import za.co.nedbank.payment.common.data.cache.beneficiary.UserBeneficiaryCacheImpl;
import za.co.nedbank.payment.common.data.datastore.beneficiary.BankBeneficiaryDataStoreFactory;
import za.co.nedbank.payment.common.data.datastore.beneficiary.InternationalRecipientDataStoreFactory;
import za.co.nedbank.payment.common.data.datastore.beneficiary.UserBeneficiaryDataStoreFactory;
import za.co.nedbank.payment.common.data.mapper.BankBeneficiaryEntityToDataMapper;
import za.co.nedbank.payment.common.data.mapper.UserBeneficiaryEntityToDataMapper;
import za.co.nedbank.payment.common.data.repository.BankBeneficiaryListRepository;
import za.co.nedbank.payment.common.data.repository.InternationalRecipientRepository;
import za.co.nedbank.payment.common.data.repository.UserBeneficiaryListRepository;
import za.co.nedbank.payment.common.domain.data.mapper.InternationalRecipientResponseEntityToDataMapper;
import za.co.nedbank.payment.common.domain.repository.IBankBeneficiaryListRepository;
import za.co.nedbank.payment.common.domain.repository.IInternationalRecipientRepository;
import za.co.nedbank.payment.common.domain.repository.IUserBeneficiaryListRepository;
import za.co.nedbank.payment.internationalpayment.data.cache.BICCodeListCache;
import za.co.nedbank.payment.internationalpayment.data.cache.BICCodeListCacheImpl;
import za.co.nedbank.payment.internationalpayment.data.datastore.BICCodeListDataStoreFactory;
import za.co.nedbank.payment.internationalpayment.data.mapper.BICCodeEntityToDataMapper;
import za.co.nedbank.payment.internationalpayment.data.repository.InternationalPaymentRepository;
import za.co.nedbank.payment.internationalpayment.domain.repository.IPaymentRepository;
import za.co.nedbank.payment.opennewinvaccount.data.repository.NBOpenNewInvAccRepository;
import za.co.nedbank.payment.opennewinvaccount.domain.repositories.IOpenNewInvAccRepository;
import za.co.nedbank.payment.vas.common.data.mapper.request.purchase.PurchaseRequestDataToEntityMapper;
import za.co.nedbank.payment.vas.common.data.mapper.request.tag.TagIdentifierRequestDataToEntityMapper;
import za.co.nedbank.payment.vas.common.data.mapper.response.purchase.PurchaseResponseEntityToDataMapper;
import za.co.nedbank.payment.vas.common.data.mapper.response.tag.TagEntityToDataMapper;
import za.co.nedbank.payment.vas.common.data.repository.VasRepository;
import za.co.nedbank.payment.vas.common.domain.repository.IVasRepository;
import za.co.nedbank.services.data.account_management.repository.AccountManagementRepository;
import za.co.nedbank.services.domain.repository.IAccountManagementRepository;
import za.co.nedbank.services.view.mapper.ReplaceCardViewModelToDataMapper;
import za.co.nedbank.ui.data.link_finance.NBLinkAccountInfoRepository;
import za.co.nedbank.ui.domain.repository.ILinkAccountInfoRepository;
import za.co.nedbank.ui.domain.repository.IMoneyRequestRepository;
import za.co.nedbank.ui.domain.repository.IMoneyRequestsActionRepository;
import za.co.nedbank.ui.domain.repository.IValidateNumberRepository;
import za.co.nedbank.ui.domain.repository.money_request.MoneyRequestRepository;
import za.co.nedbank.ui.domain.repository.money_request.MoneyRequestsActionRepository;
import za.co.nedbank.ui.domain.repository.money_request.ValidateNumberRepository;

@Module
public class AppActivityModule {

    private final Activity activity;

    public AppActivityModule(final Activity activity) {
        this.activity = activity;
    }

    @Provides
    @ActivityScope
    public ILinkAccountInfoRepository provideNBLinkAccountInfoRepository(Context context) {
        return new NBLinkAccountInfoRepository(context);
    }

    @Provides
    @ActivityScope
    IVasRepository provideVasRepository(final NetworkClient networkClient,
                                        final TagIdentifierRequestDataToEntityMapper tagIdentifierRequestDataToEntityMapper,
                                        final TagEntityToDataMapper tagEntityToDataMapper,
                                        final PurchaseResponseEntityToDataMapper purchaseResponseEntityToDataMapper,
                                        final PurchaseRequestDataToEntityMapper purchaseRequestDataToEntityMapper) {
        return new VasRepository(networkClient,
                tagIdentifierRequestDataToEntityMapper,
                tagEntityToDataMapper,
                purchaseResponseEntityToDataMapper,
                purchaseRequestDataToEntityMapper);
    }

    @Provides
    @ActivityScope
    public IUserBeneficiaryListRepository provideBeneficiaryRepository(UserBeneficiaryDataStoreFactory userBeneficiaryDataStoreFactory, UserBeneficiaryEntityToDataMapper userBeneficiaryEntityToDataMapper) {
        return new UserBeneficiaryListRepository(userBeneficiaryEntityToDataMapper, userBeneficiaryDataStoreFactory);
    }

    @Provides
    @ActivityScope
    public UserBeneficiaryCache provideUserBeneficiaryCache() {
        return new UserBeneficiaryCacheImpl();
    }

    @Provides
    @ActivityScope
    public IMoneyRequestsActionRepository provideMoneyRequestsActionRepository(final MoneyRequestsActionRepository moneyRequestsActionRepository) {
        return moneyRequestsActionRepository;
    }

    @Provides
    @ActivityScope
    public IMoneyRequestRepository provideMoneyRequestRepository(final MoneyRequestRepository moneyRequestRepository) {
        return moneyRequestRepository;
    }

    @Provides
    @ActivityScope
    public IValidateNumberRepository provideValidateNumberRepository(final ValidateNumberRepository validateNumberRepository) {
        return validateNumberRepository;
    }

    @Provides
    @ActivityScope
    IAccountManagementRepository provideAccountManagementRepository(final AccountManagementRepository accountManagementRepository) {
        return accountManagementRepository;
    }

    @Provides
    @ActivityScope
    IInternationalRecipientRepository provideInternationalBenListRepository(NetworkClient networkClient, InternationalRecipientResponseEntityToDataMapper recipientEntityToDataMapper, InternationalRecipientDataStoreFactory dataStoreFactory) {
        return new InternationalRecipientRepository(networkClient, recipientEntityToDataMapper, dataStoreFactory);
    }

    @Provides
    @ActivityScope
    public IBankBeneficiaryListRepository provideBankBeneficiaryRepository(BankBeneficiaryDataStoreFactory dataStoreFactory, BankBeneficiaryEntityToDataMapper mapper) {
        return new BankBeneficiaryListRepository(dataStoreFactory, mapper);
    }

    @Provides
    @ActivityScope
    public InternationalRecipientListCache provideInternationalUserBeneficiaryCache() {
        return new InternationalRecipientListImpl();
    }

    @Provides
    @ActivityScope
    public BankBeneficiaryCache provideBankBeneficiaryCache() {
        return new BankBeneficiaryCacheImpl();
    }

    @Provides
    @ActivityScope
    public IAccountsRepository provideAccountsRepository(final NetworkClient networkClient) {
        return new AccountsRepository(networkClient);
    }

    @Provides
    @ActivityScope
    IOpenNewInvAccRepository providesOpenNewInvAccRepository(final AccountsEntityToDataMapper accountsEntityToDataMapper, final NetworkClient networkClient) {
        return new NBOpenNewInvAccRepository(accountsEntityToDataMapper, networkClient);
    }

    @Provides
    LocationManager provideLocationManager(Activity activity) {
        return (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
    }

    @Provides
    ReplaceCardViewModelToDataMapper provideReplaceCardViewModelToDataMapper() {
        return new ReplaceCardViewModelToDataMapper();
    }

    @Provides
    @ActivityScope
    IPaymentRepository providesInternationalPaymentRepository(final NetworkClient networkClient,
                                                              final BICCodeListDataStoreFactory bicCodeListDataStoreFactory,
                                                              final BICCodeEntityToDataMapper bicCodeEntityToDataMapper) {
        return new InternationalPaymentRepository(networkClient, bicCodeListDataStoreFactory, bicCodeEntityToDataMapper);
    }

    @Provides
    @ActivityScope
    BICCodeListCache provideBICCodeCache() {
        return new BICCodeListCacheImpl();
    }
}
