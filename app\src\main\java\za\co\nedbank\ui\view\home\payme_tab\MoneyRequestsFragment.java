/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.payme_tab;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.Map;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.databinding.CardWidgetMoneyRequestsBinding;
import za.co.nedbank.payment.common.model.user_contact.UserContactViewModel;
import za.co.nedbank.payment.common.view.IPayChildScreenTypes;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.domain.model.money_request.NotificationDataModel;
import za.co.nedbank.uisdk.component.CompatEditText;
import za.co.nedbank.uisdk.validation.ValidatableInput;

public class MoneyRequestsFragment extends NBBaseFragment implements PayMoneyRequestView {

    @Inject
    MoneyRequestsFragmentPresenter mMoneyRequestsFragmentPresenter;
    private CardWidgetMoneyRequestsBinding binding;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = CardWidgetMoneyRequestsBinding.inflate(inflater, container, false);
        binding.btnNext.setOnClickListener(v -> onNextButtonClick());
        binding.tvViewMoneyRequest.setOnClickListener(v -> onViewMoneyRequestClick());
        binding.recipientIv.setOnClickListener(v -> onRecipientIconClick());
        return binding.getRoot();
    }

    @Override
    public void onCreate(@Nullable final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        setupForKeyboardDismiss(binding.recipientNameEt, getNBActivity());
        setNextButtonEnabled(false);
        addListenerToRecipientName(binding.recipientNameEt);
        addListenerToRecipientPhone(binding.recipientPhoneEt);

    }

    @Override
    public void setNextButtonEnabled(boolean enabled) {
        binding.btnNext.setEnabled(enabled);
    }

    @SuppressLint("ClickableViewAccessibility")
    public void setupForKeyboardDismiss(View view, final Activity activity) {
        if (!(view instanceof EditText)) {
            view.setOnTouchListener((View v, MotionEvent event) -> {
                ViewUtils.hideSoftKeyboard(activity, v);
                return false;
            });
        }

        if (view instanceof ViewGroup) {
            for (int i = za.co.nedbank.core.Constants.ZERO; i < ((ViewGroup) view).getChildCount(); i++) {
                View innerView = ((ViewGroup) view).getChildAt(i);
                setupForKeyboardDismiss(innerView, activity);
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        mMoneyRequestsFragmentPresenter.unbind();
    }

    @Override
    public void onResume() {
        super.onResume();
        mMoneyRequestsFragmentPresenter.bind(this);
        getNotifications();
    }


    void onViewMoneyRequestClick() {
        mMoneyRequestsFragmentPresenter.navigateToViewMoneyRequests();
    }

    @Override
    public void showError(String error) {
        ViewUtils.hideViews(binding.tvNotificationCount);
    }

    @SuppressLint("DefaultLocale")
    @Override
    public void showNotifications(NotificationDataModel notificationDataModel) {

        if (notificationDataModel.getNotificationCounter() > za.co.nedbank.core.Constants.ZERO) {
            ViewUtils.showViews(binding.tvNotificationCount);
            binding.tvNotificationCount.setText(String.format("%d%s", notificationDataModel.getNotificationCounter(), StringUtils.EMPTY_STRING));
        } else {
            ViewUtils.hideViews(binding.tvNotificationCount);
        }
    }

    private void getNotifications() {
        mMoneyRequestsFragmentPresenter.getNotifications();
    }


    public void onRecipientIconClick() {
        mMoneyRequestsFragmentPresenter.handleRecipientIconClick();
    }

    public void addListenerToRecipientName(CompatEditText compatEdtRecipientName){

        RxTextView.textChanges(compatEdtRecipientName.getInputField()).subscribe(chars -> {
            if (compatEdtRecipientName.hasError()) compatEdtRecipientName.clearErrors();
            compatEdtRecipientName.clearErrors();
            mMoneyRequestsFragmentPresenter.handleRecipientNameTextChanged();
        });

        compatEdtRecipientName.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                validateInput(compatEdtRecipientName, Validator.ValidatorType.RECIPIENT_NAME_VALIDATOR);
                binding.recipientNameEt.clearFocus();
            }
        });

    }

    public void addListenerToRecipientPhone(CompatEditText compatEdtRecipientPhone){

        RxTextView.textChanges(compatEdtRecipientPhone.getInputField()).subscribe(chars -> {
            if (compatEdtRecipientPhone.hasError()) compatEdtRecipientPhone.clearErrors();
            compatEdtRecipientPhone.clearErrors();
            mMoneyRequestsFragmentPresenter.handleMobileNumberTextChanged();
        }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        compatEdtRecipientPhone.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                validateInput(compatEdtRecipientPhone, Validator.ValidatorType.RECIPIENT_CONTACT_VALIDATOR);
                if (TextUtils.isEmpty(binding.recipientPhoneEt.getValue())) {
                    setCountryCodeVisibility(false);
                }
                binding.recipientPhoneEt.clearFocus();
            } else {
                setCountryCodeVisibility(true);
            }
        });

        RxTextView.editorActions(compatEdtRecipientPhone.getInputField())
                .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                .subscribe(chars -> {
                    ViewUtils.hideSoftKeyboard(getNBActivity(), compatEdtRecipientPhone);
                    compatEdtRecipientPhone.clearFocus();
                    validateInput(compatEdtRecipientPhone, Validator.ValidatorType.RECIPIENT_CONTACT_VALIDATOR);
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

    }


    void validateInput(final ValidatableInput<String> input, Validator.ValidatorType validatorType) {
        setNextButtonEnabled(!(input != null && !mMoneyRequestsFragmentPresenter.validateInput(input, validatorType)));
    }

    @Override
    public void handleNextClick() {
        mMoneyRequestsFragmentPresenter.validateMobileNumber(mMoneyRequestsFragmentPresenter.getMobileNumberWithoutCountryCode(binding.recipientPhoneEt.getValue()));
    }

    @Override
    public void trackValidateFailure(String errorCode) {
        mMoneyRequestsFragmentPresenter.trackFailure(getString(R.string.money_request_failure_msg), errorCode);
    }

    @Override
    public void trackFailure(boolean isApiFailure, String tagName, String apiName, String message, String apiErrorCode) {
        mMoneyRequestsFragmentPresenter.trackFailure(isApiFailure, tagName, apiName, message, apiErrorCode);
    }

    @Override
    public void showLoadingOnButton(boolean inProgress) {
        binding.btnNext.setLoadingVisible(inProgress);
    }

    @Override
    public void setCountryCodeVisibility(boolean shouldVisible) {
        setMarginToMobileFieldContainer();
    }

    private void setMarginToMobileFieldContainer() {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.setMargins(0, 6, 0, 0);
        binding.mobileNumberContainer.setLayoutParams(params);
    }

    @Override
    public void handleEnterRecipientName() {
        passScreenInputsToPresenter();
    }

    @Override
    public void handleEnterMobileNumber() {
        passScreenInputsToPresenter();
    }

    @Override
    public void showErrorMessage(String message) {
        showError(getString(R.string.error), message);
    }

    @Override
    public void navigateToRequestDetailOnSuccess() {
        mMoneyRequestsFragmentPresenter.navigateToRequestDetailScreen();
    }

    private void passScreenInputsToPresenter() {
        mMoneyRequestsFragmentPresenter.checkInputsOnScreen(binding.recipientNameEt, binding.recipientPhoneEt);
    }

    @Override
    public String getRecipientName() {
        return binding.recipientNameEt.getValue();
    }

    @Override
    public String getRecipientMobileNumber() {
        return String.format("%s%s", StringUtils.ZERO, binding.recipientPhoneEt.getValue());
    }

    @Override
    public String getRecipientNumberWithoutZero(String recipientContactNumber) {
        if (recipientContactNumber != null) {
            if (recipientContactNumber.startsWith(StringUtils.ZERO)) {
                recipientContactNumber = recipientContactNumber.substring(1);
                return recipientContactNumber;
            } else if (recipientContactNumber.startsWith(getString(R.string.sa_country_code))) {
                recipientContactNumber = recipientContactNumber.substring(3);
                if (recipientContactNumber.startsWith(StringUtils.ZERO)) {
                    recipientContactNumber = recipientContactNumber.substring(1);
                }
                return recipientContactNumber;
            } else if (recipientContactNumber.startsWith(getString(R.string.sa_country_code_with_plus_prefix))) {
                recipientContactNumber = recipientContactNumber.substring(5);
            } else if (recipientContactNumber.startsWith(getString(R.string.sa_country_code_with_zero_prefix))) {
                recipientContactNumber = recipientContactNumber.substring(4);
            }
        }
        return recipientContactNumber;
    }


    @Override
    public void setResult(Map<String, Object> params) {
        readResult(params);
    }

    private void readResult(Map<String, Object> resultMap) {
        if (null != resultMap && resultMap.size() > za.co.nedbank.core.Constants.ZERO) {
            int screenType = Constants.NO_ITEM_SELECTED;
            Object screenTypeObject = resultMap.get(Constants.EXTRAS.SCREEN_TYPE);
            if (screenTypeObject instanceof Integer) {
                screenType = (Integer) screenTypeObject;
            }
            Object objectReceived;
            if (screenType == IPayChildScreenTypes.USER_CONTACT_SCREEN) {
                objectReceived = resultMap.get(PayNavigatorTarget.EXTRAS.CONTACT_SELECTED);
                if (objectReceived instanceof UserContactViewModel) {
                    UserContactViewModel userContactViewModel = (UserContactViewModel) objectReceived;
                    binding.recipientNameEt.setText(userContactViewModel.getContactName());
                    binding.recipientPhoneEt.requestFocus();
                    binding.recipientPhoneEt.setText(getRecipientNumberWithoutZero(userContactViewModel.getPhoneNumber()));
                    binding.recipientPhoneEt.clearFocus();
                    setCountryCodeVisibility(!TextUtils.isEmpty(userContactViewModel.getPhoneNumber()));
                }
            }
        }
    }

    public void onNextButtonClick() {
        showLoadingOnButton(true);
        mMoneyRequestsFragmentPresenter.handleNextClick();
    }

}

