package za.co.nedbank.ui.view.card_delivery.branch_confirmation;

import za.co.nedbank.ui.view.card_delivery.CardDeliveryConfirmationBaseView;

public interface CardDeliveryBranchConfirmationView extends CardDeliveryConfirmationBaseView {

    void updateBranchNameAddress(String branchName, String branchAddress);

    void showLoading(boolean isLoading);

    String getCardPlasticId();

    String getDefaultBranchId();

}
