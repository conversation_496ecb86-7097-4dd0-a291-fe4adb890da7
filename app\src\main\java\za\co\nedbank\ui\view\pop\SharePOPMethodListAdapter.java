/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.pop;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import za.co.nedbank.databinding.RowSharePopMethodBinding;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/2019.
 */

class SharePOPMethodListAdapter extends RecyclerView.Adapter {
    private final String mSelectedSharePOPMethod;
    private final List<String> mSharePOPMethodList;
    private final IItemClickCallback itemClickCallback;

    SharePOPMethodListAdapter(String selectedSharePOPMethod, List<String> sharePOPMethodList, IItemClickCallback itemClickCallback) {
        this.mSelectedSharePOPMethod = selectedSharePOPMethod;
        this.mSharePOPMethodList = sharePOPMethodList;
        this.itemClickCallback = itemClickCallback;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        RowSharePopMethodBinding binding = RowSharePopMethodBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new SharePOPMethodViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof SharePOPMethodViewHolder) {
            SharePOPMethodViewHolder accountTypeViewHolder = (SharePOPMethodViewHolder) holder;
            boolean isSelectedAccount = mSharePOPMethodList.get(position).equalsIgnoreCase(mSelectedSharePOPMethod);
            accountTypeViewHolder.mIvTick.setVisibility(isSelectedAccount ? View.VISIBLE : View.INVISIBLE);
            accountTypeViewHolder.mTcSharePOPMethodName.setText(mSharePOPMethodList.get(position));
            accountTypeViewHolder.position = holder.getAdapterPosition();
        }
    }

    @Override
    public int getItemCount() {
        return mSharePOPMethodList == null ? 0 : mSharePOPMethodList.size();
    }

    interface IItemClickCallback {
        void onItemClick(int position, String sharePOPMethod);
    }

    class SharePOPMethodViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        ImageView mIvTick;
        TextView mTcSharePOPMethodName;
        int position;

        SharePOPMethodViewHolder(RowSharePopMethodBinding binding) {
            super(binding.getRoot());
            this.mIvTick = binding.ivTick;
            this.mTcSharePOPMethodName = binding.tvSharePopMethodName;
            binding.getRoot().setOnClickListener(this);
        }

        @Override
        public void onClick(View v) {
            if (null != itemClickCallback) {
                itemClickCallback.onItemClick(position, mSharePOPMethodList.get(position));
            }
        }
    }
}
