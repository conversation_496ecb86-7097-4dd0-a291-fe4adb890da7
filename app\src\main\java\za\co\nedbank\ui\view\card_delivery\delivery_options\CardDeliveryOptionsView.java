package za.co.nedbank.ui.view.card_delivery.delivery_options;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.enroll_v2.view.model.card_delivery.CardDeliveryOptionsViewModel;

public interface CardDeliveryOptionsView extends NBBaseView {

    String getFlow();
    String getCardName();

    void updateOptions(List<CardDeliveryOptionsViewModel> options);
    void showProgress(boolean isVisible);

    void hideContent();

    boolean isCardReplaceFeeApplicable();

    void updateFeeInfo(boolean isFeeApplicable, boolean isCardDeliveryFreeProduct);

    String getUserError();

    void onSetBackResult();

    String getCardActionName();

}
