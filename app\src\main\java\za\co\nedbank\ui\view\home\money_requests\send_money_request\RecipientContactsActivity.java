/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;

import com.jakewharton.rxbinding2.widget.RxTextView;

import org.zakariya.stickyheaders.StickyHeaderLayoutManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.IActivityComListener;
import za.co.nedbank.core.view.IFragmentToActivityComListener;
import za.co.nedbank.databinding.ActivityUserContactsBinding;
import za.co.nedbank.payment.buy.view.mapper.ContactToSectionViewModelMapper;
import za.co.nedbank.payment.common.listener.ISearchNameInputListener;
import za.co.nedbank.payment.common.listener.ITabbedPagerFragmentSnackbarActionListener;
import za.co.nedbank.payment.common.model.user_contact.UserContactViewModel;
import za.co.nedbank.payment.common.view.IPayChildScreenTypes;
import za.co.nedbank.payment.common.view.IRecyclerviewFragmentView;
import za.co.nedbank.payment.common.view.SearchListAdapter;
import za.co.nedbank.payment.common.view.model.SectionAdapterViewModel;
import za.co.nedbank.payment.common.view.tracking.PaymentsTrackingEvent;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.component.CompatSearch;
import za.co.nedbank.uisdk.component.listener.IAlphabetsBarItemSelectionListener;
import za.co.nedbank.uisdk.widget.NBSnackbar;


public class RecipientContactsActivity extends NBBaseActivity implements UserContactsView,
        ISearchNameInputListener, IRecyclerviewFragmentView,
        ITabbedPagerFragmentSnackbarActionListener,
        IFragmentToActivityComListener, IActivityComListener,
        IAlphabetsBarItemSelectionListener {

    private static final String INITIAL_SEARCH_QUERY = StringUtils.EMPTY_STRING;
    protected boolean mShowSearchFieldInList = true;
    @Inject
    RecipientContactsPresenter mPresenter;
    protected boolean mFragmentDisplayed;
    protected SearchListAdapter mSearchListAdapter;
    private Map<String, List<UserContactViewModel>> mUserContactViewModelMap;
    private int mViewType;
    private boolean mIsSearchBoxClicked;
    private String mSearchInput = StringUtils.EMPTY_STRING;
    private ActivityUserContactsBinding binding;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityUserContactsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        binding.toolbar.setTitle(R.string.select_a_contact);
        initToolbar(binding.toolbar, true, true);
        AppDI.getActivityComponent(this).inject(this);
        if (getIntent() != null && getIntent().getExtras() != null && getIntent().getExtras().containsKey(Constants.BundleKeys.VIEW_PAGER_WTH_TAB_VIEW)) {
            mViewType = getIntent().getIntExtra(Constants.BundleKeys.VIEW_PAGER_WTH_TAB_VIEW
                    , Constants.ISearchListViewType.VIEW_TYPE_BUY);
        }
        setUpRecyclerView();
        addListenerForCategorySearchInput(binding.etCategorySearchInput);
        binding.allowButton.setOnClickListener(v -> mPresenter.handleAllowButtonClicked());
    }

    void addListenerForCategorySearchInput(CompatSearch compatEdtCategorySearchInput) {
        RxTextView.textChanges(compatEdtCategorySearchInput.getInputField()).subscribe(chars -> mPresenter.handleSearchField(compatEdtCategorySearchInput.getValue().trim()), throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }


    @Override
    public void onResume() {
        super.onResume();
        mPresenter.bind(this);
        mPresenter.checkContactReadPermissionGranted();
        mPresenter.getUserContacts(StringUtils.EMPTY_STRING);
    }


    @Override
    public void onPause() {
        super.onPause();
        mPresenter.unbind();
    }

    @Override
    public void showError(String errorMessage) {
        NBSnackbar.instance().action(getString(za.co.nedbank.payment.R.string.snackbar_action_retry), this::dismissSnack).build(binding.allowContactAccessRl, errorMessage);
    }

    private void dismissSnack() {
        NBSnackbar.instance().dismissSnackBar();
    }

    @Override
    public void showContacts(Map<String, List<UserContactViewModel>> userContactViewModelMap) {
        if (null != userContactViewModelMap && userContactViewModelMap.size() > 0) {
            setVisibilityOnEmptyStateView(false);
            setCategorySearchInputLayoutVisibility();
            mUserContactViewModelMap = userContactViewModelMap;
            List<SectionAdapterViewModel> sectionAdapterViewModels = ContactToSectionViewModelMapper.mapData(userContactViewModelMap);
            mSearchListAdapter = new SearchListAdapter(sectionAdapterViewModels,
                    showActivityRecipientName -> {
                        mShowSearchFieldInList = false;
                        onEvent(IActivityComListener.EVENT_CONSTANTS.EVENT_SEARCH_BOX_CLICKED);
                    },
                    (sectionPosition, childPosition) -> mPresenter.onContactSelected(getSelectedContact(sectionPosition, childPosition)),
                    getString(R.string.search_for_a_recipient), this);
            mSearchListAdapter.setViewType(mViewType);
            mSearchListAdapter.setShowEditTextHeader(false);
            binding.layoutRecyclerViewWithProgressbar.recyclerview.setAdapter(mSearchListAdapter);
            if (TextUtils.isEmpty(mSearchInput.trim())) {
                setVisibilityOnAlphabetsBar(true);
                binding.layoutRecyclerViewWithProgressbar.alphabetsVerticalBar.setEnabledCharacters(mSearchListAdapter.buildEnabledItemsList());
                binding.layoutRecyclerViewWithProgressbar.alphabetsVerticalBar.setAlphabetSelected(mSearchListAdapter.getSectionForPosition(0));
            } else {
                setVisibilityOnAlphabetsBar(false);
            }
        } else {
            setEmptyStateMessage(getString(R.string.no_results_found));
            setEmptyStateIcon(R.drawable.ic_empty_bank_list);
            setVisibilityOnEmptyStateView(true);
            setEmptyStateKidsMessage(mPresenter.isKidsProfile());
        }
    }

    protected void setVisibilityOnAlphabetsBar(boolean visible) {
        if (visible) {
            ViewUtils.showViews(binding.layoutRecyclerViewWithProgressbar.alphabetsVerticalBar);
        } else {
            ViewUtils.hideViews(binding.layoutRecyclerViewWithProgressbar.alphabetsVerticalBar);
        }
    }

    @Override
    public void showPermissionNotProvided() {
        onEvent(IActivityComListener.EVENT_CONSTANTS.EVENT_READ_CONTACT_NOT_PERMITTED);
        ViewUtils.hideViews(binding.layoutRecyclerViewWithProgressbar.recyclerview, binding.layoutRecyclerViewWithProgressbar.pbRecyclerViewLoading);
        ViewUtils.showViews(binding.allowContactAccessRl);
        binding.allowButton.setText(getString(R.string.allow));
    }

    @Override
    public void showPermissionDeniedView() {
        ViewUtils.showViews(binding.allowContactAccessRl);
        ViewUtils.hideViews(binding.layoutRecyclerViewWithProgressbar.recyclerview);
        binding.allowButton.setText(getString(R.string.open_settings));
    }

    @Override
    public void showPermissionProvidedView() {
        onEvent(IActivityComListener.EVENT_CONSTANTS.EVENT_READ_CONTACT_PERMITTED);
        ViewUtils.showViews(binding.layoutRecyclerViewWithProgressbar.recyclerview);
        ViewUtils.hideViews(binding.allowContactAccessRl);
        mPresenter.getUserContacts(INITIAL_SEARCH_QUERY);
    }

    @Override
    public void handleSearchInput(String searchText) {
        receiveSearchInput(searchText);
    }

    @Override
    public void setActivityResult(UserContactViewModel selectedContact) {
        Bundle bundle = new Bundle();
        bundle.putInt(Constants.EXTRAS.SCREEN_TYPE, IPayChildScreenTypes.USER_CONTACT_SCREEN);
        bundle.putParcelable(PayNavigatorTarget.EXTRAS.CONTACT_SELECTED, selectedContact);
        onEvent(IActivityComListener.EVENT_CONSTANTS.EVENT_CONTACT_SELECTED, bundle);
    }

    @Override
    public void onEvent(int event) {
        switch (event) {
            case IActivityComListener.EVENT_CONSTANTS.EVENT_SEARCH_BOX_CLICKED:
                mIsSearchBoxClicked = true;
                setCategorySearchInputLayoutVisibility();
                break;
            case IActivityComListener.EVENT_CONSTANTS.EVENT_READ_CONTACT_NOT_PERMITTED:
                ViewUtils.hideViews(binding.etCategorySearchInput);
                break;
            case IActivityComListener.EVENT_CONSTANTS.EVENT_READ_CONTACT_PERMITTED:
                checkAndShowSearchField();
                break;
            default:
        }
    }

    private void setCategorySearchInputLayoutVisibility() {
        binding.etCategorySearchInput.setVisibility(View.VISIBLE);
        ViewUtils.showSoftKeyboard(this, binding.etCategorySearchInput);
    }

    private void checkAndShowSearchField() {
        if (mIsSearchBoxClicked) {
            ViewUtils.showViews(binding.etCategorySearchInput);
        }
    }

    @Override
    public void onEvent(int event, Bundle bundle) {
        switch (event) {
            case IActivityComListener.EVENT_CONSTANTS.EVENT_CONTACT_SELECTED:
                if (null != bundle) {
                    Intent intent = new Intent();
                    intent.putExtras(bundle);
                    setResult(Activity.RESULT_OK, intent);
                }
                finish();
                break;
            default:
        }
    }


    @Override
    public void openAppSettings() {
        if (getPackageName() != null) {
            Intent intent = new Intent();
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts(Constants.PACKAGE, getPackageName(), null);
            intent.setData(uri);
            startActivity(intent);
        }
    }

    @Override
    public void receiveSearchInput(String inputText) {
        mSearchInput = inputText;
        setVisibilityOnAlphabetsBar(TextUtils.isEmpty(inputText.trim()));
        mPresenter.getUserContacts(inputText);
    }

    @Override
    public void receiveRetryActionClick() {
        if (mFragmentDisplayed) {
            mPresenter.checkContactReadPermissionGranted();
        }
    }

    private UserContactViewModel getSelectedContact(int sectionPosition, int childPosition) {
        int index = 0;
        for (Map.Entry<String, List<UserContactViewModel>> stringListEntry : mUserContactViewModelMap.entrySet()) {
            Map.Entry<String, List<UserContactViewModel>> pair = stringListEntry;
            if (index++ == sectionPosition) {
                ArrayList<UserContactViewModel> list = (ArrayList<UserContactViewModel>) pair.getValue();
                return list.get(childPosition);
            }
        }
        return null;
    }

    protected void setUpRecyclerView() {
        binding.layoutRecyclerViewWithProgressbar.recyclerview.setItemAnimator(null);
        binding.layoutRecyclerViewWithProgressbar.recyclerview.setHasFixedSize(true);
        binding.layoutRecyclerViewWithProgressbar.recyclerview.setNestedScrollingEnabled(false);
        binding.layoutRecyclerViewWithProgressbar.recyclerview.setLayoutManager(new StickyHeaderLayoutManager());
        binding.layoutRecyclerViewWithProgressbar.alphabetsVerticalBar.setListener(this);
    }

    @Override
    public void setVisibilityOnProgressBar(boolean isVisible) {
        if (isVisible) {
            ViewUtils.showViews(binding.layoutRecyclerViewWithProgressbar.pbRecyclerViewLoading);
            ViewUtils.hideViews(binding.layoutRecyclerViewWithProgressbar.recyclerview);
        } else {
            ViewUtils.hideViews(binding.layoutRecyclerViewWithProgressbar.pbRecyclerViewLoading);
            ViewUtils.showViews(binding.layoutRecyclerViewWithProgressbar.recyclerview);
        }
    }

    @Override
    public void setVisibilityOnEmptyStateView(boolean isVisible) {
        if (isVisible) {
            ViewUtils.showViews(binding.layoutRecyclerViewWithProgressbar.emptyStateViewLl);
            ViewUtils.hideViews(binding.layoutRecyclerViewWithProgressbar.recyclerview);
        } else {
            ViewUtils.hideViews(binding.layoutRecyclerViewWithProgressbar.emptyStateViewLl);
            ViewUtils.showViews(binding.layoutRecyclerViewWithProgressbar.recyclerview);
        }
    }

    protected void setEmptyStateMessage(String message) {
        binding.layoutRecyclerViewWithProgressbar.emptyStateMessage.setText(message);
    }

    protected void setEmptyStateKidsMessage(boolean isKids) {
        if (binding.layoutRecyclerViewWithProgressbar.emptyStateMessageKidProfile != null && isKids){
            ViewUtils.showViews(binding.layoutRecyclerViewWithProgressbar.emptyStateMessageKidProfile);
        }
    }


    protected void setEmptyStateIcon(int emptyStateIconResource) {
        binding.layoutRecyclerViewWithProgressbar.emptyStateIv.setImageResource(emptyStateIconResource);
    }

    @Override
    public String provideTrackingEvent() {
        String trackingEvent = StringUtils.EMPTY_STRING;
        if (mViewType == Constants.ISearchListViewType.VIEW_TYPE_BUY) {
            trackingEvent = PaymentsTrackingEvent.BUY_MOBILE_FORM_RECIPIENT_LIST;
        }
        return trackingEvent;
    }

    @Override
    public void selectedCharacter(int charSelected) {
        int sectionIndex = mSearchListAdapter.getSectionForSelectedCharacter(charSelected);
        int adapterPosition = mSearchListAdapter.getAdapterPositionForSectionHeader(sectionIndex);
        binding.layoutRecyclerViewWithProgressbar.recyclerview.scrollToPosition(adapterPosition);
    }

    @Override
    public void isKidsProfile(boolean isKids) {
        if (binding.layoutRecyclerViewWithProgressbar.emptyStateMessageKidProfile != null && isKids){
            ViewUtils.showViews(binding.layoutRecyclerViewWithProgressbar.emptyStateMessageKidProfile);
        }
    }
}
