/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients.choose_recipient;

import za.co.nedbank.core.base.dialog.BaseDialogActivityInterface;

/**
 * Created by charurani on 23-08-2017.
 */

public interface IChooseRecipientDialogActivityInterface extends BaseDialogActivityInterface {
    void receiveRecipientData(ChooseRecipientsViewModel chooseRecipientsViewModel);
}
