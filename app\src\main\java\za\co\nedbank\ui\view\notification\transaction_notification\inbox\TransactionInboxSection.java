package za.co.nedbank.ui.view.notification.transaction_notification.inbox;

import za.co.nedbank.core.base.adapter.Section;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;

public class TransactionInboxSection extends Section<FBTransactionNotificationsViewModel> {

    private final long startDate;
    private final long endDate;

    public TransactionInboxSection(final String name, final long startDate, final long endDate) {
        super(name);
        this.startDate = startDate;
        this.endDate = endDate;
    }

    @Override
    public boolean fitSectionCriteria(final FBTransactionNotificationsViewModel model) {
        return model.getDate() >= startDate && model.getDate() <= endDate;
    }
}