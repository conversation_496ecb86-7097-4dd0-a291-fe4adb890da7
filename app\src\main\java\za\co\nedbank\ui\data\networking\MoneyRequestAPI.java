/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.networking;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import za.co.nedbank.ui.data.entity.money_request.NotificationsResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentRequestEntity;
import za.co.nedbank.ui.data.entity.money_request.PaymentResponseEntity;

/**
 * Created by sandip.lawate on 2/22/2018.
 */

public interface MoneyRequestAPI {
    @POST("retail/paymentrequest/v1/paymentrequests")
    Observable<PaymentResponseEntity> sendPaymentRequest(@Body PaymentRequestEntity paymentRequestEntity);

    @GET("retail/paymentrequest/v1/notifications")
    Observable<NotificationsResponseEntity> getNotifications();
}
