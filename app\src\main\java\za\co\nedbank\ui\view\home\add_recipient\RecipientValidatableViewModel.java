/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import za.co.nedbank.uisdk.validation.ValidatableInput;

/**
 * Created by priyadhingra on 3/14/2018.
 */

public class RecipientValidatableViewModel {
    private ValidatableInput<String> recipientNumber;
    private ValidatableInput<String> recipientRef;
    private ValidatableInput<String> yourRef;


    public ValidatableInput<String> getRecipientNumber() {
        return recipientNumber;
    }

    public void setRecipientNumber(ValidatableInput<String> recipientNumber) {
        this.recipientNumber = recipientNumber;
    }

    public ValidatableInput<String> getRecipientRef() {
        return recipientRef;
    }

    public void setRecipientRef(ValidatableInput<String> recipientRef) {
        this.recipientRef = recipientRef;
    }

    public ValidatableInput<String> getYourRef() {
        return yourRef;
    }

    public void setYourRef(ValidatableInput<String> yourRef) {
        this.yourRef = yourRef;
    }
}
