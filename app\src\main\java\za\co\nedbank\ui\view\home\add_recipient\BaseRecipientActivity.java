/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.jakewharton.rxbinding2.widget.RxTextView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import za.co.nedbank.R;
import za.co.nedbank.core.IChildScreenTypes;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.domain.model.response.CoreFastPayDomainResponseData;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.payment.PaymentsUtility;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.INBRecyclerViewListener;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerView;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewModel;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.validation.Validator;
import za.co.nedbank.core.view.banklist.model.BankBranchViewModel;
import za.co.nedbank.core.view.banklist.model.BankViewModel;
import za.co.nedbank.core.view.recipient.BankAccountViewDataModel;
import za.co.nedbank.core.view.recipient.EmailViewDataModel;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.payment.common.view.IPayChildScreenTypes;
import za.co.nedbank.payment.pay.domain.data.model.account_type.AccountType;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.payment.pay.view.model.SelectedBankViewModel;
import za.co.nedbank.uisdk.component.CompatButton;
import za.co.nedbank.uisdk.component.CompatEditText;
import za.co.nedbank.uisdk.validation.ValidatableInput;

/**
 * Created by priyadhingra on 9/14/2017.
 */

public abstract class BaseRecipientActivity extends NBBaseActivity implements BaseRecipientView, INBRecyclerViewListener, BankAccountAdapter.IActivityAdapterComListener, NBFlexibleItemCountRecyclerviewAdapter.IAdapterInteractionListener {

    protected LinearLayout lnrTopView;
    protected CompatEditText etRecipientName;
    protected NBFlexibleItemCountRecyclerView rvAccount;
    protected NBFlexibleItemCountRecyclerView rvMobileNumber;
    protected NBFlexibleItemCountRecyclerView rvCreditCard;
    protected NBFlexibleItemCountRecyclerView rvRppPayment;
    protected NBFlexibleItemCountRecyclerView rvElectricity;
    protected NBFlexibleItemCountRecyclerView rvShapeID;
    protected NBFlexibleItemCountRecyclerView rvEmail;
    protected CompatButton mLoadingButton;

    protected List<NbRecyclerViewBaseDataModel> mBankAccountViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mMobileNumberViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mElectricityMeterViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mCreditCardViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mShapIDViewDataModelList;
    protected List<NbRecyclerViewBaseDataModel> mEmailViewDataModelList;
    protected BankAccountAdapter mBankAccountAdapter;
    protected MobileNumberAdapter mMobileNumberAdapter;
    protected ElectricityMeterAdapter mElectricityMeterAdapter;
    protected CreditCardAdapter mCreditCardAdapter;
    protected ShapIDAdapter mShapIDAdapter;
    protected EmailAdapter mEmailAdapter;
    private int mSelectedBankItemPos = -1;
    protected RecipientViewModel mRecipientViewModel;
    private BaseRecipientPresenter mBaseRecipientPresenter;
    protected NBFlexibleItemCountRecyclerviewModel mBankAccountNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mMobileNumberNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mElectricityNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mCreditCardNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mShapIdNbFlexibleItemCountRecyclerviewModel;
    protected NBFlexibleItemCountRecyclerviewModel mEmailNbFlexibleItemCountRecyclerviewModel;
    private BankViewModel mBankViewModel;
    private String mSelectedAccountTypeCode;
    private Handler mUiHandler;
    private List<CoreFastPayDomainResponseData> fastPayDomainList = new ArrayList<>();
    private HashMap<String,String> fastPayDomainNameMap = new HashMap<>();

    protected void onCreate(@Nullable Bundle savedInstanceState, @NonNull BaseRecipientPresenter baseRecipientPresenter) {
        super.onCreate(savedInstanceState);
        setUpViews();
        this.mBaseRecipientPresenter = baseRecipientPresenter;
        mUiHandler = new Handler();
        if (etRecipientName != null){
            addListenerForRecipientName(etRecipientName);
        }

    }

    private void setUpViews() {
        lnrTopView = findViewById(R.id.ll_content_view);
        etRecipientName = findViewById(R.id.et_recipient_name);
        rvAccount = findViewById(R.id.rv_account);
        rvMobileNumber = findViewById(R.id.rv_mobile_number);
        rvCreditCard = findViewById(R.id.rv_credit_card);
        rvRppPayment = findViewById(R.id.rv_rpp_payment);
        rvElectricity = findViewById(R.id.rv_electricity);
        rvShapeID = findViewById(R.id.rv_shapid);
        rvEmail = findViewById(R.id.rv_email);
        mLoadingButton = findViewById(R.id.update_add_recipient_loading_button);
        if (mLoadingButton != null) {
            mLoadingButton.setOnClickListener(v -> onAddButtonClick());
        }
    }


    @SuppressLint("CheckResult")
    public void addListenerForRecipientName(CompatEditText compatEdtRecipientName) {

        RxTextView.textChanges(compatEdtRecipientName.getInputField()).subscribe(chars -> {
            if (compatEdtRecipientName.hasError()) {
                compatEdtRecipientName.clearErrors();
                validateInput(etRecipientName, Validator.ValidatorType.RECIPIENT_NAME_CHAR_VALIDATOR);
            } else if (!chars.toString().isEmpty()) {
                validateInput(etRecipientName, Validator.ValidatorType.RECIPIENT_NAME_CHAR_VALIDATOR);
            }
            if (mRecipientViewModel != null) {
                mRecipientViewModel.setRecipientName(compatEdtRecipientName.getValue());
            }
            passScreenInputToPresenter();
        },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

        compatEdtRecipientName.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                validateInput(etRecipientName, Validator.ValidatorType.RECIPIENT_NAME_CHAR_VALIDATOR);
            }
        });

        RxTextView.editorActions(compatEdtRecipientName.getInputField())
                .filter(actionId -> actionId == EditorInfo.IME_ACTION_DONE)
                .subscribe(chars -> {
                    ViewUtils.hideSoftKeyboard(BaseRecipientActivity.this, compatEdtRecipientName);
                    compatEdtRecipientName.clearFocus();
                    validateInput(etRecipientName, Validator.ValidatorType.RECIPIENT_NAME_CHAR_VALIDATOR);
                },throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));

    }

    @Override
    public void validateInput(final ValidatableInput<String> input, Validator.ValidatorType validatorType) {
        if (input != null) {
            mBaseRecipientPresenter.validateInput(input, validatorType);
        }
    }

    protected void sendBeneficiaryListRefreshIntent() {
        Bundle bundle = new Bundle();
        bundle.putBoolean(Constants.EXTRAS.IS_BENEFICIARY_LIST_REFRESH, true);
        Intent intent = new Intent();
        intent.putExtras(bundle);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    protected void setLayoutParamOfRecyclerView(boolean isViewEditable) {
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        int marginTop = 0;
        if (!isViewEditable) {
            marginTop = (int) getResources().getDimension(R.dimen.dimen_5dp);
        }
        layoutParams.setMargins(0, marginTop, 0, marginTop);
        rvAccount.setLayoutParams(layoutParams);
        rvShapeID.setLayoutParams(layoutParams);
        rvMobileNumber.setLayoutParams(layoutParams);
        rvElectricity.setLayoutParams(layoutParams);
        rvCreditCard.setLayoutParams(layoutParams);
        rvEmail.setLayoutParams(layoutParams);
    }

    protected void setupRecyclerViews() {
        boolean isBankApprovedBeneficiary = isBankApprovedBeneficiary();
        //prepare account model
        mBankAccountNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.bank_accounts));
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_bank_account));
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_other_bank_account));
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.BANK);
        mBankAccountNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_bank_account_item_layout);
        if (isBankApprovedBeneficiary) {
            mBankAccountNbFlexibleItemCountRecyclerviewModel.setFooterShow(false);
        } else {

            //prepare mobile number model
            mMobileNumberNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.mobile_num));
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_mobile_number));
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_other_mobile_number));
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.MOBILE_NUMBER);
            mMobileNumberNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_mobile_number_item_layout);

            //prepare credit card model
            mCreditCardNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.credit_card));
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_credit_card_number));
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_credit_card_number));
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.CREDIT_CARD);
            mCreditCardNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_credit_card_item_layout);

            //prepare shap id model
            mShapIdNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mShapIdNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mShapIdNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.caps_pay_shap));
            mShapIdNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_shapid));
            mShapIdNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_shapid));
            mShapIdNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.SHAPID);
            mShapIdNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_shapid_item_layout);


            //prepare electricity model
            mElectricityNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mElectricityNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.electricity_water_metre));
            mElectricityNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_meter_number));
            mElectricityNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_other_meter_number));
            mElectricityNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.ELECTRICITY_METER);
            mElectricityNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_electricity_item_layout);

            //prepare email model
            mEmailNbFlexibleItemCountRecyclerviewModel = new NBFlexibleItemCountRecyclerviewModel();
            mEmailNbFlexibleItemCountRecyclerviewModel.setHeaderText(getString(R.string.email));
            mEmailNbFlexibleItemCountRecyclerviewModel.setFooterShow(false);
            mEmailNbFlexibleItemCountRecyclerviewModel.setDeleteButtonShow(false);
            mEmailNbFlexibleItemCountRecyclerviewModel.setFooterText(getString(R.string.add_another_email_address));
            mEmailNbFlexibleItemCountRecyclerviewModel.setEmptyItemListFooterText(getString(R.string.add_other_email_address));
            mEmailNbFlexibleItemCountRecyclerviewModel.setVIEWType(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.EMAIL);
            mEmailNbFlexibleItemCountRecyclerviewModel.setItemView(R.layout.nb_recyclerview_email_item_layout);
        }
        setUpRecyclerViewAdapter();
    }

    protected abstract void setUpRecyclerViewAdapter();

    @Override
    public void onItemDataChange() {
        passScreenInputToPresenter();
    }

    @Override
    public void onItemRemoved(int pos, int viewType, NbRecyclerViewBaseDataModel nbRecyclerViewBaseDataModel) {
        passScreenInputToPresenter();
    }

    private void passScreenInputToPresenter() {
        if (etRecipientName != null && etRecipientName.hasError()) {
            mBaseRecipientPresenter.disableLoadingButton();
        } else if (isBankApprovedBeneficiary()) {
            mBaseRecipientPresenter.checkInputsOnScreenForBDF(etRecipientName, mBankAccountViewDataModelList);
        } else {
            mBaseRecipientPresenter.checkInputsOnScreen(etRecipientName, mBankAccountViewDataModelList, mMobileNumberViewDataModelList, mElectricityMeterViewDataModelList, mCreditCardViewDataModelList, mShapIDViewDataModelList);
        }
    }

    @Override
    public String getDefaultBankNameText() {
        return getString(R.string.pay_select_bank_label);
    }

    @Override
    public HashMap<String, String> setFastPayDomainNameMap(List<CoreFastPayDomainResponseData> fastPayDomainList) {
        this.fastPayDomainList = fastPayDomainList;
        int keyForMap = 1;
        for (CoreFastPayDomainResponseData coreFastPayDomainResponseData : fastPayDomainList) {
            fastPayDomainNameMap.put("" + keyForMap, coreFastPayDomainResponseData.getValue().toLowerCase());
            keyForMap++;
        }
        return fastPayDomainNameMap;
    }

    @Override
    public HashMap<String, String> fetchFastPayDomainNameMap() {
        return this.fastPayDomainNameMap;
    }

    @Override
    public List<CoreFastPayDomainResponseData> fetchFastPayDomainNameList() {
        return this.fastPayDomainList;
    }

    @Override
    public void setLoadingButtonEnabled(boolean enabled) {
        if (mLoadingButton != null) {
            mLoadingButton.setEnabled(enabled);
        }
    }

    @Override
    public void showLoadingOnButton(boolean inProgress) {
        if (mLoadingButton != null) {
            mLoadingButton.setLoadingVisible(inProgress);
        }
    }

    @Override
    public void dataValidationSuccess() {

    }

    public void onAddButtonClick() {
        List<RecipientValidatableViewModel> bankAccountNumberInputList = new ArrayList<>();
        List<RecipientValidatableViewModel> mobileNumberInputList = new ArrayList<>();
        List<RecipientValidatableViewModel> meterNumberInputList = new ArrayList<>();
        List<RecipientValidatableViewModel> creditCardNumberInputList = new ArrayList<>();
        List<RecipientValidatableViewModel> shapIdInputList = new ArrayList<>();
        List<RecipientValidatableViewModel> emailInputList = new ArrayList<>();

        if (mBankAccountViewDataModelList.size() > 0) {
            for (int index = 0; index < mBankAccountViewDataModelList.size(); index++) {
                BankAccountViewDataModel bankAccountViewDataModel = (BankAccountViewDataModel) mBankAccountViewDataModelList.get(index);
                BankAccountAdapter.VHBankAccountItem accountViewHolder = (BankAccountAdapter.VHBankAccountItem) rvAccount.findViewHolderForAdapterPosition(index + 1);
                RecipientValidatableViewModel bankAccountValidatableVM = new RecipientValidatableViewModel();
                bankAccountValidatableVM.setRecipientNumber(accountViewHolder.accountNumber);
                bankAccountValidatableVM.setYourRef(accountViewHolder.yourRef);
                bankAccountValidatableVM.setRecipientRef(accountViewHolder.recipientRef);
                bankAccountNumberInputList.add(bankAccountValidatableVM);
            }
        }

        if (null != mMobileNumberViewDataModelList && mMobileNumberViewDataModelList.size() > 0) {
            for (int index = 0; index < mMobileNumberViewDataModelList.size(); index++) {
                RecipientValidatableViewModel mobileValidatableVM = new RecipientValidatableViewModel();
                MobileNumberAdapter.VHMobileNumberItem mobileNumberViewHolder = (MobileNumberAdapter.VHMobileNumberItem) rvMobileNumber.findViewHolderForAdapterPosition(index + 1);
                mobileValidatableVM.setRecipientNumber(mobileNumberViewHolder.mobileNumber);
                mobileValidatableVM.setYourRef(mobileNumberViewHolder.yourRef);
                mobileNumberInputList.add(mobileValidatableVM);
            }
        }

        if (null != mCreditCardViewDataModelList && mCreditCardViewDataModelList.size() > 0) {
            for (int index = 0; index < mCreditCardViewDataModelList.size(); index++) {
                RecipientValidatableViewModel creditCardValidatableVM = new RecipientValidatableViewModel();
                CreditCardAdapter.VHCreditCardItem vhCreditCardItem = (CreditCardAdapter.VHCreditCardItem) rvCreditCard.findViewHolderForAdapterPosition(index + 1);
                creditCardValidatableVM.setRecipientNumber(vhCreditCardItem.etCardNumber);
                creditCardValidatableVM.setYourRef(vhCreditCardItem.yourRef);
                creditCardValidatableVM.setRecipientRef(vhCreditCardItem.recipientReference);
                creditCardNumberInputList.add(creditCardValidatableVM);
            }
        }

        if (CollectionUtils.isNotEmpty(mShapIDViewDataModelList)) {
            for (int index = 0; index < mShapIDViewDataModelList.size(); index++) {
                RecipientValidatableViewModel shapeIdValidatableVM = new RecipientValidatableViewModel();
                ShapIDAdapter.VHShapIdItem vhShapIdItem = (ShapIDAdapter.VHShapIdItem) rvShapeID.findViewHolderForAdapterPosition(index + 1);
                shapeIdValidatableVM.setRecipientNumber(vhShapIdItem.etShapId);
                shapeIdValidatableVM.setYourRef(vhShapIdItem.yourRef);
                shapeIdValidatableVM.setRecipientRef(vhShapIdItem.recipientReference);
                shapIdInputList.add(shapeIdValidatableVM);
            }
        }

        if (null != mElectricityMeterViewDataModelList && mElectricityMeterViewDataModelList.size() > 0) {
            for (int index = 0; index < mElectricityMeterViewDataModelList.size(); index++) {
                RecipientValidatableViewModel electricityValidatableVM = new RecipientValidatableViewModel();
                ElectricityMeterAdapter.VHElectricityMetreItem electricityMeterViewHolder = (ElectricityMeterAdapter.VHElectricityMetreItem) rvElectricity.findViewHolderForAdapterPosition(index + 1);
                electricityValidatableVM.setRecipientNumber(electricityMeterViewHolder.meterNumber);
                electricityValidatableVM.setYourRef(electricityMeterViewHolder.yourRef);
                meterNumberInputList.add(electricityValidatableVM);
            }
        }

        if (null != mEmailViewDataModelList && mEmailViewDataModelList.size() > 0) {
            for (int index = 0; index < mEmailViewDataModelList.size(); index++) {
                EmailViewDataModel emailViewDataModel = (EmailViewDataModel) mEmailViewDataModelList.get(index);
                if (!TextUtils.isEmpty(emailViewDataModel.getEmail())) {
                    RecipientValidatableViewModel emailValidatableVM = new RecipientValidatableViewModel();
                    EmailAdapter.VHEmailItem electricityMeterViewHolder = (EmailAdapter.VHEmailItem) rvEmail.findViewHolderForAdapterPosition(index + 1);
                    emailValidatableVM.setRecipientNumber(electricityMeterViewHolder.etEmail);
                    emailInputList.add(emailValidatableVM);
                }
            }
        }
        mBaseRecipientPresenter.validateInput(etRecipientName, bankAccountNumberInputList, creditCardNumberInputList, mobileNumberInputList, meterNumberInputList, emailInputList, shapIdInputList);
    }

    @Override
    public void onSetExistingBankModel(BankViewModel bankViewModel) {
        this.mBankViewModel = bankViewModel;
    }

    @Override
    public void setResult(Map<String, Object> resultMap) {
        if (resultMap != null && resultMap.size() > 0) {
            int screen = -1;
            Object screenTypeObject = resultMap.get(Constants.EXTRAS.SCREEN_TYPE);
            if (screenTypeObject instanceof Integer) {
                screen = (Integer) screenTypeObject;
            }
            if (screen == IChildScreenTypes.BANK_NAME_SCREEN) {
                Object objectReceived = resultMap.get(NavigationTarget.EXTRAS.BANK_SELECTED);
                if (objectReceived instanceof BankViewModel) {
                    mBankViewModel = (BankViewModel) objectReceived;
                    int selectedAccountTypeNameResId;
                    if (PaymentsUtility.isNedBank(mBankViewModel.getBankCode(), mBankViewModel.getBankName())) {
                        mSelectedAccountTypeCode = za.co.nedbank.core.Constants.ACCOUNT_TYPES.SA.getAccountTypeCode();
                        selectedAccountTypeNameResId = za.co.nedbank.core.Constants.ACCOUNT_TYPES.SA.getAccountTypeStringResId();
                    } else {
                        mSelectedAccountTypeCode = za.co.nedbank.core.Constants.ACCOUNT_TYPES.U0.getAccountTypeCode();
                        selectedAccountTypeNameResId = za.co.nedbank.core.Constants.ACCOUNT_TYPES.U0.getAccountTypeStringResId();
                    }
                    mBankAccountAdapter.onBankSelected(mSelectedBankItemPos, mBankViewModel, mSelectedAccountTypeCode, selectedAccountTypeNameResId);
                    passScreenInputToPresenter();
                }
            } else if (screen == IChildScreenTypes.BRANCH_SCREEN) {
                Object objectReceived = resultMap.get(NavigationTarget.EXTRAS.BRANCH_SELECTED);
                if (objectReceived instanceof BankBranchViewModel) {
                    BankBranchViewModel bankBranchViewModel = (BankBranchViewModel) objectReceived;
                    mBankAccountAdapter.onBranchSelected(mSelectedBankItemPos, bankBranchViewModel);
                    passScreenInputToPresenter();
                }
            } else if (screen == IPayChildScreenTypes.ACCOUNT_TYPE_SCREEN) {
                Object objectReceived = resultMap.get(PayNavigatorTarget.EXTRAS.SELECTED_ACCOUNT_TYPE);
                if (objectReceived instanceof AccountType) {
                    AccountType accountType = (AccountType) objectReceived;
                    mSelectedAccountTypeCode = accountType.getAccountTypeCode();
                    mBankAccountAdapter.onAccountTypeSelected(mSelectedBankItemPos, accountType);
                    passScreenInputToPresenter();
                }
            }
        }
    }

    private void hideKeyboard() {
        if (null != etRecipientName) {
            etRecipientName.clearFocus();
            ViewUtils.hideSoftKeyboard(BaseRecipientActivity.this, etRecipientName);
        }
    }

    @Override
    public SelectedBankViewModel getSelectedBankViewModel() {
        SelectedBankViewModel selectedBankViewModel = new SelectedBankViewModel();
        if (null != mBankViewModel) {
            selectedBankViewModel = new SelectedBankViewModel();
            selectedBankViewModel.setBankCode(mBankViewModel.getBankCode());
            selectedBankViewModel.setBranchList(mBankViewModel.getBranchList());
        }
        return selectedBankViewModel;
    }

    @Override
    public void onBankSelected(int pos) {
        hideKeyboard();
        // wait for keyboard to hide
        mUiHandler.postDelayed(() -> {
            mSelectedBankItemPos = pos;
            mBaseRecipientPresenter.handleSelectBankLayoutClick();
        }, Constants.KEYBOARD_HIDE_TIME);
    }

    @Override
    public void onAccountTypeSelected(int adapterPosition) {
        hideKeyboard();
        mUiHandler.postDelayed(() -> {
            mSelectedBankItemPos = adapterPosition;
            mBaseRecipientPresenter.handleSelectAccountTypeClick();
        }, Constants.KEYBOARD_HIDE_TIME);
    }

    @Override
    public void onBranchCodeSelected(int adapterPosition) {
        hideKeyboard();
        mUiHandler.postDelayed(() -> {
            mSelectedBankItemPos = adapterPosition;
            mBaseRecipientPresenter.handleSelectBranchCodeClick();
        }, Constants.KEYBOARD_HIDE_TIME);
    }

    @Override
    public void onAdapterInteraction() {
        hideKeyboard();
    }

    @Override
    public String getSelectedAccountTypeCode() {
        return mSelectedAccountTypeCode;
    }


    protected boolean isBankApprovedBeneficiary() {
        if (null != mRecipientViewModel && null != mRecipientViewModel.getBankAccountViewDataModelList()
                && mRecipientViewModel.getBankAccountViewDataModelList().size() > 0
                && mRecipientViewModel.getBankAccountViewDataModelList().get(0) instanceof BankAccountViewDataModel) {
            BankAccountViewDataModel bankAccountVM = (BankAccountViewDataModel) mRecipientViewModel.getBankAccountViewDataModelList().get(0);
            return BeneficiaryConstants.BENEFICIARY_TYPE_ID_BDF.equalsIgnoreCase(bankAccountVM.getBeneficiaryType());

        }
        return false;
    }

    @Override
    public void hideAddShapIdSection() {
        ViewUtils.hideViews(rvShapeID);
    }
}
