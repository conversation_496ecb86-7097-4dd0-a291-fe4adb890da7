/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.datastore;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.ui.data.entity.money_request.ValidateNumberEntity;
import za.co.nedbank.ui.data.networking.ValidateNumberAPI;


public class ValidateMobileNumberFactory {

    private final NetworkClient mNetworkClient;

    @Inject
    ValidateMobileNumberFactory(NetworkClient networkClient) {
        this.mNetworkClient = networkClient;
    }

    public Observable<ValidateNumberEntity> validateMobileNumber(String mobileNumber) {
        return mNetworkClient.create(ValidateNumberAPI.class).validateNumberEntityObservable(mobileNumber);
    }
}
