/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import androidx.annotation.NonNull;

import java.util.UUID;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.enrol.ApproveItFallbackUsecase;
import za.co.nedbank.core.domain.usecase.enrol.FraudApproveItUsecase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.payment.ErrorViewModel;
import za.co.nedbank.core.payment.PaymentsUtility;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.nid_sdk.main.interaction.model.ApproveItRequestDto;
import za.co.nedbank.nid_sdk.main.interaction.model.FraudApproveItAcknowledgeDto;
import za.co.nedbank.nid_sdk.main.views.approveit.check.ApproveItWorkFlow;
import za.co.nedbank.payment.pay.domain.data.mapper.pay.PayRequestViewModelToDataMapper;
import za.co.nedbank.payment.pay.domain.data.mapper.pay.PayResponseDataToViewModelMapper;
import za.co.nedbank.payment.pay.domain.data.model.pay.response.PayMainDataModel;
import za.co.nedbank.payment.pay.domain.usecase.PayToAccountUseCase;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;
import za.co.nedbank.payment.pay.view.model.response.PayMainViewModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsActionDataModel;
import za.co.nedbank.ui.domain.usecase.money_request.MoneyRequestsActionUseCase;
import za.co.nedbank.ui.view.mapper.PaymentActionViewToDataModelMapper;
import za.co.nedbank.ui.view.model.PaymentActionViewModel;
import za.co.nedbank.ui.view.tracking.AppTracking;

/**
 * Created by devrath.rathee on 2/23/2018.
 */

public class MoneyPaymentReviewPresenter extends NBBasePresenter<MoneyResponseReviewView> {

    private final PayToAccountUseCase mPayToAccountUseCase;
    private final FraudApproveItUsecase mFraudApproveItUseCase;
    private final ApproveItFallbackUsecase mApproveItFallbackUsecase;
    private final MoneyRequestsActionUseCase moneyRequestsActionUseCase;
    private final PayRequestViewModelToDataMapper mPayRequestViewModelToDataMapper;
    private final PayResponseDataToViewModelMapper mPayResponseDataToViewModelMapper;
    private final PaymentActionViewToDataModelMapper paymentActionViewToDataModelMapper;
    private final ApplicationStorage mApplicationStorage;
    private final NavigationRouter navigationRouter;
    private final ErrorHandler errorHandler;
    private final FeatureSetController featureSetController;
    private ErrorViewModel errorViewModel;
    private final Analytics mAnalytics;
    private int RESEND_COUNTER = 0;

    @Inject
    MoneyPaymentReviewPresenter(MoneyRequestsActionUseCase moneyRequestsActionUseCase,
                                PayToAccountUseCase payToAccountUseCase, FraudApproveItUsecase fraudApproveItUsecase, ApproveItFallbackUsecase approveItFallbackUsecase,
                                PayRequestViewModelToDataMapper payRequestViewModelToDataMapper,
                                PayResponseDataToViewModelMapper payResponseDataToViewModelMapper,
                                PaymentActionViewToDataModelMapper paymentActionViewToDataModelMapper,
                                @NonNull NavigationRouter navigationRouter, @NonNull ErrorHandler errorHandler,
                                Analytics analytics, ApplicationStorage applicationStorage, FeatureSetController featureSetController) {
        this.moneyRequestsActionUseCase = moneyRequestsActionUseCase;
        this.mPayToAccountUseCase = payToAccountUseCase;
        this.mFraudApproveItUseCase = fraudApproveItUsecase;
        this.mApproveItFallbackUsecase = approveItFallbackUsecase;
        this.mPayRequestViewModelToDataMapper = payRequestViewModelToDataMapper;
        this.mPayResponseDataToViewModelMapper = payResponseDataToViewModelMapper;
        this.paymentActionViewToDataModelMapper = paymentActionViewToDataModelMapper;
        this.mApplicationStorage = applicationStorage;
        this.navigationRouter = navigationRouter;
        this.errorHandler = errorHandler;
        this.mAnalytics = analytics;
        this.featureSetController = featureSetController;
    }

    @Override
    protected void onBind() {
        super.onBind();
        mAnalytics.sendEvent(AppTracking.TAG_PAY_ME_PAY_NOW_PAYMENT_REVIEW, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    void setReviewData() {
        view.setPaymentDetails(view.getPaymentViewModel(), view.getReceiverMobile());
    }

    void navigateToMoneyPaymentDone() {
        mAnalytics.sendEvent(AppTracking.TAG_PAY_ME_PAY_NOW_PAYMENT_SUCCESSFUL, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MONEY_RESPONSE_SUCCESS)
                .withParam(NavigationTarget.PARAM_RECEIVER_MOBILE_NUMBER, view.getReceiverMobile())
                .withParam(NavigationTarget.PARAM_PAYMENT_MODEL, view.getPaymentViewModel()));
    }

    void makeAccountPaymentCall() {
        makeAccountPaymentCall(null);
    }

    void trackPayNowConfirm() {
        mAnalytics.sendEvent(AppTracking.TAG_PAY_ME_PAY_NOW_PAYMENT_CONFIRM, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    void makeAccountPaymentCall(String requestID) {
        PaymentsViewModel paymentsViewModel = view.getPaymentsViewModel();

        if (StringUtils.isNullOrEmpty(requestID)) {
            paymentsViewModel.setRequestID(UUID.randomUUID().toString());
        } else {
            paymentsViewModel.setRequestID(requestID);
        }

        payToAccountTransaction(paymentsViewModel);
    }

    void payToAccountTransaction(PaymentsViewModel paymentsViewModel) {
        if (paymentsViewModel.getPaymentViewModelList() == null
                || paymentsViewModel.getPaymentViewModelList().isEmpty()) {
            return;
        }
        if (view != null) {
            view.setLoadingEnabled(true);
            view.setDoublePaymentLayoutVisibility(false);
        }
        mPayToAccountUseCase.execute(mPayRequestViewModelToDataMapper.mapRequestPayViewModelToDataModel(paymentsViewModel))
                .compose(bindToLifecycle())
                .subscribe(payMainDataModel -> {
                    if (view != null) {
                        view.setLoadingEnabled(false);
                    }
                    if (payMainDataModel != null) {
                        PayMainViewModel payMainViewModel = mPayResponseDataToViewModelMapper.mapRequestPayDataToEntityModel(payMainDataModel);
                        setErrorViewModel(PaymentsUtility.retrieveApiCallErrors(payMainViewModel.getMetadata(), errorHandler.getUnknownError(), DtoHelper.TRANSACTION));
                        if (errorViewModel != null) {
                            if (errorViewModel.isTransactionSuccess()) {
                                handleTransactionSuccess(paymentsViewModel, payMainDataModel);
                            } else if (errorViewModel.isDoublePayment()) {
                                handleDoublePayment();
                            } else if (errorViewModel.isTransactionPending()) {
                                callFraudApproveItUseCase(errorViewModel, paymentsViewModel.getRequestID(), mFraudApproveItUseCase);
                            } else if (errorViewModel.isTransactionFallback()) {
                                callFraudApproveItUseCase(errorViewModel, paymentsViewModel.getRequestID(), mApproveItFallbackUsecase);
                            } else {
                                handleTransactionFailure(paymentsViewModel.getRequestID(), errorViewModel.getErrorMessages()[0]);
                            }
                        }
                    } else {
                        handleTransactionFailure(paymentsViewModel.getRequestID(), errorHandler.getUnknownError());
                    }
                }, throwable -> {
                    handleTransactionFailure(paymentsViewModel.getRequestID(), errorHandler.getErrorMessage(throwable).getMessage());
                });
    }

    private void handleTransactionSuccess(PaymentsViewModel paymentsViewModel, PayMainDataModel payMainDataModel) {
        if (paymentsViewModel.isValidate()) {
            paymentsViewModel.setValidate(!paymentsViewModel.isValidate());
            payToAccountTransaction(paymentsViewModel);
        } else {
            paymentsViewModel.setSaveBeneficiaryFailed(errorViewModel.isBeneficiarySaveFailed());
            paymentsViewModel.getPaymentViewModelList().get(0).setTransactionNumber(errorViewModel.getEexecEngineRef());
            performTransactionComplete(getBatchId(payMainDataModel));
        }
    }

    private void performTransactionComplete(long batchId) {
        PaymentActionViewModel paymentActionViewModel = view.getPaymentActionRequestModel();
        paymentActionViewModel.setPaymentBatchId(batchId);
        performPaymentRequestsAction(paymentActionViewModel);
    }

    private void handleTransactionFailure(String requestId, String errorMessage) {
        if (view != null) {
            view.setLoadingEnabled(false);
            view.setEnabledActivityTouch(true);
            view.setPaymentPostCallError(requestId, errorMessage);
        }
        trackFailure(true, errorMessage, null);
    }

    private void preApiCallTasks() {
        if (view != null) {
            view.setLoadingEnabled(false);
            view.setEnabledActivityTouch(false);
        }
    }

    private void postApiCallTasks() {
        if (view != null) {
            view.setLoadingEnabled(true);
            view.setEnabledActivityTouch(true);
        }
    }

    private void callFraudApproveItUseCase(ErrorViewModel errorViewModel, String requestId, UseCase<ApproveItRequestDto, FraudApproveItAcknowledgeDto> useCase) {
        String referenceURL = Constants.FraudApproveItEndPoint.PAYMENT_ACCOUNT;
        ApproveItRequestDto requestDto = new ApproveItRequestDto();
        requestDto.setAuthRefernce(errorViewModel.getTransactionId());
        requestDto.setResult(errorViewModel.getTransactionResult());
        if (!featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.SENSITIVE_TRANSACTIONS_CALLBACK)) {
            requestDto.setApproveItType(ApproveItWorkFlow.CALLBACK);
        }
        requestDto.setApproveItApiEndPoint(referenceURL + errorViewModel.getTransactionId() + Constants.FraudApproveItEndPoint.STATUS);
        requestDto.setMobielNumber(StringUtils.obfuscateMobileNumber(mApplicationStorage
                .getString(StorageKeys.MOBILE_NUMBER_UNMASKED, StringUtils.EMPTY_STRING)));
        useCase.execute(requestDto)
                .doOnSubscribe(disposable -> preApiCallTasks())
                .subscribe(acknowledgeDto -> {
                    handleApproveItResponse(acknowledgeDto, requestId);
                }, throwable -> {
                    if (view != null) {
                        postApiCallTasks();
                        view.setPaymentPostCallError(requestId, errorHandler.getErrorMessage(throwable).getMessage());
                    }
                });
    }

    private void handleApproveItResponse(FraudApproveItAcknowledgeDto acknowledgeDto, String requestId) {
        if (view != null) {
            if (acknowledgeDto.getResultCode() != null && acknowledgeDto.getResultCode().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                performTransactionComplete(acknowledgeDto.getBatchId() != null ? Long.parseLong(acknowledgeDto.getBatchId()) : 0);
            } else if (acknowledgeDto.getResultCode() != null && acknowledgeDto.getResultCode().equalsIgnoreCase(DtoHelper.RESEND)
                    && RESEND_COUNTER++ < 3) {
                makeAccountPaymentCall(requestId);
            } else if (acknowledgeDto.getResultCode() != null && acknowledgeDto.getResultCode().equalsIgnoreCase(DtoHelper.FAILURE)
                    && acknowledgeDto.showErrorMessage()) {
                handleTransactionFailure(requestId, acknowledgeDto.getMessage());
            } else {
                handleTransactionFailure(requestId, errorHandler.getUnknownError());
            }
        }
        postApiCallTasks();
    }

    private long getBatchId(PayMainDataModel payMainDataModel) {
        return Long.parseLong(payMainDataModel.getMetadata().getResultData().get(0).getBatchID());
    }

    private void setErrorViewModel(ErrorViewModel errorViewModel) {
        this.errorViewModel = errorViewModel;
    }

    void performPaymentRequestsAction(PaymentActionViewModel paymentActionViewModel) {
        moneyRequestsActionUseCase.execute(paymentActionViewToDataModelMapper.mapPaymentActionViewToData(paymentActionViewModel))
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.setLoadingEnabled(true);
                    }
                })
                .doFinally(() -> {
                    if (view != null) {
                        view.setLoadingEnabled(false);
                    }
                })
                .subscribe(this::handleActionResultData, throwable -> {
                    handleError(throwable);
                    mAnalytics.trackFailure(false, AppTracking.TAG_PAY_ME_PAY_NOW_PAYMENT_FAILURE, za.co.nedbank.core.ApiAliasConstants.AUTH_REQ, errorHandler.getErrorMessage(throwable).getMessage(), null);
                });
    }

    private void handleError(Throwable throwable) {
        if (view != null) {
            view.showError(errorHandler.getErrorMessage(throwable).getMessage());
        }
    }

    private void handleDoublePayment() {
        if (view != null) {
            view.setPayButtonEnable(false);
            view.setDoublePaymentLayoutVisibility(true);
            view.trackDoublePayment();
        }
    }

    void navigateToDashboardScreen() {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.HOME);
        navigationTarget.withIntentFlagClearTopSingleTop(true);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void handleActionResultData(MoneyRequestsActionDataModel moneyRequestsActionDataModel) {
        navigateToMoneyPaymentDone();
    }

    void trackDoublePayment(String errorMessage) {
        trackFailure(true, errorMessage, null);
    }

    private void trackFailure(boolean isApiFailure, String errorMessage, String errorCode) {
        mAnalytics.trackFailure(isApiFailure, AppTracking.TAG_PAY_ME_PAY_NOW_PAYMENT_FAILURE, za.co.nedbank.core.ApiAliasConstants.SUB_PAY, errorMessage, errorCode);
    }
}
