/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.money_request;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.ui.data.mapper.money_requests.ValidateNumberEntityToDataMapper;
import za.co.nedbank.ui.domain.repository.IValidateNumberRepository;


public class ValidateMobileNumberUseCase extends UseCase<String, MetaDataModel> {

    private final IValidateNumberRepository iValidateNumberRepository;
    private final ValidateNumberEntityToDataMapper validateNumberEntityToDataMapper;

    @Inject
    ValidateMobileNumberUseCase(final UseCaseComposer useCaseComposer,
                                final IValidateNumberRepository iValidateNumberRepository,
                                final ValidateNumberEntityToDataMapper validateNumberEntityToDataMapper
    ) {
        super(useCaseComposer);
        this.iValidateNumberRepository = iValidateNumberRepository;
        this.validateNumberEntityToDataMapper = validateNumberEntityToDataMapper;
    }

    @Override
    protected Observable<MetaDataModel> createUseCaseObservable(String mobileNumber) {
        return iValidateNumberRepository.validateMobileNumber(mobileNumber).map(validateNumberEntityToDataMapper::mapValidateNumberEntityToDataModel);
    }

}
