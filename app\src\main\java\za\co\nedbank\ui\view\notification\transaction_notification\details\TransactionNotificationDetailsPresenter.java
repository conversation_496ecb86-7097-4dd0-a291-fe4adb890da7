package za.co.nedbank.ui.view.notification.transaction_notification.details;


import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.FBNotificationUserChoiceData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsAnalyticData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsData;
import za.co.nedbank.core.domain.model.notifications.FBResponseData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.notifications.FBNotificationUserChoicePreSignupUseCase;
import za.co.nedbank.core.domain.usecase.notifications.FBNotificationsAnalyticsPreSignupUseCase;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.core.view.mapper.fbnotifications.FBNotificationsInnerDetailsDataToViewModelMapper;
import za.co.nedbank.ui.view.notification.transaction_notification.TransactionTrackingEvent;

public class TransactionNotificationDetailsPresenter extends NBBasePresenter<TransactionNotificationDetailsView> {

    private static final String TAG = TransactionNotificationDetailsPresenter.class.getSimpleName();
    private final NavigationRouter mNavigationRouter;
    private final ApplicationStorage mMemoryApplicationStorage;
    private final ApplicationStorage mApplicationStorage;
    private final Analytics analytics;
    private final APIInformation apiInformation;
    private final FBNotificationsAnalyticsPreSignupUseCase mFBNotificationsAnalyticsPreSignupUseCase;
    private final FBNotificationUserChoicePreSignupUseCase mFBNotificationUserChoicePreSignupUseCase;
    private final FBNotificationsInnerDetailsDataToViewModelMapper mFBNotificationsInnerDetailsDataToViewModelMapper;

    private FBNotificationsViewModel mFBNotificationsViewModel = new FBNotificationsViewModel();

    @Inject
    public TransactionNotificationDetailsPresenter(NavigationRouter navigationRouter,
                                                   @Named("memory") ApplicationStorage memoryApplicationStorage,
                                                   ApplicationStorage applicationStorage,
                                                   Analytics analytics,
                                                   APIInformation apiInformation, FBNotificationsAnalyticsPreSignupUseCase fbNotificationsAnalyticsPreSignupUseCase,
                                                   FBNotificationsInnerDetailsDataToViewModelMapper fbNotificationsInnerDetailsDataToViewModelMapper,
                                                   FBNotificationUserChoicePreSignupUseCase fbNotificationUserChoicePreSignupUseCase) {

        this.mNavigationRouter = navigationRouter;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.mApplicationStorage = applicationStorage;
        this.analytics = analytics;
        this.apiInformation = apiInformation;
        this.mFBNotificationsAnalyticsPreSignupUseCase = fbNotificationsAnalyticsPreSignupUseCase;
        this.mFBNotificationsInnerDetailsDataToViewModelMapper = fbNotificationsInnerDetailsDataToViewModelMapper;
        this.mFBNotificationUserChoicePreSignupUseCase = fbNotificationUserChoicePreSignupUseCase;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

    @Override
    protected void onUnbind() {
        super.onUnbind();
    }

    protected FBTransactionNotificationsViewModel getFBTransactionNotificationViewModel(FBNotificationsViewModel fbNotificationsViewModel) {
        FBNotificationsData notificationData = mFBNotificationsInnerDetailsDataToViewModelMapper.transformBack(fbNotificationsViewModel);
        return mFBNotificationsInnerDetailsDataToViewModelMapper.transformTransactionViewModel(notificationData);
    }


    public void handleOnClick(String action) {
        switch (action) {
            case NotificationConstants.NAVIGATION_TARGET.REPORT_FRAUD:
                startLoginFlow();
                break;
            case NotificationConstants.NAVIGATION_TARGET.DEBIT_ORDER_LIST:
                startLoginFlow();
                break;
            case NotificationConstants.NAVIGATION_TARGET.DISMISS:
                view.closeScreen();
                break;
            case NotificationConstants.NAVIGATION_TARGET.ENROLLMENT_FLOW:
                startEnrollmentFlow();
                break;
            default:
                startLoginFlow();
                break;
        }
    }

    private void startEnrollmentFlow() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ENROLL_V2_LANDING).withClearStack(true));
    }

    private void startLoginFlow() {
        mMemoryApplicationStorage.putBoolean(Constants.IS_NOTIF_DETAILS_SHOWED, true);
        if (apiInformation.isLoggedOut()) {
            navigateToLogin();
        } else {
            view.navigateToNextScreen();
            view.close();
        }
    }


    public void navigateToLogin() {
        mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.LOGIN)
                .withParam(za.co.nedbank.core.Constants.IS_FROM_REPORT_FRAUD_SCREEN, true))
                .subscribe(
                        navigationResult -> {
                            if (null != navigationResult && navigationResult.isOk() && view != null) {
                                // view.onLoggedIn();
                            }
                        });
    }

    public void navigateToReportFraud(FBTransactionNotificationsViewModel transactionModel) {
        analytics.sendEvent(TransactionTrackingEvent.ActionName.TRANSACTION_NOTIFICATION_REPORT_FRAUD_CONFIRM, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);

        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MORE_REPORT_FRAUD)
                .withParam(Constants.PARAM_REPORT_SUSPICIOUS, transactionModel)
                .withParam(NavigationTarget.IS_FROM_REPORT, true));
    }

    public void clearNotificationData(Integer notificationId) {
        FBNotificationsViewModel notificationViewModel = (FBNotificationsViewModel) mMemoryApplicationStorage.getObject(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL);
        if (notificationViewModel != null && notificationViewModel.getNotificationId() != null && notificationViewModel.getNotificationId().equals(notificationId)) {
            mMemoryApplicationStorage.clearValue(NotificationConstants.STORAGE_KEYS.NOTIFICATION_VIEW_MODEL);
        }
    }

    public void sendAnalyticsToServer(FBNotificationsViewModel fbNotificationsViewModel) {
        mFBNotificationsViewModel = fbNotificationsViewModel;
        if (mFBNotificationsViewModel.getMustTrackFirstRead() != null && !mFBNotificationsViewModel.getMustTrackFirstRead()
                || (mFBNotificationsViewModel.getMustTrackAllReads() != null && !mFBNotificationsViewModel.getMustTrackAllReads()
                && NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_READ.equals(mFBNotificationsViewModel.getStatus())))
            return;
        mFBNotificationsAnalyticsPreSignupUseCase.execute(createUserAnalyticsRequest())
                .compose(bindToLifecycle())
                .subscribe(analyticsResponseData -> handleAnalyticsUserChoiceResponse(analyticsResponseData, null, false), throwable -> NBLogger.e(TAG, throwable.getMessage()));
    }


    private FBNotificationsAnalyticData createUserAnalyticsRequest() {
        FBNotificationsAnalyticData fbNotificationsAnalyticData = new FBNotificationsAnalyticData();
        FBNotificationsAnalyticData.Analytic fbAnalytics = new FBNotificationsAnalyticData.Analytic();
        fbAnalytics.setDeviceId(getDeviceID());
        fbAnalytics.setAnalyticValue(NotificationConstants.ANALYTIC_TYPES.NOTIFICATION_READ);
        fbAnalytics.setDeviceDate(getDate());
        fbNotificationsAnalyticData.setAnalytic(fbAnalytics);

        fbNotificationsAnalyticData.setNotificationId(mFBNotificationsViewModel.getNotificationId());
        return fbNotificationsAnalyticData;
    }

    String getDeviceID() {
        String deviceId = mApplicationStorage.getString(NotificationConstants.STORAGE_KEYS.UUID, StringUtils.EMPTY_STRING);
        if (StringUtils.isNullOrEmpty(deviceId)) {
            deviceId = mApplicationStorage.getString(NotificationConstants.STORAGE_KEYS.UUID_PRE_LOGIN, StringUtils.EMPTY_STRING);
        }
        return deviceId;
    }

    private String getDate() {
        SimpleDateFormat dateFormatForParsing = new SimpleDateFormat(FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
        dateFormatForParsing.setTimeZone(TimeZone.getTimeZone(FormattingUtil.SOUTH_AFRICA_TIMZONE_ID));
        return dateFormatForParsing.format(new Date());
    }

    public void sendUserResponseToServer(String value, String label) {
        mFBNotificationUserChoicePreSignupUseCase.execute(createUserResponseRequest(value))
                .compose(bindToLifecycle())
                .subscribe(analyticsResponseData -> handleAnalyticsUserChoiceResponse(analyticsResponseData, label, true), throwable -> NBLogger.e(TAG, throwable.getMessage()));

    }

    private FBNotificationUserChoiceData createUserResponseRequest(String value) {
        FBNotificationUserChoiceData fbNotificationUserChoiceData = new FBNotificationUserChoiceData();
        FBNotificationUserChoiceData.ChannelInfo channelInfo = new FBNotificationUserChoiceData.ChannelInfo();
        FBNotificationUserChoiceData.Response response = new FBNotificationUserChoiceData.Response();
        response.setResponseValue(value);
        response.setDeviceId(getDeviceID());
        response.setDeviceDate(getDate());
        response.setResponsePriority(mFBNotificationsViewModel.getResponsePriority());
        fbNotificationUserChoiceData.setChannelInfo(channelInfo);
        fbNotificationUserChoiceData.setResponse(response);
        fbNotificationUserChoiceData.setNotificationId(mFBNotificationsViewModel.getNotificationId());
        return fbNotificationUserChoiceData;

    }

    private void handleAnalyticsUserChoiceResponse(FBResponseData analyticsResponseData, String label, boolean isUserChoice) {
        int onlySuccessElement = 1;
        if (analyticsResponseData != null && analyticsResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = analyticsResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && !resultDetailList.isEmpty()) {
                    if (resultData.size() == onlySuccessElement && resultDetailList.size() == onlySuccessElement && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        handleSuccess(isUserChoice, label);
                        break;
                    } else {
                        handleFailure(resultDetailList);
                    }
                }

            }
        }
    }

    private void handleSuccess(boolean isUserChoice, String label) {
        if (isUserChoice) {
            NBLogger.e(TAG, "User Choice api success, Selected response :" + label);
        } else {
            NBLogger.e(TAG, "analytics api success, notification id :" + mFBNotificationsViewModel.getNotificationId());
        }
    }


    private void handleFailure(ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                NBLogger.e(TAG, resultDetailViewModel.getReason());
            }
        }
    }

    public void addInStorage(FBNotificationsViewModel.ResponseOption selectedResponse) {
        mMemoryApplicationStorage.putObject(NotificationConstants.STORAGE_KEYS.SELECTED_RESPONSE, selectedResponse);
    }
}
