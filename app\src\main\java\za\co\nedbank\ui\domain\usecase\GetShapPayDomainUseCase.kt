package za.co.nedbank.ui.domain.usecase

import io.reactivex.Observable
import za.co.nedbank.core.data.mapper.GetShapPayEntityToDataMapper
import za.co.nedbank.core.domain.UseCaseComposer
import za.co.nedbank.core.domain.VoidUseCase
import za.co.nedbank.core.domain.model.response.CoreFastPayDomainResponseDataModel
import za.co.nedbank.core.domain.repository.IRecipientRepository
import javax.inject.Inject

open class GetShapPayDomainUseCase @Inject constructor(
    useCaseComposer: UseCaseComposer?,
    private val repository: IRecipientRepository,
    private val domainEntityToDataMapper: GetShapPayEntityToDataMapper
) : VoidUseCase<CoreFastPayDomainResponseDataModel>(useCaseComposer) {

    override fun createUseCaseObservable(): Observable<CoreFastPayDomainResponseDataModel> {
        return repository.fastPayDomainList.map { entity ->
            domainEntityToDataMapper.mapDomainsEntityToDataModel(entity)
        }
    }
}
