package za.co.nedbank.ui.view.notification.ajo_notification_details.navigation;

import static java.lang.Long.parseLong;
import static za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_FOR_APPLICATION;
import static za.co.nedbank.core.Constants.CreditCard.validateCreditCardId;
import static za.co.nedbank.core.Constants.ZERO_TIME_FORMAT;
import static za.co.nedbank.core.data.storage.StorageKeys.IS_NEW_TAG_MT;
import static za.co.nedbank.core.feature.FeatureConstants.TRANSACT_LOTTO_PWB;
import static za.co.nedbank.core.navigation.NavigationTarget.KEY_AVO_PROD_URL;
import static za.co.nedbank.core.navigation.NavigationTarget.NAVIGATION_FROM;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_BIRTH_DATE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_CLIENT_TYPE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD;
import static za.co.nedbank.enroll_v2.view.fica.error.FicaErrorHandler.FicaResultErrorCodes.SALARY_NOT_UPDATED;
import static za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountConstants.INVESTMENTS;
import static za.co.nedbank.payment.opennewinvaccount.view.suggestion.SuggestionPresenter.IS_NOTICE_DEPOSIT;
import static za.co.nedbank.payment.vas.prepaid.utils.PrepaidConstants.EXTRAS.PREPAID_ADVANCE_FLOW;
import static za.co.nedbank.services.Constants.AvoShopConstants.AVO_TARGET_DEEPLINK_PARAM;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.ACCIDENTAL_INSURANCE_PRODUCT_ID;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.FlowType.STANDALONE;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.INSURANCE_PRODUCT_ID;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.InsuranceCoverConstants.MIN_VALUE;
import static za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants.VEHICLE_VAP_PRODUCT_ID;
import static za.co.nedbank.services.view.navigation.ServicesNavigationTarget.DOWNLOAD_PROOF_OF_ACCOUNT_ACTIVITY;
import static za.co.nedbank.services.view.navigation.ServicesNavigationTarget.PARAM_DOCUMENT_REFERENCE;
import static za.co.nedbank.services.view.navigation.ServicesNavigationTarget.PARAM_POA_ACCOUNT;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import org.greenrobot.eventbus.EventBus;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;

import io.reactivex.Observable;
import za.co.nedbank.R;
import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.FicaProductFlow;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.ResponseStorageKey;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.constants.IAccountOptions;
import za.co.nedbank.core.convochatbot.view.ChatbotConstants;
import za.co.nedbank.core.data.accounts.model.AccountDto;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.data.storage.StorageUtility;
import za.co.nedbank.core.deeplink.notification.NotificationAdapter;
import za.co.nedbank.core.domain.CachableValue;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.profile.UserProfile;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetAccountsUseCase;
import za.co.nedbank.core.domain.usecase.GetEmcertIdUseCase;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.enrol.LoginSecurityUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.domain.usecase.ita.ITADeviceManagementUseCase;
import za.co.nedbank.core.domain.usecase.moa.ProductUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetMdmProfileUseCase;
import za.co.nedbank.core.domain.usecase.profile.GetProfileUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.ErrorMsgEvent;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseData;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseDataToViewModelMapper;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseViewModel;
import za.co.nedbank.core.payment.recent.RecentPaymentUseCase;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.ApplicantIdForeignCheckValidator;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.moa.ProductRequestViewModelToDataMapper;
import za.co.nedbank.core.view.mapper.moa.ProductResponseDataToViewModelMapper;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.core.view.model.InsuranceViewModel;
import za.co.nedbank.core.view.model.InvestmentSwitchingViewModel;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.booking.BookingEntryType;
import za.co.nedbank.core.view.model.moa.ProductDataViewModel;
import za.co.nedbank.core.view.model.moa.ProductRequestViewModel;
import za.co.nedbank.core.view.model.moa.ProductResponseDataViewModel;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.domain.usecases.preapproved_offfers.CheckUserEligibilityUseCase;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.enroll_v2.view.ntf.etetoken.usecases.UserDetailsUsecase;
import za.co.nedbank.loans.common.navigator.LoansNavigatorTarget;
import za.co.nedbank.loans.preapprovedaccountsoffers.Constants;
import za.co.nedbank.loans.preapprovedaccountsoffers.navigator.PreApprovedOffersNavigationTarget;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextDataViewModelMapper;
import za.co.nedbank.nid_sdk.main.views.sbs.context_switch.model.SwitchContextFedarationDetailsViewModel;
import za.co.nedbank.payment.atm.navigator.AtmNavigatorTarget;
import za.co.nedbank.payment.crossborder.navigator.CrossBorderNavigationTarget;
import za.co.nedbank.payment.ngi.navigator.NgiNavigatorTarget;
import za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountConstants;
import za.co.nedbank.payment.opennewinvaccount.OpenNewInvAccountTarget;
import za.co.nedbank.payment.opennewinvaccount.domain.OpenNewAccUserEntriesViewModel;
import za.co.nedbank.payment.opennewinvaccount.domain.datamodel.ProductDataModel;
import za.co.nedbank.payment.opennewinvaccount.domain.mapper.AllProductResponseDataModelToViewModelMapper;
import za.co.nedbank.payment.opennewinvaccount.domain.usecase.GetAllProductsUseCase;
import za.co.nedbank.payment.opennewinvaccount.view.model.AllProductsResponseViewModel;
import za.co.nedbank.payment.opennewinvaccount.view.model.AllProductsViewModel;
import za.co.nedbank.payment.pay.view.PayMode;
import za.co.nedbank.payment.rtp.navigator.RtpNavigator;
import za.co.nedbank.payment.vas.common.domain.model.response.tag.TagData;
import za.co.nedbank.payment.vas.common.domain.usecase.GetTagListUseCase;
import za.co.nedbank.payment.vas.common.domain.usecase.GetVasOfferingsUseCase;
import za.co.nedbank.payment.vas.common.utils.AvailableLimitHolder;
import za.co.nedbank.payment.vas.common.utils.VasAccountsHolder;
import za.co.nedbank.payment.vas.common.utils.VasConstants;
import za.co.nedbank.payment.vas.common.view.mapper.request.TagIdentifierRequestViewModelToDataMapper;
import za.co.nedbank.payment.vas.common.view.mapper.response.tag.TagDataToViewModelMapper;
import za.co.nedbank.payment.vas.common.view.mapper.response.tag.TagItemDataToViewModelMapper;
import za.co.nedbank.payment.vas.common.view.model.request.tag.TagIdentifierRequestViewModel;
import za.co.nedbank.payment.vas.common.view.model.response.tag.ProductViewModel;
import za.co.nedbank.payment.vas.common.view.model.response.tag.TagItemViewModel;
import za.co.nedbank.payment.vas.common.view.model.response.tag.TagViewModel;
import za.co.nedbank.payment.vas.dailylotto.utils.LottoConstants;
import za.co.nedbank.payment.vas.electricity.model.ElectricityViewModel;
import za.co.nedbank.payment.vas.electricity.navigator.ElectricityNavigatorTarget;
import za.co.nedbank.payment.vas.electricity.utils.ElectricityConstants;
import za.co.nedbank.payment.vas.electricity.utils.ElectricityUtility;
import za.co.nedbank.payment.vas.prepaid.navigator.PrepaidNavigatorTarget;
import za.co.nedbank.payment.vas.prepaid.utils.PrepaidConstants;
import za.co.nedbank.payment.vas.prepaid.view.model.PrepaidPurchaseViewModel;
import za.co.nedbank.payment.vas.vouchers.model.VoucherPurchaseViewModel;
import za.co.nedbank.payment.vas.vouchers.navigator.VouchersNavigatorTarget;
import za.co.nedbank.payment.vas.vouchers.utils.VoucherConstants;
import za.co.nedbank.profile.view.ProfileConstants;
import za.co.nedbank.profile.view.navigation.ProfileNavigationTarget;
import za.co.nedbank.profile.view.tracking.ProfileTracking;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.card.CardDataModel;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.usecase.account_management.GetLimitSettingUseCase;
import za.co.nedbank.services.domain.usecase.cards.GetCardsUseCase;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.services.insurance.domain.data.request.generic.insured_property.NonNedbankInsuredRequestDataModel;
import za.co.nedbank.services.insurance.domain.usecase.common.GetAllowedCoverAmountUseCase;
import za.co.nedbank.services.insurance.domain.usecase.common.GetNonNedbankInsuredPropertiesUseCase;
import za.co.nedbank.services.insurance.domain.usecase.my_cover.NIFPMyCoverAllowCoverUseCase;
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants;
import za.co.nedbank.services.insurance.view.other.enums.generic.InsuranceProductType;
import za.co.nedbank.services.insurance.view.other.mapper.dashboard.amount.AllowedAmountResponseDataToViewMapper;
import za.co.nedbank.services.insurance.view.other.mapper.dashboard.cover_amount.AllowedCoverAmountResponseDataToViewModelMapper;
import za.co.nedbank.services.insurance.view.other.mapper.generic.insured_property.NonNedbankInsuredPropertiesDomainToViewMapper;
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.amount.AllowedAmountResponseViewModel;
import za.co.nedbank.services.insurance.view.other.model.response.dashboard.cover_amount.AllowedCoverAmountResponseViewModel;
import za.co.nedbank.services.insurance.view.other.model.response.generic.insured_property.NonNedbankInsuredPropertiesViewModel;
import za.co.nedbank.services.view.mapper.LimitSettingDataModelToLimitSettingViewModelMapper;
import za.co.nedbank.services.view.model.limitSetting.LimitSettingViewModel;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.domain.model.AvoWalletDetailsModel;
import za.co.nedbank.ui.domain.usecase.GetAvoWalletDetailsUseCase;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;
import za.co.nedbank.core.deeplink.DeeplinkUtils;

public class NavigationHandlerPresenter extends NBBasePresenter<NavigationHandlerView> {

    private static final String TAG = NavigationHandlerPresenter.class.getSimpleName();
    private final NavigationRouter navigationRouter;
    private final ApplicationStorage mMemoryApplicationStorage;
    private final APIInformation mApiInformation;
    private final Analytics mAnalytics;
    private final FeatureSetController mFeatureSetController;
    private final ApplicationStorage mApplicationStorage;
    private final GetUserDetailUseCase mGetUserDetailsUseCase;
    private final GetOverviewUseCase mGetOverviewUseCase;
    private final GetEmcertIdUseCase mGetEmcertIdUseCase;
    private final GetProfileUseCase mGetProfileUseCase;
    private final GetVasOfferingsUseCase mGetVasOfferingsUsecase;
    private final RecentPaymentUseCase mRecentPaymentsUseCase;
    private final SwitchContextDataViewModelMapper mSwitchContextDataViewModelMapper;
    private final RecentPaymentResponseDataToViewModelMapper mRecentPaymentResponseDataToViewModelMapper;
    private final GetMdmProfileUseCase mdmProfileUseCase;
    private final GetFedarationListUseCase mGetFedarationsListUseCase;
    private final GetAllProductsUseCase mGetAllProductsUseCase;
    private final NIFPMyCoverAllowCoverUseCase mNifpMyCoverAllowCoverUseCase;
    private final AllowedAmountResponseDataToViewMapper mNifpMyCoverAmountResponseDataToViewModelMapper;
    private final TagItemDataToViewModelMapper tagItemDataToViewModelMapper;
    private final AllProductResponseDataModelToViewModelMapper mAllProductResponseDataModelToViewModelMapper;
    private final GetAvoWalletDetailsUseCase mWalletDetailsUseCase;
    private final ProductRequestViewModelToDataMapper mProductRequestViewModelToDataMapper;
    private final ProductUseCase mProductUsecase;
    private final ProductResponseDataToViewModelMapper mProductResponseDataToViewModelMapper;
    private final GetAllowedCoverAmountUseCase mGetAllowedCoverAmountUseCase;
    private final AllowedCoverAmountResponseDataToViewModelMapper mAllowedCoverAmountResponseDataToViewModelMapper;
    private final NonNedbankInsuredPropertiesDomainToViewMapper mNonNedbankInsuredPropertiesDomainToViewMapper;
    private final GetTagListUseCase mGetTagListUsecase;
    private final TagIdentifierRequestViewModelToDataMapper mtagIdentifierRequestViewModelToDataMapper;
    private final TagDataToViewModelMapper mTagDataToViewModelMapper;
    private String mAccountId;
    private UserProfile mUserProfile;
    private final LoginSecurityUseCase mLoginSecurityUsecase;
    private final ITADeviceManagementUseCase mItaDeviceManagementUseCase;
    private RecentPaymentResponseViewModel mRecipientPaymentViewModel;
    private RecentPaymentResponseViewModel mOnceOffPaymentViewModel;
    private List<AccountViewModel> mInsuranceAccountList;
    private final GetNonNedbankInsuredPropertiesUseCase mNonNedbankInsuredPropertiesUseCase;
    private final StorageUtility storageUtility;
    private List<NonNedbankInsuredPropertiesViewModel> mPropertiesViewModels;
    private final OpenNewAccUserEntriesViewModel userEntriesViewModel = new OpenNewAccUserEntriesViewModel();
    private final GetAccountsUseCase mGetAccountsUseCase;
    private final UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper;
    private final ApplicantIdForeignCheckValidator mApplicantIdForeignCheckValidator;
    private final List<AccountDto> creditCardAccountDtoList = new ArrayList<>();
    private final GetCardsUseCase mGetCardsUseCase;
    private final GetLimitSettingUseCase mGetLimitSettingUseCase;
    private static final String OD_VALIDATION_TYPE = "Overdraft";
    private static final String SELF = "SELF";
    private final LimitSettingDataModelToLimitSettingViewModelMapper mToLimitSettingViewModelMapper;
    private final UserDetailsUsecase mUserDetailsUsecase;
    private final CheckUserEligibilityUseCase checkUserEligibilityUseCase;
    private final NotificationAdapter notificationAdapter;

    @Inject
    public NavigationHandlerPresenter(final UserDetailsUsecase userDetailsUsecase,
                                      final LimitSettingDataModelToLimitSettingViewModelMapper toLimitSettingViewModelMapper,
                                      final GetLimitSettingUseCase getLimitSettingUseCase,
                                      final GetCardsUseCase getCardsUseCase,
                                      final NavigationRouter navigationRouter,
                                      final APIInformation apiInformation,
                                      final GetUserDetailUseCase getUserDetailUseCase,
                                      final GetOverviewUseCase getOverviewUseCase,
                                      final GetProfileUseCase getProfileUseCase,
                                      final GetEmcertIdUseCase getEmcertIdUseCase,
                                      final GetVasOfferingsUseCase getVasOfferingsUseCase,
                                      final GetMdmProfileUseCase getMdmProfileUseCase,
                                      final RecentPaymentUseCase recentPaymentUseCase,
                                      final GetAllProductsUseCase getAllProductsUseCase,
                                      final GetNonNedbankInsuredPropertiesUseCase getNonNedbankInsuredPropertiesUseCase,
                                      final NIFPMyCoverAllowCoverUseCase nifpMyCoverAllowCoverUseCase,
                                      final ProductUseCase productUseCase,
                                      final ProductRequestViewModelToDataMapper productRequestViewModelToDataMapper,
                                      final ProductResponseDataToViewModelMapper productResponseDataToViewModelMapper,
                                      final AllowedAmountResponseDataToViewMapper nifpMyCoverAmountResponseDataToViewModelMapper,
                                      final AllProductResponseDataModelToViewModelMapper allProductResponseDataModelToViewModelMapper,
                                      final RecentPaymentResponseDataToViewModelMapper recentPaymentResponseDataToViewModelMapper,
                                      final SwitchContextDataViewModelMapper switchContextDataViewModelMapper,
                                      final GetFedarationListUseCase getFedarationListUseCase,
                                      final TagItemDataToViewModelMapper tagItemDataToViewModelMapper,
                                      final GetAllowedCoverAmountUseCase getAllowedCoverAmountUseCase,
                                      final NonNedbankInsuredPropertiesDomainToViewMapper nonNedbankInsuredPropertiesDomainToViewMapper,
                                      final AllowedCoverAmountResponseDataToViewModelMapper allowedCoverAmountResponseDataToViewModelMapper,
                                      final FeatureSetController featureSetController,
                                      final ApplicationStorage applicationStorage,
                                      final @Named("memory") ApplicationStorage memoryApplicationStorage,
                                      final Analytics analytics,
                                      final LoginSecurityUseCase loginSecurityUseCase,
                                      final ITADeviceManagementUseCase itaDeviceManagementUseCase,
                                      final GetAvoWalletDetailsUseCase walletDetailsUseCase,
                                      final GetTagListUseCase getTagListUseCase,
                                      final TagIdentifierRequestViewModelToDataMapper tagIdentifierRequestViewModelToDataMapper,
                                      final TagDataToViewModelMapper tagDataToViewModelMapper,
                                      final StorageUtility storageUtility,
                                      final GetAccountsUseCase getAccountsUseCase,
                                      final ApplicantIdForeignCheckValidator applicantIdForeignCheckValidator,
                                      final UserDetailDataToViewModelMapper userDetailDataToViewModelMapper,
                                      final CheckUserEligibilityUseCase checkUserEligibilityUseCase,
                                      final NotificationAdapter notificationAdapter) {
        this.navigationRouter = navigationRouter;
        this.mApiInformation = apiInformation;
        this.mFeatureSetController = featureSetController;
        this.mApplicationStorage = applicationStorage;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.mGetUserDetailsUseCase = getUserDetailUseCase;
        this.mGetOverviewUseCase = getOverviewUseCase;
        this.mGetEmcertIdUseCase = getEmcertIdUseCase;
        this.mGetProfileUseCase = getProfileUseCase;
        this.mGetFedarationsListUseCase = getFedarationListUseCase;
        this.mdmProfileUseCase = getMdmProfileUseCase;
        this.mRecentPaymentsUseCase = recentPaymentUseCase;
        this.mGetAllowedCoverAmountUseCase = getAllowedCoverAmountUseCase;
        this.mNonNedbankInsuredPropertiesUseCase = getNonNedbankInsuredPropertiesUseCase;
        this.mGetAllProductsUseCase = getAllProductsUseCase;
        this.mNifpMyCoverAllowCoverUseCase = nifpMyCoverAllowCoverUseCase;
        this.mProductUsecase = productUseCase;
        this.mProductRequestViewModelToDataMapper = productRequestViewModelToDataMapper;
        this.mProductResponseDataToViewModelMapper = productResponseDataToViewModelMapper;
        this.mNifpMyCoverAmountResponseDataToViewModelMapper = nifpMyCoverAmountResponseDataToViewModelMapper;
        this.mAllProductResponseDataModelToViewModelMapper = allProductResponseDataModelToViewModelMapper;
        this.mNonNedbankInsuredPropertiesDomainToViewMapper = nonNedbankInsuredPropertiesDomainToViewMapper;
        this.mAllowedCoverAmountResponseDataToViewModelMapper = allowedCoverAmountResponseDataToViewModelMapper;
        this.mRecentPaymentResponseDataToViewModelMapper = recentPaymentResponseDataToViewModelMapper;
        this.mSwitchContextDataViewModelMapper = switchContextDataViewModelMapper;
        this.mAnalytics = analytics;
        this.mLoginSecurityUsecase = loginSecurityUseCase;
        this.mGetVasOfferingsUsecase = getVasOfferingsUseCase;
        this.mGetTagListUsecase = getTagListUseCase;
        this.mTagDataToViewModelMapper = tagDataToViewModelMapper;
        this.tagItemDataToViewModelMapper = tagItemDataToViewModelMapper;
        this.mItaDeviceManagementUseCase = itaDeviceManagementUseCase;
        this.mWalletDetailsUseCase = walletDetailsUseCase;
        this.mtagIdentifierRequestViewModelToDataMapper = tagIdentifierRequestViewModelToDataMapper;
        this.storageUtility = storageUtility;
        this.mGetAccountsUseCase = getAccountsUseCase;
        this.mUserDetailDataToViewModelMapper = userDetailDataToViewModelMapper;
        this.mGetCardsUseCase = getCardsUseCase;
        this.mGetLimitSettingUseCase = getLimitSettingUseCase;
        this.mToLimitSettingViewModelMapper = toLimitSettingViewModelMapper;
        this.mUserDetailsUsecase = userDetailsUsecase;
        this.mApplicantIdForeignCheckValidator = applicantIdForeignCheckValidator;
        this.checkUserEligibilityUseCase = checkUserEligibilityUseCase;
        this.notificationAdapter = notificationAdapter;
    }

    void clearNavigationData() {
        mMemoryApplicationStorage.clearValue(NotificationConstants.STORAGE_KEYS.AJO_NOTIFICATION_LINK);
    }

    void handleOnClick(AjoPushPayloadDataModel.ActionButton selectedResponse) {
        String action = getActionName(selectedResponse.getLink());
        if (DeeplinkUtils.canMoveToDeeplinkFramework(action)) {
            prepareDataAndSendToAdapter(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_INVESTMENT_ACCOUNT)) {
            handleInvestmentAccountFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.NON_TP_JOIN_FAMILY_BANKING)) {
            handleNonTpJoinFamilyBankingFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SHARE_MONEY_APP)) {
            handleShareMoneyAppFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.RESPOND_FAMILY_BANKING)) {
            handleFamilyBankingFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.CREDIT_SCORE_DASHBOARD)) {
            handleCreditScoreFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SMART_MONEY_DASHBOARD)) {
            handleSmartMoneyDashboardFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BIOMETRIC_SETTINGS)) {
            handleOpenBiometricSettingFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PERSONAL_LOAN)) {
            handleApplyPersonalLoanFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.JOIN_FAMILY_BANKING)) {
            handleJoinFamilyBanking();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.LATEST)) {
            openCovid19();
        } else {
            handleOnClick1(selectedResponse, action);
        }
    }

    private String getActionName(String link) {
        String actionName = StringUtils.EMPTY_STRING;
        if (!StringUtils.isNullOrEmpty(link)) {
            Uri uri = Uri.parse(link);
            if (uri != null) {
                actionName = uri.getLastPathSegment();
            }
            if (StringUtils.isNullOrEmpty(actionName) && link.contains("://")) {
                actionName = link.split("://")[1];
            }
            if (StringUtils.isNullOrEmpty(actionName)) {
                actionName = link;
            }
        }

        return StringUtils.isNotEmpty(actionName) ? actionName : StringUtils.EMPTY_STRING;
    }

    private void handleOnClick1(AjoPushPayloadDataModel.ActionButton selectedResponse, String action) {
        if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OFFERS_FOR_YOU)) {
            handleOffersForYouClick();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_LOTTO)) {
            String birthDateParam = storageUtility.getDOB();
            if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS)) {
                handleNavigationVasToggleOffFlow(true, birthDateParam);
            } else if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_LOTTERY_SHOW_GAME)) {
                getSelectLottoGameFlowFromToggleFlag(birthDateParam);
            } else {
                handleNavigationErrorsFlow();
            }
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.CREDIT_SHORTFALL_EDUCATION)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_FUNERAL_POLICY)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PERSONAL_ACCIDENT_COVER)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_LIFE_COVER)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_HOME_OWNERS_COVER)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_JUST_INVEST_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_32DAY_NOTICE_ACCOUNT)) {
            getUserDetails(action);
        } else {
            handleOnClick2(selectedResponse, action);
        }
    }

    private void handleOnClick2(AjoPushPayloadDataModel.ActionButton selectedResponse, String action) {
        if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MONEY_TRADER)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_EASY_ACCESS_DEPOSIT_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PRIME_SELECT_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_EFIXED_DEPOSIT_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PLATINUM_FIXED_DEPOSIT_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PLATINUM_INVEST_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase((NotificationConstants.NAVIGATION_TARGET.UPDATE_NED_ID_DETAILS))) {
            handleUpdateNedIdDetails();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_KIDS_ACCOUNT)) {
            handleOpenKidsAccountFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_PPW)) {
            mMemoryApplicationStorage.putBoolean(StorageKeys.IS_PPW_FLOW, true);
            String birthDateParam = storageUtility.getDOB();
            getVasOfferings(birthDateParam, VasConstants.VasOfferingsCodes.ELECTRICITY);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_UTILITY)) {
            String birthDateParam = storageUtility.getDOB();
            getVasOfferings(birthDateParam, NotificationConstants.NAVIGATION_TARGET.BUY_UTILITY);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_PPE)) {
            String birthDateParam = storageUtility.getDOB();
            handleBuyPpeFlow(birthDateParam);
            resetVasLimitsAndAccounts();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_PREPAIDS)) {
            String birthDateParam = storageUtility.getDOB();
            handleBuyPrepaid(birthDateParam);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.BUY_VOUCHER)) {
            String birthDateParam = storageUtility.getDOB();
            handleBuyVasVoucherFlow(birthDateParam);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.VIEW_STATEMENTS)) {
            handleViewStatementsFlow();
        } else {
            handleOnClick3(selectedResponse, action);
        }
    }

    private void handleOnClick3(AjoPushPayloadDataModel.ActionButton selectedResponse, String action) {
        if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.ITA_SETTINGS)) {
            handleITASettingFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.NOTIFICATION_PREFS)) {
            handleNotificationPrefFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.RETENTION_JOURNEY)) {
            handleRetentionFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.NON_TP_PRODUCTS)) {
            handleNonTPProductFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.PROFILE_DETAILS)) {
            handleProfileDetailFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.ATM_BRANCH_LOCATOR)) {
            handleATMBranchLocatorFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.GET_CASH)) {
            handleGetCashFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.RECENT_PAYMENTS)) {
            handleRecentPaymentsFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.CREDIT_HEALTH)) {
            mApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME, NotificationConstants.NAVIGATION_TARGET.CREDIT_HEALTH);
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, action));
            close();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.MONEY_TRACKER_DASHBOARD)) {
            mApplicationStorage.putBoolean(IS_NEW_TAG_MT, false);
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MONEY_TRACKER_FLOW_NAVIGATION_ACTIVITY));
            close();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.VIEW_GREENBACKS)) {
            mApplicationStorage.putString(NotificationConstants.STORAGE_KEYS.NAVIGATION_TARGET_HOME, NotificationConstants.NAVIGATION_TARGET.VIEW_GREENBACKS);
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, action));
            close();
        } else {
            handleOnClick4(selectedResponse, action);
        }
    }

    private void handleOnClick4(AjoPushPayloadDataModel.ActionButton selectedResponse, String action) {
        if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SEND_MONEY)) {
            handleSendMoneyFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.SETUP_APPOINTMENT)) {
            handleSetupAppointmentFlow();
        } else if (action.equalsIgnoreCase(AjoPushPayloadDataModel.ActionButtonType.DISMISS)) {
            handleNavigationErrorsFlow();
        } else if (action.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_PAYSHAPP)) {
            handlePayShap();
        } else if (action.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_DISCS_FINES)) {
            handleDiscsFines();
        } else if (action.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_HOME_LOAN)) {
            handleHomeLoan();
        } else if (action.equalsIgnoreCase(ChatbotConstants.INTENT_IDENTIFIERS.INTENT_POS_APPLICATIONS)) {
            handlePOSApplications();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_MI_GOALS_PREMIUM)) {
            handleMiGoalsFlow(za.co.nedbank.enroll_v2.Constants.MiGoals.MIGOALS_PREMIUM);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_MI_GOALS)) {
            handleMiGoalsFlow(za.co.nedbank.enroll_v2.Constants.MiGoals.MIGOALS);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_MI_GOALS_PLUS)) {
            handleMiGoalsFlow(za.co.nedbank.enroll_v2.Constants.MiGoals.MIGOALS_PLUS);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.MY_COVER_BUILD_YOUR_OWN_PACKAGE)
                || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.MY_COVER_FIXED_FAMILY_PACKAGE)
                || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.MY_COVER_R10000_INDIVIDUAL_PACKAGE)
                || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.MY_COVER_R30000_INDIVIDUAL_PACKAGE)
        ) {
            handleInsuranceFlow(action, InsuranceConstants.InsuranceTypeConstant.FUNERAL);
        } else {
            handleOnClick5(selectedResponse, action);
        }
    }

    private void handleOnClick5(AjoPushPayloadDataModel.ActionButton selectedResponse, String action) {
        if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_PERSONAL_LINES_MOTOR)
                || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_PERSONAL_LINES_BUILDINGS)
                || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_PERSONAL_LINES_HOUSE_CONTENT)
                || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_COMPREHENSIVE_WARRANTY)
                || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_GAP_WARRANTY)
                || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_ESSENTIAL_WARRANTY)) {
            getAccountIdandInitiateFlow("", action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.MY_PREPAID_ADVANCE_HISTORY)) {
            handleMyPrepaidAdvanceHistoryFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.INSURANCE_REVIEW_APPLICATIONS)) {
            handleInsuranceApplicationReviewFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_JUST_SAVE_ACCOUNT)) {
            getUserDetails(action);
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.CREDIT_CARD_PRODUCT)) {
            navigateToApplyCreditCard();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.INCREASE_OD_LIMIT)) {
            getOverdraftLimitSettingData();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_AVO_IN_APP)) {
            moveToShop();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.VIEW_EXPIRY_RTP_REQUEST)
                || action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.RECEIVED_PAYSHAP_REQUEST)) {
            navigateToPayshapRequest();
        } else {
            handleOnClick6(selectedResponse, action);
        }
    }

    private void handleOnClick6(AjoPushPayloadDataModel.ActionButton selectedResponse, String action) {
      if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.OPEN_PROOF_OF_ACCOUNT)) {
            openProofOfAccountFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_NEW_OVERDRAFT)) {
            handleApplyOverdraftFlow();
        } else if (action.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.AVO_TERM_AGREEMENT)) {
            handleAvoTermAgreement();
        } else {
            NavigationTarget target = resolveNavigationTarget(selectedResponse);
            if (target != null) {
                navigationRouter.navigateTo(target.withIntentFlagClearTopSingleTop(true));
                close();
            } else {
                handleNavigationErrorsFlow();
            }
        }
    }

    private void navigateToPayshapRequest() {
        NavigationTarget targetScreen = NavigationTarget.to(NavigationTarget.MAKE_PAYSHAP_REQUEST)
                .withParam(RtpNavigator.REQUEST_ID, getValueFromParamHashMap(NotificationConstants.ADDITIONAL_PARAM.UETR));
        navigationRouter.navigateTo(targetScreen);
        close();
    }


    @SuppressLint("CheckResult")
    private void getCardAccountAndInitiateFlow() {
        String cardNumber = getValueFromParamHashMap(NotificationConstants.ADDITIONAL_PARAM.CARD_NUMBER);
        if (cardNumber == null) {
            handleNavigationErrorsFlow();
            return;
        }
        mGetCardsUseCase.execute().compose(bindToLifecycle())
                .subscribe(cardDataModels -> {
                    if (CollectionUtils.isNotEmpty(cardDataModels) && view != null) {
                        CardDataModel cardDataModel = getSelectedCardDataModel(cardDataModels, cardNumber);
                        getUserPartyId(cardDataModel);
                    } else {
                        handleNavigationErrorsFlow();
                    }
                }, this::handleErrors);
    }

    @SuppressLint("CheckResult")
    public void getUserPartyId(CardDataModel cardDataModel) {
        if (cardDataModel == null) {
            handleNavigationErrorsFlow();
            return;
        }
        mUserDetailsUsecase
                .execute(APIInformation.getInstance().getToken(), SELF)
                .compose(bindToLifecycle())
                .subscribe(userDetailsResponse -> {
                    if (userDetailsResponse != null && userDetailsResponse.getData().getSecurityDetails() != null &&
                            userDetailsResponse.getData().getSecurityDetails().getEnterpriseCustomerNumber() != null) {
                        navigateToWebView(cardDataModel, userDetailsResponse.getData().getSecurityDetails().getEnterpriseCustomerNumber());
                    }
                }, this::handleErrors);
    }

    String isLinkedCreditCard(String ccAccountNumber, String plasticNo) {
        String itemAccountId = StringUtils.EMPTY_STRING;
        ccAccountNumber = !StringUtils.isNullOrEmpty(ccAccountNumber) ? ccAccountNumber : plasticNo;
        if (!StringUtils.isNullOrEmpty(ccAccountNumber) && CollectionUtils.isNotEmpty(this.creditCardAccountDtoList)) {
            List<AccountDto> cardAccountsList = new ArrayList<>();
            for (AccountDto account : creditCardAccountDtoList) {
                if (account != null && !StringUtils.isNullOrEmpty(account.number) && (account.number.equalsIgnoreCase(ccAccountNumber) || account.number.equalsIgnoreCase(plasticNo))) {
                    cardAccountsList.add(account);
                }
            }
            itemAccountId = cardAccountsList.isEmpty() ? StringUtils.EMPTY_STRING : cardAccountsList.get(0).id;
        }
        return itemAccountId;
    }

    public void navigateToWebView(CardDataModel cardDataModel, String partyID) {
        String itemAccountId = isLinkedCreditCard(cardDataModel.getAccountNumber(), cardDataModel.getCardNumber());
        if (StringUtils.isNullOrEmpty(itemAccountId) || StringUtils.isNullOrEmpty(partyID)) {
            handleNavigationErrorsFlow();
            return;
        }
        NavigationTarget navigationTarget;
        if (validateCreditCardId(cardDataModel.getProductId())) {
            navigationTarget = NavigationTarget.to(NavigationTarget.NTF_WEBVIEW_ACTIVITY);
        } else {
            navigationTarget = NavigationTarget.to(NavigationTarget.CREDIT_LIMIT_INCREASE_ENTRY);
        }
        navigationTarget.withParam(za.co.nedbank.core.Constants.LimitIncreaseBundleKeys.PARTY_ID, partyID);
        navigationTarget.withParam(za.co.nedbank.core.Constants.LimitIncreaseBundleKeys.TOKEN, APIInformation.getInstance().getToken());
        navigationTarget.withParam(za.co.nedbank.core.Constants.LimitIncreaseBundleKeys.EXISTING_ACCOUNT_NUMBER, cardDataModel.getAccountNumber());
        navigationTarget.withParam(za.co.nedbank.core.Constants.LimitIncreaseBundleKeys.EXISTING_PRODUCT_ID, cardDataModel.getProductId());
        navigationTarget.withParam(za.co.nedbank.core.Constants.LimitIncreaseBundleKeys.IS_FROM_CREDIT_LIMIT_INCREASE, true);
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.SELECTED_BANK_ID, itemAccountId);
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.BUNDLE_PARENT_ACCOUNT_NUMBER, cardDataModel.getAccountNumber());
        navigationTarget.withParam(za.co.nedbank.core.Constants.PRODUCT_ID, cardDataModel.getProductId());
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.CARD_DESCRIPTION, cardDataModel.getDescription());
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FROM_CLI_CLICK, true);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    private CardDataModel getSelectedCardDataModel(List<CardDataModel> cardDataModelList, String cardNumber) {
        CardDataModel cardDataModel = null;
        for (CardDataModel model : cardDataModelList) {
            if (model.getCardNumber().equalsIgnoreCase(cardNumber)) {
                cardDataModel = model;
                break;
            }
        }
        return cardDataModel;
    }


    /*[CODE START]
     * Apply credit card*/
    void navigateToApplyCreditCard() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.CREDIT_CARD_FACILITY)) {
            NavigationTarget navigationTarget = NavigationTarget.to(EnrollV2NavigatorTarget.BROWSE_CREDIT_CARD_ACTIVITY);
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FROM_APPLY_JOURNEY, za.co.nedbank.core.Constants.FLOW_CONSTANTS.CREDIT_CARD_FLOW);
            navigationTarget.withParam(NavigationTarget.IS_DEEPLINK, true);
            navigationRouter.navigateTo(navigationTarget);
        } else {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.PREAPPROVED_OFFERS_APPLY_ACTIVITY);
            navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.NFPCreditCardParam.IS_PRE_LOGIN, za.co.nedbank.core.Constants.FLOW_CONSTANTS.CREDIT_CARD_FLOW);
            navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FROM_APPLY_JOURNEY, za.co.nedbank.core.Constants.FLOW_CONSTANTS.CREDIT_CARD_FLOW);
            navigationRouter.navigateTo(navigationTarget);
        }
        close();
    }

    /*[CODE START]
     * Value added offers
     * Manage Overdraft*/
    @SuppressLint("CheckResult")
    private void getOverdraftLimitSettingData() {
        String accountId = getValueFromParamHashMap(NotificationConstants.ADDITIONAL_PARAM.ACCOUNT_ID);
        if (accountId == null) {
            handleNavigationErrorsFlow();
            return;
        }
        mGetLimitSettingUseCase.execute(OD_VALIDATION_TYPE)
                .compose(bindToLifecycle())
                .subscribe(limitSettingDataModel -> getUserDetailsForManageOD(mToLimitSettingViewModelMapper.mapLimitSettingsDataModel(limitSettingDataModel), accountId), this::handleErrors);
    }

    @SuppressLint("CheckResult")
    private void getUserDetailsForManageOD(LimitSettingViewModel limitSettingViewModel, String accountId) {
        if (limitSettingViewModel == null) {
            handleNavigationErrorsFlow();
            return;
        }
        mGetUserDetailsUseCase
                .execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetailsData ->
                                navigateToManageOverdraft(userDetailsData, limitSettingViewModel, accountId),
                        this::handleErrors);

    }

    private void navigateToManageOverdraft(UserDetailData userDetailData, LimitSettingViewModel limitSettingViewModel, String accountId) {
        if (userDetailData == null) {
            handleNavigationErrorsFlow();
            return;
        }
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.MANAGE_OVERDRAFT);
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_ACCOUNT_ID, accountId);
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_EMAIL_VALUE, userDetailData.getEmailAddress());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_CELL_PHONE, userDetailData.getCellNumber());
        navigationTarget.withParam(za.co.nedbank.services.Constants.BUNDLE_DAILY_LIMIT_VALUE, limitSettingViewModel);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    /*Method to get required value on the basis of provided key from the Params Hashmap*/
    private String getValueFromParamHashMap(String key) {
        if (view == null) {
            return null;
        }
        HashMap<String, String> paramHashmap = view.getParamHashmap();
        if (paramHashmap == null) {
            return null;
        }
        return paramHashmap.get(key);
    }

    private void handleInsuranceApplicationReviewFlow() {
        Observable.zip(mGetOverviewUseCase.execute(), mGetUserDetailsUseCase.execute(false),
                        (overview, userDetailData) -> {
                            if (overview != null && userDetailData != null) {
                                handleInsuranceAndUserDetail(overview);
                            }
                            return userDetailData;
                        }).compose(bindToLifecycle())
                .subscribe(this::navigateToInsuranceApplicationReview,
                        throwable -> close()
                );
    }

    private void handleInsuranceAndUserDetail(CachableValue<Overview> overview) {
        if (overview.get() != null && CollectionUtils.isNotEmpty(overview.get().accountsOverviews)) {
            for (AccountsOverview accountsOverview : overview.get().accountsOverviews) {
                if ((accountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && accountsOverview.insuranceAccountList != null) {
                    mInsuranceAccountList = accountsOverview.insuranceAccountList;
                }
            }
        }
    }

    private void navigateToInsuranceApplicationReview(UserDetailData userDetailData) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN)
                .withParam(ServicesNavigationTarget.PARAM_VALID_INSURANCE_POLICY, isInsuranceExist(userDetailData))
                .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, mInsuranceAccountList)
                .withParam(InsuranceConstants.BundleKeys.FROM_DEEPLINK, true);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    boolean isInsuranceExist(UserDetailData userDetailData) {
        boolean isValidRSAId = mApplicantIdForeignCheckValidator.validateInput(userDetailData.getIdOrTaxIdNumber()).isOk();
        boolean isBusinessUser = StringUtils.isNotEmpty(userDetailData.getClientType()) && Integer.parseInt(userDetailData.getClientType())
                > za.co.nedbank.core.Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;
        int age = getAgeFromRsaId(userDetailData.getIdOrTaxIdNumber());
        return (!mFeatureSetController.isFeatureDisabled(FeatureConstants.INSURANCE)
                && age >= FormattingUtil.AGE_LIMIT && !isBusinessUser && isValidRSAId);
    }

    int getAgeFromRsaId(String idOrTaxIdNoStr) {
        if (StringUtils.isNotEmpty(idOrTaxIdNoStr) && idOrTaxIdNoStr.length() > 6) {
            String mBirthDate = AppUtility.getDateOfBirthFromRsaId(idOrTaxIdNoStr);
            return AppUtility.calculateAge(mBirthDate + ZERO_TIME_FORMAT);
        }
        return za.co.nedbank.services.Constants.ZERO;
    }

    private void handleMyPrepaidAdvanceHistoryFlow() {
        navigationRouter.navigateTo(
                NavigationTarget.to(PrepaidNavigatorTarget.PREPAID_TRANSACTIONS)
                        .withParam(PREPAID_ADVANCE_FLOW, true)
        );
        close();
    }

    public void handlePayShap() {
        mGetAccountsUseCase.execute(IAccountOptions.PAYMENT_ACCOUNTS)
                .compose(bindToLifecycle())
                .subscribe(payAccountsList ->
                        {
                            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHAP_ID_MANAGEMENT)
                                    .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.PAYMENTV3_ACCOUNTS, payAccountsList));
                            close();
                        },
                        throwable -> handleNavigationErrorsFlow());
    }

    public void handleDiscsFines() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.VEHICLES_LANDING));
        close();
    }

    public void handleHomeLoan() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME_LOAN_TOOLKIT));
        close();
    }

    public void handlePOSApplications() {
        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.APPLY_INSESSION));
        close();
    }

    public void handleSetupAppointmentFlow() {
        mGetProfileUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(this::handleSetupAppointmentResponse, error -> handleNavigationErrorsFlow());
    }

    private void handleSetupAppointmentResponse(UserProfile userProfile) {
        if (userProfile != null) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NEW_APPOINTMENT)
                    .withParam(NavigationTarget.PARAM_SEC_OFFICER_CD, userProfile.getSecOfficerCd())
                    .withParam(NavigationTarget.PARAM_BOOKING_ENTRY_TYPE, BookingEntryType.GET_IN_TOUCH));
            close();
        } else {
            handleNavigationErrorsFlow();
        }
    }

    private void handleOffersForYouClick() {
        NavigationTarget navigationTarget = NavigationTarget.to(LoansNavigatorTarget.LOANS_BORROW_SCREEN);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    private void handleSendMoneyFlow() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SEND_CASH_LANDING));
        close();
    }

    private void handleViewStatementsFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_MOBSTAT_STATEMENTS)) {
            HashMap<String, Object> cdata = new HashMap<>();
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, TrackingEvent.ANALYTICS.VAL_GET_STATEMENTS);
            cdata.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_DOCUMENTTYPE, TrackingEvent.ANALYTICS.VAL_STATEMENTS);
            mAnalytics.sendEventActionWithMap(TrackingEvent.SD_STATEMENTS, cdata);
            navigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.DOWNLOAD_STATEMENT_ACTIVITY).withParam(ServicesNavigationTarget.PARAM_NOTIFICATION_ACCOUNT_NUMBER, mAccountId));
            close();
        } else {
            handleNavigationErrorsFlow();
        }
    }

    private void getSelectLottoGameFlowFromToggleFlag(String birthDateParam) {
        boolean isVasDailyLottoEnabled = !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_LOTTERY);
        if (isVasDailyLottoEnabled) {
            getVasOfferings(birthDateParam, VasConstants.VasOfferingsCodes.NEW_LOTTERY_GAMES);
        } else {
            handleBuyLottoFlow(birthDateParam);
        }
    }

    void onClickMyCoverLayout(InsuranceProductType insuranceProduct, List<AccountViewModel> accountList,
                              int productId, double allowedCoverAmount, double existingCoverAmount) {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_EDUCATION_SCREEN)
                .withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, insuranceProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, productId)
                .withParam(InsuranceConstants.ParamKeys.PARAM_ALLOWED_COVER_AMOUNT, allowedCoverAmount)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EXISTING_COVER_AMOUNT, existingCoverAmount)
                .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, accountList)
                .withParam(InsuranceConstants.BundleKeys.NON_NEDBANK_INSURANCE_PROPERTIES, mPropertiesViewModels);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    void onClickProductLayout(InsuranceProductType insuranceProduct, List<AccountViewModel> accountList,
                              int productId, double allowedCoverAmount, double existingCoverAmount) {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_EDUCATION_SCREEN);
        navigationTarget.withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, insuranceProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, productId)
                .withParam(InsuranceConstants.ParamKeys.PARAM_ALLOWED_COVER_AMOUNT, allowedCoverAmount)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EXISTING_COVER_AMOUNT, existingCoverAmount)
                .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, accountList)
                .withParam(InsuranceConstants.BundleKeys.NON_NEDBANK_INSURANCE_PROPERTIES, mPropertiesViewModels);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    private void handleRecentPaymentsFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_RECENT_PAYMENT)) {
            getRecentPayments();
        } else {
            handleNavigationErrorsFlow();
        }

    }

    void handleGetCashFlow() {
        navigationRouter.navigateTo(NavigationTarget.to(AtmNavigatorTarget.ATM_TUTORIAL_SCREEN));
        close();
    }

    public void getOnceOffPayments() {
        mRecentPaymentsUseCase.execute(za.co.nedbank.core.payment.recent.Constants.ONE, za.co.nedbank.core.payment.recent.Constants.ONCE_OFF)
                .compose(bindToLifecycle())
                .subscribe(onceOffPaymentsResponseData -> {
                            if (onceOffPaymentsResponseData != null)
                                mOnceOffPaymentViewModel = mRecentPaymentResponseDataToViewModelMapper.mapData(onceOffPaymentsResponseData);
                            handleViewMorePaymentClick();
                        }
                        , throwable -> handleViewMorePaymentClick());
    }

    public void getRecentPayments() {
        mRecentPaymentsUseCase.execute(za.co.nedbank.core.payment.recent.Constants.ONE, za.co.nedbank.core.payment.recent.Constants.SAVED)
                .compose(bindToLifecycle())
                .subscribe(this::processRecentPaymentsResponse
                        , throwable -> handleNavigationErrorsFlow());
    }

    private void processRecentPaymentsResponse(RecentPaymentResponseData recentPaymentsResponseData) {
        if (recentPaymentsResponseData != null) {
            mRecipientPaymentViewModel = mRecentPaymentResponseDataToViewModelMapper.mapData(recentPaymentsResponseData);
            if (mRecipientPaymentViewModel != null && mRecipientPaymentViewModel.getData() != null && !mRecipientPaymentViewModel.getData().isEmpty()) {
                getOnceOffPayments();
            } else {
                handleNavigationErrorsFlow();
            }
        } else {
            handleNavigationErrorsFlow();
        }
    }

    public void handleViewMorePaymentClick() {
        NavigationTarget mNavigationTarget = NavigationTarget.to(NavigationTarget.RECENT_PAYMENT_HISTORY);

        if (mRecipientPaymentViewModel != null && CollectionUtils.isNotEmpty(mRecipientPaymentViewModel.getData()))
            mNavigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.RECIPIENT_PAYMENT_DATA, mRecipientPaymentViewModel.getData());
        if (mOnceOffPaymentViewModel != null && mOnceOffPaymentViewModel.getMetaDataViewModel() != null)
            mNavigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.RECIPIENT_DATA_PAGE_LIMIT, mOnceOffPaymentViewModel.getMetaDataViewModel().getPageLimit());
        if (mOnceOffPaymentViewModel != null && CollectionUtils.isNotEmpty(mOnceOffPaymentViewModel.getData())) {
            mNavigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.ONCEOFF_PAYMENT_DATA, mOnceOffPaymentViewModel.getData());
        }
        mNavigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.CURRENT_PAGE, 0);
        if (mOnceOffPaymentViewModel != null && mOnceOffPaymentViewModel.getMetaDataViewModel() != null)
            mNavigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.ONCEOFF_DATA_PAGE_LIMIT, mOnceOffPaymentViewModel.getMetaDataViewModel().getPageLimit());

        navigationRouter.navigateTo(mNavigationTarget);
        close();
    }

    private void handleProfileDetailFlow() {
        if (!isMDMDisabled()) {
            getMdmProfileDetails();
        } else {
            getProfileDetails();
        }
    }

    public boolean isMDMDisabled() {
        return mFeatureSetController.isFeatureDisabled(FeatureConstants.MDMPROFILEDETAILS);
    }

    void getMdmProfileDetails() {
        mdmProfileUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(userProfile -> {
                    if (view != null) {
                        String rsaIDOrPassport;
                        if (!StringUtils.isNullOrEmpty(userProfile.getResident()) && userProfile.getResident().equalsIgnoreCase(FormattingUtil.SOUTH_AFRICA_CODE)) {
                            rsaIDOrPassport = userProfile.getRsaId();
                        } else {
                            rsaIDOrPassport = userProfile.getPassportNumber();
                        }
                        getFederationList(userProfile, rsaIDOrPassport);
                    }
                }, this::handleErrors);

    }

    void getProfileDetails() {
        mGetProfileUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(profileData -> {
                    if (view != null) {
                        String rsaIDOrPassport;
                        if (!StringUtils.isNullOrEmpty(profileData.getResident()) && profileData.getResident().equalsIgnoreCase(FormattingUtil.SOUTH_AFRICA_CODE)) {
                            rsaIDOrPassport = profileData.getRsaId();
                        } else {
                            rsaIDOrPassport = profileData.getPassportNumber();
                        }
                        getFederationList(profileData, rsaIDOrPassport);
                    }
                }, this::handleErrors);

    }

    private void getFederationList(UserProfile profile, String rsaIdOrPassport) {
        mGetFedarationsListUseCase.execute().subscribe(fedarationList -> {
            ArrayList<SwitchContextFedarationDetailsViewModel> switchContextFedarationDetailsViewModels =
                    (ArrayList<SwitchContextFedarationDetailsViewModel>) mSwitchContextDataViewModelMapper
                            .mapFedarationDetailResponse(fedarationList);
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NEW_PROFILE_DETAILS)
                    .withParam(ProfileNavigationTarget.PARAM_PROFILE_MODEL, switchContextFedarationDetailsViewModels.get(0))
                    .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.USER_PROFILE, profile)
                    .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_DEFAULT_PROFILE, switchContextFedarationDetailsViewModels.get(0).isDefaultFederation())
                    .withParam(za.co.nedbank.core.Constants.RSA_ID_OR_PASSPORT, rsaIdOrPassport)
                    .withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.IS_NON_TP_USER, isNonTpUser())
                    .withAllData(Boolean.TRUE));
            close();
        }, this::handleErrors);

    }


    private void handleErrors(Throwable throwable) {
        handleNavigationErrorsFlow();
    }

    public void handleATMBranchLocatorFlow() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.ATM_AND_BRANCHES_PROFILE).withParam(NavigationTarget.PARAM_IS_ATM_VISIBLE, true));
        close();
    }

    public void handleFamilyBankingFlow() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_INVITE_AND_ACCEPT_FAMILY_BANKING));
        close();
    }

    public void handleSmartMoneyDashboardFlow() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NFW_DASHBOARD_ACTIVITY));
        close();
    }

    public void handleCreditScoreFlow() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CREDIT_HEALTH_FLOW));
        close();
    }

    private void handleJoinFamilyBanking() {
        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.FAMILY_BANKING_STATUS_LOADING_SCREEN);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    private void handleApplyPersonalLoanFlow() {
        NavigationTarget mNavigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.PERSONAL_LOAN_WISHLIST_ACTIVITY);
        mNavigationTarget.withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.IS_PRE_LOGIN, false);
        mNavigationTarget.withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.IS_NON_NEDBANK_ID_FLOW, false);
        mNavigationTarget.withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.REDIRECT_FROM_SCREEN_NAME, StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(mNavigationTarget);
        close();
    }

    private void handleNonTPProductFlow() {
        getUserInfoForSavingsPocket();
    }

    void getUserInfoForSavingsPocket() {
        mGetUserDetailsUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetailData -> navigateToProductsList(),
                        throwable -> handleNavigationErrorsFlow());
    }

    void navigateToProductsList() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_MOA_IN_APP_ENABLED)) {
            NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.MOA_PRODUCT_LIST);
            navigationTarget.withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.PRODUCT_IDS, getValueFromParamHashMap(NotificationConstants.ADDITIONAL_PARAM.PRODUCT_IDS));
            navigationRouter.navigateTo(navigationTarget);
        }
        close();
    }

    private void handleNotificationPrefFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.NOTIFICATIONS_PREFERENCES)) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES));
            close();
        } else {
            handleNavigationErrorsFlow();
        }
    }

    private void handleITASettingFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_ITA)) {
            mItaDeviceManagementUseCase.execute()
                    .compose(bindToLifecycle()).subscribe(o -> {
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        } else {
            handleNavigationErrorsFlow();
        }
    }


    void handleBuyLottoFlow(String birthDateParam) {
        if (!mFeatureSetController.isFeatureDisabled(TRANSACT_LOTTO_PWB)) {
            NavigationTarget mNavigationTarget = NavigationTarget.to(NavigationTarget.VAS_FEATURE_UNAVAILABLE);
            mNavigationTarget.withParam(LottoConstants.EXTRAS.GAME_TYPE, LottoConstants.ConstantKey.DIRECT_GAME);
            mNavigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDateParam);
            mNavigationTarget.withParam(LottoConstants.EXTRAS.FLOW_TYPE, LottoConstants.LottoFlowType.NOTIFICATION);
            navigationRouter.navigateTo(mNavigationTarget);
            close();
        }
    }

    void openProofOfAccountFlow() {
        NavigationTarget targetScreen = NavigationTarget.to(DOWNLOAD_PROOF_OF_ACCOUNT_ACTIVITY)
                .withParam(PARAM_POA_ACCOUNT, getValueFromParamHashMap(NotificationConstants.ADDITIONAL_PARAM.POA_ACCOUNT))
                .withParam(PARAM_DOCUMENT_REFERENCE, getValueFromParamHashMap(NotificationConstants.ADDITIONAL_PARAM.POA_REFERENCE));
        navigationRouter.navigateTo(targetScreen);
        close();
    }

    private void handleOpenBiometricSettingFlow() {
        mLoginSecurityUsecase.execute(Boolean.FALSE)
                .compose(bindToLifecycle()).subscribe(loginSecurityDto -> {
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    public void handleBuyPpeFlow(String birthDate) {
        if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS)) {
            handleNavigationVasToggleOffFlow(true, birthDate);
        } else if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_ELECTRICITY)) {
            handleNavigationVasToggleOffFlow(false, birthDate);
        } else {
            getVasOfferings(birthDate, VasConstants.VasOfferingsCodes.ELECTRICITY);
        }
    }

    private void handleBuyVasVoucherFlow(String birthDate) {
        if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS)) {
            handleNavigationVasToggleOffFlow(true, birthDate);
        } else if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_VOUCHER)) {
            handleNavigationVasToggleOffFlow(false, birthDate);
        } else {
            getVasOfferings(birthDate, VasConstants.VasOfferingsCodes.VOUCHER);
        }
    }

    public void handleBuyPrepaid(String birthDate) {
        if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS)) {
            handleNavigationVasToggleOffFlow(true, birthDate);
        } else if (mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.VAS_PREPAID)) {
            handleNavigationVasToggleOffFlow(false, birthDate);
        } else {
            getVasOfferings(birthDate, VasConstants.VasOfferingsCodes.PREPAID);
        }
    }

    public void getVasOfferings(String birthDate, String vasProductType) {
        mGetVasOfferingsUsecase.execute()
                .compose(bindToLifecycle())
                .map(referenceDataResponseParentData -> tagItemDataToViewModelMapper.mapTagItemDataListToViewModelList(referenceDataResponseParentData.getData()))
                .subscribe(vasOfferingsListResponse -> {
                    if (null != view) {
                        if (CollectionUtils.isNotEmpty(vasOfferingsListResponse)) {
                            handleVasOfferingsSuccessResponse(vasOfferingsListResponse, birthDate, vasProductType);
                        } else {
                            handleNavigationErrorsFlow();
                        }
                    }
                }, throwable -> handleNavigationErrorsFlow());
    }

    private void handleVasOfferingsSuccessResponse(List<TagItemViewModel> vasOfferingsList, String birthDate, String vasProductType) {
        TagItemViewModel tagItemsViewModel;
        if (vasProductType.equals(NotificationConstants.NAVIGATION_TARGET.BUY_UTILITY)) {
            tagItemsViewModel = getVoucherViewModel(vasOfferingsList, VasConstants.VasOfferingsCodes.ELECTRICITY);
        } else {
            tagItemsViewModel = getVoucherViewModel(vasOfferingsList, vasProductType);
        }
        if (tagItemsViewModel != null) {
            handleRespectedVasFlow(tagItemsViewModel.getId(), birthDate, vasProductType);
        } else {
            handleNavigationErrorsFlow();
        }
    }

    public void getCategoriesList(Integer tagID) {
        Observable<TagData> observableCategories;
        observableCategories = mGetTagListUsecase.execute(mtagIdentifierRequestViewModelToDataMapper.mapTagIdentifierRequestViewModelToData(new TagIdentifierRequestViewModel(tagID)));

        observableCategories.compose(bindToLifecycle())
                .map(mTagDataToViewModelMapper::mapTagDataToViewModel)
                .subscribe(tagViewModel -> {
                    if (null != view) {
                        if (tagViewModel == null || CollectionUtils.isEmpty(tagViewModel.getTagRelationships())) {
                            handleNavigationErrorsFlow();
                            return;
                        }
                        if (mMemoryApplicationStorage.getBoolean(StorageKeys.IS_PPW_FLOW, false))
                            handlePPWNavigation(tagViewModel);
                        else
                            handlePELNavigation(tagViewModel.getTagRelationships());
                    }
                }, throwable -> handleNavigationErrorsFlow());
    }

    public void handlePELNavigation(List<TagViewModel> categories) {
        TagViewModel peltagViewModel = getPELTagViewModel(categories);

        if (peltagViewModel == null || peltagViewModel.getTag() == null) {
            handleNavigationErrorsFlow();
            return;
        }

        ElectricityViewModel electricityViewModel = new ElectricityViewModel();
        TagIdentifierRequestViewModel identifierRequestViewModel = new TagIdentifierRequestViewModel();
        identifierRequestViewModel.addIdToRequestList(peltagViewModel.getTag().getId());
        electricityViewModel.setTagIdentifierRequestViewModel(identifierRequestViewModel);
        if (CollectionUtils.isNotEmpty(peltagViewModel.getTag().getProducts())) {
            electricityViewModel.setProductList(peltagViewModel.getTag().getProducts());
            ProductViewModel productViewModel = getPELProductViewModel(peltagViewModel.getTag().getProducts());
            if (productViewModel != null) {
                electricityViewModel.setFbw(false);
                electricityViewModel.setElectricityFlow(true);
                electricityViewModel.setProductViewModel(productViewModel);
                electricityViewModel.setFbe(false);

                navigationRouter.navigateTo(NavigationTarget.to(ElectricityNavigatorTarget.ELECTRICITY_METER_DETAIL)
                        .withParam(ElectricityConstants.EXTRAS.ELECTRICITY_VIEW_MODEL, electricityViewModel));
                close();
            } else {
                handleNavigationErrorsFlow();
            }
        } else {
            handleNavigationErrorsFlow();
        }
    }

    void resetVasLimitsAndAccounts() {
        VasAccountsHolder.resetVasAccountsList();
        AvailableLimitHolder.resetVasLimits();
    }

    private void handleRespectedVasFlow(Integer id, String birthDate, String vasProductType) {
        if (VasConstants.VasOfferingsCodes.VOUCHER.equalsIgnoreCase(vasProductType)) {
            handleVoucherItemClick(id, birthDate);
        } else if (VasConstants.VasOfferingsCodes.PREPAID.equalsIgnoreCase(vasProductType)) {
            handleBuyPrepaidItemClick(id);
        } else if (VasConstants.VasOfferingsCodes.NEW_LOTTERY_GAMES.equalsIgnoreCase(vasProductType)) {
            NavigationTarget target = NavigationTarget.to(NavigationTarget.VAS_FEATURE_UNAVAILABLE);
            target.withParam(LottoConstants.EXTRAS.FLOW_TYPE, LottoConstants.LottoFlowType.NOTIFICATION);
            target.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDate);
            target.withParam(LottoConstants.EXTRAS.GAME_TYPE, LottoConstants.ConstantKey.T_C_GAME);
            target.withParam(LottoConstants.EXTRAS.TAG_ID, id);
            navigationRouter.navigateTo(target);
            close();
        } else if (VasConstants.VasOfferingsCodes.ELECTRICITY.equalsIgnoreCase(vasProductType)) {
            getCategoriesList(id);
        } else if (NotificationConstants.NAVIGATION_TARGET.BUY_UTILITY.equalsIgnoreCase(vasProductType)) {
            NavigationTarget target = NavigationTarget.to(ElectricityNavigatorTarget.UTILITY_DASHBOARD);
            ElectricityViewModel electricityViewModel = new ElectricityViewModel();
            electricityViewModel.setTagIdentifierRequestViewModel(new TagIdentifierRequestViewModel(id));
            target.withParam(ElectricityConstants.EXTRAS.ELECTRICITY_VIEW_MODEL, electricityViewModel);
            navigationRouter.navigateTo(target);
            close();
        }
    }

    private void handlePPWNavigation(TagViewModel categories) {
        mMemoryApplicationStorage.putBoolean(StorageKeys.IS_PPW_FLOW, false);

        TagViewModel mTagViewModel = getWaterTagViewModel(categories.getTagRelationships());
        ElectricityViewModel mElectricityViewModel = new ElectricityViewModel();
        if (mTagViewModel == null || mTagViewModel.getTag() == null) {
            handleNavigationErrorsFlow();
            return;
        }

        mElectricityViewModel.setTagIdentifierRequestViewModel(new TagIdentifierRequestViewModel(mTagViewModel.getTag().getId()));
        if (CollectionUtils.isNotEmpty(mTagViewModel.getTag().getProducts())) {
            ProductViewModel ppwProductModel = getPPWProductsViewModel(mTagViewModel.getTag().getProducts());
            if (ppwProductModel != null) {
                mElectricityViewModel.setProductViewModel(ppwProductModel);
                mElectricityViewModel.setElectricityFlow(false);
                mElectricityViewModel.setFbe(false);
                mElectricityViewModel.setFbw(false);
                mElectricityViewModel.setProductList(mTagViewModel.getTag().getProducts());

                navigationRouter.navigateTo(NavigationTarget.to(ElectricityNavigatorTarget.ELECTRICITY_METER_DETAIL)
                        .withParam(ElectricityConstants.EXTRAS.ELECTRICITY_VIEW_MODEL, mElectricityViewModel));
                close();
            } else {
                handleNavigationErrorsFlow();
            }
        } else {
            handleNavigationErrorsFlow();
        }
    }

    private ProductViewModel getPPWProductsViewModel(List<ProductViewModel> productViewModelList) {
        for (ProductViewModel mProductViewModel : productViewModelList) {
            if (ElectricityConstants.ProductCode.WATER_FLOW.equalsIgnoreCase(mProductViewModel.getVendorProductCode())) {
                return mProductViewModel;
            }
        }
        return null;
    }

    private TagViewModel getWaterTagViewModel(List<TagViewModel> tagViewModels) {
        for (TagViewModel mTagViewModel : tagViewModels) {
            if (ElectricityConstants.ProductCode.WATER_FLOW.equalsIgnoreCase(mTagViewModel.getTag().getCode())) {
                return mTagViewModel;
            }
        }
        return null;
    }

    private TagItemViewModel getVoucherViewModel(List<TagItemViewModel> tagItemViewModels, String vasProductType) {
        for (TagItemViewModel mTagItemViewModel : tagItemViewModels) {
            if (vasProductType.equalsIgnoreCase(mTagItemViewModel.getCode())) {
                return mTagItemViewModel;
            }
        }
        return null;

    }

    private TagViewModel getPELTagViewModel(List<TagViewModel> tagViewModels) {
        for (TagViewModel mTagViewModel : tagViewModels) {
            if (mTagViewModel.getTag() != null && ElectricityUtility.isElectrictyFlow(mTagViewModel.getTag().getCode())) {
                return mTagViewModel;
            }
        }
        return null;

    }

    private ProductViewModel getPELProductViewModel(List<ProductViewModel> productViewModels) {
        for (ProductViewModel mProductViewModel : productViewModels) {
            if (mProductViewModel.getVendorProductCode() != null && ElectricityUtility.isPrepaidElectricity(mProductViewModel.getVendorProductCode())) {
                return mProductViewModel;
            }
        }
        return null;

    }

    public void handleBuyPrepaidItemClick(Integer tagId) {
        NavigationTarget target = NavigationTarget.to(PrepaidNavigatorTarget.PREPAID_DASHBOARD);
        PrepaidPurchaseViewModel prepaidPurchaseViewModel = new PrepaidPurchaseViewModel();
        prepaidPurchaseViewModel.setTagIdentifierRequestViewModel(new TagIdentifierRequestViewModel(tagId));
        target.withParam(PrepaidConstants.EXTRAS.PURCHASE_PREPAID_MODEL, prepaidPurchaseViewModel);
        navigationRouter.navigateTo(target);
        close();
    }

    public void handleVoucherItemClick(Integer id, String birthDate) {
        VoucherPurchaseViewModel mVoucherPurchaseViewModel = new VoucherPurchaseViewModel();
        mVoucherPurchaseViewModel.setTagIdentifierRequestViewModel(new TagIdentifierRequestViewModel());
        mVoucherPurchaseViewModel.getTagIdentifierRequestViewModel().addIdToRequestList(id);
        NavigationTarget target = null;
        if (!mApplicationStorage.getBoolean(VoucherConstants.StorageKeys.VOUCHER_INTRO_DISPLAYED, false)) {
            target = NavigationTarget.to(VouchersNavigatorTarget.VOUCHER_INTRODUCTION);
        } else {
            target = NavigationTarget.to(VouchersNavigatorTarget.VOUCHER_DASHBOARD);
        }
        target.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDate);
        target.withParam(VoucherConstants.VOUCHER_PURCHASE_VIEW_MODEL, mVoucherPurchaseViewModel);
        navigationRouter.navigateTo(target);
        close();
    }

    private void handleUpdateNedIdDetails() {
        mLoginSecurityUsecase.execute(Boolean.TRUE)
                .compose(bindToLifecycle()).subscribe(o -> {
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    boolean isNonTpUser() {
        String clientType = mApplicationStorage.getString(StorageKeys.CLIENT_TYPE, ClientType.TP.toString());
        return ClientType.getEnum(clientType) == ClientType.NON_TP;
    }

    public void openCovid19() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.COVID_19));
        close();
    }

    private void handleOpenKidsAccountFlow() {
        callProductAcquisitionForMinor();
    }


    void callProductAcquisitionForMinor() {

        Map<String, Object> contextData = new HashMap<>();
        contextData.put(EnrollV2TrackingEvent.ANALYTICS.KEY_PRODUCTS, EnrollV2TrackingEvent.ANALYTICS.VAL_BANK_NEDBANK_4ME);
        mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.ANALYTICS.KEY_PRODUCT_SELECTED, contextData);
        mMemoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, EnrollV2TrackingEvent.ANALYTICS.VAL_BANK_NEDBANK_4ME);

        mMemoryApplicationStorage.putBoolean(StorageKeys.IS_PROFESSIONAL_PRODUCT, false);
        mMemoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT, za.co.nedbank.enroll_v2.Constants.MINOR_PRODUCT_NAME);
        ProductRequestViewModel mProductRequestViewModel = new ProductRequestViewModel();
        mProductRequestViewModel.setMonthlySalary(0);
        mProductRequestViewModel.setDateofbirth(new SimpleDateFormat(za.co.nedbank.enroll_v2.Constants.DATE_FORMAT, Locale.getDefault()).format(new Date()));
        mProductRequestViewModel.setProductType(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.getAccountTypeCode());

        mProductUsecase.execute(mProductRequestViewModelToDataMapper.mapData(mProductRequestViewModel))
                .compose(bindToLifecycle())
                .map(mProductResponseDataToViewModelMapper::mapData)
                .subscribe(productResponseDataViewModel -> {
                    if (productResponseDataViewModel.getMetadata().isSuccess()
                            && productResponseDataViewModel.getProductDataResponseDataViewModel() != null
                            && productResponseDataViewModel.getProductDataResponseDataViewModel().getProductDataViewModels() != null
                            && !productResponseDataViewModel.getProductDataResponseDataViewModel().getProductDataViewModels().isEmpty()) {
                        mMemoryApplicationStorage.putString(StorageKeys.FICA_SESSION_ID,
                                productResponseDataViewModel.getProductDataResponseDataViewModel().getSessionId());
                        mMemoryApplicationStorage.putInteger(StorageKeys.FICA_PRODUCT_FLOW,
                                FicaProductFlow.MINOR);
                        navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.MOA_PRODUCT_DETAIL)
                                .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SELECTED_PRODUCT, productResponseDataViewModel
                                        .getProductDataResponseDataViewModel()
                                        .getProductDataViewModels()
                                        .get(0)));
                        close();
                    } else {
                        handleException(productResponseDataViewModel.getMetadata().getThrowable());
                    }
                }, this::handleException);
    }

    private void handleException(Throwable throwable) {
        handleNavigationErrorsFlow();
    }

    private void handleShareMoneyAppFlow() {
        mGetProfileUseCase
                .execute(false)
                .compose(bindToLifecycle())
                .subscribe(
                        userProfile -> {
                            if (userProfile != null) {
                                mUserProfile = userProfile;
                                getEmcertId();
                            } else {
                                handleNavigationErrorsFlow();
                            }
                        },
                        error -> handleNavigationErrorsFlow()
                );


    }

    private void getEmcertId() {
        mGetEmcertIdUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(emcertId -> {
                    if (view != null && emcertId != null) {
                        String userName = String.valueOf(TextUtils.concat(mUserProfile.getFirstName(), StringUtils.SPACE, mUserProfile.getLastName()));
                        navigateToShareMoneyApp(userName, emcertId);
                    }
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }


    private void navigateToShareMoneyApp(String userName, String emcertId) {
        mAnalytics.sendEvent(ProfileTracking.CLICK_SHARE_THE_APP, ProfileTracking.CLICK_SHARE_MONEY_APP, StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(NavigationTarget.to(ProfileNavigationTarget.SHARE_THE_MONEY_APP)
                .withParam(NavigationTarget.PARAM_NAME, userName)
                .withParam(ProfileConstants.EMCERT_ID, emcertId));
        close();
    }

    private void getAccountIdandInitiateFlow(String accountNumber, String actionName) {
        mGetOverviewUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(overviewCachableResponse -> {
                    Overview overview = overviewCachableResponse.get().clone();
                    setAccountDetails(overview, accountNumber);
                    switch (actionName) {
                        case NotificationConstants.NAVIGATION_TARGET.APPLY_FUNERAL_POLICY ->
                                handleApplyMyLifeOrFuneralFlow(overview, InsuranceConstants.INSURANCE_PRODUCT_ID);
                        case NotificationConstants.NAVIGATION_TARGET.APPLY_LIFE_COVER ->
                                handleApplyMyLifeOrFuneralFlow(overview, InsuranceConstants.MY_COVER_LIFE_PRODUCT_ID);
                        case NotificationConstants.NAVIGATION_TARGET.APPLY_PERSONAL_ACCIDENT_COVER,
                             NotificationConstants.NAVIGATION_TARGET.APPLY_HOME_OWNERS_COVER,
                             NotificationConstants.NAVIGATION_TARGET.COVER_PERSONAL_LINES_BUILDINGS,
                             NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_GAP_WARRANTY,
                             NotificationConstants.NAVIGATION_TARGET.CREDIT_SHORTFALL_EDUCATION,
                             NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_COMPREHENSIVE_WARRANTY,
                             NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_ESSENTIAL_WARRANTY,
                             NotificationConstants.NAVIGATION_TARGET.COVER_PERSONAL_LINES_MOTOR,
                             NotificationConstants.NAVIGATION_TARGET.COVER_PERSONAL_LINES_HOUSE_CONTENT,
                             NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER ->
                                handleApplyInsureFlow(overview, actionName);
                        default -> handleNavigationErrorsFlow();
                    }

                }, throwable -> {
                    NBLogger.e(TAG, throwable.getMessage());
                    handleNavigationErrorsFlow();
                });
    }

    private void handleApplyInsureFlow(Overview overview, String action) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview accountsOverview : overview.accountsOverviews) {
                boolean doBreakLoop = getInsuranceAccountList(mInsuranceAccountList, accountsOverview, action, overview);
                if (doBreakLoop) break;
            }
        } else {
            handleNavigationErrorsFlow();
        }
    }

    void getAllowedCoverAmount() {
        String partyId = getRSAId();
        mGetAllowedCoverAmountUseCase.execute(ACCIDENTAL_INSURANCE_PRODUCT_ID, partyId)
                .compose(bindToLifecycle())
                .subscribe(allowedCoverAmountResponseData -> {
                    AllowedCoverAmountResponseViewModel allowedCoverAmountResponseViewModel = mAllowedCoverAmountResponseDataToViewModelMapper
                            .map(allowedCoverAmountResponseData);
                    navigateToInsuranceEducationScreen(InsuranceProductType.PERSONAL_ACCIDENTAL,
                            mInsuranceAccountList,
                            InsuranceConstants.ACCIDENTAL_INSURANCE_PRODUCT_ID,
                            allowedCoverAmountResponseViewModel.getAllowedcoveramount(),
                            allowedCoverAmountResponseViewModel.getExistingcoveramount(),
                            NotificationConstants.NAVIGATION_TARGET.APPLY_PERSONAL_ACCIDENT_COVER);
                }, throwable -> handleNavigationErrorsFlow());
    }


    private String getRSAId() {
        UserDetailViewModel userDataModel = (UserDetailViewModel) mMemoryApplicationStorage
                .getObject(ResponseStorageKey.USERDETAILDATA);
        if (userDataModel != null) {
            return userDataModel.getIdOrTaxIdNumber();
        }
        return null;
    }

    boolean getInsuranceAccountList(List<AccountViewModel> insuranceAccountList, AccountsOverview mAccountsOverview, String actionName, Overview overview) {
        boolean doBreakLoop = false;
        if ((mAccountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                && mAccountsOverview.insuranceAccountList != null) {
            insuranceAccountList = mAccountsOverview.insuranceAccountList;
        }
        if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_PERSONAL_ACCIDENT_COVER)) {
            getAllowedCoverAmount();
            doBreakLoop = true;
        } else if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_PERSONAL_LINES_BUILDINGS)) {
            navigateToInsuranceEducationScreen(InsuranceProductType.PERSONAL_LINES_BUILDING,
                    insuranceAccountList, InsuranceConstants.MY_COVER_INSURANCE_COMBO_ID, 0.0, 0.0, actionName);
            doBreakLoop = true;
        } else if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_PERSONAL_LINES_MOTOR)) {
            navigateToVehicleInsuranceScreen(
                    insuranceAccountList);
            doBreakLoop = true;
        } else if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_PERSONAL_LINES_HOUSE_CONTENT)) {
            navigateToInsuranceEducationScreen(InsuranceProductType.PERSONAL_LINES_HOME_CONTENT,
                    insuranceAccountList, InsuranceConstants.MY_COVER_INSURANCE_COMBO_ID, 0.0, 0.0, actionName);
            doBreakLoop = true;
        } else {
            doBreakLoop = getInsuranceAccountList2(insuranceAccountList, actionName, overview);
        }
        return doBreakLoop;
    }

    private boolean getInsuranceAccountList2(List<AccountViewModel> insuranceAccountList, String actionName, Overview overview) {
        boolean doBreakLoop = false;
        if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_HOME_OWNERS_COVER) || actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER)) {
            List<Long> homeLoanAccounts = new ArrayList<>();
            for (AccountsOverview mAccountsOverview1 : overview.accountsOverviews) {
                addAccountsOverviewTypeLoan(homeLoanAccounts, mAccountsOverview1);
            }
            InsuranceViewModel insuranceViewModel = new InsuranceViewModel();
            insuranceViewModel.setAge(FormattingUtil.getAgeDifference(getClientDob()));
            insuranceViewModel.setInsuranceAccountList(insuranceAccountList);
            insuranceViewModel.setHomeLoan(CollectionUtils.isNotEmpty(homeLoanAccounts));
            insuranceViewModel.setHomeLoanAccounts(homeLoanAccounts);
            if (CollectionUtils.isNotEmpty(insuranceViewModel.getHomeLoanAccounts()) && !mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_INSURANCE_HOC)) {
                getNonNedbankInsuredPropertiesUseCase(insuranceViewModel, actionName);
                doBreakLoop = true;
            } else if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER)) {
                onClickMyCoverLayout(InsuranceProductType.PERSONAL_LINES_BUILDING, insuranceAccountList,
                        InsuranceConstants.MY_COVER_INSURANCE_BUILDING_PRODUCT_ID, 0.0, 0.0);
                doBreakLoop = true;
            } else {
                handleNavigationErrorsFlow();
                doBreakLoop = true;
            }
        } else if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_COMPREHENSIVE_WARRANTY)
                || actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_ESSENTIAL_WARRANTY)
                || actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.CREDIT_SHORTFALL_EDUCATION)
                || actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_GAP_WARRANTY)) {
            navigateToInsuranceEducationScreen(InsuranceProductType.VVAPS,
                    insuranceAccountList, InsuranceConstants.VEHICLE_VAP_PRODUCT_ID, 0.0, 0.0, actionName);
            doBreakLoop = true;
        }
        return doBreakLoop;
    }

    void navigateToInsuranceEducationScreen(InsuranceProductType insuranceProduct,
                                            List<AccountViewModel> accountList,
                                            int productId, double allowedCoverAmount,
                                            double existingCoverAmount, String actionName) {
        NavigationTarget navigationTarget;
        if (insuranceProduct == InsuranceProductType.MY_COVER_LIFE) {
            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_NEW_EDUCATION_ACTIVITY);
        } else {
            navigationTarget = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_EDUCATION_SCREEN);
        }
        navigationTarget.withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, insuranceProduct)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, productId)
                .withParam(InsuranceConstants.ParamKeys.PARAM_ALLOWED_COVER_AMOUNT, allowedCoverAmount)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EXISTING_COVER_AMOUNT, existingCoverAmount)
                .withParam(za.co.nedbank.core.Constants.IS_DEEP_LINK_FLOW, true)
                .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, accountList);
        if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_COMPREHENSIVE_WARRANTY)) {
            navigationTarget.withParam(InsuranceConstants.BundleKeys.VVAP_PRODUCT_TYPE, InsuranceProductType.VVAPS_CW);
        } else if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_ESSENTIAL_WARRANTY)) {
            navigationTarget.withParam(InsuranceConstants.BundleKeys.VVAP_PRODUCT_TYPE, InsuranceProductType.VVAPS_EW);
        } else if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.CREDIT_SHORTFALL_EDUCATION)) {
            navigationTarget.withParam(InsuranceConstants.BundleKeys.VVAP_PRODUCT_TYPE, InsuranceProductType.VVAPS_CSF);
        } else if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.COVER_VVAPS_GAP_WARRANTY)) {
            navigationTarget.withParam(InsuranceConstants.BundleKeys.VVAP_PRODUCT_TYPE, InsuranceProductType.VVAPS_GAP);
        }
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    void navigateToVehicleInsuranceScreen(List<AccountViewModel> accountList) {
        int maxCount = za.co.nedbank.core.Constants.ZERO;
        List<NonNedbankInsuredPropertiesViewModel> addressList = new ArrayList<>();
        ArrayList<String> vinNumberList = new ArrayList<>();
        ArrayList<String> registerNumberList = new ArrayList<>();
        NavigationTarget navigationTarget;
        navigationTarget = NavigationTarget.to(ServicesNavigationTarget.QUICK_QUOTE_STEPPER_SCREEN);
        navigationTarget.withParam(InsuranceConstants.ParamKeys.PARAM_INSURANCE_PRODUCT_TYPE, InsuranceProductType.QUICK_QUOTE_VEHICLE)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_ID, InsuranceConstants.MY_COVER_INSURANCE_COMBO_ID)
                .withParam(InsuranceConstants.ParamKeys.PRODUCT_FLOW_TYPE, STANDALONE)
                .withParam(InsuranceConstants.ParamKeys.PARAM_ALLOWED_COVER_AMOUNT, 0.0)
                .withParam(InsuranceConstants.ParamKeys.PARAM_EXISTING_COVER_AMOUNT, 0.0)
                .withParam(InsuranceConstants.ParamKeys.FROM_POLICY_LIST, false)
                .withParam(InsuranceConstants.ParamKeys.PARAM_PL_COMBO_MAX_COVER_COUNT, maxCount)
                .withParam(ServicesNavigationTarget.EXTRA_INSURANCE_PROPERTY_ADDRESS, addressList)
                .withParam(ServicesNavigationTarget.SAVED_REGISTRATION_MOTOR_DATA, registerNumberList)
                .withParam(ServicesNavigationTarget.SAVED_VIN_MOTOR_DATA, vinNumberList)
                .withParam(InsuranceConstants.BundleKeys.INSURANCE_CA_SA_BANK_ACCOUNT, accountList);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    void addAccountsOverviewTypeLoan(List<Long> homeLoanAccountList, AccountsOverview mAccountsOverview) {
        if ((mAccountsOverview.overviewType == OverviewType.LOANS)
                && mAccountsOverview.accountSummaries != null) {
            for (AccountSummary mAccountSummary : mAccountsOverview.accountSummaries) {
                if (!StringUtils.isNullOrEmpty(mAccountSummary.getAccountCode()) &&
                        mAccountSummary.getAccountCode().equals(za.co.nedbank.core.Constants.ACCOUNT_TYPES.HL.name())) {
                    homeLoanAccountList.add(Long.parseLong(mAccountSummary.getNumber()));
                }
            }
        }
    }


    private void getNonNedbankInsuredPropertiesUseCase(InsuranceViewModel insuranceViewModel, String actionName) {
        NonNedbankInsuredRequestDataModel nonNedbankInsuredRequestDataModel = new NonNedbankInsuredRequestDataModel();
        List<Long> homeLoanAccountList = insuranceViewModel.getHomeLoanAccounts();
        mInsuranceAccountList = insuranceViewModel.getInsuranceAccountList();
        if (CollectionUtils.isNotEmpty(homeLoanAccountList)) {
            nonNedbankInsuredRequestDataModel.setInsurer(StringUtils.EMPTY_STRING);
            nonNedbankInsuredRequestDataModel.setHomeLoanAccountNumbers(homeLoanAccountList.toArray(new Long[homeLoanAccountList.size()]));
        }
        mNonNedbankInsuredPropertiesUseCase.execute(nonNedbankInsuredRequestDataModel)
                .compose(bindToLifecycle()).subscribe(nonNedbankInsuredPropertiesDomain -> {
                    insuranceViewModel.setHomeLoan(CollectionUtils.isNotEmpty(nonNedbankInsuredPropertiesDomain));
                    List<NonNedbankInsuredPropertiesViewModel> nonNedbankInsuredPropertiesViewModels =
                            mNonNedbankInsuredPropertiesDomainToViewMapper.map(nonNedbankInsuredPropertiesDomain);
                    mPropertiesViewModels = nonNedbankInsuredPropertiesViewModels;
                    navigateToHOCMyCover(actionName);
                }, error -> navigateToHOCMyCover(actionName));
    }

    private void navigateToHOCMyCover(String actionName) {
        if (actionName.equalsIgnoreCase(NotificationConstants.NAVIGATION_TARGET.APPLY_MY_COVER)) {
            onClickMyCoverLayout(InsuranceProductType.PERSONAL_LINES_BUILDING, mInsuranceAccountList, InsuranceConstants.MY_COVER_INSURANCE_BUILDING_PRODUCT_ID, 0.0, 0.0);
        } else {
            onClickProductLayout(InsuranceProductType.HOME_OWNER_COVER, mInsuranceAccountList, InsuranceConstants.HOC_INSURANCE_PRODUCT_ID, 0.0, 0.0);
        }
    }

    private String getClientDob() {
        UserDetailViewModel userDataModel = (UserDetailViewModel) mMemoryApplicationStorage
                .getObject(ResponseStorageKey.USERDETAILDATA);
        if (userDataModel != null) {
            return userDataModel.getBirthDate();
        }
        return null;
    }

    void getUserDetails(String target) {
        if (view != null) {
            mGetUserDetailsUseCase
                    .execute(false)
                    .compose(bindToLifecycle())
                    .subscribe(userDetailsData -> setUserData(userDetailsData, target),
                            throwable -> handleNavigationErrorsFlow());
        }
    }

    private void setUserData(UserDetailData userData, String target) {
        userEntriesViewModel.setBirthDate(userData.getBirthDate());
        userEntriesViewModel.setSecOfficerCd(userData.getSecOfficerCd());
        userEntriesViewModel.setCisNumber(userData.getCisNumber());
        userEntriesViewModel.setClientType(userData.getClientType());
        userEntriesViewModel.setInvestmentProductType(StringUtils.EMPTY_STRING);
        getAllProducts(target);
    }

    void getAllProducts(String targetScreen) {
        if (view != null) {
            mGetAllProductsUseCase
                    .execute(getProductDataModel())
                    .compose(bindToLifecycle())
                    .subscribe(productResponseDataModel -> {
                        String productId;
                        switch (targetScreen) {
                            case NotificationConstants.NAVIGATION_TARGET.APPLY_32DAY_NOTICE_ACCOUNT:
                                productId = NotificationConstants.InsuranceProductCodes.DAY32NOTICE.getValue();
                                break;
                            case NotificationConstants.NAVIGATION_TARGET.APPLY_JUST_INVEST_ACCOUNT:
                                productId = NotificationConstants.InsuranceProductCodes.JUSTINVEST.getValue();
                                break;
                            case NotificationConstants.NAVIGATION_TARGET.APPLY_MONEY_TRADER:
                                productId = NotificationConstants.InsuranceProductCodes.MONEYTRADER.getValue();
                                break;
                            case NotificationConstants.NAVIGATION_TARGET.APPLY_EASY_ACCESS_DEPOSIT_ACCOUNT:
                                productId = NotificationConstants.InsuranceProductCodes.EASYACCESSDEPOSIT.getValue();
                                break;
                            case NotificationConstants.NAVIGATION_TARGET.APPLY_EFIXED_DEPOSIT_ACCOUNT:
                                productId = NotificationConstants.InsuranceProductCodes.EFIXEDDEPOSIT.getValue();
                                break;
                            case NotificationConstants.NAVIGATION_TARGET.APPLY_PLATINUM_INVEST_ACCOUNT:
                                productId = NotificationConstants.InsuranceProductCodes.PLATINUMINVEST.getValue();
                                break;
                            case NotificationConstants.NAVIGATION_TARGET.APPLY_PLATINUM_FIXED_DEPOSIT_ACCOUNT:
                                productId = NotificationConstants.InsuranceProductCodes.PLATINUMFIXEDDEPOSIT.getValue();
                                break;
                            case NotificationConstants.NAVIGATION_TARGET.APPLY_PRIME_SELECT_ACCOUNT:
                                productId = NotificationConstants.InsuranceProductCodes.PRIMESELECT.getValue();
                                break;
                            case NotificationConstants.NAVIGATION_TARGET.OPEN_JUST_SAVE_ACCOUNT:
                                productId = NotificationConstants.InsuranceProductCodes.JUSTSAVE.getValue();
                                break;
                            default:
                                productId = null;
                                break;
                        }
                        AllProductsViewModel productsViewModel = filterInvestData(mAllProductResponseDataModelToViewModelMapper.mapAllProductsDataModelToViewModel(productResponseDataModel), productId);
                        handleInsuranceProductsFlow(targetScreen, productsViewModel);
                    }, error -> handleNavigationErrorsFlow());
        }
    }

    private void handleInsuranceProductsFlow(String targetScreen, AllProductsViewModel productsViewModel) {
        if (productsViewModel != null) {
            if (NotificationConstants.NAVIGATION_TARGET.OPEN_JUST_SAVE_ACCOUNT.equalsIgnoreCase(targetScreen)) {
                handleJustSaveFlow(productsViewModel, userEntriesViewModel);
            } else {
                navigateToMoreInfo(productsViewModel.getIsNoticeDeposit(), productsViewModel, userEntriesViewModel);
            }
        } else {
            handleNavigationErrorsFlow();
        }
    }

    private AllProductsViewModel filterInvestData(AllProductsResponseViewModel productsResponseViewModel, String productId) {
        for (AllProductsViewModel mAllProductsViewModel : productsResponseViewModel.getAllProductsViewModels()) {
            if (Long.toString(mAllProductsViewModel.getProductId()).equals(productId)) {
                return mAllProductsViewModel;
            }
        }
        return null;
    }

    private ProductDataModel getProductDataModel() {
        ProductDataModel mProductDataModel = new ProductDataModel();
        mProductDataModel.setType(INVESTMENTS);
        int clientAge = AppUtility.calculateAge(userEntriesViewModel.getBirthDate());
        mProductDataModel.setClientAge(clientAge);
        if (clientAge == 0) {
            mProductDataModel.setClientType(userEntriesViewModel.getClientType());
        } else {
            mProductDataModel.setClientType(userEntriesViewModel.getSecOfficerCd());
        }
        return mProductDataModel;
    }

    void navigateToMoreInfo(String isNoticeDeposit, AllProductsViewModel mAllProductsViewModel, OpenNewAccUserEntriesViewModel userEntriesViewModel) {
        if (view != null) {
            NavigationTarget target = NavigationTarget.to(OpenNewInvAccountTarget.TARGET_SUGGESION_MOREINFO);
            target.withParam(IS_NOTICE_DEPOSIT, isNoticeDeposit);
            target.withParam(OpenNewInvAccountConstants.KEY_IS_FILTER_ENABLED_SUGGESION, false);
            target.withParam(NgiNavigatorTarget.PARAM_INVESTMENT_SWITCHING_FLOW, za.co.nedbank.core.Constants.ZERO);
            target.withParam(OpenNewInvAccountConstants.KEY_PRODUCT_MODEL, mAllProductsViewModel);
            target.withParam(OpenNewInvAccountConstants.KEY_USERENTRIES_MODEL, userEntriesViewModel);
            target.withParam(za.co.nedbank.core.Constants.KEY_INVESTMENT_ONLINE_PROJECTED_AMOUNT, userEntriesViewModel.getProjectedAmount());
            target.withParam(NavigationTarget.PARAM_SWITCHING_INVESTMENT_VIEW_MODEL, new InvestmentSwitchingViewModel());
            navigationRouter.navigateTo(target);
            close();
        }
    }

    private void handleApplyMyLifeOrFuneralFlow(Overview overview, int productID) {
        if (overview != null && overview.accountsOverviews != null) {
            for (AccountsOverview mAccountsOverview : overview.accountsOverviews) {
                if ((mAccountsOverview.overviewType == OverviewType.EVERYDAY_BANKING)
                        && mAccountsOverview.insuranceAccountList != null) {
                    mInsuranceAccountList = mAccountsOverview.insuranceAccountList;
                    getNIFPMyCoverAllowedCoverAmount(productID);
                }
            }
        }
    }

    void getNIFPMyCoverAllowedCoverAmount(int productID) {
        String partyID = getRSAId();
        List<String> arrayOfRsaId = new ArrayList<>();
        if (StringUtils.isNotEmpty(partyID)) {
            arrayOfRsaId.add(partyID);
        }
        mNifpMyCoverAllowCoverUseCase.execute(productID, arrayOfRsaId)
                .compose(bindToLifecycle())
                .subscribe(allowedCoverAmountResponseData -> setNIFPMyCoverAmount(mNifpMyCoverAmountResponseDataToViewModelMapper
                                .map(allowedCoverAmountResponseData), productID),
                        throwable -> handleNavigationErrorsFlow());
    }

    private void setNIFPMyCoverAmount(List<AllowedAmountResponseViewModel> allowedAmountResponseViewModelList, int productID) {
        if (CollectionUtils.isNotEmpty(allowedAmountResponseViewModelList)) {
            AllowedCoverAmountResponseViewModel allowedCoverAmountResponseViewModel = allowedAmountResponseViewModelList.get(za.co.nedbank.core.Constants.ZERO).getAllowedCoverAmountResponseViewModel();
            switch (productID) {
                case INSURANCE_PRODUCT_ID:
                    setAllowedFuneralCoverAmount(allowedCoverAmountResponseViewModel);
                    break;
                case VEHICLE_VAP_PRODUCT_ID:
                    setAllowedVVAPSAmount(allowedCoverAmountResponseViewModel);
                    break;
                case InsuranceConstants.MY_COVER_LIFE_PRODUCT_ID:
                    setMyCoverAllowedCoverAmount(allowedCoverAmountResponseViewModel);
                    break;
                default:
                    // There is no default use case
                    break;
            }
        } else {
            handleNavigationErrorsFlow();
        }
    }

    private void setAllowedFuneralCoverAmount(AllowedCoverAmountResponseViewModel allowedCoverAmountResponseViewModel) {
        if (allowedCoverAmountResponseViewModel.getAllowedcoveramount() > MIN_VALUE) {
            double allowedMaxCoverAmount = allowedCoverAmountResponseViewModel.getAllowedcoveramount();
            double existingCoverAmount = allowedCoverAmountResponseViewModel.getExistingcoveramount();
            onClickProductLayout(InsuranceProductType.FUNERAL_OWN_COVER,
                    mInsuranceAccountList, InsuranceConstants.INSURANCE_PRODUCT_ID, allowedMaxCoverAmount, existingCoverAmount);
        } else {
            handleNavigationErrorsFlow();

        }
    }

    private void setAllowedVVAPSAmount(AllowedCoverAmountResponseViewModel mAllowedCoverAmountResponseViewModel) {
        double allowedMaxCoverAmount = 0.0;
        double existingCoverAmount = 0.0;
        if (mAllowedCoverAmountResponseViewModel != null) {
            allowedMaxCoverAmount = mAllowedCoverAmountResponseViewModel.getAllowedcoveramount();
            existingCoverAmount = mAllowedCoverAmountResponseViewModel.getExistingcoveramount();
        }
        InsuranceProductType insuranceType = InsuranceProductType.VVAPS;
        navigateToInsuranceEducationScreen(insuranceType, mInsuranceAccountList, InsuranceConstants.VEHICLE_VAP_PRODUCT_ID, allowedMaxCoverAmount, existingCoverAmount, NotificationConstants.NAVIGATION_TARGET.CREDIT_SHORTFALL_EDUCATION);
    }

    public void setMyCoverAllowedCoverAmount(AllowedCoverAmountResponseViewModel mAllowedCoverAmountResponseViewModel) {
        if (mAllowedCoverAmountResponseViewModel.getAllowedcoveramount() > InsuranceConstants.InsuranceCoverConstants.MY_COVER_MIN_VALUE) {
            double existingCoverAmount = mAllowedCoverAmountResponseViewModel.getExistingcoveramount();
            double allowedMaxCoverAmount = mAllowedCoverAmountResponseViewModel.getAllowedcoveramount();
            navigateToInsuranceEducationScreen(InsuranceProductType.MY_COVER_LIFE, mInsuranceAccountList,
                    InsuranceConstants.MY_COVER_LIFE_PRODUCT_ID, allowedMaxCoverAmount,
                    existingCoverAmount, NotificationConstants.NAVIGATION_TARGET.APPLY_LIFE_COVER);
        } else {
            handleNavigationErrorsFlow();
        }
    }

    private void setAccountDetails(Overview overview, String accountNumber) {
        for (AccountsOverview mAccountsOverview : overview.accountsOverviews) {
            for (AccountSummary accountSummary : mAccountsOverview.accountSummaries) {
                if (accountSummary.getNumber() != null && accountSummary.getNumber().equalsIgnoreCase(accountNumber)) {
                    mAccountId = accountSummary.getId();
                    break;
                }
            }
        }
    }

    private NavigationTarget resolveNavigationTarget(AjoPushPayloadDataModel.ActionButton responseOption) {
        String target = getActionName(responseOption.getLink());

        if (TextUtils.isEmpty(target)) return null;
        switch (target) {
            case NotificationConstants.NAVIGATION_TARGET.GREEN_BACK_ENROLLMENT -> {
                if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.REWARDS_ENROLMENT)) {
                    return NavigationTarget.to(ProfileNavigationTarget.REWARDS_LANDING);
                }
                return null;
            }
            case NotificationConstants.NAVIGATION_TARGET.TRANSACT -> {
                return NavigationTarget.to(NavigationTarget.HOME).withParam(NotificationConstants.EXTRA.NAVIGATION_TARGET, target);
            }
            case NotificationConstants.NAVIGATION_TARGET.PRE_APPROVED_OFFERS -> {
                return NavigationTarget.to(PreApprovedOffersNavigationTarget.PRE_APPROVED_OFFERS_NOTIFICATIONS)
                        .withParam(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.NOTIFICATION_DETAILS_SCREEN);
            }
            case NotificationConstants.NAVIGATION_TARGET.INS_OFFERS_FOR_YOU -> {
                return NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN)
                        .withParam(InsuranceConstants.BundleKeys.SHOW_OFFERS_FOR_YOU, true);
            }
            case NotificationConstants.NAVIGATION_TARGET.REPORT_FRAUD -> {
                return obtainNavigationTargetForReportFraud();
            }
            case NotificationConstants.NAVIGATION_TARGET.REDEEM_GB_FOREX -> {
                return NavigationTarget.to(ServicesNavigationTarget.GREENBACKS_FOREX_LANDING);
            }
            case NotificationConstants.NAVIGATION_TARGET.REDEEM_GB_CHARGES_AND_FEES -> {
                return NavigationTarget.to(ServicesNavigationTarget.CHARGES_AND_FEES_LANDING);
            }
            case NotificationConstants.NAVIGATION_TARGET.REDEEM_GB_DONATION -> {
                return NavigationTarget.to(ServicesNavigationTarget.DONATIONS_LANDING);
            }
            case NotificationConstants.NAVIGATION_TARGET.PAY_TO_MOBILE -> {
                return NavigationTarget.to(NavigationTarget.PAY).withParam(CrossBorderNavigationTarget.PARAM_SET_PAYMENT_TYPE, PayMode.MOBILE);
            }
            case NotificationConstants.NAVIGATION_TARGET.APPLY_FINANCIAL_PLANNER -> {
                return NavigationTarget.to(NavigationTarget.FINANCIAL_PLANNER_FORM).withParam(za.co.nedbank.enroll_v2.Constants.NfpParam.IS_PRE_LOGIN, false);
            }
            case NotificationConstants.NAVIGATION_TARGET.FEEDBACK -> {
                return NavigationTarget.to(NavigationTarget.CONTACT_US_ITEM).withParam(NavigationTarget.PARAM_CONTACT_US_FEATURE, za.co.nedbank.services.Constants.CONTACT_US.FEEDBACK);
            }
            case NotificationConstants.NAVIGATION_TARGET.ADD_RECIPIENT -> {
                return NavigationTarget.to(NavigationTarget.ADD_RECIPIENT);
            }
            case NotificationConstants.NAVIGATION_TARGET.PROFILE_LIMITS -> {
                return NavigationTarget.to(NavigationTarget.PROFILE_LIMITS);
            }
            case NotificationConstants.NAVIGATION_TARGET.PAY_TO_FOREIGN -> {
                return NavigationTarget.to(NavigationTarget.PAY).withParam(CrossBorderNavigationTarget.PARAM_SET_PAYMENT_TYPE, PayMode.FOREIGN);
            }
            case NotificationConstants.NAVIGATION_TARGET.SETUP_MONEY_TACKER -> {
                return NavigationTarget.to(ServicesNavigationTarget.MONEY_TRACKER_DASHBOARD_PURPOSE);
            }
            case NotificationConstants.NAVIGATION_TARGET.VIEW_INWARD_PAYMENTS -> {
                return NavigationTarget.to(NavigationTarget.INTERNATIONAL_PAYMENT_HISTORY);
            }
            case NotificationConstants.NAVIGATION_TARGET.COMMUNICATION_PREFERENCES -> {
                return NavigationTarget.to(ProfileNavigationTarget.APP_COMMUNICATION);
            }
            case NotificationConstants.NAVIGATION_TARGET.PERSONALISE_APP -> {
                return NavigationTarget.to(ProfileNavigationTarget.APP_PERSONALISATION);
            }
            case NotificationConstants.NAVIGATION_TARGET.SCAN2PAY_DEFAULT_CARD -> {
                return NavigationTarget.to(ProfileNavigationTarget.SCANPAY_SET_DEFAULT_CARD);
            }
            case NotificationConstants.NAVIGATION_TARGET.PAY_ME_REQUEST -> {
                return NavigationTarget.to(NavigationTarget.PAY_ME);
            }
            case NotificationConstants.NAVIGATION_TARGET.INSURANCE_OPTIONS -> {
                return NavigationTarget.to(NavigationTarget.INSURANCE_DASHBOARD_SCREEN);
            }
            default -> {
                return null;
            }
        }
    }

    private NavigationTarget obtainNavigationTargetForReportFraud() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.REPORT_FRAUD)) {
            return NavigationTarget.to(NavigationTarget.MORE_REPORT_FRAUD);
        }
        return null;
    }

    private void handleNonTpJoinFamilyBankingFlow() {
        mMemoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT_GROUP_AND_PRODUCT, StringUtils.EMPTY_STRING);
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.MOA_BASIC_INFORMATION));
        close();
    }

    private void handleInvestmentAccountFlow() {
        mGetUserDetailsUseCase.execute(false).compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    if (userDetail != null && view != null) {
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NGI_GET_STARTED)
                                .withParam(PARAM_ONIA_CLIENT_TYPE, userDetail.getClientType())
                                .withParam(PARAM_ONIA_BIRTH_DATE, userDetail.getBirthDate())
                                .withParam(PARAM_ONIA_SEC_OFFICER_CD, userDetail.getSecOfficerCd())
                                .withParam(NavigationTarget.PARAM_ONIA_CIS_NUMBER, userDetail.getCisNumber()));
                        close();
                    } else {
                        handleNavigationErrorsFlow();
                    }
                }, error -> handleNavigationErrorsFlow());
    }

    void handleChatNotification() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.APP_CHAT)) {
            navigateToChatActivity();
        } else {
            handleNavigationErrorsFlow();
        }
    }

    private void navigateToChatActivity() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withIntentFlagClearTopSingleTop(true));
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CHAT_SCREEN));
        close();
    }

    private void handleNavigationErrorsFlow() {
        handleNavigationErrorsFlow(null);
    }

    private void handleNavigationErrorsFlow(ErrorMsgEvent errorMsgEvent) {
        if (view != null) {
            if (view.fromScreen(NotificationConstants.AjoConstants.AJO_PUSH)) {
                Intent result = new Intent();
                result.putExtra(NotificationConstants.AjoConstants.ACTION, NotificationConstants.AjoConstants.DISMISS);
                view.setResult(result);
            } else if (view.fromScreen(NotificationConstants.AjoConstants.PUSH_NOTIFICATION)) {
                Intent result = new Intent();
                result.putExtra(NotificationConstants.AjoConstants.ACTION, NotificationConstants.AjoConstants.ERROR);
                view.setResult(result);
            } else if (view.fromScreen(NotificationConstants.AjoConstants.POST_ERROR_MSG) && errorMsgEvent != null) {
                close();
                EventBus.getDefault().post(errorMsgEvent);
            } else {
                close();
            }
        }
    }

    private void handleNavigationVasToggleOffFlow(boolean isVasToggleOff, String birthDate) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.VAS_TOGGLE_OFF);
        navigationTarget.withParam(NavigationTarget.PARAM_BIRTH_DATE, birthDate);
        navigationTarget.withParam(VasConstants.EXTRAS.VAS_TOGGLE_OFF, isVasToggleOff);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    private void handleRetentionFlow() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_RETENTION)) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.RETENTION_TASK_SELECTION_NOTIFICATION_JOURNEY));
            close();
        } else {
            handleNavigationErrorsFlow();
        }
    }

    @SuppressLint("CheckResult")
    private void handleMiGoalsFlow(String productId) {
        if (StringUtils.isNullOrEmpty(productId)) return;
        ProductRequestViewModel productRequestViewModel = new ProductRequestViewModel();
        productRequestViewModel.setMonthlySalary(0);
        productRequestViewModel.setDateofbirth(StringUtils.EMPTY_STRING);
        productRequestViewModel.setProductType(za.co.nedbank.core.Constants.ACCOUNT_TYPES.CA.getAccountTypeCode());

        mProductUsecase.execute(mProductRequestViewModelToDataMapper.mapData(productRequestViewModel))
                .compose(bindToLifecycle())
                .map(mProductResponseDataToViewModelMapper::mapData)
                .subscribe(productResponseViewModel -> {
                    if (productResponseViewModel.getMetadata() != null
                            && productResponseViewModel.getMetadata().isSuccess()
                            && productResponseViewModel.getProductDataResponseDataViewModel() != null
                            && productResponseViewModel.getProductDataResponseDataViewModel().getProductDataViewModels() != null
                            && !productResponseViewModel.getProductDataResponseDataViewModel().getProductDataViewModels().isEmpty()) {
                        handleMiGoalsSuccessFlow(productResponseViewModel, productId);
                    } else if (productResponseViewModel.getMetadata() != null
                            && productResponseViewModel.getMetadata().getResultCode() != null
                            && productResponseViewModel.getProductDataResponseDataViewModel() != null
                            && productResponseViewModel.getMetadata().getResultCode().equalsIgnoreCase(SALARY_NOT_UPDATED)) {
                        mMemoryApplicationStorage.putString(StorageKeys.FICA_SESSION_ID,
                                productResponseViewModel.getProductDataResponseDataViewModel().getSessionId());
                        navigateToUpdateSalaryScreen(productResponseViewModel, productId);
                    } else {
                        handleNavigationErrorsFlow();
                    }
                }, throwable -> handleNavigationErrorsFlow());
    }

    private void handleJustSaveFlow(AllProductsViewModel allProductsViewModel, OpenNewAccUserEntriesViewModel userEntriesViewModel) {
        InvestmentSwitchingViewModel investmentSwitchingViewModel = new InvestmentSwitchingViewModel();
        NavigationTarget navigationTarget = NavigationTarget.to(OpenNewInvAccountTarget.TARGET_ONIA_DETAILS);
        navigationTarget.withParam(OpenNewInvAccountConstants.KEY_USERENTRIES_MODEL, userEntriesViewModel);
        navigationTarget.withParam(OpenNewInvAccountConstants.KEY_PRODUCT_MODEL, allProductsViewModel);
        navigationTarget.withParam(OpenNewInvAccountConstants.KEY_IS_FILTER_ENABLED_SUGGESION, false);
        navigationTarget.withParam(NgiNavigatorTarget.PARAM_INVESTMENT_SWITCHING_FLOW, za.co.nedbank.core.Constants.ZERO);
        navigationTarget.withParam(NavigationTarget.PARAM_NTF_INVESTMENT_TYPE, StringUtils.EMPTY_STRING);
        navigationTarget.withParam(NavigationTarget.PARAM_NTF_AGE, za.co.nedbank.core.Constants.ZERO);
        navigationTarget.withParam(NavigationTarget.PARAM_INVONLINE_FIRST_CONVERSION_DATE, StringUtils.EMPTY_STRING);
        navigationTarget.withParam(NavigationTarget.PARAM_SWITCHING_INVESTMENT_VIEW_MODEL, investmentSwitchingViewModel);
        if (za.co.nedbank.core.Constants.CODE_Y.equalsIgnoreCase(allProductsViewModel.getIsNoticeDeposit())) {
            navigationTarget.withParam(OpenNewInvAccountConstants.KEY_TO_NOTICEORFIXED_ACCOUNT, OpenNewInvAccountConstants.VAL_TO_NOTICEACCOUNT);
        } else {
            navigationTarget.withParam(OpenNewInvAccountConstants.KEY_TO_NOTICEORFIXED_ACCOUNT, OpenNewInvAccountConstants.VAL_TO_FIXEDACCOUNT);
        }
        navigationTarget.withParam(NavigationTarget.PARAM_NTF, false);
        navigationTarget.withParam(NavigationTarget.PARAM_NTF_SECOND_LOGIN, false);
        navigationRouter.navigateTo(navigationTarget);
        close();
    }

    private void handleMiGoalsSuccessFlow(ProductResponseDataViewModel productResponseViewModel, String productId) {
        List<ProductDataViewModel> productList = productResponseViewModel.getProductDataResponseDataViewModel().getProductDataViewModels();
        ProductDataViewModel productDataViewModel = getProductViewModelByProductID(productId, productList);
        if (productDataViewModel != null) {
            mMemoryApplicationStorage.putString(StorageKeys.FICA_SESSION_ID, productResponseViewModel.getProductDataResponseDataViewModel().getSessionId());
            mMemoryApplicationStorage.putString(StorageKeys.FICA_SELECTED_PRODUCT, productDataViewModel.getProductName());
            mMemoryApplicationStorage.putBoolean(StorageKeys.IS_PROFESSIONAL_PRODUCT, productDataViewModel.isProfessionalProduct());
            mMemoryApplicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);

            if (productDataViewModel.isDaeProduct()) {
                long timestamp = System.currentTimeMillis() / 1000;
                mMemoryApplicationStorage.putLong(StorageKeys.NTF_ON_BOARDING_TIME, timestamp);
                mMemoryApplicationStorage.putString(StorageKeys.SELECTED_PRODUCT_GROUP_AND_PRODUCT, productDataViewModel.getProductName());
                navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.NTF_WEBVIEW_ACTIVITY).withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SELECTED_PRODUCT, productDataViewModel)
                        .withParam(za.co.nedbank.enroll_v2.Constants.KEY_PRODUCT_ID, parseLong(productDataViewModel.getProductId())));
            } else {
                navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.MOA_PRODUCT_DETAIL).withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SELECTED_PRODUCT, productDataViewModel));
            }
            close();
        } else {
            handleNavigationErrorsFlow();
        }
    }

    private void navigateToUpdateSalaryScreen(ProductResponseDataViewModel productResponseDataViewModel, String productId) {
        NavigationTarget navTarget;
        String monthlySalary = productResponseDataViewModel.getProductDataResponseDataViewModel().getMonthlySalary();
        if (monthlySalary == null) {
            navTarget = NavigationTarget.to(EnrollV2NavigatorTarget.MOA_UPDATE_CUSTOMER_SALARY)
                    .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.IS_SALARY_AVAILABLE, false);
        } else {
            navTarget = NavigationTarget.to(EnrollV2NavigatorTarget.MOA_UPDATE_CUSTOMER_SALARY_PREP)
                    .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.SALARY, monthlySalary);
        }
        navTarget.withParam(za.co.nedbank.core.Constants.IS_FROM_DEEPLINK, true);
        navigationRouter.navigateWithResult(navTarget).subscribe(
                navigationResult -> {
                    if (navigationResult.isOk()) {
                        handleMiGoalsFlow(productId);
                    } else {
                        handleNavigationErrorsFlow(new ErrorMsgEvent(
                                view.getStringRes(R.string.error_somethings_wrong),
                                view.getStringRes(R.string.error_generic),
                                view.getStringRes(R.string.dismiss_txt)));
                    }
                }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable))
        );
    }

    private ProductDataViewModel getProductViewModelByProductID(String productId, List<ProductDataViewModel> productList) {
        for (ProductDataViewModel productViewModel : productList) {
            if (productId.equalsIgnoreCase(productViewModel.getProductId())) {
                return productViewModel;
            }
        }
        return null;
    }

    public void handleInsuranceFlow(String action, String productName) {
        mGetUserDetailsUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    mMemoryApplicationStorage.putObject(ResponseStorageKey.USERDETAILDATA,
                            mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail));
                    NavigationTarget navigationTarget =
                            NavigationTarget.to(ServicesNavigationTarget.PRODUCT_SELECTION_LIST_ACTIVITY)
                                    .withParam(za.co.nedbank.core.Constants.NAVIGATION_TAG, action)
                                    .withParam(InsuranceConstants.ParamKeys.PARAM_PRODUCT_NAME, productName);
                    navigationRouter.navigateTo(navigationTarget);
                    close();
                }, throwable -> handleNavigationErrorsFlow());
    }

    boolean isLoggedIn() {
        return !mApiInformation.isLoggedOut();
    }

    boolean canNavigateToLogin() {
        return mMemoryApplicationStorage.getBoolean(NotificationConstants.STORAGE_KEYS.CAN_NAVIGATE_TO_LOGIN, false);
    }

    private void close() {
        if (view != null) view.close();
    }

    @SuppressLint("CheckResult")
    public void moveToShop() {
        boolean doNotShowAgain = mApplicationStorage.getBoolean(StorageKeys.DATA_USAGE_NEVER_ASK_AGAIN_WIDGET_SHOP, false);

        if (doNotShowAgain || mFeatureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_DATA_USAGE_SCREEN)) {
            navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHOP_DASHBOARD));
            close();
            return;
        }

        navigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.DATA_USAGE_WARNING)
                        .withParam(za.co.nedbank.core.Constants.FROM_SCREEN, za.co.nedbank.core.Constants.FROM_SHOP_WIDGET)
                        .withParam(za.co.nedbank.core.Constants.COMPLETE_SCREEN_LABEL, view.getString(NotificationConstants.NAVIGATION_TARGET.OPEN_AVO_IN_APP)))
                .subscribe(navigationResult -> {
                    boolean accepted = navigationResult.getBooleanParam(NavigationTarget.RESULT_DATA_USAGE_WARNING_ACCEPT);
                    if (accepted) {
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SHOP_DASHBOARD));
                    }
                }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
    }

    @SuppressLint("CheckResult")
    private void handleApplyOverdraftFlow() {
        boolean isRRBClient = false;
        if (!StringUtils.isNullOrEmpty(getValueFromParamHashMap(NotificationConstants.ADDITIONAL_PARAM.IS_RRB_CLIENT))) {
            isRRBClient = Boolean.parseBoolean(getValueFromParamHashMap(NotificationConstants.ADDITIONAL_PARAM.IS_RRB_CLIENT));
        }
        if (isRRBClient) {
            navigationRouter.navigateTo(NavigationTarget.to(EnrollV2NavigatorTarget.OVERDRAFT_DETAILS_ACTIVITY));
            close();
        } else {
            checkUserEligibilityUseCase.execute(za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.OD.getOfferTypeId())
                    .compose(bindToLifecycle())
                    .subscribe(userEligibilityDataModel -> {
                        if (userEligibilityDataModel.getData() != null) {
                            if (Boolean.TRUE.equals(userEligibilityDataModel.getData().isEligible())) {
                                navigateToOffersApplyActivity();
                            } else {
                                navigateToODUserNotEligibleErrorActivity(userEligibilityDataModel.getData().getPhoneNo());
                            }
                        } else {
                            navigateToOffersApplyActivity();
                        }
                    }, throwable -> navigateToOffersApplyActivity());
        }
    }

    private void navigateToOffersApplyActivity() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.PREAPPROVED_OFFERS_APPLY_ACTIVITY)
                .withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.OFFER_TYPE_ID, za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.OD.getOfferTypeId()));
        close();
    }

    private void navigateToODUserNotEligibleErrorActivity(String phoneNumber) {
        navigationRouter.navigateTo(NavigationTarget.to(PreApprovedOffersNavigationTarget.USER_ELIGIBILITY_ERROR)
                .withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.OFFER_TYPE_ID, String.valueOf(za.co.nedbank.core.Constants.PRE_APPROVED_OFFER_TYPE.OD.getOfferTypeId()))
                .withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.PHONE_NUMBER, phoneNumber));
        close();
    }

    private void handleAvoTermAgreement() {
        String itemUrl = getValueFromParamHashMap(NotificationConstants.ADDITIONAL_PARAM.REDIRECT_URL);
        String from = getValueFromParamHashMap(NotificationConstants.Navigation.FROM);
        boolean dontShowMoveWarning = mApplicationStorage.getBoolean(
                StorageKeys.DONT_SHOW_AVO_MOVE_WARNING,
                false);

        if (dontShowMoveWarning) {
            getWalletDetails(itemUrl);
        } else {
            navigateMoveToAvoActivity(itemUrl, from);
        }
    }

    private void navigateMoveToAvoActivity(String url, String from) {
        navigationRouter.navigateTo(
                NavigationTarget.to(NavigationTarget.AVO_MOVING_TO_AVO)
                        .withParam(KEY_AVO_PROD_URL, url)
                        .withParam(NAVIGATION_FROM, from));
        close();
    }

    @SuppressLint("CheckResult")
    private void getWalletDetails(String itemUrl) {
        mWalletDetailsUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(result ->
                                openAvoTermAgreementAppInBrowser(itemUrl, result),
                        throwable -> handleNavigationErrorsFlow()
                );
    }

    private void openAvoTermAgreementAppInBrowser(
            String itemUrl,
            AvoWalletDetailsModel result
    ) {
        String mUrl = mFeatureSetController.getDynamicFeatureValue(FeatureConstants.AVO_PWA_LOGIN_URL);

        if (mUrl != null && !mUrl.startsWith(za.co.nedbank.core.Constants.HTTPS_STRING))
            mUrl = za.co.nedbank.core.Constants.HTTPS_STRING + mUrl;

        if (result.getAvoId() != null)
            mUrl = mUrl + za.co.nedbank.core.Constants.AVO_CODE + result.getAvoId();
        if (!StringUtils.isNullOrEmpty(itemUrl)) {
            mUrl += AVO_TARGET_DEEPLINK_PARAM + itemUrl;
        }
        view.startBrowser(mUrl);
        close();
    }

    private void prepareDataAndSendToAdapter(String action) {
        HashMap<String, String> paramData = null;
        HashMap<String, String> paramDataMap = view.getParamHashmap();
        if (view != null && paramDataMap != null) {
            paramData = new HashMap<>(paramDataMap);
        }
        notificationAdapter.fetchDataFromNotification(action, paramData, null);
        close();
    }
}
