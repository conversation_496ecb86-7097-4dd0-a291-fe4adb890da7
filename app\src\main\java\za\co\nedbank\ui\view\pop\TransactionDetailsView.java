package za.co.nedbank.ui.view.pop;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.payment.recent.RecentPaymentResponseDetailsViewModel;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;

public interface TransactionDetailsView extends NBBaseView {
    void handleButtonVisibility(boolean isDisable, boolean isTransactionBeyond90, boolean isSharePOPBeyond90Disable);

    void setTransactionDetailData(RecentPaymentResponseDetailsViewModel recentPaymentResponseDetailData);

    void handleProgressBar(boolean isVisible);

    void errorSnackBar();

    boolean isBeyond90Days();

    String fetchRecipientType();

    String getDataToAccountName();

    String getAccountType(TransactionHistoryViewModel transactionHistoryViewModel);

    void handlePayAgainClick();

    boolean isOnceOffFromOverViewTransaction();

    void openPopForInterNationalPayment(String bankName);

    void checkRecentPaymentTransaction();

}