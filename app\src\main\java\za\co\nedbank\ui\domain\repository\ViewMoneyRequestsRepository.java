/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.repository;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.ui.data.datastore.MoneyRequestsFactory;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsMainResponseEntity;

/**
 * Created by swapnil.gawande on 2/23/2018.
 */

public class ViewMoneyRequestsRepository implements IMoneyRequestsRepository {
    private final MoneyRequestsFactory moneyRequestsFactory;

    @Inject
    ViewMoneyRequestsRepository(final MoneyRequestsFactory moneyRequestsFactory) {
        this.moneyRequestsFactory = moneyRequestsFactory;
    }

    @Override
    public Observable<MoneyRequestsMainResponseEntity> getMoneyRequestList(String requesterRole) {
        return moneyRequestsFactory.getMoneyRequestList(requesterRole);
    }
}
