/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients;

import androidx.annotation.NonNull;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.internal.operators.observable.ObservableScan;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.constants.BeneficiaryConstants;
import za.co.nedbank.core.constants.IAccountOptions;
import za.co.nedbank.core.domain.model.ClientPreferenceDto;
import za.co.nedbank.core.domain.usecase.GetAccountsUseCase;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.errors.HttpStatus;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.payment.common.domain.data.model.limits.LimitsInnerDataModel;
import za.co.nedbank.payment.common.domain.usecases.GetCollatedUserBeneficiaryUseCase;
import za.co.nedbank.payment.common.domain.usecases.GetDefaultAccountUseCase;
import za.co.nedbank.payment.common.domain.usecases.LimitsDataUseCase;
import za.co.nedbank.payment.common.view.listener.ILimitOptions;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.payment.pay.validation.AccountBalanceValidator;
import za.co.nedbank.payment.pay.validation.AmountInMultiplesValidator;
import za.co.nedbank.payment.pay.validation.AmountLimitValidator;
import za.co.nedbank.payment.transfer.navigator.TransferNavigationTarget;
import za.co.nedbank.payment.transfer.validation.MinimumTransferAmountValidator;
import za.co.nedbank.ui.view.tracking.AppTracking;
import za.co.nedbank.ui.view.tracking.AppTrackingParam;
import za.co.nedbank.uisdk.validation.ValidatableInput;

import static za.co.nedbank.payment.pay.navigator.PayNavigatorTarget.EXTRAS.IS_QUICK_PAY_FLOW;

/**
 * Created by charurani on 21-08-2017.
 */

public class MyRecipientsPresenter extends NBBasePresenter<IMyRecipientsView> {

    private final NavigationRouter mNavigationRouter;
    private final GetCollatedUserBeneficiaryUseCase mGetCollatedUserBeneficiaryUseCase;
    private final GetDefaultAccountUseCase mGetDefaultAccountUseCase;
    private final LimitsDataUseCase mLimitsDataUseCase;
    private final GetAccountsUseCase mGetAccountsUseCase;
    private final NonEmptyTextValidator mNonEmptyTextValidator;
    private final ErrorHandler mErrorHandler;
    private final MinimumTransferAmountValidator mMinimumTransferAmountValidator;
    private final AmountInMultiplesValidator mAmountInMultiplesValidator;
    private final AmountLimitValidator mAmountLimitValidator;
    private final AccountBalanceValidator mAccountBalanceValidator;
    private Analytics mAnalytics;
    private boolean mIsRecipientSelectionTracked;

    @Inject
    MyRecipientsPresenter(@NonNull NavigationRouter navigationRouter,
                          @NonNull GetCollatedUserBeneficiaryUseCase getCollatedUserBeneficiaryUseCase,
                          @NonNull GetDefaultAccountUseCase getDefaultAccountUseCase,
                          @NonNull LimitsDataUseCase limitsDataUseCase,
                          @NonNull GetAccountsUseCase getAccountsUseCase,
                          @NonNull NonEmptyTextValidator nonEmptyTextValidator,
                          @NonNull MinimumTransferAmountValidator minimumTransferAmountValidator,
                          @NonNull AmountInMultiplesValidator amountInMultiplesValidator,
                          @NonNull AmountLimitValidator amountLimitValidator,
                          @NonNull AccountBalanceValidator accountBalanceValidator,
                          @NonNull ErrorHandler errorHandler,
                          @NonNull Analytics analytics) {
        this.mNavigationRouter = navigationRouter;
        this.mGetCollatedUserBeneficiaryUseCase = getCollatedUserBeneficiaryUseCase;
        this.mGetDefaultAccountUseCase = getDefaultAccountUseCase;
        this.mLimitsDataUseCase = limitsDataUseCase;
        this.mGetAccountsUseCase = getAccountsUseCase;
        this.mNonEmptyTextValidator = nonEmptyTextValidator;
        this.mMinimumTransferAmountValidator = minimumTransferAmountValidator;
        this.mAmountInMultiplesValidator = amountInMultiplesValidator;
        this.mAmountLimitValidator = amountLimitValidator;
        this.mAccountBalanceValidator = accountBalanceValidator;
        this.mErrorHandler = errorHandler;
        this.mAnalytics = analytics;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }

    void getUserBeneficiaryData(boolean isClearCacheData) {
        mGetCollatedUserBeneficiaryUseCase.execute(isClearCacheData)
                .compose(bindToLifecycle())
                .subscribe(
                        beneficiaryList -> {
                            if (view != null) {
                                view.receiveUserBeneficiaryBeans(beneficiaryList);
                            }
                        },
                        throwable -> {
                            if (view != null) {
                                handleError(throwable, true, IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API);
                            }
                        });
    }

    void handleRecipientsTypeClick() {
        if (view != null) {
            view.showChooseRecipientDialog();
        }
    }

    void handleFromAccountTypeClick() {
        if (view != null) {
            view.showFromAccountTypeDialog();
        }
    }

    void handleAmountTextEnter() {
        if (view != null) {
            view.handleEnterAccountNumber();
        }
    }

    void checkInputsOnScreen(@NonNull ValidatableInput<String> amountNumberInput, String fromAccount) {
        try {
            if (mNonEmptyTextValidator.validateInput(amountNumberInput.getValue()).isOk() &&
                    Float.parseFloat(FormattingUtil.convertCurrencyToAmount(amountNumberInput.getValue())) > 0
                    && !StringUtils.isNullOrEmpty(fromAccount)) {
                if (view != null) {
                    view.setPayButtonEnabled(true);
                }
            } else {
                if (view != null) {
                    view.setPayButtonEnabled(false);
                }
            }
        } catch (NumberFormatException exception) {
            if (view != null) {
                view.setPayButtonEnabled(false);
            }
        }
    }

    void handlePayButtonClick(String beneficiaryType) {
        if (view != null) {
            view.hideKeyBoard();
            view.setActivityTouchEnabled(false);
            view.showLoadingOnButton(true);
        }
        makeLimitRequests(beneficiaryType);
    }

    void handleDismissSnackBarActionClick(@IMyRecipientsView.IMyRecipientsViewAPIErrorType int apiErrorType) {
        if (view != null && apiErrorType != IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API) {
            view.setPayButtonEnabled(true);
            view.dismissSnackBar();
        }
    }

    void handleFailedLoadRecipientLayoutClick() {
        if (view != null) {
            view.setVisibilityOnRecipientLoadLayout(false);
            view.updateUserBeneficiaryData();
        }
    }

    boolean isAmountValid(ValidatableInput<String> amountInput, boolean isMobileNumberFlow) {
        mMinimumTransferAmountValidator.setMinimumTransferAmount(TransferNavigationTarget.VALUES.MINIMUM_AMOUNT);
        if (isMobileNumberFlow) {
            return mAmountInMultiplesValidator.validateInput(amountInput.getValue()).isOk();
        } else {
            return mMinimumTransferAmountValidator.validateInput(amountInput.getValue()).isOk();
        }
    }

    boolean isAmountWithinLimits(ValidatableInput<String> amountInput, double availableLimit) {
        mAmountLimitValidator.setLimit(availableLimit);
        return mAmountLimitValidator.validateInput(amountInput.getValue()).isOk();
    }

    boolean isAmountLessThanAccountBalance(ValidatableInput<String> amountInput, double selectedAccountBalance, boolean isViewAvailBalance) {
        mAccountBalanceValidator.setAvailableBalance(selectedAccountBalance, isViewAvailBalance);
        return mAccountBalanceValidator.validateInput(amountInput.getValue()).isOk();
    }

    void handleRecipientSelected() {
        if (!mIsRecipientSelectionTracked) {
            mIsRecipientSelectionTracked = true;
            mAnalytics.sendEvent(AppTracking.QUICKPAY, AppTrackingParam.QUICK_PAY_SELECT_RECIPIENT_ACTION, StringUtils.EMPTY_STRING);
        }
    }

    private void navigateToPayReview() {
        if (view != null) {
            AccountViewModel fromAccountViewModel = view.getSelectedFromAccountViewModel();
            if (fromAccountViewModel != null) {
                NavigationTarget navigationTarget = NavigationTarget.to(PayNavigatorTarget.PAY_REVIEW);
                navigationTarget.withParam(PayNavigatorTarget.EXTRAS.PAY_MODEL, view.buildPaymentsViewModel(fromAccountViewModel));
                navigationTarget.withParam(IS_QUICK_PAY_FLOW, true);
                mNavigationRouter.navigateTo(navigationTarget);
                if (view != null) {
                    view.setActivityTouchEnabled(true);
                    view.showLoadingOnButton(false);
                }
            }
        }
    }

    private void handleError(Throwable throwable, boolean shouldCheckNoContent, @IMyRecipientsView.IMyRecipientsViewAPIErrorType int apiErrorType) {
        Error error = mErrorHandler.getErrorMessage(throwable);
        if (shouldCheckNoContent && error.getCode() == HttpStatus.NO_CONTENT) {
            switch (apiErrorType) {
                case IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API:
                    view.setVisibilityOnEmptyStateView(true);
                    break;
                case IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_LIMITS_API:
                    view.showError(mErrorHandler.getErrorMessage(throwable).getMessage(), apiErrorType);
                    break;
                case IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_ACCOUNTS_API:
                    view.showError(view.provideNoAccountString(), apiErrorType);
                    break;
                case IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_DEFAULT_ACCOUNTS_API:
                case IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_INTERNATIONAL_FLOW:
                    break;
            }
        } else {
            if (apiErrorType != IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API) {
                view.showError(mErrorHandler.getErrorMessage(throwable).getMessage(), apiErrorType);
            } else {
                view.setVisibilityOnRecipientLoadLayout(true);
            }
        }
        view.setActivityTouchEnabled(true);
        if (apiErrorType != IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_RECIPIENTS_API) {
            view.showLoadingOnButton(false);
        }
        view.setPayButtonEnabled(false);
    }

    void fetchFromAccountDetails() {
        Observable<ClientPreferenceDto> defaultAccountObservable = mGetDefaultAccountUseCase.execute();
        Observable<List<AccountViewModel>> accountsObservable = mGetAccountsUseCase.execute(IAccountOptions.PAYMENT_ACCOUNTS);

        if (defaultAccountObservable != null && accountsObservable != null) {
            Observable.zip(defaultAccountObservable.doOnError(throwable -> {
                if (view != null) {
                    handleError(throwable, false, IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_DEFAULT_ACCOUNTS_API);
                }
            }), accountsObservable.doOnError(throwable -> {
                if (view != null) {
                    handleError(throwable, true, IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_ACCOUNTS_API);
                }
            }), (clientPreferenceDto, accountViewModels) -> {
                if (clientPreferenceDto != null && accountViewModels != null) {
                    if (view != null) {
                        view.receiveAccounts(accountViewModels);
                        view.receiveDefaultAccountIdentifierValue(clientPreferenceDto.value);
                    }
                    return true;
                }
                return false;
            }).compose(bindToLifecycle()).subscribe(isResponseReceivedFromApis -> {
                //response already sent to view above
            }, throwable -> {
                //need not do anything, respective error handled by observables
            });
        }
    }

    private void makeLimitRequests(String beneficiaryType) {
        Observable<LimitsInnerDataModel> paymentLimitObservable = mLimitsDataUseCase.execute(ILimitOptions.PAYMENT_LIMIT);
        Observable<LimitsInnerDataModel> imaliLimitObservable = mLimitsDataUseCase.execute(ILimitOptions.IMALI_LIMIT);

        if (paymentLimitObservable != null && imaliLimitObservable != null) {
            if (beneficiaryType.equalsIgnoreCase(BeneficiaryConstants.BENEFICIARY_TYPE_PREPAID)) {
                ObservableScan.zip(paymentLimitObservable.doOnError(throwable -> {
                    if (view != null) {
                        handleError(throwable, false, IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_LIMITS_API);
                    }
                }), imaliLimitObservable.doOnError(throwable -> {
                    if (view != null) {
                        handleError(throwable, false, IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_LIMITS_API);
                    }
                }), (paymentLimitModel, imaliLimitsModel) -> {
                    if (paymentLimitModel != null && imaliLimitsModel != null) {
                        if (view != null) {
                            double paymentLimit = paymentLimitModel.getUserAvailableDailyLimit();
                            double imaliLimit = imaliLimitsModel.getUserAvailableDailyLimit();
                            if (imaliLimit < paymentLimit) {
                                view.setLimitToBeCompared(imaliLimit);
                            } else {
                                view.setLimitToBeCompared(paymentLimit);
                            }
                        }
                        return true;
                    }
                    return false;
                }).compose(bindToLifecycle()).subscribe(isResponseReceivedFromApis -> {
                    if (isResponseReceivedFromApis) {
                        navigateToPayReview();
                    }
                }, throwable -> {
                    //need not do anything, respective error handled by observables
                });
            } else {
                paymentLimitObservable
                        .compose(bindToLifecycle())
                        .subscribe(paymentLimitsData -> {
                            if (paymentLimitsData != null) {
                                if (view != null) {
                                    view.setLimitToBeCompared(paymentLimitsData.getUserAvailableDailyLimit());
                                    navigateToPayReview();
                                }
                            }
                        }, throwable -> {
                            if (view != null) {
                                handleError(throwable, false, IMyRecipientsView.IMyRecipientsViewAPIErrorType.ERROR_IN_LIMITS_API);
                            }
                        });
            }
        }
    }
}
