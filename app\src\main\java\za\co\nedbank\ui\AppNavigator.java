/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui;

import static za.co.nedbank.core.navigation.NavigationTarget.MFC_SETTLEMENT_QUOTE;
import static za.co.nedbank.core.navigation.NavigationTarget.SCAN_PAY_AUTHENTICATION;
import static za.co.nedbank.core.navigation.NavigationTarget.SCAN_PAY_CARDS_BLOCK;
import static za.co.nedbank.core.navigation.NavigationTarget.SELECT_CARD;
import static za.co.nedbank.core.navigation.NavigationTarget.TERMS_AND_CONDITION_LOGIN;
import static za.co.nedbank.profile.view.navigation.ProfileNavigationTarget.REWARDS_LANDING;

import za.co.nedbank.core.concierge.chat.view.ChatActivity;
import za.co.nedbank.core.concierge.chat.view.InitiateChatActivity;
import za.co.nedbank.core.convochatbot.view.ChatbotErrorActivity;
import za.co.nedbank.core.convochatbot.view.ChatbotIntroductionActivity;
import za.co.nedbank.core.navigation.BaseNavigator;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.nedpay.view.scantnc.ScanTermAndCondActivity;
import za.co.nedbank.core.nedpay.view.scanupdatedtnc.ScanUpdatedTermAndCondActivity;
import za.co.nedbank.core.view.country.CountrySelectionActivity;
import za.co.nedbank.core.view.loc.LocErrorScreenActivity;
import za.co.nedbank.core.view.terms_and_condition.TermsAndConditionsActivity;
import za.co.nedbank.core.view.transaction.RestrictedTransactionActivity;
import za.co.nedbank.core.voc.view.DisplaySurveyActivity;
import za.co.nedbank.enroll_v2.EnrollV2NavigatorTarget;
import za.co.nedbank.enroll_v2.view.applications.ApplicationStatusActivity;
import za.co.nedbank.enroll_v2.view.applications.CancelApplicationActivity;
import za.co.nedbank.enroll_v2.view.applications.CancelApplicationReasonActivity;
import za.co.nedbank.enroll_v2.view.apply_credit_card.credit_details.CreditDetailsActivity;
import za.co.nedbank.enroll_v2.view.apply_credit_card.credit_details.RRBCreditDetailsActivity;
import za.co.nedbank.enroll_v2.view.apply_credit_card.statement_declaration.StatementDeclarationActivity;
import za.co.nedbank.enroll_v2.view.content.ExpendedContentPageActivity;
import za.co.nedbank.enroll_v2.view.fica.dha_lead_form.FicaDHALeadCallbackRequestActivity;
import za.co.nedbank.enroll_v2.view.fica.fais.FicaFaisActivity;
import za.co.nedbank.enroll_v2.view.fica.intent.BorrowIntentActivity;
import za.co.nedbank.enroll_v2.view.gpaylink.GpayIntroductionActivity;
import za.co.nedbank.enroll_v2.view.linking.AutoAccountsLinkingActivity;
import za.co.nedbank.enroll_v2.view.linking.ManualLinkingAccountActivity;
import za.co.nedbank.enroll_v2.view.login.enroll_v3_landing.EnrollLandingV3Activity;
import za.co.nedbank.enroll_v2.view.moa.product_detail.ProductDetailActivity;
import za.co.nedbank.enroll_v2.view.ntf.view.ntf_login.NTFLoginActivity;
import za.co.nedbank.enroll_v2.view.ntf.view.ntf_signup.NTFSignupActivity;
import za.co.nedbank.enroll_v2.view.ntf.view.ntf_webview.EnrollNTFWebviewActivity;
import za.co.nedbank.enroll_v2.view.ntf.view.ntf_your_app.EnrollNTFYourAppActivity;
import za.co.nedbank.enroll_v2.view.personal_loan.personal_loan_product.PersonalLoanDetailsActivity;
import za.co.nedbank.enroll_v2.view.scan_authentication.ScanPayAuthenticationActivity;
import za.co.nedbank.enroll_v2.view.view_more_detail.ProductDetailDeepLinkActivity;
import za.co.nedbank.kidsbanking.view.child.deRegister.DeRegisterChildActivity;
import za.co.nedbank.kidsbanking.view.child.fraudcampaignalert.FraudCampaignAlertActivity;
import za.co.nedbank.kidsbanking.view.child.register.ChildRegisterActivity;
import za.co.nedbank.kidsbanking.view.child.tnc.UpdatedTncMinorActivity;
import za.co.nedbank.kidsbanking.view.common.ChildTransactionErrorMessageActivity;
import za.co.nedbank.kidsbanking.view.common.errorscreen.KidsApiErrorActivity;
import za.co.nedbank.kidsbanking.view.parent.delinkMinorProfile.DelinkChildProfileActivity;
import za.co.nedbank.kidsbanking.view.parent.fraudCampaignNotice.AckFraudAwarenessActivity;
import za.co.nedbank.kidsbanking.view.parent.register.KidsBankingRegisterParentActivity;
import za.co.nedbank.kidsbanking.view.parent.termsnconditions.ParentAckUpdatedTncActivity;
import za.co.nedbank.loans.preapprovedaccountsoffers.navigator.PreApprovedOffersNavigationTarget;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.credit_health.loading.CreditHealthLoadingActivity;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.disclaimers.PreApprovedOffersDisclaimersActivity;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.landingactivity.LoanLandingActivity;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.non_ghost_disclaimer.NonGhostDisclaimerActivity;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.view_preapproved_offers.PreApprovedOffersNotificationsActivity;
import za.co.nedbank.payment.buy.view.BuyLandingActivity;
import za.co.nedbank.payment.common.view.sensitive.SensitiveTransactionError;
import za.co.nedbank.payment.mfcsettlementrequest.view.mfcsettlementquote.MfcSettlementQuoteActivity;
import za.co.nedbank.payment.ngi.investmentoption.landing.InvestmentOptionLandingActivity;
import za.co.nedbank.payment.ngi.view.fatcanoncompliant.FatcaNonCompliantActivity;
import za.co.nedbank.payment.ngi.view.genericngierror.NGIGenericErrorActivity;
import za.co.nedbank.payment.ngi.view.getstarted.GetStartedActivity;
import za.co.nedbank.payment.ngi.view.taxdeclaration.TaxDeclarationActivity;
import za.co.nedbank.payment.ntf.agegroup.NTFAgeGroupActivity;
import za.co.nedbank.payment.opennewinvaccount.view.fiasdisclaimer.FIASDisclaimerActivity;
import za.co.nedbank.payment.opennewinvaccount.view.filterhelpmedecide.MPAQuestionsActivity;
import za.co.nedbank.payment.opennewinvaccount.view.findguarenteedsaving.HelpMeFindGuaranteedSavingsActivity;
import za.co.nedbank.payment.opennewinvaccount.view.investmenterror.InvestmentErrorActivity;
import za.co.nedbank.payment.opennewinvaccount.view.oniaeducation.OniaEducationActivity;
import za.co.nedbank.payment.opennewinvaccount.view.rightoptions.RightOptionsActivity;
import za.co.nedbank.payment.opennewinvaccount.view.suggestion.SuggestionActivity;
import za.co.nedbank.payment.pay.bill_payment.view.card.BillPaymentChooseCardActivity;
import za.co.nedbank.payment.pay.view.amount.PayAmountActivity;
import za.co.nedbank.payment.reinvest.view.fiasdisclaimer.FIASDisclaimerReInvestActivity;
import za.co.nedbank.payment.rpp.view.details.RppShapIdDetailsActivity;
import za.co.nedbank.payment.rpp.view.edit.RppEditShapIdActivity;
import za.co.nedbank.payment.rpp.view.error.RPPRetrievShapIdFailureActivity;
import za.co.nedbank.payment.rpp.view.linkedaccount.LinkedAccountListActivity;
import za.co.nedbank.payment.rpp.view.management.RPPManagementActivity;
import za.co.nedbank.payment.rpp.view.registration.RppRegisterShapeIdActivity;
import za.co.nedbank.payment.rpp.view.registration_success.RppRegisterSuccessActivity;
import za.co.nedbank.payment.rtp.view.rtp_container.MakePayshapRequestActivity;
import za.co.nedbank.payment.scan.pay.view.NoCardsAvailable.NoCardsActivity;
import za.co.nedbank.payment.scan.pay.view.SelectCardActivity;
import za.co.nedbank.payment.scan.pay.view.cards_blocked.ScanPayCardsBlockedActivity;
import za.co.nedbank.payment.scan.pay.view.done.ScanPaymentDoneActivity;
import za.co.nedbank.payment.scan.pay.view.not_enrolled.ScanNotEnrolledActivity;
import za.co.nedbank.payment.scan.pay.view.payment.PaymentActivity;
import za.co.nedbank.payment.transfer.view.HlSettlementErrorActivity;
import za.co.nedbank.payment.transfer.view.TransferActivity;
import za.co.nedbank.payment.vas.common.view.feature_unavailable.FeatureUnavailableActivity;
import za.co.nedbank.payment.vas.common.view.landing.VasLandingActivity;
import za.co.nedbank.payment.vas.common.view.termconditions.VasTermAndConditionsActivity;
import za.co.nedbank.payment.vas.common.view.vas_product_toggle_check.VasProductToggleOffActivity;
import za.co.nedbank.profile.view.feedback.success.FeedbackSuccessActivity;
import za.co.nedbank.profile.view.map.moredetails.openinghours.OpeningHoursActivity;
import za.co.nedbank.profile.view.profile.edit.occupation.OccupationListActivity;
import za.co.nedbank.profile.view.profile.edit.suburb.SuburbAndCitySearchActivity;
import za.co.nedbank.profile.view.profile.limits.ProfileLimitForBusinessUser;
import za.co.nedbank.profile.view.profile.limits.ProfileLimitsActivity;
import za.co.nedbank.profile.view.profile.management.ProfileManagementActivity;
import za.co.nedbank.profile.view.rewards.RewardsLandingActivity;
import za.co.nedbank.profile.view.rewards.RewardsTnCActivity;
import za.co.nedbank.profile.view.settings.SettingsActivity;
import za.co.nedbank.services.digitalunboxing.view.UnboxingActivity;
import za.co.nedbank.services.familybanking.view.spouseinvitation.FamilyBankingInviteAndAcceptConditionsActivity;
import za.co.nedbank.services.familybanking.view.spouseinvitation.FamilyBankingInviteAndOpenAccountActivity;
import za.co.nedbank.services.moneymanager.view.flow_navigation.MoneyTrackerFlowNavigationActivity;
import za.co.nedbank.services.nfw.view.dashboard.NFWDashboardActivity;
import za.co.nedbank.services.view.account.AccountDetailsActivity;
import za.co.nedbank.services.view.account.features.ShareAccountInfoActivity;
import za.co.nedbank.services.view.account.multipleformat_statements.view.AccountStatementsActivity;
import za.co.nedbank.services.view.account.transactions.once_off_payments.OnceOffPaymentActivity;
import za.co.nedbank.services.view.contact.report_fraud.ReportFraudFailureActivity;
import za.co.nedbank.services.view.contact.report_fraud.ReportFraudMaxAttemptsActivity;
import za.co.nedbank.services.view.contact.report_fraud.ReportFraudSuccessActivity;
import za.co.nedbank.services.view.contact.report_suspicious.ContactUsActivity;
import za.co.nedbank.services.view.contact.report_suspicious.ContactUsItemActivity;
import za.co.nedbank.services.view.contact.report_suspicious.GetInTouchActivity;
import za.co.nedbank.services.view.contact.report_suspicious.ReportSuspiciousActivity;
import za.co.nedbank.services.view.contact.report_suspicious.tagbanker.TagYourBankerActivity;
import za.co.nedbank.services.view.contact.report_suspicious.tagbanker.status.TaggingSuccessActivity;
import za.co.nedbank.services.view.contact.suspicious_reasons.ReasonsActivity;
import za.co.nedbank.services.view.global_navigation.navigation_container.SelectStatementOrDocumentOptionActivity;
import za.co.nedbank.services.view.online_savings.educational.InterestRatesActivity;
import za.co.nedbank.ui.notifications.NotificationNavigationActivity;
import za.co.nedbank.ui.splashscreen.SplashScreenActivity;
import za.co.nedbank.ui.view.card_delivery.branch_confirmation.CardDeliveryBranchConfirmationActivity;
import za.co.nedbank.ui.view.card_delivery.deliver_result.CardDeliveryResultActivity;
import za.co.nedbank.ui.view.card_delivery.deliver_to_me_confirmation.DeliverToMeConfirmationActivity;
import za.co.nedbank.ui.view.card_delivery.delivery_options.CardDeliveryOptionsActivity;
import za.co.nedbank.ui.view.card_delivery.locker_confirmation.CardDeliveryLockerConfirmationActivity;
import za.co.nedbank.ui.view.card_delivery.locker_map.LockerMapActivity;
import za.co.nedbank.ui.view.coming_soon.ComingSoonActivity;
import za.co.nedbank.ui.view.deeplink.view.AppDeeplinkActivity;
import za.co.nedbank.ui.view.developer_option.DeveloperOptionAlertActivity;
import za.co.nedbank.ui.view.enbichatbot.ChatbotActivity;
import za.co.nedbank.ui.view.error.GenericTechnicalErrorActivity;
import za.co.nedbank.ui.view.fatca.FatcaRestrictionMessageActivity;
import za.co.nedbank.ui.view.home.HomeActivity;
import za.co.nedbank.ui.view.home.add_recipient.AddRecipientActivity;
import za.co.nedbank.ui.view.home.cms_content.CMSMediaContentActivity;
import za.co.nedbank.ui.view.home.edit_recipient.EditRecipientActivity;
import za.co.nedbank.ui.view.home.fc_deep_linking.DeepLinkIntermediateActivity;
import za.co.nedbank.ui.view.home.investments.AccountTypeInvestmentActivity;
import za.co.nedbank.ui.view.home.latest.latestWidget.LatestWidgetActivity;
import za.co.nedbank.ui.view.home.money_requests.send_money_request.MoneyRequestActivity;
import za.co.nedbank.ui.view.home.money_requests.send_money_request.MoneyRequestSuccessActivity;
import za.co.nedbank.ui.view.home.money_requests.send_money_request.RecipientContactsActivity;
import za.co.nedbank.ui.view.home.money_requests.send_money_request.ViewMoneyRequestDetailsActivity;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.MoneyPaymentActivity;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.MoneyPaymentReviewActivity;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.MoneyPaymentSuccessActivity;
import za.co.nedbank.ui.view.home.money_requests.view_money_response.ViewMoneyRequestsActivity;
import za.co.nedbank.ui.view.home.odd_restriction.OddRestrictionErrorActivity;
import za.co.nedbank.ui.view.home.product_deep_linking.ProductDeepLinkActivity;
import za.co.nedbank.ui.view.home.quick_pay.QuickPayActivity;
import za.co.nedbank.ui.view.home.recipient_detail.RecipientDetailActivity;
import za.co.nedbank.ui.view.home.recipient_detail.RecipientDetailWithHistoryActivity;
import za.co.nedbank.ui.view.home.tax_certificates_accounts.TaxCertificatesAccountsActivity;
import za.co.nedbank.ui.view.home.verifyme.IDVLVerifyMeActivity;
import za.co.nedbank.ui.view.ita.ITAFlowActivity;
import za.co.nedbank.ui.view.ita.ita_authentication.ITAAuthenticationActivity;
import za.co.nedbank.ui.view.ita.qrcodelogin.QrCodeProcessingActivity;
import za.co.nedbank.ui.view.ita.qrcodelogin.QrLoginResultActivity;
import za.co.nedbank.ui.view.notification.NotificationCenterActivity;
import za.co.nedbank.ui.view.notification.ajo_notification_details.AJONotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.ajo_notification_details.navigation.NavigationHandlerActivity;
import za.co.nedbank.ui.view.notification.contextswitch.ContextSwitchConfirmationActivity;
import za.co.nedbank.ui.view.notification.notification_details.NotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.notification_details.redirect_url.RedirectUrlActivity;
import za.co.nedbank.ui.view.notification.notification_messages.NotificationMessagesActivity;
import za.co.nedbank.ui.view.notification.notification_permission.NotificationEnableActivity;
import za.co.nedbank.ui.view.notification.notification_preferences.NotificationPreferenceActivity;
import za.co.nedbank.ui.view.notification.notification_preferences.account_preference.AccountPreferenceActivity;
import za.co.nedbank.ui.view.notification.notification_preferences.all_accounts_preferences.AllAccountsPreferenceActivity;
import za.co.nedbank.ui.view.notification.notification_preferences.delivery_preferences.DeliveryPreferencesActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.details.TransactionNotificationDetailsActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.inbox.TransactionInboxActivity;
import za.co.nedbank.ui.view.notification.transaction_notification.report_fraud.ReportFraudTransactionActivity;
import za.co.nedbank.ui.view.pay_me.PayMeActivity;
import za.co.nedbank.ui.view.pop.NewShareProofOfPaymentActivity;
import za.co.nedbank.ui.view.pop.SharePopMethodsActivity;
import za.co.nedbank.ui.view.pop.TransactionDetailsActivity;
import za.co.nedbank.ui.view.pop.failure_pop.FailurePopActivity;
import za.co.nedbank.ui.view.pop.success_pop.SuccessPopActivity;
import za.co.nedbank.ui.view.refica.FicaErrorActivity;
import za.co.nedbank.ui.view.refica.FicaSuccessActivity;
import za.co.nedbank.ui.view.refica.VerifyMeActivity;
import za.co.nedbank.ui.view.retention.MultipleAccountsShareActivity;
import za.co.nedbank.ui.view.retention.RetentionTaskSelectionActivity;
import za.co.nedbank.ui.view.retention.RetentionWelcomeActivity;
import za.co.nedbank.ui.view.retention.feedback.RetentionFeedbackActivity;
import za.co.nedbank.ui.view.retention.feedback.RetentionFeedbackSuccessActivity;
import za.co.nedbank.ui.view.retention.notification_journey.RetentionInformationActivity;
import za.co.nedbank.ui.view.retention.notification_journey.RetentionNotificationJourneyActivity;
import za.co.nedbank.ui.view.sales_landing.SalesLandingActivity;

public class AppNavigator extends BaseNavigator {

    public AppNavigator() {
        forTarget(NavigationTarget.LANDING_LOANS).openActivity(LoanLandingActivity.class);
        forTarget(NavigationTarget.HOME).openActivity(HomeActivity.class);
        forTarget(NavigationTarget.FICA_VERIFY_ME).openActivity(VerifyMeActivity.class);
        forTarget(NavigationTarget.FICA_SUCCESS).openActivity(FicaSuccessActivity.class);
        forTarget(NavigationTarget.FICA_ERROR).openActivity(FicaErrorActivity.class);
        forTarget(NavigationTarget.SHARE_ACC_INFO).openActivity(ShareAccountInfoActivity.class);
        forTarget(NavigationTarget.TRANSFER).openActivity(TransferActivity.class);
        forTarget(NavigationTarget.HL_SETTLEMENT_ERROR).openActivity(HlSettlementErrorActivity.class);
        forTarget(NavigationTarget.SPLASH_SCREEN).openActivity(SplashScreenActivity.class);
        forTarget(NavigationTarget.DEVELOPER_OPTION_ALERT).openActivity(DeveloperOptionAlertActivity.class);
        forTarget(NavigationTarget.BUY_LANDING).openActivity(BuyLandingActivity.class);
        forTarget(NavigationTarget.COMING_SOON).openActivity(ComingSoonActivity.class);
        forTarget(NavigationTarget.ADD_RECIPIENT).openActivity(AddRecipientActivity.class);
        forTarget(NavigationTarget.EDIT_RECIPIENT).openActivity(EditRecipientActivity.class);
        forTarget(NavigationTarget.RECIPIENT_DETAIL).openActivity(RecipientDetailActivity.class);
        forTarget(NavigationTarget.RECIPIENT_DETAIL_WITH_HISTORY).openActivity(RecipientDetailWithHistoryActivity.class);
        forTarget(NavigationTarget.SCAN_TO_PAYMENT).openActivity(PaymentActivity.class);
        forTarget(NavigationTarget.SCAN_TERM_AND_CONDITION_SCREEN).openActivity(ScanTermAndCondActivity.class);
        forTarget(NavigationTarget.SCAN_TERM_AND_CONDITION_UPDATED_SCREEN).openActivity(ScanUpdatedTermAndCondActivity.class);
        forTarget(NavigationTarget.SCAN_NOT_ENROLLED_SCREEN).openActivity(ScanNotEnrolledActivity.class);
        forTarget(NavigationTarget.SCAN_PAYMENT_DONE_SCREEN).openActivity(ScanPaymentDoneActivity.class);
        forTarget(NavigationTarget.NOCARDS_AVAILABLE).openActivity(NoCardsActivity.class);
        forTarget(SCAN_PAY_AUTHENTICATION).openActivity(ScanPayAuthenticationActivity.class);
        forTarget(TERMS_AND_CONDITION_LOGIN).openActivity(TermsAndConditionsActivity.class);
        forTarget(NavigationTarget.VIEW_MONEY_REQUESTS).openActivity(ViewMoneyRequestsActivity.class);
        forTarget(NavigationTarget.MONEY_REQUEST_SUCCESS).openActivity(MoneyRequestSuccessActivity.class);
        forTarget(NavigationTarget.VIEW_MONEY_RESPONSE).openActivity(MoneyPaymentActivity.class);
        forTarget(NavigationTarget.MONEY_RESPONSE_REVIEW).openActivity(MoneyPaymentReviewActivity.class);
        forTarget(NavigationTarget.SEND_MONEY_REQUESTS).openActivity(MoneyRequestActivity.class);
        forTarget(NavigationTarget.MONEY_REQUEST_DETAIL).openActivity(ViewMoneyRequestDetailsActivity.class);
        forTarget(NavigationTarget.MONEY_RESPONSE_SUCCESS).openActivity(MoneyPaymentSuccessActivity.class);
        forTarget(NavigationTarget.PHONE_CONTACT_BENEFICIARY_LIST).openActivity(RecipientContactsActivity.class);
        forTarget(SELECT_CARD).openActivity(SelectCardActivity.class);
        forTarget(NavigationTarget.NGI_TAX_DECLARATION).openActivity(TaxDeclarationActivity.class);
        forTarget(SCAN_PAY_CARDS_BLOCK).openActivity(ScanPayCardsBlockedActivity.class);
        forTarget(NavigationTarget.CHAT_SCREEN).openActivity(ChatActivity.class);
        forTarget(NavigationTarget.CONVO_CHAT_SCREEN).openActivity(ChatbotActivity.class);
        forTarget(NavigationTarget.CHATBOT_INTRODUCTION_SCREEN).openActivity(ChatbotIntroductionActivity.class);
        forTarget(NavigationTarget.INITIATE_CHAT_SCREEN).openActivity(InitiateChatActivity.class);
        forTarget(NavigationTarget.FEEDBACK_SUCCESS).openActivity(FeedbackSuccessActivity.class);
        forTarget(MFC_SETTLEMENT_QUOTE).openActivity(MfcSettlementQuoteActivity.class);
        forTarget(NavigationTarget.REWARDS_TNC).openActivity(RewardsTnCActivity.class);
        forTarget(NavigationTarget.NID_FORGOT_COUNTRY_SELECTION).openActivity(CountrySelectionActivity.class);
        forTarget(NavigationTarget.TARGET_ONIA_FIAS_DISCLAIMER).openActivity(FIASDisclaimerActivity.class);
        forTarget(NavigationTarget.TARGET_ONIA_INVESTMENT_ERROR).openActivity(InvestmentErrorActivity.class);
        forTarget(NavigationTarget.NTF_AGE_GROUP_ACTIVITY).openActivity(NTFAgeGroupActivity.class);
        forTarget(NavigationTarget.TARGET_NGI_MINOR_ERROR).openActivity(NGIGenericErrorActivity.class);
        forTarget(NavigationTarget.TARGET_FATCA_NON_COMPLIANT_ERROR).openActivity(FatcaNonCompliantActivity.class);
        forTarget(NavigationTarget.DEEP_LINK_INTERMEDIATE_SCREEN).openActivity(DeepLinkIntermediateActivity.class);
        forTarget(NavigationTarget.DEEP_LINK_PRODUCT_SHORT_DETAIL).openActivity(ProductDeepLinkActivity.class);
        forTarget(NavigationTarget.DEEP_LINK_PRODUCT_FULL_DETAIL).openActivity(ProductDetailDeepLinkActivity.class);
        forTarget(NavigationTarget.ODD_RESTRICTION_ERROR).openActivity(OddRestrictionErrorActivity.class);

        forTarget(NavigationTarget.ENROLL_V2_LANDING).openActivity(EnrollLandingV3Activity.class);

        forTarget(REWARDS_LANDING).openActivity(RewardsLandingActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_NAVIGATION).openActivity(NotificationNavigationActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_CENTER).openActivity(NotificationCenterActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_DETAILS).openActivity(NotificationDetailsActivity.class);
        forTarget(NavigationTarget.TARGET_AJO_NOTIFICATION_DETAILS).openActivity(AJONotificationDetailsActivity.class);
        forTarget(NavigationTarget.TARGET_GLOBAL_NAVIGATION_HANDLER).openActivity(NavigationHandlerActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_INVITE_AND_ACCEPT_FAMILY_BANKING).openActivity(FamilyBankingInviteAndAcceptConditionsActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_INVITE_AND_OPEN_ACCOUNT_FAMILY_BANKING).openActivity(FamilyBankingInviteAndOpenAccountActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_MESSAGES).openActivity(NotificationMessagesActivity.class);
        forTarget(NavigationTarget.TARGET_TRANSACTION_NOTIFICATION_REPORT_FRAUD).openActivity(ReportFraudTransactionActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_TRANSACTION).openActivity(TransactionInboxActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_TRANSACTION_DETAILS).openActivity(TransactionNotificationDetailsActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES).openActivity(NotificationPreferenceActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_DELIVERY_PREFRENCES).openActivity(DeliveryPreferencesActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES_ALL_ACCOUNTS).openActivity(AllAccountsPreferenceActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES_ACCOUNT).openActivity(AccountPreferenceActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_REDIRECT_URL).openActivity(RedirectUrlActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_ERROR).openActivity(FailurePopActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_CONTEXT_SWITCH_CONFIRM).openActivity(ContextSwitchConfirmationActivity.class);
        forTarget(NavigationTarget.TARGET_NOTIFICATION_ENABLE).openActivity(NotificationEnableActivity.class);
        forTarget(PreApprovedOffersNavigationTarget.PRE_APPROVED_OFFERS_NOTIFICATIONS).openActivity(PreApprovedOffersNotificationsActivity.class);
        forTarget(NavigationTarget.REINVEST_FIAS).openActivity(FIASDisclaimerReInvestActivity.class);
        forTarget(NavigationTarget.VAS).openActivity(VasLandingActivity.class);
        forTarget(NavigationTarget.VAS_TERM_AND_CONDITIONS).openActivity(VasTermAndConditionsActivity.class);
        forTarget(NavigationTarget.CONTACT_US).openActivity(ContactUsActivity.class);
        forTarget(NavigationTarget.CONTACT_US_ITEM).openActivity(ContactUsItemActivity.class);
        forTarget(NavigationTarget.GET_IN_TOUCH).openActivity(GetInTouchActivity.class);
        forTarget(NavigationTarget.TARGET_RIGHT_OPTIONS).openActivity(RightOptionsActivity.class);
        forTarget(NavigationTarget.TARGET_SUGGESTION_ACTIVITY).openActivity(SuggestionActivity.class);
        forTarget(NavigationTarget.TARGET_HELP_ME_FIND_GUARANTEED_SAVINGS_ACTIVITY).openActivity(HelpMeFindGuaranteedSavingsActivity.class);
        forTarget(NavigationTarget.TARGET_MPA_QUESTIONS).openActivity(MPAQuestionsActivity.class);
        forTarget(NavigationTarget.TARGET_ONIA_EDU_PAGE).openActivity(OniaEducationActivity.class);
        forTarget(NavigationTarget.MORE_REPORT_FRAUD).openActivity(ReportSuspiciousActivity.class);
        forTarget(NavigationTarget.REPORT_FRAUD_SUCCESS).openActivity(ReportFraudSuccessActivity.class);
        forTarget(NavigationTarget.REPORT_FRAUD_FAILURE).openActivity(ReportFraudFailureActivity.class);
        forTarget(NavigationTarget.REPORT_FRAUD_MAX_ATTEMPTS).openActivity(ReportFraudMaxAttemptsActivity.class);
        forTarget(NavigationTarget.SUSPICIOUS_REASONS).openActivity(ReasonsActivity.class);
        forTarget(NavigationTarget.MANUAL_ACCOUNT_LINKING).openActivity(ManualLinkingAccountActivity.class);
        forTarget(NavigationTarget.KB_AUTO_ACCOUNT_LINKING).openActivity(AutoAccountsLinkingActivity.class);
        forTarget(NavigationTarget.CMS_MEDIA_CONTENT).openActivity(CMSMediaContentActivity.class);
        forTarget(NavigationTarget.NEW_SHARE_PROOF_OF_PAYMENT_METHOD).openActivity(SharePopMethodsActivity.class);
        forTarget(NavigationTarget.NEW_SHARE_PROOF_OF_PAYMENT).openActivity(NewShareProofOfPaymentActivity.class);
        forTarget(NavigationTarget.TRANSACTION_HISTORY_DETAIL).openActivity(TransactionDetailsActivity.class);
        forTarget(NavigationTarget.SHARE_PROOF_OF_PAYMENT_API_RESPONSE).openActivity(SuccessPopActivity.class);
        forTarget(NavigationTarget.PROFILE_MANAGEMENT).openActivity(ProfileManagementActivity.class);
        forTarget(NavigationTarget.MONEY_TRACKER_FLOW_NAVIGATION_ACTIVITY).openActivity(MoneyTrackerFlowNavigationActivity.class);
        forTarget(NavigationTarget.TARGET_NGI_GET_STARTED).openActivity(GetStartedActivity.class);
        forTarget(NavigationTarget.TARGET_NGI_COUNTRY_LIST).openActivity(CountrySelectionActivity.class);
        forTarget(NavigationTarget.QUICK_PAY).openActivity(QuickPayActivity.class);
        forTarget(NavigationTarget.PAY_ME).openActivity(PayMeActivity.class);
        forTarget(NavigationTarget.COVID_19).openActivity(LatestWidgetActivity.class);

        forTarget(NavigationTarget.CREDIT_LIMIT_INCREASE_ENTRY).openActivity(NonGhostDisclaimerActivity.class);
        forTarget(NavigationTarget.PAY_AMOUNT).openActivity(PayAmountActivity.class);
        forTarget(NavigationTarget.TAX_CERTIFICATE_ACCOUNTS).openActivity(TaxCertificatesAccountsActivity.class);
        forTarget(NavigationTarget.INTEREST_RATES_SCREEN).openActivity(InterestRatesActivity.class);
        forTarget(NavigationTarget.EXPENDED_CONTENT_PAGE).openActivity(ExpendedContentPageActivity.class);
        forTarget(NavigationTarget.TARGET_FAIS_EVERDAY_BANKING).openActivity(FicaFaisActivity.class);
        forTarget(NavigationTarget.TARGET_DISCLAIMER_EVERDAY_BANKING).openActivity(PreApprovedOffersDisclaimersActivity.class);
        forTarget(NavigationTarget.FICA_DHA_LEAD_CALL_BACK_REQUEST).openActivity(FicaDHALeadCallbackRequestActivity.class);
        forTarget(NavigationTarget.BRANCH_OPENING_HOURS_ACTIVITY).openActivity(OpeningHoursActivity.class);
        //retention
        forTarget(NavigationTarget.RETENTION_FEEDBACK_SCREEN).openActivity(RetentionFeedbackActivity.class);
        forTarget(NavigationTarget.RETENTION_FEEDBACK_SUCCESS).openActivity(RetentionFeedbackSuccessActivity.class);
        forTarget(NavigationTarget.RETENTION_TASK_SELECTION_ACTIVITY).openActivity(RetentionTaskSelectionActivity.class);
        forTarget(NavigationTarget.RETENTION_WELCOME_ACTIVITY).openActivity(RetentionWelcomeActivity.class);
        forTarget(NavigationTarget.RETENTION_MULTIPLE_SHARE_ACCOUNT_ACTIVITY).openActivity(MultipleAccountsShareActivity.class);
        forTarget(NavigationTarget.RETENTION_INFO_ACTIVITY).openActivity(RetentionInformationActivity.class);
        forTarget(NavigationTarget.RETENTION_TASK_SELECTION_NOTIFICATION_JOURNEY).openActivity(RetentionNotificationJourneyActivity.class);
        forTarget(NavigationTarget.TAG_YOUR_BANKER).openActivity(TagYourBankerActivity.class);
        forTarget(NavigationTarget.TAG_YOUR_BANKER_SUCCESS).openActivity(TaggingSuccessActivity.class);
        forTarget(NavigationTarget.TARGET_ITA_FLOW_SCREEN).openActivity(ITAFlowActivity.class);
        forTarget(NavigationTarget.QRCODE_FLOW_PROCESSING).openActivity(QrCodeProcessingActivity.class);
        forTarget(NavigationTarget.QR_LOGIN_RESULT).openActivity(QrLoginResultActivity.class);
        forTarget(NavigationTarget.TARGET_ITA_AUTHENTICATION_SCREEN).openActivity(ITAAuthenticationActivity.class);
        forTarget(NavigationTarget.BILL_PAYMENT_CHOOSE_CARD).openActivity(BillPaymentChooseCardActivity.class);
        forTarget(NavigationTarget.TARGET_ACCOUNT_TYPE_LIST).openActivity(AccountTypeInvestmentActivity.class);
        forTarget(NavigationTarget.SETTINGS).openActivity(SettingsActivity.class);
        forTarget(NavigationTarget.PROFILE_LIMITS).openActivity(ProfileLimitsActivity.class);
        forTarget(NavigationTarget.ONCEOFF_PAYMENTS).openActivity(OnceOffPaymentActivity.class);
        forTarget(NavigationTarget.CREDIT_HEALTH_FLOW).openActivity(CreditHealthLoadingActivity.class);
        forTarget(NavigationTarget.DISPLAY_SURVEY).openActivity(DisplaySurveyActivity.class);
        forTarget(NavigationTarget.LOC_ERROR_SCREEN).openActivity(LocErrorScreenActivity.class);
        forTarget(NavigationTarget.SHAP_ID_MANAGEMENT).openActivity(RPPManagementActivity.class);
        forTarget(NavigationTarget.SHAP_ID_DETAILS).openActivity(RppShapIdDetailsActivity.class);
        forTarget(NavigationTarget.MAKE_PAYSHAP_REQUEST).openActivity(MakePayshapRequestActivity.class);
        forTarget(NavigationTarget.REGISTER_SHAPE_ID).openActivity(RppRegisterShapeIdActivity.class);
        forTarget(NavigationTarget.RPP_REGISTER_SUCCESS).openActivity(RppRegisterSuccessActivity.class);
        forTarget(NavigationTarget.SHAP_ID_EDIT).openActivity(RppEditShapIdActivity.class);
        forTarget(NavigationTarget.RETRIEVE_SHAP_ID_FAILURE).openActivity(RPPRetrievShapIdFailureActivity.class);
        forTarget(NavigationTarget.SHAP_ID_LINKED_ACCOUNTS).openActivity(LinkedAccountListActivity.class);
        forTarget(NavigationTarget.RESTRICTED_TRANSACTION_SCREEN).openActivity(RestrictedTransactionActivity.class);

        forTarget(NavigationTarget.SELECT_STATEMENT_OR_DOCUMENT_OPTION).openActivity(SelectStatementOrDocumentOptionActivity.class);
        forTarget(NavigationTarget.DEBIT_ORDER_FRAGMENT).openActivity(AccountDetailsActivity.class);
        forTarget(NavigationTarget.ACCOUNT_STATEMENTS_ACTIVITY).openActivity(AccountStatementsActivity.class);
        forTarget(NavigationTarget.SHARE_ACCOUNT_INFO_ACTIVITY).openActivity(ShareAccountInfoActivity.class);

        forTarget(NavigationTarget.ERROR_SCREEN_ACTIVITY).openActivity(ChatbotErrorActivity.class);

        forTarget(NavigationTarget.BORROW_INTENT_ACTIVITY).openActivity(BorrowIntentActivity.class);
        /*Card Delivery*/
        forTarget(NavigationTarget.CARD_DELIVERY_BRANCH_CONFIRMATION).openActivity(CardDeliveryBranchConfirmationActivity.class);
        forTarget(NavigationTarget.CARD_DELIVERY_LOCKER_CONFIRMATION).openActivity(CardDeliveryLockerConfirmationActivity.class);
        forTarget(NavigationTarget.CARD_DELIVERY_DELIVER_TO_ME_CONFIRMATION).openActivity(DeliverToMeConfirmationActivity.class);
        forTarget(NavigationTarget.CARD_DELIVERY_RESULT).openActivity(CardDeliveryResultActivity.class);
        forTarget(NavigationTarget.CARD_DELIVERY_OPTIONS).openActivity(CardDeliveryOptionsActivity.class);
        forTarget(NavigationTarget.LOCKER_MAP).openActivity(LockerMapActivity.class);
        forTarget(NavigationTarget.NTF_YOUR_APP_ACTIVITY).openActivity(EnrollNTFYourAppActivity.class);
        forTarget(NavigationTarget.NTF_WEBVIEW_ACTIVITY).openActivity(EnrollNTFWebviewActivity.class);
        forTarget(NavigationTarget.NTF_LOGIN_ACTIVITY).openActivity(NTFLoginActivity.class);
        forTarget(NavigationTarget.NTF_SIGNUP_ACTIVITY).openActivity(NTFSignupActivity.class);
        forTarget(NavigationTarget.MOA_PRODUCT_DETAIL).openActivity(ProductDetailActivity.class);
        forTarget(NavigationTarget.PROFILE_LIMITS_BUSINESS_USER).openActivity(ProfileLimitForBusinessUser.class);
        forTarget(NavigationTarget.NFW_DASHBOARD_ACTIVITY).openActivity(NFWDashboardActivity.class);
        forTarget(NavigationTarget.VAS_TOGGLE_OFF).openActivity(VasProductToggleOffActivity.class);
        forTarget(NavigationTarget.VAS_FEATURE_UNAVAILABLE).openActivity(FeatureUnavailableActivity.class);
        forTarget(NavigationTarget.FATCA_RESTRICTION_MESSAGE_SCREEN).openActivity(FatcaRestrictionMessageActivity.class);
        forTarget(NavigationTarget.SENSITIVE_TRANCTION_MESSAGE_SCREEN).openActivity(SensitiveTransactionError.class);
        forTarget(NavigationTarget.IDVL_VERIFY_ME).openActivity(IDVLVerifyMeActivity.class);

        forTarget(NavigationTarget.UNBOXING_ACTIVITY).openActivity(UnboxingActivity.class);
        forTarget(NavigationTarget.TARGET_GPAY_INRODUCTION).openActivity(GpayIntroductionActivity.class);
        forTarget(NavigationTarget.STATEMENT_DECLARATION_SCREEN).openActivity(StatementDeclarationActivity.class);
        //Kids banking
        forTarget(NavigationTarget.UPDATED_TNC_KIDS_SCREEN).openActivity(UpdatedTncMinorActivity.class);
        forTarget(NavigationTarget.FRAUD_ALERT_KIDS_SCREEN).openActivity(FraudCampaignAlertActivity.class);
        forTarget(NavigationTarget.KIDS_BANKING_PARENT_REGISTRATION_ACTIVITY).openActivity(KidsBankingRegisterParentActivity.class);
        forTarget(NavigationTarget.KIDS_BANKING_CHILD_REGISTER_ACTIVITY).openActivity(ChildRegisterActivity.class);
        forTarget(NavigationTarget.PROFILE_DEREGISTER_KIDS_BANKING).openActivity(DeRegisterChildActivity.class);
        forTarget(NavigationTarget.KIDS_API_ERROR_SCREEN_ACTIVITY).openActivity(KidsApiErrorActivity.class);
        forTarget(NavigationTarget.PROFILE_DELINK_KIDS_BANKING).openActivity(DelinkChildProfileActivity.class);
        forTarget(NavigationTarget.LOC_TRANSACTION_ERROR_SCREEN).openActivity(ChildTransactionErrorMessageActivity.class);
        forTarget(NavigationTarget.KIDS_ACK_TERMS_AND_CONDITION_SCREEN).openActivity(ParentAckUpdatedTncActivity.class);
        forTarget(NavigationTarget.PARENT_ACK_FRAUD_AWARENESS_SCREEN).openActivity(AckFraudAwarenessActivity.class);

        forTarget(NavigationTarget.NTF_CANCEL_APPLICATION_ACTIVITY).openActivity(CancelApplicationActivity.class);
        forTarget(NavigationTarget.APPLICATION_STATUS_ACTIVITY).openActivity(ApplicationStatusActivity.class);
        forTarget(NavigationTarget.NTF_CANCEL_REASON_ACTIVITY).openActivity(CancelApplicationReasonActivity.class);

        forTarget(NavigationTarget.GENERIC_TECHNICAL_ERROR_SCREEN).openActivity(GenericTechnicalErrorActivity.class);

        forTarget(NavigationTarget.SALES_LANDING).openActivity(SalesLandingActivity.class);
        forTarget(EnrollV2NavigatorTarget.PERSONAL_LOAN_DETAILS_ACTIVITY).openActivity(PersonalLoanDetailsActivity.class);
        forTarget(EnrollV2NavigatorTarget.FICA_ERROR_SCREEN).openActivity(za.co.nedbank.enroll_v2.view.apply_credit_card.fica_screen.FicaErrorActivity.class);
        forTarget(NavigationTarget.TARGET_INVESTMENT_OPTION_LANDING).openActivity(InvestmentOptionLandingActivity.class);

        forTarget(NavigationTarget.SUBURB_AND_CITY_SEARCH_DATA).openActivity(SuburbAndCitySearchActivity.class);
        forTarget(NavigationTarget.OCCUPATION_LIST_DATA).openActivity(OccupationListActivity.class);

        forTarget(NavigationTarget.RRB_CREDIT_DETAILS_ACTIVITY).openActivity(RRBCreditDetailsActivity.class);
        forTarget(NavigationTarget.CREDIT_DETAILS_ACTIVITY).openActivity(CreditDetailsActivity.class);

        forTarget(NavigationTarget.APP_DEEPLINK_MANAGER).openActivity(AppDeeplinkActivity.class);
    }
}