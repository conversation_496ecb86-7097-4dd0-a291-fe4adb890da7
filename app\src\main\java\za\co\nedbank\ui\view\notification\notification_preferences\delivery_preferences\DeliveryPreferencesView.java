package za.co.nedbank.ui.view.notification.notification_preferences.delivery_preferences;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.view.fbnotifications.ClientPreferenceViewModel;

public interface DeliveryPreferencesView extends NBBaseView {
    void showProgress(boolean isVisible);

    void showErrorForUpdatePreferences(boolean isApiFailure);

    void enableSaveButton(boolean enable);

    String fetchDeliveryPeriod(int time);

    String fetchDeliveryTime(ClientPreferenceViewModel clientPreferenceViewModel, boolean isSnoozeActive);
}
