/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.edit_recipient;

import java.util.Map;

import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.ui.view.home.add_recipient.BaseRecipientView;

/**
 * Created by priyadhingra on 9/12/2017.
 */

interface EditRecipientView extends BaseRecipientView {

    void putEditRecipient();
    void onRecipientEdited(RecipientViewModel recipientViewModel);
    void showEditRecipientApiError(Map<Integer, String> matchBackNumber, String... message);
    void showEditRecipientApiError(String... message);
    void showEditRecipientApiError();
    void showApproveItApiError(String... message);
    void showDeleteRecipientApiError(String... message);
    void onRecipientDeleted();
    RecipientViewModel getRecipientViewModel();
    void handleFewRecipientEditionFailedError(String... message);
    void finishIt();
    void setMatchBackNumber(int matchBackNumber);
}
