/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import java.util.List;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.ui.domain.model.money_request.PaymentResponseModel;

/**
 * Created by sandip.lawate on 2/15/2018.
 */

interface MoneyRequestDetailsView extends NBBaseView {

    void showError(String error);

    void setAccounts(List<AccountViewModel> accountViewModelList);

    void setNextButtonEnabled(final boolean isEnabled);

    void sendMoneyRequestSuccess(PaymentResponseModel paymentResponseModel);

    void sendMoneyRequestFailure(PaymentResponseModel paymentResponseModel);

    void showLoadingOnButton(boolean shouldShowLoading);

    void showAccountsErrorView(String... error);

    void trackApiFailure(String apiName);

}
