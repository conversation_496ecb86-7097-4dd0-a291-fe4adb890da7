/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.overview.dashboard;


import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.base.adapter.BaseAdapter;
import za.co.nedbank.core.dashboard.CardAdapter;
import za.co.nedbank.core.dashboard.DashboardCard;
import za.co.nedbank.core.dashboard.DashboardCardType;
import za.co.nedbank.payment.pay.view.model.PaymentsViewModel;
import za.co.nedbank.ui.view.home.my_recipients.MyRecipientsWidget;

public class DashboardAdapter extends BaseAdapter<DashboardCard> {

    private final List<CardAdapter> cardAdapters = new ArrayList<>();
    private final List<DashboardCardType> dashboardOrder = new LinkedList<>();

    public DashboardAdapter(final Context context) {
        super(context);
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(final ViewGroup parent, final int viewType) {
        final View view = layoutInflater.inflate(R.layout.item_dashboard_card, parent, false);
        return new DashboardAdapterViewHolder(view);
    }

    @Override
    public void onBindViewHolder(final RecyclerView.ViewHolder holder, final DashboardCard model, final int position) {
        ((DashboardAdapterViewHolder) holder).bindCard(model);
    }

    @Override
    public void swapData(final List<DashboardCard> newData) {
        if (newData != null) {
            items.clear();
            items.addAll(newData);
        }
    }

    public void registerCardAdapter(final CardAdapter cardAdapter) {
        cardAdapters.add(cardAdapter);
    }

    public void setDashboardOrder(final List<DashboardCardType> dashboardOrder) {
        this.dashboardOrder.clear();
        this.dashboardOrder.addAll(dashboardOrder);

        instantiateCards();
    }

    private void instantiateCards() {
        List<DashboardCard> dashboardCards = new ArrayList<>();

        for (DashboardCardType cardType : dashboardOrder) {
            for (CardAdapter cardAdapter : cardAdapters) {
                if (cardAdapter.supports(cardType)) {
                    final DashboardCard card = cardAdapter.getCardForType(cardType);
                    if (card != null) {
                        dashboardCards.add(card);
                    }
                }
            }
        }

        DiffUtil.DiffResult result = DiffUtil.calculateDiff(new DashboardAdapterDiffCallback(items, dashboardCards));
        swapData(dashboardCards);

        result.dispatchUpdatesTo(this);
    }

    public void cleanup() {
        for (CardAdapter cardAdapter : cardAdapters) {
            cardAdapter.cleanup();
        }
    }

    public void passPaymentUpdatedBundleToRecipientCard(PaymentsViewModel paymentsViewModel) {
        for (DashboardCard card : items) {
            if (card.getCardType() == DashboardCardType.MY_RECIPIENTS) {
                View view = card.getContent();
                if (view instanceof MyRecipientsWidget) {
                    ((MyRecipientsWidget) view).receiveUpdatedPaymentBundle(paymentsViewModel);
                }
            }
        }
    }

    public void updateUserBeneficiaryData() {
        for (DashboardCard card : items) {
            if (card.getCardType() == DashboardCardType.MY_RECIPIENTS) {
                View view = card.getContent();
                if (view instanceof MyRecipientsWidget) {
                    ((MyRecipientsWidget) view).updateUserBeneficiaryData();
                }
            }
        }
    }
}
