/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.payme_tab;

import java.util.Map;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.ui.domain.model.money_request.NotificationDataModel;

/**
 * Created by sandip.lawate on 2/15/2018.
 */

public interface PayMoneyRequestView extends NBBaseView {

    void setResult(Map<String, Object> params);

    void showError(String error);

    void showNotifications(NotificationDataModel notificationDataModel);

    void setNextButtonEnabled(boolean enabled);

    void handleNextClick();

    String getRecipientName();

    String getRecipientMobileNumber();

    String getRecipientNumberWithoutZero(String recipientContactNumber);

    void showLoadingOnButton(boolean inProgress);

    void setCountryCodeVisibility(boolean shouldVisible);

    void showErrorMessage(String message);

    void navigateToRequestDetailOnSuccess();

    void handleEnterRecipientName();

    void handleEnterMobileNumber();

    void trackValidateFailure(String errorCode);

    void trackFailure(boolean isApiFailure, String tagName, String apiName, String message, String apiErrorCode);
}
