package za.co.nedbank.ui.view.notification.notification_preferences.delivery_preferences;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.ClientPreferenceData;
import za.co.nedbank.core.domain.model.notifications.DeviceClientPreferenceData;
import za.co.nedbank.core.domain.model.notifications.FBResponseData;
import za.co.nedbank.core.domain.model.notifications.FBUpdatePreferenceRequestData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.notifications.UpdatePreferenceUseCase;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.ClientPreferenceViewModel;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.PreApprovedOffersTrackingEvent;

public class DeliveryPreferencesPresenter extends NBBasePresenter<DeliveryPreferencesView> {

    public static final String TAG = DeliveryPreferencesPresenter.class.getSimpleName();

    private UpdatePreferenceUseCase mUpdatePreferenceUseCase;
    private final ApplicationStorage mApplicationStorage;
    private Analytics mAnalytics;
    private boolean mIsSnoozeActive;
    private String delTime = StringUtils.EMPTY_STRING;


    @Inject
    public DeliveryPreferencesPresenter(final UpdatePreferenceUseCase updatePreferenceUseCase,
                                        final ApplicationStorage applicationStorage,
                                        final Analytics analytics) {
        this.mUpdatePreferenceUseCase = updatePreferenceUseCase;
        this.mApplicationStorage = applicationStorage;
        this.mAnalytics = analytics;
    }

    public void updatePreferences(ClientPreferenceViewModel clientPreferenceViewModel, boolean isSnoozeActive) {
        this.mIsSnoozeActive = isSnoozeActive;
        delTime = view.fetchDeliveryTime(clientPreferenceViewModel, isSnoozeActive);
        mUpdatePreferenceUseCase.execute(getUpdatePrefReqData(clientPreferenceViewModel)).compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                    if (view != null) {
                        view.showProgress(true);
                    }
                })
                .doOnTerminate(() -> {
                    if (view != null) {
                        view.showProgress(false);
                    }
                })
                .subscribe(fbResponseData -> {
                    if (fbResponseData != null && view != null) {
                        handleUpdatePreferencesResponse(fbResponseData);
                    } else {
                        showPreferencesError(false);
                    }
                }, throwable -> showPreferencesError(false));
    }

    private void handleUpdatePreferencesResponse(FBResponseData fbResponseData) {
        int ONLY_SUCCESS_ELEMENT = 1;
        if (fbResponseData != null && fbResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = fbResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && !resultDetailList.isEmpty()) {
                    if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        navigateBack();
                        break;
                    } else {
                        handleError(resultDetailList);
                    }
                }
            }
        }
    }

    private void handleError(ArrayList<ResultDetailModel> resultDetailList) {
        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                showPreferencesError(true);
            }
        }
    }

    public void trackSnoozeFailure(boolean isApiFailure, String message) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setSubFeature(mIsSnoozeActive ? PreApprovedOffersTrackingEvent.VAL_SNOOZE_MARKETING_ON : PreApprovedOffersTrackingEvent.VAL_SNOOZE_MARKETING_OFF);
        adobeContextData.setContext3(delTime);
        mAnalytics.trackFailure(isApiFailure, PreApprovedOffersTrackingEvent.TAG_NOTIFICATION_SNOOZE_ON_FAILURE, ApiAliasConstants.NT_PREF, message, null, cdata);
    }

    public void navigateBack() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setSubFeature(mIsSnoozeActive ? PreApprovedOffersTrackingEvent.VAL_SNOOZE_MARKETING_ON : PreApprovedOffersTrackingEvent.VAL_SNOOZE_MARKETING_OFF);
        adobeContextData.setContext3(delTime);
        mAnalytics.sendEventActionWithMap(PreApprovedOffersTrackingEvent.TAG_NOTIFICATION_SNOOZE_ON_SUCCESSFUL, cdata);
        trackActionDeliveryPreferenceBack();
        view.close();
    }

    private void showPreferencesError(boolean isApiFailure) {
        if (view != null) {
            view.showErrorForUpdatePreferences(isApiFailure);
        }
    }

    private FBUpdatePreferenceRequestData getUpdatePrefReqData(ClientPreferenceViewModel clientPreferenceViewModel) {
        FBUpdatePreferenceRequestData fbUpdatePreferenceRequestData = new FBUpdatePreferenceRequestData();
        FBUpdatePreferenceRequestData.Preference preference = fbUpdatePreferenceRequestData.new Preference();
        ClientPreferenceData clientPreferenceData = new ClientPreferenceData();
        clientPreferenceData.setAllowPushNotificationForOffers(clientPreferenceViewModel.getAllowPushNotificationForOffers());
        clientPreferenceData.setLowPriorityDeliveryPeriod(clientPreferenceViewModel.getLowPriorityDeliveryPeriod());
        DeviceClientPreferenceData deviceClientPreferenceData = new DeviceClientPreferenceData();
        deviceClientPreferenceData.setAllowUnauthenticatedInbox(clientPreferenceViewModel.getAllowUnauthentictedInbox());
        deviceClientPreferenceData.setDeviceId(getDeviceId());
        clientPreferenceData.setDeviceClientPreference(deviceClientPreferenceData);

        preference.setClientPreference(clientPreferenceData);
        preference.setTiPreferences(null);

        fbUpdatePreferenceRequestData.setPreference(preference);
        return fbUpdatePreferenceRequestData;
    }


    private String getDeviceId() {
        return mApplicationStorage.getString(NotificationConstants.STORAGE_KEYS.UUID, StringUtils.EMPTY_STRING);
    }

    public void trackActionDeliveryPreferenceBack() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setSubFeatureCount();
        adobeContextData.setContext3Count();
        mAnalytics.sendEventActionWithMap(PreApprovedOffersTrackingEvent.TAG_BACK_NOTIFICATION_PREFERENCES, cdata);
    }
}
