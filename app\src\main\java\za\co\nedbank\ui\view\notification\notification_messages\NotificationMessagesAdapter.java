
package za.co.nedbank.ui.view.notification.notification_messages;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Typeface;
import android.text.Html;
import android.view.HapticFeedbackConstants;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import za.co.nedbank.R;
import za.co.nedbank.core.base.adapter.BaseLoadMoreAdapter;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.FBNotificationsViewModel;
import za.co.nedbank.databinding.ItemNotificationMessagesBinding;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;


public class NotificationMessagesAdapter extends BaseLoadMoreAdapter<FBNotificationsViewModel> implements MultipleSelectionHandler.OnSelectionChangeListener {

    private final Context context;
    private OnMessageItemClickListener mOnMessageItemClickListener;
    private MultipleSelectionHandler mMultipleSelectionHandler;
    private MultipleSelectionHandler.OnSelectionChangeListener mOnSelectionChangedListener;
    private Map<Integer, FBNotificationsViewModel> mItemsToBeDeleted;
    private boolean mHasLongPressed;

    public NotificationMessagesAdapter(final Context context) {
        super(context);
        setUseLoadingMore(true);
        this.context = context;
        mMultipleSelectionHandler = new MultipleSelectionHandler(this);

    }

    @Override
    protected void onBindItemViewHolder(RecyclerView.ViewHolder holder, FBNotificationsViewModel model, int position) {
        ((ViewHolder) holder).bindData(model);
    }

    @Override
    protected RecyclerView.ViewHolder onCreateItemViewHolder(ViewGroup parent, int viewType) {
        ItemNotificationMessagesBinding binding = ItemNotificationMessagesBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }


    void setOnMessageItemClickListener(OnMessageItemClickListener onMessageItemClickListener) {
        this.mOnMessageItemClickListener = onMessageItemClickListener;
    }

    public void addData(List<FBNotificationsViewModel> newData) {
        super.addData(newData);
        setCurrentPage(getCurrentPage() + 1);
        if (isAllSelected()) mMultipleSelectionHandler.selectAll(true, items.size());
    }

    @SuppressLint("UseSparseArrays")
    void removeItemAt(FBNotificationsViewModel viewModel) {

        int position = items.indexOf(viewModel);
        if (position >= 0) {
            if (mItemsToBeDeleted == null) {
                mItemsToBeDeleted = new TreeMap<>();
            } else {
                mItemsToBeDeleted.clear();
            }
            mItemsToBeDeleted.put(position, items.get(position));
            items.remove(position);
            notifyItemRemoved(position);
        }
    }


    @Override
    public void onSelectionStarted() {
        if (mOnSelectionChangedListener != null) {
            mOnSelectionChangedListener.onSelectionStarted();
        }
        notifyDataSetChanged();
    }


    @Override
    public void onSelectionChanged(List<Integer> selectedItems) {
        mOnSelectionChangedListener.onSelectionChanged(selectedItems);
    }

    void setOnSelectionChangedListener(MultipleSelectionHandler.OnSelectionChangeListener
                                               onSelectionChangedListener) {
        mOnSelectionChangedListener = onSelectionChangedListener;
    }

    void clearSelections() {
        mMultipleSelectionHandler.clearSelections();
        notifyDataSetChanged();
    }

    void deleteSelectedItems() {
        List<Integer> selectedPositions = mMultipleSelectionHandler.getSelectedPositions();
        if (mItemsToBeDeleted == null) {
            mItemsToBeDeleted = new TreeMap<>();
        } else {
            mItemsToBeDeleted.clear();
        }

        for (Integer pos :
                selectedPositions) {
            mItemsToBeDeleted.put(pos, items.get(pos));
        }

        for (Integer key : mItemsToBeDeleted.keySet()) {

            FBNotificationsViewModel viewModel = mItemsToBeDeleted.get(key);

            int deletePos = items.indexOf(viewModel);
            items.remove(viewModel);
            notifyItemRemoved(deletePos);
        }

    }

    List<FBNotificationsViewModel> getSelectedItems() {
        List<FBNotificationsViewModel> selectedItems = new ArrayList<>();
        List<Integer> selectedPositions = mMultipleSelectionHandler.getSelectedPositions();
        for (Integer pos :
                selectedPositions) {
            selectedItems.add(items.get(pos));
        }
        return selectedItems;
    }

    void addItemAtTop(FBNotificationsViewModel fbNotificationsViewModel) {
        boolean isPrevSelected = isAllSelected();
        items.add(0, fbNotificationsViewModel);
        mMultipleSelectionHandler.handleNewItemAtTop(isPrevSelected);
        //refresh all positions
        notifyDataSetChanged();
    }

    boolean isAllSelected() {
        int count = items.size();
        if (count > 0) {
            return count == mMultipleSelectionHandler.getSelectedPositions().size();
        }
        return false;
    }

    void selectAll(boolean selectAll) {
        mMultipleSelectionHandler.selectAll(selectAll, items.size());
        notifyDataSetChanged();
    }

    public void restoreDeletedItems() {
        if (mItemsToBeDeleted != null) {
            for (Integer originalPositions :
                    mItemsToBeDeleted.keySet()) {
                FBNotificationsViewModel item = mItemsToBeDeleted.get(originalPositions);
                if (items.size() <= originalPositions) {
                    items.add(item);
                } else {
                    items.add(originalPositions, item);
                }
                notifyItemInserted(originalPositions);
            }
        }

    }

    List<FBNotificationsViewModel> recentDeletedItems() {
        return mItemsToBeDeleted != null ? new ArrayList<>(mItemsToBeDeleted.values()) : null;
    }

    public FBNotificationsViewModel getItemAt(int position) {
        return items.get(position);
    }

    class ViewHolder extends RecyclerView.ViewHolder implements MultipleSelectionHandler.SelectableViewHolder {

        private ItemNotificationMessagesBinding binding;

        public ViewHolder(ItemNotificationMessagesBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }

        void bindData(FBNotificationsViewModel fbNotificationsViewModel) {
            binding.tvNotificationTitle.setText(Html.fromHtml(fbNotificationsViewModel.getHeading()));
            binding.tvHeading.setText(Html.fromHtml(fbNotificationsViewModel.getSubHeading()));
            if (!fbNotificationsViewModel.isRead()) {
                binding.tvNotificationTitle.setTypeface(Typeface.DEFAULT_BOLD);
            } else {
                binding.tvNotificationTitle.setTypeface(Typeface.DEFAULT);
            }
            if (!StringUtils.isNullOrEmpty(fbNotificationsViewModel.getActiveExpiryDate())) {
                binding.tvExpireTime.setText(context.getString(R.string.expires) + StringUtils.SPACE + FormattingUtil.getFormattedDateSAtoLocal(fbNotificationsViewModel.getActiveExpiryDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS, FormattingUtil.DATE_FORMAT_DD_MMM_YYYY_SPACE_SEPARATOR));
                boolean isGoingToExpireSoon = isGoingToExpireSoon(fbNotificationsViewModel.getActiveExpiryDate());
                int textColor = isGoingToExpireSoon ? R.color.notification_expire_red : R.color.color_bbbbbb;
                binding.tvExpireTime.setTextColor(ContextCompat.getColor(context, textColor));
                if (isGoingToExpireSoon) {
                    int unreadDrawableResource = R.drawable.unread_expire_soon_notification_indicator;
                    binding.ivUnreadIndicator.setImageResource(unreadDrawableResource);
                    binding.ivUnreadIndicator.setVisibility(View.VISIBLE);
                } else if (!fbNotificationsViewModel.isRead()) {
                    int unreadDrawableResource = R.drawable.unread_notification_indicator;
                    binding.ivUnreadIndicator.setImageResource(unreadDrawableResource);
                    binding.ivUnreadIndicator.setVisibility(View.VISIBLE);
                } else {
                    binding.ivUnreadIndicator.setVisibility(View.GONE);
                }
                binding.tvExpireTime.setVisibility(View.VISIBLE);
            }else {
                binding.tvExpireTime.setVisibility(View.INVISIBLE);
            }

            int visibility = mMultipleSelectionHandler.isSelectable() ? View.VISIBLE : View.GONE;
            binding.ivCheckbox.setVisibility(visibility);

            mMultipleSelectionHandler.bind(this);
            binding.messageContainer.setOnClickListener(v -> onClick());
            binding.messageContainer.setOnLongClickListener(this::onLongClick);
            binding.messageContainer.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    ViewHolder.this.onTouch(event);
                    return false;
                }
            });
        }

        public void onClick() {
            if (!mMultipleSelectionHandler.tapSelection(this)) {
                if (mOnMessageItemClickListener != null) {
                    int clickedPosition = getAdapterPosition();
                    FBNotificationsViewModel fbNotificationsViewModel = items.get(clickedPosition);
                    mOnMessageItemClickListener.onMessageItemClick(fbNotificationsViewModel, clickedPosition);
                }
            }
        }

        public boolean onLongClick(View v) {
            mHasLongPressed = true;
            if (!mMultipleSelectionHandler.isSelectable()) {
                //vibrate before selection started
                v.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS);
            }
            return true;
        }

        public boolean onTouch(MotionEvent event) {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                if (mHasLongPressed) {
                    mMultipleSelectionHandler.handleSelection(this);
                    mHasLongPressed = false;
                    return true;
                }
            }
            return false;
        }

        @Override
        public void setSelected(boolean isSelected) {
            int imageResource = isSelected ? R.drawable.checkbox_selected : R.drawable.checkbox;
            binding.ivCheckbox.setImageResource(imageResource);

        }
    }

    private boolean isGoingToExpireSoon(String expiryDate) {
        long secondsRemainingFromNow = NotificationUtils.secondsRemainingFromNow(expiryDate, FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
        return secondsRemainingFromNow <= TimeUnit.HOURS.toSeconds(NotificationConstants.SETTINGS.EXPIRY_ALERT_TIME_IN_HOURS);
    }

    public interface OnMessageItemClickListener {
        void onMessageItemClick(FBNotificationsViewModel fbNotificationsViewModel, int pos);
    }
}

