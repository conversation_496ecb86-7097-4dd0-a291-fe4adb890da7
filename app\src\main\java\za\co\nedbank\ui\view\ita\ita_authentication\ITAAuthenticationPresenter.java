package za.co.nedbank.ui.view.ita.ita_authentication;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;

public class ITAAuthenticationPresenter extends NBBasePresenter<ITAAuthenticationView> {

    @Inject
    NavigationRouter mNavigationRouter;

    @Inject
    public ITAAuthenticationPresenter(final NavigationRouter navigationRouter) {
        this.mNavigationRouter = navigationRouter;
    }

}
