/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import java.util.List;
import java.util.Map;

import za.co.nedbank.payment.common.model.user_contact.UserContactViewModel;
import za.co.nedbank.payment.common.view.IRecyclerviewFragmentView;


interface UserContactsView extends IRecyclerviewFragmentView {

    void showError(String message);

    void showContacts(Map<String, List<UserContactViewModel>> userContactDatas);

    void showPermissionNotProvided();

    void showPermissionDeniedView();

    void showPermissionProvidedView();

    void handleSearchInput(String searchText);

    void openAppSettings();

    void setActivityResult(UserContactViewModel selectedContact);

}
