/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients.choose_from_account;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.databinding.FragmentDialogChooseRecipientCellViewBinding;
import za.co.nedbank.ui.view.home.my_recipients.choose_recipient.IChooseRecipientListItemListener;

/**
 * Created by priya<PERSON>ngra on 10-04-2018.
 */

public class ChooseFromAccountRecyclerAdapter extends RecyclerView.Adapter<ChooseFromAccountRecyclerAdapter.ChooseFromAccountItemViewHolder> {

    private final List<AccountViewModel> payAccountsViewModelList;
    private final IChooseRecipientListItemListener mIChooseRecipientListItemListener;
    private int lastSelectedPos;
    private Context context;

    public ChooseFromAccountRecyclerAdapter(List<AccountViewModel> payAccountsViewModelList, IChooseRecipientListItemListener iChooseRecipientListItemListener,int lastSelectedPos) {
        this.payAccountsViewModelList = payAccountsViewModelList;
        this.mIChooseRecipientListItemListener = iChooseRecipientListItemListener;
        this.lastSelectedPos = lastSelectedPos;
    }

    @Override
    public ChooseFromAccountItemViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        FragmentDialogChooseRecipientCellViewBinding binding = FragmentDialogChooseRecipientCellViewBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ChooseFromAccountItemViewHolder(binding, mIChooseRecipientListItemListener);
    }

    @Override
    public void onBindViewHolder(ChooseFromAccountItemViewHolder holder, int position) {
        String accountType = payAccountsViewModelList.get(position).getAccountType();
        if (accountType.equalsIgnoreCase(Constants.ACCOUNT_TYPES.SA.getAccountTypeCode()) || accountType.equalsIgnoreCase(Constants.ACCOUNT_TYPES.CA.getAccountTypeCode())) {
            holder.getRecipientTypeImageView().setImageResource(R.drawable.ic_wallet);
        } else {
            holder.getRecipientTypeImageView().setImageResource(R.drawable.ic_credit_card);
        }
        holder.getRecipientMobileOrBankTextView().setText(payAccountsViewModelList.get(position).getDisplayAccountName());
        if (lastSelectedPos == position) {
            ViewUtils.showViews(holder.getSelectorImageView());
        } else {
            ViewUtils.setInvisibleAction(holder.getSelectorImageView());
        }
        // accessibility
        holder.mRootLayout.setContentDescription(String.format(context.getString(R.string.quick_pay_select_account_value_acc), payAccountsViewModelList.get(position).getDisplayAccountName()));
        holder.mRootLayout.setOnClickListener(v -> holder.onRootLayoutSelected());
    }

    @Override
    public int getItemCount() {
        return payAccountsViewModelList != null ?
                 payAccountsViewModelList.size() : 0;
    }

    class ChooseFromAccountItemViewHolder extends RecyclerView.ViewHolder {

        ImageView mAccountTypeImageView;
        TextView mAccountNameTextView;
        LinearLayout mRootLayout;
        ImageView mSelectorImageView;

        private final IChooseRecipientListItemListener mIChooseRecipientListItemListener;

        ChooseFromAccountItemViewHolder(FragmentDialogChooseRecipientCellViewBinding binding, IChooseRecipientListItemListener iChooseRecipientListItemListener) {
            super(binding.getRoot());
            this.mIChooseRecipientListItemListener = iChooseRecipientListItemListener;
            this.mAccountTypeImageView = binding.chooseRecipientListRecipientTypeIv;
            this.mAccountNameTextView = binding.chooseRecipientListRecipientAccountOrMobileTv;
            this.mRootLayout = binding.chooseRecipientCellRootLl;
            this.mSelectorImageView = binding.chooseRecipientSelectorIv;
        }

        ImageView getRecipientTypeImageView() {
            return mAccountTypeImageView;
        }

        TextView getRecipientMobileOrBankTextView() {
            return mAccountNameTextView;
        }

        ImageView getSelectorImageView() {
            return mSelectorImageView;
        }

        void onRootLayoutSelected() {
            ViewUtils.showViews(this.getSelectorImageView());
            if (mIChooseRecipientListItemListener != null) {
                mIChooseRecipientListItemListener.selectedListItem(getAdapterPosition());
            }
        }
    }
}
