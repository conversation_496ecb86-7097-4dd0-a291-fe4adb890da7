package za.co.nedbank.ui.view.deeplink.view

import android.content.Intent
import android.os.Bundle
import android.view.View
import za.co.nedbank.R
import za.co.nedbank.core.base.NBBaseActivity
import za.co.nedbank.core.data.networking.APIInformation
import za.co.nedbank.core.databinding.ActivityGenericDeeplinkBinding
import za.co.nedbank.core.notification.NotificationConstants
import za.co.nedbank.core.utils.BundleUtils
import za.co.nedbank.core.utils.IntentUtils
import za.co.nedbank.core.utils.StringUtils
import za.co.nedbank.ui.di.AppDI
import javax.inject.Inject

/**
 * Activity responsible for handling app-specific deeplink navigation.
 * Receives deeplink data from the intent, processes navigation, and manages UI state.
 *
 * @constructor Injects the presenter and initializes the activity.
 */
class AppDeeplinkActivity : NBBaseActivity(), AppDeeplinkView {

    private var mAppBinding: ActivityGenericDeeplinkBinding? = null
    var presenter: AppDeeplinkPresenter? = null
        @Inject set

    private var appModuleTarget: String? = null
    private var appModuleParamHashmap: HashMap<String, String>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        AppDI.getActivityComponent(this).inject(this)
        super.onCreate(savedInstanceState)
        receiveDataFromBundleForAppModuleDeepLinks()
        mAppBinding = ActivityGenericDeeplinkBinding.inflate(layoutInflater)
        setContentView(mAppBinding!!.root)
        presenter!!.bind(this)
        handleAppModuleNavigation()
    }

    @Suppress("UNCHECKED_CAST")
    private fun receiveDataFromBundleForAppModuleDeepLinks() {
        if (intent != null && intent.extras != null) {
            if (intent.extras!!.containsKey(NotificationConstants.Navigation.PARAM)) {
                (BundleUtils.getSerializable(
                    intent.extras,
                    NotificationConstants.Navigation.PARAM,
                    HashMap::class.java
                )
                        as HashMap<String, String>).also { appModuleParamHashmap = it }
            }
            if (intent.extras!!.containsKey(NotificationConstants.Navigation.TARGET)) {
                appModuleTarget = intent.extras!!.getString(
                    NotificationConstants.Navigation.TARGET,
                    StringUtils.EMPTY_STRING
                )
            }
        }
    }

    private fun handleAppModuleNavigation() {
        mAppBinding!!.progressBar.visibility = View.VISIBLE

        if (!StringUtils.isNullOrEmpty(appModuleTarget)) {
            if (!APIInformation.getInstance().isLoggedOut) {
                presenter!!.handleOnClick(appModuleTarget!!)
                presenter!!.clearNavigationData()
            } else {
                openLaunchScreen()
            }
        } else close()
    }

    private fun openLaunchScreen() {
        val mIntents: MutableList<Intent> = ArrayList()
        val appLaunchIntent: Intent? = packageManager.getLaunchIntentForPackage(packageName)
        appLaunchIntent?.let { mIntents.add(it) }
        memoryApplicationStorage.putString(
            NotificationConstants.STORAGE_KEYS.AJO_NOTIFICATION_LINK,
            appModuleTarget
        )
        startActivities(mIntents.toTypedArray())
        close()
    }

    override fun getAppParamHashmap(): HashMap<String, String>? {
        return appModuleParamHashmap
    }

    override fun showProgress(isShow: Boolean) {
        mAppBinding!!.progressBar.visibility = if (isShow) View.VISIBLE else View.GONE
    }

    override fun startBrowser(url: String?) {
        IntentUtils.openDefaultBrowser(this, url)
        close()
    }

    override fun getActivityLabel(): String? {
        return getString(R.string.data_usage_name_avo)
    }

    override fun onDestroy() {
        presenter!!.unbind()
        super.onDestroy()
    }
}