/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.mapper.money_requests;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.data.mapper.MetaDataEntityToDataMapper;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsMainResponseEntity;
import za.co.nedbank.ui.data.entity.money_request.MoneyRequestsResponseEntity;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsDataModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsMainDataModel;

/**
 * Created by swapnil.gawande on 2/23/2018.
 */

public class MoneyRequestsResponseEntityToDataMapper {
    private final MetaDataEntityToDataMapper mMetaDataEntityToDataMapper;

    @Inject
    MoneyRequestsResponseEntityToDataMapper(MetaDataEntityToDataMapper metaDataEntityToDataMapper) {
        this.mMetaDataEntityToDataMapper = metaDataEntityToDataMapper;
    }

    public MoneyRequestsMainDataModel mapMoneyRequestsMainResponseEntityToDataModel(MoneyRequestsMainResponseEntity moneyRequestsMainResponseEntity) {
        MoneyRequestsMainDataModel moneyRequestsMainDataModel = new MoneyRequestsMainDataModel();
        if (moneyRequestsMainResponseEntity != null) {
            moneyRequestsMainDataModel.setMoneyRequestsDataModels(mapMoneyRequestsResponseEntityToDataModel(moneyRequestsMainResponseEntity.getMoneyRequestsResponseEntities()));
            moneyRequestsMainDataModel.setMetaDataModel(mMetaDataEntityToDataMapper.mapMetaData(moneyRequestsMainResponseEntity.getMetaDataEntity()));
        }
        return moneyRequestsMainDataModel;
    }

    private ArrayList<MoneyRequestsDataModel> mapMoneyRequestsResponseEntityToDataModel(List<MoneyRequestsResponseEntity> moneyRequestsResponseEntities) {
        ArrayList<MoneyRequestsDataModel> moneyRequestsDataModels = new ArrayList<>();
        if (moneyRequestsResponseEntities != null) {
            for (MoneyRequestsResponseEntity moneyRequestsResponseEntity : moneyRequestsResponseEntities) {
                MoneyRequestsDataModel moneyRequestsDataModel = new MoneyRequestsDataModel();
                moneyRequestsDataModel.setReminder(moneyRequestsResponseEntity.isReminder());
                moneyRequestsDataModel.setPayLater(moneyRequestsResponseEntity.isPayLater());
                moneyRequestsDataModel.setPaymentRequestId(moneyRequestsResponseEntity.getPaymentRequestId());
                moneyRequestsDataModel.setPaid(moneyRequestsResponseEntity.isPaid());
                moneyRequestsDataModel.setReject(moneyRequestsResponseEntity.isReject());
                moneyRequestsDataModel.setRequestDate(moneyRequestsResponseEntity.getRequestDate());
                moneyRequestsDataModel.setPartyName(moneyRequestsResponseEntity.getPartyName());
                moneyRequestsDataModel.setRequestAmount(moneyRequestsResponseEntity.getRequestAmount());
                moneyRequestsDataModel.setPartyPhoneNumber(moneyRequestsResponseEntity.getPartyPhoneNumber());
                moneyRequestsDataModel.setPartyDescription(moneyRequestsResponseEntity.getPartyDescription());
                moneyRequestsDataModel.setRequestStatus(moneyRequestsResponseEntity.getRequestStatus());
                moneyRequestsDataModel.setPartyAccountType(moneyRequestsResponseEntity.getPartyAccountType());
                moneyRequestsDataModel.setPartyAccountNumber(moneyRequestsResponseEntity.getPartyAccountNumber());
                moneyRequestsDataModel.setExpiryDate(moneyRequestsResponseEntity.getExpiryDate());
                moneyRequestsDataModel.setCurrentDate(moneyRequestsResponseEntity.getCurrentDate());
                moneyRequestsDataModel.setProcessDate(moneyRequestsResponseEntity.getProcessDate());
                moneyRequestsDataModels.add(moneyRequestsDataModel);
            }
        }
        return moneyRequestsDataModels;
    }
}
