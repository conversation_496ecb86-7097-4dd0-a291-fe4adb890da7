package za.co.nedbank.ui.domain.mapper;

import javax.inject.Inject;

import za.co.nedbank.core.data.accounts.model.AvoWalletDetailsEntity;
import za.co.nedbank.ui.domain.model.AvoWalletDetailsModel;

public class AvoWalletDetailsEntityToDomainDataMapper {

    @Inject
    AvoWalletDetailsEntityToDomainDataMapper() {

    }

    public AvoWalletDetailsModel mapAvoWalletEntityToDomainModel(AvoWalletDetailsEntity walletDetailsEntity){
        AvoWalletDetailsModel avoWalletDetailsModel = new AvoWalletDetailsModel();
        if(walletDetailsEntity != null){
            avoWalletDetailsModel.setAvoWalletAccount(walletDetailsEntity.isAvoWalletAccount());
            avoWalletDetailsModel.setAvoId(walletDetailsEntity.getAvoId());
            avoWalletDetailsModel.setAvoLoginUrl(walletDetailsEntity.getAvoLoginUrl());
            avoWalletDetailsModel.setAvoRegisterUrl(walletDetailsEntity.getAvoRegisterUrl());
        }
        return avoWalletDetailsModel;
    }
}
