/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.recipient_detail;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import java.util.Map;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.payment.recent.Constants;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.sharedui.ui.SimpleDividerItemDecoration;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NBFlexibleItemCountRecyclerviewAdapter;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.mapper.RecipientViewModelToEntityMapper;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.core.view.recipient.UserBeneficiaryCollectiveDataViewModel;
import za.co.nedbank.databinding.ActivityRecipientDetailWithHistoryBinding;
import za.co.nedbank.payment.common.domain.data.mapper.beneficiary.user.UserBeneficiaryMapper;
import za.co.nedbank.payment.common.view.IPayChildScreenTypes;
import za.co.nedbank.payment.common.view.beneficiary.user.model.SelectedUserBeneficiaryViewModel;
import za.co.nedbank.payment.pay.navigator.PayNavigatorTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.add_recipient.BankAccountAdapter;
import za.co.nedbank.ui.view.home.add_recipient.BaseRecipientActivity;
import za.co.nedbank.ui.view.home.add_recipient.CreditCardAdapter;
import za.co.nedbank.ui.view.home.add_recipient.ElectricityMeterAdapter;
import za.co.nedbank.ui.view.home.add_recipient.EmailAdapter;
import za.co.nedbank.ui.view.home.add_recipient.MobileNumberAdapter;
import za.co.nedbank.ui.view.home.add_recipient.ShapIDAdapter;
import za.co.nedbank.ui.view.pop.RecipientDetailsFragment;
import za.co.nedbank.ui.view.pop.RecipientDetailsPresenter;
import za.co.nedbank.ui.view.pop.TransactionHistoryFragment;
import za.co.nedbank.uisdk.component.CompatTextView;
import za.co.nedbank.uisdk.widget.NBSnackbar;

/**
 * Created by priyadhingra on 9/12/2017.
 */

public class RecipientDetailWithHistoryActivity extends BaseRecipientActivity implements RecipientDetailView {

    @Inject
    RecipientDetailPresenter mRecipientDetailPresenter;
    @Inject
    RecipientViewModelToEntityMapper mEditRecipientViewModelToEntityMapper;
    @Inject
    UserBeneficiaryMapper mUserBeneficiaryMapper;
    private boolean mIsItemEdited;
    private boolean mSelectRecipientAccount = false;
    private UserBeneficiaryCollectiveDataViewModel mUserBeneficiaryCollectiveDataViewModel;
    private ISectionedListItemSelectedListener itemSelectedListener = new ISectionedListItemSelectedListener() {
        @Override
        public void itemSelected(int sectionPosition, int itemPositionInSection) {
            setupResult(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE.values()[sectionPosition], itemPositionInSection);
        }
    };
    private RecipientDetailsFragment mRecipientDetailsFragment;
    private ActivityRecipientDetailWithHistoryBinding binding;

    @SuppressLint("MissingSuperCall")
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        binding = ActivityRecipientDetailWithHistoryBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        super.onCreate(savedInstanceState, mRecipientDetailPresenter);
        initToolbar(binding.toolbar, true, false);

        mRecipientDetailPresenter.bind(this);
        parseDataFromIntent();
        if (mIsItemEdited) {
            mRecipientDetailPresenter.callGetSingleRecipientUseCase(String.valueOf(mRecipientViewModel.getContactCardId()));
        }
        setUpViewAsPerIntentData();
        hideEditButtonForKidsProfile();
        binding.tvEdit.setOnClickListener(v -> handleEditClick());
    }

    @Override
    public String getDefaultBankNameText() {
        return StringUtils.EMPTY_STRING;
    }

    void handleEditClick() {
        if(showFeatureNotAllowed()) return;
        mRecipientDetailPresenter.handleEditClick(mRecipientViewModel);
    }

    private void setupViewPager(ViewPager viewPager) {
        DetailsPagerAdapter dashboardPagerAdapter = new DetailsPagerAdapter((this).getSupportFragmentManager());
        mRecipientDetailsFragment = new RecipientDetailsFragment();
        Bundle bundle = new Bundle();
        bundle.putParcelable(Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL, mRecipientViewModel);
        bundle.putParcelable(Constants.EXTRAS.USER_BENEFICIARY_VIEW_MODEL, mUserBeneficiaryCollectiveDataViewModel);
        mRecipientDetailsFragment.setArguments(bundle);
        dashboardPagerAdapter.addFragment(mRecipientDetailsFragment, getString(R.string.details));
        mRecipientDetailPresenter.trackOnPageLoad(0);
        Bundle bundle1 = new Bundle();
        bundle1.putParcelable(Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL, mRecipientViewModel);
        Fragment transactionHistoryFragment = new TransactionHistoryFragment();
        transactionHistoryFragment.setArguments(bundle1);
        dashboardPagerAdapter.addFragment(transactionHistoryFragment, getString(R.string.history));
        viewPager.setAdapter(dashboardPagerAdapter);
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                //do nothing
            }

            @Override
            public void onPageSelected(int position) {
                mRecipientDetailPresenter.trackOnPageLoad(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                //do nothing
            }
        });
    }

    private void makeTabsCustomView() {
        // Iterate over all tabs and set the custom view
        CompatTextView quickPayTextView = (CompatTextView) LayoutInflater.from(this).inflate(R.layout.custom_dasboard_tab, null);
        quickPayTextView.setText(getString(R.string.details));

        CompatTextView nbPayMeTextView = (CompatTextView) LayoutInflater.from(this).inflate(R.layout.custom_dasboard_tab, null);
        nbPayMeTextView.setText(getString(R.string.history));
        binding.detailsTabs.getTabAt(za.co.nedbank.services.Constants.ZERO).setCustomView(quickPayTextView);
        binding.detailsTabs.getTabAt(za.co.nedbank.services.Constants.ONE).setCustomView(nbPayMeTextView);
        if (binding.detailsTabs.getTabAt(0) != null) {
            binding.detailsTabs.getTabAt(0).select();
        }
    }

    private void setUpViewAsPerIntentData() {
        if (mRecipientViewModel != null && !TextUtils.isEmpty(mRecipientViewModel.getRecipientName())) {
            binding.tvNameInitial.setText(StringUtils.getNameInitials(mRecipientViewModel.getRecipientName()));
            binding.tvLabelRecipientName.setText(mRecipientViewModel.getRecipientName());
        }

        setupRecyclerViews();
        setLayoutParamOfRecyclerView(false);
        binding.viewpager.setSwipeEnabled(false);
        setupViewPager(binding.viewpager);
        binding.detailsTabs.setupWithViewPager(binding.viewpager);
        makeTabsCustomView();
    }

    @Override
    protected void onResume() {
        super.onResume();
        mRecipientDetailPresenter.bind(this);
    }

    private void parseDataFromIntent() {
        if (getIntent() != null) {
            mUserBeneficiaryCollectiveDataViewModel = getIntent().getParcelableExtra(Constants.EXTRAS.EDIT_RECIPIENT_VIEW_MODEL);
            mSelectRecipientAccount = getIntent().getBooleanExtra(Constants.EXTRAS.SELECT_RECIPIENT_ACCOUNT, false);
            if (mUserBeneficiaryCollectiveDataViewModel != null) {
                mRecipientViewModel = mEditRecipientViewModelToEntityMapper.mapToRecipientViewModel(mUserBeneficiaryCollectiveDataViewModel);
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        mRecipientDetailPresenter.unbind();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if(intent!=null && (intent.hasExtra(RecipientDetailsPresenter.IS_FROM_FICA) && mRecipientDetailsFragment!=null)){
                mRecipientDetailsFragment.handleFicaResponse(intent.getExtras());

        }
    }

    @Override
    protected void setUpRecyclerViewAdapter() {
        if (mRecipientViewModel != null) {
            ViewUtils.showViews(rvAccount);
            buildBankAccountAdapter();

            buildMobNumberAdapter();

            buildElectricityMeterAdapter();

            buildEmailAdapter();

            buildCreditCardAdapter();

            buildShapIdAdapter();
        }
    }

    private void buildShapIdAdapter() {
        if (mShapIdNbFlexibleItemCountRecyclerviewModel != null && CollectionUtils.isNotEmpty(mRecipientViewModel.getShapIdViewDataModelList())) {
            ViewUtils.showViews(rvShapeID);
            mShapIDViewDataModelList = mRecipientViewModel.getShapIdViewDataModelList();
            mShapIDAdapter = new ShapIDAdapter(this, mShapIdNbFlexibleItemCountRecyclerviewModel,
                    mShapIDViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
            mShapIDAdapter.setEditable(false);
            rvShapeID.addItemDecoration(new SimpleDividerItemDecoration(this));
            rvShapeID.setAdapter(mShapIDAdapter);
        } else {
            ViewUtils.hideViews(rvShapeID);
        }
    }

    private void buildCreditCardAdapter() {
        if (mCreditCardNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getCreditCardViewDataModelList() != null && !mRecipientViewModel.getCreditCardViewDataModelList().isEmpty()) {
            ViewUtils.showViews(rvCreditCard);
            mCreditCardViewDataModelList = mRecipientViewModel.getCreditCardViewDataModelList();
            mCreditCardAdapter = new CreditCardAdapter(this, mCreditCardNbFlexibleItemCountRecyclerviewModel,
                    mCreditCardViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
            mCreditCardAdapter.setEditable(false);
            rvCreditCard.addItemDecoration(new SimpleDividerItemDecoration(this));
            rvCreditCard.setAdapter(mCreditCardAdapter);
        } else {
            ViewUtils.hideViews(rvCreditCard);
        }
    }

    private void buildEmailAdapter() {
        if (mEmailNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getEmailViewDataModelList() != null && !mRecipientViewModel.getEmailViewDataModelList().isEmpty()) {
            ViewUtils.showViews(rvEmail);
            mEmailViewDataModelList = mRecipientViewModel.getEmailViewDataModelList();
            mEmailAdapter = new EmailAdapter(this, mEmailNbFlexibleItemCountRecyclerviewModel, mEmailViewDataModelList, null, null);
            mEmailAdapter.setEditable(false);
            rvEmail.setAdapter(mEmailAdapter);
        } else {
            ViewUtils.hideViews(rvEmail);
        }
    }

    private void buildElectricityMeterAdapter() {
        if (mElectricityNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getElectricityMeterViewDataModelList() != null && !mRecipientViewModel.getElectricityMeterViewDataModelList().isEmpty()) {
            ViewUtils.showViews(rvElectricity);
            mElectricityMeterViewDataModelList = mRecipientViewModel.getElectricityMeterViewDataModelList();
            mElectricityMeterAdapter = new ElectricityMeterAdapter(this, mElectricityNbFlexibleItemCountRecyclerviewModel,
                    mElectricityMeterViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
            mElectricityMeterAdapter.setINBRecyclerViewListener(this);
            mElectricityMeterAdapter.setEditable(false);
            rvElectricity.addItemDecoration(new SimpleDividerItemDecoration(this));
            rvElectricity.setAdapter(mElectricityMeterAdapter);
        } else {
            ViewUtils.hideViews(rvElectricity);
        }
    }

    private void buildMobNumberAdapter() {
        if (mMobileNumberNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getMobileNumberViewDataModelList() != null && !mRecipientViewModel.getMobileNumberViewDataModelList().isEmpty()) {
            ViewUtils.showViews(rvMobileNumber);
            mMobileNumberViewDataModelList = mRecipientViewModel.getMobileNumberViewDataModelList();
            mMobileNumberAdapter = new MobileNumberAdapter(this, mMobileNumberNbFlexibleItemCountRecyclerviewModel,
                    mMobileNumberViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
            mMobileNumberAdapter.setINBRecyclerViewListener(this);
            mMobileNumberAdapter.setEditable(false);
            rvMobileNumber.addItemDecoration(new SimpleDividerItemDecoration(this));
            rvMobileNumber.setAdapter(mMobileNumberAdapter);
        } else {
            ViewUtils.hideViews(rvMobileNumber);
        }
    }

    private void buildBankAccountAdapter() {
        if (mBankAccountNbFlexibleItemCountRecyclerviewModel != null && mRecipientViewModel.getBankAccountViewDataModelList() != null && !mRecipientViewModel.getBankAccountViewDataModelList().isEmpty()) {
            mBankAccountViewDataModelList = mRecipientViewModel.getBankAccountViewDataModelList();
            mBankAccountAdapter = new BankAccountAdapter(this, mBankAccountNbFlexibleItemCountRecyclerviewModel, mBankAccountViewDataModelList, null, mSelectRecipientAccount ? itemSelectedListener : null);
            mBankAccountAdapter.setINBRecyclerViewListener(this);
            mBankAccountAdapter.setIActivityAdapterComListener(this);
            mBankAccountAdapter.setEditable(false);
            rvAccount.addItemDecoration(new SimpleDividerItemDecoration(this));
            rvAccount.setAdapter(mBankAccountAdapter);
        } else {
            ViewUtils.hideViews(rvAccount);
        }
    }

    private void setupResult(NBFlexibleItemCountRecyclerviewAdapter.VIEW_TYPE view_type, int itemPositionInSection) {
        Bundle bundle = new Bundle();
        bundle.putInt(Constants.EXTRAS.SCREEN_TYPE, IPayChildScreenTypes.BENEFICIARY_SCREEN);
        SelectedUserBeneficiaryViewModel selectedUserBeneficiaryViewModel = null;
        switch (view_type) {
            case BANK:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getBankBeneficiaryList().get(itemPositionInSection));
                break;
            case CREDIT_CARD:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getCreditCardBeneficiaryList().get(itemPositionInSection));
                break;
            case MOBILE_NUMBER:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getMobileBeneficiaryList().get(itemPositionInSection));
                break;
            case ELECTRICITY_METER:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getElectricityBeneficiaryList().get(itemPositionInSection));
                break;
            case SHAPID:
                selectedUserBeneficiaryViewModel = mUserBeneficiaryMapper.mapUserBeneficiaryDetailsViewModelToSelectedUserBeneficiaryViewModel(mUserBeneficiaryCollectiveDataViewModel.getRppBeneficiaryList().get(itemPositionInSection));
                break;
            case EMAIL:
                break;
        }
        bundle.putParcelable(PayNavigatorTarget.EXTRAS.BENEFICIARY_SELECTED, selectedUserBeneficiaryViewModel);
        Intent intent = new Intent();
        intent.putExtras(bundle);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    @Override
    public void setResult(Map<String, Object> resultMap) {
        if (resultMap != null && resultMap.size() > 0) {
            Object isItemDeleted = resultMap.get(Constants.EXTRAS.IS_ITEM_DELETED);
            Object errorModel = resultMap.get(Constants.EXTRAS.ERROR_MESSAGE);
            if (errorModel instanceof String[]) {
                String[] errorStrings = (String[]) errorModel;
                if (errorStrings.length > 0) {
                    NBSnackbar.instance().action(getString(R.string.snackbar_dismiss), () -> {
                    }).build(lnrTopView, getString(R.string.few_recipient_failed_error));
                }
            }
            if (isItemDeleted instanceof Boolean) {
                sendBeneficiaryListRefreshIntent();
            } else {
                refreshUpdateInfo(resultMap);
            }
        }
    }

    private void refreshUpdateInfo(Map<String, Object> resultMap) {
        Object isItemEdited = resultMap.get(Constants.EXTRAS.IS_ITEM_EDITED);
        RecipientViewModel recipientViewModel=(RecipientViewModel) resultMap.get(Constants.EXTRAS.EDIT_RECIPIENT);
        mRecipientDetailsFragment.setEditedEmailAddress(recipientViewModel);
        mRecipientDetailsFragment.setEditedPhoneNumber(recipientViewModel);

        if (isItemEdited instanceof Boolean) {
            mIsItemEdited = (boolean) isItemEdited;
            if(mIsItemEdited){
                if(recipientViewModel!=null){
                    mRecipientViewModel = recipientViewModel;
                    mUserBeneficiaryCollectiveDataViewModel =  mEditRecipientViewModelToEntityMapper.mapToBeneficiaryCollectiveDataViewModel(mRecipientViewModel);
                }
                setUpViewAsPerIntentData();
            }
        }
    }

    @Override
    public void supportFinishAfterTransition() {
        mRecipientDetailPresenter.handleBackPressed();
    }

    @Override
    public void dataValidationSuccess() {
        // data validation not required
    }

    @Override
    public void onRecipientDetailFetched(RecipientViewModel recipientViewModel) {
        ViewUtils.hideViews(binding.pbRecyclerViewLoading);
        setEnabledActivityTouch(true);
        if (recipientViewModel != null) {
            mRecipientViewModel = recipientViewModel;
            mUserBeneficiaryCollectiveDataViewModel =  mEditRecipientViewModelToEntityMapper.mapToBeneficiaryCollectiveDataViewModel(mRecipientViewModel);
            setUpViewAsPerIntentData();
        }
    }

    @Override
    public void showGetSingleRecipientApiError(String... message) {
        setEnabledActivityTouch(true);
        ViewUtils.hideViews(binding.pbRecyclerViewLoading);
        NBSnackbar.instance().action(getString(R.string.snackbar_action_retry), () -> mRecipientDetailPresenter.callGetSingleRecipientUseCase(String.valueOf(mRecipientViewModel.getContactCardId()))).build(lnrTopView, message);
    }

    @Override
    public void showProgressBar() {
        ViewUtils.showViews(binding.pbRecyclerViewLoading);
    }

    @Override
    public void finishOnBack() {
        if (mIsItemEdited) {
            sendBeneficiaryListRefreshIntent();
        } else {
            finish();
        }
    }

    @Override
    public void onBackPressed() {
        mRecipientDetailPresenter.handleBackPressed();
    }

    //kids profile don't have access to Edit recipient..
    private void hideEditButtonForKidsProfile() {
        boolean isKidsProfile = mRecipientDetailPresenter.isKidsProfile();
        if (isKidsProfile){
            ViewUtils.hideViews(binding.tvEdit);
        }
    }

    @Override
    public int getMatchBackNumber() {
        return 0;
    }
}
