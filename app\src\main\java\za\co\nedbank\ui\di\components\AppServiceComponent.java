/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.di.components;


import dagger.Subcomponent;
import za.co.nedbank.ui.ITANavigationService;
import za.co.nedbank.ui.di.modules.AppServiceModule;
import za.co.nedbank.ui.notifications.NBFirebaseMessagingService;
import za.co.nedbank.ui.notifications.NBHmsMessagingService;
import za.co.nedbank.ui.notifications.NotificationNavigationService;


@Subcomponent(modules = {AppServiceModule.class})
public interface AppServiceComponent {

    void inject(NBFirebaseMessagingService service);

    void inject(NotificationNavigationService service);

    void inject(NBHmsMessagingService service);

    void inject(ITANavigationService service);

}
