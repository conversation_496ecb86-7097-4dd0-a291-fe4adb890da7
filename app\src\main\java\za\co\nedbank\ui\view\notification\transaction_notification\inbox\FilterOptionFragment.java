package za.co.nedbank.ui.view.notification.transaction_notification.inbox;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import androidx.fragment.app.Fragment;

import java.util.ArrayList;
import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.databinding.FragmentFilterOptionBinding;

/*created by <PERSON><PERSON>*/

public class FilterOptionFragment extends Fragment {

    private List<String> optionList;
    private String type;
    private Context mContext;

    private static final String ARG_TYPE = "arg_type";
    private static final String ARG_OPTIONS = "arg_options";

    private OnFragmentInteractionListener mListener;
    private FragmentFilterOptionBinding binding;

    public FilterOptionFragment() {
        // Required empty public constructor
    }

    public static FilterOptionFragment newInstance(ArrayList<String> optionList, String type) {
        FilterOptionFragment fragment = new FilterOptionFragment();
        Bundle args = new Bundle();
        args.putStringArrayList(ARG_OPTIONS, optionList);
        args.putString(ARG_TYPE, type);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            optionList = getArguments().getStringArrayList(ARG_OPTIONS);
            type = getArguments().getString(ARG_TYPE);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentFilterOptionBinding.inflate(inflater, container, false);
        setUpOptions();
        return binding.getRoot();
    }

    private void setUpOptions() {
        if (optionList != null && !optionList.isEmpty()) {
            RadioGroup rg = new RadioGroup(getActivity()); //create the RadioGroup
            rg.setOrientation(LinearLayout.VERTICAL);
            for (int i = 0; i < optionList.size(); i++) {

                View view = LayoutInflater.from(mContext).inflate(R.layout.transaction_filter_item, null);
                RadioButton rb = view.findViewById(R.id.rbFilterItem);
                rb.setText(optionList.get(i));
                rb.setButtonDrawable(R.drawable.lifestyle_radiobutton_selector);
                rb.setId(i);
                rg.addView(view);
            }

            rg.setOnCheckedChangeListener((group, checkedId) -> {
                if (mListener != null) {
                    mListener.onFragmentInteraction(optionList.get(checkedId), type);
                }
            });
            binding.optionContainer.addView(rg);

        }
    }


    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = context;
        if (context instanceof OnFragmentInteractionListener) {
            mListener = (OnFragmentInteractionListener) context;
        } else {
            throw new RuntimeException(context.toString()
                    + " must implement OnFragmentInteractionListener");
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mListener = null;
    }

    public interface OnFragmentInteractionListener {
        void onFragmentInteraction(String toString, String type);
    }

}
