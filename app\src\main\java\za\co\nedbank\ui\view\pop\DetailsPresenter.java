package za.co.nedbank.ui.view.pop;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.navigation.NavigationRouter;

public class DetailsPresenter extends NBBasePresenter<DetailsView> {

    private final NavigationRouter mNavigationRouter;

    @Inject
    DetailsPresenter(final NavigationRouter navigationRouter) {
        this.mNavigationRouter = navigationRouter;
    }

    @Override
    protected void onBind() {
        super.onBind();
    }
}