package za.co.nedbank.ui.data.entity.money_request;

import com.squareup.moshi.Json;

public class CMSResponseEntity {

    @<PERSON><PERSON>(name = "dc:creator")
    private String creator;

    @Json(name = "dam:Physicalheightininches")
    private String physicalheightininches;

    @<PERSON><PERSON>(name = "dam:Physicalwidthininches")
    private String physicalwidthininches;

    @J<PERSON>(name = "dam:Fileformat")
    private String fileformat;
    @<PERSON><PERSON>(name = "dam:Progressive")
    private String progressive;
    @<PERSON><PERSON>(name = "tiff:ImageLength")
    private String imageLength;
    @<PERSON><PERSON>(name = "dam:extracted")
    private String extracted;
    @Json(name = "dc:format")
    private String format;
    @J<PERSON>(name = "dam:SpecialInstructions")
    private String specialInstructions;
    @J<PERSON>(name = "dc:description")
    private String description;
    @J<PERSON>(name = "dam:Bitsperpixel")
    private String bitsperpixel;
    @<PERSON><PERSON>(name = "dam:MIMEtype")
    private String mimeType;
    @<PERSON><PERSON>(name = "dam:Physicalwidthindpi")
    private String physicalwidthindpi;

    @<PERSON><PERSON>(name = "dam:Physicalheightindpi")
    private String physicalheightindpi;

    @J<PERSON>(name = "dc:modified")
    private String modified;
    @J<PERSON>(name = "dam:Numberofimages")
    private String numberofimages;

    @Json(name = "dam:Numberoftextualcomments")
    private int numberoftextualcomments;
    @Json(name = "tiff:ImageWidth")
    private int imageWidth;
    @Json(name = "dam:sha1")
    private String sha1;

    @Json(name = "dam:size")
    private String size;

    @Json(name = "dc:title")
    private String title;


    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getPhysicalheightininches() {
        return physicalheightininches;
    }

    public void setPhysicalheightininches(String physicalheightininches) {
        this.physicalheightininches = physicalheightininches;
    }

    public String getPhysicalwidthininches() {
        return physicalwidthininches;
    }

    public void setPhysicalwidthininches(String physicalwidthininches) {
        this.physicalwidthininches = physicalwidthininches;
    }

    public String getFileformat() {
        return fileformat;
    }

    public void setFileformat(String fileformat) {
        this.fileformat = fileformat;
    }

    public String getProgressive() {
        return progressive;
    }

    public void setProgressive(String progressive) {
        this.progressive = progressive;
    }

    public String getImageLength() {
        return imageLength;
    }

    public void setImageLength(String imageLength) {
        this.imageLength = imageLength;
    }

    public String getExtracted() {
        return extracted;
    }

    public void setExtracted(String extracted) {
        this.extracted = extracted;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getSpecialInstructions() {
        return specialInstructions;
    }

    public void setSpecialInstructions(String specialInstructions) {
        this.specialInstructions = specialInstructions;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBitsperpixel() {
        return bitsperpixel;
    }

    public void setBitsperpixel(String bitsperpixel) {
        this.bitsperpixel = bitsperpixel;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public String getPhysicalwidthindpi() {
        return physicalwidthindpi;
    }

    public void setPhysicalwidthindpi(String physicalwidthindpi) {
        this.physicalwidthindpi = physicalwidthindpi;
    }

    public String getPhysicalheightindpi() {
        return physicalheightindpi;
    }

    public void setPhysicalheightindpi(String physicalheightindpi) {
        this.physicalheightindpi = physicalheightindpi;
    }

    public String getModified() {
        return modified;
    }

    public void setModified(String modified) {
        this.modified = modified;
    }

    public String getNumberofimages() {
        return numberofimages;
    }

    public void setNumberofimages(String numberofimages) {
        this.numberofimages = numberofimages;
    }

    public int getNumberoftextualcomments() {
        return numberoftextualcomments;
    }

    public void setNumberoftextualcomments(int numberoftextualcomments) {
        this.numberoftextualcomments = numberoftextualcomments;
    }

    public int getImageWidth() {
        return imageWidth;
    }

    public void setImageWidth(int imageWidth) {
        this.imageWidth = imageWidth;
    }

    public String getSha1() {
        return sha1;
    }

    public void setSha1(String sha1) {
        this.sha1 = sha1;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }


}
