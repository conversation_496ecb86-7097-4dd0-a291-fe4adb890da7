package za.co.nedbank.ui.view.card_delivery.delivery_options;

import android.content.Intent;
import android.os.Bundle;

import androidx.recyclerview.widget.DividerItemDecoration;

import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.dialog.InformationalDialog;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.ActivityCardDeliveryOptionsBinding;
import za.co.nedbank.enroll_v2.view.model.card_delivery.CardDeliveryOptionsViewModel;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.di.AppDI;

public class CardDeliveryOptionsActivity extends NBBaseActivity implements CardDeliveryOptionsView, CardDeliveryOptionsAdapter.OnOptionClickListener {

    private static final String TAG = CardDeliveryOptionsActivity.class.getSimpleName();
    @Inject
    CardDeliveryOptionsPresenter presenter;
    private ActivityCardDeliveryOptionsBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCardDeliveryOptionsBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        presenter.bind(this);
        presenter.sendPageAnalytics();
        presenter.checkFeeApplicable(getFlow());
        // get isProfessionalProduct value either from intent or get here through API
        presenter.loadContent(getFlow());
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        NBLogger.e(TAG, "newIntent");
        //coming from card delivery result
        if (intent.getBooleanExtra(NavigationTarget.PARAM_IS_CARD_ORDERING_SUCCESS, false)) {
            setResult(RESULT_OK);
        } else {
            setResult(RESULT_CANCELED);
        }
        if (intent.hasExtra(NavigationTarget.VALUE_CARD_DELIVERY_FLOW_EFICA) && !intent.getBooleanExtra(NavigationTarget.VALUE_CARD_DELIVERY_FLOW_EFICA, false)) {
            close();
        }else{
            presenter.navigateToAccountReadyScreen();
        }
    }

    @Override
    public String getFlow() {
        return getIntent().getStringExtra(NavigationTarget.PARAM_CARD_DELIVERY_FLOW);
    }

    @Override
    public String getCardName() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_DESCRIPTION);
    }

    @Override
    public String getCardActionName() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_ACTION_NAME);
    }

    @Override
    public void updateOptions(List<CardDeliveryOptionsViewModel> options) {
        presenter.featureCheckForLocker(options);
        CardDeliveryOptionsAdapter adapter = new CardDeliveryOptionsAdapter(options);
        adapter.setOptionClickListener(this);
        binding.layoutCardDeliveryOptions.rvDeliveryOptions.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));
        binding.layoutCardDeliveryOptions.rvDeliveryOptions.setAdapter(adapter);
        binding.layoutCardDeliveryOptions.rvDeliveryOptions.setNestedScrollingEnabled(false);
    }

    @Override
    public boolean isCardReplaceFeeApplicable() {
        return getIntent().getBooleanExtra(ServicesNavigationTarget.PARAM_ALLOW_CARD, false);
    }

    @Override
    public void updateFeeInfo(boolean isFeeApplicable, boolean isCardDeliveryFreeProduct) {
        if (isCardDeliveryFreeProduct) {
            binding.layoutCardDeliveryOptions.infoText.setText(R.string.card_delivery_fee_for_product);
        } else {
            int textRes = isFeeApplicable ? R.string.card_delivery_fee_applicable_info : R.string.card_delivery_fee_free_info;
            binding.layoutCardDeliveryOptions.infoText.setText(textRes);
            binding.layoutCardDeliveryOptions.infoText.setContentDescription(String.format("%s %s", getString(R.string.important_information), getString(textRes)));
        }
    }

    @Override
    public String getUserError() {
        return getString(R.string.card_ordering_unavailable);
    }

    @Override
    public void showProgress(boolean isVisible) {
        if (isVisible) {
            ViewUtils.hideViews(binding.layoutCardDeliveryOptions.optionContainer);
            binding.layoutCardDeliveryOptions.progressBar.show();
        } else {
            binding.layoutCardDeliveryOptions.progressBar.hide();
            ViewUtils.showViews(binding.layoutCardDeliveryOptions.optionContainer);
        }
    }

    @Override
    public void hideContent() {
        ViewUtils.hideViews(binding.layoutCardDeliveryOptions.optionContainer);
    }

    @Override
    public void onOptionClicked(CardDeliveryOptionsViewModel selectedOption) {
        presenter.handleOptionClicked(selectedOption);
    }

    @Override
    protected void onDestroy() {
        presenter.unbind();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        if (presenter.isEfica(getFlow())) {
            String message = String.format(getString(R.string.back_button_will_not_work_choose_next_message), getString(R.string.an_action));
            InformationalDialog.getInstance(getString(R.string.please_note_title), message, getString(R.string.all_caps_ok))
                    .show(getSupportFragmentManager(), getLocalClassName());
        } else {
            super.onBackPressed();
        }
    }

    public void onSetBackResult(){
        setResult(RESULT_OK);
        presenter.navigateToAccountReadyScreen();
    }
}