package za.co.nedbank.ui.domain.repository.pop;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.ui.data.entity.pop.SharePOPRecipientsRequestEntity;
import za.co.nedbank.ui.data.entity.pop.ShareProofOfPaymentRequestEntity;
import za.co.nedbank.ui.data.entity.pop.ShareProofOfPaymentResponseEntity;
import za.co.nedbank.ui.data.networking.PostShareProofOfPaymentAPI;

public class NBShareProofOfPaymentRepository implements ShareProofOfPaymentRepository {

    private final NetworkClient mNetworkClient;

    @Inject
    public NBShareProofOfPaymentRepository(NetworkClient networkClient) {
        this.mNetworkClient = networkClient;
    }

    @Override
    public Observable<ShareProofOfPaymentResponseEntity> postShareProofOfPayment(List<ShareProofOfPaymentRequestEntity> shareProofOfPaymentRequestEntityList, String contractID) {
        return mNetworkClient.create(PostShareProofOfPaymentAPI.class).postShareProofOfPayment(shareProofOfPaymentRequestEntityList, contractID);
    }

    @Override
    public Observable<ShareProofOfPaymentResponseEntity> postShareProofOfPaymentRecipients(SharePOPRecipientsRequestEntity sharePOPRecipientsRequestEntity, String transactionId) {
        return mNetworkClient.create(PostShareProofOfPaymentAPI.class).postShareProofOfPaymentRecipients(sharePOPRecipientsRequestEntity, transactionId);
    }

    @Override
    public Observable<ShareProofOfPaymentResponseEntity> postShareProofOfPaymentRecipientsForSchedulePayment(SharePOPRecipientsRequestEntity sharePOPRecipientsRequestEntity, String transactionId) {
        return mNetworkClient.create(PostShareProofOfPaymentAPI.class).postShareProofOfPaymentRecipientsForSchedulePayment(sharePOPRecipientsRequestEntity, transactionId);
    }


}
