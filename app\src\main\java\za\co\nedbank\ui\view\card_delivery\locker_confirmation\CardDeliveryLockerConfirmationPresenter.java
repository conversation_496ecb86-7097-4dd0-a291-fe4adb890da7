package za.co.nedbank.ui.view.card_delivery.locker_confirmation;

import java.util.HashMap;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.card_delivery.PostCardDeliveryOptionRequestData;
import za.co.nedbank.core.domain.model.card_delivery.PostCardDeliveryOptionsResponseData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.card_delivery.PostCardDeliveryOptionUseCase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.card_delivery.CardDeliveryOptionsEnum;
import za.co.nedbank.services.domain.usecase.cards.ReplaceCardUseCase;
import za.co.nedbank.services.view.mapper.ReplaceCardViewModelToDataMapper;
import za.co.nedbank.services.view.model.ReplaceCardViewModel;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConfirmationBasePresenter;

import static za.co.nedbank.ui.view.card_delivery.CardDeliveryAnalytics.EVENT_CM_REPLACE_CARD_FAILURE_RETRY;

public class CardDeliveryLockerConfirmationPresenter extends CardDeliveryConfirmationBasePresenter<CardDeliveryLockerConfirmationView> {

    private int retryCount = 0;
    private static final int MAX_RETRY_COUNT = 2;

    @Inject
    protected CardDeliveryLockerConfirmationPresenter(PostCardDeliveryOptionUseCase postCardDeliveryOptionUseCase,
                                                      ReplaceCardUseCase replaceCardUseCase,
                                                      ReplaceCardViewModelToDataMapper replaceCardViewModelToDataMapper,
                                                      NavigationRouter navigationRouter,
                                                      @Named("memory") ApplicationStorage applicationStorage,
                                                      Analytics analytics,
                                                      ErrorHandler errorHandler) {
        super(postCardDeliveryOptionUseCase, replaceCardUseCase, replaceCardViewModelToDataMapper,
                navigationRouter, applicationStorage, analytics, errorHandler);
    }

    @Override
    protected ReplaceCardViewModel createReplaceCardRequestEntity() {
        ReplaceCardViewModel replaceCardViewModel = new ReplaceCardViewModel(view.getCardPlasticId());
        replaceCardViewModel.setLockerCode(view.getLockerBranchCode());
        replaceCardViewModel.setLockerName(view.getLockerName());
        return replaceCardViewModel;
    }

    @Override
    protected PostCardDeliveryOptionRequestData createPostDeliveryOptionRequestEntity() {
        PostCardDeliveryOptionRequestData requestData = new PostCardDeliveryOptionRequestData();
        requestData.setBranchCode(view.getLockerBranchCode());
        requestData.setLocationName(view.getLockerName());
        requestData.setDeliveryOptionCode(CardDeliveryOptionsEnum.LOCKER.getValue());
        String sessionId = applicationStorage.getString(StorageKeys.FICA_SESSION_ID, StringUtils.EMPTY_STRING);
        requestData.setSessionId(sessionId);
        return requestData;
    }

    public void chooseDifferentLocker() {
        sendEventAnalytics(CardDeliveryAnalytics.EVENT_CM_CHANGE_LOCKER);
        view.close();
    }

    public void confirmClick() {
        sendEventAnalytics(CardDeliveryAnalytics.EVENT_CM_CONFIRM_LOCKER);
        handleConfirmation();
    }

    public void sendEventAnalytics(String eventName) {

        HashMap<String, Object> contextDataMap = new HashMap<>();
        AdobeContextData contextData = new AdobeContextData(contextDataMap);
        contextData.setCategoryAndProduct(CardDeliveryAnalytics.VAL_CARDS_CATEGORY, getProductName());
        contextData.setProductAccount(getProductName());

        if (isEficaFlow()) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_SALES_AND_ONBOARDING);
            contextData.setFeature(CardDeliveryAnalytics.VAL_PRODUCT_ON_BOARDING);
        } else if (isReplaceCardFlow()) {
            contextData.setFeatureCategory(CardDeliveryAnalytics.VAL_CARD_MAINTENANCE);
            contextData.setFeature(view.getCardActionName());
        }
        contextData.setSubFeature(view.getCardDeliverySubFeature());
        analytics.sendEventActionWithMap(eventName, contextData.getCdata());

    }

    public void handleLockerDetails() {
        view.updateLockerDetailUI();
    }

    @Override
    protected void navigateToResult(boolean isSuccess) {
        if (isReplaceCardFlow() && !isSuccess) {
            view.showRetryScreen();
            sendPageAnalytics(CardDeliveryAnalytics.VAL_LOCKER_RETRY, CardDeliveryAnalytics.VAL_DELIVERY_TO_A_DSV_LOCKER);
        } else {
            super.navigateToResult(isSuccess);
        }
    }

    public void retry() {
        if (++retryCount <= MAX_RETRY_COUNT) {
            sendEventAnalytics(EVENT_CM_REPLACE_CARD_FAILURE_RETRY);
            view.updateRetryCountAccessibility();
            handleConfirmation();
        }
    }

    public int getRemainingRetryAttempt() {
        return MAX_RETRY_COUNT - retryCount;
    }

    public void sendPageAnalytics() {
        sendPageAnalytics(CardDeliveryAnalytics.VAL_CONFIRM_LOCKER_DETAILS, CardDeliveryAnalytics.VAL_DELIVERY_TO_A_DSV_LOCKER);
    }

    @Override
    protected void showEficaCardDeliverConfirmationResult(PostCardDeliveryOptionsResponseData responseData) {
        super.showEficaCardDeliverConfirmationResult(responseData);
        /*analytics for next page*/
        sendPageAnalytics(CardDeliveryAnalytics.VAL_LOCKER_DETAILS_CONFIRMED, CardDeliveryAnalytics.VAL_DELIVERY_TO_A_DSV_LOCKER);
    }
}
