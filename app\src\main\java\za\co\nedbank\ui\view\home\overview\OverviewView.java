/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.overview;

import android.content.res.Resources;

import androidx.annotation.DrawableRes;

import java.util.List;
import java.util.Map;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.dashboard.DashboardCardType;
import za.co.nedbank.core.data.accounts.model.LinkableAccountDto;
import za.co.nedbank.core.enumerations.BackgroundImageTypeEnum;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.core.view.model.view_banker.ViewBankerDetailsViewModel;
import za.co.nedbank.core.view.model.view_banker.ViewShowBankerResponseViewModel;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.insurance.view.other.model.response.offer.offer_details.InsGetOfferItemViewModel;

/**
 * Created by charurani on 30-06-2017.
 */

interface OverviewView extends NBBaseView {
    void setDashboardOrder(List<DashboardCardType> dashboardOrder);

    void showPreferredName(String name);

    void showOverviewError(String error);

    void showOverviewLoading(boolean loading);

    void showOverview(Overview overview);

    boolean isFeatureDisabled(String feature);

    void showOverviewReloading(boolean reloading);

    void refreshBalances();

    void receivePreApprovedOffersCount(int preApprovedOfferCount);

    void handleErrorPreApprovedOffers();

    void handleErrorLifecycleDashboard();

    Resources getResources();

    void setBackgroundImage(String imagePath);

    void setBackgroundImage(@DrawableRes int imageId);

    boolean canTransact();

    String getFicaStatus();

    void hasTransactableAccount(boolean isTransactable);

    void setUserInfo(UserDetailViewModel userDetailViewModel);

    void showEmptyView(String error);

    void showError(String message);

    void loadOverview(BackgroundImageTypeEnum backgroundImageTypeEnum);

    void shouldShowOverlay(boolean showOverlay);

    void navigateToTargetOverviewPage();

    String providePreapprovedPersonalOfferText(String loanOfferType);

    void onUnreadChatEvent(UnreadChatEvent unreadChatEvent);

    void setResult(Map<String, Object> params);

    void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent);

    void setChatIcon(int totalUnreadMessageCount);

    void showCustomerName(String name, boolean isDefaultProfile);

    String getBirthDate();

    boolean isSAResident();

    List<LinkableAccountDto> getLinkableAccountList(LinkableAccountDto linkableAccountDto);

    String getNewFreeFeature();

    String getMyPocketString();

    String getSavingsGoalPocket();

    void showViewBankerIcon(ViewShowBankerResponseViewModel viewBankerDetailsViewModel);

    void showGetCashIcon(boolean b);

    void showShopIcon(boolean b);

    void showBankerDetails(ViewBankerDetailsViewModel viewBankerDetailsViewModel);

    void showBankerError();

    String getOverviewProductGroup();

    String getOverviewAccountType(OverviewType overviewType, String accountName, String accountCode, boolean isPocketAccount);

    void receiveCountForBorrowWidget(int notificationCount);

    void moveToDashboardPosition(int position);

    void sendAnalytics(String actionName, String entryPoint);

    void openAvoInWebBrowser(String url);

    void openAVODeepLinkScreen(String deeplink);

    String getErrorScreenTitle();

    String getErrorScreenDescription();

    String getErrorScreenButtonTitle();

    void updateDynamicAvoBanner(MediaCardViewModel mediaCardViewModel);

    void updateLocalAvoBanner();

    void loadBackgroundImage();

    void setOverviewTitleFor(OverviewType overviewType);

    void onServerStateChanged(boolean chatConnected);

    void showShapIDIcon();

    void showWidgetProgress(boolean visible);

    boolean isAccessibilityEnabled();

    String getActivityLabel();

    boolean isDashboardAdamOfferVisibleFirstTimeAfterLogin();

    void setDashboardAdamOfferVisibleFirstTimeAfterLogin(boolean b);

    void showInsuranceOffersCount(List<InsGetOfferItemViewModel> insuranceOfferList);

    String getUserTypeString(boolean isBusinessUser);
}
