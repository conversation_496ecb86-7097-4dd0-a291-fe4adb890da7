package za.co.nedbank.ui.view.home.fc_deep_linking;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.Nullable;

import com.facetec.sdk.FaceTecSDK;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.databinding.ActivityDeepLinkIntermediateBinding;
import za.co.nedbank.enroll_v2.view.model.fica.ClientDeviceInfoRequestViewModel;
import za.co.nedbank.ui.di.AppDI;

public class DeepLinkIntermediateActivity extends NBBaseActivity implements DeepLinkIntermediateView {

    @Inject
    DeepLinkIntermediatePresenter mDeepLinkIntermediatePresenter;

    private String mFeatureName = null;
    private boolean isPreLogin;
    private ActivityDeepLinkIntermediateBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDeepLinkIntermediateBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        init();
    }

    public void init() {
        AppDI.getActivityComponent(this).inject(this);
        mDeepLinkIntermediatePresenter.bind(this);

        // show the loading
        binding.loadingView.setLoadingVisible(true);

        // get data from intent
        getDataFromIntent();

        // call api through presenter
        mDeepLinkIntermediatePresenter.handleFlowForFeature(mFeatureName,isPreLogin);
    }

    private void getDataFromIntent() {
        if (getIntent() != null) {
            Bundle extras = getIntent().getExtras();
            if (extras != null) {
                mFeatureName = extras.getString(NavigationTarget.PARAM_EXTRA_FEATURE_NAME);
                isPreLogin=extras.getBoolean(NavigationTarget.PARAM_PRE_LOGIN_DEEP_LINK);
            }
        }
    }


    @Override
    public void showError() {
        // clear the value from the application storage
        mDeepLinkIntermediatePresenter.clearDeepLinkingValue();

        // set the value and send it to home screen
        Intent intent = new Intent();
        intent.putExtra(Constants.DEEP_LINKING_RESULT_TITLE, getString(R.string.something_went_wrong_plain));
        intent.putExtra(Constants.DEEP_LINKING_RESULT_MESSAGE, getString(R.string.deeplinking_error_message, mFeatureName));
        setResult(RESULT_OK, intent);
        finishActivity();
    }


    @Override
    public void handleError(){
        // clear the value from the application storage
        mDeepLinkIntermediatePresenter.clearDeepLinkingValue();
        finishActivity();
    }


    @Override
    public void finishActivity() {
        finish();
    }

    @Override
    protected void onDestroy() {
        mDeepLinkIntermediatePresenter.unbind();
        super.onDestroy();
    }

    public void getClientDeviceInfoRequestViewModel() {
        ClientDeviceInfoRequestViewModel clientDeviceInfoRequestViewModel = new ClientDeviceInfoRequestViewModel();
        clientDeviceInfoRequestViewModel.setOperatingSystem(za.co.nedbank.enroll_v2.Constants.OS_NAME);
        clientDeviceInfoRequestViewModel.setOperatingSystemVersion(Build.VERSION.RELEASE);
        clientDeviceInfoRequestViewModel.setSerialNumber(DeviceUtils.getSerialNumber(this));
        clientDeviceInfoRequestViewModel.setZoomVersion(FaceTecSDK.version());
        clientDeviceInfoRequestViewModel.setModelNumber(Build.MODEL);
        mDeepLinkIntermediatePresenter.getUserInfoForSavingPocket(clientDeviceInfoRequestViewModel);
    }
}