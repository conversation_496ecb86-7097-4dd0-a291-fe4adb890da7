package za.co.nedbank.ui.view.notification.notification_preferences.delivery_preferences;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.fbnotifications.ClientPreferenceViewModel;
import za.co.nedbank.databinding.ActivityPreferenceDeliveryBinding;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.PreApprovedOffersTrackingEvent;
import za.co.nedbank.ui.di.AppDI;

public class DeliveryPreferencesActivity extends NBBaseActivity implements DeliveryPreferencesView {

    @Inject
    DeliveryPreferencesPresenter mDeliveryPreferencesPresenter;

    private Boolean allowPushNotificationForOffers;
    private Boolean allowUnauthentictedInbox;
    private String lowPriorityDeliveryPeriod = NotificationConstants.LOW_PRIORITY_DELIVERY_ALL;
    private String selectedTime;
    private ActivityPreferenceDeliveryBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityPreferenceDeliveryBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        mDeliveryPreferencesPresenter.bind(this);
        receiveBundle();
        initToolbar(binding.toolbar, true, this.getString(R.string.delivery_prefs_heading));
        setupUI();

    }

    private void setupUI() {
        setUpOptions();
        binding.preferenceContainer.setVisibility(View.GONE);
        enableSaveButton(false);
        initData();
    }

    private void initData() {
        if (!lowPriorityDeliveryPeriod.equals(NotificationConstants.LOW_PRIORITY_DELIVERY_ALL)) {
            binding.preferenceContainer.setVisibility(View.VISIBLE);
            binding.allMsgContainer.setVisibility(View.GONE);
            binding.tvOptionsTxt.setVisibility(View.VISIBLE);
            binding.saveChangesButton.setVisibility(View.VISIBLE);
            binding.switchSnoozeActive.setChecked(true);
            announceForAccessibility(TextUtils.concat(getString(R.string.you_have_chosen), selectedTime, getString(R.string.as_preferred_time_to_receive)).toString());
        } else {
            announceForAccessibility(getString(R.string.accessibility_content_no_pref_selected));
        }
        binding.switchSnoozeActive.setOnCheckedChangeListener((buttonView, isChecked) -> onSnoozeActiveChanged(isChecked));
        binding.saveChangesButton.setOnClickListener(v -> onClickSaveChanges());
    }

    private void announceForAccessibility(String accesiblitycontent) {
        Observable.timer(2, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(result-> AccessibilityUtils.announceForAccessibility(this, accesiblitycontent));

    }

    @Override
    public String fetchDeliveryPeriod(int time) {
        return getString(time);
    }

    @Override
    public String fetchDeliveryTime(ClientPreferenceViewModel clientPreferenceViewModel, boolean isSnoozeActive) {
        String delTime = StringUtils.EMPTY_STRING;
        if (isSnoozeActive && clientPreferenceViewModel.getLowPriorityDeliveryPeriod() != null && !clientPreferenceViewModel.getLowPriorityDeliveryPeriod().isEmpty()) {
            for (NotificationConstants.LowPriorityDeliveryPeriod l : NotificationConstants.LowPriorityDeliveryPeriod.values()) {
                delTime = PreApprovedOffersTrackingEvent.VAL_PREFERRED_NOTIFICATION_TIMINGS + fetchDeliveryPeriod(l.getLabel());
            }
        }
        return delTime;
    }

    private void setUpOptions() {

        List<String> optionList = new ArrayList<>();
        RadioGroup rg = new RadioGroup(this);
        rg.setOrientation(LinearLayout.VERTICAL);
        for (NotificationConstants.LowPriorityDeliveryPeriod l : NotificationConstants.LowPriorityDeliveryPeriod.values()) {

            View view = LayoutInflater.from(this).inflate(R.layout.transaction_filter_item, null);
            RadioButton rb = view.findViewById(R.id.rbFilterItem);
            rb.setText(l.getLabel());
            optionList.add(l.getValue());
            rb.setButtonDrawable(R.drawable.lifestyle_radiobutton_selector);
            rb.setId(l.ordinal());
            rb.setChecked(l.getValue().contains(lowPriorityDeliveryPeriod));
            if (l.getValue().contains(lowPriorityDeliveryPeriod))
                selectedTime = getString(l.getLabel());
            rg.addView(rb);

        }

        rg.setOnCheckedChangeListener((group, checkedId) -> {
            binding.optionMsgContainer.setVisibility(View.VISIBLE);
            lowPriorityDeliveryPeriod = optionList.get(checkedId);
            enableSaveButton(true);
        });


        binding.preferenceContainer.addView(rg);
    }


    @Override
    protected void onDestroy() {
        mDeliveryPreferencesPresenter.unbind();
        super.onDestroy();
    }


    private void receiveBundle() {
        if (getIntent() != null && getIntent().getExtras() != null) {
            allowPushNotificationForOffers = getIntent().getExtras().getBoolean(NotificationConstants.EXTRA.CLIENT_PREFERENCES_FOR_YOU, false);
            allowUnauthentictedInbox = getIntent().getExtras().getBoolean(NotificationConstants.EXTRA.CLIENT_PREFERENCES_UNAUTH, false);
            lowPriorityDeliveryPeriod = getIntent().getExtras().getString(NotificationConstants.EXTRA.CLIENT_PREFERENCES_DELIVERY, NotificationConstants.LOW_PRIORITY_DELIVERY_ALL);
        }

    }


    public void onClickSaveChanges() {
        mDeliveryPreferencesPresenter.updatePreferences(getClientPreferenceViewModel(), binding.switchSnoozeActive.isChecked());
    }

    private ClientPreferenceViewModel getClientPreferenceViewModel() {
        ClientPreferenceViewModel clientPreferenceViewModel = new ClientPreferenceViewModel();
        clientPreferenceViewModel.setAllowPushNotificationForOffers(allowPushNotificationForOffers);
        clientPreferenceViewModel.setAllowUnauthentictedInbox(allowUnauthentictedInbox);
        clientPreferenceViewModel.setLowPriorityDeliveryPeriod(lowPriorityDeliveryPeriod);
        return clientPreferenceViewModel;
    }


    public void onSnoozeActiveChanged(boolean isChecked) {

        if (isChecked) {
            binding.preferenceContainer.setVisibility(View.VISIBLE);
            binding.allMsgContainer.setVisibility(View.GONE);
            binding.tvOptionsTxt.setVisibility(View.VISIBLE);
            binding.saveChangesButton.setVisibility(View.VISIBLE);
            announceForAccessibility(getString(R.string.snooze_active_prompt));
        } else {
            binding.preferenceContainer.setVisibility(View.GONE);
            binding.allMsgContainer.setVisibility(View.VISIBLE);
            binding.tvOptionsTxt.setVisibility(View.GONE);
            binding.optionMsgContainer.setVisibility(View.GONE);
            enableSaveButton(false);
            lowPriorityDeliveryPeriod = NotificationConstants.LOW_PRIORITY_DELIVERY_ALL;
            onClickSaveChanges();
        }

    }

    @Override
    public void showProgress(boolean isVisible) {
        binding.progressBar.setVisibility(isVisible ? View.VISIBLE : View.GONE);
    }


    @Override
    public void showErrorForUpdatePreferences(boolean isApiFailure) {
        showError(getString(R.string.something_went_wrong), getString(R.string.try_again_later));
        mDeliveryPreferencesPresenter.trackSnoozeFailure(isApiFailure, getString(R.string.something_went_wrong));
    }

    @Override
    public void enableSaveButton(boolean isEnable) {
        binding.saveChangesButton.setEnabled(isEnable);
        binding.saveChangesButton.setClickable(isEnable);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        if (menuItem.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(menuItem);
    }

    @Override
    public void onBackPressed() {
        mDeliveryPreferencesPresenter.trackActionDeliveryPreferenceBack();
        super.onBackPressed();
    }

}
