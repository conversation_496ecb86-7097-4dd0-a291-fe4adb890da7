package za.co.nedbank.ui.view.enbichatbot;

import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.dialog.BaseDialogFragment;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.databinding.FragmentDialogNontpAgentErrorBinding;
import za.co.nedbank.ui.di.AppDI;


public class NonTpLiveAgentErrorDialog extends BaseDialogFragment {

    public static final String TAG = NonTpLiveAgentErrorDialog.class.getCanonicalName();

    public static NonTpLiveAgentErrorDialog getInstance() {
        NonTpLiveAgentErrorDialog bundleOptionDialogFragment = new NonTpLiveAgentErrorDialog();

        return bundleOptionDialogFragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, R.style.AppTheme_Dialog_MyDialogTheme);

    }

    @Override
    public View onCreateView(@NonNull final LayoutInflater inflater, @Nullable final ViewGroup container, @Nullable final Bundle savedInstanceState) {
        FragmentDialogNontpAgentErrorBinding binding = FragmentDialogNontpAgentErrorBinding.inflate(inflater, container, false);
        AppDI.getActivityComponent(((NBBaseActivity) getActivity())).inject(this);
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            getDialog().getWindow().requestFeature(Window.FEATURE_NO_TITLE);
            getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }
        binding.everydayBankingContact.setOnClickListener(v -> callEverydayClick());
        binding.complaintContact.setOnClickListener(v -> callComplaintClick());
        binding.btnBackToEnbi.setOnClickListener(v -> callBackToEnbiClick());
        return binding.getRoot();
    }

    public void callEverydayClick() {
        callNedbankCenter("+27 800 555 111");
    }

    public void callComplaintClick() {
        callNedbankCenter("+27 860 444 000");
    }

    public void callBackToEnbiClick() {
        dismiss();
    }

    public void callNedbankCenter(String contactNumber) {
        Intent intent = new Intent(Intent.ACTION_DIAL);
        intent.setData(Uri.parse(NavigationTarget.TEL + contactNumber));
        startActivity(intent);
    }

}