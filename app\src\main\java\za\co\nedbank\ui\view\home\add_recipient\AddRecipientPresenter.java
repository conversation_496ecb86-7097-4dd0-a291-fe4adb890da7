/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.add_recipient;

import android.annotation.SuppressLint;
import android.app.Activity;

import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.annotations.NonNull;
import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.mapper.RecipientResponseDataToViewModelMapper;
import za.co.nedbank.core.domain.model.response.RecipientResponseData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.enrol.ApproveItFallbackUsecase;
import za.co.nedbank.core.domain.usecase.enrol.FraudApproveItUsecase;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.payment.ErrorViewModel;
import za.co.nedbank.core.payment.PaymentsUtility;
import za.co.nedbank.core.sharedui.ui.nbrecyclerview.NbRecyclerViewBaseDataModel;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.CollectionUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.validation.AccountValidator;
import za.co.nedbank.core.validation.CreditCardNumberValidator;
import za.co.nedbank.core.validation.ElectricityMeterNumberValidator;
import za.co.nedbank.core.validation.EmailValidator;
import za.co.nedbank.core.validation.MobileNumberValidator;
import za.co.nedbank.core.validation.NonEmptyTextValidator;
import za.co.nedbank.core.validation.RecipientNameCharValidator;
import za.co.nedbank.core.validation.ReferenceFieldValidator;
import za.co.nedbank.core.validation.ShapIdValidator;
import za.co.nedbank.core.view.idvl.VerificationInfoViewModel;
import za.co.nedbank.core.view.mapper.RecipientViewModelToEntityMapper;
import za.co.nedbank.core.view.metadata.ResultDataViewModel;
import za.co.nedbank.core.view.metadata.ResultDetailViewModel;
import za.co.nedbank.core.view.recipient.RecipientResponseViewModel;
import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.nid_sdk.main.interaction.model.ApproveItRequestDto;
import za.co.nedbank.nid_sdk.main.interaction.model.FraudApproveItAcknowledgeDto;
import za.co.nedbank.nid_sdk.main.views.approveit.check.ApproveItWorkFlow;
import za.co.nedbank.payment.common.validation.RecipientNameValidator;
import za.co.nedbank.payment.common.view.tracking.PaymentsTracking;
import za.co.nedbank.profile.view.profile.limits.IDVLProcessType;
import za.co.nedbank.ui.domain.usecase.AddRecipientUseCase;
import za.co.nedbank.ui.domain.usecase.GetShapPayDomainUseCase;
import za.co.nedbank.ui.domain.usecase.RecipientStatusUseCase;
import za.co.nedbank.ui.view.home.MatchBackNumberUtils;


/**
 * Created by priyadhingra on 9/4/2017.
 */

public class AddRecipientPresenter extends BaseRecipientPresenter<AddRecipientView> {

    private final AddRecipientUseCase mAddRecipientUseCase;
    private final RecipientStatusUseCase mRecipientStatusUseCase;
    private final RecipientViewModelToEntityMapper mAddRecipientViewModelToEntityMapper;
    private final RecipientResponseDataToViewModelMapper mAddRecipientResponseDataToViewModelMapper;
    private final Analytics mAnalytics;
    private final FraudApproveItUsecase mFraudApproveItUsecase;
    private final ApproveItFallbackUsecase mApproveItFallbackUsecase;
    private final ApplicationStorage mApplicationStorage;
    private final Activity mContext;
    private String verificationReferenceId;

    private enum ProcessStepSecurity {
        ITA,
        IDVL,
        NONE
    }
    private ProcessStepSecurity processStepSecurity = ProcessStepSecurity.NONE;

    @Inject
    AddRecipientPresenter(final NavigationRouter navigationRouter, final RecipientNameValidator recipientNameValidator, final RecipientNameCharValidator recipientNameNewValidator, final MobileNumberValidator mMobileNumberValidator, final NonEmptyTextValidator nonEmptyTextValidator,
                          final ReferenceFieldValidator referenceFieldValidator, final CreditCardNumberValidator creditCardNumberValidator, final AccountValidator accountValidator,
                          final ElectricityMeterNumberValidator electricityMeterNumberValidator, final EmailValidator emailValidator,
                          final AddRecipientUseCase addRecipientUseCase,
                          final RecipientStatusUseCase recipientStatusUseCase,
                          final ErrorHandler errorHandler,
                          final RecipientViewModelToEntityMapper addRecipientViewModelToEntityMapper,
                          final FraudApproveItUsecase fraudApproveItUsecase,
                          final ApproveItFallbackUsecase approveItFallbackUsecase,
                          final ApplicationStorage applicationStorage,
                          final GetShapPayDomainUseCase getFastPayDomainUseCase,
                          final Activity context, final FeatureSetController featureSetController,
                          final RecipientResponseDataToViewModelMapper addRecipientResponseDataToViewModelMapper, final Analytics analytics, final ShapIdValidator shapIdValidator) {
        super(navigationRouter, recipientNameValidator, recipientNameNewValidator, mMobileNumberValidator, nonEmptyTextValidator, referenceFieldValidator, creditCardNumberValidator, accountValidator, electricityMeterNumberValidator, emailValidator, errorHandler, featureSetController, shapIdValidator, getFastPayDomainUseCase);
        this.mAddRecipientUseCase = addRecipientUseCase;
        this.mRecipientStatusUseCase = recipientStatusUseCase;
        this.mAddRecipientViewModelToEntityMapper = addRecipientViewModelToEntityMapper;
        this.mAddRecipientResponseDataToViewModelMapper = addRecipientResponseDataToViewModelMapper;
        this.mAnalytics = analytics;
        this.mApplicationStorage = applicationStorage;
        this.mFraudApproveItUsecase = fraudApproveItUsecase;
        this.mApproveItFallbackUsecase = approveItFallbackUsecase;
        this.mContext = context;
    }

    @Override
    protected void onBind() {
        if (null != mNavigationResult && mNavigationResult.isOk() && view != null) {
            view.setResult(mNavigationResult.getParams());
        }
    }

    void postAddRecipient(String recipientName, List<NbRecyclerViewBaseDataModel> bankAccountViewDataModelList,
                          List<NbRecyclerViewBaseDataModel> creditCardViewDataModelList,
                          List<NbRecyclerViewBaseDataModel> mobileNumberViewDataModelList,
                          List<NbRecyclerViewBaseDataModel> electricityMeterViewDataModelList,
                          List<NbRecyclerViewBaseDataModel> emailViewDataModelList,
                          List<NbRecyclerViewBaseDataModel> shapeIdDataModelList
    ) {
        if (view != null && (CollectionUtils.isNotEmpty(bankAccountViewDataModelList) || CollectionUtils.isNotEmpty(mobileNumberViewDataModelList) || CollectionUtils.isNotEmpty(electricityMeterViewDataModelList) || CollectionUtils.isNotEmpty(creditCardViewDataModelList) || CollectionUtils.isNotEmpty(shapeIdDataModelList))) {
            int lastMatchBackNumber = MatchBackNumberUtils.removeMatchBackNumbers(bankAccountViewDataModelList, creditCardViewDataModelList, mobileNumberViewDataModelList, electricityMeterViewDataModelList, shapeIdDataModelList, null);
            ((AddRecipientView) view).setMatchBackNumber(lastMatchBackNumber);
            callAddRecipientUseCase(mAddRecipientViewModelToEntityMapper.mapInputDataToRecipientViewModel(recipientName, null, view.getDefaultBankNameText(), bankAccountViewDataModelList, mobileNumberViewDataModelList
                    , electricityMeterViewDataModelList, creditCardViewDataModelList, emailViewDataModelList, null, null, shapeIdDataModelList));
        }
    }

    public String getVerificationReferenceId() {
        return verificationReferenceId;
    }

    @SuppressLint("CheckResult")
    void callRecipientStatus(String verificationReferenceId) {
        mRecipientStatusUseCase.execute(verificationReferenceId)
                .doOnSubscribe(disposable -> preApiCallTasks())
                .compose(bindToLifecycle())
                .subscribe(addRecipientResponseData -> {
                    FraudApproveItAcknowledgeDto acknowledgeDto = mAddRecipientResponseDataToViewModelMapper.mapRecipientResponseToAcknowledgeDto(addRecipientResponseData);
                    handleStatusResponse(acknowledgeDto);
                }, throwable -> handleTransactionError(null, throwable));
    }

    @SuppressLint("CheckResult")
    private void callAddRecipientUseCase(@NonNull final RecipientViewModel addRecipientViewModel) {
        mAddRecipientUseCase.execute(addRecipientViewModel)
                .doOnSubscribe(disposable -> preApiCallTasks())
                .compose(bindToLifecycle())
                .subscribe(addRecipientResponseData -> {
                    processStepSecurity = ProcessStepSecurity.NONE;
                    handleAddRecipientResponse(addRecipientResponseData, addRecipientViewModel);
                }, throwable -> {
                    if (view instanceof AddRecipientView) {
                        ((AddRecipientView) view).showAddRecipientApiError();
                        trackFailure(false, PaymentsTracking.RECIPIENTS_ADD_FAILURE, ApiAliasConstants.ADD_REC, mErrorHandler.getErrorMessage(throwable).getMessage(), null);
                    }
                });
    }

    void handleAddRecipientResponse(RecipientResponseData addRecipientResponseData,
                                    RecipientViewModel addRecipientViewModel) {
        if (null != addRecipientResponseData) {
            RecipientResponseViewModel addRecipientResponseViewModel = mAddRecipientResponseDataToViewModelMapper.mapRecipientResponseDataToViewModel(addRecipientResponseData);
            ErrorViewModel errorViewModel = PaymentsUtility.retrieveApiCallErrors(addRecipientResponseViewModel.getMetadata(), mErrorHandler.getUnknownError(), DtoHelper.BENEFICIARYSAVED);
            boolean isValidate = addRecipientViewModel != null && addRecipientViewModel.isValidate();
            if (errorViewModel.isBeneficiarySaveFailed()) {
                handleTransactionError(errorViewModel, null);
            } else if (errorViewModel.isTransactionSuccess()) {
                if (isValidate) {
                    handleValidateResponse(addRecipientResponseViewModel, addRecipientViewModel);
                } else {
                    sendProcessStepAnalytics(false);
                    handleTransactionSuccess(addRecipientResponseViewModel, errorViewModel);
                }
            } else if (errorViewModel.isTransactionPending()) {
                callFraudApproveItUsecase(errorViewModel, mFraudApproveItUsecase);
                sendProcessStepAnalytics(isValidate);
            } else if (errorViewModel.isTransactionFallback()) {
                callFraudApproveItUsecase(errorViewModel, mApproveItFallbackUsecase);
                sendProcessStepAnalytics(isValidate);
            } else if (addRecipientResponseViewModel.getData() != null
                    && addRecipientResponseViewModel.getData().getSecureTransaction() != null
                    && StringUtils.isNotEmpty(addRecipientResponseViewModel.getData().getSecureTransaction().getProcessStep())) {
                processStepSecurity = ProcessStepSecurity.IDVL;
                sendProcessStepAnalytics(isValidate);
                verificationReferenceId = String.valueOf(addRecipientResponseViewModel.getData().getSecureTransaction().getVerificationReferenceId());
                handleProcessType(addRecipientResponseViewModel, errorViewModel);
            } else {
                handleTransactionError(errorViewModel, null);
            }
        }
    }

    private void sendProcessStepAnalytics(boolean validate) {
        if (!validate) {
            HashMap<String, Object> cdata = new HashMap<>();
            if (ProcessStepSecurity.IDVL.equals(processStepSecurity) || ProcessStepSecurity.ITA.equals(processStepSecurity)) {
                AdobeContextData adobeContextData = new AdobeContextData(cdata);
                if (ProcessStepSecurity.IDVL.equals(processStepSecurity)) {
                    adobeContextData.setContext8(TrackingEvent.ANALYTICS.VAL_PROCESS_STEP_UP);
                } else if (ProcessStepSecurity.ITA.equals(processStepSecurity)) {
                    adobeContextData.setContext8(TrackingEvent.ANALYTICS.VAL_PROCESS_ITA);
                }
            }
            mAnalytics.sendEventActionWithMap(TrackingEvent.ANALYTICS.VAL_RECIPIENTS_ADD_PROCESS_STEP_CHECK, cdata);
        }
    }

    public void handleProcessType(RecipientResponseViewModel profileLimitsResponseViewModel, ErrorViewModel errorViewModel) {
        if (profileLimitsResponseViewModel != null
                && profileLimitsResponseViewModel.getData() != null
                && profileLimitsResponseViewModel.getData().getSecureTransaction() != null
                && StringUtils.isNotEmpty(profileLimitsResponseViewModel.getData().getSecureTransaction().getProcessStep())) {
            String processStepType = profileLimitsResponseViewModel.getData().getSecureTransaction().getProcessStep();
            String bioSessionToken = profileLimitsResponseViewModel.getData().getSecureTransaction().getBioSessionToken();
            Integer verificationID = profileLimitsResponseViewModel.getData().getSecureTransaction().getVerificationReferenceId();
            IDVLProcessType idvlProcessType = IDVLProcessType.valueOf(processStepType);
            switch (idvlProcessType) {
                case COMPLETED:
                    handleTransactionSuccess(profileLimitsResponseViewModel, errorViewModel);
                    break;
                case FACIALSTEPUP:
                    stopLoading();
                    mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.SECURITY_ALERT_SCREEN)
                            .withParam(NavigationTarget.PARAM_BIO_SESSION_TOKEN, bioSessionToken)
                            .withParam(NavigationTarget.PARAM_PROCESS_TYPE, processStepType)
                            .withParam(NavigationTarget.NAVIGATION_FROM, NavigationTarget.ADD_RECIPIENT)
                            .withParam(NavigationTarget.PARAM_VERIFICATION_INFO, getVerificationInfoViewModel(verificationID, StringUtils.EMPTY_STRING, 0))
                    );
                    break;
                case STOP:
                    moveToStopScreen();
                    break;
                default:
                    break;
            }
        }
    }

    public void stopLoading() {
        if (view != null) {
            view.showLoadingOnButton(false);
            view.setLoadingButtonEnabled(true);
            view.setEnabledActivityTouch(true);
        }
    }

    public void moveToStopScreen() {
        stopLoading();
        trackFailure(true, PaymentsTracking.RECIPIENTS_ADD_FAILURE, ApiAliasConstants.ADD_REC, mErrorHandler.getUnknownError(), null);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.STOP_SCREEN)
                .withParam(NavigationTarget.NAVIGATION_FROM, NavigationTarget.ADD_RECIPIENT));
    }


    public VerificationInfoViewModel getVerificationInfoViewModel(Integer id, String method, Integer otp) {
        VerificationInfoViewModel verificationInfoViewModel = new VerificationInfoViewModel();
        verificationInfoViewModel.setVerificationID(id);
        verificationInfoViewModel.setVerificationMethod(method);
        verificationInfoViewModel.setOtp(otp);
        return verificationInfoViewModel;
    }

    private void handleValidateResponse(RecipientResponseViewModel addRecipientResponseViewModel, RecipientViewModel addRecipientViewModel) {
        if (addRecipientResponseViewModel.getData() != null) {
            addRecipientViewModel.setValidate(!addRecipientViewModel.isValidate());
            AddRecipientPresenter.this.callAddRecipientUseCase(addRecipientViewModel);
        } else {
            if (view instanceof AddRecipientView) {
                ((AddRecipientView) view).showAddRecipientApiError(mErrorHandler.getUnknownError());
                trackFailure(true, PaymentsTracking.RECIPIENTS_ADD_FAILURE, ApiAliasConstants.ADD_REC, mErrorHandler.getUnknownError(), null);
            }
        }
    }

    private void handleTransactionSuccess(RecipientResponseViewModel addRecipientResponseViewModel, ErrorViewModel errorViewModel) {
        if (addRecipientResponseViewModel.getMetadata() != null && addRecipientResponseViewModel.getMetadata().getResultData() != null && !addRecipientResponseViewModel.getMetadata().getResultData().isEmpty()) {
            boolean isSuccessForAllRecipient = isTransactionSuccessForAll(addRecipientResponseViewModel);
            if (view instanceof AddRecipientView) {
                if (isSuccessForAllRecipient) {
                    ((AddRecipientView) view).onRecipientAdded();
                } else {
                    ((AddRecipientView) view).handleFewRecipientAdditionFailedError(errorViewModel.getErrorMessages());
                    trackFailure(true, PaymentsTracking.RECIPIENTS_ADD_FAILURE, ApiAliasConstants.ADD_REC, errorViewModel.getErrorMessage(), null);
                }
            }
        }
    }

    private void handleTransactionError(ErrorViewModel errorViewModel, Throwable throwable) {
        if (view instanceof AddRecipientView) {
            if (errorViewModel != null) {
                setMatchBackNumberErrorMap(errorViewModel.getMatchBackNumberMap(), MatchBackNumberUtils.getMatchBackNumberAvoidList());
                ((AddRecipientView) view).showAddRecipientApiError(errorViewModel.getMatchBackNumberMap(), errorViewModel.getErrorMessages());
                trackFailure(true, PaymentsTracking.RECIPIENTS_ADD_FAILURE, ApiAliasConstants.ADD_REC, errorViewModel.getErrorMessage(), null);
            } else if (throwable != null) {
                ((AddRecipientView) view).showAddRecipientApiError(mErrorHandler.getErrorMessage(throwable).getMessage());
                trackFailure(false, PaymentsTracking.RECIPIENTS_ADD_FAILURE, ApiAliasConstants.ADD_REC, mErrorHandler.getErrorMessage(throwable).getMessage(), null);
            }
        }
    }

    private boolean isTransactionSuccessForAll(RecipientResponseViewModel addRecipientResponseViewModel) {
        List<ResultDataViewModel> addRecipientResultList = addRecipientResponseViewModel.getMetadata().getResultData();
        for (ResultDataViewModel resultDatumViewModel : addRecipientResultList) {
            if (resultDatumViewModel.getResultDetail() != null && !resultDatumViewModel.getResultDetail().isEmpty()) {
                for (ResultDetailViewModel resultDetailViewModel : resultDatumViewModel.getResultDetail()) {
                    if (resultDetailViewModel.getStatus() != null && !resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private void handleStatusResponse(FraudApproveItAcknowledgeDto acknowledgeDto) {
        if (acknowledgeDto.getResultCode() != null && acknowledgeDto.getResultCode().equalsIgnoreCase(DtoHelper.SUCCESS)) {
            ((AddRecipientView) view).onRecipientAdded();
        } else if (acknowledgeDto.getResultCode() != null && acknowledgeDto.getResultCode().equalsIgnoreCase(DtoHelper.RESEND)) {
            ((AddRecipientView) view).postAddRecipient();
        } else {
            handleApproveItFailure(acknowledgeDto);
        }
    }

    private void callFraudApproveItUsecase(ErrorViewModel errorViewModel, UseCase<ApproveItRequestDto, FraudApproveItAcknowledgeDto> useCase) {
        processStepSecurity = ProcessStepSecurity.ITA;
        useCase.execute(getApproveItRequestDto(errorViewModel))
                .subscribe(acknowledgeDto -> {
                    NBLogger.v("KK FraudApproveIt", acknowledgeDto.getResultCode() + "");
                    if (view != null) {
                        handleStatusResponse(acknowledgeDto);
                    }
                }, throwable -> {
                    if (view instanceof AddRecipientActivity) {
                        ((AddRecipientActivity) view).showAddRecipientApiError(mErrorHandler.getErrorMessage(throwable).getMessage());
                        trackFailure(false, PaymentsTracking.RECIPIENTS_ADD_FAILURE, ApiAliasConstants.FR_AU, mErrorHandler.getErrorMessage(throwable).getMessage(), null);
                    }
                });
    }

    private void handleApproveItFailure(FraudApproveItAcknowledgeDto acknowledgeDto) {
        if (acknowledgeDto.getMessage() != null) {
            showError(acknowledgeDto.getMessage());
            trackFailure(true, PaymentsTracking.RECIPIENTS_ADD_FAILURE, ApiAliasConstants.FR_AU, acknowledgeDto.getMessage(), null);
        } else {
            String errorMsg = mContext.getString(za.co.nedbank.profile.R.string.request_rejected);
            showError(errorMsg);
            trackFailure(true, PaymentsTracking.RECIPIENTS_ADD_FAILURE, ApiAliasConstants.FR_AU, errorMsg, null);
        }
    }

    private ApproveItRequestDto getApproveItRequestDto(ErrorViewModel errorViewModel) {
        String transactionID = errorViewModel.getTransactionId() != null ? errorViewModel.getTransactionId() : StringUtils.EMPTY_STRING;
        String refrenceURL = Constants.FraudApproveItEndPoint.RECIPIENT;
        ApproveItRequestDto requestDto = new ApproveItRequestDto();
        requestDto.setAuthRefernce(transactionID);
        requestDto.setResult(errorViewModel.getTransactionResult());
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.SENSITIVE_TRANSACTIONS_CALLBACK)) {
            requestDto.setApproveItType(ApproveItWorkFlow.CALLBACK);
        }
        requestDto.setApproveItApiEndPoint(refrenceURL + transactionID + Constants.FraudApproveItEndPoint.STATUS);
        requestDto.setMobielNumber(StringUtils.obfuscateMobileNumber(mApplicationStorage.getString(StorageKeys.MOBILE_NUMBER_UNMASKED, StringUtils.EMPTY_STRING)));
        return requestDto;
    }

    private void showError(String message) {
        String finalMessage;
        if (view != null) {
            if (!StringUtils.isNullOrEmpty(message) && message.contentEquals("REJECT")) {
                finalMessage = mContext.getString(za.co.nedbank.profile.R.string.request_rejected);
            } else if (!StringUtils.isNullOrEmpty(message)) {
                finalMessage = message;
            } else {
                finalMessage = mContext.getString(za.co.nedbank.core.R.string.something_went_wrong);
            }
            ((AddRecipientView) view).showApproveItApiError(finalMessage);
        }
    }

    void trackSuccessAction() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        adobeContextData.setFeature(TrackingEvent.ANALYTICS.VAL_FEATURE_MANAGE_RECIPIENTS);
        adobeContextData.setSubFeature(TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_ADD_NEW_RECIPIENT);
        adobeContextData.setStepName(TrackingEvent.ANALYTICS.VAL_STEP_NAME_ADD_RECIPIENT);

        if (ProcessStepSecurity.IDVL.equals(processStepSecurity) || ProcessStepSecurity.ITA.equals(processStepSecurity))
            adobeContextData.setContext8Count();

        mAnalytics.sendEventActionWithMap(PaymentsTracking.RECIPIENTS_ADD_SUCCESSFUL, cdata);
    }

    public void trackFailure(boolean isApiFailure, String tagName, String apiName, String message, String apiErrorCode) {
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_ADDITIONAL_SERVICES);
        adobeContextData.setFeature(TrackingEvent.ANALYTICS.VAL_FEATURE_MANAGE_RECIPIENTS);
        adobeContextData.setSubFeature(TrackingEvent.ANALYTICS.VAL_SUB_FEATURE_ADD_NEW_RECIPIENT);
        adobeContextData.setStepName(TrackingEvent.ANALYTICS.VAL_STEP_NAME_ADD_RECIPIENT);

        if (ProcessStepSecurity.IDVL.equals(processStepSecurity) || ProcessStepSecurity.ITA.equals(processStepSecurity))
            adobeContextData.setContext8Count();

        mAnalytics.trackFailure(isApiFailure, tagName, apiName, message, apiErrorCode, contextData);
    }
}


