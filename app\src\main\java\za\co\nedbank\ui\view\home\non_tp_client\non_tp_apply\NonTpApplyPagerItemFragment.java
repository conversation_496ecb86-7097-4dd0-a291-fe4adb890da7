/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.non_tp_client.non_tp_apply;

import static za.co.nedbank.core.navigation.NavigationTarget.DISABLE_BANK_CARD;
import static za.co.nedbank.core.navigation.NavigationTarget.LOGIN_DATA;
import static za.co.nedbank.core.navigation.NavigationTarget.POSITION;
import static za.co.nedbank.core.navigation.NavigationTarget.SCALE;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;

import java.lang.ref.WeakReference;

import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.sharedui.listener.IViewPagerChildClickListener;
import za.co.nedbank.core.utils.DeviceUtils;
import za.co.nedbank.databinding.NonTpApplyPagerItemBinding;

public class NonTpApplyPagerItemFragment extends NBBaseFragment {

    private boolean isEnable;
    int position;
    private static WeakReference<IViewPagerChildClickListener> mIControlledViewPagerChildClickListener;

    public static Fragment newInstance(Context context, NonTpCyclicPagerData loginItemData, float scale, boolean isEnabled, int position, WeakReference<IViewPagerChildClickListener> iControlledViewPagerChildClickListener, boolean isEnable) {
        mIControlledViewPagerChildClickListener = iControlledViewPagerChildClickListener;
        Bundle b = new Bundle();
        b.putFloat(SCALE, scale);
        b.putSerializable(LOGIN_DATA, loginItemData);
        b.putBoolean(DISABLE_BANK_CARD,isEnable);
        b.putInt(POSITION, position);
        return Fragment.instantiate(context, NonTpApplyPagerItemFragment.class.getName(), b);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        NonTpApplyPagerItemBinding binding = NonTpApplyPagerItemBinding.inflate(inflater, container, false);
        NonTpCyclicPagerData nonTpApplyCyclicData = (NonTpCyclicPagerData) this.getArguments().getSerializable(LOGIN_DATA);
        float scale = this.getArguments().getFloat(SCALE);
        position = getArguments().getInt(POSITION);
        isEnable = getArguments().getBoolean(DISABLE_BANK_CARD);
        binding.disableView.getLayoutParams().height = (int) getResources().getDimension(za.co.nedbank.enroll_v2.R.dimen.unilateral_list_height);

        binding.itemIcon.getLayoutParams().width = Math.round(DeviceUtils.getDeviceWidth(getActivity()) * .6f);
        ViewGroup.LayoutParams params = binding.llParentNonTpApply.getLayoutParams();
        params.width = Math.round(DeviceUtils.getDeviceWidth(getActivity()) * .6f);
        binding.llParentNonTpApply.setLayoutParams(params);
        binding.viewProduct.setText(nonTpApplyCyclicData.getItemActionLink());

        binding.itemText.setText(nonTpApplyCyclicData.getItemText());
        binding.itemIcon.setImageDrawable(getActivity().getResources().getDrawable(nonTpApplyCyclicData.getItemIcon()));


        binding.clnrContainerNonTpApply.setScaleBoth(scale);


        if (!isEnable && NonTpCyclicPagerData.values()[position] == NonTpCyclicPagerData.BANK) {
            binding.viewProduct.setVisibility(View.INVISIBLE);
            binding.disableView.setVisibility(View.VISIBLE);
        } else {
            binding.viewProduct.setVisibility(View.VISIBLE);
            binding.disableView.setVisibility(View.GONE);
        }

        binding.llParentNonTpApply.setOnClickListener(viewWithClick -> {
            if (mIControlledViewPagerChildClickListener != null && mIControlledViewPagerChildClickListener.get() != null) {
                mIControlledViewPagerChildClickListener.get().onItemClick(position);
            }
        });
        return binding.getRoot();
    }

}