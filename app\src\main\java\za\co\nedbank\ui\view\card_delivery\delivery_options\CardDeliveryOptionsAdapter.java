package za.co.nedbank.ui.view.card_delivery.delivery_options;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import za.co.nedbank.core.view.card_delivery.CardDeliveryOptionsEnum;
import za.co.nedbank.enroll_v2.databinding.ItemCardDeliveryOptionBinding;
import za.co.nedbank.enroll_v2.view.model.card_delivery.CardDeliveryOptionsViewModel;

public class CardDeliveryOptionsAdapter extends RecyclerView.Adapter<CardDeliveryOptionsAdapter.ViewHolder> {


    private final List<CardDeliveryOptionsViewModel> options;
    private OnOptionClickListener optionClickListener;

    public CardDeliveryOptionsAdapter(List<CardDeliveryOptionsViewModel> options) {
        this.options = options;
    }

    @NonNull
    @NotNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull @NotNull ViewGroup parent, int viewType) {
        ItemCardDeliveryOptionBinding binding = ItemCardDeliveryOptionBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull @NotNull ViewHolder holder, int position) {
        holder.bind(options.get(position));
    }

    public void setOptionClickListener(OnOptionClickListener optionClickListener) {
        this.optionClickListener = optionClickListener;
    }

    @Override
    public int getItemCount() {
        return options.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        ItemCardDeliveryOptionBinding binding;

        public ViewHolder(@NonNull ItemCardDeliveryOptionBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }

        public void bind(CardDeliveryOptionsViewModel option) {
            binding.ivIcon.setImageResource(getIcon(option.getOptionCode()));
            binding.tvOptionName.setText(option.getOptionName());
            binding.tvOptionsDescription.setText(option.getOptionDescription());
            setEnabled(option);
            setUpAccessibility(option);
            binding.container.setOnClickListener(v -> onClick());
        }

        private void setEnabled(CardDeliveryOptionsViewModel option) {
            itemView.setAlpha(option.isEnabled() ? 1 : 0.5f);
            itemView.setClickable(option.isEnabled());
        }

        private void setUpAccessibility(CardDeliveryOptionsViewModel option) {
            CardDeliveryOptionsEnum optionEnum = CardDeliveryOptionsEnum.fromValue(option.getOptionCode());
            boolean disableAccessibility = !option.isEnabled() || optionEnum == CardDeliveryOptionsEnum.BRANCH_PICK
                    || optionEnum == CardDeliveryOptionsEnum.UNKNOWN;
            itemView.setImportantForAccessibility(disableAccessibility ? View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS :
                    View.IMPORTANT_FOR_ACCESSIBILITY_YES);
        }

        private int getIcon(String optionCode) {
            return CardDeliveryOptionsEnum.fromValue(optionCode).getIcon();
        }

        void onClick() {
            if (optionClickListener != null) {
                CardDeliveryOptionsViewModel option = options.get(getAdapterPosition());
                if (option.isEnabled()) {
                    optionClickListener.onOptionClicked(option);
                }
            }
        }
    }

    interface OnOptionClickListener {
        void onOptionClicked(CardDeliveryOptionsViewModel selectedOption);
    }
}
