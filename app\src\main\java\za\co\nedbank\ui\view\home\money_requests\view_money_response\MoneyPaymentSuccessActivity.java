/*
* Copyright © 2018 Nedbank. All rights reserved.
*/

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.os.Bundle;

import org.greenrobot.eventbus.EventBus;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.AjoInAppEvent;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.databinding.ActivityMoneyPaymentSuccessBinding;
import za.co.nedbank.payment.pay.view.model.PaymentViewModel;
import za.co.nedbank.ui.di.AppDI;


public class MoneyPaymentSuccessActivity extends NBBaseActivity implements MoneyResponseSuccessView {

    @Inject
    MoneyPaymentSuccessPresenter moneyPaymentSuccessPresenter;
    private ActivityMoneyPaymentSuccessBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), false));
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        binding = ActivityMoneyPaymentSuccessBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        binding.payDoneFinishBtn.setOnClickListener(v -> doneClick());
    }

    @Override
    protected void onResume() {
        super.onResume();
        moneyPaymentSuccessPresenter.bind(this);
        moneyPaymentSuccessPresenter.setPaymentSuccessDetails();
    }

    @Override
    protected void onPause() {
        super.onPause();
        moneyPaymentSuccessPresenter.unbind();
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new AjoInAppEvent(this.getClass(), true));
        super.onDestroy();
    }

    @Override
    public String getReceiverMobile() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_RECEIVER_MOBILE_NUMBER)) {
            return bundle.getString(NavigationTarget.PARAM_RECEIVER_MOBILE_NUMBER);
        }

        return null;
    }

    @Override
    public PaymentViewModel getPaymentViewModel() {
        Bundle bundle = getIntent().getExtras();
        if (bundle != null && bundle.containsKey(NavigationTarget.PARAM_PAYMENT_MODEL)) {
            return bundle.getParcelable(NavigationTarget.PARAM_PAYMENT_MODEL);
        }
        return null;
    }

    @Override
    public void finishScreen() {
        finish();
    }

    @Override
    public void setPaymentSuccessData(PaymentViewModel paymentViewModel, String receiverPhoneNumber) {
        if (paymentViewModel != null) {
            binding.payDoneSuccessDetailText.setText(String.format("%s%s%s%s%s", FormattingUtil.convertToSouthAfricaFormattedCurrency(paymentViewModel.getAmount()), StringUtils.SPACE, getString(R.string.paid_to_pay), StringUtils.SPACE, paymentViewModel.getBeneficiaryName()));
            if (paymentViewModel.getFromAccountViewModel() != null) {
                binding.paymentFromLayout.setDescriptionText(paymentViewModel.getFromAccountViewModel().getDisplayAccountName() == null ?
                        StringUtils.EMPTY_STRING : paymentViewModel.getFromAccountViewModel().getDisplayAccountName());
            }
            binding.paymentYourReferenceLayout.setDescriptionText(paymentViewModel.getUserReference() == null ? StringUtils.EMPTY_STRING : paymentViewModel.getUserReference());
            binding.paymentRecipientReferenceLayout.setDescriptionText(paymentViewModel.getBeneficiaryReference() == null ? StringUtils.EMPTY_STRING : paymentViewModel.getBeneficiaryReference());
            binding.paymentDateLayout.setDescriptionText(FormattingUtil.convertMillisToDisplaySlashDateString(paymentViewModel.getStartDate()));
            binding.paymentTransactionNumber.setDescriptionText(paymentViewModel.getTransactionNumber() == null ? StringUtils.EMPTY_STRING : paymentViewModel.getTransactionNumber());
        }
        if (receiverPhoneNumber != null) {
            binding.paymentRecipientCellphoneTypeLayout.setDescriptionText(receiverPhoneNumber);
        }
    }

    public void doneClick() {
        moneyPaymentSuccessPresenter.navigateToViewMoneyRequestScreen();
    }

    @Override
    public void onBackPressed() {
        moneyPaymentSuccessPresenter.navigateToViewMoneyRequestScreen();
    }
}
