/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.model.money_request;

/**
 * Created by swapnil.gawande on 2/23/2018.
 */

public class MoneyRequestsDataModel {
    private boolean isReminder;
    private boolean isPayLater;
    private long paymentRequestId;
    private boolean isPaid;
    private boolean isReject;
    private String requestDate;
    private String partyName;
    private double requestAmount;
    private String partyPhoneNumber;
    private String partyDescription;
    private String requestStatus;
    private String partyAccountType;
    private String partyAccountNumber;
    private String expiryDate;
    private String currentDate;
    private String processDate;

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getCurrentDate() {
        return currentDate;
    }

    public void setCurrentDate(String currentDate) {
        this.currentDate = currentDate;
    }

    public String getProcessDate() {
        return processDate;
    }

    public void setProcessDate(String processDate) {
        this.processDate = processDate;
    }


    public boolean isReminder() {
        return isReminder;
    }

    public void setReminder(boolean isReminder) {
        this.isReminder = isReminder;
    }


    public boolean isPayLater() {
        return isPayLater;
    }

    public void setPayLater(boolean isPayLater) {
        this.isPayLater = isPayLater;
    }

    public long getPaymentRequestId() {
        return paymentRequestId;
    }

    public void setPaymentRequestId(long paymentRequestId) {
        this.paymentRequestId = paymentRequestId;
    }

    public boolean isPaid() {
        return isPaid;
    }

    public void setPaid(boolean isPaid) {
        this.isPaid = isPaid;
    }

    public boolean isReject() {
        return isReject;
    }

    public void setReject(boolean isReject) {
        this.isReject = isReject;
    }


    public String getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(String requestDate) {
        this.requestDate = requestDate;
    }

    public String getPartyName() {
        return partyName;
    }

    public void setPartyName(String partyName) {
        this.partyName = partyName;
    }

    public double getRequestAmount() {
        return requestAmount;
    }

    public void setRequestAmount(double requestAmount) {
        this.requestAmount = requestAmount;
    }


    public String getPartyPhoneNumber() {
        return partyPhoneNumber;
    }

    public void setPartyPhoneNumber(String partyPhoneNumber) {
        this.partyPhoneNumber = partyPhoneNumber;
    }

    public String getPartyDescription() {
        return partyDescription;
    }

    public void setPartyDescription(String partyDescription) {
        this.partyDescription = partyDescription;
    }


    public String getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(String requestStatus) {
        this.requestStatus = requestStatus;
    }

    public String getPartyAccountType() {
        return partyAccountType;
    }

    public void setPartyAccountType(String partyAccountType) {
        this.partyAccountType = partyAccountType;
    }

    public String getPartyAccountNumber() {
        return partyAccountNumber;
    }

    public void setPartyAccountNumber(String partyAccountNumber) {
        this.partyAccountNumber = partyAccountNumber;
    }
}
