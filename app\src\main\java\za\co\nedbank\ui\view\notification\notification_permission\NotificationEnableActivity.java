package za.co.nedbank.ui.view.notification.notification_permission;

import android.os.Bundle;

import androidx.annotation.Nullable;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.databinding.ActivityNotificatoinEnableBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;

public class NotificationEnableActivity extends NBBaseActivity implements NotificationEnableView {

    @Inject
    NotificationEnablePresenter notificationEnablePresenter;

    @Inject
    @Named("memory")
    ApplicationStorage mMemoryApplicationStorage;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        notificationEnablePresenter.bind(this);
        ActivityNotificatoinEnableBinding binding = ActivityNotificatoinEnableBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setActionListeners();
        notificationEnablePresenter.sendPageLoadAnalytics();
    }

    @Override
    protected void onResume() {
        super.onResume();
        setNotificationEnabledAnalytics();
    }

    private void setNotificationEnabledAnalytics() {
        if (mMemoryApplicationStorage.getBoolean(za.co.nedbank.core.Constants.KEY_CAME_FROM_NOTIFICATION_SETTING_SCREEN,false)) {
            mMemoryApplicationStorage.putBoolean(za.co.nedbank.core.Constants.KEY_CAME_FROM_NOTIFICATION_SETTING_SCREEN,false);
            if (!NotificationUtils.areNotificationsEnabled(this)) {
                notificationEnablePresenter.trackNotificationNotEnabledAction(getString(R.string.analytics_enable_notification_failure));
            } else {
                notificationEnablePresenter.trackNotificationEnabledAction();
            }
            finish();
        }
    }

    private void setActionListeners() {
        findViewById(R.id.btnNegative).setOnClickListener(view -> {
            notificationEnablePresenter.trackActionNotificationNotNowClicked();
            finish();
        });
        findViewById(R.id.btnPositive).setOnClickListener(view -> {
            notificationEnablePresenter.trackActionNotificationEnableClicked();
            notificationEnablePresenter.navigateToNotificationSettings(android.os.Build.VERSION.SDK_INT);
        });
    }

    @Override
    public void onBackPressed() {
        notificationEnablePresenter.trackActionNotificationNotNowClicked();
        super.onBackPressed();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        notificationEnablePresenter.unbind();
    }
}
