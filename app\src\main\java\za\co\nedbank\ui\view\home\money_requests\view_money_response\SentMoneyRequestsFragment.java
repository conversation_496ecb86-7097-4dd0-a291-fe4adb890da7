/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.utils.NBAnimationUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.FragmentSentMoneyRequestsLayoutBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsAdapterModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsViewModel;

/**
 * Created by swapnil.gawande on 2/20/2018.
 */

public class SentMoneyRequestsFragment extends NBBaseFragment implements SentMoneyRequestsView, SentMoneyRequestsRecyclerAdapter.ISentMoneyRequestsRecyclerListener {

    @Inject
    SentMoneyRequestsPresenter sentMoneyRequestsPresenter;

    private SentMoneyRequestsRecyclerAdapter sentMoneyRequestsRecyclerAdapter;
    private FragmentSentMoneyRequestsLayoutBinding binding;

    public static SentMoneyRequestsFragment newInstance() {
        return new SentMoneyRequestsFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getFragmentComponent(this).inject(this);
    }

    @Override
    public View onCreateView(@NonNull final LayoutInflater inflater, @Nullable final ViewGroup container, @Nullable final Bundle savedInstanceState) {
        binding = FragmentSentMoneyRequestsLayoutBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(final View view, @Nullable final Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

    }

    @Override
    public void onResume() {
        super.onResume();
        sentMoneyRequestsPresenter.bind(this);
        sentMoneyRequestsPresenter.getMoneyRequests();
    }

    @Override
    public void onPause() {
        super.onPause();
        sentMoneyRequestsPresenter.unbind();
    }

    @Override
    public void onRemindClicked(MoneyRequestsViewModel moneyRequestsViewModel) {
        sentMoneyRequestsPresenter.onRemindClicked(moneyRequestsViewModel);
    }

    @Override
    public void showLoadingView() {
        ViewUtils.showViews(binding.pbRecyclerViewLoading);
    }

    @Override
    public void hideLoadingView() {
        ViewUtils.hideViews(binding.pbRecyclerViewLoading);
    }

    @Override
    public void showEmptyListIndicator() {
        NBAnimationUtils.fadeIn(binding.llEmptySentMoneyRequests);
    }

    @Override
    public void showMoneyRequests(List<MoneyRequestsAdapterModel> moneyRequestsAdapterModels) {
        if (moneyRequestsAdapterModels != null && moneyRequestsAdapterModels.size() > 0) {
            initAdapter();
            sentMoneyRequestsRecyclerAdapter.setMoneyRequestsAdapterModels(moneyRequestsAdapterModels);

        }
    }

    @Override
    public void updateMoneyRequests() {
        sentMoneyRequestsPresenter.getMoneyRequests();
    }

    @Override
    public void showError(String error) {
        showError(getString(R.string.error), error);
    }

    private void initAdapter() {
        sentMoneyRequestsRecyclerAdapter = new SentMoneyRequestsRecyclerAdapter(getActivity(), this);
        binding.recyclerViewSentMoneyRequests.setAdapter(sentMoneyRequestsRecyclerAdapter);
    }
}
