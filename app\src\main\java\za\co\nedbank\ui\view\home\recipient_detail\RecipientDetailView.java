/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.recipient_detail;

import za.co.nedbank.core.view.recipient.RecipientViewModel;
import za.co.nedbank.ui.view.home.add_recipient.BaseRecipientView;

/**
 * Created by priyadhingra on 9/12/2017.
 */

interface RecipientDetailView extends BaseRecipientView {

    void onRecipientDetailFetched(RecipientViewModel recipientViewModel);
    void showGetSingleRecipientApiError(String... message);
    void showProgressBar();
    void finishOnBack();
}
