package za.co.nedbank.ui.view.retention.notification_journey;

import za.co.nedbank.core.base.NBBaseView;

public interface RetentionNotificationJourneyView extends NBBaseView {
    void showError();
    void setVisibilityCompTask();
    void setVisibilityTodoLabel();
    void setShareAccountCompDivider();
    void setSecurityCompDivider();
    void setDebitCompDivider();
    void setVisibilityProfileGroupComp();
    void setProfileLimitHeadingComp();
    void setShareDividerTodo();
    void setSecurityDividerTodo();
    void setDebitDividerTodo();
    void setProfileGroupTodo();
    void setShareDividerComp();
    void setDebitGroupComp();
    void setDebitCompHeading();
    void setDebitGroupTodo();
    void setSecurityGroupComp();
    void setSecurityHeadingComp();
    void setSecurityGroupTodo();
    void setShareGroupComp();
    void setShareHeadingComp();
    void setShareGroupTodo();
}
