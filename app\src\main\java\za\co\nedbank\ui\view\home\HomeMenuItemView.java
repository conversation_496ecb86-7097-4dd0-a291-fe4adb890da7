/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import za.co.nedbank.R;
import za.co.nedbank.databinding.ViewHomeMenuItemBinding;

/**
 * Created by ch<PERSON>rani on 29-06-2017.
 */

public class HomeMenuItemView extends LinearLayout {

    private int mItemTextColor, mItemImageResourceId;
    private String mItemText;
    private boolean mSelected;
    private ViewHomeMenuItemBinding binding;

    public HomeMenuItemView(Context context) {
        super(context);
        inflateLayout();
    }

    public HomeMenuItemView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.HomeMenuItemView, 0, 0);
        try {
            mItemText = a.getString(R.styleable.HomeMenuItemView_menuText);
            mItemTextColor = a.getResourceId(R.styleable.HomeMenuItemView_menuTextColor, 0);
            mItemImageResourceId = a.getResourceId(R.styleable.HomeMenuItemView_imageSrc, 0);
            mSelected = a.getBoolean(R.styleable.HomeMenuItemView_enabled, false);
        } finally {
            a.recycle();
        }
        inflateLayout();
    }

    public HomeMenuItemView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        inflateLayout();
    }

    private void inflateLayout() {
        binding = ViewHomeMenuItemBinding.inflate(LayoutInflater.from(getContext()), this, true);
        binding.tvHomeMenuItem.setSelected(mSelected);
        binding.tvHomeMenuItem.setText(mItemText);
        binding.tvHomeMenuItem.setTextColor(ContextCompat.getColorStateList(getContext(), mItemTextColor));
        binding.imvHomeMenuItem.setImageDrawable(ContextCompat.getDrawable(getContext(), mItemImageResourceId));
        binding.imvHomeMenuItem.setSelected(mSelected);
    }

    @Override
    public void setSelected(boolean selected) {
        binding.imvHomeMenuItem.setSelected(selected);
        binding.tvHomeMenuItem.setSelected(selected);
        binding.tvHomeMenuItem.setTypeface(selected ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
    }

    public void setIconSelectorDrawable(int selectorDrawableId){
        mItemImageResourceId = selectorDrawableId;
        binding.imvHomeMenuItem.setImageDrawable(ContextCompat.getDrawable(getContext(), mItemImageResourceId));
    }

    public void setTextColorSelector(int colorSelector){
        mItemTextColor = colorSelector;
        binding.tvHomeMenuItem.setTextColor(ContextCompat.getColorStateList(getContext(), mItemTextColor));
    }

    public void setNotificationDotVisibility(boolean visibility){
        binding.imvHomeMenuNotiDot.setVisibility(visibility ? View.VISIBLE : View.GONE);
    }

    public void setText(String text)
    {
        binding.tvHomeMenuItem.setText(text);
    }
}
