package za.co.nedbank.ui.domain.repository.pop;

import io.reactivex.Observable;
import za.co.nedbank.ui.domain.model.pop.TransactionHistoryParentData;

public interface TransactionRepository {
    Observable<TransactionHistoryParentData> getTransactionHistory(int contactCardId,
                                                                   String pageSize,
                                                                   String page,
                                                                   String startDate,
                                                                   String endDate);
}
