package za.co.nedbank.ui.view.pop;

import java.util.Map;

import za.co.nedbank.core.base.NBBaseView;

public interface NewShareProofOfPaymentView extends NBBaseView {
    void setResult(Map<String, Object> params);

    String getSelectedSharePOPMethod();

    void setSharedButtonEnabled(boolean isEnabled);

    void sharePOPFailure();

    void trackFailure(boolean isApiFailure, String errorCode);

    void sharePOPSuccess();

    void showLoading(boolean isLoading);

    boolean isFromRecipientHistory();
}
