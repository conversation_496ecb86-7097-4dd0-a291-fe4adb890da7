/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.utils.NBAnimationUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.FragmentReceivedMoneyRequestsLayoutBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsAdapterModel;
import za.co.nedbank.ui.domain.model.money_request.MoneyRequestsViewModel;

/**
 * Created by swapnil.gawande on 2/20/2018.
 */

public class ReceivedMoneyRequestsFragment extends NBBaseFragment implements ReceivedMoneyRequestsView, ReceivedMoneyRequestsRecyclerAdapter.IReceivedMoneyRequestsRecyclerListener {

    @Inject
    ReceivedMoneyRequestsPresenter receivedMoneyRequestsPresenter;

    private ReceivedMoneyRequestsRecyclerAdapter receivedMoneyRequestsRecyclerAdapter;
    private FragmentReceivedMoneyRequestsLayoutBinding binding;

    public static ReceivedMoneyRequestsFragment newInstance() {
        return new ReceivedMoneyRequestsFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        AppDI.getFragmentComponent(this).inject(this);
    }

    @Override
    public View onCreateView(@NonNull final LayoutInflater inflater, @Nullable final ViewGroup container, @Nullable final Bundle savedInstanceState) {
        binding = FragmentReceivedMoneyRequestsLayoutBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(final View view, @Nullable final Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

    }

    @Override
    public void onResume() {
        super.onResume();
        receivedMoneyRequestsPresenter.bind(this);
        receivedMoneyRequestsPresenter.getMoneyRequests();
    }

    @Override
    public void onPause() {
        super.onPause();
        receivedMoneyRequestsPresenter.unbind();
    }

    @Override
    public void onRejectClicked(MoneyRequestsViewModel moneyRequestsViewModel) {
        receivedMoneyRequestsPresenter.onRejectClicked(moneyRequestsViewModel);
    }

    @Override
    public void onPayLaterClicked(MoneyRequestsViewModel moneyRequestsViewModel) {
        receivedMoneyRequestsPresenter.onPayLaterClicked(moneyRequestsViewModel);
    }

    @Override
    public void onPayNowClicked(MoneyRequestsViewModel moneyRequestsViewModel) {
        receivedMoneyRequestsPresenter.onPayNowClicked(moneyRequestsViewModel);
    }

    @Override
    public void showLoadingView() {
        ViewUtils.showViews(binding.pbRecyclerViewLoading);
    }

    @Override
    public void hideLoadingView() {
        ViewUtils.hideViews(binding.pbRecyclerViewLoading);
    }

    @Override
    public void showEmptyListIndicator() {
        NBAnimationUtils.fadeIn(binding.llEmptyReceivedMoneyRequests);
    }

    @Override
    public void showMoneyRequests(List<MoneyRequestsAdapterModel> moneyRequestsAdapterModels) {
        if(moneyRequestsAdapterModels!=null && moneyRequestsAdapterModels.size()>0) {
            initAdapter();
            receivedMoneyRequestsRecyclerAdapter.setMoneyRequestsAdapterModels(moneyRequestsAdapterModels);

        }
    }

    @Override
    public void updateMoneyRequests() {
        receivedMoneyRequestsPresenter.getMoneyRequests();
    }

    @Override
    public void showError(String error) {
        showError(getString(R.string.error), error);
    }

    private void initAdapter() {
        receivedMoneyRequestsRecyclerAdapter = new ReceivedMoneyRequestsRecyclerAdapter(getActivity(), this);
        binding.recyclerViewReceivedMoneyRequests.setAdapter(receivedMoneyRequestsRecyclerAdapter);
    }
}
