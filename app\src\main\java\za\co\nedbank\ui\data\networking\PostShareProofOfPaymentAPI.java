/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.networking;

import java.util.List;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import za.co.nedbank.ui.data.entity.pop.SharePOPRecipientsRequestEntity;
import za.co.nedbank.ui.data.entity.pop.ShareProofOfPaymentRequestEntity;
import za.co.nedbank.ui.data.entity.pop.ShareProofOfPaymentResponseEntity;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 10/18/2019.
 */

public interface PostShareProofOfPaymentAPI {

    @POST("payments/v3/transactions/{contractID}/notifications")
    Observable<ShareProofOfPaymentResponseEntity> postShareProofOfPayment(@Body List<ShareProofOfPaymentRequestEntity> shareProofOfPaymentRequestEntityList, @Path("contractID") String contractID);

    @POST("contactcards/v1/contactcards/transactions/{transactionId}/notifications")
    Observable<ShareProofOfPaymentResponseEntity> postShareProofOfPaymentRecipients(@Body SharePOPRecipientsRequestEntity shareProofOfPaymentRequestEntityList, @Path("transactionId") String transactionId);

    @PUT("payments/v3/transactions/{transactionId}/notificationdetails")
    Observable<ShareProofOfPaymentResponseEntity> postShareProofOfPaymentRecipientsForSchedulePayment(@Body SharePOPRecipientsRequestEntity shareProofOfPaymentRequestEntityList, @Path("transactionId") String transactionId);
}
