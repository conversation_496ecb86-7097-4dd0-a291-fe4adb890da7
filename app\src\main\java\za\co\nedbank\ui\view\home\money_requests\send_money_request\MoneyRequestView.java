/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.send_money_request;

import java.util.Map;

import za.co.nedbank.core.base.NBBaseView;


public interface MoneyRequestView extends NBBaseView {

    void setResult(Map<String, Object> params);

    void setNextButtonEnabled(boolean enabled);

    void handleEnterRecipientName();

    void handleEnterMobileNumber();

    void showErrorMessage(String message);

    void navigateToRequestDetailOnSuccess();

    void handleNextClick();

    String getRecipientName();

    String getRecipientMobileNumber();

    String getRecipientNumberWithoutZero(String recipientContactNumber);

    void showLoadingOnButton(boolean inProgress);

    void setCountryCodeVisibility(boolean shouldVisible);
}