/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.notification.transaction_notification.inbox;


import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Vibrator;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.adapter.BaseSectionedAdapter;
import za.co.nedbank.core.base.adapter.Section;
import za.co.nedbank.core.base.adapter.SectionAdapterItem;
import za.co.nedbank.core.databinding.ItemTransactionSectionBinding;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.databinding.ItemTransactionRowBinding;
import za.co.nedbank.payment.databinding.TaxCertificateActiveAccountBinding;
import za.co.nedbank.ui.notifications.utils.NotificationUtils;
import za.co.nedbank.ui.view.notification.notification_messages.MultipleSelectionHandler;

public class TransactionsInboxAdapter extends BaseSectionedAdapter<FBTransactionNotificationsViewModel> implements MultipleSelectionHandler.OnSelectionChangeListener {

    private final boolean isAdvancedSearch;
    private WeakReference<TransactionInboxRowInterface> transactionRowInterface;
    private Map<Integer, SectionAdapterItem<FBTransactionNotificationsViewModel>> mItemsToBeDeleted;
    private int expandIndex = -1;
    private int notificationID = -1;
    private MultipleSelectionHandler mMultipleSelectionHandler;
    private MultipleSelectionHandler.OnSelectionChangeListener mOnSelectionChangedListener;
    private TransactionsInboxAdapter.OnMessageItemClickListener mOnMessageItemClickListener;
    private Vibrator vibe;
    private Context mContext;
    private String sortParameter = null;

    public TransactionsInboxAdapter(final Context context, boolean isAdvancedSearch) {
        super(context);
        mMultipleSelectionHandler = new MultipleSelectionHandler(this);
        setUseLoadingMore(false);
        setLoadMoreEnabled(false);
        setResetPagingOnSwap(false);
        this.isAdvancedSearch = isAdvancedSearch;
        vibe = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
        mContext = context;
    }

    @Override
    public List<Section<FBTransactionNotificationsViewModel>> getSections(List<FBTransactionNotificationsViewModel> items) {
        List<Section<FBTransactionNotificationsViewModel>> sections = new ArrayList<>();
        String type = getSectionType();
        if (type == null || type.length() == 0) {
            sections = prepareMostRecentSections(items);
        } else {
            sections.clear();
            switch (type) {
                case Constants.FilterTypes.MOST_RECENT_DATE:
                    sections.addAll(prepareMostRecentSections(items));
                    break;
                case Constants.FilterTypes.OLDEST_DATE:
                    sections.addAll(prepareOldestSections(items));
                    break;
                case Constants.FilterTypes.LOWEST_AMOUNT:
                    sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.transaction_filter_label_lowest_amount)) {
                        @Override
                        public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                            return true;
                        }
                    });
                    break;
               /* case Constants.FilterTypes.MONEY_OUT:
                    sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_sort_up)) {
                        @Overridel
                        public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                            return true;
                        }
                    });
                    break;*/
                //case Constants.FilterTypes.OLDEST_DATE:
                case Constants.FilterTypes.HIGHEST_AMOUNT:
                    sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.transaction_filter_label_highest_amount)) {
                        @Override
                        public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                            return true;
                        }
                    });
                    break;
                /*case Constants.FilterTypes.MONEY_IN:
                    sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_sort_down)) {
                        @Override
                        public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                            return true;
                        }
                    });
                    break;*/

                /*case Constants.FilterTypes.READ_UNREAD:
                    sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_UNREAD)) {
                        @Override
                        public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                            return !model.isRead();
                        }
                    });
                    sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_read)) {
                        @Override
                        public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                            return model.isRead();
                        }
                    });
                    break;

                case Constants.FilterTypes.TRANSACTION_TYPE:

                    Set<String> tranTypeList = new HashSet<>();
                    for (FBTransactionNotificationsViewModel viewModel : items) {
                        tranTypeList.add(viewModel.getMetaTransType());
                    }

                    for (String transactioType : tranTypeList) {
                        sections.add(new Section<FBTransactionNotificationsViewModel>(transactioType) {
                            @Override
                            public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                                return model.getMetaTransType().equals(transactioType);
                            }
                        });
                    }

                    break;

                case Constants.FilterTypes.ACC_NUMBER:

                    Set<String> accNumberList = new HashSet<>();
                    for (FBTransactionNotificationsViewModel viewModel : items) {
                        accNumberList.add(viewModel.getMetaAccNumber());
                    }

                    for (String accNumberModel : accNumberList) {
                        sections.add(new Section<FBTransactionNotificationsViewModel>(accNumberModel) {
                            @Override
                            public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                                return model.getMetaAccNumber().equals(accNumberModel);
                            }
                        });
                    }
                    break;

                case Constants.FilterTypes.CARD_NUMBER:

                    Set<String> cardNumberList = new HashSet<>();
                    for (FBTransactionNotificationsViewModel viewModel : items) {
                        cardNumberList.add(viewModel.getMetaCardnumber());
                    }

                    for (String cardNumberModel : cardNumberList) {
                        sections.add(new Section<FBTransactionNotificationsViewModel>(getDisplayCardNumber(cardNumberModel)) {
                            @Override
                            public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                                return model.getMetaCardnumber().equals(cardNumberModel);
                            }
                        });
                    }
                    break;


                default:
                    break;*/
            }

        }
        return sections;
    }

    private String getSectionType() {
        return sortParameter;
    }

    @Override
    protected RecyclerView.ViewHolder onCreateContentViewHolder(final ViewGroup parent, final int viewType) {
        ItemTransactionRowBinding binding = ItemTransactionRowBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new TransactionInboxViewHolder(binding, this, mMultipleSelectionHandler, mOnMessageItemClickListener, vibe);
    }

    @Override
    protected RecyclerView.ViewHolder onCreateSectionViewHolder(final ViewGroup parent, final int viewType) {
        ItemTransactionSectionBinding binding = ItemTransactionSectionBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        if (isAdvancedSearch) {
            ViewUtils.hideViews(binding.getRoot());
        } else {
            ViewUtils.showViews(binding.getRoot());
        }
        return new SectionViewHolder(binding);
    }

    @Override
    protected void onBindContentViewHolder(final RecyclerView.ViewHolder holder, final SectionAdapterItem<FBTransactionNotificationsViewModel> model, final int position) {
        ((TransactionInboxViewHolder) holder).setup(transactionRowInterface, model.getContent(), position, isLastItemInSection(position));
    }

    @Override
    protected void onBindSectionViewHolder(final RecyclerView.ViewHolder holder, final SectionAdapterItem<FBTransactionNotificationsViewModel> model, final int position) {
        ((SectionViewHolder) holder).setup(model.getSection());
    }

    public void setTransactionRowInterface(final TransactionInboxRowInterface transactionRowInterface) {
        this.transactionRowInterface = new WeakReference<>(transactionRowInterface);
    }

    private List<Section<FBTransactionNotificationsViewModel>> prepareOldestSections(final List<FBTransactionNotificationsViewModel> transactions) {
        List<Section<FBTransactionNotificationsViewModel>> sections = new ArrayList<>();
        if (transactions == null || transactions.isEmpty()) {
            return sections;
        }

        sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_older)) {
            @Override
            public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                return NotificationUtils.isOlderThanYesterday(model.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            }
        });
        sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_yesterday)) {
            @Override
            public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                return NotificationUtils.isYesterday(model.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            }
        });

        sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_today)) {
            @Override
            public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                return NotificationUtils.isToday(model.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            }
        });


        return sections;
    }
    private List<Section<FBTransactionNotificationsViewModel>> prepareMostRecentSections(final List<FBTransactionNotificationsViewModel> transactions) {
        List<Section<FBTransactionNotificationsViewModel>> sections = new ArrayList<>();
        if (transactions == null || transactions.isEmpty()) {
            return sections;
        }
        sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_today)) {
            @Override
            public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                return NotificationUtils.isToday(model.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            }
        });

        sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_yesterday)) {
            @Override
            public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                return NotificationUtils.isYesterday(model.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            }
        });

        sections.add(new Section<FBTransactionNotificationsViewModel>(mContext.getString(R.string.notification_section_label_older)) {
            @Override
            public boolean fitSectionCriteria(FBTransactionNotificationsViewModel model) {
                return NotificationUtils.isOlderThanYesterday(model.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS);
            }
        });


        return sections;
    }

    public int getExpandIndex() {
        return expandIndex;
    }

    public void setExpandIndex(int expandIndex) {
        this.expandIndex = expandIndex;
    }

    public void setSelected(int notificationID) {
        this.notificationID = notificationID;
    }

    public int getSelectedNotificationID() {
        return notificationID;
    }

    private long getMinimumTransactionDate(final List<FBTransactionNotificationsViewModel> transactions) {
        long minDate = transactions.get(0).getDate();

        for (FBTransactionNotificationsViewModel transaction : transactions) {
            if (transaction.getDate() <= minDate) {
                minDate = transaction.getDate();
            }
        }
        return minDate;
    }

    @SuppressLint("UseSparseArrays")
    void removeItemAt(SectionAdapterItem<FBTransactionNotificationsViewModel> viewModel) {

        int position = items.indexOf(viewModel);
        if (position >= 0) {
            if (mItemsToBeDeleted == null) {
                mItemsToBeDeleted = new TreeMap<>();
            } else {
                mItemsToBeDeleted.clear();
            }
            mItemsToBeDeleted.put(position, items.get(position));
            items.remove(position);
            notifyItemRemoved(position);
        }
    }

    void deleteSelectedItems() {
        List<Integer> selectedPositions = mMultipleSelectionHandler.getSelectedPositions();
        if (mItemsToBeDeleted == null) {
            mItemsToBeDeleted = new TreeMap<>();
        } else {
            mItemsToBeDeleted.clear();
        }

        for (Integer pos :
                selectedPositions) {
            mItemsToBeDeleted.put(pos, items.get(pos));
        }

        for (Integer key : mItemsToBeDeleted.keySet()) {

            SectionAdapterItem<FBTransactionNotificationsViewModel> viewModel = mItemsToBeDeleted.get(key);

            int deletePos = items.indexOf(viewModel);
            items.remove(viewModel);
            notifyItemRemoved(deletePos);
        }

    }

    public void restoreDeletedItems() {
        if (mItemsToBeDeleted != null) {
            for (Integer originalPositions :
                    mItemsToBeDeleted.keySet()) {
                SectionAdapterItem<FBTransactionNotificationsViewModel> item = mItemsToBeDeleted.get(originalPositions);
                if (items.size() <= originalPositions) {
                    items.add(item);
                } else {
                    items.add(originalPositions, item);
                }
                notifyItemInserted(originalPositions);
            }
        }

    }

    List<SectionAdapterItem<FBTransactionNotificationsViewModel>> recentDeletedItems() {
        return mItemsToBeDeleted != null ? new ArrayList<SectionAdapterItem<FBTransactionNotificationsViewModel>>(mItemsToBeDeleted.values()) : null;
    }

    public SectionAdapterItem<FBTransactionNotificationsViewModel> getItemAt(int position) {
        return items.get(position);
    }

    boolean isAllSelected() {
        if (!items.isEmpty()) {
            return items.size() == mMultipleSelectionHandler.getSelectedPositions().size();
        }
        return false;
    }

    @Override
    public void onSelectionStarted() {
        if (mOnSelectionChangedListener != null) {
            mOnSelectionChangedListener.onSelectionStarted();
        }
        notifyDataSetChanged();
    }

    public List<SectionAdapterItem<FBTransactionNotificationsViewModel>> getSelectedItems() {
        List<SectionAdapterItem<FBTransactionNotificationsViewModel>> selectedItems = new ArrayList<>();
        List<Integer> selectedPositions = mMultipleSelectionHandler.getSelectedPositions();
        for (Integer pos :
                selectedPositions) {
            selectedItems.add(items.get(pos));
        }
        return selectedItems;
    }


    @Override
    public void onSelectionChanged(List<Integer> selectedItems) {
        mOnSelectionChangedListener.onSelectionChanged(selectedItems);
    }

    void setOnSelectionChangedListener(MultipleSelectionHandler.OnSelectionChangeListener
                                               onSelectionChangedListener) {
        mOnSelectionChangedListener = onSelectionChangedListener;
    }

    public void setOnMessageItemClickListener(TransactionsInboxAdapter.OnMessageItemClickListener onMessageItemClickListener) {
        this.mOnMessageItemClickListener = onMessageItemClickListener;
    }

    public void clearSelections() {
        mMultipleSelectionHandler.clearSelections();
        notifyDataSetChanged();
    }

    public void selectAll(boolean selectAll) {
        mMultipleSelectionHandler.selectAll(selectAll, items.size());
        notifyDataSetChanged();
    }

    public void swapSectionedData(List<FBTransactionNotificationsViewModel> transactions, String sortParameter) {
        this.sortParameter = sortParameter;
        swapSectionedData(transactions);
    }

    public interface OnMessageItemClickListener {
        void onMessageItemClick(SectionAdapterItem<FBTransactionNotificationsViewModel> fbTransactionNotificationsViewModelSectionAdapterItem, int pos);
    }

    //Simple card number convert into masking card number  like (1234 **** **** 6789)
    private String getDisplayCardNumber(final String number) {
        if (number != null && number.trim().length() > 4) {
            String trimmed = number.trim();
            String obfuscated = "**** **** " + trimmed.substring(trimmed.length() - 4, trimmed.length());
            return obfuscated;
        }
        return number;
    }
}
