/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.payme_tab;

import android.content.Context;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.sharedui.ui.NBCardWidget;
import za.co.nedbank.databinding.WidgetPaymeTabBinding;
import za.co.nedbank.services.Constants;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.uisdk.component.CompatTextView;


public class PayMeTabWidget extends NBCardWidget implements View.OnTouchListener, NBBaseView {

    private final WidgetPaymeTabBinding binding;
    @Inject
    PayMeTabPresenter mPresenter;

    private OnTabSelectListener mTabSelectListener;

    public interface OnTabSelectListener {
        public void onTabSelected(TabLayout.Tab tab);
    }

    public PayMeTabWidget(final Context context) {
        this(context, null);
    }

    public PayMeTabWidget(final Context context, @Nullable final AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public void setTabSelectListener(OnTabSelectListener tabSelectListener){
        this.mTabSelectListener = tabSelectListener;
    }

    public PayMeTabWidget(final Context context, @Nullable final AttributeSet attrs, final int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        binding = WidgetPaymeTabBinding.inflate(LayoutInflater.from(getContext()), this, true);

        if (getContext() instanceof NBBaseActivity) {
            AppDI.getActivityComponent((NBBaseActivity) getContext()).inject(this);
        } else {
            throw new ClassCastException();
        }
        binding.viewpager.setOnTouchListener(this);
        binding.viewpager.setSwipeEnabled(false);
        setupViewPager(binding.viewpager);
        binding.dashboardTabs.addOnTabSelectedListener(new PayMeTabWidget.PayMeTabListener());
        binding.dashboardTabs.setupWithViewPager(binding.viewpager);
        makeTabsCustomView();


    }

    @Override
    public void setTitle(String title) {
        //title not required

    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        mPresenter.bind(this);
    }

    private void setupViewPager(ViewPager viewPager) {
        DashboardPagerAdapter dashboardPagerAdapter = new DashboardPagerAdapter(((NBBaseActivity) getContext()).getSupportFragmentManager());
        dashboardPagerAdapter.addFragment(new MyRecipientsTabFragment(), getContext().getString(R.string.quick_pay));
        dashboardPagerAdapter.addFragment(new MoneyRequestsFragment(), getContext().getString(R.string.pay_me));
        viewPager.setAdapter(dashboardPagerAdapter);
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        return true;
    }


    private class PayMeTabListener
            implements TabLayout.OnTabSelectedListener {

        @Override
        public void onTabSelected(final TabLayout.Tab tab) {
            collapseAppBarLayout(tab);
            makeTabTypefaceNormal(tab, false);
        }

        @Override
        public void onTabUnselected(final TabLayout.Tab tab) {
            makeTabTypefaceNormal(tab, true);
        }

        @Override
        public void onTabReselected(final TabLayout.Tab tab) {
            collapseAppBarLayout(tab);
            makeTabTypefaceNormal(tab, false);
        }

        private void makeTabTypefaceNormal(final TabLayout.Tab tab,
                                           final boolean isUnselected) {
            View tabContainer;
            View tabTextView;
            if (tab == null
                    || ((tabContainer = tab.getCustomView()) == null)
                    || ((tabTextView = tabContainer.findViewById(android.R.id.text1)) == null)) {
                return;
            }
            final TextView textView = (TextView) tabTextView;

            textView.setTypeface(Typeface.SANS_SERIF,
                    isUnselected ? Typeface.NORMAL : Typeface.BOLD);
        }
    }

    private void makeTabsCustomView() {
        // Iterate over all tabs and set the custom view
        CompatTextView quickPayTextView = (CompatTextView) LayoutInflater.from(getContext()).inflate(R.layout.custom_dasboard_tab, null);
        quickPayTextView.setText(getContext().getString(R.string.quick_pay));

        CompatTextView nbPayMeTextView = (CompatTextView) LayoutInflater.from(getContext()).inflate(R.layout.custom_dasboard_tab, null);
        nbPayMeTextView.setText(getContext().getString(R.string.pay_me));
        binding.dashboardTabs.getTabAt(Constants.ZERO).setCustomView(quickPayTextView);
        binding.dashboardTabs.getTabAt(Constants.ONE).setCustomView(nbPayMeTextView);
        if (binding.dashboardTabs.getTabAt(0) != null) {
            binding.dashboardTabs.getTabAt(0).select();
        }
    }

    private void collapseAppBarLayout(TabLayout.Tab tab){
        if(mTabSelectListener != null){
            mTabSelectListener.onTabSelected(tab);
        }

    }
}

