package za.co.nedbank.ui.view.notification.notification_preferences.all_accounts_preferences;


import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.sharedui.listener.IListItemClickListener;
import za.co.nedbank.core.view.fbnotifications.AccountPreference;
import za.co.nedbank.databinding.AccountPreferenceCellviewBinding;


public class AllAccountsPreferenceRecyclerViewAdapter extends RecyclerView.Adapter<AllAccountsPreferenceRecyclerViewAdapter.AllAccountsPrefViewHolder> {

    private List<AccountPreference> mAccountPreferenceList;
    private IListItemClickListener<Integer> mListItemClickListener;

    public AllAccountsPreferenceRecyclerViewAdapter(List<AccountPreference> accountPreferenceList,
                                                    IListItemClickListener<Integer> listItemClickListener) {
        this.mAccountPreferenceList = accountPreferenceList;
        this.mListItemClickListener = listItemClickListener;
    }

    @Override
    public AllAccountsPrefViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        AccountPreferenceCellviewBinding binding = AccountPreferenceCellviewBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new AllAccountsPrefViewHolder(binding, mListItemClickListener);
    }

    @Override
    public void onBindViewHolder(AllAccountsPrefViewHolder holder, int position) {
        AccountPreference accountPreference = mAccountPreferenceList.get(position);
        if (accountPreference != null) {
            holder.accountName.setText(accountPreference.getAccountType().getAccountType());
            if (accountPreference.getAccountStatusCode() != null &&
                    accountPreference.getAccountStatusCode().equals(Constants.PENDING_ACT_ACCOUNT_STATUS_CODE)) {
                holder.accountNumber.setText(holder.itemView.getContext().getResources().getString(R.string.pending_activation));
            } else {
                holder.accountNumber.setText(accountPreference.getAccountNumber());
            }
        }
        holder.rootLayout.setOnClickListener(v -> holder.onItemClick());
    }

    @Override
    public int getItemCount() {
        return mAccountPreferenceList != null
                ? mAccountPreferenceList.size() : 0;
    }

    static class AllAccountsPrefViewHolder extends RecyclerView.ViewHolder {
        TextView accountName;
        TextView accountNumber;
        ConstraintLayout rootLayout;

        private IListItemClickListener<Integer> mListItemClickListener;

        AllAccountsPrefViewHolder(AccountPreferenceCellviewBinding binding,
                                  IListItemClickListener<Integer> listItemClickListener) {
            super(binding.getRoot());
            mListItemClickListener = listItemClickListener;
            accountName = binding.tvAccountName;
            accountNumber = binding.tvAccountNumber;
            rootLayout = binding.allAccountsPrefsRootLayout;
        }

        void onItemClick() {
            mListItemClickListener.onItemClick(getAdapterPosition());
        }
    }

    List<AccountPreference> getAllAccountsPreferenceList() {
        return mAccountPreferenceList;
    }
}

