package za.co.nedbank.ui.view.error;

import android.os.Bundle;

import androidx.core.text.HtmlCompat;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.databinding.ActivityGenericTechnicalErrorBinding;

public class GenericTechnicalErrorActivity extends NBBaseActivity implements NBBaseView {

    //Default screen name
    String screenName = NavigationTarget.GENERIC_TECHNICAL_ERROR_SCREEN;
    private ActivityGenericTechnicalErrorBinding binding;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityGenericTechnicalErrorBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        setUI();
    }

    private void setUI(){
        // Pass screenName from source and customize UI accordingly
        if (NavigationTarget.GENERIC_TECHNICAL_ERROR_SCREEN.equals(screenName)) {
            binding.errorHeaderTxt.setText(HtmlCompat.fromHtml(getString(R.string.technical_error_description), HtmlCompat.FROM_HTML_MODE_LEGACY));
        }
        binding.btnBack.setOnClickListener(v -> onClickBack());
    }

    void onClickBack() {
        finish();
    }
}
