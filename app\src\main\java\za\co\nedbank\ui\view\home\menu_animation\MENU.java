/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.menu_animation;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 03/08/17.
 */

public enum MENU {
    TRANSFER(2, 20, 30, 50), PAY(1, 30, 90, 100), BUY(0, 90, 150, 100);

    private final int childPosition;
    private final float startAngle;
    private final float endAngle;
    private final long animateDuration;

    MENU(int childPosition, float startAngle, float endAngle, long animateDuration) {
        this.childPosition = childPosition;
        this.startAngle = startAngle;
        this.endAngle = endAngle;
        this.animateDuration = animateDuration;
    }

    public int getChildPosition() {
        return childPosition;
    }

    public float getStartAngle() {
        return startAngle;
    }

    public float getEndAngle() {
        return endAngle;
    }

    public long getAnimateDuration() {
        return animateDuration;
    }
}
