/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.notification.transaction_notification.inbox;


import android.content.Context;
import android.graphics.Typeface;
import android.os.Vibrator;
import android.view.HapticFeedbackConstants;
import android.view.MotionEvent;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import java.lang.ref.WeakReference;
import java.util.List;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.databinding.ItemTransactionRowBinding;
import za.co.nedbank.ui.view.notification.notification_messages.MultipleSelectionHandler;

public class TransactionInboxViewHolder extends RecyclerView.ViewHolder implements MultipleSelectionHandler.SelectableViewHolder {

    private TransactionsInboxAdapter.OnMessageItemClickListener onMessageItemClickListener;
    private TransactionsInboxAdapter adapter;
    private WeakReference<TransactionInboxRowInterface> transactionRowInterface;
    private FBTransactionNotificationsViewModel model;
    private MultipleSelectionHandler multipleSelectionHandler;
    private boolean mHasLongPressed;
    private Vibrator vibrate;
    private Context mContext;
    private ItemTransactionRowBinding binding;

    TransactionInboxViewHolder(final ItemTransactionRowBinding binding, TransactionsInboxAdapter adapter, MultipleSelectionHandler multipleSelectionHandler, TransactionsInboxAdapter.OnMessageItemClickListener onMessageItemClickListener, Vibrator vibrate) {
        super(binding.getRoot());
        this.binding = binding;
        mContext = itemView.getContext();
        binding.transactionRow.setTag(this);
        this.adapter = adapter;
        this.multipleSelectionHandler = multipleSelectionHandler;
        this.onMessageItemClickListener = onMessageItemClickListener;
        this.vibrate = vibrate;
    }

    public void setup(final WeakReference<TransactionInboxRowInterface> transactionRowInterface, final FBTransactionNotificationsViewModel model, int position, final boolean lastItemInSection) {
        this.transactionRowInterface = transactionRowInterface;
        this.model = model;
        try {
            setAmount();

            int padding20px = mContext.getResources().getDimensionPixelSize(R.dimen.dimen_20dp);
            int padding15px = mContext.getResources().getDimensionPixelSize(R.dimen.dimen_15dp);

            binding.ivUnreadIndicator.setVisibility(model.isRead() ? View.GONE : View.VISIBLE);
            if (model.isRead()) {
                binding.transactionName.setPadding(padding20px, padding20px, padding20px, 0);
                binding.transactionDate.setPadding(padding20px, 0, padding20px, padding20px);
            } else {
                binding.transactionName.setPadding(padding15px, padding20px, padding20px, 0);
                binding.transactionDate.setPadding(padding15px, 0, padding20px, padding20px);
            }

            if (model.isRead()) {
                binding.transactionName.setTypeface(Typeface.createFromAsset(mContext.getAssets(), mContext.getString(R.string.font_path_roboto_light)));
                binding.transactionAmount.setTypeface(Typeface.createFromAsset(mContext.getAssets(), mContext.getString(R.string.font_path_roboto_light)));
            } else {
                binding.transactionName.setTypeface(Typeface.createFromAsset(mContext.getAssets(), mContext.getString(R.string.font_path_roboto_medium)));
                binding.transactionAmount.setTypeface(Typeface.createFromAsset(mContext.getAssets(), mContext.getString(R.string.font_path_roboto_medium)));
            }

            if (model.getMetaDate() != null) {
                String date = FormattingUtil.getFormattedDateSAtoLocal(model.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS, FormattingUtil.DATE_FORMAT_DD_SPACE_MMM_SPACE_YYYY);
                String time = FormattingUtil.getFormattedDateSAtoLocal(model.getMetaDate(), FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS, FormattingUtil.TIME_IN_HH_MM);
                binding.transactionDate.setText(String.format("%s at %S", date, time));
            }

            binding.details.tvAccountDetail.setText(model.getMetaAccNumber());
            binding.details.tvTransactionType.setText(model.getMetaTransType());



            // viewReportFraudContainer , Check fraud option
            List<FBTransactionNotificationsViewModel.ResponseOption> responseOptions = model.getResponseOptions();
            if (responseOptions != null && !responseOptions.isEmpty() && responseOptions.get(0).getAction() != null) {
                String transactionType = model.getMetaTransType();
                switch (transactionType) {
                    case NotificationConstants.TRANSACTION_TYPES.DEBIT_ORDER:
                        binding.infoTxt.setText(this.mContext.getResources().getString(R.string.debit_order_info_txt));
                        binding.dummyView.setVisibility(View.VISIBLE);
                        binding.reportFraudContainer.setVisibility(View.VISIBLE);
                        break;
                    case NotificationConstants.TRANSACTION_TYPES.PAYMENT:
                    case NotificationConstants.TRANSACTION_TYPES.PREPAID:
                    case NotificationConstants.TRANSACTION_TYPES.PURCHASE:
                    case NotificationConstants.TRANSACTION_TYPES.WITHDRAWAL:
                    case NotificationConstants.TRANSACTION_TYPES.MPESA:
                    case NotificationConstants.TRANSACTION_TYPES.INTERBANK_EFT:
                    case NotificationConstants.TRANSACTION_TYPES.PREPAID_AIRTIME:
                    case NotificationConstants.TRANSACTION_TYPES.PREPAID_ELECTRICITY:
                    case NotificationConstants.TRANSACTION_TYPES.LOTTO:
                    case NotificationConstants.TRANSACTION_TYPES.SEND_IMALI:
                    case NotificationConstants.TRANSACTION_TYPES.DEBIT:
                        binding.infoTxt.setText(this.mContext.getResources().getString(R.string.report_fraud_info_txt));
                        binding.dummyView.setVisibility(View.VISIBLE);
                        binding.reportFraudContainer.setVisibility(View.VISIBLE);
                        break;
                    default:
                        binding.reportFraudContainer.setVisibility(View.GONE);

                }
                binding.navigationLink.setText(model.getResponseOptions().get(0).getLabel());
                binding.navigationLink.setTag(model.getResponseOptions().get(0));
            } else {
                binding.reportFraudContainer.setVisibility(View.GONE);
            }

            binding.transactionRowSelected.setVisibility(View.GONE);
            if (adapter.getExpandIndex() != position) {
                ViewUtils.hideViews(binding.layoutDetails);
                if (adapter.getSelectedNotificationID() != -1) {
                    if (adapter.getSelectedNotificationID() == model.getNotificationId()) {
                        binding.transactionRowSelected.setVisibility(View.VISIBLE);
                    }
                }
            } else {
                adapter.setSelected(-1);
                binding.layoutDetails.setVisibility(View.VISIBLE);
            }

            binding.transactionName.setText(getTransactionDetail(model));

            //Hide view if value is null or empty
            if (StringUtils.isNullOrEmpty(model.getMetaCardnumber())) {
                ViewUtils.hideViews(binding.details.lblCardNumber, binding.details.tvCardNumber);
            } else {
                ViewUtils.showViews(binding.details.lblCardNumber, binding.details.tvCardNumber);
                binding.details.tvCardNumber.setText(model.getMetaCardnumber());
            }

            if (StringUtils.isNullOrEmpty(model.getMetaLocation())) {
                ViewUtils.hideViews(binding.details.lblLocation, binding.details.tvLocation);
            } else {
                ViewUtils.showViews(binding.details.lblLocation, binding.details.tvLocation);
                binding.details.tvLocation.setText(model.getMetaLocation());
            }

            if (StringUtils.isNullOrEmpty(model.getMetaNarrative())) {
                ViewUtils.hideViews(binding.details.lblNarrative, binding.details.tvNarrative);
            } else {
                ViewUtils.showViews(binding.details.lblNarrative, binding.details.tvNarrative);
                binding.details.tvNarrative.setText(model.getMetaNarrative());
            }

            binding.divider.setVisibility(lastItemInSection ? View.GONE : View.VISIBLE);
            int visibility = multipleSelectionHandler.isSelectable() ? View.VISIBLE : View.GONE;
            binding.ivCheckbox.setVisibility(visibility);
            binding.ivCheckboxContainer.setVisibility(visibility);

        } catch (Exception e) {
            NBLogger.e("Transaction Inbox Holder", e.getMessage());
        }
        multipleSelectionHandler.bind(this);
        binding.transactionRow.setOnClickListener(this::onClick);
        binding.navigationLink.setOnClickListener(this::onClick);
        binding.notificationMessagesForegroundView.setOnClickListener(this::onClick);

        binding.transactionRow.setOnLongClickListener(this::onLongClick);
        binding.navigationLink.setOnLongClickListener(this::onLongClick);
        binding.notificationMessagesForegroundView.setOnLongClickListener(this::onLongClick);

        binding.transactionRow.setOnTouchListener((v, event) -> onTouch(event));
        binding.navigationLink.setOnTouchListener((v, event) -> onTouch(event));
        binding.notificationMessagesForegroundView.setOnTouchListener((v, event) -> onTouch(event));
    }

    private String getTransactionDetail(FBTransactionNotificationsViewModel model) {
        StringBuilder sb = new StringBuilder();
        if (model.getMetaTransType() != null) {
            sb.append(model.getMetaTransType());
            sb.append(StringUtils.SPACE);
            sb.append(StringUtils.HYPHEN);
            sb.append(StringUtils.SPACE);
        }
        if (model.getMetaNarrative() != null) {
            sb.append(model.getMetaNarrative());
        }
        return sb.toString();
    }



    void setOnMessageItemClickListener(TransactionsInboxAdapter.OnMessageItemClickListener onMessageItemClickListener) {
        this.onMessageItemClickListener = onMessageItemClickListener;
    }

    public void onClick(View view) {
        if (!multipleSelectionHandler.tapSelection(this)) {
            if (onMessageItemClickListener != null && adapter != null) {
                if (view.getId() == R.id.transaction_row) {
                    adapter.setSelected(-1);
                    RecyclerView.ViewHolder viewHolder = (RecyclerView.ViewHolder) view.getTag();
                    int index = viewHolder.getAdapterPosition();
                    if (adapter.getExpandIndex() == index) {
                        adapter.setExpandIndex(-1);
                        ViewUtils.hideViews(binding.layoutDetails);
                        adapter.notifyDataSetChanged();
                    } else {
                        adapter.setExpandIndex(index);
                        ViewUtils.showViews(binding.layoutDetails);
                        if (model.isRead()) {
                            adapter.notifyDataSetChanged();
                        } else {
                            if (transactionRowInterface != null) {
                                transactionRowInterface.get().onTransactionDetailsShow(model);
                            }
                        }
                    }
                } else if (view.getId() == R.id.navigation_link) {
                    if (transactionRowInterface != null && transactionRowInterface.get() != null) {
                        transactionRowInterface.get().onTransactionClicked(model);
                    }
                }
            }
        }
    }

    public boolean onLongClick(View v) {
        vibrate.vibrate(Constants.FIFTY);
        mHasLongPressed = true;
        if (!multipleSelectionHandler.isSelectable()) {
            //vibrate before selection started
            v.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS);
        }
        return true;
    }

    public boolean onTouch(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_UP) {
            if (mHasLongPressed) {
                multipleSelectionHandler.handleSelection(this);
                mHasLongPressed = false;
                return true;
            }
        }
        return false;
    }


    private void setAmount() {
        String amount = FormattingUtil.convertToSouthAfricaFormattedCurrency(model.getMetaAmountInDouble());
        binding.transactionAmount.setText(amount);
    }

    @Override
    public void setSelected(boolean isSelected) {
        int imageResource = isSelected ? R.drawable.checkbox_selected : R.drawable.checkbox;
        binding.ivCheckbox.setImageResource(imageResource);
    }

}
