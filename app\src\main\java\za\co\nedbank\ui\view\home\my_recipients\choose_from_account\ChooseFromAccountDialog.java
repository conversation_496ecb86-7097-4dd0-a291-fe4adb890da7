/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients.choose_from_account;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.dialog.BaseDialogFragment;
import za.co.nedbank.core.view.model.AccountViewModel;
import za.co.nedbank.databinding.FragmentDialogChooseRecipientBinding;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.my_recipients.choose_recipient.IChooseRecipientListItemListener;

/**
 * Created by priyadhingra on 10-04-2018.
 */

public class ChooseFromAccountDialog extends BaseDialogFragment implements ChooseFromAccountDialogView,IChooseRecipientListItemListener {

    public static final String TAG = ChooseFromAccountDialog.class.getCanonicalName();
    private static final String MODEL = "model";
    private static final String LAST_SELECTED_POS = "last_selected_pos";
    private IChooseFromAccountDialogActivityInterface mDialogActivityInterface;
    private List<AccountViewModel> payAccounts;
    private ChooseFromAccountRecyclerAdapter mChooseFromAccountRecyclerAdapter;
    private int lastSelectedPos;

    @Inject
    ChooseFromAccountDialogPresenter mPresenter;

    public static Fragment getInstance(Context context, ArrayList<AccountViewModel> payAccounts,int lastSelectedPos) {
        Bundle b = new Bundle();
        b.putParcelableArrayList(MODEL, payAccounts);
        b.putInt(LAST_SELECTED_POS,lastSelectedPos);
        return Fragment.instantiate(context, ChooseFromAccountDialog.class.getName(), b);
    }

    public void setDialogActivityInterface(IChooseFromAccountDialogActivityInterface dialogActivityInterface) {
        mDialogActivityInterface = dialogActivityInterface;
    }

    @Nullable
    @Override
    public View onCreateView(final LayoutInflater inflater, @Nullable final ViewGroup container, @Nullable final Bundle savedInstanceState) {
        FragmentDialogChooseRecipientBinding binding = FragmentDialogChooseRecipientBinding.inflate(inflater, container, false);
        AppDI.getActivityComponent(((NBBaseActivity) getActivity())).inject(this);
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        if(getDialog().getWindow() != null){
            getDialog().getWindow().setDimAmount(0);
        }

        binding.recipientSelectRecylcerview.setItemAnimator(null);
        binding.recipientSelectRecylcerview.setHasFixedSize(true);
        binding.recipientSelectRecylcerview.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false));

        if(getArguments() != null){
            payAccounts = this.getArguments().getParcelableArrayList(MODEL);
            lastSelectedPos = this.getArguments().getInt(LAST_SELECTED_POS);
            if (payAccounts != null) {
                mChooseFromAccountRecyclerAdapter = new ChooseFromAccountRecyclerAdapter(payAccounts,this,lastSelectedPos);
                binding.recipientSelectRecylcerview.setAdapter(mChooseFromAccountRecyclerAdapter);
            }
        }
        return binding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();
        mPresenter.bind(this);
    }

    @Override
    public void onPause() {
        super.onPause();
        mPresenter.unbind();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mDialogActivityInterface = null;
    }

    @Override
    public void dismissDialog(int selectedPos) {
        if (mDialogActivityInterface != null) {
            mDialogActivityInterface.onDialogDismiss(TAG);
            this.lastSelectedPos = selectedPos;
            mDialogActivityInterface.onItemSelected(lastSelectedPos);
        }
        dismiss();
    }

    @Override
    public void selectedListItem(int position) {
        mPresenter.handleFromAccountSelected(position);
    }

}
