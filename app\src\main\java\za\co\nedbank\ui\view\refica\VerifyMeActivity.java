package za.co.nedbank.ui.view.refica;

import android.os.Bundle;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.databinding.ActivityFicaVerifyIdentityBinding;
import za.co.nedbank.ui.di.AppDI;

public class VerifyMeActivity extends NBBaseActivity implements VerifyMeView {

    @Inject
    VerifyMePresenter verifyMePresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AppDI.getActivityComponent(this).inject(this);
        ActivityFicaVerifyIdentityBinding binding = ActivityFicaVerifyIdentityBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        verifyMePresenter.bind(this);
        binding.nextButtonSecurity.setOnClickListener(v -> onSecurityNextButtonClicked());
        binding.cancelButtonSecurity.setOnClickListener(v -> finish());
        binding.icCrossSecurity.setOnClickListener(v -> finish());
    }

    public void onSecurityNextButtonClicked() {
        verifyMePresenter.sendAnalytics();
        navigateToVerifyMe();
    }

    public void navigateToVerifyMe() {
        setResult(RESULT_OK, getIntent());
        finish();
    }
}