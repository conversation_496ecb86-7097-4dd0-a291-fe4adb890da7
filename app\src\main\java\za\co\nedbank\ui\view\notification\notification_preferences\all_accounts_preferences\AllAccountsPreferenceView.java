package za.co.nedbank.ui.view.notification.notification_preferences.all_accounts_preferences;

import za.co.nedbank.core.base.NBBaseView;

public interface AllAccountsPreferenceView extends NBBaseView {
    void showProgress(boolean isVisible);

    void showErrorForUpdatePreferences(boolean isApiFailure, boolean isPushPrefOn, boolean isSMSPrefOn);

    void enableSaveButton(boolean enable);

    void updateUI();
}
