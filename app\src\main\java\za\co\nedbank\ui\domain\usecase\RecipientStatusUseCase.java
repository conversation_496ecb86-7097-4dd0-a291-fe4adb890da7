/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.data.mapper.RecipientResponseEntityToDomainDataMapper;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.model.response.RecipientResponseData;
import za.co.nedbank.core.domain.repository.IRecipientRepository;

public class RecipientStatusUseCase extends UseCase<String, RecipientResponseData> {

    private final IRecipientRepository mIRecipientRepository;
    private final RecipientResponseEntityToDomainDataMapper mAddRecipientResponseEntityToDomainDataMapper;

    @Inject
    protected RecipientStatusUseCase(final UseCaseComposer useCaseComposer,
                                     final IRecipientRepository iRecipientRepository,
                                     RecipientResponseEntityToDomainDataMapper addRecipientResponseEntityToDomainDataMapper) {
        super(useCaseComposer);
        this.mIRecipientRepository = iRecipientRepository;
        this.mAddRecipientResponseEntityToDomainDataMapper = addRecipientResponseEntityToDomainDataMapper;
        setCacheObservable(false);
    }

    @Override
    protected Observable<RecipientResponseData> createUseCaseObservable(String recipientStatus) {
        return mIRecipientRepository.recipientStatus(recipientStatus).map(mAddRecipientResponseEntityToDomainDataMapper::mapRecipientResponseEntityToData);
    }
}
