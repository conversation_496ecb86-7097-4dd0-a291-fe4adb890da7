
mkdir -p app/build/reports/coverage

cp -Rf enroll/build/reports/jacoco/ app/build/reports/coverage/enroll/
cp -Rf app/build/reports/jacoco/ app/build/reports/coverage/app/
cp -Rf services/build/reports/jacoco/ app/build/reports/coverage/services/
cp -Rf payment/build/reports/jacoco/ app/build/reports/coverage/payment/
cp -Rf profile/build/reports/jacoco/ app/build/reports/coverage/profile/
cp -Rf pfm/build/reports/jacoco/ app/build/reports/coverage/pfm/
cp -Rf core/build/reports/jacoco/ app/build/reports/coverage/core/