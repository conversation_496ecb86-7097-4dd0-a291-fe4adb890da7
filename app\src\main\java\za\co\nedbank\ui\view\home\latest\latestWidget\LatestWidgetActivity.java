package za.co.nedbank.ui.view.home.latest.latestWidget;

import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.concierge.chat.view.NBChatBaseActivity;
import za.co.nedbank.core.dashboard.HomeWidget;
import za.co.nedbank.databinding.ActivityLatestScreenBinding;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.home.latest.HomeLatestFragment;

public class LatestWidgetActivity extends NBChatBaseActivity implements LatestWidgetView {

    @Inject
    LatestWidgetPresenter mPresenter;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActivityLatestScreenBinding binding = ActivityLatestScreenBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        AppDI.getActivityComponent(this).inject(this);
        mPresenter.bind(this);
        binding.toolbar.setTitle(R.string.title_latest);
        initToolbar(binding.toolbar, true, true, true,ChatIconColor.GREY);
        showLatestFragment();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mPresenter.unbind();
    }

    @Override
    protected void handleChatMenuClicked(String conversationId) {
        super.handleChatMenuClicked(EnrollV2TrackingEvent.TAG_WIDGET_LATEST_CHAT);
        mPresenter.onChatMenuClicked(getString(R.string.title_latest));
    }

    public void showLatestFragment()
    {
        FragmentManager mFragmentManager = getSupportFragmentManager();
        Fragment fragment = mFragmentManager.findFragmentById(R.id.latest_contentFrame);
        HomeLatestFragment homeLatestFragment = null;
        if (fragment instanceof HomeLatestFragment) {
            homeLatestFragment = (HomeLatestFragment) fragment;
        }
        if (homeLatestFragment == null) {
            homeLatestFragment = HomeLatestFragment.getInstance();
        }
        Bundle bundle = new Bundle();
        bundle.putInt(Constants.BUNDLE_KEYS.FROM_ACTIVITY, HomeWidget.ACTION_LATEST);
        homeLatestFragment.setArguments(bundle);
        homeLatestFragment.setNonTpUILatest(false);
        replaceFragment(mFragmentManager, homeLatestFragment, R.id.latest_contentFrame);
    }

}