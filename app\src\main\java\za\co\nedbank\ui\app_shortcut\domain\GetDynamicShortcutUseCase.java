package za.co.nedbank.ui.app_shortcut.domain;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.VoidUseCase;
import za.co.nedbank.ui.app_shortcut.AppShortcutModel;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 19/01/18.
 */

public class GetDynamicShortcutUseCase extends VoidUseCase<List<AppShortcutModel>> {

    private final DynamicShortcutRepository mDynamicShortcutRepository;

    @Inject
    protected GetDynamicShortcutUseCase(final UseCaseComposer useCaseComposer, final DynamicShortcutRepository dynamicShortcutRepository) {
        super(useCaseComposer);
        this.mDynamicShortcutRepository = dynamicShortcutRepository;
    }

    @Override
    protected Observable<List<AppShortcutModel>> createUseCaseObservable() {
        return mDynamicShortcutRepository.getAppShortcutList();
    }
}
