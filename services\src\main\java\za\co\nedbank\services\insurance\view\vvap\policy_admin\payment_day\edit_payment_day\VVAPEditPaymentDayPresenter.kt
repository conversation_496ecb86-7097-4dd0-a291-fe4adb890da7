package za.co.nedbank.services.insurance.view.vvap.policy_admin.payment_day.edit_payment_day

import android.annotation.SuppressLint
import za.co.nedbank.core.base.NBBasePresenter
import za.co.nedbank.core.navigation.NavigationResult
import za.co.nedbank.core.navigation.NavigationRouter
import za.co.nedbank.core.navigation.NavigationTarget
import za.co.nedbank.core.tracking.Analytics
import za.co.nedbank.core.tracking.TrackingEvent
import za.co.nedbank.core.tracking.adobe.AdobeContextData
import za.co.nedbank.core.utils.FormattingUtil
import za.co.nedbank.core.validation.NonEmptyTextValidator
import za.co.nedbank.services.insurance.domain.data.response.vvap.manage_policy.finance.submit_finance.FinanceChangesResponseDataModel
import za.co.nedbank.services.insurance.domain.data.response.vvap.manage_policy.payment_day.GetPaymentDayResponseDataModel
import za.co.nedbank.services.insurance.domain.usecase.vvap.manage_policy.payment_day.UpdatePaymentDayDetailsUseCase
import za.co.nedbank.services.insurance.domain.usecase.vvap.manage_policy.payment_day.VVAPGetPaymentDayUseCase
import za.co.nedbank.services.insurance.view.generic.common.other.model.InsuranceCodeDescriptionViewModel
import za.co.nedbank.services.insurance.view.other.constants.InsuranceConstants
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.manage_policy.finance.FinanceChangesRequestViewToDataMapper
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.manage_policy.finance.FinanceChangesResponseDataToViewMapper
import za.co.nedbank.services.insurance.view.other.mapper.vvaps.manage_policy.payment_day.PaymentDayResponseDataToViewMapper
import za.co.nedbank.services.insurance.view.vvap.policy_admin.other.helper.PolicyAdminChangeRequestHelper
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget
import za.co.nedbank.uisdk.validation.ValidatableInput
import javax.inject.Inject

class VVAPEditPaymentDayPresenter @Inject constructor(
    private val mNavigationRouter: NavigationRouter,
    private val mNonEmptyTextValidator: NonEmptyTextValidator,
    private val mVvapGetPaymentDayUseCase: VVAPGetPaymentDayUseCase,
    private val updatePaymentDayUseCase: UpdatePaymentDayDetailsUseCase,
    private val mGetPaymentDataToViewMapper: PaymentDayResponseDataToViewMapper,
    private val financeChangesRequestViewToDataMapper: FinanceChangesRequestViewToDataMapper,
    private val financeChangesResponseViewToDataMapper: FinanceChangesResponseDataToViewMapper,
    private val analytics: Analytics
) : NBBasePresenter<VVAPEditPaymentDayView>() {

    private var mPaymentDayList: List<InsuranceCodeDescriptionViewModel?>? = null

    @SuppressLint("CheckResult")
    fun fetchPaymentDayList(paymentDay: String, isFuneral: Boolean = false) {
        mVvapGetPaymentDayUseCase.execute(paymentDay).compose(bindToLifecycle())
            .doOnSubscribe { showViewProgressBar(true) }
            .doOnTerminate { showViewProgressBar(false) }
            .subscribe({ data: GetPaymentDayResponseDataModel ->
                //check if response is success
                val viewModel = mGetPaymentDataToViewMapper.map(data)

                if (viewModel.metadata?.resultData?.get(0)?.resultDetail?.get(0)?.result != null
                    && viewModel.metadata.resultData[0]?.resultDetail?.get(0)?.result.equals(
                        InsuranceConstants.ResponseCode.CODE_R00,
                        ignoreCase = true
                    )
                ) {
                    val paymentDayList = ArrayList<InsuranceCodeDescriptionViewModel>()
                    for (dropDownViewModel in viewModel.data!!) {

                        val dayText = if (isFuneral) {
                            dropDownViewModel // No suffix
                        } else {
                            FormattingUtil.getDayOfMonthSuffix(dropDownViewModel.toInt())
                        }

                        val day = InsuranceCodeDescriptionViewModel(dropDownViewModel, dayText)
                        paymentDayList.add(day)
                    }
                    mPaymentDayList = paymentDayList.toList()
                } else {
                    showAPIError()
                }
            }) { showAPIError() }
    }


    fun showViewProgressBar(b: Boolean) {
        view?.showProgressBar(b)
    }

    fun showAPIError() {
        showViewProgressBar(false)
        view?.showAPIError()
    }

    fun validateAllFields(
        paymentDay: ValidatableInput<String>,
        isTermAccept: Boolean,
        isPaymentDayUpdated: Boolean
    ) {
        view?.setNextButtonEnable(
            mNonEmptyTextValidator.validateInput(paymentDay.value).isOk
                    && isTermAccept && isPaymentDayUpdated
        )
    }

    @SuppressLint("CheckResult")
    fun updatePaymentDayDetails(
        paymentDay: String,
        policyNumber: String,
        riskSerialNumber: String,
        insuranceProductType: String
    ) {

        updatePaymentDayUseCase.execute(insuranceProductType,
            financeChangesRequestViewToDataMapper.map(
                PolicyAdminChangeRequestHelper(
                    paymentDayHelper = paymentDay,
                    policyNumber = policyNumber,
                    riskSerialNumber = riskSerialNumber,
                ).getViewModel()
            )
        ).compose(bindToLifecycle())
            .doOnSubscribe { showViewProgressBar(true) }
            .doOnTerminate { showViewProgressBar(false) }
            .subscribe({ data: FinanceChangesResponseDataModel ->

                //check if response is success
                val viewModel = financeChangesResponseViewToDataMapper.map(data)
                if (viewModel.resultSet != null
                    && viewModel.resultSet!!.resultCode != null
                ) {
                    when {
                        viewModel.resultSet!!.resultCode.equals(
                            InsuranceConstants.ResponseCode.CODE_R00,
                            ignoreCase = true
                        ) -> {
                            view?.submitSuccess(paymentDay)
                        }
                        viewModel.resultSet!!.resultCode.equals(
                            InsuranceConstants.ResponseCode.CODE_R01,
                            ignoreCase = true
                        ) -> {
                            showSubmitAPIError()
                        }
                        else -> {
                            showAPIError()
                        }
                    }
                }
            }) { showAPIError() }
    }

    private fun showSubmitAPIError() {
        view?.showSubmitError()
    }

    @SuppressLint("CheckResult")
    fun navigateToDaySelection() {
        val target = NavigationTarget.to(ServicesNavigationTarget.INSURANCE_COMMON_SELECTION_SCREEN)
        target.withParam(
            InsuranceConstants.ParamKeys.PARAM_COMMON_SELECTION_LIST,
            mPaymentDayList
        )
        target.withParam(
            InsuranceConstants.ParamKeys.PARAM_COMMON_SELECTION_TYPE,
            InsuranceConstants.CommonTypeConstants.PAYMENT_DAY.toInt()
        )
        mNavigationRouter.navigateWithResult(target)
            .subscribe { navigationResult: NavigationResult? ->
                if (null != navigationResult && navigationResult.isOk) {
                    val map =
                        navigationResult.params
                    if (map.containsKey(InsuranceConstants.ParamKeys.PARAM_SELECTED_TYPE_MODEL)) {
                        val paymentDay =
                            map[InsuranceConstants.ParamKeys.PARAM_SELECTED_TYPE_MODEL] as InsuranceCodeDescriptionViewModel?
                        view.setPaymentDay(paymentDay)
                    }
                }
            }
    }

    fun navigateToPaymentDayDetail(paymentDay: String?) {
        mNavigationRouter.navigateTo(
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_VVAPS_PAYMENT_DAY_DETAILS)
                .withParam(InsuranceConstants.ParamKeys.PARAM_UPDATE_PAYMENT_DETAILS, paymentDay)
                .withIntentFlagClearTopSingleTop(true)
        )
        view?.close()
    }

    fun moveToProductListScreen() {
        val navigationTarget =
            NavigationTarget.to(ServicesNavigationTarget.INSURANCE_DETAILS_CONTAINER)
        navigationTarget.withIntentFlagClearTopSingleTop(true)
        mNavigationRouter.navigateTo(navigationTarget)
        if (view != null) {
            view!!.close()
        }
    }

    fun sendEventWithProduct(eventName: String?, subProduct: String?,productType: String?) {
        val cdata = HashMap<String, Any>()
        val adobeContextData = AdobeContextData(cdata)
        adobeContextData.setCategoryAndProduct(
            TrackingEvent.ANALYTICS.INSURANCE_PRODUCT_CATEGORY,
            productType
        )
        adobeContextData.setProductCategory(TrackingEvent.ANALYTICS.INSURENCE)
        adobeContextData.setProductAccount(productType)
        adobeContextData.setSubProduct(subProduct)
        analytics.sendEventActionWithMap(eventName, cdata)
    }
}