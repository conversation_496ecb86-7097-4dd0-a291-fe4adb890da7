/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.VoidUseCase;
import za.co.nedbank.services.domain.repository.AccountsRepository;
import za.co.nedbank.ui.domain.mapper.AvoWalletDetailsEntityToDomainDataMapper;
import za.co.nedbank.ui.domain.model.AvoWalletDetailsModel;

public class GetAvoWalletDetailsUseCase extends VoidUseCase<AvoWalletDetailsModel> {

    private final AccountsRepository mAccountsRepository;
    private final AvoWalletDetailsEntityToDomainDataMapper mEntityToDomainDataMapper;

    @Inject
    protected GetAvoWalletDetailsUseCase(final UseCaseComposer useCaseComposer,
                                         final AccountsRepository accountsRepository,
                                         AvoWalletDetailsEntityToDomainDataMapper entityToDomainDataMapper) {
        super(useCaseComposer);
        this.mAccountsRepository = accountsRepository;
        this.mEntityToDomainDataMapper = entityToDomainDataMapper;
    }

    @Override
    protected Observable<AvoWalletDetailsModel> createUseCaseObservable() {
        return mAccountsRepository.getWalletDetails().map(mEntityToDomainDataMapper::mapAvoWalletEntityToDomainModel);
    }
}
