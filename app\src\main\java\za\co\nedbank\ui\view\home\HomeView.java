/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home;

import java.util.List;
import java.util.Map;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.services.view.account.branchcode.model.BranchCodeViewModel;
import za.co.nedbank.ui.app_shortcut.AppShortcutModel;

/**
 * Created by ch<PERSON>rani on 29-06-2017.
 */

interface HomeView extends NBBaseView {

    void showDemoModeEnabledDialog();

    void showOverviewUI();

    void showCardsUI(boolean isFromDeepLink);

    void toggleTransactionMenu();

    void resetMenuWithoutAnimation();

    void showRecipientUI();

    void showApplyNonTPUI();

    void showAccountsNonTPUI();

    void showLatestNonTpSalesUI();

    void showMoreOptionsUI(boolean isFingerPrintAltered);

    void setResult(Map<String, Object> params);

    boolean isFeatureDisabled(String feature);

    void hideTutorialGuide();

    void publishShortcuts(List<AppShortcutModel> appShortcutList);

    void showError(final String errorMessage);

    void showErrorWithOutTitle(final String errorMessage);

    void setUserInfo(UserDetailViewModel userDetailViewModel);

    void finishAffinity();

    String getDeviceID();

    void setUserId(String nidUserName);

    String getUserID();

    String getBirthDate();

    boolean isITAEnrolled();

    boolean isShowITA();

    void setOverviewUserInfo(boolean isSuccess, UserDetailData userDetailData, Error error);

    String getFbToken();

    boolean isHmsApiPreferred();

    Map<String, Object> convertStringToHashmap(String jsonString);

    String convertHashmapToString(Map<String, Object> deviceRegistrationMap);

    void setBranchCodeList(List<BranchCodeViewModel> branchCodeViewModelList);
}
