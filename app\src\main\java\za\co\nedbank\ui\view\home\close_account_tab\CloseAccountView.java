package za.co.nedbank.ui.view.home.close_account_tab;

import java.util.ArrayList;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.ui.view.model.ClosedAccount;

public interface CloseAccountView extends NBBaseView {
    void showProgressBar(boolean enable);

    void showEmptyPlaceHolder(boolean enable);

    void showAccountsList(ArrayList<ClosedAccount> closedAccounts);

    void showErrorMessage(String message);
}