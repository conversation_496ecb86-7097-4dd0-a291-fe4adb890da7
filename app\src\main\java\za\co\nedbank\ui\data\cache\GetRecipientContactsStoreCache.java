/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.cache;

import android.os.Build;
import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import io.reactivex.Observable;
import io.reactivex.annotations.NonNull;
import za.co.nedbank.payment.buy.domain.model.response.UserContactData;
import za.co.nedbank.ui.domain.repository.IGetUserContactsCache;


public class GetRecipientContactsStoreCache implements IGetUserContactsCache {
    private List<UserContactData> mUserContactDataList = new ArrayList<>();

    @Override
    public Observable<List<UserContactData>> getContacts(String searchQuery) {
        return Observable.create(emitter -> {
            if (TextUtils.isEmpty(searchQuery)) {
                emitter.onNext(mUserContactDataList);
                emitter.onComplete();
            } else {
                final List<UserContactData> userContactDataList =
                        GetRecipientContactsStoreCache.this.mUserContactDataList;
                List<UserContactData> filteredList;
                if (userContactDataList != null) {
                    filteredList = getFilteredList(userContactDataList,searchQuery);
                    emitter.onNext(filteredList);
                    emitter.onComplete();
                } else {
                    emitter.onError(new Exception());
                }
            }
        });
    }

    public List<UserContactData> getFilteredList( List<UserContactData> userContactDataList,String searchQuery){
        List<UserContactData> filteredList;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            filteredList = userContactDataList.stream()
                    .filter(userContactData -> isMatch(userContactData, searchQuery))
                    .collect(Collectors.toList());
        } else {
            filteredList = new ArrayList<>();
            for (UserContactData userContactData : userContactDataList) {
                if (isMatch(userContactData, searchQuery)) {
                    filteredList.add(userContactData);
                }
            }
        }
       return filteredList;
    }

    @Override
    public void setList(List<UserContactData> userContactDataList) {
        mUserContactDataList = userContactDataList;
    }

    @Override
    public boolean isDataCached() {
        return mUserContactDataList.size() > 0;
    }

    private boolean isMatch(@NonNull UserContactData userContactData, @NonNull String searchQuery) {
        return Pattern.compile(Pattern.quote(searchQuery), Pattern.CASE_INSENSITIVE).matcher(userContactData.getContactName()).find()
                || Pattern.compile(Pattern.quote(searchQuery), Pattern.CASE_INSENSITIVE).matcher(userContactData.getPhoneNumber()).find();
    }
}
