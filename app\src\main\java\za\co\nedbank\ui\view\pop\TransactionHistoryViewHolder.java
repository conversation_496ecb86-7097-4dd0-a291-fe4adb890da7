package za.co.nedbank.ui.view.pop;

import static za.co.nedbank.core.utils.FormattingUtil.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS;

import androidx.recyclerview.widget.RecyclerView;

import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.DecimalFormat;

import za.co.nedbank.R;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.model.TransactionHistoryViewModel;
import za.co.nedbank.databinding.ItemTransactionHistoryBinding;

class TransactionHistoryViewHolder extends RecyclerView.ViewHolder {
    private static final int DECIMAL_LENGTH = 2;
    private DateTimeFormatter formatter;
    private TransactionHistoryRowInterface transactionHistoryRowInterface;
    private TransactionHistoryViewModel transactionHistoryViewModel;
    private boolean lastItemInSection;
    private ItemTransactionHistoryBinding binding;

    public TransactionHistoryViewHolder(ItemTransactionHistoryBinding binding) {
        super(binding.getRoot());
        this.binding = binding;
        formatter = DateTimeFormat.forPattern(itemView.getContext().getString(R.string.transaction_history_date_pattern));
    }

    public void setup(TransactionHistoryRowInterface transactionHistoryRowInterface, TransactionHistoryViewModel transactionHistoryViewModel, boolean lastItemInSection) {
        this.transactionHistoryRowInterface = transactionHistoryRowInterface;
        this.transactionHistoryViewModel = transactionHistoryViewModel;
        this.lastItemInSection = lastItemInSection;
        if (transactionHistoryViewModel != null) {
            if (!StringUtils.isNullOrEmpty(transactionHistoryViewModel.getStartDate())) {
                binding.date.setText(formatter.print(FormattingUtil.getFormattedDate(transactionHistoryViewModel.getStartDate(), DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS)));
            }
            if (!StringUtils.isNullOrEmpty(transactionHistoryViewModel.getMyReference())) {
                binding.description.setText(transactionHistoryViewModel.getMyReference());
            }
            DecimalFormat format = new DecimalFormat("0.00");
            String amountText = format.format(transactionHistoryViewModel.getAmount());
            amountText = FormattingUtil.convertFromSouthAfricaCurrency(amountText);
            binding.amount.setText(new StringBuilder().append(FormattingUtil.convertAmountToSACurrency(amountText, "R", DECIMAL_LENGTH)).toString());
            binding.transactionHistoryRow.setOnClickListener(v -> onTransactionHistoryClick());
        }

    }

    public void onTransactionHistoryClick() {
        if (transactionHistoryRowInterface != null) {
            transactionHistoryRowInterface.transactionHistoryClicked(transactionHistoryViewModel);
        }
    }
}
