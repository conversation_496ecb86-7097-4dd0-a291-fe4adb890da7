/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.link_finance;

import javax.inject.Inject;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.ui.domain.repository.ILinkAccountStatusRepository;

/**
 * Created by piyushgupta01 on 8/1/2017.
 */

public class NBLinkAccountStatusRepository implements ILinkAccountStatusRepository {

    private final ApplicationStorage applicationStorage;

    @Inject
    public NBLinkAccountStatusRepository(ApplicationStorage applicationStorage) {
        this.applicationStorage = applicationStorage;
    }

    @Override
    public int getAccountLinkedStatus() {
        //TODO this will be replaced with an API call
        return applicationStorage.getInteger(StorageKeys.IS_LINK_CLICKED, Constants.NEDBANK_ACCOUNT_LINKED_STATE.ACCOUNT_NOT_LINKED);
    }

}
