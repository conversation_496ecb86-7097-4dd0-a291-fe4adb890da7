/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.notification.transaction_notification.inbox;


import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import za.co.nedbank.core.base.adapter.Section;
import za.co.nedbank.core.databinding.ItemTransactionSectionBinding;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;


public class SectionViewHolder extends RecyclerView.ViewHolder {

    TextView sectionName;

    SectionViewHolder(final ItemTransactionSectionBinding binding) {
        super(binding.getRoot());
        sectionName = binding.transactionSectionName;
    }

    public void setup(final Section<FBTransactionNotificationsViewModel> section) {
        sectionName.setText(section.getName());
    }
}
