/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.di.modules;

import android.content.Context;

import dagger.Module;
import dagger.Provides;
import za.co.nedbank.core.base.NBBaseFragment;
import za.co.nedbank.core.data.view_banker.ViewBankerRepository;
import za.co.nedbank.core.di.scopes.ActivityScope;
import za.co.nedbank.core.domain.repository.view_banker.IViewBankerDetailsRepository;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.networking.NetworkClient;
import za.co.nedbank.payment.common.data.cache.beneficiary.InternationalRecipientListCache;
import za.co.nedbank.payment.common.data.cache.beneficiary.InternationalRecipientListImpl;
import za.co.nedbank.payment.common.data.cache.beneficiary.UserBeneficiaryCache;
import za.co.nedbank.payment.common.data.cache.beneficiary.UserBeneficiaryCacheImpl;
import za.co.nedbank.payment.common.data.datastore.beneficiary.UserBeneficiaryDataStoreFactory;
import za.co.nedbank.payment.common.data.mapper.UserBeneficiaryEntityToDataMapper;
import za.co.nedbank.payment.common.data.repository.UserBeneficiaryListRepository;
import za.co.nedbank.payment.common.domain.repository.IUserBeneficiaryListRepository;
import za.co.nedbank.payment.internationalpayment.data.cache.BICCodeListCache;
import za.co.nedbank.payment.internationalpayment.data.cache.BICCodeListCacheImpl;
import za.co.nedbank.payment.internationalpayment.data.datastore.BICCodeListDataStoreFactory;
import za.co.nedbank.payment.internationalpayment.data.mapper.BICCodeEntityToDataMapper;
import za.co.nedbank.payment.internationalpayment.data.repository.InternationalPaymentRepository;
import za.co.nedbank.payment.internationalpayment.domain.repository.IPaymentRepository;
import za.co.nedbank.ui.data.NBFinanceDataRepository;
import za.co.nedbank.ui.data.link_finance.NBLinkAccountInfoRepository;
import za.co.nedbank.ui.data.link_finance.NBLinkAccountStatusRepository;
import za.co.nedbank.ui.domain.repository.IFinanceDataRepository;
import za.co.nedbank.ui.domain.repository.ILinkAccountInfoRepository;
import za.co.nedbank.ui.domain.repository.ILinkAccountStatusRepository;
import za.co.nedbank.ui.domain.repository.IMoneyRequestRepository;
import za.co.nedbank.ui.domain.repository.IMoneyRequestsActionRepository;
import za.co.nedbank.ui.domain.repository.IMoneyRequestsRepository;
import za.co.nedbank.ui.domain.repository.IValidateNumberRepository;
import za.co.nedbank.ui.domain.repository.ViewMoneyRequestsRepository;
import za.co.nedbank.ui.domain.repository.money_request.MoneyRequestRepository;
import za.co.nedbank.ui.domain.repository.money_request.MoneyRequestsActionRepository;
import za.co.nedbank.ui.domain.repository.money_request.ValidateNumberRepository;

/**
 * Created by charurani on 03-07-2017.
 */
@Module
public class AppFragmentModule {

    private final NBBaseFragment fragment;

    public AppFragmentModule(final NBBaseFragment fragment) {
        this.fragment = fragment;
    }

    @Provides
    NBBaseFragment provideFragment() {
        return fragment;
    }

    @Provides
    public IFinanceDataRepository provideFinanceDataRepository() {
        return new NBFinanceDataRepository();
    }

    /*@Provides
    CategoryEntityToDataMapper provideCategoryEntityToDataMapper() {
        return new CategoryEntityToDataMapper();
    }*/

    @Provides
    public ILinkAccountInfoRepository provideLinkAccountInfoRepository(Context context) {
        return new NBLinkAccountInfoRepository(context);
    }

    @Provides
    public ILinkAccountStatusRepository provideLinkAccountStatusRepository(ApplicationStorage applicationStorage) {
        return new NBLinkAccountStatusRepository(applicationStorage);
    }

    @Provides
    public IMoneyRequestsRepository provideMoneyRequestsRepository(final ViewMoneyRequestsRepository viewMoneyRequestsRepository) {
        return viewMoneyRequestsRepository;
    }

    @Provides
    public IMoneyRequestRepository provideMoneyRequestRepository(final MoneyRequestRepository moneyRequestRepository) {
        return moneyRequestRepository;
    }

    @Provides
    public IMoneyRequestsActionRepository provideMoneyRequestsActionRepository(final MoneyRequestsActionRepository moneyRequestsActionRepository) {
        return moneyRequestsActionRepository;
    }

    @Provides
    public IUserBeneficiaryListRepository provideBeneficiaryRepository(UserBeneficiaryDataStoreFactory userBeneficiaryDataStoreFactory, UserBeneficiaryEntityToDataMapper userBeneficiaryEntityToDataMapper) {
        return new UserBeneficiaryListRepository(userBeneficiaryEntityToDataMapper, userBeneficiaryDataStoreFactory);
    }

    @Provides
    @ActivityScope
    public UserBeneficiaryCache provideUserBeneficiaryCache() {
        return new UserBeneficiaryCacheImpl();
    }

    @Provides
    @ActivityScope
    public InternationalRecipientListCache provideInternationalUserBeneficiaryCache() {
        return new InternationalRecipientListImpl();
    }

    @Provides
    public IValidateNumberRepository provideValidateNumberRepository(final ValidateNumberRepository validateNumberRepository) {
        return validateNumberRepository;
    }

    @Provides
    public IViewBankerDetailsRepository provideViewBankerDetailsRepository(final ViewBankerRepository viewBankerRepository) {
        return viewBankerRepository;
    }

    @Provides
    IPaymentRepository providesInternationalPaymentRepository(final NetworkClient networkClient,
                                                              final BICCodeListDataStoreFactory bicCodeListDataStoreFactory,
                                                              final BICCodeEntityToDataMapper bicCodeEntityToDataMapper) {
        return new InternationalPaymentRepository(networkClient, bicCodeListDataStoreFactory, bicCodeEntityToDataMapper);
    }

    @Provides
    BICCodeListCache provideBICCodeCache() {
        return new BICCodeListCacheImpl();
    }
}
