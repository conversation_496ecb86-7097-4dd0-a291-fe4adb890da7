package za.co.nedbank.ui.view.card_delivery.deliver_result;

import android.graphics.Typeface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.method.LinkMovementMethod;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.databinding.ActivityCardDeliveryResultBinding;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.di.AppDI;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryConstants;
import za.co.nedbank.ui.view.card_delivery.CardDeliveryUtils;

public class CardDeliveryResultActivity extends NBBaseActivity implements CardDeliveryResultView {

    @Inject
    CardDeliveryResultPresenter presenter;
    private ActivityCardDeliveryResultBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCardDeliveryResultBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        presenter.bind(this);
        presenter.checkResult(getFlow());
        binding.layoutCardDeliveryResult.btnDone.setOnClickListener(v -> presenter.navigateToNextScreen());
    }

    @Override
    public String getFlow() {
        return getIntent().getStringExtra(NavigationTarget.PARAM_CARD_DELIVERY_FLOW);
    }

    @Override
    public String getCardDeliverySubFeature() {
        return getIntent().getStringExtra(CardDeliveryConstants.EXTRA_SELECT_CARD_DELIVERY_OPTION_NAME);
    }

    @Override
    public String getSelectedDeliveryOption() {
        return getIntent().getStringExtra(CardDeliveryConstants.EXTRA_SELECT_CARD_DELIVERY_OPTION);
    }

    @Override
    public String getCardActionName() {
        return getIntent().getStringExtra(ServicesNavigationTarget.PARAM_CARD_ACTION_NAME);
    }

    @Override
    public boolean isOperationSuccess() {
        return getIntent().getBooleanExtra(NavigationTarget.PARAM_IS_CARD_ORDERING_SUCCESS, false);
    }

    @Override
    public void showLockerSuccess() {
        binding.layoutCardDeliveryResult.tvHeading.setText(R.string.card_on_the_way);
        setMessageWithPhNumber(R.string.card_delivery_locker_success,true);
    }

    @Override
    public void showDeliverToMeSuccess() {
        binding.layoutCardDeliveryResult.tvHeading.setText(R.string.card_on_the_way);
        setMessageWithPhNumber(R.string.card_deliver_to_me_success, true);
    }

    @Override
    public void showBranchPickSuccess() {
        binding.layoutCardDeliveryResult.tvHeading.setText(R.string.card_on_the_way);
        setMessageWithPhNumber(R.string.card_delivery_fica_branch_pick_success, true);
    }

    @Override
    public void showReplaceCardOrderingFailure() {
        setErrorTextsAndIcon(R.string.card_replacement_failed);
        setMessageWithPhNumber(R.string.card_replacement_failed_message, false);
    }

    @Override
    public void showEficaLockerListError() {
        setErrorTextsAndIcon(R.string.unable_to_display_dsv_locker_locations);
        setMessageWithPhNumber(R.string.dsv_locker_locations_not_loaded_error, true);
    }

    @Override
    public boolean isLockerLocationError() {
        return getIntent().getBooleanExtra(CardDeliveryConstants.EXTRA_IS_LOCKERS_NOT_LOADED_ERROR, false);
    }

    @Override
    public void showEficaCardServiceUnavailableError() {
        setErrorTextsAndIcon(R.string.card_ordering_unavailable);
        setMessageWithPhNumber(R.string.card_ordering_unavailable_fica_message, true);
    }

    @Override
    public void showReplaceCardServiceUnavailableError() {
        setErrorTextsAndIcon(R.string.card_ordering_unavailable);
        setMessageWithPhNumber(R.string.card_ordering_unavailable_replace_card_message, true);
    }

    private void setErrorTextsAndIcon(int errorHeading) {
        binding.layoutCardDeliveryResult.ivResultIcon.setImageResource(R.drawable.ic_card_delivery_something_went_wrong);
        binding.layoutCardDeliveryResult.tvHeading.setText(errorHeading);
        binding.layoutCardDeliveryResult.tvHeading.setTypeface(binding.layoutCardDeliveryResult.tvHeading.getTypeface(), Typeface.BOLD);
        binding.layoutCardDeliveryResult.btnDone.setText(getString(R.string.close));
    }

    private void setMessageWithPhNumber(int strRes, boolean showRrbTelNumber) {
        SpannableString phoneSpan;
        if (showRrbTelNumber) {
            phoneSpan = CardDeliveryUtils.createPhNumberSpannableRrbTelNo(this, getString(strRes));
        } else {
            phoneSpan = CardDeliveryUtils.createPhNumberSpannable(this, getString(strRes));
        }
        binding.layoutCardDeliveryResult.tvMessage.setText(phoneSpan);
        binding.layoutCardDeliveryResult.tvMessage.setMovementMethod(LinkMovementMethod.getInstance());
        binding.layoutCardDeliveryResult.tvMessage.setContentDescription(phoneSpan);
    }

    @Override
    public void onBackPressed() {
        presenter.navigateToNextScreen();
    }

    @Override
    protected void onDestroy() {
        presenter.unbind();
        super.onDestroy();
    }
}