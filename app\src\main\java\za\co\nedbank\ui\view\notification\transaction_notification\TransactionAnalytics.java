package za.co.nedbank.ui.view.notification.transaction_notification;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.tracking.Analytics;

public class TransactionAnalytics {

    private final Analytics mAnalytics;
    private final ApplicationStorage mApplicationStorage;

    @Inject
    public TransactionAnalytics(final Analytics analytics,
                         @Named("memory") ApplicationStorage applicationStorage) {
        this.mAnalytics = analytics;
        this.mApplicationStorage = applicationStorage;
    }

    public void sendEvent(String eventName, String key, String value) {
        String modifiedKey = key;
        mAnalytics.sendEvent(eventName, modifiedKey, value);
    }
}
