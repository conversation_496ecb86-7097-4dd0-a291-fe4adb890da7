package za.co.nedbank.ui.notifications;

import android.app.Notification;
import android.content.Context;
import android.graphics.Bitmap;

import androidx.core.app.NotificationCompat;
import androidx.core.text.HtmlCompat;

import com.squareup.picasso.Picasso;

import org.apache.commons.validator.routines.UrlValidator;

import java.io.IOException;

import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.notification.NotificationData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.ui.Tools;
import za.co.nedbank.ui.notifications.martec.AjoPushPayloadDataModel;

public class DefaultNotificationBuilder implements NotificationBuilder {

    @Override
    public NotificationCompat.Builder build(Context context, PushNotificationItem item) {
        NotificationData notificationData = item.getNotificationData();
        Tools tools = new Tools();
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, notificationData.getChannelId())
                .setSmallIcon(tools.getSmallNotificationIcon())
                .setContentTitle(notificationData.getHeading())
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setShowWhen(true)
                .setAutoCancel(true)
                .setDefaults(Notification.DEFAULT_ALL)
                .setContentIntent(item.getPendingIntent())
                .setDeleteIntent(item.getDeletePendingIntent());
        if (!StringUtils.isNullOrEmpty(notificationData.getSubHeading())) {

            builder.setStyle(new NotificationCompat.BigTextStyle()
                            .bigText(getSubHeading(notificationData.getSubHeading())))
                    .setContentText(getSubHeading(notificationData.getSubHeading()));
        }
        // Adding actions here
        if (item.getActions() != null) {
            for (NotificationCompat.Action action : item.getActions()) {
                builder.addAction(action);
            }
        }
        String notificationUrl = notificationData.getUrl();
        UrlValidator urlValidator =  new UrlValidator();
        if (!StringUtils.isNullOrEmpty(notificationUrl) && urlValidator.isValid(notificationUrl)) {
            try {
                Bitmap bitmap = Picasso.get().load(notificationUrl).get();
                if (bitmap != null) {
                    NotificationCompat.BigPictureStyle style = new NotificationCompat.BigPictureStyle();
                    style.bigPicture(bitmap);
                    builder.setStyle(style);
                    builder.setLargeIcon(bitmap);
                }
            } catch (IOException e) {
                NBLogger.e("Exception:", "DefaultNotificationBuilder", e);
            }
        }
        return builder;
    }

    private CharSequence getSubHeading(String subHeading) {
        return HtmlCompat.fromHtml(subHeading, HtmlCompat.FROM_HTML_MODE_LEGACY);
    }

    @Override
    public NotificationCompat.Builder buildAJO(Context context, PushNotificationItem item) {
        AjoPushPayloadDataModel payload = item.getAjoNotificationData();
        Tools tools = new Tools();
        String channelId = String.valueOf(payload.getChannelId().hashCode());
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, channelId)
                .setSmallIcon(tools.getSmallNotificationIcon())
                .setContentTitle(payload.getTitle())
                .setPriority(payload.getNotificationPriority())
                .setShowWhen(true)
                .setAutoCancel(true)
                .setDefaults(Notification.DEFAULT_ALL);

        if (!StringUtils.isNullOrEmpty(payload.getBody())) {
            builder.setStyle(new NotificationCompat.BigTextStyle()
                            .bigText(getSubHeading(payload.getBody())))
                    .setContentText(getSubHeading(payload.getBody()));
        }

        builder.setContentIntent(item.getPendingIntent());
        builder.setDeleteIntent(item.getDeletePendingIntent());

        if (!StringUtils.isNullOrEmpty(payload.getImageUrl())) {
            try {
                Bitmap bitmap = Picasso.get().load(payload.getImageUrl()).get();
                if (bitmap != null) {
                    NotificationCompat.BigPictureStyle style = new NotificationCompat.BigPictureStyle();
                    style.bigPicture(bitmap);
                    style.setBigContentTitle(payload.getTitle());
                    style.setSummaryText(payload.getBody());
                    builder.setStyle(style);
                    builder.setLargeIcon(bitmap);
                }
            } catch (IOException e) {
                NBLogger.e("Exception:", "DefaultNotificationBuilderAJO", e);
            }
        }

        setVisibility(builder, payload);
        return builder;
    }

    private static void setVisibility(final NotificationCompat.Builder notificationBuilder,
                                      final AjoPushPayloadDataModel payload) {
        final int visibility = payload.getNotificationVisibility();
        switch (visibility) {
            case NotificationCompat.VISIBILITY_PUBLIC ->
                    notificationBuilder.setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
            case NotificationCompat.VISIBILITY_SECRET ->
                    notificationBuilder.setVisibility(NotificationCompat.VISIBILITY_SECRET);
            default -> notificationBuilder.setVisibility(NotificationCompat.VISIBILITY_PRIVATE);
        }
    }
}
