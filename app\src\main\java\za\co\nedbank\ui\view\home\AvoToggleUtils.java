package za.co.nedbank.ui.view.home;

import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.ui.domain.model.AvoWalletDetailsModel;

public class AvoToggleUtils {

    private AvoToggleUtils() { }

    public static AvoToggleRedirection getAvoAppRederictionURL(FeatureSetController featureSetController,
                                                               AvoWalletDetailsModel result, boolean isBusinessUser) {
        String avoAppRedirectionUrl;
        boolean shouldOpenInWebView;
        if (isBusinessUser) {
            if (result.isAvoWalletAccount()) {
                avoAppRedirectionUrl = featureSetController.getDynamicFeatureValue(FeatureConstants.AVO_PWA_MERCHANT_LOGIN_URL);
                shouldOpenInWebView = !featureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_MERCHANT_WEB_VIEW_LOGIN);
            } else {
                avoAppRedirectionUrl = featureSetController.getDynamicFeatureValue(FeatureConstants.AVO_PWA_MERCHANT_REGISTER_URL);
                shouldOpenInWebView = !featureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_MERCHANT_WEB_VIEW_REGISTER);
            }
        } else {
            if (result.isAvoWalletAccount()) {
                avoAppRedirectionUrl = featureSetController.getDynamicFeatureValue(FeatureConstants.AVO_PWA_LOGIN_URL);
                shouldOpenInWebView = !featureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_WEB_VIEW_LOGIN);
            } else {
                avoAppRedirectionUrl = featureSetController.getDynamicFeatureValue(FeatureConstants.AVO_PWA_REGISTER_URL);
                shouldOpenInWebView = !featureSetController.isFeatureDisabled(FeatureConstants.FTR_AVO_WEB_VIEW_REGISTER);
            }
        }
        if (avoAppRedirectionUrl != null && !avoAppRedirectionUrl.startsWith(za.co.nedbank.core.Constants.HTTPS_STRING))
            avoAppRedirectionUrl = za.co.nedbank.core.Constants.HTTPS_STRING + avoAppRedirectionUrl;

        if (result.getAvoId() != null)
            avoAppRedirectionUrl = avoAppRedirectionUrl + za.co.nedbank.core.Constants.AVO_CODE_PARAM + result.getAvoId();
        else
            avoAppRedirectionUrl = avoAppRedirectionUrl + za.co.nedbank.core.Constants.CMPID_PARAM;

        return new AvoToggleRedirection(avoAppRedirectionUrl, shouldOpenInWebView);
    }

    public static class AvoToggleRedirection {
        private String avoAppRedirectionUrl;
        private boolean shouldOpenInWebView;

        public AvoToggleRedirection(String avoAppRedirectionUrl, boolean shouldOpenInWebView) {
            this.avoAppRedirectionUrl = avoAppRedirectionUrl;
            this.shouldOpenInWebView = shouldOpenInWebView;
        }

        public String getAvoAppRedirectionUrl() {
            return avoAppRedirectionUrl;
        }

        public boolean shouldOpenInWebView() {
            return shouldOpenInWebView;
        }
    }
}
