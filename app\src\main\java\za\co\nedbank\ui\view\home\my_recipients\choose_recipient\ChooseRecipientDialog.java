/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.my_recipients.choose_recipient;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import javax.inject.Inject;

import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.base.dialog.BaseDialogFragment;
import za.co.nedbank.databinding.FragmentDialogChooseRecipientBinding;
import za.co.nedbank.ui.di.AppDI;

/**
 * Created by charurani on 23-08-2017.
 */

public class ChooseRecipientDialog extends BaseDialogFragment implements ChooseRecipientDialogView,IChooseRecipientListItemListener {

    public static final String TAG = ChooseRecipientDialog.class.getCanonicalName();
    private static final String MODEL = "model";

    private IChooseRecipientDialogActivityInterface mDialogActivityInterface;
    private ChooseRecipientsViewModel mChooseRecipientsViewModel;
    private ChooseRecipientRecyclerAdapter mChooseRecipientRecyclerAdapter;

    @Inject
    ChooseRecipientDialogPresenter mPresenter;

    public static Fragment getInstance(Context context, ChooseRecipientsViewModel chooseRecipientsViewModel) {
        Bundle b = new Bundle();
        b.putParcelable(MODEL, chooseRecipientsViewModel);
        return Fragment.instantiate(context, ChooseRecipientDialog.class.getName(), b);
    }

    public void setDialogActivityInterface(IChooseRecipientDialogActivityInterface dialogActivityInterface) {
        mDialogActivityInterface = dialogActivityInterface;
    }

    @Nullable
    @Override
    public View onCreateView(final LayoutInflater inflater, @Nullable final ViewGroup container, @Nullable final Bundle savedInstanceState) {
        FragmentDialogChooseRecipientBinding binding = FragmentDialogChooseRecipientBinding.inflate(inflater, container, false);
        AppDI.getActivityComponent(((NBBaseActivity) getActivity())).inject(this);
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        if(getDialog().getWindow() != null){
            getDialog().getWindow().setDimAmount(0);
        }

        binding.recipientSelectRecylcerview.setItemAnimator(null);
        binding.recipientSelectRecylcerview.setHasFixedSize(true);
        binding.recipientSelectRecylcerview.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false));

        if(getArguments() != null){
            mChooseRecipientsViewModel = this.getArguments().getParcelable(MODEL);
            if (mChooseRecipientsViewModel != null) {
                mChooseRecipientRecyclerAdapter = new ChooseRecipientRecyclerAdapter(mChooseRecipientsViewModel,this);
                binding.recipientSelectRecylcerview.setAdapter(mChooseRecipientRecyclerAdapter);
            }
        }
        return binding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();
        mPresenter.bind(this);
    }

    @Override
    public void onPause() {
        super.onPause();
        mPresenter.unbind();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mDialogActivityInterface = null;
    }

    @Override
    public void dismissDialogWithData(ChooseRecipientsViewModel chooseRecipientsViewModel) {
        if (mDialogActivityInterface != null) {
            mDialogActivityInterface.onDialogDismiss(TAG);
            mDialogActivityInterface.receiveRecipientData(chooseRecipientsViewModel);
        }
        dismiss();
    }

    @Override
    public void selectedListItem(int position) {
        mPresenter.handleRecipientSelected(position);
    }

    @Override
    public ChooseRecipientsViewModel buildRecipientSelectedModel(int position){
        mChooseRecipientsViewModel.setCurrentBeneficiarySelection(position);
        return mChooseRecipientsViewModel;
    }
}
