package za.co.nedbank.ui.view.home.latest;

import android.util.Log;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.Constants;
import za.co.nedbank.core.ResponseStorageKey;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.common.CardType;
import za.co.nedbank.core.common.FeatureCardEnum;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.dashboard.HomeWidget;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetPreferredNameUseCase;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.domain.usecase.media_content.AppState;
import za.co.nedbank.core.domain.usecase.media_content.MediaContentUseCase;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.errors.HttpStatus;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.tracking.appsflyer.AddContextData;
import za.co.nedbank.core.tracking.appsflyer.AppsFlyerTags;
import za.co.nedbank.core.utils.AppUtility;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.model.media_content.AppLayoutViewModel;
import za.co.nedbank.core.view.model.media_content.MediaCardViewModel;
import za.co.nedbank.enroll_v2.tracking.EnrollTrackingParam.EnrollV2TrackingParam;
import za.co.nedbank.enroll_v2.tracking.EnrollV2TrackingEvent;

import static za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.ENABLE_JAVA_SCRIPT;
import static za.co.nedbank.core.utils.AppUtility.isValidWebUrl;
import static za.co.nedbank.enroll_v2.Constants.BundleKeys.CMS_URL;
import static za.co.nedbank.enroll_v2.Constants.BundleKeys.FROM_WIDGET;

public class HomeLatestPresenter extends NBBasePresenter<HomeLatestView> {
    private boolean isDynamicContentFeatureToggleEnabled;
    private MediaContentUseCase mMediaContentUseCase;
    private ErrorHandler mErrorHandler;
    private ApplicationStorage mApplicationStorage;
    private GetPreferredNameUseCase mGetPreferredNameUseCase;
    private ApplicationStorage mMemoryApplicationStorage;
    private UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper;
    private GetUserDetailUseCase mGetUserDetailUseCase;
    private NavigationRouter mNavigationRouter;
    private Analytics mAnalytics;
    private FeatureSetController mFeatureSetController;
    private final GetFedarationListUseCase getFedarationListUseCase;
    private List<AppLayoutViewModel> mMediaContentList;
    private final AFAnalyticsTracker mAfAnalyticsTracker;

    @Inject
    public HomeLatestPresenter(MediaContentUseCase mediaContentUseCase, ErrorHandler errorHandler,
                               ApplicationStorage applicationStorage, GetPreferredNameUseCase getPreferredNameUseCase,
                               @Named("memory") ApplicationStorage memoryApplicationStorage, UserDetailDataToViewModelMapper userDetailDataToViewModelMapper,
                               GetUserDetailUseCase getUserDetailUseCase, NavigationRouter navigationRouter, Analytics analytics,
                               FeatureSetController featureSetController, final GetFedarationListUseCase getFedarationListUseCase,
                               final AFAnalyticsTracker afAnalyticsTracker) {
        this.mMediaContentUseCase = mediaContentUseCase;
        this.mErrorHandler = errorHandler;
        this.mApplicationStorage = applicationStorage;
        this.mGetPreferredNameUseCase = getPreferredNameUseCase;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.mUserDetailDataToViewModelMapper = userDetailDataToViewModelMapper;
        this.mGetUserDetailUseCase = getUserDetailUseCase;
        this.mAnalytics = analytics;
        this.mNavigationRouter = navigationRouter;
        this.mFeatureSetController = featureSetController;
        this.getFedarationListUseCase = getFedarationListUseCase;
        this.mAfAnalyticsTracker = afAnalyticsTracker;

        setDynamicFeatureToggleState(!mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_CONTENT_FEATURES));
        mMediaContentUseCase.setIsDynamicFeatureEnabled(isDynamicContentFeatureToggleEnabled);
    }

    public boolean isNewFeatureTileEnable() {
        return !mFeatureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_NEWFEATURES_TILE_CONTROL);
    }


    public void loadMediaContent() {
        if (!mFeatureSetController.isFeatureDisabled(FeatureConstants.APP_LAYOUT_CMS_CONTENT)) {
            mMediaContentUseCase.execute(AppState.POST_LOGIN)
                    .compose(bindToLifecycle())
                    .doOnSubscribe(disposable -> {
                        showProgress(true);
                    })
                    .doOnTerminate(() -> {
                        showProgress(false);
                    })
                    .subscribe(mediaContentList -> {
                        mMediaContentList = mediaContentList;
                        showMediaAndOffer(mediaContentList);
                    }, throwable -> {
                        showMediaAndOffer(null);
                        if (view != null)
                            view.showLatestApiError(mErrorHandler.getErrorMessage(throwable).getMessage());
                    });
        } else if (view != null) {
            view.showMediaAndOfferCards(null);
        }
    }

    private void showMediaAndOffer(List<AppLayoutViewModel> mediaContentList) {
        if (view != null) {
            if (!isDynamicContentFeatureToggleEnabled) {
                view.showMediaAndOfferCards(addFeatureCard(mediaContentList));
            } else {
                view.showMediaAndOfferCards(mediaContentList);
            }
        }
    }

    private void showProgress(boolean isProgress) {
        if (view != null) {
            view.showProgress(isProgress);
        }
    }

    List<AppLayoutViewModel> addFeatureCard(List<AppLayoutViewModel> layoutModel) {
        AppLayoutViewModel appLayoutFeatureCard = getFeatureCards();
        if (view != null && !HomeWidget.COVID_19.equals(view.getFromWidgetStatus())) {
            if (layoutModel != null && !layoutModel.isEmpty()) {
                boolean isNedbankProductsExist = layoutModel.stream()
                        .anyMatch(model -> model.getCardType() == CardType.NEDBANK_PRODUCTS);
                if (isNedbankProductsExist) {
                    layoutModel.add(2, appLayoutFeatureCard);
                } else {
                    layoutModel.add(1, appLayoutFeatureCard);
                }
            } else {
                layoutModel = new ArrayList<>();
                layoutModel.add(appLayoutFeatureCard);
            }
        }
        return layoutModel;
    }

    private AppLayoutViewModel getFeatureCards() {
        AppLayoutViewModel featureCard = new AppLayoutViewModel();
        featureCard.setCardType(CardType.FEATURE_CARD);

        List<FeatureCardEnum> featureCardEnumList = new ArrayList<>();
        featureCardEnumList.add(FeatureCardEnum.AVO);
        featureCardEnumList.add(FeatureCardEnum.SALARY);
        featureCardEnumList.add(FeatureCardEnum.MY_POCKET);
        featureCardEnumList.add(FeatureCardEnum.GREENBACKS);
        featureCardEnumList.add(FeatureCardEnum.DEBIT_ORDERS);
        featureCardEnumList.add(FeatureCardEnum.INVESTMENTS);

        featureCard.setFeatureCardEnumList(featureCardEnumList);
        return featureCard;
    }

    public void getTotalBankingUnreadMessages() {
        int totalUnreadMessageCount = mApplicationStorage.getInteger(StorageKeys.BANKING_TOTAL_UNREAD_MESSAGE_COUNT, za.co.nedbank.core.Constants.ZERO);
        if (view != null) {
            view.updateChatIcon(totalUnreadMessageCount);
        }
    }

    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    public void onUnreadChatEvent(UnreadChatEvent unreadChatEvent) {
        if (view != null) {
            view.onUnreadChatEvent(unreadChatEvent);
        }
    }

    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    public void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent) {
        if (view != null) {
            view.onUnreadLifestyleChatEvent(unreadChatEvent);
        }
    }

    public void loadPreferredUserName() {
        mGetPreferredNameUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(
                        name -> {
                            if (view != null) {
                                view.setPreferredName(name);
                            }
                        },
                        error -> {
                            if (view != null) {
                                view.handlePreferredNameError();
                            }
                            NBLogger.e("DEBUG", "Error Getting Name", error);
                        }
                );
    }


    public void fetchUserDetail() {
        mGetUserDetailUseCase.execute(false)
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    if (userDetail != null && view != null) {
                        saveUserInfoData(userDetail);
                        view.setUserInfo(mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail));
                    }
                }, error -> {
                    if (view != null) {
                        Error errorModel = mErrorHandler.getErrorMessage(error);
                        if (errorModel.getCode() != HttpStatus.NO_CONTENT) {
                            view.showLatestApiError(errorModel.getMessage());
                        }
                    }
                });
    }

    private void saveUserInfoData(final UserDetailData userDetailData) {
        if (userDetailData != null && view != null) {
            mMemoryApplicationStorage.putObject(ResponseStorageKey.USERDETAILDATA,
                    mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData));
        }
    }

    public void loadCustomerNAme() {
        getFedarationListUseCase.execute().subscribe(fedarationList -> {
            if (view != null) {
                if (fedarationList != null && fedarationList.size() > 0
                        && !StringUtils.isNullOrEmpty(fedarationList.get(0).getCustName())) {
                    view.setCustomerName(StringUtils.removeTitles(fedarationList.get(0).getCustName()));
                } else {
                    view.handlePreferredNameError();
                }
            }
        }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    public void navigateToMediaDetail(String link, boolean fromWidget) {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CMS_MEDIA_CONTENT).withParam(CMS_URL, link).withParam(ENABLE_JAVA_SCRIPT, true).withParam(FROM_WIDGET, fromWidget));
    }

    public void logEventOnMediaCardClick(String mediaCard, int position) {
        trackCompaignInitiationOnAppsflyer(mediaCard);

        HashMap cData = new HashMap();
        AdobeContextData adobeContextData = new AdobeContextData(cData);
        adobeContextData.setFeatureCategory(TrackingParam.VAL_MARKETING_COMMUNICATION);
        adobeContextData.setFeature(TrackingParam.VAL_COMPAIGN);
        adobeContextData.setSubFeature(TrackingParam.VAL_MEDIA_CARDS);
        adobeContextData.setInternalCompaign(mediaCard);
        adobeContextData.setImpressions();
        adobeContextData.setSequence(String.format(TrackingEvent.ANALYTICS.VAL_MEDIA_TILE_SEQUENCE, position));
        if (view != null && HomeWidget.COVID_19.equals(view.getFromWidgetStatus())) {
            mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.WIDGET_LATEST_MEDIA_CARD, cData);
        } else {
            mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.KEY_LATEST_NON_TP_MEDIA_CARD, cData);
        }
    }

    void trackFeatureCards(String featureName) {
        if (!StringUtils.isNullOrEmpty(featureName)) {
            HashMap<String, String> cData = new HashMap<>();
            cData.put(TrackingEvent.ANALYTICS.KEY_NEDBANK_FEATURE, TrackingParam.VAL_CONTENT_OR_MEDIA_CARDS);
            cData.put(EnrollV2TrackingParam.ANALYTICS.KEY_VALUE, StringUtils.removeHtmlCharactersFromString(featureName));
            mAnalytics.sendEventActionWithMap(EnrollV2TrackingEvent.KEY_LATEST_NEW_FEATURES, cData);
        }
    }

    void navigateToFeatureCardDetail(FeatureCardEnum featureCardEnum) {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FEATURE_CARD_DETAIL)
                .withParam(Constants.SELECTED_FEATURE_CARD, featureCardEnum)
                .withParam(Constants.DYNAMIC_FEATURES_ENABLED, false));
    }

    void navigateToFeatureCardDetail(MediaCardViewModel selectedFeatureCardItemViewModel, HomeWidget widgetFlow) {
        String link = selectedFeatureCardItemViewModel.getRedirectUrl();
        if (isValidWebUrl(link)) {
            navigateToMediaDetail(link, widgetFlow != null);
        } else if (mMediaContentList != null) {
            List<MediaCardViewModel> filteredFeatureCardItemViewModelList = new ArrayList<>();
            List<MediaCardViewModel> list;
            for (AppLayoutViewModel appLayoutViewModel : mMediaContentList) {
                if (appLayoutViewModel.getCardType() == CardType.FEATURE_CARD) {
                    list = appLayoutViewModel.getDynamicFeatureCardViewModel();
                    filteredFeatureCardItemViewModelList = getFeatureCardList(list);
                }
            }
            navigationOnExpandedCTALink(selectedFeatureCardItemViewModel, filteredFeatureCardItemViewModelList);
        }

    }

    private void navigationOnExpandedCTALink(MediaCardViewModel selectedFeatureCardItemViewModel, List<MediaCardViewModel> filteredFeatureCardItemViewModelList) {
        if (!filteredFeatureCardItemViewModelList.isEmpty()) {
            mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.FEATURE_CARD_DETAIL)
                    .withParam(Constants.DYNAMIC_FEATURES_ENABLED, true)
                    .withParam(Constants.SELECTED_FEATURE_CARD, selectedFeatureCardItemViewModel)
                    .withParam(Constants.DYNAMIC_FEATURE_LIST, filteredFeatureCardItemViewModelList));
        }
    }

    private List<MediaCardViewModel> getFeatureCardList(List<MediaCardViewModel> list) {
        List<MediaCardViewModel> filteredFeatureCardItemViewModelList = new ArrayList<>();
        if (list != null) {
            for (MediaCardViewModel mediaCardViewModel : list) {
                if (!isValidWebUrl(mediaCardViewModel.getRedirectUrl())) {
                    filteredFeatureCardItemViewModelList.add(mediaCardViewModel);
                }
            }
        }
        return filteredFeatureCardItemViewModelList;
    }

    void setDynamicFeatureToggleState(boolean isDynamicContentFeatureToggleEnabled) {
        this.isDynamicContentFeatureToggleEnabled = isDynamicContentFeatureToggleEnabled;
    }

    boolean isDynamicFeatureToggleEnabled() {
        return isDynamicContentFeatureToggleEnabled;
    }


    private void navigateToExpendedContentPage(MediaCardViewModel mediaCardViewModel) {
        if (mediaCardViewModel != null) {
            boolean doNotShowAgain = mApplicationStorage.getBoolean(StorageKeys.DATA_USAGE_NEVER_ASK_AGAIN_HOME_SCREEN, false);

            if (StringUtils.isNullOrEmpty(mediaCardViewModel.getVideoId()) || doNotShowAgain) {
                showExpandedContent(mediaCardViewModel);
                return;
            }

            mNavigationRouter.navigateWithResult(NavigationTarget.to(NavigationTarget.DATA_USAGE_WARNING)
                            .withParam(Constants.FROM_SCREEN, Constants.HOME))
                    .subscribe(navigationResult -> {
                        boolean accepted = navigationResult.getBooleanParam(NavigationTarget.RESULT_DATA_USAGE_WARNING_ACCEPT);
                        if (accepted) {
                            showExpandedContent(mediaCardViewModel);
                        }
                    }, throwable -> NBLogger.e(getClass().getCanonicalName(), throwable.getMessage()));
        }
    }

    private void showExpandedContent(MediaCardViewModel mediaCardViewModel) {
        if (mediaCardViewModel == null) {
            return;
        }
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.EXPENDED_CONTENT_PAGE)
                .withParam(za.co.nedbank.enroll_v2.Constants.BundleKeys.EXTRA_MEDIA_CARD, mediaCardViewModel));
    }

    public void onClickContentItem(MediaCardViewModel mediaCardViewModel, HomeWidget widgetFlow) {
        if (mediaCardViewModel != null && mediaCardViewModel.getRedirectUrl() != null && (AppUtility.isValidWebUrl(mediaCardViewModel.getRedirectUrl()))) {
            navigateToMediaDetail(mediaCardViewModel.getRedirectUrl(), widgetFlow != null);
        } else {
            navigateToExpendedContentPage(mediaCardViewModel);
        }
    }

    public void trackCompaignInitiationOnAppsflyer(String compaignName) {
        HashMap<String, Object> cdata = new HashMap<>();
        AddContextData addContextData = new AddContextData(cdata);
        addContextData.setCompaignName(compaignName);

        mAfAnalyticsTracker.sendEvent(AppsFlyerTags.AF_COMPAIGN_INITIATION, cdata);
    }

    public void navigateToChatActivity() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CHAT_SCREEN));
    }

    public void navigateToChatBotActivity() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CONVO_CHAT_SCREEN).withIntentSingleTop(true));
    }

    public void navigateToChatBotIntroductionActivity() {
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CHATBOT_INTRODUCTION_SCREEN).withIntentSingleTop(true));
    }

}

