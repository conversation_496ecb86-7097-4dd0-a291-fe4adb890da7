package za.co.nedbank.ui.view.ita.qrcodelogin;

import androidx.annotation.StringRes;

import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.uisdk.widget.NBSnackbar;

interface QrCodeProcessingView extends NBBaseView {

    void showError(final String title,
                   final String message,
                   final String actionTitle,
                   final int duration,
                   final NBSnackbar.OnActionClickListener action);

    String getString(@StringRes int resId);

    String getIntentType();

    String getToken();
}
