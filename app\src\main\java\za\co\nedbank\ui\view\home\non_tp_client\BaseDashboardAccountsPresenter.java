package za.co.nedbank.ui.view.home.non_tp_client;

import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_BIRTH_DATE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_CLIENT_TYPE;
import static za.co.nedbank.core.navigation.NavigationTarget.PARAM_ONIA_SEC_OFFICER_CD;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.util.Log;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.inject.Named;

import io.reactivex.Notification;
import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Action;
import io.reactivex.functions.Consumer;
import io.reactivex.subjects.ReplaySubject;
import microsoft.aspnet.signalr.client.ConnectionState;
import za.co.nedbank.R;
import za.co.nedbank.core.ApiAliasConstants;
import za.co.nedbank.core.FicaWorkFlow;
import za.co.nedbank.core.ResponseStorageKey;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.concierge.chat.GlobalEventBus;
import za.co.nedbank.core.concierge.chat.model.LifestyleUnreadChatIconEvent;
import za.co.nedbank.core.concierge.chat.model.ServerStateChangedEvent;
import za.co.nedbank.core.concierge.chat.model.UnreadChatEvent;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.data.storage.StorageUtility;
import za.co.nedbank.core.domain.CachableValue;
import za.co.nedbank.core.domain.GetNonTpUserDetailUseCase;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.model.metadata.MetaDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDataModel;
import za.co.nedbank.core.domain.model.metadata.ResultDetailModel;
import za.co.nedbank.core.domain.model.notifications.FBNotificationCountResponseData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsCountRequestData;
import za.co.nedbank.core.domain.model.preapprovedoffers.GetPreApprovedOfferRequestData;
import za.co.nedbank.core.domain.model.preapprovedoffers.view_preapproved_offers.PreApprovedOffersData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.GetPreferredNameUseCase;
import za.co.nedbank.core.domain.usecase.enrol.sbs.GetFedarationListUseCase;
import za.co.nedbank.core.domain.usecase.investmentonline.GetFicaStatusUseCase;
import za.co.nedbank.core.domain.usecase.media_content.AppState;
import za.co.nedbank.core.domain.usecase.media_content.AvoMediaContentUseCase;
import za.co.nedbank.core.domain.usecase.notifications.GetFBNotificationsCountExtendedUseCase;
import za.co.nedbank.core.domain.usecase.preapprovedoffers.GetPreApprovedOfferUseCase;
import za.co.nedbank.core.errors.Error;
import za.co.nedbank.core.errors.ErrorHandler;
import za.co.nedbank.core.errors.HttpStatus;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationResult;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.networking.entersekt.DtoHelper;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.ChatTracking;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.tracking.appsflyer.AFAnalyticsTracker;
import za.co.nedbank.core.tracking.appsflyer.AddContextData;
import za.co.nedbank.core.tracking.appsflyer.AppsFlyerTags;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.mapper.preapprovedoffers.PreApprovedOffersDataToViewModelMapper;
import za.co.nedbank.core.view.preapprovedoffers.PreApprovedOffersUtility;
import za.co.nedbank.core.view.preapprovedoffers.view_preapproved_offers.PreApprovedOffersDetailsViewModel;
import za.co.nedbank.core.view.preapprovedoffers.view_preapproved_offers.PreApprovedOffersInnerDetailsViewModel;
import za.co.nedbank.core.view.preapprovedoffers.view_preapproved_offers.PreApprovedOffersViewModel;
import za.co.nedbank.enroll_v2.domain.usecases.fica.AcquisitionUseCase;
import za.co.nedbank.enroll_v2.view.fica.error.FicaErrorHandler;
import za.co.nedbank.enroll_v2.view.mapper.fica.AcquisitionUseCaseRequestViewModelToDataMapper;
import za.co.nedbank.enroll_v2.view.mapper.fica.AcquisitionUseCaseResponseDataToViewModelMapper;
import za.co.nedbank.loans.preapprovedaccountsoffers.Constants;
import za.co.nedbank.loans.preapprovedaccountsoffers.navigator.PreApprovedOffersNavigationTarget;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.CreditHealthTrackingEvent;
import za.co.nedbank.loans.preapprovedaccountsoffers.tracking.CreditHealthTrackingValue;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.common.OfferNavigationModel;
import za.co.nedbank.loans.preapprovedaccountsoffers.view.common.offer_combination.OfferCategory;
import za.co.nedbank.payment.ngi.NGIConstants;
import za.co.nedbank.profile.view.navigation.ProfileNavigationTarget;
import za.co.nedbank.profile.view.tracking.ProfileTracking;
import za.co.nedbank.services.domain.mapper.PendingAccountsEntityToDataModelMapper;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.investmentnotices.PendingAccountDataModel;
import za.co.nedbank.services.domain.model.investmentnotices.PendingAccountListDataModel;
import za.co.nedbank.services.domain.model.overview.AccountsOverview;
import za.co.nedbank.services.domain.model.overview.InvestmentAccountSummeryViewModel;
import za.co.nedbank.services.domain.model.overview.Overview;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.usecase.investmentnotices.GetPendingAccountsUsecase;
import za.co.nedbank.services.domain.usecase.overview.GetOverviewUseCase;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.services.view.tracking.ServicesTracking;
import za.co.nedbank.ui.domain.model.AvoWalletDetailsModel;
import za.co.nedbank.ui.domain.usecase.GetAvoWalletDetailsUseCase;
import za.co.nedbank.ui.view.home.AvoToggleUtils;
import za.co.nedbank.ui.view.tracking.AppTracking;

public abstract class BaseDashboardAccountsPresenter<V extends BaseDashboardAccountsView> extends NBBasePresenter<BaseDashboardAccountsView> {
    protected static final String TAG = BaseDashboardAccountsPresenter.class.getSimpleName();
    protected final GetPreferredNameUseCase getPreferredNameUseCase;
    protected final GetOverviewUseCase getOverviewUseCase;
    protected final ApplicationStorage mMemoryApplicationStorage;
    protected NavigationResult mNavigationResult;
    protected final GetFBNotificationsCountExtendedUseCase fbNotificationsCountExtendedUseCase;
    protected final GetNonTpUserDetailUseCase getNonTpUserDetailUseCase;
    protected final UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper;
    protected final GetFicaStatusUseCase getFicaStatusUseCase;
    private final GetAvoWalletDetailsUseCase mWalletDetailsUseCase;
    protected final AFAnalyticsTracker mAfAnalyticsTracker;
    private final AvoMediaContentUseCase mMediaContentUseCase;

    protected final NavigationRouter navigationRouter;
    protected final ErrorHandler errorHandler;
    protected final Analytics analytics;
    protected final FeatureSetController featureSetController;
    protected final ApplicationStorage mApplicationStorage;
    protected final StorageUtility storageUtility;
    protected final FicaErrorHandler ficaErrorHandler;
    protected final AcquisitionUseCase acquisitionUseCase;
    protected final AcquisitionUseCaseResponseDataToViewModelMapper acquisitionUseCaseResponseDataToViewModelMapper;
    protected final AcquisitionUseCaseRequestViewModelToDataMapper acquisitionUseCaseRequestViewModelToDataMapper;

    protected String clientType;
    protected String birthDate;
    protected String secOfficerID;
    protected String cisNumber;
    protected String idOrTaxIdNo;
    protected boolean mIsBusinessUser;
    protected int investmentAccountCount;
    private int notificationCount;
    private boolean isViewUpdatedWithPositiveCount;
    private static final int FINANCIAL_WELLNESS_POSITION = 0;
    private boolean mIsFinancialWellnessAvailable = false;
    private boolean mLandOnFinancialWellnessCarousel = false;
    private final GetFedarationListUseCase getFedarationListUseCase;
    private final GetPreApprovedOfferUseCase mGetPreApprovedOffersUseCase;
    private final PreApprovedOffersDataToViewModelMapper mPreApprovedOffersDataToViewModelMapper;
    private double savvyBundleAmountOffered;
    private int savvyBundleDisplayIndex = -1;
    private PreApprovedOffersViewModel preApprovedOffersViewModel;
    private PreApprovedOffersInnerDetailsViewModel mSavvyBundleOffer;
    private InvestmentAccountSummeryViewModel investmentAccountSummeryViewModel;
    private final GetPendingAccountsUsecase getPendingAccountsUsecase;
    private PendingAccountListDataModel pendingAccountListDataModel;
    private final PendingAccountsEntityToDataModelMapper pendingAccountsEntitiyToDataModelMapper;



    public BaseDashboardAccountsPresenter(final GetPreferredNameUseCase getPreferredNameUseCase,
                                          final GetOverviewUseCase getOverviewUseCase,
                                          final NavigationRouter navigationRouter,
                                          final ErrorHandler errorHandler,
                                          final Analytics analytics,
                                          final StorageUtility storageUtility,
                                          final FeatureSetController featureSetController,
                                          final ApplicationStorage applicationStorage,
                                          @Named("memory") final ApplicationStorage memoryApplicationStorage,
                                          final GetFBNotificationsCountExtendedUseCase getFBNotificationsCountExtendedUseCase,
                                          final GetNonTpUserDetailUseCase getNonTpUserDetailUseCase,
                                          final UserDetailDataToViewModelMapper userDetailDataToViewModelMapper,
                                          final GetFicaStatusUseCase getFicaStatusUseCase,
                                          final FicaErrorHandler ficaErrorHandler, AcquisitionUseCase acquisitionUseCase,
                                          final AcquisitionUseCaseResponseDataToViewModelMapper acquisitionUseCaseResponseDataToViewModelMapper,
                                          final AcquisitionUseCaseRequestViewModelToDataMapper acquisitionUseCaseRequestViewModelToDataMapper,
                                          final GetFedarationListUseCase getFedarationListUseCase,
                                          final GetAvoWalletDetailsUseCase walletDetailsUseCase,
                                          final AFAnalyticsTracker afAnalyticsTracker,
                                          final PendingAccountsEntityToDataModelMapper pendingAccountsEntitiyToDataModelMapper,
                                          final GetPendingAccountsUsecase getPendingAccountsUsecase,
                                          final AvoMediaContentUseCase mediaContentUseCase, GetPreApprovedOfferUseCase getPreApprovedOffersUseCase,
                                          PreApprovedOffersDataToViewModelMapper preApprovedOffersDataToViewModelMapper) {
        this.getPreferredNameUseCase = getPreferredNameUseCase;
        this.getOverviewUseCase = getOverviewUseCase;
        this.navigationRouter = navigationRouter;
        this.errorHandler = errorHandler;
        this.analytics = analytics;
        this.storageUtility = storageUtility;
        this.featureSetController = featureSetController;
        this.mApplicationStorage = applicationStorage;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.fbNotificationsCountExtendedUseCase = getFBNotificationsCountExtendedUseCase;
        this.getNonTpUserDetailUseCase = getNonTpUserDetailUseCase;
        this.mUserDetailDataToViewModelMapper = userDetailDataToViewModelMapper;
        this.getFicaStatusUseCase = getFicaStatusUseCase;
        this.ficaErrorHandler = ficaErrorHandler;
        this.acquisitionUseCase = acquisitionUseCase;
        this.acquisitionUseCaseRequestViewModelToDataMapper = acquisitionUseCaseRequestViewModelToDataMapper;
        this.acquisitionUseCaseResponseDataToViewModelMapper = acquisitionUseCaseResponseDataToViewModelMapper;
        this.getFedarationListUseCase = getFedarationListUseCase;
        this.mWalletDetailsUseCase = walletDetailsUseCase;
        this.mAfAnalyticsTracker = afAnalyticsTracker;
        this.mMediaContentUseCase = mediaContentUseCase;
        this.mGetPreApprovedOffersUseCase = getPreApprovedOffersUseCase;
        this.getPendingAccountsUsecase = getPendingAccountsUsecase;
        this.pendingAccountsEntitiyToDataModelMapper = pendingAccountsEntitiyToDataModelMapper;
        this.mPreApprovedOffersDataToViewModelMapper = preApprovedOffersDataToViewModelMapper;
    }

    @Override
    protected void onBind() {
        super.onBind();
        GlobalEventBus.getBus().register(this);

        if (mApplicationStorage.getInteger(StorageKeys.LIFESTYLE_TOTAL_UNREAD_MESSAGE_COUNT, za.co.nedbank.core.Constants.ZERO) > za.co.nedbank.core.Constants.ZERO) {
            GlobalEventBus.getBus().post(new LifestyleUnreadChatIconEvent(true));
        } else {
            GlobalEventBus.getBus().post(new LifestyleUnreadChatIconEvent(false));
        }
    }

    @Override
    protected void onUnbind() {
        super.onUnbind();
        GlobalEventBus.getBus().unregister(this);
    }

    public void onAccountsLoading(boolean loading) {
    }

    public void onAccountsLoaded(Overview overview, boolean isContinue) {
    }

    public void onAccountsLoadingError(String errorMsg) {
    }

    protected void onReceivePreApprovedOffersCount(int notificationCount) {
    }

    public String getFicaStatusValue() {
        return null;
    }

    public void onFicaStatusError(String msg) {
    }

    public abstract boolean canTransact();

    public abstract void onChatIconCount(int chatIconCount);


    private void getUserInfoData(final UserDetailData userDetailData) {
        if (userDetailData != null && view != null) {
            view.setUserInfo(mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData), Boolean.TRUE);
            mMemoryApplicationStorage.putObject(ResponseStorageKey.USERDETAILDATA,
                    mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData));
        }
    }

    public void loadPreferredUserName() {
        getPreferredNameUseCase
                .execute()
                .compose(bindToLifecycle())
                .subscribe(
                        name -> {
                            if (view != null) {
                                view.setPreferredName(name);
                            }
                        },
                        error -> {
                            if (view != null) {
                                view.handlePreferredNameError();
                            }
                            NBLogger.e("DEBUG", "Error Getting Name", error);
                        }
                );
    }


    public void fetchUserDetail(boolean updateCustomerName) {
        getNonTpUserDetailUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(userDetail -> {
                    if (userDetail != null && view != null) {
                        clientType = userDetail.getClientType();
                        secOfficerID = userDetail.getSecOfficerCd();
                        birthDate = userDetail.getBirthDate();
                        cisNumber = userDetail.getCisNumber();
                        idOrTaxIdNo = userDetail.getIdOrTaxIdNumber();
                        mIsBusinessUser = userDetail.getClientType() != null && Integer.parseInt(userDetail.getClientType())
                                > za.co.nedbank.services.Constants.BUSINESS_USER_PROFILE_CHECK_LIMIT;
                        saveUserInfoData(userDetail);
                        view.setUserInfo(mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetail), updateCustomerName);
                    }
                }, error -> {
                    if (view != null) {
                        Error errorModel = errorHandler.getErrorMessage(error);
                        if (errorModel.getCode() == HttpStatus.NO_CONTENT) {
                            view.showEmptyUserView(errorModel.getMessage());
                        } else {
                            view.showError(errorModel.getMessage());
                        }
                    }
                });
    }

    private void saveUserInfoData(final UserDetailData userDetailData) {
        if (userDetailData != null && view != null) {
            mMemoryApplicationStorage.putObject(ResponseStorageKey.USERDETAILDATA,
                    mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(userDetailData));
        }
    }

    public void loadCustomerNAme() {
        getFedarationListUseCase.execute().subscribe(fedarationList -> {
            if (view != null) {
                if (fedarationList != null && fedarationList.size() > 0
                        && !StringUtils.isNullOrEmpty(fedarationList.get(0).getCustName())) {
                    view.setCustomerName(StringUtils.removeTitles(fedarationList.get(0).getCustName()));
                } else {
                    view.handlePreferredNameError();
                }
            }
        }, throwable -> NBLogger.e(za.co.nedbank.core.Constants.TAG, Log.getStackTraceString(throwable)));
    }

    private boolean isPreApprovedOffer() {
        return (view != null && !view.isFeatureDisabled(FeatureConstants.PRE_APPROVED_ACCOUNTS));
    }

    private void checkViewAndThrowException(Throwable throwable) {
        if (view != null) {
            onAccountsLoading(false);
            onAccountsLoadingError(errorHandler.getErrorMessage(throwable).getMessage());
        }
    }

    public void loadAccountsData() {
        onAccountsLoading(true);
        //resetting bell icon animation flag
        isViewUpdatedWithPositiveCount = false;
        Observable<CachableValue<Overview>> overviewObservable = getOverviewUseCase.execute();
        if (isPreApprovedOffer()) {
            /*Notification unread count required for Notification MVP*/
            savvyBundleAmountOffered = 0;
            Observable<PreApprovedOffersData> preApprovedOffersDataObservable = mGetPreApprovedOffersUseCase.execute(new GetPreApprovedOfferRequestData(za.co.nedbank.core.Constants.PreApprovedOffersProductCode.ALL.getProductCode(), null));

            ReplaySubject<Boolean> isPreApprovedDataAvailable = ReplaySubject.create();
            isPreApprovedDataAvailable.onNext(false);
            ReplaySubject<Boolean> isOverviewDataAvailable = ReplaySubject.create();
            isOverviewDataAvailable.onNext(false);
            ReplaySubject<Overview> overviewReplaySubject = ReplaySubject.create();
            Observable<Boolean> overViewAndOfferCountObservable =
                    Observable.merge(
                            overviewObservable
                                    .doOnError(this::checkViewAndThrowException),
                            preApprovedOffersDataObservable
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .doOnNext(preApprovedOffersData -> {
                                        preApprovedOffersViewModel = mPreApprovedOffersDataToViewModelMapper.transform(preApprovedOffersData);
                                        calculationsOnOfferModel();
                                    })
                                    .map(preApprovedOffersData -> preApprovedOffersViewModel)
                                    .onErrorReturnItem(new PreApprovedOffersViewModel(new PreApprovedOffersDetailsViewModel())))
                            .doFinally(new Action() {
                                @Override
                                public void run() throws Exception {
                                    isPreApprovedDataAvailable.onComplete();
                                    isOverviewDataAvailable.onComplete();
                                }
                            }).compose(bindToLifecycle())
                            .doOnEach(new Consumer<Object>() {
                                @Override
                                public void accept(Object o) throws Exception {
                                    NBLogger.e(TAG, o + "");
                                    if (isCachableValue(o)) {
                                        isOverviewDataAvailable.onNext(true);
                                        try {
                                            CachableValue<Overview> overview = (CachableValue<Overview>) ((Notification) o).getValue();
                                            Overview overviewValue = overview.get().clone();
                                            overviewReplaySubject.onNext(overview.get().clone());
                                            NBLogger.e(TAG, "overviewreplaysubject " + isPreApprovedDataAvailable.getValue());
                                            operationOnCachebleObject(isPreApprovedDataAvailable, overviewValue, overview.isContinued());

                                        } catch (Exception e) {
                                            onAccountsLoading(false);
                                            onAccountsLoadingError(errorHandler.getErrorMessage(e).getMessage());
                                        }
                                    } else if (isPreApprovedViewModelInstance(o)) {
                                        isPreApprovedDataAvailable.onNext(true);
                                        NBLogger.e(TAG, "isPreApprovedDataAvailable " + isOverviewDataAvailable.getValue() + " " +
                                                overviewReplaySubject.hasValue());
                                        overViewDataManupulation(isOverviewDataAvailable, overviewReplaySubject);
                                    }
                                }

                            })
                            .map(o -> o != null);
            overViewAndOfferCountObservable.subscribe(isResponseReceivedFromApis -> {
                //response already sent to view above
                NBLogger.e(TAG, "view.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS) success");
            }, throwable -> {
                //errors already passed to view above
                NBLogger.e(TAG, throwable.getMessage());
            });
        }
        else {
            overviewObservable
                    .compose(bindToLifecycle())
                    .subscribe(
                            overview -> {
                                getPendingAccounts(overview.get(), overview.isContinued());
                            },
                            error -> {
                                onAccountsLoading(false);
                                onAccountsLoadingError(errorHandler.getErrorMessage(error).getMessage());
                            });
        }

        pushNotificationSetter();
    }

    void getPendingAccounts(Overview overviewValue, boolean continued) {
        getPendingAccountsUsecase
                .execute(za.co.nedbank.core.Constants.API_PARAM_OPEN_ACCOUNT, za.co.nedbank.core.Constants.API_PARAM_PENDING)
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                })
                .doOnTerminate(() -> {
                })
                .subscribe(getPendingAccountDataListEntity -> {
                    pendingAccountListDataModel = pendingAccountsEntitiyToDataModelMapper.
                            getPendingAccountsDataModels(getPendingAccountDataListEntity);
                    handleOverviewData(overviewValue, continued);
                }, throwable -> {
                    if (view != null) {
                        handleOverviewData(overviewValue, continued);
                    }
                });
    }

    private void operationOnCachebleObject(ReplaySubject<Boolean> isPreApprovedDataAvailable, Overview overviewValue, boolean continued) {
        if (isPreApproveAvailableMethod(isPreApprovedDataAvailable)) {
            addOfferToOverview(overviewValue);
            getPendingAccounts(overviewValue,continued);
        } else if (isOverviewEmpty(overviewValue)) {
            handleOverviewData(overviewValue, continued);
        }
    }

    private boolean isPreApproveAvailableMethod(ReplaySubject<Boolean> isPreApprovedDataAvailable) {
        return isPreApprovedDataAvailable.getValue();
    }

    private void overViewDataManupulation(ReplaySubject<Boolean> isOverviewDataAvailable, ReplaySubject<Overview> overviewReplaySubject) {
        if (isOverviewDataAvailable.getValue() && overviewReplaySubject.hasValue()) {
            Overview overviewValue = overviewReplaySubject.getValue();
            addOfferToOverview(overviewValue);
            getPendingAccounts(overviewValue, false);

        }
    }

    public boolean isNotificationPresent() {
        int totalUnreadNotificationCount = mApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, 0);
        return totalUnreadNotificationCount > 0;
    }

    private void pushNotificationSetter() {
        if (!isNotificationPresent() && !view.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS)) {
            getPushNotificationCount();
        }
    }

    private boolean isOverviewEmpty(Overview overviewValue) {
        return (overviewValue.accountsOverviews != null && overviewValue.accountsOverviews.size() > 0);
    }

    private boolean isNotificationInstance(Object o) {
        return (o instanceof Notification && ((Notification) o).isOnNext());
    }

    private boolean isPreApprovedViewModelInstance(Object o) {
        return (isNotificationInstance(o) && (((Notification) o).getValue() instanceof PreApprovedOffersViewModel));
    }

    private boolean isCachableValue(Object o) {
        return (isNotificationInstance(o) && (((Notification) o).getValue() instanceof CachableValue));
    }

    private void calculationsOnOfferModel() {
        if (preApprovedOffersViewModel != null && preApprovedOffersViewModel.getPreApprovedOffersMetadataViewModel() != null) {

            String errorMessageIfAny = PreApprovedOffersUtility.extractErrorFromMetaData(preApprovedOffersViewModel.getPreApprovedOffersMetadataViewModel());
            if (StringUtils.isNullOrEmpty(errorMessageIfAny) && isValidForCalculateMax()) {
                calculateMaximumLoanAmountOfferedUnread(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList());
            }
            if (StringUtils.isNullOrEmpty(errorMessageIfAny) && savvyBundleAmountOffered <= 0) {
                extractPreApprovedOfferForPersistentDisplay(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList(),
                        za.co.nedbank.core.Constants.EverdayBankingOffers.SAVVY_BUNDLE, za.co.nedbank.core.Constants.EverdayBankingOffers.SAVVY_PLUS);
            }

        }
    }

    private boolean isValidForCalculateMax() {
        return (preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel() != null
                && preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList() != null
                && preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().size() > 0);
    }

    private boolean isPreApproveInnerListExist(List<PreApprovedOffersInnerDetailsViewModel> preApprovedOffersInnerDetailsViewModelList) {
        return (preApprovedOffersInnerDetailsViewModelList != null && preApprovedOffersInnerDetailsViewModelList.size() > 0);
    }

    private void calculateMaximumLoanAmountOfferedUnread(List<PreApprovedOffersInnerDetailsViewModel> preApprovedOffersInnerDetailsViewModelList) {
        double maximumSavvyBundleAmount = 0.0;

        if (isPreApproveInnerListExist(preApprovedOffersInnerDetailsViewModelList)) {
            PreApprovedOffersInnerDetailsViewModel preApprovedOffersInnerDetailsViewModel;
            for (int count = 0; count < preApprovedOffersInnerDetailsViewModelList.size(); count++) {
                preApprovedOffersInnerDetailsViewModel = preApprovedOffersInnerDetailsViewModelList.get(count);
                if (StringUtils.isNullOrEmpty(preApprovedOffersInnerDetailsViewModel.getStatus()) && preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersAmountViewModel() != null) {
                    if (!StringUtils.isNullOrEmpty(preApprovedOffersInnerDetailsViewModel.getMessage())) {
                        preApprovedOffersInnerDetailsViewModel.setMessage(removeAllHTMLFormatCodes(preApprovedOffersInnerDetailsViewModel.getMessage()));
                    }
                    maximumSavvyBundleAmount = maxAmount(preApprovedOffersInnerDetailsViewModel, count, maximumSavvyBundleAmount);

                }
            }
        }

        setSavvyBundleMaxAmount(maximumSavvyBundleAmount);

    }

    private double maxAmount(PreApprovedOffersInnerDetailsViewModel preApprovedOffersInnerDetailsViewModel, int count, double maximumSavvyBundleAmount) {
        double loanAmount = preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersAmountViewModel().getAmount();

        if ((preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel() != null && loanAmount > 0) && isOfferHasCode(preApprovedOffersInnerDetailsViewModel) && (maximumSavvyBundleAmount < loanAmount)) {
            savvyBundleDisplayIndex = count;
            maximumSavvyBundleAmount = loanAmount;
        }


        return maximumSavvyBundleAmount;
    }

    private void setSavvyBundleMaxAmount(double maximumSavvyBundleAmount) {
        if (maximumSavvyBundleAmount > 0) {
            savvyBundleAmountOffered = maximumSavvyBundleAmount;
        }
    }

    private boolean isOfferHasCode(PreApprovedOffersInnerDetailsViewModel preApprovedOffersInnerDetailsViewModel) {
        return (!TextUtils.isEmpty(preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel().getCode()) &&
                za.co.nedbank.core.Constants.EverdayBankingOffers.SAVVY_BUNDLE.equals(preApprovedOffersInnerDetailsViewModel.
                        getPreApprovedOffersOfferTypeViewModel().getCode()));
    }

    private String removeAllHTMLFormatCodes(String message) {
        if (!StringUtils.isNullOrEmpty(message)) {
            message = message.replaceAll(Constants.NON_BREAKING_SPACE_HTML_STRING, StringUtils.SPACE);
            message = message.replaceAll(Constants.START_AMOUNT_HTML_STRING, StringUtils.SPACE);
            message = message.replaceAll(Constants.END_AMOUNT_HTML_STRING, StringUtils.SPACE);
        }
        return message;
    }

    private boolean isPreApprovedInnerDetailModelExist(Overview overviewValue) {
        return (overviewValue.accountsOverviews != null && overviewValue.accountsOverviews.size() > 0
                && preApprovedOffersViewModel != null
                && preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel() != null
                && preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList() != null
                && preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().size() > 0);
    }

    private boolean isEverydayBanking(AccountsOverview accountsOverview) {
        return accountsOverview.overviewType.getValue() == OverviewType.EVERYDAY_BANKING.getValue();
    }

    private boolean savvyBundleValueExist() {
        return (mSavvyBundleOffer != null && mSavvyBundleOffer.getPreApprovedOffersOfferTypeViewModel() != null && mSavvyBundleOffer.getPreApprovedOffersAmountViewModel() != null);
    }

    private void addOfferToOverview(Overview overviewValue) {
        if (isPreApprovedInnerDetailModelExist(overviewValue)) {
            boolean isSavvyBundleOfferExisting = false;
            for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
                isSavvyBundleOfferExisting = isEverydayBanking(accountsOverview);


                if (isSavvyBundleOfferExisting && savvyBundleAmountOffered > 0) {
                    accountsOverview.accountSummaries.add(0, getPreApprovedOfferAccountSummary(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).getId(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).getPreApprovedOffersOfferTypeViewModel().getId(),
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).getPreApprovedOffersOfferTypeViewModel().getCode(),
                            OverviewType.EVERYDAY_BANKING, preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).getShortMessage(), savvyBundleAmountOffered,
                            preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).isDebiCheckMandateEnable()));

                } else if (isSavvyBundleOfferExisting && savvyBundleValueExist()) {
                    accountsOverview.accountSummaries.add(0, getPreApprovedOfferAccountSummary(mSavvyBundleOffer.getId(), mSavvyBundleOffer.getPreApprovedOffersOfferTypeViewModel().getId(), mSavvyBundleOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                            OverviewType.EVERYDAY_BANKING, mSavvyBundleOffer.getShortMessage(), mSavvyBundleOffer.getPreApprovedOffersAmountViewModel().getAmount(),
                            mSavvyBundleOffer.isDebiCheckMandateEnable(), mSavvyBundleOffer.getOfferCategoryViewModel().getId()));
                }
            }

            if (!isSavvyBundleOfferExisting && savvyBundleAmountOffered > 0) {
                overviewValue.accountsOverviews.add(0, getPreApprovedOfferAccountOverView(preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).getId(),
                        preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).getPreApprovedOffersOfferTypeViewModel().getId(),
                        preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).getPreApprovedOffersOfferTypeViewModel().getCode(),
                        OverviewType.EVERYDAY_BANKING, preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).getShortMessage(), savvyBundleAmountOffered,
                        preApprovedOffersViewModel.getPreApprovedOffersDetailsViewModel().getPreApprovedOffersInnerDetailsViewModelList().get(savvyBundleDisplayIndex).isDebiCheckMandateEnable()));
            } else if (!isSavvyBundleOfferExisting && savvyBundleValueExist()) {
                overviewValue.accountsOverviews.add(0, getPreApprovedOfferAccountOverView(mSavvyBundleOffer.getId(), mSavvyBundleOffer.getPreApprovedOffersOfferTypeViewModel().getId(), mSavvyBundleOffer.getPreApprovedOffersOfferTypeViewModel().getCode(),
                        OverviewType.EVERYDAY_BANKING, mSavvyBundleOffer.getShortMessage(), mSavvyBundleOffer.getPreApprovedOffersAmountViewModel().getAmount(),
                        mSavvyBundleOffer.isDebiCheckMandateEnable(), mSavvyBundleOffer.getOfferCategoryViewModel().getId()));
            }
        }

    }

    public AccountsOverview getPreApprovedOfferAccountOverView(long offerId,
                                                               long offerTypeId,
                                                               String offerType,
                                                               OverviewType overviewType,
                                                               String preApprovedOfferType,
                                                               double amountOffered,
                                                               boolean isDebiCheckEnabled,
                                                               @OfferCategory int... offerCategory) {
        AccountsOverview loanAccountsOverview = new AccountsOverview();
        loanAccountsOverview.overviewType = overviewType;
        loanAccountsOverview.accountSummaries.add(getPreApprovedOfferAccountSummary(offerId, offerTypeId, offerType, overviewType, preApprovedOfferType, amountOffered, isDebiCheckEnabled, offerCategory));
        return loanAccountsOverview;
    }

    public AccountSummary getPreApprovedOfferAccountSummary(long offerId,
                                                            long offerTypeId,
                                                            String offerType,
                                                            OverviewType overviewType,
                                                            String preApprovedOfferType,
                                                            double amountOffered,
                                                            boolean isDebiCheckEnabled,
                                                            @OfferCategory int... offerCategory) {
        AccountSummary accountSummary = new AccountSummary();
        //set offerId, offerTypeId offerType and to account summary object
        accountSummary.setId(String.valueOf(offerId));
        accountSummary.setNumber(String.valueOf(offerTypeId));
        accountSummary.setPreApprovedOfferType(offerType);
        accountSummary.setDebicheckMandateEnabled(isDebiCheckEnabled);
        accountSummary.setPreApprovedOfferAccount(true);
        accountSummary.setAccountType(overviewType);
        if (view != null) {
            accountSummary.setName(preApprovedOfferType);
        } else {
            accountSummary.setName(StringUtils.EMPTY_STRING);
        }
        accountSummary.setSummaryValue(BigDecimal.valueOf(amountOffered));
        if (offerCategory != null && offerCategory.length != 0)
            accountSummary.setOfferCategory(offerCategory[0]);
        return accountSummary;
    }

    private boolean checkOfferTypeArgs(String... offerTypeArgs) {
        return (offerTypeArgs != null && offerTypeArgs.length > 0);
    }

    @SuppressLint("CheckResult")
    private void extractPreApprovedOfferForPersistentDisplay(List<PreApprovedOffersInnerDetailsViewModel> preApprovedOffersInnerDetailsViewModelList, String... offerTypeArgs) {
        Observable<PreApprovedOffersInnerDetailsViewModel> preApprovedOfferInnerViewModelObservable = obtainObservableForPreApprovedOfferForPersistentDisplay(preApprovedOffersInnerDetailsViewModelList, offerTypeArgs);
        preApprovedOfferInnerViewModelObservable
                .compose(bindToLifecycle())
                .subscribe(preApprovedOffersInnerDetailsViewModel -> {
                    if (preApprovedOffersInnerDetailsViewModel != null && checkOfferTypeArgs(offerTypeArgs)) {
                        for (String offerType : offerTypeArgs) {
                            if (za.co.nedbank.core.Constants.EverdayBankingOffers.SAVVY_BUNDLE.equals(offerType)) {
                                mSavvyBundleOffer = preApprovedOffersInnerDetailsViewModel;
                                break;
                            }
                        }

                    }
                }, throwable -> {
                    NBLogger.e(TAG, throwable.getMessage());
                });
    }

    @SuppressLint("CheckResult")
    private Observable<PreApprovedOffersInnerDetailsViewModel> obtainObservableForPreApprovedOfferForPersistentDisplay(List<PreApprovedOffersInnerDetailsViewModel> preApprovedOffersInnerDetailsViewModelList, String... offerTypeArgs) {
        Observable<PreApprovedOffersInnerDetailsViewModel> preApprovedOfferInnerViewModelObservable = Observable.fromIterable(preApprovedOffersInnerDetailsViewModelList)
                .filter(preApprovedOffersInnerDetailsViewModel -> {
                    boolean isOfferTypeMatch = false;
                    if (preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel() != null && !StringUtils.isNullOrEmpty(preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel().getCode())
                            && offerTypeArgs != null && offerTypeArgs.length > 0) {
                        for (String offerType : offerTypeArgs) {
                            if (preApprovedOffersInnerDetailsViewModel.getPreApprovedOffersOfferTypeViewModel().getCode().equalsIgnoreCase(offerType)) {
                                isOfferTypeMatch = true;
                                break;
                            }
                        }
                    }
                    return isOfferTypeMatch;
                }).takeLast(1);
        return preApprovedOfferInnerViewModelObservable;
    }


    private void getPushNotificationCount() {
        //resetting notificationCount
        notificationCount = 0;
        Observable<FBNotificationCountResponseData> notificationObservable = fbNotificationsCountExtendedUseCase.execute(createFBNotificationCountRequest());
        notificationObservable
                .compose(bindToLifecycle())
                .subscribe(notificationCountResponseData -> {
                            handleNotificationCountResponse(notificationCountResponseData);
                        },
                        throwable -> {
                            /*background api no need to handle*/
                            NBLogger.e(TAG, throwable.getMessage());
                        });
    }


    private void handleNotificationCountResponse(FBNotificationCountResponseData fbNotificationCountResponseData) {
        int ONLY_SUCCESS_ELEMENT = 1;
        if (fbNotificationCountResponseData != null & fbNotificationCountResponseData.getMetaData() != null) {
            MetaDataModel metaDataModel = fbNotificationCountResponseData.getMetaData();
            List<ResultDataModel> resultData = metaDataModel.getResultData();
            for (ResultDataModel resultDataModel : resultData) {
                ArrayList<ResultDetailModel> resultDetailList = resultDataModel.getResultDetail();
                if (resultDetailList != null && resultDetailList.size() > 0) {
                    if (resultData.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.size() == ONLY_SUCCESS_ELEMENT && resultDetailList.get(0).getStatus() != null && resultDetailList.get(0).getStatus().equalsIgnoreCase(DtoHelper.SUCCESS)) {
                        // success case
                        if (view != null) {
                            notificationCount = fbNotificationCountResponseData.getGeneralCount();
                            saveNotificationCount(notificationCount);
                            updateViewWithCount(notificationCount);
                            saveTotalNotificationCount(notificationCount);
                        }
                        break;
                    } else {
                        for (ResultDetailModel resultDetailViewModel : resultDetailList) {
                            if (resultDetailViewModel.getStatus() != null && resultDetailViewModel.getStatus().equalsIgnoreCase(DtoHelper.FAILURE)) {
                                NBLogger.e(TAG, resultDetailViewModel.getReason());
                            }
                        }
                    }
                }
            }
        }
    }

    private void saveTotalNotificationCount(int notificationCount) {
        if (notificationCount > 0) {
            mMemoryApplicationStorage.putInteger(StorageKeys.TOTAL_NOTIFICATION_COUNT, notificationCount);
        } else {
            mMemoryApplicationStorage.clearValue(StorageKeys.TOTAL_NOTIFICATION_COUNT);
        }
    }

    private void updateViewWithCount(int notificationCount) {
        if (!isViewUpdatedWithPositiveCount) {
            view.receiveNotificationCount(notificationCount);
            isViewUpdatedWithPositiveCount = notificationCount > 0;
        }
    }


    private void saveNotificationCount(int notificationCount) {
        mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, notificationCount);
        mApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, notificationCount);
    }

    private FBNotificationsCountRequestData createFBNotificationCountRequest() {
        FBNotificationsCountRequestData fbNotificationsCountRequestData = new FBNotificationsCountRequestData();
        fbNotificationsCountRequestData.setStatus(NotificationConstants.NOTIFICATION_STATUS_TYPES.NOTIFICATION_UNREAD);
        return fbNotificationsCountRequestData;
    }

    public void showGroupInvestmentAccount(boolean showCategoryDashboard,List<AccountsOverview> accountsOverviews){
        if(showCategoryDashboard){
            groupInvestmentAccounts(accountsOverviews);
        }
    }
    private void handleOverviewData(Overview overviewValue, boolean isContinued) {
        analytics.sendEvent(AppTracking.NON_TP_LOAD_INVESTMENT, StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
        if (view != null) {
            investmentAccountCount = 0;
            for (AccountsOverview accountsOverview : overviewValue.accountsOverviews) {
                if (accountsOverview.overviewType == OverviewType.INVESTMENTS) {
                    for (AccountSummary accountSummary : accountsOverview.accountSummaries) {
                        if (!StringUtils.isNullOrEmpty(accountSummary.getInstitutionName())
                                && accountSummary.getInstitutionName().equals(za.co.nedbank.services.Constants.UNIT_TRUST_TYPE)) {
                            investmentAccountCount++;
                        }
                    }
                }
            }
            // Adding a new carousal page - Financial Wellness
            if (!featureSetController.isFeatureDisabled(FeatureConstants.FTR_FINANCIAL_WELLNESS)) {
                addFinancialWellness(overviewValue);
            }
            onAccountsLoading(false);
            onAccountsLoaded(overviewValue, isContinued);
        }
    }

    /* Based on the value of FTR_DISPLAY_CH_FIRST, determine if we need to land on the
     * Financial Wellness carousel or not
     * If true and if credit health is available then land on Financial Wellness carousel for the first time.
     * After that, land on Everyday Banking */
    private void addFinancialWellness(Overview overview) {
        if (view != null && overview != null && overview.accountsOverviews != null) {
            int financialWellnessDisplayedOnLadingPageCount = mApplicationStorage.getInteger(StorageKeys.FINANCIAL_WELLNESS_DISPLAYED_ON_LANDING_COUNT, 0);

            if (financialWellnessDisplayedOnLadingPageCount <= Constants.MAX_COUNT_FOR_FINANCIAL_WELLNESS_CAROUSAL_AT_FIRST_POSITION
                    && !featureSetController.isFeatureDisabled(FeatureConstants.FTR_DISPLAY_CH_FIRST) &&
                    isCreditHealthAvailableBase()) {
                mLandOnFinancialWellnessCarousel = true;
            }

            AccountsOverview financialWellnessOverview = getFinancialWellnessOverview();
            if (financialWellnessOverview != null
                    && financialWellnessOverview.accountSummaries != null
                    && !financialWellnessOverview.accountSummaries.isEmpty()) {
                overview.accountsOverviews.add(FINANCIAL_WELLNESS_POSITION, financialWellnessOverview);
                mIsFinancialWellnessAvailable = true;
            }
        }
    }

    private AccountsOverview getFinancialWellnessOverview() {
        List<AccountSummary> accountSummaryList = new ArrayList<>();
        AccountsOverview financialWellnessOverview = null;

        //Credit Health
        addCreditHealthAccountSummary(accountSummaryList);

        if (!accountSummaryList.isEmpty()) {
            financialWellnessOverview = new AccountsOverview();
            financialWellnessOverview.accountSummaries = accountSummaryList;
            financialWellnessOverview.overviewType = OverviewType.FINANCIAL_WELLNESS;
        }

        return financialWellnessOverview;
    }

    public void addCreditHealthAccountSummary(List<AccountSummary> accountSummaryList) {
        if (isCreditHealthAvailableBase() && isCreditHealthAvailableForCarousal()
                && accountSummaryList != null) {
            AccountSummary accountSummary = new AccountSummary();
            accountSummary.setName(za.co.nedbank.services.Constants.CREDIT_HEALTH);
            accountSummary.setAccountType(OverviewType.FINANCIAL_WELLNESS);
            accountSummary.setSummaryValue(new BigDecimal(0));
            accountSummary.setNumber(StringUtils.HYPHEN);
            accountSummary.setAccountShown(true);
            accountSummaryList.add(accountSummary);
        }
    }

    public boolean isCreditHealthAvailableForCarousal() {
        return !featureSetController.isFeatureDisabled(FeatureConstants.FTR_CREDIT_HEALTH_CAROUSAL);
    }

    public boolean isCreditHealthAvailableBase() {
        return (!featureSetController.isFeatureDisabled(FeatureConstants.FTR_CREDIT_HEALTH)
                && !mIsBusinessUser
                && StringUtils.isValidRSAId(storageUtility.getIdOrTaxNumberString())
                && (StringUtils.isNullOrEmpty(birthDate) || getAgeByBirthDate() >= Constants.CREDIT_HEALTH_MIN_AGE)
        );
    }

    public int getAgeByBirthDate() {
        return StringUtils.isNotEmpty(birthDate) ? FormattingUtil.getAgeDifference(birthDate) : za.co.nedbank.core.Constants.ZERO;
    }

    public boolean isFinancialWellnessAvailable() {
        return mIsFinancialWellnessAvailable;
    }

    public boolean isLandOnFinancialWellnessCarousel() {
        return mLandOnFinancialWellnessCarousel;
    }

    private void groupInvestmentAccounts(List<AccountsOverview> accountsOverviews) {
        investmentAccountSummeryViewModel = new InvestmentAccountSummeryViewModel();
        boolean haveInvAccounts = false;
        for (AccountsOverview x : accountsOverviews) {
            if (x.overviewType == OverviewType.INVESTMENTS && !x.accountSummaries.isEmpty()) {
                haveInvAccounts = true;
            }
        }
        if(!haveInvAccounts){
            AccountsOverview ov = new  AccountsOverview();
            ov.overviewType = OverviewType.INVESTMENTS;
            ov.accountSummaries = new ArrayList<>();
            accountsOverviews.add(ov);
        }
        for (AccountsOverview accountsOverview : accountsOverviews) {
            if (accountsOverview.overviewType == OverviewType.INVESTMENTS){
                    List<AccountSummary> accountSummaries = accountsOverview.accountSummaries;
                    filterInvestmentAccountType(accountSummaries, investmentAccountSummeryViewModel);
                    addInvestmentProducts(accountsOverview.accountSummaries, investmentAccountSummeryViewModel);
                addPendingInvestmentAccounts(accountSummaries);

                break;
            }

        }

    }

    private void addPendingInvestmentAccounts(List<AccountSummary> accountSummaries) {
        if (pendingAccountListDataModel != null && pendingAccountListDataModel.getPendingAccountDataModels()!=null) {
            for (PendingAccountDataModel pendingAccountDataModel : pendingAccountListDataModel.getPendingAccountDataModels()) {
                accountSummaries.add(getInvestmentAccountSummary(String.format("%s%s%s", pendingAccountDataModel.getInvestorNumber(),
                        StringUtils.HYPHEN, pendingAccountDataModel.getInvestmentNumber()), true));
            }
        }
    }

    private void invalidateAccountCache() {
        getOverviewUseCase.invalidateCache();
    }

    public void filterInvestmentAccountType(List<AccountSummary> accountSummaries, InvestmentAccountSummeryViewModel investmentAccountSummeryViewModel) {
        if (accountSummaries != null) {
            Iterator<AccountSummary> iterator = accountSummaries.iterator();
            while (iterator.hasNext()) {
                AccountSummary accountSummary = iterator.next();
                if (accountSummary.getAccountCode() != null){
                    addAccountSummary(investmentAccountSummeryViewModel, iterator, accountSummary);
                    addOtherNgiProductAccountSummary(investmentAccountSummeryViewModel, iterator, accountSummary);
                }
            }
        }
    }

    private void addAccountSummary(InvestmentAccountSummeryViewModel investmentAccountSummeryViewModel, Iterator<AccountSummary> iterator, AccountSummary accountSummary) {
        if (isTFUTProduct(accountSummary)){
            investmentAccountSummeryViewModel.getTfUtProductList().add(accountSummary);
            iterator.remove();
        }else if (isTFIProduct(accountSummary)){
            investmentAccountSummeryViewModel.getTfiProductList().add(accountSummary);
            iterator.remove();
        }else if (isNoticeProduct(accountSummary)){
            investmentAccountSummeryViewModel.getNoticeProductList().add(accountSummary);
            iterator.remove();
        }else if (isFixedProduct(accountSummary)){
            investmentAccountSummeryViewModel.getFixedProductList().add(accountSummary);
            iterator.remove();
        }else if (isRAProduct(accountSummary)){
            investmentAccountSummeryViewModel.getRaProductList().add(accountSummary);
            iterator.remove();
        }else if (isUTProduct(accountSummary)){
            investmentAccountSummeryViewModel.getUtProductList().add(accountSummary);
            iterator.remove();
        }
    }

    private void addOtherNgiProductAccountSummary(InvestmentAccountSummeryViewModel investmentAccountSummeryViewModel, Iterator<AccountSummary> iterator, AccountSummary accountSummary) {
        if (isLAProduct(accountSummary)){
            investmentAccountSummeryViewModel.getLaProductList().add(accountSummary);
            iterator.remove();
        }else if (isPENProduct(accountSummary)){
            investmentAccountSummeryViewModel.getPenProductList().add(accountSummary);
            iterator.remove();
        }else if (isPROVProduct(accountSummary)){
            investmentAccountSummeryViewModel.getProvProductList().add(accountSummary);
            iterator.remove();
        }else if (isENDProduct(accountSummary)){
            investmentAccountSummeryViewModel.getEndProductList().add(accountSummary);
            iterator.remove();
        }
    }

        private boolean isTFIProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equals(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_DS)
                && accountSummary.getTfsIndicator() != null && accountSummary.getTfsIndicator().equals(ServicesNavigationTarget.InvestmentConstant.CODE_Y);
    }

    private boolean isTFUTProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV)
                && accountSummary.getInvestmentProductId().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.ACCOUNT_TYPE_TFI);
    }

    private boolean isUTProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV)
                && accountSummary.getInvestmentProductId().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.ACCOUNT_TYPE_UT);
    }

    private boolean isRAProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV)
                && accountSummary.getInvestmentProductId().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.ACCOUNT_TYPE_RA);
    }

    private boolean isLAProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV)
                && accountSummary.getInvestmentProductId().equalsIgnoreCase(NGIConstants.NGI_CONSTANT_LA);
    }

    private boolean isPENProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV)
                && accountSummary.getInvestmentProductId().equalsIgnoreCase(NGIConstants.NGI_CONSTANT_PEN);
    }

    private boolean isPROVProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV)
                && accountSummary.getInvestmentProductId().equalsIgnoreCase(NGIConstants.NGI_CONSTANT_PROV);
    }

    private boolean isENDProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV)
                && accountSummary.getInvestmentProductId().equalsIgnoreCase(NGIConstants.NGI_CONSTANT_END);
    }

    private boolean isFixedProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_DS)
                && accountSummary.getNoticeProduct() != null && accountSummary.getNoticeProduct().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.CODE_N)
                && (accountSummary.getTfsIndicator() == null || accountSummary.getTfsIndicator().equals(ServicesNavigationTarget.InvestmentConstant.CODE_N));
    }

    private boolean isNoticeProduct(AccountSummary accountSummary) {
        return accountSummary.getAccountCode().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_DS)
                && accountSummary.getNoticeProduct() != null && accountSummary.getNoticeProduct().equalsIgnoreCase(ServicesNavigationTarget.InvestmentConstant.CODE_Y)
                && (accountSummary.getTfsIndicator() == null || accountSummary.getTfsIndicator().equals(ServicesNavigationTarget.InvestmentConstant.CODE_N));
    }

    private void addInvestmentProducts(List<AccountSummary> accountSummary, InvestmentAccountSummeryViewModel investmentAccountSummeryViewModel) {
        getInvestmentAccSummery(accountSummary, investmentAccountSummeryViewModel);
        AccountsOverview accountsOverview = new AccountsOverview();
        accountsOverview.accountSummaries = accountSummary;
        accountsOverview.overviewType = OverviewType.INVESTMENTS;
        accountsOverview.summaryValue1 = new BigDecimal(0);
        accountsOverview.summaryValue2 = new BigDecimal(0);
    }

     public void getInvestmentAccSummery(List<AccountSummary> accountSummary, InvestmentAccountSummeryViewModel investmentAccountSummeryViewModel) {
        int i = za.co.nedbank.core.Constants.ZERO;
        if (investmentAccountSummeryViewModel != null ){
            if (!investmentAccountSummeryViewModel.getNoticeProductList().isEmpty()){
                AccountSummary summary = new AccountSummary(String.valueOf(i++), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                        ServicesNavigationTarget.InvestmentConstant.NOTICE_PRODUCT
                        , BigDecimal.valueOf(investmentAccountSummeryViewModel.getNoticeProductAvailableBal()), StringUtils.CURRENCY_SYMBOL);
                summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_DS);
                summary.setNoticeProduct(ServicesNavigationTarget.InvestmentConstant.CODE_Y);
                summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getNoticeProductList().size());
                summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getNoticeAccTotalCurrentBal());
                accountSummary.add(summary);
            }
            if (!investmentAccountSummeryViewModel.getFixedProductList().isEmpty()){
                AccountSummary summary = new AccountSummary(String.valueOf(i++), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                        ServicesNavigationTarget.InvestmentConstant.TERM_DEPOSITS,
                        BigDecimal.valueOf(investmentAccountSummeryViewModel.getFixedProductAvailableBalance()), StringUtils.CURRENCY_SYMBOL);
                summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_DS);
                summary.setNoticeProduct(ServicesNavigationTarget.InvestmentConstant.CODE_N);
                summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getFixedProductList().size());
                summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getFixedAccTotalCurrentBal());
                accountSummary.add(summary);
            }

            if (!investmentAccountSummeryViewModel.getRaProductList().isEmpty()){
                AccountSummary summary = new AccountSummary(String.valueOf(i++), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                        ServicesNavigationTarget.InvestmentConstant.RETIREMENT_ANNUITY,
                        BigDecimal.valueOf(investmentAccountSummeryViewModel.getRAAvailableBalance()), StringUtils.CURRENCY_SYMBOL);
                summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV);
                summary.setInvestmentProductId(ServicesNavigationTarget.InvestmentConstant.ACCOUNT_TYPE_RA);
                summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getRaProductList().size());
                summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getRaAccTotalMarketValue());
                accountSummary.add(summary);
            }

            i = addOtherNgiProducts(accountSummary, investmentAccountSummeryViewModel, i);

            if (!investmentAccountSummeryViewModel.getUtProductList().isEmpty()){
                AccountSummary summary = new AccountSummary(String.valueOf(i++), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                        ServicesNavigationTarget.InvestmentConstant.UNIT_TRUST,
                        BigDecimal.valueOf(investmentAccountSummeryViewModel.getUTAvailableBalance()), StringUtils.CURRENCY_SYMBOL);
                summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV);
                summary.setInvestmentProductId(ServicesNavigationTarget.InvestmentConstant.ACCOUNT_TYPE_UT);
                summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getUtProductList().size());
                summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getUtAccTotalMarketValue());
                accountSummary.add(summary);
            }

            if (!investmentAccountSummeryViewModel.getTfiProductList().isEmpty()){
                AccountSummary summary = new AccountSummary(String.valueOf(i), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                        ServicesNavigationTarget.InvestmentConstant.TAX_FREE_INVESTMENTS,
                        BigDecimal.valueOf(investmentAccountSummeryViewModel.getTFIAvailableBalance()), StringUtils.CURRENCY_SYMBOL);
                summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_DS);
                summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getTfiProductList().size());
                summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getTfiAccTotalCurrentBal());
                summary.setTfsIndicator(ServicesNavigationTarget.InvestmentConstant.CODE_Y);
                accountSummary.add(summary);
            }

            if (!investmentAccountSummeryViewModel.getTfUtProductList().isEmpty()){
                AccountSummary summary = new AccountSummary(String.valueOf(i), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                        ServicesNavigationTarget.InvestmentConstant.TAX_FREE_UNIT_TRUST,
                        BigDecimal.valueOf(investmentAccountSummeryViewModel.getTFUTAvailableBalance()), StringUtils.CURRENCY_SYMBOL);
                summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV);
                summary.setInvestmentProductId(ServicesNavigationTarget.InvestmentConstant.ACCOUNT_TYPE_TFI);
                summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getTfUtProductList().size());
                summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getTfUtAccTotalMarketValue());
                accountSummary.add(summary);
            }
        }
    }

    private int addOtherNgiProducts(List<AccountSummary> accountSummary, InvestmentAccountSummeryViewModel investmentAccountSummeryViewModel, int i) {
        if (!investmentAccountSummeryViewModel.getLaProductList().isEmpty()){
            AccountSummary summary = new AccountSummary(String.valueOf(i++), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                    ServicesNavigationTarget.InvestmentConstant.LIVING_ANNUITY,
                    BigDecimal.valueOf(investmentAccountSummeryViewModel.getLaAvailableBalance()), StringUtils.CURRENCY_SYMBOL);
            summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV);
            summary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_LA);
            summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getLaProductList().size());
            summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getLaTotalMarketValue());
            accountSummary.add(summary);
        }

        if (!investmentAccountSummeryViewModel.getPenProductList().isEmpty()){
            AccountSummary summary = new AccountSummary(String.valueOf(i++), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                    ServicesNavigationTarget.InvestmentConstant.PENSION_PRESERVATION,
                    BigDecimal.valueOf(investmentAccountSummeryViewModel.getPenAvailableBalance()), StringUtils.CURRENCY_SYMBOL);
            summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV);
            summary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_PEN);
            summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getPenProductList().size());
            summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getPenTotalMarketValue());
            accountSummary.add(summary);
        }

        if (!investmentAccountSummeryViewModel.getProvProductList().isEmpty()){
            AccountSummary summary = new AccountSummary(String.valueOf(i++), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                    ServicesNavigationTarget.InvestmentConstant.PROVIDENT_PRESERVATION,
                    BigDecimal.valueOf(investmentAccountSummeryViewModel.getProvAvailableBalance()), StringUtils.CURRENCY_SYMBOL);
            summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV);
            summary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_PROV);
            summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getProvProductList().size());
            summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getProvTotalMarketValue());
            accountSummary.add(summary);
        }

        if (!investmentAccountSummeryViewModel.getEndProductList().isEmpty()){
            AccountSummary summary = new AccountSummary(String.valueOf(i++), StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS,
                    ServicesNavigationTarget.InvestmentConstant.ENDOWMENT_PLAN,
                    BigDecimal.valueOf(investmentAccountSummeryViewModel.getEndAvailableBalance()), StringUtils.CURRENCY_SYMBOL);
            summary.setAccountCode(ServicesNavigationTarget.InvestmentConstant.INVONLINE_PRODUCT_TYPE_INV);
            summary.setInvestmentProductId(NGIConstants.NGI_CONSTANT_END);
            summary.setNoticeProductCounter(investmentAccountSummeryViewModel.getEndProductList().size());
            summary.setTotalAvailableBalance(investmentAccountSummeryViewModel.getEndTotalMarketValue());
            accountSummary.add(summary);
        }
        return i;
    }

    private AccountSummary getInvestmentAccountSummary(String invName, boolean isPendingAccount) {
        AccountSummary accountSummary = new AccountSummary("0", StringUtils.EMPTY_STRING, OverviewType.INVESTMENTS_PENDING_ACCOUNTS, view.getResources().getString(R.string.onia_dashboard_pending_acc_name), new BigDecimal(0), invName);
        accountSummary.setOpenNewInvAccount(true);
        accountSummary.setIspendingAccount(isPendingAccount);
        accountSummary.setIsProductCategory(false);
        return accountSummary;
    }

    public void navigateToChatActivity() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CHAT_SCREEN));
    }

    public void handleUpdateApp(String message, String title) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.APP_SOFT_UPDATE_AVAILABLE);
        navigationTarget
                .withParam(NavigationTarget.IS_DEVICE_ROOTED_PARAM, false)
                .withParam(NavigationTarget.IS_FROM_APP_SHORTCUT, false)
                .withParam(NavigationTarget.IS_SECOND_LOGIN, true)
                .withParam(NavigationTarget.UPDATE_APP_DESC, message)
                .withParam(NavigationTarget.UPDATE_APP_TITLE, title);
        navigationRouter.navigateTo(navigationTarget);

        analytics.sendEvent(ChatTracking.AdobeEventName.EVENT_NAME_UPDATE_APP,
                StringUtils.EMPTY_STRING, StringUtils.EMPTY_STRING);
    }

    public void navigateToChatErrorActivity() {
        navigationRouter.navigateTo(
                NavigationTarget.to(NavigationTarget.ERROR_SCREEN_ACTIVITY));
    }

    public void navigateToChatBotActivity(String conversationId) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.CONVO_CHAT_SCREEN).withIntentSingleTop(true);
        navigationTarget.withParam(NavigationTarget.CONVERSATION_ID,conversationId);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void navigateToChatBotIntroductionActivity() {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.CHATBOT_INTRODUCTION_SCREEN).withIntentSingleTop(true));
    }
    public void navigateToChatBotLiveActivity() {
        navigationRouter.navigateTo(
                NavigationTarget.to(NavigationTarget.CONVO_CHAT_SCREEN).withIntentSingleTop(true));
    }

    private boolean isSavvyBundleOffer(AccountSummary accountSummary) {
        return (accountSummary.getAccountType() == OverviewType.EVERYDAY_BANKING && accountSummary.isPreApprovedOfferAccount());
    }

    private boolean isLoanOffer(AccountSummary accountSummary) {
        return (accountSummary.getAccountType() == OverviewType.LOANS && accountSummary.isPreApprovedOfferAccount());
    }

    private boolean isCreditHealth(AccountSummary accountSummary) {
        return (accountSummary.getAccountType() == OverviewType.EVERYDAY_BANKING
                && za.co.nedbank.services.Constants.NON_TP_CH_TITLE.equals(accountSummary.getName()));
    }

    public void accountTypeClicked(final AccountSummary accountSummary) {
        if (view != null) {
            if (isSavvyBundleOffer(accountSummary)) {
                trackActionWithContextDataNA(AppTracking.DASHBOARD_VIEW_OFFER_EDB_SB);
                NavigationTarget navigationTarget = prepareNavigationTarget(Long.parseLong(accountSummary.getNumber()),
                        Long.parseLong(accountSummary.getId()), accountSummary.getPreApprovedOfferType(),
                        accountSummary.getOfferCategory(), accountSummary.isDebicheckMandateEnabled());
                navigationRouter.navigateTo(navigationTarget);
            } else if (isLoanOffer(accountSummary)) {
                trackActionWithContextDataNA(AppTracking.DASHBOARD_VIEW_OFFER);
                NavigationTarget navigationTarget;
                navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.PRE_APPROVED_CAMPAIGN);
                navigationTarget.withParam(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.DASHBOARD_SCREEN);
                OfferNavigationModel offerNavigationModel = new OfferNavigationModel();
                offerNavigationModel.setOfferTypeId(Long.parseLong(accountSummary.getNumber()));
                offerNavigationModel.setOfferId(Long.parseLong(accountSummary.getId()));
                offerNavigationModel.setOfferType(accountSummary.getPreApprovedOfferType());
                offerNavigationModel.setFlowEntryDashboardCarousel(true);
                offerNavigationModel.setOfferCategory(accountSummary.getOfferCategory());
                offerNavigationModel.setDebiCheckMandateEnable(accountSummary.isDebicheckMandateEnabled());
                navigationTarget.withParam(Constants.BundleKeys.OFFER_MODEL, offerNavigationModel);
                navigationRouter.navigateTo(navigationTarget);
            } else if (accountSummary.getAccountType() == OverviewType.INVESTMENTS) {
                onInvestmentAccountClicked(accountSummary, investmentAccountSummeryViewModel);
            } else if (accountSummary.getAccountType() == OverviewType.REWARDS) {
                navigateToRewardsLandingScreen(accountSummary);
            } else if (accountSummary.getAccountType() == OverviewType.FOREIGN_CURRENCY_ACCOUNT) {
                navigateToForeignTravelCardScreen(accountSummary);
            } else if (isCreditHealth(accountSummary) ||
                    (accountSummary.getAccountType() == OverviewType.FINANCIAL_WELLNESS
                            && za.co.nedbank.services.Constants.CREDIT_HEALTH.equals(accountSummary.getName()))) {
                trackViewMyCreditScore();
                openCreditHealthScreen();
            } else {
                navigateToAccountDetails(accountSummary);
            }
            if (accountSummary != null && accountSummary.isDormantAccount()) {
                analytics.sendEvent(TrackingEvent.DORMANT_ACCOUNT_SECTION, TrackingParam.DORMANT_ACCOUNT_USAGE_DETAIL, StringUtils.EMPTY_STRING);
            }
        }
    }

    public void onInvestmentAccountClicked(AccountSummary accountSummary, InvestmentAccountSummeryViewModel investmentAccountSummeryViewModel) {
        invalidateAccountCache();
        if(isTFIProduct(accountSummary)) {
                navigateToProductTypeList(investmentAccountSummeryViewModel.getTfiProductList(), accountSummary.getName());
        } else if (isNoticeProduct(accountSummary)){
            navigateToProductTypeList(investmentAccountSummeryViewModel.getNoticeProductList(), accountSummary.getName());
        }else if (isFixedProduct(accountSummary)){
            navigateToProductTypeList(investmentAccountSummeryViewModel.getFixedProductList(), accountSummary.getName());
        }else if (isRAProduct(accountSummary)){
            navigateToProductTypeList(investmentAccountSummeryViewModel.getRaProductList(), accountSummary.getName());
        }else if (isLAProduct(accountSummary)){
            navigateToProductTypeList(investmentAccountSummeryViewModel.getLaProductList(), accountSummary.getName());
        }else if (isPENProduct(accountSummary)){
            navigateToProductTypeList(investmentAccountSummeryViewModel.getPenProductList(), accountSummary.getName());
        }else if (isPROVProduct(accountSummary)){
            navigateToProductTypeList(investmentAccountSummeryViewModel.getProvProductList(), accountSummary.getName());
        }else if (isENDProduct(accountSummary)){
            navigateToProductTypeList(investmentAccountSummeryViewModel.getEndProductList(), accountSummary.getName());
        }else if (isUTProduct(accountSummary)){
            navigateToProductTypeList(investmentAccountSummeryViewModel.getUtProductList(), accountSummary.getName());
        } else if (isTFUTProduct((accountSummary))) {
            navigateToProductTypeList(investmentAccountSummeryViewModel.getTfUtProductList(), accountSummary.getName());
        }

    }

    void openCreditHealthScreen() {
        mMemoryApplicationStorage.putInteger(StorageKeys.FICA_WORKFLOW, FicaWorkFlow.IN_APP);

        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.CREDIT_HEALTH_FLOW);
        navigationRouter.navigateTo(navigationTarget);
    }

    private void trackViewMyCreditScore() {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setFeatureCategory(CreditHealthTrackingValue.FEATURE_CATEGORY);
        adobeContextData.setFeature(CreditHealthTrackingValue.FEATURE_CREDIT_SCORE);
        analytics.sendEventActionWithMap(CreditHealthTrackingEvent.DASHBOARD_SELECT_CREDIT_SCORE, adobeContextData.getCdata());
    }

    private void navigateToProductTypeList(List<AccountSummary> accountSummaryList, String accountType) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_ACCOUNT_TYPE_LIST)
                .withParam(ServicesNavigationTarget.INVONLINE_ACCOUNT_LIST, accountSummaryList)
                .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, canTransact())
                .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, clientType)
                .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, getFicaStatusValue())
                .withParam(ServicesNavigationTarget.INVONLINE_ACCOUNT_TYPE, accountType)
                .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                .withParam(PARAM_ONIA_BIRTH_DATE, birthDate).withParam(PARAM_ONIA_SEC_OFFICER_CD, secOfficerID);
        navigationRouter.navigateTo(navigationTarget);
    }

    private OfferNavigationModel prepareOfferNavigationModelObject(Long offerTypeId,
                                                                   Long offerId,
                                                                   String offerType,
                                                                   long offerCategory,
                                                                   boolean isDebiCheckMandateEnabled) {
        OfferNavigationModel offerNavigationModel = new OfferNavigationModel();
        offerNavigationModel.setOfferTypeId(offerTypeId);
        offerNavigationModel.setOfferId(offerId);
        offerNavigationModel.setOfferType(offerType);
        offerNavigationModel.setFlowEntryDashboardCarousel(true);
        offerNavigationModel.setOfferCategory(offerCategory);
        offerNavigationModel.setDebiCheckMandateEnable(isDebiCheckMandateEnabled);
        return offerNavigationModel;
    }

    private NavigationTarget prepareNavigationTarget(Long offerTypeId,
                                                     Long offerId,
                                                     String offerType,
                                                     long offerCategory,
                                                     boolean isDebiCheckMandateEnabled) {
        NavigationTarget navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.LOAN_LANDING_ACTIVITY);
        navigationTarget.withParam(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.DASHBOARD_SCREEN);
        navigationTarget.withParam(Constants.BundleKeys.OFFER_MODEL,
                prepareOfferNavigationModelObject(offerTypeId, offerId, offerType, offerCategory, isDebiCheckMandateEnabled));
        navigationTarget.withParam(za.co.nedbank.core.Constants.PreApprovedOffersBundleKeys.FLOW_ENTRY_DASHBOARD_CAROUSEL, true);
        return navigationTarget;
    }

    private void navigateToForeignTravelCardScreen(AccountSummary accountSummary) {
        mMemoryApplicationStorage.putObject(za.co.nedbank.core.Constants.CARD_RELATIONSHIP, accountSummary.getCardRelationShip());
        mMemoryApplicationStorage.putObject(za.co.nedbank.core.Constants.IDENTIFICATION_NUMBER, accountSummary.getIdentificationNumber());

        NavigationTarget navigationTarget = NavigationTarget.to(ServicesNavigationTarget.TRAVEL_CARD_DETAILS)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, view.canTransact())
                .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, view.getFicaStatus())
                .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void trackGreenbackRewards(long gbStartTime) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.resetDimensions();
        adobeContextData.setCategoryAndProduct(ProfileTracking.VAL_REWARDS_GREENBACKS);
        adobeContextData.setProductAccount(ProfileTracking.VAL_PRODUCT_GREENBACKS);
        adobeContextData.setInitiations();
        adobeContextData.setEntryPoint(TrackingParam.VAL_APPLY_DASHBOARD);
        mMemoryApplicationStorage.putLong(StorageKeys.PRODUCT_ON_BOARDING_TIME, gbStartTime);
        analytics.sendEventActionWithMap(ProfileTracking.KEY_PRODUCT_SELECTED, cdata);

        Map<String, Object> gBData = new HashMap<>();
        AddContextData addContextData = new AddContextData(gBData);
        addContextData.setOnBoardingProcess(AppsFlyerTags.VAL_REAL_TIME);
        addContextData.setProduct(ServicesTracking.VAL_SAVING_POCKET);
        addContextData.setProductGroup(ServicesTracking.VAL_BANK);
        mAfAnalyticsTracker.sendEvent(AppsFlyerTags.AF_EXISTING_CLIENT_INITIATION, gBData);
    }

    private void navigateToRewardsLandingScreen(AccountSummary accountSummary) {

        if (StringUtils.isNullOrEmpty(accountSummary.getNumber())) {
            boolean isEnrolmentInProgress = mApplicationStorage.getBoolean(StorageKeys.IS_GB_REWARDS_ENROLMENT_IN_PROGRESS, false);
            if (isEnrolmentInProgress) {
                NBLogger.d(TAG, "Is GB rewards enrolment in progress: " + isEnrolmentInProgress);
                return;
            }
            long timestamp = System.currentTimeMillis() / 1000;
            trackGreenbackRewards(timestamp);
            if (accountSummary.getAccountType() == OverviewType.REWARDS && accountSummary != null) {
                navigationRouter.navigateTo(NavigationTarget.to(ProfileNavigationTarget.REWARDS_LANDING)
                        .withParam(za.co.nedbank.profile.common.Constants.Extras.ID_NUMBER, accountSummary.getId()));
            }
        } else {
            navigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS)
                    .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                    .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber())
                    .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                    .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_ACCOUNT_TYPE, accountSummary.getAccountCode())
                    .withParam(ServicesNavigationTarget.PARAM_REWARDS_TYPE, accountSummary.getName())
                    .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                    .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                    .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, view.canTransact())
                    .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, view.getFicaStatus())
                    .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, clientType)
                    .withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate())
                    .withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice())
                    .withParam(ServicesNavigationTarget.INVONLINE_DELETE_NOTICE, accountSummary.getDeleteNotice())
                    .withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct())
                    .withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment())
                    .withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment())
                    .withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment())
                    .withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit())
                    .withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit())
                    .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                    .withParam(ServicesNavigationTarget.PARAM_UNIT_TRUST_ACCOUNT_COUNT, investmentAccountCount)
                    .withParam(ServicesNavigationTarget.PARAM_IS_SA_RESIDENT, view.isSAResident())
            );
        }
    }

    public void getFicaStatus() {
        getFicaStatusUseCase
                .execute()
                .compose(bindToLifecycle())
                .doOnSubscribe(disposable -> {
                })
                .doOnTerminate(() -> {
                })
                .subscribe(ficaStatusDataModel -> {
                    if (ficaStatusDataModel.getIsFica()) {
                        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_RIGHT_OPTIONS).withParam(PARAM_ONIA_CLIENT_TYPE, clientType)
                                .withParam(PARAM_ONIA_BIRTH_DATE, birthDate).withParam(PARAM_ONIA_SEC_OFFICER_CD, secOfficerID));
                    } else {
                        showClientTypeErrorActivity(za.co.nedbank.core.Constants.INVONLINE_ONIA_ERROR_FICA_ERROR_TYPE);
                    }
                }, throwable -> {
                    if (view != null) {
                        onFicaStatusError(errorHandler.getErrorMessage(throwable).getMessage());
                    }
                });
    }

    public boolean isClientTypeValid(String clientType) {
        return view != null && clientType != null && (!clientType.equals(za.co.nedbank.core.Constants.CLIENT_TYPE_51) && !clientType.equals(za.co.nedbank.core.Constants.CLIENT_TYPE_52));
    }

    private void navigateToAccountDetails(AccountSummary accountSummary) {
        navigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.ACCOUNT_DETAILS)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_ID, accountSummary.getId())
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_TYPE, accountSummary.getAccountType().getValue())
                .withParam(ServicesNavigationTarget.PARAM_OVERVIEW_ACCOUNT_TYPE, accountSummary.getAccountCode())
                .withParam(ServicesNavigationTarget.IS_PROFILE_ACCOUNT, accountSummary.isProfileAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_DORMANT_ACCOUNT, accountSummary.isDormantAccount())
                .withParam(ServicesNavigationTarget.PARAM_IS_TRANSACTABLE, canTransact())
                .withParam(ServicesNavigationTarget.INVONLINE_CLIENT_TYPE, clientType)
                .withParam(ServicesNavigationTarget.INVONLINE_FIRST_WITHDRAWAL_DATE, accountSummary.getFirstWithdrawalDate())
                .withParam(ServicesNavigationTarget.INVONLINE_PLACE_NOTICE, accountSummary.getPlaceNotice())
                .withParam(ServicesNavigationTarget.INVONLINE_PAYOUT_FIXEDEPOSIT, accountSummary.getPayoutFixedDeposit())
                .withParam(ServicesNavigationTarget.INVONLINE_HAS_RECURRING_PAYMENT, accountSummary.getHasRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_DELETE_RECURRING_PAYMENT, accountSummary.getDeleteRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_NOTICE_PRODUCT, accountSummary.getNoticeProduct())
                .withParam(ServicesNavigationTarget.INVONLINE_MAINTAIN_RECURRING_PAYMENT, accountSummary.getMaintainRecurringPayment())
                .withParam(ServicesNavigationTarget.INVONLINE_MATURING_INVESTMENT_ENABLED, accountSummary.getReinvestFixedDeposit())
                .withParam(ServicesNavigationTarget.PARAM_FICA_STATUS, getFicaStatusValue())
                .withParam(ServicesNavigationTarget.PARAM_IS_UPLIFT_DORMANCY_AVAILABLE, true)
                .withParam(ServicesNavigationTarget.PARAM_ACCOUNT_NO, accountSummary.getNumber())
        );
    }

    public void handleClickNotificationCount() {
        boolean isNotificationDisabled = featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.PUSH_NOTIFICATIONS);
        String target = isNotificationDisabled ? PreApprovedOffersNavigationTarget.PRE_APPROVED_OFFERS_NOTIFICATIONS : NavigationTarget.TARGET_NOTIFICATION_CENTER;
        NavigationTarget navigationTarget = NavigationTarget.to(target);
        navigationTarget.withParam(Constants.BundleKeys.SCREEN_NAME, Constants.ScreenIdentifiers.DASHBOARD_SCREEN);
        navigationTarget.withParam(za.co.nedbank.core.Constants.BUNDLE_KEYS.FLOW_JOURNEY_FLAG, za.co.nedbank.core.Constants.FLOW_CONSTANTS.POST_LOGIN_DASHBORD_NOTIFICATION_FLOW);
        navigationRouter.navigateTo(navigationTarget);

    }

    public void showClientTypeErrorActivity(int errorType) {
        navigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_ONIA_INVESTMENT_ERROR)
                .withParam(NavigationTarget.KEY_ONIA_ERROR_TYPE, errorType));
    }

    public boolean isFeatureDisabled(String featureName) {
        return featureSetController.isFeatureDisabled(featureName);
    }

    public void getTotalBankingUnreadMessages() {
        int totalUnreadMessageCount = mApplicationStorage.getInteger(StorageKeys.BANKING_TOTAL_UNREAD_MESSAGE_COUNT, za.co.nedbank.core.Constants.ZERO);
        onChatIconCount(totalUnreadMessageCount);
    }

    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    public void onUnreadChatEvent(UnreadChatEvent unreadChatEvent) {
        if (view != null) {
            view.onUnreadChatEvent(unreadChatEvent);
        }
    }

    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    public void onUnreadLifestyleChatEvent(LifestyleUnreadChatIconEvent unreadChatEvent) {
        if (view != null) {
            view.onUnreadLifestyleChatEvent(unreadChatEvent);
        }
    }

    public void getAvoWalletDetails() {
        onAccountsLoading(true);
        mWalletDetailsUseCase.execute()
                .compose(bindToLifecycle())
                .subscribe(
                        this::openAvoAppInBrowser,
                        throwable -> {
                            analytics.trackFailure(false, AppTracking.MY_ACCOUNTS_AVO_FAILURE, ApiAliasConstants.AVO_WLD, errorHandler.getErrorMessage(throwable).getMessage(), null);
                            if (view != null) {
                                view.showError(errorHandler.getErrorMessage(throwable).getMessage());
                                onAccountsLoading(false);
                            }
                        }
                );
    }

    private void openAvoAppInBrowser(AvoWalletDetailsModel result) {
        Map<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setContext10(result.getAvoId());
        analytics.sendEventActionWithMap(AppTracking.MY_ACCOUNTS_AVO_SUCCESS, cdata);
        AvoToggleUtils.AvoToggleRedirection redirectionURL = AvoToggleUtils
                .getAvoAppRederictionURL(featureSetController, result, false);
        if (redirectionURL.shouldOpenInWebView()) {
            openAvoInWebView(redirectionURL.getAvoAppRedirectionUrl());
        } else {
            if (view != null) {
                view.startBrowser(redirectionURL.getAvoAppRedirectionUrl());
            }
        }
    }

    private void openAvoInWebView(String redirectUrl) {
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_REDIRECT_URL);
        navigationTarget = navigationTarget.withParam(NotificationConstants.EXTRA.URL, redirectUrl)
                .withParam(NotificationConstants.EXTRA.IS_AVO_APP_URL, true);
        navigationRouter.navigateTo(navigationTarget);
    }

    public void trackAppsFlyerEvent() {
        mAfAnalyticsTracker.sendEvent(AppTracking.AF_AVO_REDIRECT, null);
    }

    public void loadMediaContent() {
        mMediaContentUseCase.execute(AppState.POST_LOGIN)
                .compose(bindToLifecycle())
                .subscribe(appLayoutViewModel -> {
                    if (view != null) {
                        view.updateMediaCardViewModel(appLayoutViewModel.getMediaCard());
                    }
                }, throwable -> {
                    if (view != null) {
                        view.updateMediaCardViewModel(null);
                    }
                });
    }

    public void trackActionWithContextDataNA(String actionName){
        HashMap<String, Object> contextData = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(contextData);
        adobeContextData.resetDimensions();
        analytics.sendEventActionWithMap(actionName, contextData);
    }

    public boolean isBusinessUser() {
       return mIsBusinessUser;
    }

    @Subscribe(sticky = true, threadMode = ThreadMode.MAIN)
    public void onServerStateChangedEvent(ServerStateChangedEvent stateChangedEvent) {
        boolean isChatDisconnected = stateChangedEvent.getNewState() == ConnectionState.Disconnected;
        view.onServerStateChanged(!isChatDisconnected);
    }

}
