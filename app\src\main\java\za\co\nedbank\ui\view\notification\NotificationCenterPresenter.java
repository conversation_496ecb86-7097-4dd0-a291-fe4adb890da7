package za.co.nedbank.ui.view.notification;


import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.model.notifications.FBNotificationCountResponseData;
import za.co.nedbank.core.domain.model.notifications.FBNotificationsCountRequestData;
import za.co.nedbank.core.domain.model.preapprovedoffers.GetPreApprovedOfferRequestData;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.domain.usecase.notifications.GetFBNotificationsCountExtendedUseCase;
import za.co.nedbank.core.domain.usecase.preapprovedoffers.GetPreApprovedOfferUseCase;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.notification.NotificationConstants;
import za.co.nedbank.core.notification.NotificationEvent;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingParam;
import za.co.nedbank.core.tracking.adobe.AdobeContextData;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.loans.preapprovedaccountsoffers.Constants;
import za.co.nedbank.loans.preapprovedaccountsoffers.navigator.PreApprovedOffersNavigationTarget;
import za.co.nedbank.profile.view.tracking.ProfileTracking;

public class NotificationCenterPresenter extends NBBasePresenter<NotificationCenterView> {

    private final NavigationRouter mNavigationRouter;
    private final ApplicationStorage mMemoryApplicationStorage;
    private final ApplicationStorage mApplicationStorage;
    private final Analytics analytics;
    private GetFBNotificationsCountExtendedUseCase getFBNotificationsCountExtendedUseCase;
    private final GetPreApprovedOfferUseCase mGetPreApprovedOffersUseCase;

    private FeatureSetController featureSetController;

    @Inject
    public NotificationCenterPresenter(NavigationRouter navigationRouter,
                                       @Named("memory") ApplicationStorage memoryApplicationStorage, ApplicationStorage applicationStorage, Analytics analytics,
                                       GetFBNotificationsCountExtendedUseCase fbNotificationsCountExtendedUseCase,
                                       GetPreApprovedOfferUseCase getPreApprovedOffersUseCase,
                                       FeatureSetController featureSetController) {

        this.mNavigationRouter = navigationRouter;
        this.mMemoryApplicationStorage = memoryApplicationStorage;
        this.mApplicationStorage = applicationStorage;
        this.analytics = analytics;
        this.getFBNotificationsCountExtendedUseCase = fbNotificationsCountExtendedUseCase;
        this.mGetPreApprovedOffersUseCase = getPreApprovedOffersUseCase;
        this.featureSetController = featureSetController;
    }

    @Override
    protected void onBind() {
        super.onBind();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onUnbind() {
        EventBus.getDefault().unregister(this);
        super.onUnbind();
    }

    void navigateToNotificationMessages() {
        trackAction(NotificationConstants.TRACKING_PARAMS.NAVIGATION_TO_NOTIFICATION_MESSAGES, TrackingParam.VAL_VIEW_NOTIFICATIONS);

        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_MESSAGES));
    }

    void navigateToNotificationTransaction() {
        trackAction(NotificationConstants.TRACKING_PARAMS.NAVIGATION_TO_NOTIFICATION_TRANSACTIONS, TrackingParam.VAL_VIEW_NOTIFICATIONS);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_TRANSACTION));
    }

    void navigateToNotificationOffers(String screenName) {
        trackAction(NotificationConstants.TRACKING_PARAMS.NAVIGATION_TO_NOTIFICATION_FOR_U, TrackingParam.VAL_VIEW_NOTIFICATIONS);
        NavigationTarget navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.PRE_APPROVED_OFFERS_NOTIFICATIONS);
        if (!featureSetController.isFeatureDisabled(FeatureConstants.DynamicToggle.FTR_FOR_YOU_AND_VALUE_ADDS_OFFERS)) {
            navigationTarget = NavigationTarget.to(PreApprovedOffersNavigationTarget.FOR_YOU_OFFERS_ENHANCE);
        }
        mNavigationRouter.navigateTo(navigationTarget
                .withParam(Constants.BundleKeys.SCREEN_NAME, screenName));
    }

    public void navigateToNotificationPreferences() {
        trackAction(ProfileTracking.CLICK_NOTIFICATION_PREFERENCES, TrackingParam.VAL_NOTIFICATIONS_PREFERENCES);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.TARGET_NOTIFICATION_PREFRENCES));
    }

    void toggleMessagesReadUnread() {
        if (view != null) {
            boolean isOfferAvailable = mApplicationStorage.getBoolean(StorageKeys.IS_PREAPPROVED_OFFERS_AVAILABLE, false);
            view.markReadOffers(!isOfferAvailable);
            if (isOfferAvailable) {
                int unreadOffersCount = mMemoryApplicationStorage.getInteger(StorageKeys.PRE_APPROVED_OFFERS_COUNT, 0);
                if (unreadOffersCount > 0) {
                    view.updateOffersCounter(unreadOffersCount);
                }
            } else {
                mMemoryApplicationStorage.clearValue(StorageKeys.PRE_APPROVED_OFFERS_COUNT);
            }
            int unreadCountMsg = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, 0);
            int unreadCountTxn = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, 0);
            updateNotificationCountUi(unreadCountTxn, unreadCountMsg);
        }
    }

    void toggleTransactionMessagesReadUnread() {
        getFBNotificationsCountExtendedUseCase.execute(createFBNotificationCountRequest())
                .compose(bindToLifecycle())
                .subscribe(notificationCountData -> {
                    updateNotificationCountUi(handleNotificationCount(notificationCountData), handleMessagesCount(notificationCountData));
                }, throwable -> {
                    if (view != null) {
//                        view.handleErrorPreApprovedOffers();
                    }
                });


    }

    void getPreApprovedOffersCount() {
        mGetPreApprovedOffersUseCase.execute(new GetPreApprovedOfferRequestData(za.co.nedbank.core.Constants.PreApprovedOffersProductCode.ALL.getProductCode(), null))
                .compose(bindToLifecycle())
                .subscribe(preApprovedOffersData -> {
                    mApplicationStorage.putBoolean(StorageKeys.IS_GET_PREAPPROVED_OFFER_COUNT_API_REQUIRED, false);
                    toggleMessagesReadUnread();
                }, throwable -> {

                });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNotificationReceived(NotificationEvent notificationEvent) {
        toggleMessagesRead();
    }

    public void toggleMessagesRead() {
        if (view != null) {
            int unreadMessagesCount = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, 0);
            view.markReadMessages(unreadMessagesCount <= 0);
            if (unreadMessagesCount > 0) {
                view.updateMessageCounter(unreadMessagesCount);
            }
            updateBadge();
        }
    }


    private void updateNotificationCountUi(int txCount, int msgCount) {
        mApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.TOTAL_UNREAD_NOTIFICATION_COUNT, txCount + msgCount);
        if (view != null) {
            view.receiveTransactionNotificationCount(txCount);
            view.markReadMessages(msgCount <= 0);
            if (msgCount > 0) {
                view.updateMessageCounter(msgCount);
            }
            updateBadge();
        }
    }

    private int handleNotificationCount(FBNotificationCountResponseData notificationCountResponseData) {
        mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, notificationCountResponseData.getTransactionCount());
        return notificationCountResponseData.getTransactionCount();
    }

    private int handleMessagesCount(FBNotificationCountResponseData notificationCountResponseData) {
        mMemoryApplicationStorage.putInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, notificationCountResponseData.getGeneralCount());
        return notificationCountResponseData.getGeneralCount();
    }

    private FBNotificationsCountRequestData createFBNotificationCountRequest() {
        FBNotificationsCountRequestData fbNotificationsCountRequestData = new FBNotificationsCountRequestData();
        fbNotificationsCountRequestData.setStatus(NotificationConstants.NOTIFICATION_STATUS_TYPES.NOTIFICATION_UNREAD);
        return fbNotificationsCountRequestData;
    }

    protected void navigateToHomeActivity(boolean isInvalidateCache) {
        if (view == null) {
            return;
        }
        NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.HOME);
        navigationTarget.withParam(za.co.nedbank.core.Constants.IS_INVALIDATE_CACHE, isInvalidateCache);
        navigationTarget.withIntentFlagClearTopSingleTop(true);
        mNavigationRouter.navigateTo(navigationTarget);
    }

    public boolean isNonTpUser() {
        String clientType = mApplicationStorage.getString(StorageKeys.CLIENT_TYPE, ClientType.TP.toString());
        return ClientType.NON_TP.toString().equals(clientType);
    }

    public boolean isSalesUser() {
        String clientType = mApplicationStorage.getString(StorageKeys.CLIENT_TYPE, ClientType.TP.toString());
        return ClientType.SALES.toString().equals(clientType);
    }

    public void onLoadAnalytics() {
        if (isNonTpUser()) {
            analytics.sendEvent(StringUtils.EMPTY_STRING, TrackingParam.NON_TP_FEATURE_NOTIFICATION_LOAD, StringUtils.EMPTY_STRING);
        }
    }

    public void handleBackAnalytics() {
        if (isNonTpUser()) {
            analytics.sendEvent(StringUtils.EMPTY_STRING, TrackingParam.NON_TP_FEATURE_NOTIFICATION_BACK, StringUtils.EMPTY_STRING);
        }
    }

    private void updateBadge() {
        int unreadCountMsg = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_NOTIFICATION_COUNT, 0);
        int unreadCountTxn = mMemoryApplicationStorage.getInteger(NotificationConstants.STORAGE_KEYS.UNREAD_TRN_NOTIFICATION_COUNT, 0);
        int totalUnreadCount = unreadCountMsg + unreadCountTxn;
        view.updateCount(totalUnreadCount);
    }

    public void trackAction(String action, String subFeature) {
        HashMap<String, Object> cdata = new HashMap<>();
        AdobeContextData adobeContextData = new AdobeContextData(cdata);
        adobeContextData.setSubFeature(subFeature);
        analytics.sendEventActionWithMap(action, cdata);
    }
}
