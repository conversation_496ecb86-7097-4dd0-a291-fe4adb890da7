package za.co.nedbank.ui.view.retention;

import android.app.Activity;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.accessibility.AccessibilityEvent;

import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import za.co.nedbank.R;
import za.co.nedbank.core.Constants;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.utils.AccessibilityUtils;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.databinding.ActivityMultipleAccountsShareBinding;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.view.account.branchcode.model.BranchCodeViewModel;
import za.co.nedbank.ui.di.AppDI;

public class MultipleAccountsShareActivity extends NBBaseActivity implements MultipleAccountsShareView, MultipleAccountsShareAdapter.IItemClickCallback {

    @Inject
    MultipleAccountsSharePresenter mPresenter;
    private UserDetailViewModel userDetailViewModel;
    private List<BranchCodeViewModel> branchCodeViewModelList;
    private ActivityMultipleAccountsShareBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMultipleAccountsShareBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        AppDI.getActivityComponent(this).inject(this);
        initView();
    }

    private void initView() {
        binding.tvShareDetail.setTypeface(Typeface.createFromAsset(getAssets(), getString(R.string.font_path_nedbank_sans)));
        if(isDebitOrderRetentionFlow()) {
            binding.tvShareDetail.setText(getResources().getString(R.string.debit_order_multi_account_label));
            binding.tvWhichAccountDetails.setText(getResources().getString(R.string.which_account_you_like_to_debit));
        }
        binding.recyclerview.setItemAnimator(null);
        binding.recyclerview.setHasFixedSize(true);
        binding.recyclerview.setLayoutManager(new LinearLayoutManager(MultipleAccountsShareActivity.this));
        binding.ivMultipleAccountBack.setOnClickListener(v -> onBackPressed());
        binding.ivCrossMultipleAccount.setOnClickListener(v -> mPresenter.handleCrossButtonClick());
    }

    @Override
    public void onResume() {
        super.onResume();
        mPresenter.bind(this);
        mPresenter.getCombineAccountDetails(isDebitOrderRetentionFlow());
    }

    @Override
    protected void onPause() {
        super.onPause();
        mPresenter.unbind();
    }

    @Override
    public void showLoadingProgress(boolean inProgress) {
        setEnabledActivityTouch(!inProgress);
        if (inProgress) {
            ViewUtils.showViews(binding.rlProgressView);
            ViewUtils.hideViews(binding.multipleAccountGroup);
        }else {

            ViewUtils.hideViews(binding.rlProgressView);
            ViewUtils.showViews(binding.multipleAccountGroup);
            setupAccessibility();
        }
    }

    private void setupAccessibility(){
        if (AccessibilityUtils.isAccessibilityServiceEnabled(this)) {
            binding.ivMultipleAccountBack.postDelayed(() -> {
                binding.ivMultipleAccountBack.requestFocus();
                binding.ivMultipleAccountBack.setFocusable(true);
                binding.ivMultipleAccountBack.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_FOCUSED);
                binding.ivMultipleAccountBack.sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED);
            }, 700);
        }
    }

    @Override
    public void setAccounts(List<AccountSummary> accounts) {
        List<AccountSummary> profileAccounts = new ArrayList<>();
        if (accounts != null && accounts.size()>0) {
            for (AccountSummary accountSummary : accounts) {
                if (accountSummary != null && accountSummary.isProfileAccount()) {
                    profileAccounts.add(accountSummary);
                }
            }
        }
        if(profileAccounts.size()>1) {
            showLoadingProgress(false);
            MultipleAccountsShareAdapter accountTypeListAdapter = new MultipleAccountsShareAdapter(profileAccounts, MultipleAccountsShareActivity.this);
            binding.recyclerview.setAdapter(accountTypeListAdapter);
        }
        else if(profileAccounts.size()==1){
            finishScreen();
            mPresenter.handleShareAccountClick(
                    userDetailViewModel,
                    profileAccounts.get(0),getAccountTypeValue(profileAccounts.get(0)),
                    profileAccounts.get(0) == null ? StringUtils.EMPTY_STRING
                            : getBranchCodeValue(profileAccounts.get(0).getAccountCode()));
        }
    }

    @Override
    public String getOverviewProductGroup(OverviewType overviewType) {
        String value = null;
        if(overviewType != null) {
            value = getString(overviewType.getAccountTypeId());
            return value;
        }
        return value;
    }


    @Override
    public String getAccountHolderName(AccountSummary accounts) {

        if(accounts!=null && accounts.getAccountCode()!=null) {
            if (accounts.getAccountCode().equals(Constants.ACCOUNT_TYPES.HL.getAccountTypeCode())) {
                return accounts.getAccountHolderName()!=null?accounts.getAccountHolderName():StringUtils.EMPTY_STRING;
            }
            if (accounts.getAccountCode().equals(Constants.ACCOUNT_TYPES.CA.getAccountTypeCode())
                    || accounts.getAccountCode().equals(Constants.ACCOUNT_TYPES.SA.getAccountTypeCode())
                    || accounts.getAccountCode().equals(Constants.ACCOUNT_TYPES.PL.getAccountTypeCode())) {

                if (!accounts.isAlternateAccount()) {
                    return String.format("%s %s", userDetailViewModel.getFirstName(), userDetailViewModel.getSurname());
                } else {
                    return accounts.getAccountHolderName()!=null?accounts.getAccountHolderName():StringUtils.EMPTY_STRING;
                }
            }
        }
        return String.format("%s %s", userDetailViewModel.getFirstName(), userDetailViewModel.getSurname());
    }

    @Override
    public void setUserInfo(UserDetailViewModel userDetailViewModel) {
        this.userDetailViewModel = userDetailViewModel;
    }

    @Override
    public void setBranchCodeList(List<BranchCodeViewModel> branchCodeViewModelList) {
        this.branchCodeViewModelList = branchCodeViewModelList;
    }

    @Override
    public void setActivityResult() {
        setResult(Activity.RESULT_OK);
    }

    @Override
    public void finishScreen() {
        finish();
    }

    @Override
    public void onItemClick(int position, AccountSummary accountDto) {
        mPresenter.handleShareAccountClick(
                userDetailViewModel,
                accountDto,getAccountTypeValue(accountDto),
                getBranchCodeValue(accountDto.getAccountCode()));
    }

    private String getBranchCodeValue(String accountType) {
        if (branchCodeViewModelList != null && branchCodeViewModelList.size() > 0) {
            for (int i = 0; i < branchCodeViewModelList.size(); i++) {
                if (branchCodeViewModelList.get(i).getAccountType()!=null && accountType.contains(branchCodeViewModelList.get(i).getAccountType())) {
                    return branchCodeViewModelList.get(i).getBranchCode()!=null ? branchCodeViewModelList.get(i).getBranchCode():StringUtils.EMPTY_STRING;
                }
            }
        }
        return StringUtils.EMPTY_STRING;
    }

    private String getAccountTypeValue(AccountSummary mAccounts){

        if(mAccounts!=null && mAccounts.getAccountCode()!=null) {
            String accountType = mAccounts.getAccountCode();
            if (accountType.equalsIgnoreCase(Constants.ACCOUNT_TYPES.CA.getAccountTypeCode())) {
                return getString(za.co.nedbank.services.R.string.current_account_sa);
            } else if (accountType.equalsIgnoreCase(Constants.ACCOUNT_TYPES.SA.getAccountTypeCode())) {
                return getString(za.co.nedbank.services.R.string.savings_account_sa);
            } else if (accountType.equalsIgnoreCase(Constants.ACCOUNT_TYPES.HL.getAccountTypeCode())) {
                return getString(za.co.nedbank.services.R.string.home_loan_sa);
            } else if (accountType.equalsIgnoreCase(Constants.ACCOUNT_TYPES.PL.getAccountTypeCode())) {
                return getString(za.co.nedbank.services.R.string.personal_loan_sa);
            } else if (accountType.equalsIgnoreCase(Constants.ACCOUNT_TYPES.IS.getAccountTypeCode())) {
                return getString(za.co.nedbank.services.R.string.mfc_account_type);
            } else {
                return accountType;
            }
        }
        return StringUtils.EMPTY_STRING;
    }

    @Override
    public void onBackPressed() {
        mPresenter.sendBackClickEvent();
        super.onBackPressed();
    }

    @Override
    public boolean isDebitOrderRetentionFlow() {
        return (getIntent() != null && getIntent().getExtras() != null &&
                getIntent().getExtras().containsKey(NavigationTarget.IS_IN_RETENTION_DEBIT_ORDER_FLOW)) &&
                getIntent().getExtras().getBoolean(NavigationTarget.IS_IN_RETENTION_DEBIT_ORDER_FLOW);
    }

}
