package za.co.nedbank.ui.view.home.investments;

import java.util.List;

import za.co.nedbank.core.ClientType;
import za.co.nedbank.core.base.NBBaseView;
import za.co.nedbank.services.domain.model.account.AccountSummary;

public interface AccountTypeListView extends NBBaseView {

    List<AccountSummary> getAccountSummeryList();

    boolean canTransact();

    String getFicaStatus();

    String getClientType();

    boolean isSAResident();

    String getBirthDate();

    String getCisNumber();

    String getSecOfficerCd();

    ClientType getUserType();

    String getProductName();

    String getDefaultAccountId();

    void showProgressBar(boolean progress);

    void showError(String message);

    void updateInvestAccountList(int id);


}
