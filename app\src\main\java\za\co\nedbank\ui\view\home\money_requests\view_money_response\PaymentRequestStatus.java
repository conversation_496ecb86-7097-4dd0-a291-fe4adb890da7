/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.money_requests.view_money_response;

import androidx.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;



/**
 * Created by swapnil.gawande on 2/25/2018.
 */

@StringDef({PaymentRequestStatus.PAID, PaymentRequestStatus.PENDING, PaymentRequestStatus.REJECTED})
@Retention(RetentionPolicy.RUNTIME)
@interface PaymentRequestStatus {
    String PAID = "AUT";
    String PENDING = "PEN";
    String REJECTED = "DEC";
}
