package za.co.nedbank.ui.view.retention;

import android.text.TextUtils;

import java.util.HashMap;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.base.NBBasePresenter;
import za.co.nedbank.core.data.accounts.model.AccountsContainerDto;
import za.co.nedbank.core.domain.model.UserDetailData;
import za.co.nedbank.core.domain.usecase.GetUserDetailUseCase;
import za.co.nedbank.core.enroll.domain.usecase.GetAccountsUseCase;
import za.co.nedbank.core.errors.ErrorProvider;
import za.co.nedbank.core.errors.ErrorType;
import za.co.nedbank.core.logging.NBLogger;
import za.co.nedbank.core.navigation.NavigationRouter;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.tracking.Analytics;
import za.co.nedbank.core.tracking.TrackingEvent;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.view.mapper.UserDetailDataToViewModelMapper;
import za.co.nedbank.core.view.model.UserDetailViewModel;
import za.co.nedbank.services.Constants;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.branchcode.BranchCodeDataModel;
import za.co.nedbank.services.domain.model.overview.OverviewType;
import za.co.nedbank.services.domain.usecase.branchcode.BranchCodeUseCase;
import za.co.nedbank.services.view.account.branchcode.model.BranchCodeViewModel;
import za.co.nedbank.services.view.mapper.BranchCodeDataModelToBranchCodeViewModelMapper;
import za.co.nedbank.services.view.navigation.ServicesNavigationTarget;
import za.co.nedbank.ui.domain.model.retention.CombineUserMultipleAccountsData;
import za.co.nedbank.ui.view.tracking.AppTracking;

public class MultipleAccountsSharePresenter extends NBBasePresenter<MultipleAccountsShareView> {

    private static final String TAG = MultipleAccountsSharePresenter.class.getSimpleName();
    private final GetAccountsUseCase getAccountsUseCase;
    private final GetUserDetailUseCase getUserDetailUseCase;
    private final BranchCodeUseCase branchCodeUseCase;
    private final BranchCodeDataModelToBranchCodeViewModelMapper modelToBranchCodeViewModelMapper;
    private final ErrorProvider errorProvider;
    private final UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper;
    private final Analytics mAnalytics;
    private final NavigationRouter mNavigationRouter;

    @Inject
    public MultipleAccountsSharePresenter(final GetAccountsUseCase getAccountsUseCase,final GetUserDetailUseCase getUserDetailUseCase,
                                          final UserDetailDataToViewModelMapper mUserDetailDataToViewModelMapper,final ErrorProvider errorProvider,
                                          final BranchCodeUseCase branchCodeUseCase,final BranchCodeDataModelToBranchCodeViewModelMapper modelToBranchCodeViewModelMapper
            ,final Analytics analytics, final NavigationRouter navigationRouter){
        this.getAccountsUseCase = getAccountsUseCase;
        this.getUserDetailUseCase = getUserDetailUseCase;
        this.branchCodeUseCase = branchCodeUseCase;
        this.mUserDetailDataToViewModelMapper = mUserDetailDataToViewModelMapper;
        this.errorProvider = errorProvider;
        this.modelToBranchCodeViewModelMapper = modelToBranchCodeViewModelMapper;
        this.mAnalytics = analytics;
        this.mNavigationRouter = navigationRouter;
    }

    @Override
    protected void onBind(){
        super.onBind();
        sendPageEvent();
    }
    void getCombineAccountDetails(boolean isDebitOrderFlow) {
        if (view != null) {
            view.showLoadingProgress(true);
        }
        Observable<List<AccountsContainerDto>> accountsObservable = getAccountsUseCase
                .execute()
                .compose(bindToLifecycle());
        Observable<UserDetailData> userDetailObservable = getUserDetailUseCase.execute(false).compose(bindToLifecycle());
        Observable<List<BranchCodeDataModel>> branchCodeObservable = branchCodeUseCase.execute().compose(bindToLifecycle());

        if (accountsObservable != null && userDetailObservable != null && branchCodeObservable != null) {
            Observable.zip(accountsObservable, userDetailObservable, branchCodeObservable, (accountsContainerDtos, userDetail, branchCodeDataModelList) -> {
                CombineUserMultipleAccountsData combineUserAccountDetailsData = new CombineUserMultipleAccountsData();
                combineUserAccountDetailsData.setAccountsContainerDtos(accountsContainerDtos);
                combineUserAccountDetailsData.setUserDetail(userDetail);
                combineUserAccountDetailsData.setBranchCodeDataModels(branchCodeDataModelList);
                return combineUserAccountDetailsData;
            }).compose(bindToLifecycle())
                    .subscribe(combineUserAccountDetailsData -> {
                        if (view != null) {
                            if (combineUserAccountDetailsData.getUserDetail() != null) {
                                view.setUserInfo(mUserDetailDataToViewModelMapper.mapDataToViewModelMapper(combineUserAccountDetailsData.getUserDetail()));
                            }
                            if(combineUserAccountDetailsData.getBranchCodeDataModels()!=null){
                                setBranchCode(modelToBranchCodeViewModelMapper.mapBranchCodeViewModel(combineUserAccountDetailsData.getBranchCodeDataModels()));
                            }
                            if(combineUserAccountDetailsData.getAccountsContainerDtos()!=null) {
                                view.setAccounts(combineUserAccountDetailsData.mapAccountWithType(combineUserAccountDetailsData.getAccountsContainerDtos(), isDebitOrderFlow));
                            }
                        }
                    }, throwable -> handleError(errorProvider.getMessage(ErrorType.GENERIC)));
        }
    }

    private void handleError(final String message) {
        NBLogger.e(TAG, message);
        if (view != null) {
            view.setActivityResult();
            view.finishScreen();
        }
    }

    private void setBranchCode(List<BranchCodeViewModel> branchCodeViewModelList) {
        view.setBranchCodeList(branchCodeViewModelList);
    }

    void handleShareAccountClick(UserDetailViewModel userDetailViewModel,
                                 AccountSummary account, String accountTypeValue, String branchCode) {

        if (view != null) {
            trackActionAccountProductNGroup(account.getAccountType(), accountTypeValue);
            if(view.isDebitOrderRetentionFlow()){
                mNavigationRouter.navigateTo(NavigationTarget.to(ServicesNavigationTarget.DEBIT_ORDER_HOW_IT_WORKS)
                        .withParam(Constants.BUNDLE_ACCOUNT_ID, account.getId()));
            }else {

                NavigationTarget navigationTarget = NavigationTarget.to(NavigationTarget.SHARE_ACC_INFO);
                navigationTarget.withParam(Constants.BUNDLE_ACCOUNT_NUMBER, account.getNumber());
                navigationTarget.withParam(Constants.BUNDLE_ACCOUNT_NAME, view.getAccountHolderName(account));
                navigationTarget.withParam(Constants.BUNDLE_ACCOUNT_TYPE, accountTypeValue);
                navigationTarget.withParam(Constants.BUNDLE_BRANCH_CODE, branchCode);
                navigationTarget.withParam(Constants.BUNDLE_CIS_NUMBER, userDetailViewModel.getCisNumber());
                navigationTarget.withParam(Constants.BUNDLE_EMAIL, userDetailViewModel.getEmailAddress());
                mNavigationRouter.navigateTo(navigationTarget);
            }
        }
    }

    private void trackActionAccountProductNGroup(OverviewType overviewType, String product) {
        String productGroup = view.getOverviewProductGroup(overviewType);
        if (!TextUtils.isEmpty(productGroup) && !TextUtils.isEmpty(product)) {
            String val = productGroup + ";" + product + ";;";

            HashMap<String,Object> cdata = new HashMap<>();
            cdata.put(TrackingEvent.ANALYTICS.KEY_PRODUCTS, val);
            mAnalytics.sendEventActionWithMap(AppTracking.RETENTION_CARD_MULTIPLE_SHARE_ACCOUNTS_SELECT, cdata);
        }
    }

    void handleCrossButtonClick(){
        mAnalytics.sendEvent(AppTracking.RETENTION_CLICK_MULTIPLE_SHARE_ACCOUNTS_CLOSE,StringUtils.EMPTY_STRING,StringUtils.EMPTY_STRING);
        mNavigationRouter.navigateTo(NavigationTarget.to(NavigationTarget.HOME).withIntentFlagClearTopSingleTop(true));
    }

    private void sendPageEvent() {
        mAnalytics.sendState(AppTracking.RETENTION_SCREEN_MULTIPLE_SHARE_ACCOUNTS);
    }

    public void sendBackClickEvent() {
        mAnalytics.sendEvent(AppTracking.RETENTION_CLICK_MULTIPLE_SHARE_ACCOUNTS_BACK,StringUtils.EMPTY_STRING,StringUtils.EMPTY_STRING);
    }
}


