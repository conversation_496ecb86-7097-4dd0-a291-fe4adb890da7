package za.co.nedbank.ui.view.home.non_tp_client.non_tp_dashboard;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.OvalShape;
import android.text.TextUtils;

import androidx.core.content.ContextCompat;

import org.zakariya.stickyheaders.SectioningAdapter;

import za.co.nedbank.R;
import za.co.nedbank.core.data.storage.StorageKeys;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.feature.FeatureConstants;
import za.co.nedbank.core.feature.FeatureSetController;
import za.co.nedbank.core.sharedui.listener.ISectionedListItemSelectedListener;
import za.co.nedbank.core.utils.FormattingUtil;
import za.co.nedbank.core.utils.StringUtils;
import za.co.nedbank.core.utils.ViewUtils;
import za.co.nedbank.databinding.ViewNonTpAccountsItemBinding;
import za.co.nedbank.services.Constants;
import za.co.nedbank.services.domain.model.account.AccountSummary;
import za.co.nedbank.services.domain.model.overview.OverviewType;

public class NonTpAccountsItemViewHolder extends SectioningAdapter.ItemViewHolder {
    private ApplicationStorage applicationStorage;

    private final FeatureSetController featureSetController;

    private int[] colors = new int[]{za.co.nedbank.services.R.color.overview_everyday_pattern_1, za.co.nedbank.services.R.color.overview_everyday_pattern_2,
            za.co.nedbank.services.R.color.overview_everyday_pattern_3, za.co.nedbank.services.R.color.overview_everyday_pattern_4,
            za.co.nedbank.services.R.color.overview_everyday_pattern_5, za.co.nedbank.services.R.color.overview_everyday_pattern_6,
            za.co.nedbank.services.R.color.overview_everyday_pattern_7, za.co.nedbank.services.R.color.overview_everyday_pattern_8,
            za.co.nedbank.services.R.color.overview_everyday_pattern_9};


    private Context mContext;
    private final ISectionedListItemSelectedListener mIViewHolderInteraction;
    private int mSectionPosition, mChildPosition;
    private ViewNonTpAccountsItemBinding binding;

    public NonTpAccountsItemViewHolder(Context context, ViewNonTpAccountsItemBinding binding, ISectionedListItemSelectedListener itemSelectedListener, ApplicationStorage applicationStorage, FeatureSetController featureSetController) {
        super(binding.getRoot());
        this.binding = binding;
        this.mIViewHolderInteraction = itemSelectedListener;
        this.mContext = context;
        this.applicationStorage = applicationStorage;
        this.featureSetController = featureSetController;
    }

    public void setSectionPosition(int sectionPosition) {
        this.mSectionPosition = sectionPosition;
    }

    public void setChildPosition(int childPosition) {
        this.mChildPosition = childPosition;
    }

    public void setup(AccountSummary accountSummary) {
        ViewUtils.showViews(binding.tvNonTpBalanceAmount, binding.tvNonTpBalanceInfo, binding.ivAccountDetailArrow);
        ViewUtils.hideViews(binding.tvJoin,binding.dividerGreenback);
        itemView.setBackgroundColor(ContextCompat.getColor(mContext,R.color.white));
        binding.ivAccountTypeBubble.setImageDrawable(getShapeDrawable(colors[mChildPosition % colors.length]));
        if(accountSummary.getAccountType() == OverviewType.REWARDS && TextUtils.isEmpty(accountSummary.getNumber())){
            boolean isEnrolmentInProgress = applicationStorage.getBoolean(StorageKeys.IS_GB_REWARDS_ENROLMENT_IN_PROGRESS, false);
            int joinTextResId = isEnrolmentInProgress ? za.co.nedbank.services.R.string.join_in_progress : za.co.nedbank.services.R.string.join;
            binding.tvJoin.setText(joinTextResId);
            binding.tvNonTpAccountNo.setText(mContext.getString(R.string.label_green_backs));
            binding.tvNonTpBalanceAmount.setText(mContext.getString(R.string.green_back_empty_points));
            itemView.setBackgroundColor(ContextCompat.getColor(mContext,R.color.accounts_green_back_background));
            ViewUtils.showViews(binding.tvJoin,binding.dividerGreenback);
            ViewUtils.hideViews(binding.tvNonTpBalanceInfo,binding.ivAccountDetailArrow);
        }else if (accountSummary.getAccountType() == OverviewType.EVERYDAY_BANKING && accountSummary.isPreApprovedOfferAccount()) {
            binding.tvNonTpAccountNo.setText(accountSummary.getName());
            ViewUtils.hideViews(binding.tvNonTpBalanceInfo, binding.ivAccountDetailArrow);
            ViewUtils.showViews(binding.tvJoin,binding.dividerGreenback);
            itemView.setBackgroundColor(ContextCompat.getColor(mContext,R.color.accounts_green_back_background));
            binding.tvJoin.setAllCaps(true);
            binding.tvJoin.setText(mContext.getString(R.string.offer_text));
            binding.tvNonTpBalanceAmount.setText(String.format("%s%s%s", FormattingUtil.convertToSouthAfricaFormattedCurrency(accountSummary.getSummaryValue().doubleValue()), StringUtils.NEW_LINE, mContext.getString(R.string.edb_banner_text)));
        }else if (accountSummary.getAccountType() == OverviewType.INVESTMENTS){
            setupInvestments(accountSummary);
        } else if (accountSummary.getAccountType() == OverviewType.EVERYDAY_BANKING
                && Constants.NON_TP_CH_TITLE.equals(accountSummary.getName())) {
            setupCreditHealth();
        } else {
            setupDefault(accountSummary);
        }
        binding.rlNonTpItemContainer.setOnClickListener(v -> handleItemClick());
    }

    private void setupInvestments(AccountSummary accountSummary) {
        boolean showCategoryDashboard = !featureSetController.isFeatureDisabled(FeatureConstants.INVESTMENT_ONLINE_NONTP_DASHBOARD_CATEGORY);
        if (showCategoryDashboard){
            binding.tvNonTpAccountNo.setText(accountSummary.getName() + StringUtils.SPACE + StringUtils.OPEN_BRACE + accountSummary.getNoticeProductCounter() + StringUtils.CLOSE_BRACE);
        } else {
            binding.tvNonTpAccountNo.setText(accountSummary.getName());
        }
        binding.tvNonTpBalanceInfo.setText(R.string.available_bal_heading);
        binding.tvNonTpBalanceAmount.setText(FormattingUtil.convertToSouthAfricaFormattedCurrency(accountSummary.getTotalAvailableBalance()));
    }

    private void setupCreditHealth() {
        binding.tvNonTpAccountNo.setText(mContext.getResources().getString(R.string.non_tp_credit_health_title));
        binding.tvNonTpBalanceInfo.setText(mContext.getResources().getString(R.string.non_tp_credit_health_sub_title));
        ViewUtils.hideViews(binding.tvNonTpBalanceAmount);
    }

    private void setupDefault(AccountSummary accountSummary) {
        if (accountSummary.getAccountType() == OverviewType.LOANS) {
            binding.tvNonTpBalanceInfo.setText(mContext.getResources().getString(R.string.account_type_summary_loan));
        } else if (accountSummary.getAccountType() == OverviewType.FOREIGN_CURRENCY_ACCOUNT) {
            ViewUtils.hideViews(binding.tvNonTpBalanceAmount, binding.tvNonTpBalanceInfo);
        }else {
            binding.tvNonTpBalanceInfo.setText(mContext.getResources().getString(R.string.account_type_summary_investments));
        }
        if(accountSummary.getAccountType() == OverviewType.INVESTMENTS_PENDING_ACCOUNTS) {
            binding.tvNonTpBalanceAmount.setText(accountSummary.getCurrency());
            ViewUtils.hideViews(binding.tvNonTpBalanceInfo, binding.ivAccountDetailArrow);
            binding.tvNonTpAccountNo.setText( accountSummary.getName());

        }else{
            binding.tvNonTpAccountNo.setText( getAccountName(accountSummary.getName()) + accountSummary.getNumber());
            binding.tvNonTpBalanceAmount.setText(FormattingUtil.convertToSouthAfricaFormattedCurrency(accountSummary.getSummaryValue().doubleValue()));
        }
    }

    private String getAccountName(String accountName){
        if(StringUtils.isNullOrEmpty(accountName))
            return StringUtils.EMPTY_STRING;
        else
            return accountName + StringUtils.SPACE + StringUtils.HYPHEN + StringUtils.SPACE;
    }

    private Drawable getShapeDrawable(int color){
        int size = mContext.getResources().getDimensionPixelSize(R.dimen.dimen_11dp);
        ShapeDrawable badge = new ShapeDrawable (new OvalShape());
        badge.setIntrinsicWidth (size);
        badge.setIntrinsicHeight (size);
        badge.getPaint().setColor(mContext.getResources().getColor(color));
        return badge;
    }


    public void handleItemClick(){
        if(mIViewHolderInteraction != null){
            mIViewHolderInteraction.itemSelected(mSectionPosition, mChildPosition);
        }
    }
}
