/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.entity.money_request.mapper;

import javax.inject.Inject;

import za.co.nedbank.core.data.mapper.MetaDataEntityToDataMapper;
import za.co.nedbank.ui.data.entity.money_request.NotificationDataEntity;
import za.co.nedbank.ui.data.entity.money_request.NotificationsResponseEntity;
import za.co.nedbank.ui.domain.model.money_request.NotificationDataModel;
import za.co.nedbank.ui.domain.model.money_request.NotificationsResponseDataModel;

/**
 * Created by sandip.lawate on 2/22/2018.
 */

public class NotificationsResponseEntityToDataMapper {

    private final MetaDataEntityToDataMapper metaDataEntityToDataMapper;

    @Inject
    NotificationsResponseEntityToDataMapper(MetaDataEntityToDataMapper metaDataEntityToDataMapper) {
        this.metaDataEntityToDataMapper = metaDataEntityToDataMapper;
    }

    public NotificationsResponseDataModel mapResponseEntityToData(NotificationsResponseEntity notificationsResponseEntity) {
        NotificationsResponseDataModel notificationsResponseDataModel = new NotificationsResponseDataModel();
        if (notificationsResponseEntity != null) {
            notificationsResponseDataModel.setMetadata(metaDataEntityToDataMapper.mapMetaData(notificationsResponseEntity.getMetaDataEntity()));
            notificationsResponseDataModel.setNotificationDataModel(mapNotificationsData(notificationsResponseEntity.getNotificationDataEntity()));
        }
        return notificationsResponseDataModel;
    }

    private NotificationDataModel mapNotificationsData(NotificationDataEntity notificationDataEntity) {
        NotificationDataModel notificationDataModel = new NotificationDataModel();
        notificationDataModel.setNotificationCounter(notificationDataEntity.getNotificationCounter());
        return notificationDataModel;
    }

}
