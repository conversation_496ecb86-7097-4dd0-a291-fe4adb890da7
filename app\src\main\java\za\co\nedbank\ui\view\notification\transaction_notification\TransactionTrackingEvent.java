package za.co.nedbank.ui.view.notification.transaction_notification;

public interface TransactionTrackingEvent {


    interface ActionName {
        String TRANSACTION_NOTIFICATION_REPORT_DISPUTE_CONFIRM = "transaction_notification_report_dispute_confirm";
        String TRANSACTION_NOTIFICATION_REPORT_FRAUD_CONFIRM = "transaction_notification_report_fraud_confirm";
        String TRANSACTION_NOTIFICATION_REPORT_DISPUTE_CANCEL = "transaction_notification_report_dispute_cancel";
        String TRANSACTION_NOTIFICATION_REPORT_FRAUD_CANCEL = "transaction_notification_report_fraud_cancel";
    }
}
