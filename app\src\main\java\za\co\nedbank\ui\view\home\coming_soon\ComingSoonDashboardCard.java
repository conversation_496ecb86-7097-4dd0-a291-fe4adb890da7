/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.view.home.coming_soon;

import android.view.View;

import za.co.nedbank.core.dashboard.DashboardCard;
import za.co.nedbank.core.dashboard.DashboardCardType;

/**
 * Created by ch<PERSON><PERSON> on 09-09-2017.
 */

public class ComingSoonDashboardCard extends DashboardCard {

    public ComingSoonDashboardCard(DashboardCardType cardType, View content) {
        super(cardType, content);
    }

    @Override
    public boolean shouldReloadContent() {
        return true;
    }
}
