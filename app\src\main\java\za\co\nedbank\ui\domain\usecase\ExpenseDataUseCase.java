/*
 * Copyright © 2017 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.annotations.NonNull;
import za.co.nedbank.core.domain.UseCase;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.ui.domain.model.ExpenseInputModel;
import za.co.nedbank.ui.domain.model.ExpenseOutputModel;
import za.co.nedbank.ui.domain.repository.IFinanceDataRepository;

/**
 * Created by piyushgupta01 on 7/20/2017.
 */

public class ExpenseDataUseCase extends UseCase<ExpenseInputModel, ExpenseOutputModel> {

    private final IFinanceDataRepository mFinanceDataRepository;

    @Inject
    protected ExpenseDataUseCase(@NonNull final UseCaseComposer useCaseComposer
            , @NonNull final IFinanceDataRepository financeDataRepository) {
        super(useCaseComposer);
        this.mFinanceDataRepository = financeDataRepository;
    }

    @Override
    protected Observable<ExpenseOutputModel> createUseCaseObservable(ExpenseInputModel param) {
        return mFinanceDataRepository.getExpense().map(stringFloatMap -> {
            float sum = 0f;
            for (Float value : stringFloatMap.values()) {
                sum += value;
            }
            ExpenseOutputModel expenseOutputModel = new ExpenseOutputModel(stringFloatMap, sum);
            return expenseOutputModel;
        });
    }
}
