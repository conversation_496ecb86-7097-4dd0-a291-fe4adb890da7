/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.domain.usecase.money_request;

import javax.inject.Inject;

import io.reactivex.Observable;
import za.co.nedbank.core.domain.UseCaseComposer;
import za.co.nedbank.core.domain.VoidUseCase;
import za.co.nedbank.ui.data.entity.money_request.mapper.NotificationsResponseEntityToDataMapper;
import za.co.nedbank.ui.domain.model.money_request.NotificationsResponseDataModel;
import za.co.nedbank.ui.domain.repository.IMoneyRequestRepository;

/**
 * Created by sandip.lawate on 2/24/2018.
 */

public class GetNotificationsUsecase extends VoidUseCase<NotificationsResponseDataModel> {

    private final IMoneyRequestRepository iMoneyRequestRepository;
    private final NotificationsResponseEntityToDataMapper mNotificationsResponseEntityToDataMapper;

    @Inject
    GetNotificationsUsecase(final UseCaseComposer composer, final IMoneyRequestRepository iViewBankerDetailsRepository, NotificationsResponseEntityToDataMapper notificationsResponseEntityToDataMapper) {
        super(composer);
        this.iMoneyRequestRepository = iViewBankerDetailsRepository;
        this.mNotificationsResponseEntityToDataMapper = notificationsResponseEntityToDataMapper;
    }

    @Override
    protected Observable<NotificationsResponseDataModel> createUseCaseObservable() {
        return iMoneyRequestRepository.getNotifications().map(mNotificationsResponseEntityToDataMapper::mapResponseEntityToData);
    }
}
