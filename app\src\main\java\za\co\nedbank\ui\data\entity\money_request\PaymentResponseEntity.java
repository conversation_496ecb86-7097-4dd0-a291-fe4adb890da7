/*
 * Copyright © 2018 Nedbank. All rights reserved.
 */

package za.co.nedbank.ui.data.entity.money_request;

/**
 * Created by sandip.lawate on 2/22/2018.
 */

import com.squareup.moshi.Json;

import za.co.nedbank.core.data.metadata.MetaDataEntity;


public class PaymentResponseEntity {

    @Json(name = "data")
    private MoneyRequestDataEntity moneyRequestDataEntity;
    @Json(name = "metadata")
    private MetaDataEntity metaDataEntity;

    public MoneyRequestDataEntity getMoneyRequestDataEntity() {
        return moneyRequestDataEntity;
    }

    public MetaDataEntity getMetaDataEntity() {
        return metaDataEntity;
    }

}
