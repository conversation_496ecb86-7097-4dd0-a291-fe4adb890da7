package za.co.nedbank.ui.notifications.martec;

import android.app.Notification;
import android.os.Build;

import androidx.core.app.NotificationCompat;

import com.adobe.marketing.mobile.services.Log;
import com.adobe.marketing.mobile.util.StringUtils;
import com.google.firebase.messaging.RemoteMessage;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import za.co.nedbank.core.notification.NotificationConstants;

public class AjoPushPayloadDataModel {
    static final String SELF_TAG = "AjoPushPayloadDataModel";

    static final class NotificationPriorities {

        private NotificationPriorities() {
        }

        static final String PRIORITY_DEFAULT = "PRIORITY_DEFAULT";
        static final String PRIORITY_MIN = "PRIORITY_MIN";
        static final String PRIORITY_LOW = "PRIORITY_LOW";
        static final String PRIORITY_HIGH = "PRIORITY_HIGH";
        static final String PRIORITY_MAX = "PRIORITY_MAX";
    }

    static final class NotificationVisibility {
        private NotificationVisibility() {
        }

        static final String PUBLIC = "PUBLIC";
        static final String PRIVATE = "PRIVATE";
        static final String SECRET = "SECRET";
    }

    public static final class ActionButtonType {
        private ActionButtonType() {
        }

        public static final String DEEPLINK = "DEEPLINK";
        public static final String WEB_URL = "WEBURL";
        public static final String DISMISS = "DISMISS";
        public static final String OPEN_APP = "OPENAPP";
        public static final String LABEL_CONTINUE = "Continue";
        public static final String LABEL_DECLINE = "Decline";
    }

    static final class ActionButtons {
        private ActionButtons() {
        }

        static final String LABEL = "label";
        static final String URI = "uri";
        static final String TYPE = "type";
    }

    static final Map<String, Integer> notificationImportanceMap = new HashMap<>();

    static final Map<String, Integer> notificationVisibilityMap = new HashMap<>();

    static final Map<String, Integer> notificationPriorityMap = new HashMap<>();

    private static final int ACTION_BUTTON_CAPACITY = 3;
    private String title;
    private String body;
    private int notificationPriority = NotificationCompat.PRIORITY_DEFAULT;
    private int notificationVisibility = Notification.VISIBILITY_PRIVATE;
    private String channelId;
    private String icon;
    private String imageUrl;
    private ActionType actionType;
    private String actionUri;
    private List<ActionButton> actionButtons = new ArrayList<>(ACTION_BUTTON_CAPACITY);
    private Map<String, String> data;
    private String messageId;

    public AjoPushPayloadDataModel(final RemoteMessage message) {
        if (message == null) {
            Log.error(MessagingConstants.LOG_TAG, SELF_TAG, "Failed to create MessagingPushPayload, remote message is null");
            return;
        }
        if (message.getData().isEmpty()) {
            Log.error(MessagingConstants.LOG_TAG, SELF_TAG, "Failed to create MessagingPushPayload, remote message data payload is null");
            return;
        }

        final String _messageId = message.getMessageId();
        if (StringUtils.isNullOrEmpty(_messageId)) {
            Log.error(MessagingConstants.LOG_TAG, SELF_TAG, "Failed to create MessagingPushPayload, message id is null or empty");
            return;
        }

        this.messageId = _messageId;
        init(message.getData());
    }

    public AjoPushPayloadDataModel(final Map<String, String> data) {
        init(data);
    }

    public String getTitle() {
        return title;
    }

    public String getBody() {
        return body;
    }

    public int getNotificationPriority() {
        return notificationPriority;
    }

    public int getNotificationVisibility() {
        return notificationVisibility;
    }

    public String getChannelId() {
        return channelId;
    }

    public String getIcon() {
        return icon;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public String getMessageId() {
        return messageId;
    }

    public ActionType getActionType() {
        return actionType;
    }

    public String getActionUri() {
        return actionUri;
    }

    public List<ActionButton> getActionButtons() {
        return actionButtons;
    }

    public Map<String, String> getData() {
        return data;
    }

    private void init(final Map<String, String> data) {
        initStaticData();
        this.data = data;
        if (data == null) {
            Log.debug(MessagingConstants.LOG_TAG, SELF_TAG, "Payload extraction failed because data provided is null");
            return;
        }
        this.title = data.get(MessagingConstants.Push.PayloadKeys.TITLE);
        this.body = data.get(MessagingConstants.Push.PayloadKeys.BODY);
        this.channelId = data.get(MessagingConstants.Push.PayloadKeys.CHANNEL_ID);
        this.icon = data.get(MessagingConstants.Push.PayloadKeys.ICON);
        this.actionUri = data.get(MessagingConstants.Push.PayloadKeys.ACTION_URI);
        this.imageUrl = data.get(MessagingConstants.Push.PayloadKeys.IMAGE_URL);

        this.notificationPriority = getNotificationPriorityFromString(data.get(MessagingConstants.Push.PayloadKeys.NOTIFICATION_PRIORITY));
        this.notificationVisibility = getNotificationVisibilityFromString(data.get(MessagingConstants.Push.PayloadKeys.NOTIFICATION_VISIBILITY));
        this.actionType = getActionTypeFromString(data.get(MessagingConstants.Push.PayloadKeys.ACTION_TYPE));
        this.actionButtons = getActionButtonsFromString(data.get(MessagingConstants.Push.PayloadKeys.ACTION_BUTTONS));
    }

    private void initStaticData() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            notificationImportanceMap.put(NotificationPriorities.PRIORITY_MIN, NotificationCompat.PRIORITY_MIN);
            notificationImportanceMap.put(NotificationPriorities.PRIORITY_LOW, NotificationCompat.PRIORITY_LOW);
            notificationImportanceMap.put(NotificationPriorities.PRIORITY_DEFAULT, NotificationCompat.PRIORITY_DEFAULT);
            notificationImportanceMap.put(NotificationPriorities.PRIORITY_HIGH, NotificationCompat.PRIORITY_HIGH);
            notificationImportanceMap.put(NotificationPriorities.PRIORITY_MAX, NotificationCompat.PRIORITY_MAX);
        }

        notificationVisibilityMap.put(NotificationVisibility.PRIVATE, Notification.VISIBILITY_PRIVATE);
        notificationVisibilityMap.put(NotificationVisibility.PUBLIC, Notification.VISIBILITY_PUBLIC);
        notificationVisibilityMap.put(NotificationVisibility.SECRET, Notification.VISIBILITY_SECRET);

        notificationPriorityMap.put(NotificationPriorities.PRIORITY_MIN, NotificationCompat.PRIORITY_MIN);
        notificationPriorityMap.put(NotificationPriorities.PRIORITY_LOW, NotificationCompat.PRIORITY_LOW);
        notificationPriorityMap.put(NotificationPriorities.PRIORITY_DEFAULT, NotificationCompat.PRIORITY_DEFAULT);
        notificationPriorityMap.put(NotificationPriorities.PRIORITY_HIGH, NotificationCompat.PRIORITY_HIGH);
        notificationPriorityMap.put(NotificationPriorities.PRIORITY_MAX, NotificationCompat.PRIORITY_MAX);
    }

    private int getNotificationPriorityFromString(final String priority) {
        if (priority == null) return NotificationCompat.PRIORITY_DEFAULT;
        final Integer resolvedPriority = notificationPriorityMap.get(priority);
        if (resolvedPriority == null) return NotificationCompat.PRIORITY_DEFAULT;
        return resolvedPriority;
    }

    private int getNotificationVisibilityFromString(final String visibility) {
        if (StringUtils.isNullOrEmpty(visibility)) return Notification.VISIBILITY_PRIVATE;
        final Integer resolvedVisibility = notificationVisibilityMap.get(visibility);
        if (resolvedVisibility == null) return Notification.VISIBILITY_PRIVATE;
        return resolvedVisibility;
    }

    private static ActionType getActionTypeFromString(final String type) {
        if (StringUtils.isNullOrEmpty(type)) {
            return ActionType.NONE;
        }

        return switch (type) {
            case ActionButtonType.DEEPLINK -> ActionType.DEEPLINK;
            case ActionButtonType.WEB_URL -> ActionType.WEB_URL;
            case ActionButtonType.DISMISS -> ActionType.DISMISS;
            case ActionButtonType.OPEN_APP -> ActionType.OPEN_APP;
            default -> ActionType.NONE;
        };
    }

    private List<ActionButton> getActionButtonsFromString(final String actionButtons) {
        List<ActionButton> actionButtonList = new ArrayList<>(ACTION_BUTTON_CAPACITY);
        if (actionButtons == null) {
            Log.debug(MessagingConstants.LOG_TAG, SELF_TAG, "Exception in converting actionButtons json string to json object, Error : actionButtons is null");
            return actionButtonList;
        }
        try {
            final JSONArray jsonArray = new JSONArray(actionButtons);
            for (int i = 0; i < jsonArray.length(); i++) {
                final JSONObject jsonObject = jsonArray.getJSONObject(i);
                final ActionButton button = getActionButton(jsonObject);
                if (button == null) continue;
                actionButtonList.add(button);
            }
        } catch (final JSONException e) {
            Log.warning(MessagingConstants.LOG_TAG, SELF_TAG, "Exception in converting actionButtons json string to json object, Error : %s", e.getLocalizedMessage());
            return actionButtonList;
        }
        return actionButtonList;
    }

    private ActionButton getActionButton(final JSONObject jsonObject) {
        try {
            final String label = jsonObject.getString(ActionButtons.LABEL);
            if (label.isEmpty()) {
                Log.debug(MessagingConstants.LOG_TAG, SELF_TAG, "Label is empty");
                return null;
            }
            String uri = null;
            final String type = jsonObject.getString(ActionButtons.TYPE);
            if (type.equals(ActionButtonType.WEB_URL) || type.equals(ActionButtonType.DEEPLINK)) {
                uri = jsonObject.optString(ActionButtons.URI);
            }

            Log.trace(MessagingConstants.LOG_TAG, SELF_TAG, "Creating an ActionButton with label (%s), uri (%s), and type (%s)", label, uri, type);
            return new ActionButton(label, uri, type);
        } catch (final JSONException e) {
            Log.warning(MessagingConstants.LOG_TAG, SELF_TAG, "Exception in converting actionButtons json string to json object, Error : %s", e.getLocalizedMessage());
            return null;
        }
    }

    public enum ActionType {
        DEEPLINK, WEB_URL, DISMISS, OPEN_APP, NONE
    }

    public static class ActionButton {
        private String label;
        private String link;
        private ActionType type;


        public ActionButton() {
        }

        public ActionButton(final String label, final String link, final String type) {
            this.label = label;
            this.link = link;
            this.type = getActionTypeFromString(type);
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getLabel() {
            return label;
        }

        public void setLink(String link) {
            this.link = link;
        }

        public String getLink() {
            return link;
        }

        public ActionType getType() {
            return type;
        }
    }

    public void fillRequiredData() {
        if (StringUtils.isNullOrEmpty(channelId)) {
            channelId = NotificationConstants.NOTIFICATION_TYPES.GENERAL;
            if (!actionButtons.isEmpty()) {
                actionButtons.clear();
            }
            ActionButton button1 = new ActionButton(ActionButtonType.LABEL_CONTINUE, actionUri, ActionButtonType.DEEPLINK);
            ActionButton button2 = new ActionButton(ActionButtonType.LABEL_DECLINE, ActionButtonType.DISMISS, ActionButtonType.DEEPLINK);
            actionButtons.add(button1);
            actionButtons.add(button2);
        }
    }
}
