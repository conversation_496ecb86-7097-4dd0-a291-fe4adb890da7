package za.co.nedbank.ui.view.notification.transaction_notification.report_fraud;

import android.os.Bundle;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import za.co.nedbank.R;
import za.co.nedbank.core.base.NBBaseActivity;
import za.co.nedbank.core.data.networking.APIInformation;
import za.co.nedbank.core.domain.storage.ApplicationStorage;
import za.co.nedbank.core.navigation.NavigationTarget;
import za.co.nedbank.core.view.fbnotifications.FBTransactionNotificationsViewModel;
import za.co.nedbank.databinding.ActivityReportFraudTransactionBinding;
import za.co.nedbank.ui.di.AppDI;

public class ReportFraudTransactionActivity extends NBBaseActivity implements ReportFraudTransactionView {

    @Inject
    ReportFraudTransactionPresenter reportFraudTransactionPresenter;

    @Inject
    APIInformation apiInformation;
    @Inject
    @Named("memory")
    ApplicationStorage memoryApplicationStorage;

    private FBTransactionNotificationsViewModel transactionModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppDI.getActivityComponent(this).inject(this);
        super.onCreate(savedInstanceState);
        ActivityReportFraudTransactionBinding binding = ActivityReportFraudTransactionBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        if (getIntent() != null && getIntent().hasExtra(NavigationTarget.PARAM_NAME)) {
            transactionModel = (FBTransactionNotificationsViewModel) getIntent().getSerializableExtra(NavigationTarget.PARAM_NAME);
        }

        if (transactionModel != null) {
            List<FBTransactionNotificationsViewModel.ResponseOption> responseOptions = transactionModel.getResponseOptions();
            if (responseOptions != null && !responseOptions.isEmpty() && responseOptions.get(0).getAction() != null) {
                String action = responseOptions.get(0).getAction();
                if (action != null) {
                    binding.btnReport.setText(getString(R.string.report_fraud));
                }
            }
        }
        binding.btnReport.setOnClickListener(v -> onClickReport());
        binding.btnCancel.setOnClickListener(v -> onClickCancel());
    }

    @Override
    protected void onResume() {
        super.onResume();
        reportFraudTransactionPresenter.bind(this);
    }

    public void onClickReport() {
        reportFraudTransactionPresenter.navigateToReportFraud(transactionModel);
        finish();
    }


    public void onClickCancel() {
        reportFraudTransactionPresenter.handleCancelClick();
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        reportFraudTransactionPresenter.unbind();
    }


}
