package za.co.nedbank.ui.domain.model.closed_account;

import java.util.ArrayList;

import za.co.nedbank.core.data.metadatav3.datamodel.MetaDataModel;

public class ClosedAccountResponseData {
    private ArrayList<ClosedAccountDataModel> data = null;
    private MetaDataModel metaDataModel;

    public MetaDataModel getMetaDataModel() {
        return metaDataModel;
    }

    public void setMetaDataModel(MetaDataModel metaDataModel) {
        this.metaDataModel = metaDataModel;
    }

    public ArrayList<ClosedAccountDataModel> getData() {
        return data;
    }

    public void setData(ArrayList<ClosedAccountDataModel> data) {
        this.data = data;
    }
}